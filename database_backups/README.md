# 数据库备份恢复指南

本目录包含PostgreSQL数据库的备份文件和恢复脚本。

## 备份文件说明

在本目录中，您可以找到以下类型的备份文件：

1. **自定义格式备份** (`fitness_db_backup_*.dump`)
   - 压缩二进制格式，可使用`pg_restore`工具恢复
   - 支持选择性恢复（表、函数等）
   - 推荐用于生产环境恢复

2. **完整SQL备份** (`fitness_db_full_*.sql`)
   - 包含所有表结构、数据和数据库对象的纯SQL文件
   - 可直接用`psql`工具执行

3. **仅结构备份** (`fitness_db_schema_*.sql`)
   - 只包含表结构、函数等定义，不包含数据
   - 适用于只需要恢复数据库结构的场景

## 使用恢复脚本

本目录中的`restore_database.sh`脚本可以帮助您恢复数据库备份。

### 脚本用法

```bash
./restore_database.sh [选项]
```

### 选项说明

- `-h, --help`      显示帮助信息
- `-d, --dump`      恢复自定义格式备份文件(.dump)
- `-f, --full`      恢复完整SQL备份文件(.sql)
- `-s, --schema`    恢复仅结构SQL备份文件(.sql)
- `-l, --local`     在本地环境运行，而不是Docker容器中

### 使用示例

1. 恢复自定义格式备份文件：
   ```bash
   ./restore_database.sh -d fitness_db_backup_20250417_141241.dump
   ```

2. 恢复完整SQL备份文件：
   ```bash
   ./restore_database.sh -f fitness_db_full_20250417_141414.sql
   ```

3. 恢复仅结构备份文件在本地环境：
   ```bash
   ./restore_database.sh -s fitness_db_schema_20250417_141354.sql -l
   ```

4. 交互式选择要恢复的备份文件：
   ```bash
   ./restore_database.sh -d
   ```

### 注意事项

1. 默认情况下，脚本会在Docker容器中恢复数据库，使用`-l`选项可以在本地环境恢复
2. 恢复前请确保PostgreSQL数据库服务已启动
3. 恢复完整备份会覆盖现有数据，请谨慎操作
4. 如果遇到权限问题，请确保脚本有执行权限：`chmod +x restore_database.sh`

## 手动恢复命令

如果您想手动恢复备份，可以使用以下命令：

### 在Docker环境中

1. 恢复自定义格式备份：
   ```bash
   docker exec -it backend-db-1 pg_restore -U postgres -d fitness_db -v /backups/fitness_db_backup_日期.dump
   ```

2. 恢复SQL备份：
   ```bash
   docker exec -it backend-db-1 psql -U postgres -d fitness_db -f /backups/fitness_db_full_日期.sql
   ```

### 在本地环境中

1. 恢复自定义格式备份：
   ```bash
   pg_restore -h localhost -p 5432 -U postgres -d fitness_db -v 备份文件路径
   ```

2. 恢复SQL备份：
   ```bash
   psql -h localhost -p 5432 -U postgres -d fitness_db -f 备份文件路径
   ``` 