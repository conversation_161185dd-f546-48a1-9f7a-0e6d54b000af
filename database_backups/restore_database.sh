#!/bin/bash

# 恢复PostgreSQL数据库备份的脚本
# 用途: 恢复之前使用pg_dump创建的数据库备份

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # 无颜色

# 数据库连接参数
DB_NAME="fitness_db"
DB_USER="postgres"
DB_PASSWORD="!scienceFit0219"
DB_HOST="db" # Docker容器名称
DB_PORT="5432"

# 脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 显示帮助信息
show_help() {
    echo -e "${BLUE}数据库恢复工具${NC}"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help      显示此帮助信息"
    echo "  -d, --dump      恢复自定义格式备份文件(.dump)"
    echo "  -f, --full      恢复完整SQL备份文件(.sql)"
    echo "  -s, --schema    恢复仅结构SQL备份文件(.sql)"
    echo "  -l, --local     在本地环境运行，而不是Docker容器中"
    echo ""
    echo "示例:"
    echo "  $0 -d fitness_db_backup_20250417_141241.dump"
    echo "  $0 -f fitness_db_full_20250417_141414.sql"
    echo "  $0 -s fitness_db_schema_20250417_141354.sql -l"
    echo ""
}

# 检查Docker是否正在运行
check_docker() {
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}错误: Docker未安装或无法访问${NC}"
        exit 1
    fi
    
    if ! docker ps &> /dev/null; then
        echo -e "${RED}错误: Docker未运行或没有足够权限${NC}"
        exit 1
    fi
}

# 检查容器是否存在
check_container() {
    if ! docker ps | grep -q "backend-db-1"; then
        echo -e "${RED}错误: 数据库容器'backend-db-1'未运行${NC}"
        exit 1
    fi
}

# 检查备份文件是否存在
check_backup_file() {
    local file="$1"
    if [ ! -f "$file" ]; then
        echo -e "${RED}错误: 备份文件'$file'不存在${NC}"
        exit 1
    fi
}

# 恢复自定义格式备份
restore_dump() {
    local dump_file="$1"
    local use_local="$2"
    
    check_backup_file "$dump_file"
    
    echo -e "${YELLOW}开始恢复自定义格式备份: $dump_file${NC}"
    
    if [ "$use_local" = true ]; then
        # 本地环境执行
        pg_restore -h localhost -p $DB_PORT -U $DB_USER -d $DB_NAME -v "$dump_file"
    else
        # Docker环境执行
        check_docker
        check_container
        docker_dump_path="/backups/$(basename "$dump_file")"
        echo -e "${YELLOW}在Docker容器中恢复备份...${NC}"
        docker exec -it backend-db-1 pg_restore -U $DB_USER -d $DB_NAME -v "$docker_dump_path"
    fi
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}备份恢复成功!${NC}"
    else
        echo -e "${RED}备份恢复失败!${NC}"
        exit 1
    fi
}

# 恢复SQL格式备份
restore_sql() {
    local sql_file="$1"
    local use_local="$2"
    
    check_backup_file "$sql_file"
    
    echo -e "${YELLOW}开始恢复SQL备份: $sql_file${NC}"
    
    if [ "$use_local" = true ]; then
        # 本地环境执行
        PGPASSWORD=$DB_PASSWORD psql -h localhost -p $DB_PORT -U $DB_USER -d $DB_NAME -f "$sql_file"
    else
        # Docker环境执行
        check_docker
        check_container
        docker_sql_path="/backups/$(basename "$sql_file")"
        echo -e "${YELLOW}在Docker容器中恢复备份...${NC}"
        docker exec -it backend-db-1 psql -U $DB_USER -d $DB_NAME -f "$docker_sql_path"
    fi
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}备份恢复成功!${NC}"
    else
        echo -e "${RED}备份恢复失败!${NC}"
        exit 1
    fi
}

# 交互式选择备份文件
select_backup_file() {
    local file_type="$1"
    local file_pattern="$2"
    
    echo -e "${BLUE}可用的${file_type}备份文件:${NC}"
    
    # 列出所有匹配的备份文件
    local files=($SCRIPT_DIR/$file_pattern)
    
    if [ ${#files[@]} -eq 0 ]; then
        echo -e "${RED}错误: 没有找到匹配的备份文件${NC}"
        exit 1
    fi
    
    # 显示可选的文件列表
    for i in "${!files[@]}"; do
        echo "[$i] $(basename "${files[$i]}")"
    done
    
    # 用户选择
    local selection
    read -p "请选择要恢复的备份文件 [0-$((${#files[@]}-1))]: " selection
    
    if [[ ! "$selection" =~ ^[0-9]+$ ]] || [ "$selection" -ge "${#files[@]}" ]; then
        echo -e "${RED}错误: 无效的选择${NC}"
        exit 1
    fi
    
    echo "${files[$selection]}"
}

# 主函数
main() {
    local backup_file=""
    local backup_type=""
    local use_local=false
    
    # 如果没有参数，显示帮助
    if [ $# -eq 0 ]; then
        show_help
        exit 0
    fi
    
    # 解析命令行参数
    while [ $# -gt 0 ]; do
        case "$1" in
            -h|--help)
                show_help
                exit 0
                ;;
            -d|--dump)
                backup_type="dump"
                if [ -n "$2" ] && [ "${2:0:1}" != "-" ]; then
                    backup_file="$SCRIPT_DIR/$2"
                    shift
                fi
                ;;
            -f|--full)
                backup_type="full"
                if [ -n "$2" ] && [ "${2:0:1}" != "-" ]; then
                    backup_file="$SCRIPT_DIR/$2"
                    shift
                fi
                ;;
            -s|--schema)
                backup_type="schema"
                if [ -n "$2" ] && [ "${2:0:1}" != "-" ]; then
                    backup_file="$SCRIPT_DIR/$2"
                    shift
                fi
                ;;
            -l|--local)
                use_local=true
                ;;
            *)
                echo -e "${RED}错误: 未知选项 $1${NC}"
                show_help
                exit 1
                ;;
        esac
        shift
    done
    
    # 如果未指定备份类型
    if [ -z "$backup_type" ]; then
        echo -e "${RED}错误: 请指定要恢复的备份类型${NC}"
        show_help
        exit 1
    fi
    
    # 如果未指定备份文件，则交互式选择
    if [ -z "$backup_file" ]; then
        case "$backup_type" in
            dump)
                backup_file=$(select_backup_file "自定义格式" "fitness_db_backup_*.dump")
                ;;
            full)
                backup_file=$(select_backup_file "完整SQL" "fitness_db_full_*.sql")
                ;;
            schema)
                backup_file=$(select_backup_file "结构SQL" "fitness_db_schema_*.sql")
                ;;
        esac
    fi
    
    # 恢复备份
    case "$backup_type" in
        dump)
            restore_dump "$backup_file" "$use_local"
            ;;
        full|schema)
            restore_sql "$backup_file" "$use_local"
            ;;
    esac
}

# 执行主函数
main "$@" 