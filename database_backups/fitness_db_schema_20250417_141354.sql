--
-- PostgreSQL database dump
--

-- Dumped from database version 15.12 (Debian 15.12-1.pgdg120+1)
-- Dumped by pg_dump version 15.12 (Debian 15.12-1.pgdg120+1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: public; Type: SCHEMA; Schema: -; Owner: postgres
--

-- *not* creating schema, since initdb creates it


ALTER SCHEMA public OWNER TO postgres;

--
-- Name: SCHEMA public; Type: COMMENT; Schema: -; Owner: postgres
--

COMMENT ON SCHEMA public IS '';


--
-- Name: import_exercise_detail_from_json(jsonb); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.import_exercise_detail_from_json(json_data jsonb) RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    detail_id INTEGER;
    v_exercise_id INTEGER;
    v_video_file VARCHAR;
BEGIN
    -- 获取exercise_id
    IF json_data->>'exercise_id' IS NOT NULL THEN
        v_exercise_id := (json_data->>'exercise_id')::INTEGER;
    ELSIF json_data->>'id' IS NOT NULL THEN
        v_exercise_id := (json_data->>'id')::INTEGER;
    ELSE
        RAISE EXCEPTION '缺少exercise_id或id字段';
    END IF;
    
    -- 处理视频文件路径
    IF json_data->>'video_file' IS NOT NULL THEN
        v_video_file := json_data->>'video_file';
    ELSE
        v_video_file := NULL;
    END IF;

    -- 插入或更新健身动作详情
    INSERT INTO exercise_details (
        id, exercise_id, target_muscles_id, synergist_muscles_id,
        ex_instructions, exercise_tips, video_file, is_public
    ) VALUES (
        COALESCE((json_data->>'detail_id')::INTEGER, nextval('exercise_details_id_seq')),
        v_exercise_id,
        CASE 
            WHEN json_data->'target_muscles_id' IS NULL OR json_data->'target_muscles_id' = 'null' THEN '{}'::INTEGER[] 
            ELSE (SELECT array_agg(value::INTEGER) FROM jsonb_array_elements_text(json_data->'target_muscles_id'))
        END,
        CASE 
            WHEN json_data->'synergist_muscles_id' IS NULL OR json_data->'synergist_muscles_id' = 'null' THEN '{}'::INTEGER[] 
            ELSE (SELECT array_agg(value::INTEGER) FROM jsonb_array_elements_text(json_data->'synergist_muscles_id'))
        END,
        CASE 
            WHEN json_data->'ex_instructions' IS NULL OR json_data->'ex_instructions' = 'null' THEN '{}'::TEXT[] 
            ELSE (SELECT array_agg(value::TEXT) FROM jsonb_array_elements_text(json_data->'ex_instructions'))
        END,
        CASE 
            WHEN json_data->'exercise_tips' IS NULL OR json_data->'exercise_tips' = 'null' THEN '{}'::TEXT[] 
            ELSE (SELECT array_agg(value::TEXT) FROM jsonb_array_elements_text(json_data->'exercise_tips'))
        END,
        v_video_file,
        COALESCE((json_data->>'is_public')::BOOLEAN, TRUE)
    )
    ON CONFLICT (id) DO UPDATE SET
        exercise_id = EXCLUDED.exercise_id,
        target_muscles_id = EXCLUDED.target_muscles_id,
        synergist_muscles_id = EXCLUDED.synergist_muscles_id,
        ex_instructions = EXCLUDED.ex_instructions,
        exercise_tips = EXCLUDED.exercise_tips,
        video_file = EXCLUDED.video_file,
        is_public = EXCLUDED.is_public
    RETURNING id INTO detail_id;
    
    RETURN detail_id;
END;
$$;


ALTER FUNCTION public.import_exercise_detail_from_json(json_data jsonb) OWNER TO postgres;

--
-- Name: import_exercise_from_json(jsonb); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.import_exercise_from_json(json_data jsonb) RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    exercise_id INTEGER;
    v_exercise_name VARCHAR;
    v_image_name VARCHAR;
    v_gif_url VARCHAR;
BEGIN
    -- 处理健身动作名称
    IF json_data->>'name' IS NOT NULL THEN
        v_exercise_name := json_data->>'name';
    ELSIF json_data->'exercise'->>'name' IS NOT NULL THEN
        v_exercise_name := json_data->'exercise'->>'name';
    ELSE
        v_exercise_name := '未命名动作';
    END IF;
    
    -- 处理图片路径
    IF json_data->>'image_name' IS NOT NULL THEN
        v_image_name := json_data->>'image_name';
    ELSIF json_data->'exercise'->>'image_name' IS NOT NULL THEN
        v_image_name := json_data->'exercise'->>'image_name';
    ELSE
        v_image_name := NULL;
    END IF;
    
    -- 处理GIF路径
    IF json_data->>'gif_url' IS NOT NULL THEN
        v_gif_url := json_data->>'gif_url';
    ELSIF json_data->'exercise'->>'gif_url' IS NOT NULL THEN
        v_gif_url := json_data->'exercise'->>'gif_url';
    ELSE
        v_gif_url := NULL;
    END IF;

    -- 插入或更新健身动作基本信息
    INSERT INTO exercises (
        id, name, en_name, body_part_id, equipment_id, 
        image_name, gif_url, description, level, 
        sort_priority, user_id, exercise_type, hit_time
    ) VALUES (
        COALESCE((json_data->>'id')::INTEGER, nextval('exercises_id_seq')),
        v_exercise_name,
        json_data->>'en_name',
        CASE 
            WHEN json_data->'body_part_id' IS NULL OR json_data->'body_part_id' = 'null' THEN '{}'::INTEGER[] 
            ELSE (SELECT array_agg(value::INTEGER) FROM jsonb_array_elements_text(json_data->'body_part_id'))
        END,
        CASE 
            WHEN json_data->'equipment_id' IS NULL OR json_data->'equipment_id' = 'null' THEN '{}'::INTEGER[] 
            ELSE (SELECT array_agg(value::INTEGER) FROM jsonb_array_elements_text(json_data->'equipment_id'))
        END,
        v_image_name,
        v_gif_url,
        json_data->>'description',
        COALESCE((json_data->>'level')::SMALLINT, 1),
        COALESCE((json_data->>'sortPriority')::INTEGER, (json_data->>'sort_priority')::INTEGER, 0),
        COALESCE(json_data->>'user_id', 'ScienceFit'),
        COALESCE(json_data->>'exercise_type', 'weight_reps'),
        0
    )
    ON CONFLICT (id) DO UPDATE SET
        name = EXCLUDED.name,
        en_name = EXCLUDED.en_name,
        body_part_id = EXCLUDED.body_part_id,
        equipment_id = EXCLUDED.equipment_id,
        image_name = EXCLUDED.image_name,
        gif_url = EXCLUDED.gif_url,
        description = EXCLUDED.description,
        level = EXCLUDED.level,
        sort_priority = EXCLUDED.sort_priority,
        user_id = EXCLUDED.user_id,
        exercise_type = EXCLUDED.exercise_type
    RETURNING id INTO exercise_id;
    
    RETURN exercise_id;
END;
$$;


ALTER FUNCTION public.import_exercise_from_json(json_data jsonb) OWNER TO postgres;

--
-- Name: import_food_from_json(jsonb); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.import_food_from_json(json_data jsonb) RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    v_food_id INTEGER;
    v_food_name TEXT;
    v_existing_id INTEGER;
    nutrient_data JSONB;
    unit_data JSONB;
    nutrient_item JSONB;
    unit_item JSONB;
    category_name TEXT;
BEGIN
    -- 提取食品名称
    v_food_name := json_data->'food'->>'name';
    
    -- 检查食品名称是否已存在
    SELECT id INTO v_existing_id FROM foods WHERE name = v_food_name;
    
    -- 如果名称已存在，直接返回已存在食品ID
    IF v_existing_id IS NOT NULL THEN
        RETURN v_existing_id;
    END IF;
    
    -- 插入食品基本信息
    INSERT INTO foods (
        id, name, code, category, category_code, food_type, goods_id, 
        thumb_image_url, large_image_url, is_liquid, 
        updated_at, can_revise
    ) VALUES (
        (json_data->'food'->>'id')::INTEGER,
        v_food_name,
        json_data->'food'->>'code',
        json_data->'food'->>'category',
        (json_data->'food'->>'category_code')::INTEGER,
        json_data->'food'->>'food_type',
        (json_data->'food'->>'goods_id')::INTEGER,
        json_data->'food'->>'thumb_image_url',
        json_data->'food'->>'large_image_url',
        (json_data->'food'->>'is_liquid')::BOOLEAN,
        (json_data->'food'->>'updated_at')::TIMESTAMP,
        (json_data->'food'->>'can_revise')::BOOLEAN
    )
    ON CONFLICT (id) DO UPDATE SET
        name = EXCLUDED.name,
        code = EXCLUDED.code,
        category = EXCLUDED.category,
        category_code = EXCLUDED.category_code,
        food_type = EXCLUDED.food_type,
        goods_id = EXCLUDED.goods_id,
        thumb_image_url = EXCLUDED.thumb_image_url,
        large_image_url = EXCLUDED.large_image_url,
        is_liquid = EXCLUDED.is_liquid,
        updated_at = EXCLUDED.updated_at,
        can_revise = EXCLUDED.can_revise
    RETURNING id INTO v_food_id;
    
    -- 插入营养概况
    INSERT INTO nutritional_profiles (
        food_id, health_light, lights, warnings, warning_scenes,
        calory, protein_fraction, fat_fraction, carb_fraction
    ) VALUES (
        v_food_id,
        (json_data->'nutritional_profile'->'red_green_section'->>'health_light')::INTEGER,
        json_data->'nutritional_profile'->'red_green_section'->'lights',
        json_data->'nutritional_profile'->'red_green_section'->'warnings',
        json_data->'nutritional_profile'->'red_green_section'->'warning_scenes',
        (json_data->'nutritional_profile'->'nutrients_section'->>'calory')::FLOAT,
        (json_data->'nutritional_profile'->'nutrients_section'->'energy_fractions'->>'protein')::FLOAT / 100,
        (json_data->'nutritional_profile'->'nutrients_section'->'energy_fractions'->>'fat')::FLOAT / 100,
        (json_data->'nutritional_profile'->'nutrients_section'->'energy_fractions'->>'carbohydrate')::FLOAT / 100
    )
    ON CONFLICT (food_id) DO UPDATE SET
        health_light = EXCLUDED.health_light,
        lights = EXCLUDED.lights,
        warnings = EXCLUDED.warnings,
        warning_scenes = EXCLUDED.warning_scenes,
        calory = EXCLUDED.calory,
        protein_fraction = EXCLUDED.protein_fraction,
        fat_fraction = EXCLUDED.fat_fraction,
        carb_fraction = EXCLUDED.carb_fraction;
    
    -- 删除旧的营养素数据
    DELETE FROM food_nutrient_values WHERE food_id = v_food_id;
    
    -- 插入主要营养素
    FOR nutrient_item IN SELECT * FROM jsonb_array_elements(json_data->'nutritional_profile'->'nutrients_section'->'values'->'main')
    LOOP
        INSERT INTO food_nutrient_values (
            food_id, name_en, name_cn, value, unit, unit_name, precision, nrv, category
        ) VALUES (
            v_food_id,
            nutrient_item->>'name_en',
            nutrient_item->>'name',
            (nutrient_item->>'value')::FLOAT,
            nutrient_item->>'unit',
            nutrient_item->>'unit_name',
            (nutrient_item->>'precision')::INTEGER,
            (nutrient_item->>'nrv')::FLOAT,
            'main'
        );
    END LOOP;
    
    -- 插入维生素
    FOR nutrient_item IN SELECT * FROM jsonb_array_elements(json_data->'nutritional_profile'->'nutrients_section'->'values'->'vitamin')
    LOOP
        INSERT INTO food_nutrient_values (
            food_id, name_en, name_cn, value, unit, unit_name, precision, nrv, category
        ) VALUES (
            v_food_id,
            nutrient_item->>'name_en',
            nutrient_item->>'name',
            (nutrient_item->>'value')::FLOAT,
            nutrient_item->>'unit',
            nutrient_item->>'unit_name',
            (nutrient_item->>'precision')::INTEGER,
            (nutrient_item->>'nrv')::FLOAT,
            'vitamin'
        );
    END LOOP;
    
    -- 插入矿物质
    FOR nutrient_item IN SELECT * FROM jsonb_array_elements(json_data->'nutritional_profile'->'nutrients_section'->'values'->'minerals')
    LOOP
        INSERT INTO food_nutrient_values (
            food_id, name_en, name_cn, value, unit, unit_name, precision, nrv, category
        ) VALUES (
            v_food_id,
            nutrient_item->>'name_en',
            nutrient_item->>'name',
            (nutrient_item->>'value')::FLOAT,
            nutrient_item->>'unit',
            nutrient_item->>'unit_name',
            (nutrient_item->>'precision')::INTEGER,
            (nutrient_item->>'nrv')::FLOAT,
            'minerals'
        );
    END LOOP;
    
    -- 插入其他营养素
    FOR nutrient_item IN SELECT * FROM jsonb_array_elements(json_data->'nutritional_profile'->'nutrients_section'->'values'->'other')
    LOOP
        INSERT INTO food_nutrient_values (
            food_id, name_en, name_cn, value, unit, unit_name, precision, nrv, category
        ) VALUES (
            v_food_id,
            nutrient_item->>'name_en',
            nutrient_item->>'name',
            (nutrient_item->>'value')::FLOAT,
            nutrient_item->>'unit',
            nutrient_item->>'unit_name',
            (nutrient_item->>'precision')::INTEGER,
            (nutrient_item->>'nrv')::FLOAT,
            'other'
        );
    END LOOP;
    
    -- 删除旧的单位数据
    DELETE FROM food_units WHERE food_id = v_food_id;
    
    -- 插入计量单位
    FOR unit_item IN SELECT * FROM jsonb_array_elements(json_data->'nutritional_profile'->'nutrients_section'->'units')
    LOOP
        INSERT INTO food_units (
            food_id, unit_name, weight, eat_weight, is_default
        ) VALUES (
            v_food_id,
            unit_item->>'unit_name',
            (unit_item->>'weight')::FLOAT,
            (unit_item->>'eat_weight')::FLOAT,
            (unit_item->>'id')::INTEGER = 0  -- 假设id=0的是默认单位
        );
    END LOOP;
    
    RETURN v_food_id;
END;
$$;


ALTER FUNCTION public.import_food_from_json(json_data jsonb) OWNER TO postgres;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: body_parts; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.body_parts (
    id integer NOT NULL,
    name character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.body_parts OWNER TO postgres;

--
-- Name: body_parts_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.body_parts_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.body_parts_id_seq OWNER TO postgres;

--
-- Name: body_parts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.body_parts_id_seq OWNED BY public.body_parts.id;


--
-- Name: equipment; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.equipment (
    id integer NOT NULL,
    name character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.equipment OWNER TO postgres;

--
-- Name: equipment_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.equipment_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.equipment_id_seq OWNER TO postgres;

--
-- Name: equipment_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.equipment_id_seq OWNED BY public.equipment.id;


--
-- Name: exercise_details; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.exercise_details (
    id integer NOT NULL,
    exercise_id integer NOT NULL,
    target_muscles_id integer[],
    synergist_muscles_id integer[],
    ex_instructions text[],
    exercise_tips text[],
    video_file character varying(255),
    is_public boolean DEFAULT true,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.exercise_details OWNER TO postgres;

--
-- Name: exercise_details_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.exercise_details_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.exercise_details_id_seq OWNER TO postgres;

--
-- Name: exercise_details_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.exercise_details_id_seq OWNED BY public.exercise_details.id;


--
-- Name: exercises; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.exercises (
    id integer NOT NULL,
    name character varying(100) NOT NULL,
    en_name character varying(100),
    body_part_id integer[],
    equipment_id integer[],
    image_name character varying(255),
    gif_url character varying(255),
    description text,
    level smallint,
    sort_priority integer DEFAULT 0,
    user_id character varying(50),
    exercise_type character varying(50),
    hit_time integer DEFAULT 0,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.exercises OWNER TO postgres;

--
-- Name: exercises_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.exercises_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.exercises_id_seq OWNER TO postgres;

--
-- Name: exercises_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.exercises_id_seq OWNED BY public.exercises.id;


--
-- Name: food_item_nutrient_intakes; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.food_item_nutrient_intakes (
    id integer NOT NULL,
    food_item_id integer,
    name_en character varying(64) NOT NULL,
    name_cn character varying(64) NOT NULL,
    value double precision,
    unit character varying(16),
    unit_name character varying(16),
    nrv_percentage double precision,
    category character varying(16) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.food_item_nutrient_intakes OWNER TO postgres;

--
-- Name: food_item_nutrient_intakes_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.food_item_nutrient_intakes_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.food_item_nutrient_intakes_id_seq OWNER TO postgres;

--
-- Name: food_item_nutrient_intakes_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.food_item_nutrient_intakes_id_seq OWNED BY public.food_item_nutrient_intakes.id;


--
-- Name: food_items; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.food_items (
    id integer NOT NULL,
    meal_record_id integer,
    food_id integer,
    name character varying(100) NOT NULL,
    quantity double precision DEFAULT 1.0,
    unit_name character varying(20) DEFAULT '份'::character varying,
    weight double precision NOT NULL,
    category character varying(50),
    cuisine_type character varying(50),
    cuisine_type_detail character varying(100),
    image_url character varying,
    health_light integer,
    lights jsonb,
    warnings jsonb,
    warning_scenes jsonb,
    calory double precision,
    protein double precision,
    fat double precision,
    carbohydrate double precision,
    protein_fraction double precision,
    fat_fraction double precision,
    carb_fraction double precision,
    is_custom boolean DEFAULT false,
    is_takeout boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.food_items OWNER TO postgres;

--
-- Name: food_items_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.food_items_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.food_items_id_seq OWNER TO postgres;

--
-- Name: food_items_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.food_items_id_seq OWNED BY public.food_items.id;


--
-- Name: food_nutrient_values; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.food_nutrient_values (
    id integer NOT NULL,
    food_id integer,
    name_en character varying(64) NOT NULL,
    name_cn character varying(64) NOT NULL,
    value double precision,
    unit character varying(16),
    unit_name character varying(16),
    "precision" integer,
    nrv double precision,
    category character varying(16) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.food_nutrient_values OWNER TO postgres;

--
-- Name: food_nutrient_values_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.food_nutrient_values_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.food_nutrient_values_id_seq OWNER TO postgres;

--
-- Name: food_nutrient_values_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.food_nutrient_values_id_seq OWNED BY public.food_nutrient_values.id;


--
-- Name: food_recognitions; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.food_recognitions (
    id integer NOT NULL,
    user_id integer,
    meal_date date NOT NULL,
    meal_type character varying(20) NOT NULL,
    image_url character varying(255),
    thumb_image_url character varying(255),
    secure_path character varying(100),
    status character varying(20) DEFAULT 'processing'::character varying,
    recognition_result jsonb,
    matched_foods jsonb,
    nutrition_totals jsonb,
    meal_record_id integer,
    user_modified boolean DEFAULT false,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.food_recognitions OWNER TO postgres;

--
-- Name: food_recognitions_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.food_recognitions_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.food_recognitions_id_seq OWNER TO postgres;

--
-- Name: food_recognitions_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.food_recognitions_id_seq OWNED BY public.food_recognitions.id;


--
-- Name: food_units; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.food_units (
    id integer NOT NULL,
    food_id integer,
    unit_name character varying(32) NOT NULL,
    weight double precision NOT NULL,
    eat_weight double precision NOT NULL,
    is_default boolean DEFAULT false,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.food_units OWNER TO postgres;

--
-- Name: food_units_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.food_units_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.food_units_id_seq OWNER TO postgres;

--
-- Name: food_units_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.food_units_id_seq OWNED BY public.food_units.id;


--
-- Name: foods; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.foods (
    id integer NOT NULL,
    name character varying(128) NOT NULL,
    code character varying(128) NOT NULL,
    category character varying(64),
    category_code integer,
    food_type character varying(32),
    goods_id integer,
    thumb_image_url character varying,
    large_image_url character varying,
    is_liquid boolean DEFAULT false,
    hot integer DEFAULT 0,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    can_revise boolean DEFAULT false,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.foods OWNER TO postgres;

--
-- Name: foods_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.foods_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.foods_id_seq OWNER TO postgres;

--
-- Name: foods_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.foods_id_seq OWNED BY public.foods.id;


--
-- Name: health_recommendations; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.health_recommendations (
    id integer NOT NULL,
    meal_record_id integer,
    recommendation_text character varying NOT NULL,
    recommendation_type character varying(32),
    priority integer DEFAULT 0,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.health_recommendations OWNER TO postgres;

--
-- Name: health_recommendations_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.health_recommendations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.health_recommendations_id_seq OWNER TO postgres;

--
-- Name: health_recommendations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.health_recommendations_id_seq OWNED BY public.health_recommendations.id;


--
-- Name: meal_records; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.meal_records (
    id integer NOT NULL,
    user_id integer,
    date date NOT NULL,
    meal_type character varying(20) NOT NULL,
    image_url character varying,
    file_id character varying(100),
    thumb_image_url character varying,
    total_calory double precision DEFAULT 0,
    total_protein double precision DEFAULT 0,
    total_fat double precision DEFAULT 0,
    total_carbohydrate double precision DEFAULT 0,
    is_ai_recognized boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.meal_records OWNER TO postgres;

--
-- Name: meal_records_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.meal_records_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.meal_records_id_seq OWNER TO postgres;

--
-- Name: meal_records_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.meal_records_id_seq OWNED BY public.meal_records.id;


--
-- Name: muscles; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.muscles (
    id integer NOT NULL,
    name character varying(50) NOT NULL,
    en_name character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.muscles OWNER TO postgres;

--
-- Name: muscles_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.muscles_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.muscles_id_seq OWNER TO postgres;

--
-- Name: muscles_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.muscles_id_seq OWNED BY public.muscles.id;


--
-- Name: nutritional_profiles; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.nutritional_profiles (
    id integer NOT NULL,
    food_id integer,
    health_light integer,
    lights jsonb,
    warnings jsonb,
    warning_scenes jsonb,
    calory double precision,
    protein_fraction double precision,
    fat_fraction double precision,
    carb_fraction double precision,
    food_rank integer,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.nutritional_profiles OWNER TO postgres;

--
-- Name: nutritional_profiles_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.nutritional_profiles_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.nutritional_profiles_id_seq OWNER TO postgres;

--
-- Name: nutritional_profiles_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.nutritional_profiles_id_seq OWNED BY public.nutritional_profiles.id;


--
-- Name: share_tracks; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.share_tracks (
    id integer NOT NULL,
    shared_by integer,
    scanned_by integer,
    share_type character varying NOT NULL,
    page character varying NOT NULL,
    scene character varying,
    qrcode_url character varying,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.share_tracks OWNER TO postgres;

--
-- Name: share_tracks_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.share_tracks_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.share_tracks_id_seq OWNER TO postgres;

--
-- Name: share_tracks_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.share_tracks_id_seq OWNED BY public.share_tracks.id;


--
-- Name: user_favorite_exercises; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_favorite_exercises (
    id integer NOT NULL,
    user_id integer,
    exercise_id integer,
    notes text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    is_active boolean DEFAULT true
);


ALTER TABLE public.user_favorite_exercises OWNER TO postgres;

--
-- Name: user_favorite_exercises_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.user_favorite_exercises_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.user_favorite_exercises_id_seq OWNER TO postgres;

--
-- Name: user_favorite_exercises_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.user_favorite_exercises_id_seq OWNED BY public.user_favorite_exercises.id;


--
-- Name: user_favorite_foods; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_favorite_foods (
    id integer NOT NULL,
    user_id integer,
    food_id integer,
    notes text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    is_active boolean DEFAULT true
);


ALTER TABLE public.user_favorite_foods OWNER TO postgres;

--
-- Name: user_favorite_foods_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.user_favorite_foods_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.user_favorite_foods_id_seq OWNER TO postgres;

--
-- Name: user_favorite_foods_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.user_favorite_foods_id_seq OWNED BY public.user_favorite_foods.id;


--
-- Name: user_settings; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_settings (
    id integer NOT NULL,
    user_id integer NOT NULL,
    notification_enabled boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.user_settings OWNER TO postgres;

--
-- Name: user_settings_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.user_settings_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.user_settings_id_seq OWNER TO postgres;

--
-- Name: user_settings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.user_settings_id_seq OWNED BY public.user_settings.id;


--
-- Name: users; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.users (
    id integer NOT NULL,
    email character varying,
    phone character varying,
    hashed_password character varying,
    openid character varying,
    unionid character varying,
    session_key character varying,
    nickname character varying,
    avatar_url character varying,
    country character varying,
    province character varying,
    city character varying,
    gender integer,
    birthday timestamp without time zone,
    age integer,
    height double precision,
    weight double precision,
    activity_level integer DEFAULT 3,
    body_type character varying,
    experience_level integer,
    fitness_goal integer,
    health_conditions character varying[],
    allergies character varying[],
    bmi double precision,
    tedd integer,
    completed boolean DEFAULT false,
    is_active boolean DEFAULT true,
    is_superuser boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.users OWNER TO postgres;

--
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.users_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE public.users_id_seq OWNER TO postgres;

--
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- Name: body_parts id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.body_parts ALTER COLUMN id SET DEFAULT nextval('public.body_parts_id_seq'::regclass);


--
-- Name: equipment id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.equipment ALTER COLUMN id SET DEFAULT nextval('public.equipment_id_seq'::regclass);


--
-- Name: exercise_details id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.exercise_details ALTER COLUMN id SET DEFAULT nextval('public.exercise_details_id_seq'::regclass);


--
-- Name: exercises id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.exercises ALTER COLUMN id SET DEFAULT nextval('public.exercises_id_seq'::regclass);


--
-- Name: food_item_nutrient_intakes id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.food_item_nutrient_intakes ALTER COLUMN id SET DEFAULT nextval('public.food_item_nutrient_intakes_id_seq'::regclass);


--
-- Name: food_items id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.food_items ALTER COLUMN id SET DEFAULT nextval('public.food_items_id_seq'::regclass);


--
-- Name: food_nutrient_values id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.food_nutrient_values ALTER COLUMN id SET DEFAULT nextval('public.food_nutrient_values_id_seq'::regclass);


--
-- Name: food_recognitions id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.food_recognitions ALTER COLUMN id SET DEFAULT nextval('public.food_recognitions_id_seq'::regclass);


--
-- Name: food_units id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.food_units ALTER COLUMN id SET DEFAULT nextval('public.food_units_id_seq'::regclass);


--
-- Name: foods id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.foods ALTER COLUMN id SET DEFAULT nextval('public.foods_id_seq'::regclass);


--
-- Name: health_recommendations id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.health_recommendations ALTER COLUMN id SET DEFAULT nextval('public.health_recommendations_id_seq'::regclass);


--
-- Name: meal_records id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.meal_records ALTER COLUMN id SET DEFAULT nextval('public.meal_records_id_seq'::regclass);


--
-- Name: muscles id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.muscles ALTER COLUMN id SET DEFAULT nextval('public.muscles_id_seq'::regclass);


--
-- Name: nutritional_profiles id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.nutritional_profiles ALTER COLUMN id SET DEFAULT nextval('public.nutritional_profiles_id_seq'::regclass);


--
-- Name: share_tracks id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.share_tracks ALTER COLUMN id SET DEFAULT nextval('public.share_tracks_id_seq'::regclass);


--
-- Name: user_favorite_exercises id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_favorite_exercises ALTER COLUMN id SET DEFAULT nextval('public.user_favorite_exercises_id_seq'::regclass);


--
-- Name: user_favorite_foods id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_favorite_foods ALTER COLUMN id SET DEFAULT nextval('public.user_favorite_foods_id_seq'::regclass);


--
-- Name: user_settings id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_settings ALTER COLUMN id SET DEFAULT nextval('public.user_settings_id_seq'::regclass);


--
-- Name: users id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- Name: body_parts body_parts_name_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.body_parts
    ADD CONSTRAINT body_parts_name_key UNIQUE (name);


--
-- Name: body_parts body_parts_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.body_parts
    ADD CONSTRAINT body_parts_pkey PRIMARY KEY (id);


--
-- Name: equipment equipment_name_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.equipment
    ADD CONSTRAINT equipment_name_key UNIQUE (name);


--
-- Name: equipment equipment_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.equipment
    ADD CONSTRAINT equipment_pkey PRIMARY KEY (id);


--
-- Name: exercise_details exercise_details_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.exercise_details
    ADD CONSTRAINT exercise_details_pkey PRIMARY KEY (id);


--
-- Name: exercises exercises_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.exercises
    ADD CONSTRAINT exercises_pkey PRIMARY KEY (id);


--
-- Name: food_item_nutrient_intakes food_item_nutrient_intakes_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.food_item_nutrient_intakes
    ADD CONSTRAINT food_item_nutrient_intakes_pkey PRIMARY KEY (id);


--
-- Name: food_items food_items_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.food_items
    ADD CONSTRAINT food_items_pkey PRIMARY KEY (id);


--
-- Name: food_nutrient_values food_nutrient_values_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.food_nutrient_values
    ADD CONSTRAINT food_nutrient_values_pkey PRIMARY KEY (id);


--
-- Name: food_recognitions food_recognitions_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.food_recognitions
    ADD CONSTRAINT food_recognitions_pkey PRIMARY KEY (id);


--
-- Name: food_units food_units_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.food_units
    ADD CONSTRAINT food_units_pkey PRIMARY KEY (id);


--
-- Name: foods foods_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.foods
    ADD CONSTRAINT foods_pkey PRIMARY KEY (id);


--
-- Name: health_recommendations health_recommendations_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.health_recommendations
    ADD CONSTRAINT health_recommendations_pkey PRIMARY KEY (id);


--
-- Name: foods idx_food_code; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.foods
    ADD CONSTRAINT idx_food_code UNIQUE (code);


--
-- Name: foods idx_food_name; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.foods
    ADD CONSTRAINT idx_food_name UNIQUE (name);


--
-- Name: meal_records meal_records_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.meal_records
    ADD CONSTRAINT meal_records_pkey PRIMARY KEY (id);


--
-- Name: muscles muscles_en_name_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.muscles
    ADD CONSTRAINT muscles_en_name_key UNIQUE (en_name);


--
-- Name: muscles muscles_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.muscles
    ADD CONSTRAINT muscles_pkey PRIMARY KEY (id);


--
-- Name: nutritional_profiles nutritional_profiles_food_id_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.nutritional_profiles
    ADD CONSTRAINT nutritional_profiles_food_id_key UNIQUE (food_id);


--
-- Name: nutritional_profiles nutritional_profiles_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.nutritional_profiles
    ADD CONSTRAINT nutritional_profiles_pkey PRIMARY KEY (id);


--
-- Name: share_tracks share_tracks_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.share_tracks
    ADD CONSTRAINT share_tracks_pkey PRIMARY KEY (id);


--
-- Name: user_favorite_exercises unique_user_exercise; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_favorite_exercises
    ADD CONSTRAINT unique_user_exercise UNIQUE (user_id, exercise_id);


--
-- Name: user_favorite_foods unique_user_food; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_favorite_foods
    ADD CONSTRAINT unique_user_food UNIQUE (user_id, food_id);


--
-- Name: user_favorite_exercises user_favorite_exercises_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_favorite_exercises
    ADD CONSTRAINT user_favorite_exercises_pkey PRIMARY KEY (id);


--
-- Name: user_favorite_foods user_favorite_foods_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_favorite_foods
    ADD CONSTRAINT user_favorite_foods_pkey PRIMARY KEY (id);


--
-- Name: user_settings user_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_settings
    ADD CONSTRAINT user_settings_pkey PRIMARY KEY (id);


--
-- Name: user_settings user_settings_user_id_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_settings
    ADD CONSTRAINT user_settings_user_id_key UNIQUE (user_id);


--
-- Name: users users_email_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_email_key UNIQUE (email);


--
-- Name: users users_openid_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_openid_key UNIQUE (openid);


--
-- Name: users users_phone_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_phone_key UNIQUE (phone);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: users users_unionid_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_unionid_key UNIQUE (unionid);


--
-- Name: idx_body_part_id_gin; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_body_part_id_gin ON public.exercises USING gin (body_part_id);


--
-- Name: idx_body_part_name_lower; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_body_part_name_lower ON public.body_parts USING btree (lower((name)::text));


--
-- Name: idx_equipment_id_gin; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_equipment_id_gin ON public.exercises USING gin (equipment_id);


--
-- Name: idx_equipment_name_lower; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_equipment_name_lower ON public.equipment USING btree (lower((name)::text));


--
-- Name: idx_exercise_detail_exercise_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_exercise_detail_exercise_id ON public.exercise_details USING btree (exercise_id);


--
-- Name: idx_exercise_detail_public; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_exercise_detail_public ON public.exercise_details USING btree (exercise_id, is_public);


--
-- Name: idx_exercise_en_name_lower; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_exercise_en_name_lower ON public.exercises USING btree (lower((en_name)::text));


--
-- Name: idx_exercise_level_hit_time; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_exercise_level_hit_time ON public.exercises USING btree (level, hit_time);


--
-- Name: idx_exercise_name; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_exercise_name ON public.exercises USING btree (name);


--
-- Name: idx_exercise_name_en_name; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_exercise_name_en_name ON public.exercises USING btree (name, en_name);


--
-- Name: idx_exercise_name_lower; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_exercise_name_lower ON public.exercises USING btree (lower((name)::text));


--
-- Name: idx_exercise_sort_priority; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_exercise_sort_priority ON public.exercises USING btree (sort_priority DESC, id);


--
-- Name: idx_favorite_exercises_active; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_favorite_exercises_active ON public.user_favorite_exercises USING btree (is_active);


--
-- Name: idx_favorite_exercises_exercise_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_favorite_exercises_exercise_id ON public.user_favorite_exercises USING btree (exercise_id);


--
-- Name: idx_favorite_exercises_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_favorite_exercises_user_id ON public.user_favorite_exercises USING btree (user_id);


--
-- Name: idx_favorite_foods_active; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_favorite_foods_active ON public.user_favorite_foods USING btree (is_active);


--
-- Name: idx_favorite_foods_food_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_favorite_foods_food_id ON public.user_favorite_foods USING btree (food_id);


--
-- Name: idx_favorite_foods_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_favorite_foods_user_id ON public.user_favorite_foods USING btree (user_id);


--
-- Name: idx_food_category; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_food_category ON public.foods USING btree (category);


--
-- Name: idx_food_category_code; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_food_category_code ON public.foods USING btree (category_code);


--
-- Name: idx_food_item_nutrients_category; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_food_item_nutrients_category ON public.food_item_nutrient_intakes USING btree (category);


--
-- Name: idx_food_item_nutrients_food_item_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_food_item_nutrients_food_item_id ON public.food_item_nutrient_intakes USING btree (food_item_id);


--
-- Name: idx_food_items_food_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_food_items_food_id ON public.food_items USING btree (food_id);


--
-- Name: idx_food_items_meal_record_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_food_items_meal_record_id ON public.food_items USING btree (meal_record_id);


--
-- Name: idx_food_nutrient_values_category; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_food_nutrient_values_category ON public.food_nutrient_values USING btree (category);


--
-- Name: idx_food_nutrient_values_food_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_food_nutrient_values_food_id ON public.food_nutrient_values USING btree (food_id);


--
-- Name: idx_food_recognitions_meal_record_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_food_recognitions_meal_record_id ON public.food_recognitions USING btree (meal_record_id);


--
-- Name: idx_food_recognitions_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_food_recognitions_user_id ON public.food_recognitions USING btree (user_id);


--
-- Name: idx_food_type; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_food_type ON public.foods USING btree (food_type);


--
-- Name: idx_food_units_food_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_food_units_food_id ON public.food_units USING btree (food_id);


--
-- Name: idx_health_recommendations_meal_record_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_health_recommendations_meal_record_id ON public.health_recommendations USING btree (meal_record_id);


--
-- Name: idx_meal_records_date; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_meal_records_date ON public.meal_records USING btree (date);


--
-- Name: idx_meal_records_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_meal_records_user_id ON public.meal_records USING btree (user_id);


--
-- Name: idx_muscle_name; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_muscle_name ON public.muscles USING btree (name);


--
-- Name: idx_muscle_name_lower; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_muscle_name_lower ON public.muscles USING btree (lower((name)::text));


--
-- Name: idx_popular_exercises; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_popular_exercises ON public.exercises USING btree (hit_time DESC) WHERE (hit_time > 0);


--
-- Name: idx_synergist_muscles_gin; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_synergist_muscles_gin ON public.exercise_details USING gin (synergist_muscles_id);


--
-- Name: idx_target_muscles_gin; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_target_muscles_gin ON public.exercise_details USING gin (target_muscles_id);


--
-- Name: exercise_details exercise_details_exercise_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.exercise_details
    ADD CONSTRAINT exercise_details_exercise_id_fkey FOREIGN KEY (exercise_id) REFERENCES public.exercises(id);


--
-- Name: food_item_nutrient_intakes food_item_nutrient_intakes_food_item_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.food_item_nutrient_intakes
    ADD CONSTRAINT food_item_nutrient_intakes_food_item_id_fkey FOREIGN KEY (food_item_id) REFERENCES public.food_items(id) ON DELETE CASCADE;


--
-- Name: food_items food_items_food_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.food_items
    ADD CONSTRAINT food_items_food_id_fkey FOREIGN KEY (food_id) REFERENCES public.foods(id);


--
-- Name: food_items food_items_meal_record_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.food_items
    ADD CONSTRAINT food_items_meal_record_id_fkey FOREIGN KEY (meal_record_id) REFERENCES public.meal_records(id) ON DELETE CASCADE;


--
-- Name: food_nutrient_values food_nutrient_values_food_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.food_nutrient_values
    ADD CONSTRAINT food_nutrient_values_food_id_fkey FOREIGN KEY (food_id) REFERENCES public.foods(id) ON DELETE CASCADE;


--
-- Name: food_recognitions food_recognitions_meal_record_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.food_recognitions
    ADD CONSTRAINT food_recognitions_meal_record_id_fkey FOREIGN KEY (meal_record_id) REFERENCES public.meal_records(id);


--
-- Name: food_units food_units_food_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.food_units
    ADD CONSTRAINT food_units_food_id_fkey FOREIGN KEY (food_id) REFERENCES public.foods(id) ON DELETE CASCADE;


--
-- Name: health_recommendations health_recommendations_meal_record_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.health_recommendations
    ADD CONSTRAINT health_recommendations_meal_record_id_fkey FOREIGN KEY (meal_record_id) REFERENCES public.meal_records(id) ON DELETE CASCADE;


--
-- Name: meal_records meal_records_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.meal_records
    ADD CONSTRAINT meal_records_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: nutritional_profiles nutritional_profiles_food_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.nutritional_profiles
    ADD CONSTRAINT nutritional_profiles_food_id_fkey FOREIGN KEY (food_id) REFERENCES public.foods(id) ON DELETE CASCADE;


--
-- Name: share_tracks share_tracks_scanned_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.share_tracks
    ADD CONSTRAINT share_tracks_scanned_by_fkey FOREIGN KEY (scanned_by) REFERENCES public.users(id) ON DELETE SET NULL;


--
-- Name: share_tracks share_tracks_shared_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.share_tracks
    ADD CONSTRAINT share_tracks_shared_by_fkey FOREIGN KEY (shared_by) REFERENCES public.users(id) ON DELETE SET NULL;


--
-- Name: user_favorite_exercises user_favorite_exercises_exercise_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_favorite_exercises
    ADD CONSTRAINT user_favorite_exercises_exercise_id_fkey FOREIGN KEY (exercise_id) REFERENCES public.exercises(id) ON DELETE CASCADE;


--
-- Name: user_favorite_exercises user_favorite_exercises_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_favorite_exercises
    ADD CONSTRAINT user_favorite_exercises_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: user_favorite_foods user_favorite_foods_food_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_favorite_foods
    ADD CONSTRAINT user_favorite_foods_food_id_fkey FOREIGN KEY (food_id) REFERENCES public.foods(id) ON DELETE CASCADE;


--
-- Name: user_favorite_foods user_favorite_foods_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_favorite_foods
    ADD CONSTRAINT user_favorite_foods_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: user_settings user_settings_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_settings
    ADD CONSTRAINT user_settings_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON DELETE CASCADE;


--
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: postgres
--

REVOKE USAGE ON SCHEMA public FROM PUBLIC;


--
-- PostgreSQL database dump complete
--

