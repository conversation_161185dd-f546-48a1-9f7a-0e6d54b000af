from sqlalchemy.orm import Session
import asyncio
from datetime import datetime

from app import models, schemas
from app.db.session import SessionLocal
from app.services.community_service import CommunityService
from app.services.workout_summary_service import WorkoutSummaryService
from app.schemas.community import WorkoutShareCreate, WorkoutDataSchema, WorkoutExerciseItem, WorkoutExerciseSet

async def test_share_workout():
    """测试训练记录分享功能"""
    print("开始测试训练记录分享功能...")
    db = SessionLocal()
    
    try:
        # 检查用户是否存在
        user = db.query(models.User).filter(models.User.id == 1).first()
        if not user:
            print("错误: ID为1的用户不存在")
            return
        
        # 创建训练数据
        workout_data = WorkoutDataSchema(
            exercises=[
                WorkoutExerciseItem(
                    name="深蹲",
                    imageUrl="/exercises/images/squat.jpg",
                    category="腿部",
                    sets=[
                        WorkoutExerciseSet(
                            type="normal",
                            weight=60.0,
                            reps=12,
                            completed=True
                        ),
                        WorkoutExerciseSet(
                            type="normal", 
                            weight=70.0,
                            reps=10,
                            completed=True
                        )
                    ],
                    hasCompletedSets=True,
                    totalVolume=1300,
                    completedSets=2
                )
            ],
            duration_seconds=1800,
            total_sets=2,
            total_volume=1300
        )
        
        # 创建分享数据
        share_data = WorkoutShareCreate(
            title=f"测试训练记录 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            content="直接通过服务测试创建的训练记录",
            visibility="Everyone",
            tags=["测试", "直接API", "训练"],
            workout_data=workout_data
        )
        
        # 创建服务实例
        workout_summary_service = WorkoutSummaryService()
        community_service = CommunityService(db)
        
        # 调用创建或更新方法
        result = await workout_summary_service.create_or_update_workout_and_summary(
            db=db,
            user_id=1,
            workout_id=None,
            data={
                "title": share_data.title,
                "content": share_data.content,
                "name": "测试训练记录",
                "description": "测试描述",
                "workout_data": share_data.workout_data.model_dump(),
                "visibility": share_data.visibility,
                "tags": share_data.tags
            }
        )
        
        workout = result.get("workout")
        daily_workout = result.get("daily_workout")
        
        if not daily_workout:
            print("错误: 无法创建训练记录")
            return
        
        # 创建关联的帖子
        post_data = schemas.community.PostCreate(
            title=share_data.title,
            content=share_data.content,
            daily_workout_id=daily_workout.id,
            visibility=share_data.visibility,
            tags=share_data.tags
        )
        
        post = await community_service.create_post_with_workout(1, post_data)
        print(f"成功创建测试帖子! Post ID: {post.id}")
        print(f"标题: {post.title}")
        print(f"内容: {post.content}")
        print(f"标签: {post.tags}")
        print(f"关联的DailyWorkout ID: {daily_workout.id}")
        print(f"关联的Workout ID: {workout.id}")
        
    except Exception as e:
        print(f"测试过程中发生错误: {str(e)}")
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_share_workout()) 