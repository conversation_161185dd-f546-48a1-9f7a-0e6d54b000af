#!/usr/bin/env python3
"""
简化的训练模型迁移脚本 - 避免模型依赖问题
"""

import os
import sys
import logging
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 数据库连接配置
DATABASE_URL = "postgresql://postgres:!scienceFit0219@127.0.0.1:5432/fitness_db"

def get_db_session():
    """获取数据库会话"""
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    return SessionLocal()

def migrate_table_structure():
    """执行表结构迁移"""
    db = get_db_session()
    try:
        logger.info("开始表结构迁移...")

        # 1. 重命名表
        try:
            db.execute(text("ALTER TABLE training_templates RENAME TO workout_templates;"))
            db.commit()
            logger.info("✓ 表重命名完成: training_templates -> workout_templates")
        except Exception as e:
            db.rollback()
            if "does not exist" in str(e) or "already exists" in str(e):
                logger.info("✓ workout_templates 表已存在，跳过重命名")
            else:
                logger.warning(f"表重命名警告: {e}")

        # 2. 更新字段
        try:
            db.execute(text("ALTER TABLE workout_templates RENAME COLUMN template_name TO name;"))
            db.commit()
            logger.info("✓ 字段重命名完成: template_name -> name")
        except Exception as e:
            db.rollback()
            if "does not exist" in str(e):
                logger.info("✓ name 字段已存在，跳过重命名")
            else:
                logger.warning(f"字段重命名警告: {e}")

        # 3. 添加新字段
        new_columns = [
            ("description", "TEXT"),
            ("estimated_duration", "SMALLINT"),
            ("target_body_parts", "INTEGER[]"),
            ("training_scenario", "VARCHAR(20)")
        ]

        for col_name, col_type in new_columns:
            try:
                db.execute(text(f"ALTER TABLE workout_templates ADD COLUMN IF NOT EXISTS {col_name} {col_type};"))
                db.commit()
                logger.info(f"✓ 添加字段: {col_name}")
            except Exception as e:
                db.rollback()
                logger.warning(f"添加字段 {col_name} 警告: {e}")

        # 4. 添加新的关联字段到 workout_exercises
        try:
            db.execute(text("ALTER TABLE workout_exercises ADD COLUMN IF NOT EXISTS template_id INTEGER REFERENCES workout_templates(id);"))
            db.commit()
            logger.info("✓ 添加字段: workout_exercises.template_id")
        except Exception as e:
            db.rollback()
            logger.warning(f"添加 template_id 字段警告: {e}")

        # 5. 创建索引
        try:
            db.execute(text("CREATE INDEX IF NOT EXISTS idx_workout_exercises_template_id ON workout_exercises(template_id);"))
            db.commit()
            logger.info("✓ 创建索引: idx_workout_exercises_template_id")
        except Exception as e:
            db.rollback()
            logger.warning(f"创建索引警告: {e}")

        logger.info("表结构迁移完成")

    except Exception as e:
        db.rollback()
        logger.error(f"表结构迁移失败: {str(e)}")
        raise
    finally:
        db.close()

def migrate_template_data():
    """迁移训练模板数据"""
    db = get_db_session()
    try:
        logger.info("开始迁移训练模板数据...")

        # 查询所有模板（使用原始SQL）
        result = db.execute(text("SELECT id, user_id, exercises FROM workout_templates WHERE exercises IS NOT NULL;"))
        templates = result.fetchall()

        migrated_count = 0

        for template_row in templates:
            template_id, user_id, exercises_json = template_row

            if not exercises_json:
                continue

            try:
                import json
                exercises = json.loads(exercises_json) if isinstance(exercises_json, str) else exercises_json

                for idx, exercise_data in enumerate(exercises):
                    exercise_id = exercise_data.get('exerciseId') or exercise_data.get('exercise_id')

                    if not exercise_id:
                        logger.warning(f"模板 {template_id} 中的动作缺少 exercise_id，跳过")
                        continue

                    # 检查动作是否存在
                    exercise_exists = db.execute(
                        text("SELECT 1 FROM exercises WHERE id = :exercise_id"),
                        {"exercise_id": exercise_id}
                    ).fetchone()

                    if not exercise_exists:
                        logger.warning(f"动作 {exercise_id} 不存在，跳过")
                        continue

                    # 处理 sets 数据 - 如果是列表，取长度；如果是数字，直接使用
                    sets_value = exercise_data.get('sets', 3)
                    if isinstance(sets_value, list):
                        sets_count = len(sets_value)
                    else:
                        sets_count = int(sets_value) if sets_value else 3

                    # 插入 WorkoutExercise 记录
                    db.execute(text("""
                        INSERT INTO workout_exercises (
                            template_id, exercise_id, sets, reps, weight,
                            rest_seconds, "order", notes, exercise_type
                        ) VALUES (
                            :template_id, :exercise_id, :sets, :reps, :weight,
                            :rest_seconds, :order, :notes, :exercise_type
                        )
                    """), {
                        "template_id": template_id,
                        "exercise_id": exercise_id,
                        "sets": sets_count,
                        "reps": str(exercise_data.get('reps', '10')),
                        "weight": exercise_data.get('weight'),
                        "rest_seconds": exercise_data.get('rest_seconds', 60),
                        "order": idx + 1,
                        "notes": exercise_data.get('notes', ''),
                        "exercise_type": exercise_data.get('exercise_type', 'weight_reps')
                    })
                    migrated_count += 1

            except (json.JSONDecodeError, TypeError) as e:
                logger.error(f"解析模板 {template_id} 的 exercises JSON 失败: {str(e)}")
                continue

        # 清空原来的 exercises JSON 字段
        db.execute(text("UPDATE workout_templates SET exercises = NULL;"))

        db.commit()
        logger.info(f"训练模板数据迁移完成，共迁移 {migrated_count} 个动作")

    except Exception as e:
        db.rollback()
        logger.error(f"训练模板数据迁移失败: {str(e)}")
        raise
    finally:
        db.close()

def migrate_user_training_records():
    """迁移用户训练记录"""
    db = get_db_session()
    try:
        logger.info("开始迁移用户训练记录...")

        # 查询所有用户训练记录
        result = db.execute(text("""
            SELECT id, user_id, exercise_id, date, sets, total_sets, notes, created_at, updated_at
            FROM user_training_plan_records
        """))
        records = result.fetchall()

        migrated_count = 0

        for record in records:
            record_id, user_id, exercise_id, date, sets_json, total_sets, notes, created_at, updated_at = record

            try:
                # 创建独立的 WorkoutExercise
                workout_exercise_result = db.execute(text("""
                    INSERT INTO workout_exercises (
                        exercise_id, sets, reps
                    ) VALUES (
                        :exercise_id, :sets, :reps
                    ) RETURNING id
                """), {
                    "exercise_id": exercise_id,
                    "sets": total_sets or 3,
                    "reps": "10"  # 默认值
                })

                workout_exercise_id = workout_exercise_result.fetchone()[0]
                db.commit()  # 提交 WorkoutExercise 创建

                # 迁移 sets JSON 数据到 SetRecord
                if sets_json:
                    import json
                    sets_data = sets_json if isinstance(sets_json, list) else json.loads(sets_json) if sets_json else []

                    for set_idx, set_data in enumerate(sets_data):
                        if isinstance(set_data, dict):
                            db.execute(text("""
                                INSERT INTO set_records (
                                    workout_exercise_id, set_number, set_type, weight, reps, completed, notes, created_at, updated_at
                                ) VALUES (
                                    :workout_exercise_id, :set_number, :set_type, :weight, :reps, :completed, :notes, :created_at, :updated_at
                                )
                            """), {
                                "workout_exercise_id": workout_exercise_id,
                                "set_number": set_idx + 1,
                                "set_type": "normal",  # 添加默认的 set_type
                                "weight": float(set_data.get('weight', 0)) if set_data.get('weight') else None,
                                "reps": int(set_data.get('reps', 0)) if set_data.get('reps') else None,
                                "completed": set_data.get('completed', False),
                                "notes": set_data.get('notes'),
                                "created_at": created_at,
                                "updated_at": updated_at
                            })
                            migrated_count += 1

                db.commit()  # 提交这个记录的所有 SetRecord

            except Exception as e:
                db.rollback()
                logger.error(f"迁移记录 {record_id} 失败: {str(e)}")
                continue

        logger.info(f"用户训练记录迁移完成，共迁移 {migrated_count} 个组记录")

    except Exception as e:
        db.rollback()
        logger.error(f"用户训练记录迁移失败: {str(e)}")
        raise
    finally:
        db.close()

def main():
    """运行完整的数据迁移"""
    try:
        logger.info("开始训练模型重构数据迁移...")

        # 阶段1：表结构迁移（已完成）
        # migrate_table_structure()

        # 阶段2：训练模板数据迁移（已完成）
        # migrate_template_data()

        # 阶段3：用户训练记录迁移
        migrate_user_training_records()

        logger.info("🎉 所有数据迁移完成！")

    except Exception as e:
        logger.error(f"数据迁移失败: {str(e)}")
        raise

if __name__ == "__main__":
    main()
