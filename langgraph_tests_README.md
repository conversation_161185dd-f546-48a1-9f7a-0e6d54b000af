# LangGraph AI Agent 测试指南

本文档提供了如何运行LangGraph AI Agent测试的详细说明。这些测试用于验证基于LangGraph框架实现的健身AI助手功能。

## 测试文件概述

测试文件分为以下几类：

1. **基本功能测试** (`test_langgraph_basic.py`)
   - 测试LangGraph服务的初始化
   - 测试消息处理功能
   - 测试状态管理
   - 测试缓存机制

2. **专家节点测试** (`test_langgraph_expert_nodes.py`)
   - 测试路由节点的意图识别功能
   - 测试参数收集器的参数提取功能
   - 测试训练计划专家节点的计划生成功能
   - 测试健身咨询专家节点的回答功能
   - 测试通用聊天专家节点的回答功能

3. **WebSocket接口测试** (`test_langgraph_websocket.py`)
   - 测试WebSocket连接
   - 测试消息发送和接收
   - 测试流式响应处理
   - 测试元数据更新
   - 测试训练计划数据接收

4. **状态管理测试** (`test_langgraph_state_management.py`)
   - 测试会话状态的持久化和恢复
   - 测试消息历史压缩功能
   - 测试缓存机制
   - 测试中断处理机制

5. **综合测试** (`test_langgraph_agent.py`)
   - 使用pytest框架进行更全面的测试
   - 包含REST API和WebSocket API的测试
   - 包含意图识别的测试

6. **测试运行脚本** (`run_langgraph_tests.py`)
   - 提供统一的入口运行所有测试
   - 支持选择性运行特定类型的测试

## 运行测试

### 前提条件

1. 确保已激活Python虚拟环境：
   ```bash
   source .venv/bin/activate
   ```

2. 确保已安装所有依赖：
   ```bash
   pip install -r requirements.txt
   ```

3. 确保数据库服务已启动并配置正确。

### 运行所有测试

使用测试运行脚本运行所有测试：

```bash
python tests/run_langgraph_tests.py
```

### 运行特定类型的测试

可以使用`--test`参数指定要运行的测试类型：

```bash
# 运行基本功能测试
python tests/run_langgraph_tests.py --test basic

# 运行专家节点测试
python tests/run_langgraph_tests.py --test expert

# 运行WebSocket接口测试
python tests/run_langgraph_tests.py --test websocket

# 运行状态管理测试
python tests/run_langgraph_tests.py --test state
```

### 使用pytest运行测试

对于`test_langgraph_agent.py`，可以使用pytest运行：

```bash
# 安装pytest
pip install pytest pytest-asyncio

# 运行测试
pytest tests/test_langgraph_agent.py -v
```

## 测试结果解释

测试脚本会输出详细的测试过程和结果，包括：

1. 发送的消息
2. 接收的响应
3. 识别的意图
4. 元数据更新
5. 处理时间
6. 错误信息（如果有）

如果测试成功，脚本将正常结束并显示"所有测试完成"。如果测试失败，将显示错误信息。

## 注意事项

1. **测试用户**：测试脚本使用ID为15的用户进行测试。确保数据库中存在此ID的用户，否则测试将失败。

2. **WebSocket测试**：WebSocket测试需要应用服务器正在运行。确保在运行测试前已启动应用服务器。

3. **API地址**：测试脚本默认使用`http://localhost:8000`作为API地址。如果应用服务器使用不同的地址，请修改测试脚本中的`BASE_URL`和`WS_BASE_URL`变量。

4. **认证**：测试脚本使用用户名和密码获取认证令牌。确保认证系统正常工作，并且测试用户有权限访问相关API。

5. **并发限制**：某些测试可能会发送多个并发请求。如果应用服务器有并发限制，可能会导致测试失败。

## 故障排除

如果测试失败，请检查以下几点：

1. 确保应用服务器正在运行并可访问。
2. 确保数据库服务正在运行并配置正确。
3. 检查日志输出，查找具体的错误信息。
4. 确保测试用户存在并有权限访问相关API。
5. 如果WebSocket测试失败，确保WebSocket端点正确配置并可访问。

## 扩展测试

如需添加新的测试，可以：

1. 在现有测试文件中添加新的测试函数。
2. 创建新的测试文件，遵循现有测试文件的结构。
3. 更新`run_langgraph_tests.py`，添加对新测试的支持。

## 联系方式

如有任何问题或建议，请联系开发团队。
