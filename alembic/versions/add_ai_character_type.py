"""添加AI角色类型字段

Revision ID: add_ai_character_type
Revises:
Create Date: 2023-07-01 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_ai_character_type'
down_revision = '83ad57d636c3'
branch_labels = None
depends_on = None


def upgrade():
    # 添加ai_character_type字段到user_settings表
    op.add_column('user_settings', sa.Column('ai_character_type', sa.String(), nullable=True, server_default='motivational'))


def downgrade():
    # 删除ai_character_type字段
    op.drop_column('user_settings', 'ai_character_type')
