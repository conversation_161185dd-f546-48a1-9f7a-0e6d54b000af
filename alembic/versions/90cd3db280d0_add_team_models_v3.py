"""add_team_models_v3

Revision ID: 90cd3db280d0
Revises: 9adbb60f176d
Create Date: 2025-04-29 17:29:39.791948

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '90cd3db280d0'
down_revision = '9adbb60f176d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('teams',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('owner_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('settings', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['owner_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_teams_id'), 'teams', ['id'], unique=False)
    op.create_index(op.f('ix_teams_name'), 'teams', ['name'], unique=False)
    op.create_table('client_relations',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('team_id', sa.Integer(), nullable=True),
    sa.Column('client_id', sa.Integer(), nullable=True),
    sa.Column('coach_id', sa.Integer(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['client_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['coach_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['team_id'], ['teams.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_client_relations_id'), 'client_relations', ['id'], unique=False)
    op.create_table('team_invitations',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('team_id', sa.Integer(), nullable=True),
    sa.Column('inviter_id', sa.Integer(), nullable=True),
    sa.Column('invitee_id', sa.Integer(), nullable=True),
    sa.Column('role', sa.String(length=20), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('expired_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['invitee_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['inviter_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['team_id'], ['teams.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_team_invitations_id'), 'team_invitations', ['id'], unique=False)
    op.create_table('team_memberships',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('team_id', sa.Integer(), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('role', sa.String(length=20), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('joined_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['team_id'], ['teams.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('team_id', 'user_id', name='uq_team_user')
    )
    op.create_index(op.f('ix_team_memberships_id'), 'team_memberships', ['id'], unique=False)
    op.create_table('team_stats',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('team_id', sa.Integer(), nullable=True),
    sa.Column('total_members', sa.Integer(), nullable=True),
    sa.Column('total_clients', sa.Integer(), nullable=True),
    sa.Column('active_clients_30d', sa.Integer(), nullable=True),
    sa.Column('total_sessions', sa.Integer(), nullable=True),
    sa.Column('completed_sessions', sa.Integer(), nullable=True),
    sa.Column('completion_rate', sa.Float(), nullable=True),
    sa.Column('growth_rate', sa.Float(), nullable=True),
    sa.Column('monthly_stats', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['team_id'], ['teams.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('team_id')
    )
    op.create_index(op.f('ix_team_stats_id'), 'team_stats', ['id'], unique=False)
    op.create_table('training_plan_templates',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('creator_id', sa.Integer(), nullable=True),
    sa.Column('team_id', sa.Integer(), nullable=True),
    sa.Column('duration_weeks', sa.Integer(), nullable=True),
    sa.Column('sessions_per_week', sa.Integer(), nullable=True),
    sa.Column('difficulty_level', sa.Integer(), nullable=True),
    sa.Column('target_audience', sa.String(), nullable=True),
    sa.Column('equipment_required', sa.ARRAY(sa.Integer()), nullable=True),
    sa.Column('is_public', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['creator_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['team_id'], ['teams.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_training_plan_templates_id'), 'training_plan_templates', ['id'], unique=False)
    op.create_table('client_training_plans',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('client_relation_id', sa.Integer(), nullable=True),
    sa.Column('training_plan_id', sa.Integer(), nullable=True),
    sa.Column('coach_id', sa.Integer(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('start_date', sa.DateTime(), nullable=False),
    sa.Column('end_date', sa.DateTime(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('scheduled_time', sa.JSON(), nullable=True),
    sa.Column('next_session', sa.DateTime(), nullable=True),
    sa.Column('completion_rate', sa.Float(), nullable=True),
    sa.Column('last_workout_date', sa.DateTime(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['client_relation_id'], ['client_relations.id'], ),
    sa.ForeignKeyConstraint(['coach_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['training_plan_id'], ['training_plans.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('client_relation_id', 'training_plan_id', 'start_date', name='uq_client_plan_period')
    )
    op.create_index(op.f('ix_client_training_plans_id'), 'client_training_plans', ['id'], unique=False)
    op.create_table('client_transfer_history',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('client_relation_id', sa.Integer(), nullable=True),
    sa.Column('from_coach_id', sa.Integer(), nullable=True),
    sa.Column('to_coach_id', sa.Integer(), nullable=True),
    sa.Column('reason', sa.String(), nullable=True),
    sa.Column('transferred_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['client_relation_id'], ['client_relations.id'], ),
    sa.ForeignKeyConstraint(['from_coach_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['to_coach_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_client_transfer_history_id'), 'client_transfer_history', ['id'], unique=False)
    op.create_table('template_exercises',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('template_id', sa.Integer(), nullable=True),
    sa.Column('exercise_id', sa.Integer(), nullable=True),
    sa.Column('day_number', sa.Integer(), nullable=True),
    sa.Column('order', sa.Integer(), nullable=True),
    sa.Column('sets', sa.Integer(), nullable=True),
    sa.Column('reps', sa.Integer(), nullable=True),
    sa.Column('rest_seconds', sa.Integer(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['exercise_id'], ['exercises.id'], ),
    sa.ForeignKeyConstraint(['template_id'], ['training_plan_templates.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_template_exercises_id'), 'template_exercises', ['id'], unique=False)
    op.create_table('training_sessions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('client_plan_id', sa.Integer(), nullable=True),
    sa.Column('scheduled_start', sa.DateTime(), nullable=False),
    sa.Column('actual_start', sa.DateTime(), nullable=True),
    sa.Column('actual_end', sa.DateTime(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('completion_rate', sa.Float(), nullable=True),
    sa.Column('feedback', sa.Text(), nullable=True),
    sa.Column('mood_rating', sa.Integer(), nullable=True),
    sa.Column('difficulty_rating', sa.Integer(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['client_plan_id'], ['client_training_plans.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_training_sessions_id'), 'training_sessions', ['id'], unique=False)
    op.create_table('session_exercise_records',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('session_id', sa.Integer(), nullable=True),
    sa.Column('exercise_id', sa.Integer(), nullable=True),
    sa.Column('order', sa.Integer(), nullable=True),
    sa.Column('sets_planned', sa.Integer(), nullable=True),
    sa.Column('sets_completed', sa.Integer(), nullable=True),
    sa.Column('set_records', sa.JSON(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.ForeignKeyConstraint(['exercise_id'], ['exercises.id'], ),
    sa.ForeignKeyConstraint(['session_id'], ['training_sessions.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_session_exercise_records_id'), 'session_exercise_records', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_session_exercise_records_id'), table_name='session_exercise_records')
    op.drop_table('session_exercise_records')
    op.drop_index(op.f('ix_training_sessions_id'), table_name='training_sessions')
    op.drop_table('training_sessions')
    op.drop_index(op.f('ix_template_exercises_id'), table_name='template_exercises')
    op.drop_table('template_exercises')
    op.drop_index(op.f('ix_client_transfer_history_id'), table_name='client_transfer_history')
    op.drop_table('client_transfer_history')
    op.drop_index(op.f('ix_client_training_plans_id'), table_name='client_training_plans')
    op.drop_table('client_training_plans')
    op.drop_index(op.f('ix_training_plan_templates_id'), table_name='training_plan_templates')
    op.drop_table('training_plan_templates')
    op.drop_index(op.f('ix_team_stats_id'), table_name='team_stats')
    op.drop_table('team_stats')
    op.drop_index(op.f('ix_team_memberships_id'), table_name='team_memberships')
    op.drop_table('team_memberships')
    op.drop_index(op.f('ix_team_invitations_id'), table_name='team_invitations')
    op.drop_table('team_invitations')
    op.drop_index(op.f('ix_client_relations_id'), table_name='client_relations')
    op.drop_table('client_relations')
    op.drop_index(op.f('ix_teams_name'), table_name='teams')
    op.drop_index(op.f('ix_teams_id'), table_name='teams')
    op.drop_table('teams')
    # ### end Alembic commands ### 