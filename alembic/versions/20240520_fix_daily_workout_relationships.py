"""Fix daily workout relationships

Revision ID: 20240520_fix_daily_workout
Revises:
Create Date: 2024-05-20 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '20240520_fix_daily_workout'
down_revision = 'add_workout_status_fields'  # 当前的头部版本
branch_labels = None
depends_on = None


def upgrade():
    # 使用单独的连接执行每个操作，避免事务失败影响后续操作

    # 1. 删除 exercises 表中的 daily_workout_id 列（如果存在）
    conn = op.get_bind()
    inspector = sa.inspect(conn)

    # 检查 exercises 表中是否存在 daily_workout_id 列
    exercises_columns = [col['name'] for col in inspector.get_columns('exercises')]
    if 'daily_workout_id' in exercises_columns:
        try:
            op.drop_column('exercises', 'daily_workout_id')
            print("成功删除 exercises.daily_workout_id 列")
        except Exception as e:
            print(f"删除 exercises.daily_workout_id 列时出错: {e}")
    else:
        print("exercises 表中不存在 daily_workout_id 列，无需删除")

    # 2. 在 workout_exercises 表中添加 daily_workout_id 列（如果不存在）
    # 重新获取连接，避免之前的错误影响
    conn = op.get_bind()
    inspector = sa.inspect(conn)

    # 检查 workout_exercises 表中是否存在 daily_workout_id 列
    workout_exercises_columns = [col['name'] for col in inspector.get_columns('workout_exercises')]
    if 'daily_workout_id' not in workout_exercises_columns:
        try:
            # 使用 execute 直接执行 SQL，避免事务问题
            conn.execute(sa.text("ALTER TABLE workout_exercises ADD COLUMN daily_workout_id INTEGER"))
            conn.execute(sa.text(
                "ALTER TABLE workout_exercises ADD CONSTRAINT fk_workout_exercises_daily_workout_id " +
                "FOREIGN KEY (daily_workout_id) REFERENCES daily_workouts (id)"
            ))
            print("成功添加 workout_exercises.daily_workout_id 列和外键约束")
        except Exception as e:
            print(f"添加 workout_exercises.daily_workout_id 列时出错: {e}")
    else:
        print("workout_exercises 表中已存在 daily_workout_id 列，无需添加")

    # 3. 确保 workout_id 列可以为 NULL
    # 重新获取连接，避免之前的错误影响
    conn = op.get_bind()

    # 检查 workout_id 列是否可为 NULL
    try:
        # 使用 execute 直接执行 SQL，避免事务问题
        conn.execute(sa.text("ALTER TABLE workout_exercises ALTER COLUMN workout_id DROP NOT NULL"))
        print("成功修改 workout_exercises.workout_id 列为可空")
    except Exception as e:
        print(f"修改 workout_exercises.workout_id 列为可空时出错: {e}")
        print("workout_id 列可能已经是可空的，或者存在其他约束")


def downgrade():
    # 使用单独的连接执行每个操作，避免事务失败影响后续操作

    # 1. 恢复 workout_id 列为非空
    conn = op.get_bind()
    try:
        conn.execute(sa.text("ALTER TABLE workout_exercises ALTER COLUMN workout_id SET NOT NULL"))
        print("成功将 workout_exercises.workout_id 列设置为非空")
    except Exception as e:
        print(f"将 workout_exercises.workout_id 列设置为非空时出错: {e}")

    # 2. 删除 workout_exercises 表中的 daily_workout_id 列
    conn = op.get_bind()
    inspector = sa.inspect(conn)

    # 检查 workout_exercises 表中是否存在 daily_workout_id 列
    workout_exercises_columns = [col['name'] for col in inspector.get_columns('workout_exercises')]
    if 'daily_workout_id' in workout_exercises_columns:
        try:
            # 先删除外键约束
            conn.execute(sa.text(
                "ALTER TABLE workout_exercises DROP CONSTRAINT IF EXISTS fk_workout_exercises_daily_workout_id"
            ))
            # 再删除列
            conn.execute(sa.text("ALTER TABLE workout_exercises DROP COLUMN daily_workout_id"))
            print("成功删除 workout_exercises.daily_workout_id 列和外键约束")
        except Exception as e:
            print(f"删除 workout_exercises.daily_workout_id 列时出错: {e}")
    else:
        print("workout_exercises 表中不存在 daily_workout_id 列，无需删除")

    # 3. 在 exercises 表中添加 daily_workout_id 列
    conn = op.get_bind()
    inspector = sa.inspect(conn)

    # 检查 exercises 表中是否存在 daily_workout_id 列
    exercises_columns = [col['name'] for col in inspector.get_columns('exercises')]
    if 'daily_workout_id' not in exercises_columns:
        try:
            # 添加列
            conn.execute(sa.text("ALTER TABLE exercises ADD COLUMN daily_workout_id INTEGER"))
            # 添加外键约束
            conn.execute(sa.text(
                "ALTER TABLE exercises ADD CONSTRAINT fk_exercises_daily_workout_id " +
                "FOREIGN KEY (daily_workout_id) REFERENCES daily_workouts (id)"
            ))
            print("成功添加 exercises.daily_workout_id 列和外键约束")
        except Exception as e:
            print(f"添加 exercises.daily_workout_id 列时出错: {e}")
    else:
        print("exercises 表中已存在 daily_workout_id 列，无需添加")
