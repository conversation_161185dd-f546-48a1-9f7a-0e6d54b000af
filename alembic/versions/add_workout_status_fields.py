"""Add workout status and timing fields

Revision ID: add_workout_status_fields
Revises: 1717bc2e18ca
Create Date: 2023-11-10

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_workout_status_fields'
down_revision = '1717bc2e18ca'  # 更新为当前head值
branch_labels = None
depends_on = None


def upgrade():
    # 添加新字段
    op.add_column('workouts', sa.Column('status', sa.String(20), nullable=False, server_default='not_started'))
    op.add_column('workouts', sa.Column('actual_duration', sa.SmallInteger(), nullable=True))
    op.add_column('workouts', sa.Column('net_duration', sa.SmallInteger(), nullable=True))
    op.add_column('workouts', sa.Column('start_time', sa.DateTime(timezone=True), nullable=True))
    op.add_column('workouts', sa.Column('end_time', sa.DateTime(timezone=True), nullable=True))
    op.add_column('workouts', sa.Column('last_status_change', sa.DateTime(timezone=True), nullable=True))
    op.add_column('workouts', sa.Column('pause_records', sa.JSON(), server_default='[]'))
    op.add_column('workouts', sa.Column('status_history', sa.JSON(), server_default='[]'))
    op.add_column('workouts', sa.Column('training_session_id', sa.Integer(), nullable=True))
    
    # 添加外键约束
    op.create_foreign_key(
        'fk_workout_training_session', 
        'workouts', 'training_sessions',
        ['training_session_id'], ['id']
    )


def downgrade():
    # 移除外键约束
    op.drop_constraint('fk_workout_training_session', 'workouts', type_='foreignkey')
    
    # 移除列
    op.drop_column('workouts', 'training_session_id')
    op.drop_column('workouts', 'status_history')
    op.drop_column('workouts', 'pause_records')
    op.drop_column('workouts', 'last_status_change')
    op.drop_column('workouts', 'end_time')
    op.drop_column('workouts', 'start_time')
    op.drop_column('workouts', 'net_duration')
    op.drop_column('workouts', 'actual_duration')
    op.drop_column('workouts', 'status') 