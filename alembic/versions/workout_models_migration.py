"""add workout models

Revision ID: workout_models_migration
Revises: 5585f0046701
Create Date: 2023-06-16 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'workout_models_migration'
down_revision = '5585f0046701'
branch_labels = None
depends_on = None


def upgrade():
    # TrainingPlan模型更新
    op.add_column('training_plans', sa.Column('plan_name', sa.String(length=100), nullable=False, server_default="个性化训练计划"))
    op.add_column('training_plans', sa.Column('fitness_goal', sa.SmallInteger(), nullable=True))
    op.add_column('training_plans', sa.Column('experience_level', sa.SmallInteger(), nullable=True))
    op.add_column('training_plans', sa.Column('duration_weeks', sa.SmallInteger(), nullable=True, server_default="4"))
    op.add_column('training_plans', sa.Column('is_active', sa.<PERSON>(), nullable=True, server_default="true"))
    op.add_column('training_plans', sa.Column('is_template', sa.<PERSON>(), nullable=False, server_default="false"))
    op.add_column('training_plans', sa.Column('privacy_setting', sa.SmallInteger(), nullable=False, server_default="1"))
    op.create_index(op.f('ix_training_plans_user_id'), 'training_plans', ['user_id'], unique=False)
    
    # Workout表
    op.create_table('workouts',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('training_plan_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('day_of_week', sa.SmallInteger(), nullable=True),
        sa.Column('day_number', sa.SmallInteger(), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('estimated_duration', sa.SmallInteger(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['training_plan_id'], ['training_plans.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_workouts_id'), 'workouts', ['id'], unique=False)
    op.create_index(op.f('ix_workouts_training_plan_id'), 'workouts', ['training_plan_id'], unique=False)
    
    # WorkoutExercise表
    op.create_table('workout_exercises',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('workout_id', sa.Integer(), nullable=False),
        sa.Column('exercise_id', sa.Integer(), nullable=False),
        sa.Column('sets', sa.SmallInteger(), nullable=False),
        sa.Column('reps', sa.String(length=50), nullable=False),
        sa.Column('rest_seconds', sa.SmallInteger(), nullable=True),
        sa.Column('order', sa.SmallInteger(), nullable=False, server_default="0"),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.Column('exercise_type', sa.String(length=50), nullable=False, server_default="weight_reps"),
        sa.Column('superset_group', sa.Integer(), nullable=True),
        sa.Column('weight', sa.String(length=50), nullable=True),
        sa.ForeignKeyConstraint(['exercise_id'], ['exercises.id'], ),
        sa.ForeignKeyConstraint(['workout_id'], ['workouts.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_workout_exercises_exercise_id'), 'workout_exercises', ['exercise_id'], unique=False)
    op.create_index(op.f('ix_workout_exercises_id'), 'workout_exercises', ['id'], unique=False)
    op.create_index(op.f('ix_workout_exercises_superset_group'), 'workout_exercises', ['superset_group'], unique=False)
    op.create_index(op.f('ix_workout_exercises_workout_id'), 'workout_exercises', ['workout_id'], unique=False)


def downgrade():
    # WorkoutExercise表
    op.drop_index(op.f('ix_workout_exercises_workout_id'), table_name='workout_exercises')
    op.drop_index(op.f('ix_workout_exercises_superset_group'), table_name='workout_exercises')
    op.drop_index(op.f('ix_workout_exercises_id'), table_name='workout_exercises')
    op.drop_index(op.f('ix_workout_exercises_exercise_id'), table_name='workout_exercises')
    op.drop_table('workout_exercises')
    
    # Workout表
    op.drop_index(op.f('ix_workouts_training_plan_id'), table_name='workouts')
    op.drop_index(op.f('ix_workouts_id'), table_name='workouts')
    op.drop_table('workouts')
    
    # TrainingPlan字段
    op.drop_index(op.f('ix_training_plans_user_id'), table_name='training_plans')
    op.drop_column('training_plans', 'privacy_setting')
    op.drop_column('training_plans', 'is_template')
    op.drop_column('training_plans', 'is_active')
    op.drop_column('training_plans', 'duration_weeks')
    op.drop_column('training_plans', 'experience_level')
    op.drop_column('training_plans', 'fitness_goal')
    op.drop_column('training_plans', 'plan_name') 