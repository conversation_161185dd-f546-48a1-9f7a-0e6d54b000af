"""Add community models

Revision ID: 5b9d60f002e5
Revises: 46c86247b4e0
Create Date: 2025-04-28 20:25:56.759017

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '5b9d60f002e5'
down_revision = '46c86247b4e0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('daily_workouts',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('training_duration', sa.SmallInteger(), nullable=True),
    sa.Column('total_capacity', sa.SmallInteger(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_daily_workouts_id'), 'daily_workouts', ['id'], unique=False)
    op.create_table('posts',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=200), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('image_urls', sa.JSON(), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('related_workout_id', sa.Integer(), nullable=True),
    sa.Column('view_count', sa.Integer(), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('reported_count', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['related_workout_id'], ['daily_workouts.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_posts_created_at'), 'posts', ['created_at'], unique=False)
    op.create_index(op.f('ix_posts_id'), 'posts', ['id'], unique=False)
    op.create_index(op.f('ix_posts_related_workout_id'), 'posts', ['related_workout_id'], unique=False)
    op.create_index(op.f('ix_posts_status'), 'posts', ['status'], unique=False)
    op.create_index(op.f('ix_posts_user_id'), 'posts', ['user_id'], unique=False)
    op.create_table('share_tracks',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('shared_by', sa.Integer(), nullable=True),
    sa.Column('scanned_by', sa.Integer(), nullable=True),
    sa.Column('share_type', sa.String(), nullable=False),
    sa.Column('page', sa.String(), nullable=False),
    sa.Column('scene', sa.String(), nullable=True),
    sa.Column('qrcode_url', sa.String(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['scanned_by'], ['users.id'], ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['shared_by'], ['users.id'], ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_share_tracks_id'), 'share_tracks', ['id'], unique=False)
    op.create_table('comments',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('post_id', sa.Integer(), nullable=False),
    sa.Column('parent_id', sa.Integer(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('reported_count', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['parent_id'], ['comments.id'], ),
    sa.ForeignKeyConstraint(['post_id'], ['posts.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_comments_created_at'), 'comments', ['created_at'], unique=False)
    op.create_index(op.f('ix_comments_id'), 'comments', ['id'], unique=False)
    op.create_index(op.f('ix_comments_parent_id'), 'comments', ['parent_id'], unique=False)
    op.create_index(op.f('ix_comments_post_id'), 'comments', ['post_id'], unique=False)
    op.create_index(op.f('ix_comments_status'), 'comments', ['status'], unique=False)
    op.create_index(op.f('ix_comments_user_id'), 'comments', ['user_id'], unique=False)
    op.create_table('post_likes',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('post_id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['post_id'], ['posts.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id', 'post_id', name='_user_post_like_uc')
    )
    op.create_index(op.f('ix_post_likes_id'), 'post_likes', ['id'], unique=False)
    op.create_index(op.f('ix_post_likes_post_id'), 'post_likes', ['post_id'], unique=False)
    op.create_index(op.f('ix_post_likes_user_id'), 'post_likes', ['user_id'], unique=False)
    op.create_table('comment_likes',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('comment_id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['comment_id'], ['comments.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id', 'comment_id', name='_user_comment_like_uc')
    )
    op.create_index(op.f('ix_comment_likes_comment_id'), 'comment_likes', ['comment_id'], unique=False)
    op.create_index(op.f('ix_comment_likes_id'), 'comment_likes', ['id'], unique=False)
    op.create_index(op.f('ix_comment_likes_user_id'), 'comment_likes', ['user_id'], unique=False)
    op.create_table('notifications',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('type', sa.String(length=50), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('is_read', sa.Boolean(), nullable=True),
    sa.Column('related_post_id', sa.Integer(), nullable=True),
    sa.Column('related_comment_id', sa.Integer(), nullable=True),
    sa.Column('related_user_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['related_comment_id'], ['comments.id'], ),
    sa.ForeignKeyConstraint(['related_post_id'], ['posts.id'], ),
    sa.ForeignKeyConstraint(['related_user_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_notifications_created_at'), 'notifications', ['created_at'], unique=False)
    op.create_index(op.f('ix_notifications_id'), 'notifications', ['id'], unique=False)
    op.create_index(op.f('ix_notifications_is_read'), 'notifications', ['is_read'], unique=False)
    op.create_index(op.f('ix_notifications_type'), 'notifications', ['type'], unique=False)
    op.create_index(op.f('ix_notifications_user_id'), 'notifications', ['user_id'], unique=False)
    op.create_table('reports',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('reporter_id', sa.Integer(), nullable=False),
    sa.Column('reason', sa.Text(), nullable=False),
    sa.Column('post_id', sa.Integer(), nullable=True),
    sa.Column('comment_id', sa.Integer(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('resolved_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['comment_id'], ['comments.id'], ),
    sa.ForeignKeyConstraint(['post_id'], ['posts.id'], ),
    sa.ForeignKeyConstraint(['reporter_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_reports_comment_id'), 'reports', ['comment_id'], unique=False)
    op.create_index(op.f('ix_reports_id'), 'reports', ['id'], unique=False)
    op.create_index(op.f('ix_reports_post_id'), 'reports', ['post_id'], unique=False)
    op.create_index(op.f('ix_reports_reporter_id'), 'reports', ['reporter_id'], unique=False)
    op.create_index(op.f('ix_reports_status'), 'reports', ['status'], unique=False)
    op.drop_table('other_dietary_spl_ul')
    op.drop_table('nutrition_reference_main')
    op.drop_table('vitamin_pregnancy_inc')
    op.drop_table('mineral_pregnancy_inc')
    op.drop_table('nutrition_pregnancy_inc')
    op.drop_table('water_pregnancy_inc')
    op.drop_table('vitamin_rni')
    op.drop_table('water_rni')
    op.drop_index('idx_training_records_date', table_name='training_records')
    op.drop_index('idx_training_records_exercise_id', table_name='training_records')
    op.drop_index('idx_training_records_user_date', table_name='training_records')
    op.drop_index('idx_training_records_user_id', table_name='training_records')
    op.drop_table('training_records')
    op.drop_table('mineral_rni')
    op.add_column('workout_exercises', sa.Column('daily_workout_id', sa.Integer(), nullable=True))
    op.alter_column('workout_exercises', 'workout_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.alter_column('workout_exercises', 'sets',
               existing_type=sa.INTEGER(),
               type_=sa.SmallInteger(),
               nullable=False,
               existing_server_default=sa.text('3'))
    op.alter_column('workout_exercises', 'reps',
               existing_type=sa.VARCHAR(length=20),
               type_=sa.String(length=50),
               nullable=False,
               existing_server_default=sa.text("'10'::character varying"))
    op.alter_column('workout_exercises', 'rest_seconds',
               existing_type=sa.INTEGER(),
               type_=sa.SmallInteger(),
               existing_nullable=True,
               existing_server_default=sa.text('60'))
    op.alter_column('workout_exercises', 'order',
               existing_type=sa.INTEGER(),
               type_=sa.SmallInteger(),
               nullable=False,
               existing_server_default=sa.text('1'))
    op.alter_column('workout_exercises', 'exercise_type',
               existing_type=sa.VARCHAR(length=50),
               nullable=False,
               existing_server_default=sa.text("'weight_reps'::character varying"))
    op.create_index(op.f('ix_workout_exercises_daily_workout_id'), 'workout_exercises', ['daily_workout_id'], unique=False)
    op.create_index(op.f('ix_workout_exercises_exercise_id'), 'workout_exercises', ['exercise_id'], unique=False)
    op.create_index(op.f('ix_workout_exercises_id'), 'workout_exercises', ['id'], unique=False)
    op.create_index(op.f('ix_workout_exercises_superset_group'), 'workout_exercises', ['superset_group'], unique=False)
    op.create_index(op.f('ix_workout_exercises_workout_id'), 'workout_exercises', ['workout_id'], unique=False)
    op.drop_constraint('workout_exercises_workout_id_fkey', 'workout_exercises', type_='foreignkey')
    op.create_foreign_key(None, 'workout_exercises', 'workouts', ['workout_id'], ['id'])
    op.create_foreign_key(None, 'workout_exercises', 'daily_workouts', ['daily_workout_id'], ['id'])
    op.create_foreign_key(None, 'workout_exercises', 'exercises', ['exercise_id'], ['id'])
    op.drop_column('workout_exercises', 'updated_at')
    op.drop_column('workout_exercises', 'created_at')
    op.alter_column('workouts', 'name',
               existing_type=sa.VARCHAR(length=255),
               type_=sa.String(length=100),
               existing_nullable=False)
    op.alter_column('workouts', 'day_of_week',
               existing_type=sa.VARCHAR(length=20),
               type_=sa.SmallInteger(),
               existing_nullable=True)
    op.alter_column('workouts', 'day_number',
               existing_type=sa.INTEGER(),
               type_=sa.SmallInteger(),
               existing_nullable=False)
    op.alter_column('workouts', 'estimated_duration',
               existing_type=sa.INTEGER(),
               type_=sa.SmallInteger(),
               existing_nullable=True,
               existing_server_default=sa.text('60'))
    op.create_index(op.f('ix_workouts_id'), 'workouts', ['id'], unique=False)
    op.create_index(op.f('ix_workouts_training_plan_id'), 'workouts', ['training_plan_id'], unique=False)
    op.drop_constraint('workouts_training_plan_id_fkey', 'workouts', type_='foreignkey')
    op.create_foreign_key(None, 'workouts', 'training_plans', ['training_plan_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'workouts', type_='foreignkey')
    op.create_foreign_key('workouts_training_plan_id_fkey', 'workouts', 'training_plans', ['training_plan_id'], ['id'], ondelete='CASCADE')
    op.drop_index(op.f('ix_workouts_training_plan_id'), table_name='workouts')
    op.drop_index(op.f('ix_workouts_id'), table_name='workouts')
    op.alter_column('workouts', 'estimated_duration',
               existing_type=sa.SmallInteger(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               existing_server_default=sa.text('60'))
    op.alter_column('workouts', 'day_number',
               existing_type=sa.SmallInteger(),
               type_=sa.INTEGER(),
               existing_nullable=False)
    op.alter_column('workouts', 'day_of_week',
               existing_type=sa.SmallInteger(),
               type_=sa.VARCHAR(length=20),
               existing_nullable=True)
    op.alter_column('workouts', 'name',
               existing_type=sa.String(length=100),
               type_=sa.VARCHAR(length=255),
               existing_nullable=False)
    op.add_column('workout_exercises', sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True))
    op.add_column('workout_exercises', sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'workout_exercises', type_='foreignkey')
    op.drop_constraint(None, 'workout_exercises', type_='foreignkey')
    op.drop_constraint(None, 'workout_exercises', type_='foreignkey')
    op.create_foreign_key('workout_exercises_workout_id_fkey', 'workout_exercises', 'workouts', ['workout_id'], ['id'], ondelete='CASCADE')
    op.drop_index(op.f('ix_workout_exercises_workout_id'), table_name='workout_exercises')
    op.drop_index(op.f('ix_workout_exercises_superset_group'), table_name='workout_exercises')
    op.drop_index(op.f('ix_workout_exercises_id'), table_name='workout_exercises')
    op.drop_index(op.f('ix_workout_exercises_exercise_id'), table_name='workout_exercises')
    op.drop_index(op.f('ix_workout_exercises_daily_workout_id'), table_name='workout_exercises')
    op.alter_column('workout_exercises', 'exercise_type',
               existing_type=sa.VARCHAR(length=50),
               nullable=True,
               existing_server_default=sa.text("'weight_reps'::character varying"))
    op.alter_column('workout_exercises', 'order',
               existing_type=sa.SmallInteger(),
               type_=sa.INTEGER(),
               nullable=True,
               existing_server_default=sa.text('1'))
    op.alter_column('workout_exercises', 'rest_seconds',
               existing_type=sa.SmallInteger(),
               type_=sa.INTEGER(),
               existing_nullable=True,
               existing_server_default=sa.text('60'))
    op.alter_column('workout_exercises', 'reps',
               existing_type=sa.String(length=50),
               type_=sa.VARCHAR(length=20),
               nullable=True,
               existing_server_default=sa.text("'10'::character varying"))
    op.alter_column('workout_exercises', 'sets',
               existing_type=sa.SmallInteger(),
               type_=sa.INTEGER(),
               nullable=True,
               existing_server_default=sa.text('3'))
    op.alter_column('workout_exercises', 'workout_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.drop_column('workout_exercises', 'daily_workout_id')
    op.create_table('mineral_rni',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('age_start_years', sa.NUMERIC(precision=5, scale=2), autoincrement=False, nullable=False),
    sa.Column('sex', sa.VARCHAR(length=6), autoincrement=False, nullable=False),
    sa.Column('rec_type', sa.VARCHAR(length=3), autoincrement=False, nullable=False),
    sa.Column('calcium', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('calcium_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('phosphor', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('phosphor_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('kalium', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('kalium_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('natrium', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('natrium_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('magnesium', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('magnesium_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('chlorine', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('chlorine_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('iron', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('iron_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('iodine', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('iodine_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('zinc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('zinc_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('selenium', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('selenium_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('copper', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('copper_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('fluorine', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('fluorine_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('chromium', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('chromium_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('cobalt', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('cobalt_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('manganese', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('manganese_unit', sa.CHAR(length=10), server_default=sa.text("'mg'::bpchar"), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='mineral_rni_pkey')
    )
    op.create_table('training_records',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('exercise_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('sets', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=False),
    sa.Column('total_sets', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('total_reps', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('total_weight', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('total_duration', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('total_distance', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('note', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('date', sa.DATE(), server_default=sa.text('CURRENT_DATE'), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['exercise_id'], ['exercises.id'], name='training_records_exercise_id_fkey'),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name='training_records_user_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='training_records_pkey')
    )
    op.create_index('idx_training_records_user_id', 'training_records', ['user_id'], unique=False)
    op.create_index('idx_training_records_user_date', 'training_records', ['user_id', 'date'], unique=False)
    op.create_index('idx_training_records_exercise_id', 'training_records', ['exercise_id'], unique=False)
    op.create_index('idx_training_records_date', 'training_records', ['date'], unique=False)
    op.create_table('water_rni',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('sex', sa.VARCHAR(length=6), autoincrement=False, nullable=False),
    sa.Column('age_start_years', sa.NUMERIC(precision=5, scale=2), autoincrement=False, nullable=False),
    sa.Column('drinking_ml', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('total_water_ml', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='water_rni_pkey')
    )
    op.create_table('vitamin_rni',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('age_start_years', sa.NUMERIC(precision=5, scale=2), autoincrement=False, nullable=False),
    sa.Column('sex', sa.VARCHAR(length=6), autoincrement=False, nullable=False),
    sa.Column('rec_type', sa.VARCHAR(length=3), autoincrement=False, nullable=False),
    sa.Column('vitamin_a', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('vitamin_a_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('vitamin_d', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('vitamin_d_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('vitamin_e', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('vitamin_e_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('thiamine', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('thiamine_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('lactoflavin', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('lactoflavin_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('vitamin_b6', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('vitamin_b6_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('vitamin_b12', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('vitamin_b12_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('vitamin_c', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('vitamin_c_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('niacin', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('niacin_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('folacin', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('folacin_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('pantothenic', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('pantothenic_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('biotin', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('biotin_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('choline', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('choline_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='vitamin_rni_pkey')
    )
    op.create_table('water_pregnancy_inc',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('stage', sa.VARCHAR(length=16), autoincrement=False, nullable=False),
    sa.Column('sex', sa.VARCHAR(length=6), autoincrement=False, nullable=False),
    sa.Column('drinking_inc_ml', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('total_water_inc_ml', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='water_pregnancy_inc_pkey')
    )
    op.create_table('nutrition_pregnancy_inc',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('stage', sa.VARCHAR(length=16), autoincrement=False, nullable=False),
    sa.Column('sex', sa.VARCHAR(length=6), autoincrement=False, nullable=False),
    sa.Column('fat', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('fat_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('saturated_fat', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('saturated_fat_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('pufa', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('pufa_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('n3fa', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('n3fa_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('la', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('la_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('ala', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('ala_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('epa_dha', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('epa_dha_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('carbohydrate', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('carbohydrate_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('fiber_dietary', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('fiber_dietary_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('fructose', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('fructose_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('protein_ear', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('protein_ear_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('protein_rni', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('protein_rni_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('protein', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('protein_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='nutrition_pregnancy_inc_pkey')
    )
    op.create_table('mineral_pregnancy_inc',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('stage', sa.VARCHAR(length=16), autoincrement=False, nullable=False),
    sa.Column('calcium_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('phosphor_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('kalium_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('natrium_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('magnesium_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('chlorine_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('iron_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('iodine_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('zinc_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('selenium_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('copper_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('fluorine_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('chromium_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('cobalt_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='mineral_pregnancy_inc_pkey')
    )
    op.create_table('vitamin_pregnancy_inc',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('stage', sa.VARCHAR(length=16), autoincrement=False, nullable=False),
    sa.Column('vitamin_d_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('thiamine_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('lactoflavin_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('notes', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='vitamin_pregnancy_inc_pkey')
    )
    op.create_table('nutrition_reference_main',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('sex', sa.VARCHAR(length=6), autoincrement=False, nullable=False),
    sa.Column('age_start_years', sa.NUMERIC(precision=5, scale=2), autoincrement=False, nullable=False),
    sa.Column('fat', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('fat_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('saturated_fat', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('saturated_fat_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('pufa', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('pufa_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('n3fa', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('n3fa_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('la', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('la_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('ala', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('ala_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('epa_dha', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('epa_dha_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('carbohydrate_ear', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('carbohydrate_ear_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('carbohydrate', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('carbohydrate_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('fiber_dietary', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('fiber_dietary_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('fructose', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('fructose_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('protein_ear', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('protein_ear_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('protein_rni', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('protein_rni_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('protein', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('protein_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='nutrition_reference_main_pkey')
    )
    op.create_table('other_dietary_spl_ul',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name_cn', sa.VARCHAR(length=32), autoincrement=False, nullable=False),
    sa.Column('spl', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('spl_unit', sa.CHAR(length=10), autoincrement=False, nullable=True),
    sa.Column('ul', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('ul_unit', sa.CHAR(length=10), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='other_dietary_spl_ul_pkey')
    )
    op.drop_index(op.f('ix_reports_status'), table_name='reports')
    op.drop_index(op.f('ix_reports_reporter_id'), table_name='reports')
    op.drop_index(op.f('ix_reports_post_id'), table_name='reports')
    op.drop_index(op.f('ix_reports_id'), table_name='reports')
    op.drop_index(op.f('ix_reports_comment_id'), table_name='reports')
    op.drop_table('reports')
    op.drop_index(op.f('ix_notifications_user_id'), table_name='notifications')
    op.drop_index(op.f('ix_notifications_type'), table_name='notifications')
    op.drop_index(op.f('ix_notifications_is_read'), table_name='notifications')
    op.drop_index(op.f('ix_notifications_id'), table_name='notifications')
    op.drop_index(op.f('ix_notifications_created_at'), table_name='notifications')
    op.drop_table('notifications')
    op.drop_index(op.f('ix_comment_likes_user_id'), table_name='comment_likes')
    op.drop_index(op.f('ix_comment_likes_id'), table_name='comment_likes')
    op.drop_index(op.f('ix_comment_likes_comment_id'), table_name='comment_likes')
    op.drop_table('comment_likes')
    op.drop_index(op.f('ix_post_likes_user_id'), table_name='post_likes')
    op.drop_index(op.f('ix_post_likes_post_id'), table_name='post_likes')
    op.drop_index(op.f('ix_post_likes_id'), table_name='post_likes')
    op.drop_table('post_likes')
    op.drop_index(op.f('ix_comments_user_id'), table_name='comments')
    op.drop_index(op.f('ix_comments_status'), table_name='comments')
    op.drop_index(op.f('ix_comments_post_id'), table_name='comments')
    op.drop_index(op.f('ix_comments_parent_id'), table_name='comments')
    op.drop_index(op.f('ix_comments_id'), table_name='comments')
    op.drop_index(op.f('ix_comments_created_at'), table_name='comments')
    op.drop_table('comments')
    op.drop_index(op.f('ix_share_tracks_id'), table_name='share_tracks')
    op.drop_table('share_tracks')
    op.drop_index(op.f('ix_posts_user_id'), table_name='posts')
    op.drop_index(op.f('ix_posts_status'), table_name='posts')
    op.drop_index(op.f('ix_posts_related_workout_id'), table_name='posts')
    op.drop_index(op.f('ix_posts_id'), table_name='posts')
    op.drop_index(op.f('ix_posts_created_at'), table_name='posts')
    op.drop_table('posts')
    op.drop_index(op.f('ix_daily_workouts_id'), table_name='daily_workouts')
    op.drop_table('daily_workouts')
    # ### end Alembic commands ### 