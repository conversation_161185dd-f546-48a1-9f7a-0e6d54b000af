"""add search indexes for exercise tables

Revision ID: 20240415_search_indexes
Revises: 20240415_hit_time
Create Date: 2024-04-15

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from sqlalchemy import inspect


# revision identifiers, used by Alembic.
revision = '20240415_search_indexes'
down_revision = '20240415_hit_time'
branch_labels = None
depends_on = None


def upgrade():
    # 辅助函数，检查索引是否已存在
    conn = op.get_bind()
    
    # 创建索引前检查是否已存在
    def create_index_if_not_exists(index_name, table_name, columns, using=None, **kw):
        inspector = inspect(conn)
        indexes = inspector.get_indexes(table_name)
        if index_name not in [idx['name'] for idx in indexes]:
            # 创建索引，根据指定的参数
            if using:
                op.execute(f"CREATE INDEX {index_name} ON {table_name} USING {using} ({','.join(columns)})")
            else:
                op.create_index(index_name, table_name, columns, **kw)
            print(f"创建索引: {index_name}")
        else:
            print(f"索引已存在: {index_name}")
    
    # 添加GIN索引，用于优化数组字段查询
    try:
        create_index_if_not_exists('idx_body_part_id_gin', 'exercises', ['body_part_id'], 'gin')
        create_index_if_not_exists('idx_equipment_id_gin', 'exercises', ['equipment_id'], 'gin')
        
        # 添加函数索引，用于不区分大小写的搜索
        op.execute("CREATE INDEX IF NOT EXISTS idx_exercise_name_lower ON exercises (lower(name))")
        op.execute("CREATE INDEX IF NOT EXISTS idx_exercise_en_name_lower ON exercises (lower(en_name))")
        
        # 添加复合索引，用于优化排序
        create_index_if_not_exists('idx_exercise_sort_priority', 'exercises', ['sort_priority', 'id'])
        create_index_if_not_exists('idx_popular_exercises', 'exercises', ['hit_time', 'id'])
        
        # 添加ExerciseDetail索引
        create_index_if_not_exists('idx_target_muscles_gin', 'exercise_details', ['target_muscles_id'], 'gin')
        create_index_if_not_exists('idx_synergist_muscles_gin', 'exercise_details', ['synergist_muscles_id'], 'gin')
        create_index_if_not_exists('idx_exercise_detail_public', 'exercise_details', ['exercise_id', 'is_public'])
        
        # 添加基础表的索引
        op.execute("CREATE INDEX IF NOT EXISTS idx_muscle_name_lower ON muscles (lower(name))")
        op.execute("CREATE INDEX IF NOT EXISTS idx_body_part_name_lower ON body_parts (lower(name))")
        op.execute("CREATE INDEX IF NOT EXISTS idx_equipment_name_lower ON equipment (lower(name))")
    except Exception as e:
        print(f"创建索引时出错: {str(e)}")


def downgrade():
    # 删除GIN索引
    try:
        op.drop_index('idx_body_part_id_gin', table_name='exercises')
        op.drop_index('idx_equipment_id_gin', table_name='exercises')
        
        # 删除函数索引
        op.drop_index('idx_exercise_name_lower', table_name='exercises')
        op.drop_index('idx_exercise_en_name_lower', table_name='exercises')
        
        # 删除复合索引
        op.drop_index('idx_exercise_sort_priority', table_name='exercises')
        op.drop_index('idx_popular_exercises', table_name='exercises')
        
        # 删除ExerciseDetail索引
        op.drop_index('idx_target_muscles_gin', table_name='exercise_details')
        op.drop_index('idx_synergist_muscles_gin', table_name='exercise_details')
        op.drop_index('idx_exercise_detail_public', table_name='exercise_details')
        
        # 删除基础表的索引
        op.drop_index('idx_muscle_name_lower', table_name='muscles')
        op.drop_index('idx_body_part_name_lower', table_name='body_parts')
        op.drop_index('idx_equipment_name_lower', table_name='equipment')
    except Exception as e:
        print(f"删除索引时出错: {str(e)}") 