"""Add gamification models

Revision ID: d236fe4a90b1
Revises: add_ai_character_type
Create Date: 2025-05-22 18:57:27.229028

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'd236fe4a90b1'
down_revision = 'add_ai_character_type'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('cards',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('image_url', sa.String(), nullable=True),
    sa.Column('card_type', sa.Enum('FOOD', 'EQUIPMENT', 'SPECIAL', name='cardtype'), nullable=False),
    sa.Column('rarity', sa.Integer(), nullable=True),
    sa.Column('strength_bonus', sa.Integer(), nullable=True),
    sa.Column('endurance_bonus', sa.Integer(), nullable=True),
    sa.Column('flexibility_bonus', sa.Integer(), nullable=True),
    sa.Column('nutrition_knowledge_bonus', sa.Integer(), nullable=True),
    sa.Column('cooking_skill_bonus', sa.Integer(), nullable=True),
    sa.Column('diet_planning_bonus', sa.Integer(), nullable=True),
    sa.Column('duration_hours', sa.Integer(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_cards_id'), 'cards', ['id'], unique=False)
    op.create_index(op.f('ix_cards_name'), 'cards', ['name'], unique=False)
    op.create_table('achievements',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('image_url', sa.String(), nullable=True),
    sa.Column('category', sa.String(), nullable=False),
    sa.Column('requirement_type', sa.String(), nullable=False),
    sa.Column('requirement_value', sa.Integer(), nullable=False),
    sa.Column('currency_reward', sa.Integer(), nullable=True),
    sa.Column('experience_reward_type', sa.String(), nullable=True),
    sa.Column('experience_reward_value', sa.Integer(), nullable=True),
    sa.Column('card_reward_id', sa.Integer(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('is_hidden', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['card_reward_id'], ['cards.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_achievements_id'), 'achievements', ['id'], unique=False)
    op.create_table('card_synthesis_recipes',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('result_card_id', sa.Integer(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['result_card_id'], ['cards.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_card_synthesis_recipes_id'), 'card_synthesis_recipes', ['id'], unique=False)
    op.create_index(op.f('ix_card_synthesis_recipes_result_card_id'), 'card_synthesis_recipes', ['result_card_id'], unique=False)
    op.create_table('currencies',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('amount', sa.Integer(), nullable=True),
    sa.Column('lifetime_earned', sa.Integer(), nullable=True),
    sa.Column('daily_earned_today', sa.Integer(), nullable=True),
    sa.Column('last_reset_date', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_currencies_id'), 'currencies', ['id'], unique=False)
    op.create_index(op.f('ix_currencies_user_id'), 'currencies', ['user_id'], unique=True)
    op.create_table('daily_checkins',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('checkin_date', sa.DateTime(), nullable=True),
    sa.Column('streak_count', sa.Integer(), nullable=True),
    sa.Column('currency_reward', sa.Integer(), nullable=True),
    sa.Column('experience_reward_exercise', sa.Integer(), nullable=True),
    sa.Column('experience_reward_diet', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_daily_checkins_id'), 'daily_checkins', ['id'], unique=False)
    op.create_index(op.f('ix_daily_checkins_user_id'), 'daily_checkins', ['user_id'], unique=False)
    op.create_table('exercise_body_parts',
    sa.Column('exercise_id', sa.Integer(), nullable=False),
    sa.Column('body_part_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['body_part_id'], ['body_parts.id'], ),
    sa.ForeignKeyConstraint(['exercise_id'], ['exercises.id'], ),
    sa.PrimaryKeyConstraint('exercise_id', 'body_part_id')
    )
    op.create_table('exercise_equipment',
    sa.Column('exercise_id', sa.Integer(), nullable=False),
    sa.Column('equipment_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['equipment_id'], ['equipment.id'], ),
    sa.ForeignKeyConstraint(['exercise_id'], ['exercises.id'], ),
    sa.PrimaryKeyConstraint('exercise_id', 'equipment_id')
    )
    op.create_table('milestones',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('days_required', sa.Integer(), nullable=False),
    sa.Column('currency_reward', sa.Integer(), nullable=True),
    sa.Column('experience_reward_type', sa.String(), nullable=True),
    sa.Column('experience_reward_value', sa.Integer(), nullable=True),
    sa.Column('card_reward_id', sa.Integer(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['card_reward_id'], ['cards.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_milestones_id'), 'milestones', ['id'], unique=False)
    op.create_table('shop_items',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('image_url', sa.String(), nullable=True),
    sa.Column('category', sa.String(), nullable=False),
    sa.Column('subcategory', sa.String(), nullable=True),
    sa.Column('price', sa.Integer(), nullable=False),
    sa.Column('stock', sa.Integer(), nullable=True),
    sa.Column('is_limited', sa.Boolean(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('discount_level_type', sa.String(), nullable=True),
    sa.Column('discount_level_required', sa.Integer(), nullable=True),
    sa.Column('discount_percentage', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('available_from', sa.DateTime(), nullable=True),
    sa.Column('available_until', sa.DateTime(), nullable=True),
    sa.Column('card_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['card_id'], ['cards.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_shop_items_id'), 'shop_items', ['id'], unique=False)
    op.create_table('tasks',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('task_type', sa.Enum('DAILY', 'WEEKLY', 'CHALLENGE', name='tasktype'), nullable=False),
    sa.Column('category', sa.Enum('EXERCISE', 'DIET', 'SOCIAL', 'COMBINED', name='taskcategory'), nullable=False),
    sa.Column('requirement_type', sa.String(), nullable=False),
    sa.Column('requirement_value', sa.Integer(), nullable=False),
    sa.Column('currency_reward', sa.Integer(), nullable=True),
    sa.Column('experience_reward_type', sa.String(), nullable=True),
    sa.Column('experience_reward_value', sa.Integer(), nullable=True),
    sa.Column('card_reward_id', sa.Integer(), nullable=True),
    sa.Column('min_level_required', sa.Integer(), nullable=True),
    sa.Column('level_type', sa.String(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['card_reward_id'], ['cards.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_tasks_id'), 'tasks', ['id'], unique=False)
    op.create_table('user_attributes',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('strength', sa.Integer(), nullable=True),
    sa.Column('endurance', sa.Integer(), nullable=True),
    sa.Column('flexibility', sa.Integer(), nullable=True),
    sa.Column('nutrition_knowledge', sa.Integer(), nullable=True),
    sa.Column('cooking_skill', sa.Integer(), nullable=True),
    sa.Column('diet_planning', sa.Integer(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_attributes_id'), 'user_attributes', ['id'], unique=False)
    op.create_index(op.f('ix_user_attributes_user_id'), 'user_attributes', ['user_id'], unique=True)
    op.create_table('user_cards',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('card_id', sa.Integer(), nullable=True),
    sa.Column('quantity', sa.Integer(), nullable=True),
    sa.Column('is_equipped', sa.Boolean(), nullable=True),
    sa.Column('obtained_at', sa.DateTime(), nullable=True),
    sa.Column('expires_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['card_id'], ['cards.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_cards_card_id'), 'user_cards', ['card_id'], unique=False)
    op.create_index(op.f('ix_user_cards_id'), 'user_cards', ['id'], unique=False)
    op.create_index(op.f('ix_user_cards_user_id'), 'user_cards', ['user_id'], unique=False)
    op.create_table('user_levels',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('exercise_level', sa.Integer(), nullable=True),
    sa.Column('exercise_experience', sa.Integer(), nullable=True),
    sa.Column('diet_level', sa.Integer(), nullable=True),
    sa.Column('diet_experience', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_levels_id'), 'user_levels', ['id'], unique=False)
    op.create_index(op.f('ix_user_levels_user_id'), 'user_levels', ['user_id'], unique=True)
    op.create_table('card_synthesis_ingredients',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('recipe_id', sa.Integer(), nullable=True),
    sa.Column('card_id', sa.Integer(), nullable=True),
    sa.Column('quantity', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['card_id'], ['cards.id'], ),
    sa.ForeignKeyConstraint(['recipe_id'], ['card_synthesis_recipes.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_card_synthesis_ingredients_card_id'), 'card_synthesis_ingredients', ['card_id'], unique=False)
    op.create_index(op.f('ix_card_synthesis_ingredients_id'), 'card_synthesis_ingredients', ['id'], unique=False)
    op.create_index(op.f('ix_card_synthesis_ingredients_recipe_id'), 'card_synthesis_ingredients', ['recipe_id'], unique=False)
    op.create_table('currency_transactions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('currency_id', sa.Integer(), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('amount', sa.Integer(), nullable=True),
    sa.Column('balance_after', sa.Integer(), nullable=True),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('transaction_type', sa.String(), nullable=True),
    sa.Column('related_entity_type', sa.String(), nullable=True),
    sa.Column('related_entity_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['currency_id'], ['currencies.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_currency_transactions_currency_id'), 'currency_transactions', ['currency_id'], unique=False)
    op.create_index(op.f('ix_currency_transactions_id'), 'currency_transactions', ['id'], unique=False)
    op.create_index(op.f('ix_currency_transactions_user_id'), 'currency_transactions', ['user_id'], unique=False)
    op.create_table('user_achievements',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('achievement_id', sa.Integer(), nullable=True),
    sa.Column('progress', sa.Integer(), nullable=True),
    sa.Column('completed', sa.Boolean(), nullable=True),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.Column('reward_claimed', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['achievement_id'], ['achievements.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_achievements_achievement_id'), 'user_achievements', ['achievement_id'], unique=False)
    op.create_index(op.f('ix_user_achievements_id'), 'user_achievements', ['id'], unique=False)
    op.create_index(op.f('ix_user_achievements_user_id'), 'user_achievements', ['user_id'], unique=False)
    op.create_table('user_milestones',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('milestone_id', sa.Integer(), nullable=True),
    sa.Column('progress', sa.Integer(), nullable=True),
    sa.Column('last_active_date', sa.DateTime(), nullable=True),
    sa.Column('completed', sa.Boolean(), nullable=True),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.Column('reward_claimed', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['milestone_id'], ['milestones.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_milestones_id'), 'user_milestones', ['id'], unique=False)
    op.create_index(op.f('ix_user_milestones_milestone_id'), 'user_milestones', ['milestone_id'], unique=False)
    op.create_index(op.f('ix_user_milestones_user_id'), 'user_milestones', ['user_id'], unique=False)
    op.create_table('user_tasks',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('task_id', sa.Integer(), nullable=True),
    sa.Column('progress', sa.Integer(), nullable=True),
    sa.Column('completed', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('expires_at', sa.DateTime(), nullable=False),
    sa.Column('completed_at', sa.DateTime(), nullable=True),
    sa.Column('reward_claimed', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['task_id'], ['tasks.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_tasks_id'), 'user_tasks', ['id'], unique=False)
    op.create_index(op.f('ix_user_tasks_task_id'), 'user_tasks', ['task_id'], unique=False)
    op.create_index(op.f('ix_user_tasks_user_id'), 'user_tasks', ['user_id'], unique=False)
    op.create_table('user_titles',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('level_id', sa.Integer(), nullable=True),
    sa.Column('title_name', sa.String(), nullable=False),
    sa.Column('title_type', sa.String(), nullable=False),
    sa.Column('obtained_at', sa.DateTime(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.ForeignKeyConstraint(['level_id'], ['user_levels.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_titles_id'), 'user_titles', ['id'], unique=False)
    op.create_index(op.f('ix_user_titles_level_id'), 'user_titles', ['level_id'], unique=False)
    op.create_index(op.f('ix_user_titles_user_id'), 'user_titles', ['user_id'], unique=False)
    op.create_table('user_purchases',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=True),
    sa.Column('item_id', sa.Integer(), nullable=True),
    sa.Column('quantity', sa.Integer(), nullable=True),
    sa.Column('price_paid', sa.Integer(), nullable=True),
    sa.Column('total_paid', sa.Integer(), nullable=True),
    sa.Column('discount_applied', sa.Float(), nullable=True),
    sa.Column('transaction_id', sa.Integer(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('shipping_address', sa.Text(), nullable=True),
    sa.Column('tracking_number', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['item_id'], ['shop_items.id'], ),
    sa.ForeignKeyConstraint(['transaction_id'], ['currency_transactions.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_purchases_id'), 'user_purchases', ['id'], unique=False)
    op.create_index(op.f('ix_user_purchases_item_id'), 'user_purchases', ['item_id'], unique=False)
    op.create_index(op.f('ix_user_purchases_user_id'), 'user_purchases', ['user_id'], unique=False)
    op.drop_table('nutrition_reference_main')
    op.drop_table('mineral_rni')
    op.drop_table('other_dietary_spl_ul')
    op.drop_table('water_rni')
    op.drop_table('nutrition_pregnancy_inc')
    op.drop_table('vitamin_rni')
    op.drop_table('water_pregnancy_inc')
    op.drop_table('vitamin_pregnancy_inc')
    op.drop_table('graph_checkpoints')
    op.drop_table('mineral_pregnancy_inc')
    op.alter_column('comment_likes', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True,
               existing_server_default=sa.text('now()'))
    op.drop_constraint('_user_comment_like_uc', 'comment_likes', type_='unique')
    op.drop_index('ix_comment_likes_comment_id', table_name='comment_likes')
    op.drop_index('ix_comment_likes_user_id', table_name='comment_likes')
    op.alter_column('comments', 'content',
               existing_type=sa.TEXT(),
               type_=sa.String(length=1000),
               existing_nullable=False)
    op.alter_column('comments', 'status',
               existing_type=sa.VARCHAR(length=20),
               type_=sa.Enum('ACTIVE', 'DELETED', 'REPORTED', name='commentstatus'),
               nullable=True,
               postgresql_using="status::text::commentstatus")
    op.alter_column('comments', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('comments', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True,
               existing_server_default=sa.text('now()'))
    op.drop_index('ix_comments_created_at', table_name='comments')
    op.drop_index('ix_comments_parent_id', table_name='comments')
    op.drop_index('ix_comments_post_id', table_name='comments')
    op.drop_index('ix_comments_status', table_name='comments')
    op.drop_index('ix_comments_user_id', table_name='comments')
    op.drop_column('comments', 'reported_count')
    op.add_column('images', sa.Column('post_id', sa.Integer(), nullable=True))
    op.add_column('images', sa.Column('title', sa.String(length=100), nullable=True))
    op.add_column('images', sa.Column('description', sa.String(length=500), nullable=True))
    op.add_column('images', sa.Column('updated_at', sa.DateTime(), nullable=True))
    op.alter_column('images', 'url',
               existing_type=sa.VARCHAR(length=255),
               type_=sa.String(length=500),
               existing_nullable=False)
    op.alter_column('images', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True,
               existing_server_default=sa.text('now()'))
    op.create_index(op.f('ix_images_id'), 'images', ['id'], unique=False)
    op.create_foreign_key(None, 'images', 'posts', ['post_id'], ['id'])
    op.drop_column('images', 'mime_type')
    op.drop_column('images', 'width')
    op.drop_column('images', 'file_size')
    op.drop_column('images', 'original_filename')
    op.drop_column('images', 'height')
    op.alter_column('notifications', 'type',
               existing_type=sa.VARCHAR(length=50),
               type_=sa.Enum('LIKE', 'COMMENT', 'FOLLOW', 'SYSTEM', name='notificationtype'),
               existing_nullable=False,
               postgresql_using="type::text::notificationtype")
    op.alter_column('notifications', 'content',
               existing_type=sa.TEXT(),
               type_=sa.String(length=500),
               existing_nullable=False)
    op.alter_column('notifications', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True,
               existing_server_default=sa.text('now()'))
    op.drop_index('ix_notifications_created_at', table_name='notifications')
    op.drop_index('ix_notifications_is_read', table_name='notifications')
    op.drop_index('ix_notifications_type', table_name='notifications')
    op.drop_index('ix_notifications_user_id', table_name='notifications')
    op.drop_constraint('notifications_related_post_id_fkey', 'notifications', type_='foreignkey')
    op.drop_constraint('notifications_related_user_id_fkey', 'notifications', type_='foreignkey')
    op.drop_constraint('notifications_related_comment_id_fkey', 'notifications', type_='foreignkey')
    op.drop_column('notifications', 'related_user_id')
    op.drop_column('notifications', 'related_post_id')
    op.drop_column('notifications', 'related_comment_id')
    op.alter_column('post_likes', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True,
               existing_server_default=sa.text('now()'))
    op.drop_constraint('_user_post_like_uc', 'post_likes', type_='unique')
    op.drop_index('ix_post_likes_post_id', table_name='post_likes')
    op.drop_index('ix_post_likes_user_id', table_name='post_likes')
    op.alter_column('posts', 'title',
               existing_type=sa.VARCHAR(length=200),
               type_=sa.String(length=100),
               existing_nullable=False)
    op.alter_column('posts', 'content',
               existing_type=sa.TEXT(),
               type_=sa.String(length=5000),
               existing_nullable=False)
    op.alter_column('posts', 'status',
               existing_type=sa.VARCHAR(length=20),
               type_=sa.Enum('ACTIVE', 'DELETED', 'REPORTED', name='poststatus'),
               nullable=True,
               postgresql_using="status::text::poststatus")
    op.alter_column('posts', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('posts', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True,
               existing_server_default=sa.text('now()'))
    op.drop_index('ix_posts_created_at', table_name='posts')
    op.drop_index('ix_posts_related_workout_id', table_name='posts')
    op.drop_index('ix_posts_status', table_name='posts')
    op.drop_index('ix_posts_user_id', table_name='posts')
    op.drop_column('posts', 'reported_count')
    op.drop_column('posts', 'image_urls')
    op.drop_column('posts', 'view_count')
    op.alter_column('reports', 'reason',
               existing_type=sa.TEXT(),
               type_=sa.String(length=500),
               existing_nullable=False)
    op.alter_column('reports', 'status',
               existing_type=sa.VARCHAR(length=20),
               type_=sa.Enum('PENDING', 'RESOLVED', 'REJECTED', name='reportstatus'),
               nullable=True,
               postgresql_using="status::text::reportstatus")
    op.alter_column('reports', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('reports', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.drop_index('ix_reports_comment_id', table_name='reports')
    op.drop_index('ix_reports_post_id', table_name='reports')
    op.drop_index('ix_reports_reporter_id', table_name='reports')
    op.drop_index('ix_reports_status', table_name='reports')
    op.drop_column('reports', 'resolved_at')
    op.alter_column('training_templates', 'exercises',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
    op.drop_index('idx_training_templates_created_at', table_name='training_templates')
    op.drop_index('idx_training_templates_user_id', table_name='training_templates')
    op.create_index(op.f('ix_training_templates_id'), 'training_templates', ['id'], unique=False)
    op.create_index(op.f('ix_training_templates_user_id'), 'training_templates', ['user_id'], unique=False)
    op.drop_constraint('training_templates_user_id_fkey', 'training_templates', type_='foreignkey')
    op.create_foreign_key(None, 'training_templates', 'users', ['user_id'], ['id'])
    op.add_column('user_relations', sa.Column('follower_id', sa.Integer(), nullable=False))
    op.add_column('user_relations', sa.Column('following_id', sa.Integer(), nullable=False))
    op.alter_column('user_relations', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True,
               existing_server_default=sa.text('now()'))
    op.create_index(op.f('ix_user_relations_id'), 'user_relations', ['id'], unique=False)
    op.drop_constraint('user_relations_related_user_id_fkey', 'user_relations', type_='foreignkey')
    op.drop_constraint('user_relations_user_id_fkey', 'user_relations', type_='foreignkey')
    op.create_foreign_key(None, 'user_relations', 'users', ['follower_id'], ['id'])
    op.create_foreign_key(None, 'user_relations', 'users', ['following_id'], ['id'])
    op.drop_column('user_relations', 'related_user_id')
    op.drop_column('user_relations', 'relation_type')
    op.drop_column('user_relations', 'updated_at')
    op.drop_column('user_relations', 'user_id')
    op.alter_column('user_training_plan_records', 'sets',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
    op.drop_index('idx_user_training_plan_records_date', table_name='user_training_plan_records')
    op.drop_index('idx_user_training_plan_records_exercise_id', table_name='user_training_plan_records')
    op.drop_index('idx_user_training_plan_records_muscle_group', table_name='user_training_plan_records')
    op.drop_index('idx_user_training_plan_records_user_id', table_name='user_training_plan_records')
    op.create_index(op.f('ix_user_training_plan_records_date'), 'user_training_plan_records', ['date'], unique=False)
    op.create_index(op.f('ix_user_training_plan_records_exercise_id'), 'user_training_plan_records', ['exercise_id'], unique=False)
    op.create_index(op.f('ix_user_training_plan_records_id'), 'user_training_plan_records', ['id'], unique=False)
    op.create_index(op.f('ix_user_training_plan_records_muscle_group'), 'user_training_plan_records', ['muscle_group'], unique=False)
    op.create_index(op.f('ix_user_training_plan_records_user_id'), 'user_training_plan_records', ['user_id'], unique=False)
    op.drop_constraint('fk_exercise_name', 'user_training_plan_records', type_='foreignkey')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_foreign_key('fk_exercise_name', 'user_training_plan_records', 'exercises', ['exercise_id'], ['id'], ondelete='CASCADE')
    op.drop_index(op.f('ix_user_training_plan_records_user_id'), table_name='user_training_plan_records')
    op.drop_index(op.f('ix_user_training_plan_records_muscle_group'), table_name='user_training_plan_records')
    op.drop_index(op.f('ix_user_training_plan_records_id'), table_name='user_training_plan_records')
    op.drop_index(op.f('ix_user_training_plan_records_exercise_id'), table_name='user_training_plan_records')
    op.drop_index(op.f('ix_user_training_plan_records_date'), table_name='user_training_plan_records')
    op.create_index('idx_user_training_plan_records_user_id', 'user_training_plan_records', ['user_id'], unique=False)
    op.create_index('idx_user_training_plan_records_muscle_group', 'user_training_plan_records', ['muscle_group'], unique=False)
    op.create_index('idx_user_training_plan_records_exercise_id', 'user_training_plan_records', ['exercise_id'], unique=False)
    op.create_index('idx_user_training_plan_records_date', 'user_training_plan_records', ['date'], unique=False)
    op.alter_column('user_training_plan_records', 'sets',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.add_column('user_relations', sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=False))
    op.add_column('user_relations', sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True))
    op.add_column('user_relations', sa.Column('relation_type', sa.VARCHAR(length=20), autoincrement=False, nullable=False))
    op.add_column('user_relations', sa.Column('related_user_id', sa.INTEGER(), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'user_relations', type_='foreignkey')
    op.drop_constraint(None, 'user_relations', type_='foreignkey')
    op.create_foreign_key('user_relations_user_id_fkey', 'user_relations', 'users', ['user_id'], ['id'])
    op.create_foreign_key('user_relations_related_user_id_fkey', 'user_relations', 'users', ['related_user_id'], ['id'])
    op.drop_index(op.f('ix_user_relations_id'), table_name='user_relations')
    op.alter_column('user_relations', 'created_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True,
               existing_server_default=sa.text('now()'))
    op.drop_column('user_relations', 'following_id')
    op.drop_column('user_relations', 'follower_id')
    op.drop_constraint(None, 'training_templates', type_='foreignkey')
    op.create_foreign_key('training_templates_user_id_fkey', 'training_templates', 'users', ['user_id'], ['id'], ondelete='CASCADE')
    op.drop_index(op.f('ix_training_templates_user_id'), table_name='training_templates')
    op.drop_index(op.f('ix_training_templates_id'), table_name='training_templates')
    op.create_index('idx_training_templates_user_id', 'training_templates', ['user_id'], unique=False)
    op.create_index('idx_training_templates_created_at', 'training_templates', ['created_at'], unique=False)
    op.alter_column('training_templates', 'exercises',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.add_column('reports', sa.Column('resolved_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True))
    op.create_index('ix_reports_status', 'reports', ['status'], unique=False)
    op.create_index('ix_reports_reporter_id', 'reports', ['reporter_id'], unique=False)
    op.create_index('ix_reports_post_id', 'reports', ['post_id'], unique=False)
    op.create_index('ix_reports_comment_id', 'reports', ['comment_id'], unique=False)
    op.alter_column('reports', 'updated_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('reports', 'created_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('reports', 'status',
               existing_type=sa.Enum('PENDING', 'RESOLVED', 'REJECTED', name='reportstatus'),
               type_=sa.VARCHAR(length=20),
               nullable=False)
    op.alter_column('reports', 'reason',
               existing_type=sa.String(length=500),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.add_column('posts', sa.Column('view_count', sa.INTEGER(), autoincrement=False, nullable=False))
    op.add_column('posts', sa.Column('image_urls', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True))
    op.add_column('posts', sa.Column('reported_count', sa.INTEGER(), autoincrement=False, nullable=False))
    op.create_index('ix_posts_user_id', 'posts', ['user_id'], unique=False)
    op.create_index('ix_posts_status', 'posts', ['status'], unique=False)
    op.create_index('ix_posts_related_workout_id', 'posts', ['related_workout_id'], unique=False)
    op.create_index('ix_posts_created_at', 'posts', ['created_at'], unique=False)
    op.alter_column('posts', 'updated_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('posts', 'created_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('posts', 'status',
               existing_type=sa.Enum('ACTIVE', 'DELETED', 'REPORTED', name='poststatus'),
               type_=sa.VARCHAR(length=20),
               nullable=False)
    op.alter_column('posts', 'content',
               existing_type=sa.String(length=5000),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('posts', 'title',
               existing_type=sa.String(length=100),
               type_=sa.VARCHAR(length=200),
               existing_nullable=False)
    op.create_index('ix_post_likes_user_id', 'post_likes', ['user_id'], unique=False)
    op.create_index('ix_post_likes_post_id', 'post_likes', ['post_id'], unique=False)
    op.create_unique_constraint('_user_post_like_uc', 'post_likes', ['user_id', 'post_id'])
    op.alter_column('post_likes', 'created_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True,
               existing_server_default=sa.text('now()'))
    op.add_column('notifications', sa.Column('related_comment_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('notifications', sa.Column('related_post_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('notifications', sa.Column('related_user_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.create_foreign_key('notifications_related_comment_id_fkey', 'notifications', 'comments', ['related_comment_id'], ['id'])
    op.create_foreign_key('notifications_related_user_id_fkey', 'notifications', 'users', ['related_user_id'], ['id'])
    op.create_foreign_key('notifications_related_post_id_fkey', 'notifications', 'posts', ['related_post_id'], ['id'])
    op.create_index('ix_notifications_user_id', 'notifications', ['user_id'], unique=False)
    op.create_index('ix_notifications_type', 'notifications', ['type'], unique=False)
    op.create_index('ix_notifications_is_read', 'notifications', ['is_read'], unique=False)
    op.create_index('ix_notifications_created_at', 'notifications', ['created_at'], unique=False)
    op.alter_column('notifications', 'created_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('notifications', 'content',
               existing_type=sa.String(length=500),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('notifications', 'type',
               existing_type=sa.Enum('LIKE', 'COMMENT', 'FOLLOW', 'SYSTEM', name='notificationtype'),
               type_=sa.VARCHAR(length=50),
               existing_nullable=False)
    op.add_column('images', sa.Column('height', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('images', sa.Column('original_filename', sa.VARCHAR(length=255), autoincrement=False, nullable=True))
    op.add_column('images', sa.Column('file_size', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('images', sa.Column('width', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('images', sa.Column('mime_type', sa.VARCHAR(length=50), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'images', type_='foreignkey')
    op.drop_index(op.f('ix_images_id'), table_name='images')
    op.alter_column('images', 'created_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('images', 'url',
               existing_type=sa.String(length=500),
               type_=sa.VARCHAR(length=255),
               existing_nullable=False)
    op.drop_column('images', 'updated_at')
    op.drop_column('images', 'description')
    op.drop_column('images', 'title')
    op.drop_column('images', 'post_id')
    op.add_column('comments', sa.Column('reported_count', sa.INTEGER(), autoincrement=False, nullable=False))
    op.create_index('ix_comments_user_id', 'comments', ['user_id'], unique=False)
    op.create_index('ix_comments_status', 'comments', ['status'], unique=False)
    op.create_index('ix_comments_post_id', 'comments', ['post_id'], unique=False)
    op.create_index('ix_comments_parent_id', 'comments', ['parent_id'], unique=False)
    op.create_index('ix_comments_created_at', 'comments', ['created_at'], unique=False)
    op.alter_column('comments', 'updated_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('comments', 'created_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True,
               existing_server_default=sa.text('now()'))
    op.alter_column('comments', 'status',
               existing_type=sa.Enum('ACTIVE', 'DELETED', 'REPORTED', name='commentstatus'),
               type_=sa.VARCHAR(length=20),
               nullable=False)
    op.alter_column('comments', 'content',
               existing_type=sa.String(length=1000),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.create_index('ix_comment_likes_user_id', 'comment_likes', ['user_id'], unique=False)
    op.create_index('ix_comment_likes_comment_id', 'comment_likes', ['comment_id'], unique=False)
    op.create_unique_constraint('_user_comment_like_uc', 'comment_likes', ['user_id', 'comment_id'])
    op.alter_column('comment_likes', 'created_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True,
               existing_server_default=sa.text('now()'))
    op.create_table('mineral_pregnancy_inc',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('stage', sa.VARCHAR(length=16), autoincrement=False, nullable=False),
    sa.Column('calcium_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('phosphor_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('kalium_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('natrium_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('magnesium_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('chlorine_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('iron_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('iodine_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('zinc_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('selenium_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('copper_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('fluorine_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('chromium_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('cobalt_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='mineral_pregnancy_inc_pkey')
    )
    op.create_table('graph_checkpoints',
    sa.Column('key', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('state', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('key', name='graph_checkpoints_pkey')
    )
    op.create_table('vitamin_pregnancy_inc',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('stage', sa.VARCHAR(length=16), autoincrement=False, nullable=False),
    sa.Column('vitamin_d_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('thiamine_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('lactoflavin_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('notes', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='vitamin_pregnancy_inc_pkey')
    )
    op.create_table('water_pregnancy_inc',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('stage', sa.VARCHAR(length=16), autoincrement=False, nullable=False),
    sa.Column('sex', sa.VARCHAR(length=6), autoincrement=False, nullable=False),
    sa.Column('drinking_inc_ml', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('total_water_inc_ml', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='water_pregnancy_inc_pkey')
    )
    op.create_table('vitamin_rni',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('age_start_years', sa.NUMERIC(precision=5, scale=2), autoincrement=False, nullable=False),
    sa.Column('sex', sa.VARCHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('rec_type', sa.VARCHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('vitamin_a', sa.NUMERIC(precision=10, scale=2), autoincrement=False, nullable=True),
    sa.Column('vitamin_a_unit', sa.VARCHAR(length=10), autoincrement=False, nullable=True),
    sa.Column('vitamin_d', sa.NUMERIC(precision=10, scale=2), autoincrement=False, nullable=True),
    sa.Column('vitamin_d_unit', sa.VARCHAR(length=10), autoincrement=False, nullable=True),
    sa.Column('vitamin_e', sa.NUMERIC(precision=10, scale=2), autoincrement=False, nullable=True),
    sa.Column('vitamin_e_unit', sa.VARCHAR(length=10), autoincrement=False, nullable=True),
    sa.Column('thiamine', sa.NUMERIC(precision=10, scale=2), autoincrement=False, nullable=True),
    sa.Column('thiamine_unit', sa.VARCHAR(length=10), autoincrement=False, nullable=True),
    sa.Column('lactoflavin', sa.NUMERIC(precision=10, scale=2), autoincrement=False, nullable=True),
    sa.Column('lactoflavin_unit', sa.VARCHAR(length=10), autoincrement=False, nullable=True),
    sa.Column('vitamin_b6', sa.NUMERIC(precision=10, scale=2), autoincrement=False, nullable=True),
    sa.Column('vitamin_b6_unit', sa.VARCHAR(length=10), autoincrement=False, nullable=True),
    sa.Column('vitamin_b12', sa.NUMERIC(precision=10, scale=2), autoincrement=False, nullable=True),
    sa.Column('vitamin_b12_unit', sa.VARCHAR(length=10), autoincrement=False, nullable=True),
    sa.Column('vitamin_c', sa.NUMERIC(precision=10, scale=2), autoincrement=False, nullable=True),
    sa.Column('vitamin_c_unit', sa.VARCHAR(length=10), autoincrement=False, nullable=True),
    sa.Column('niacin', sa.NUMERIC(precision=10, scale=2), autoincrement=False, nullable=True),
    sa.Column('niacin_unit', sa.VARCHAR(length=10), autoincrement=False, nullable=True),
    sa.Column('folacin', sa.NUMERIC(precision=10, scale=2), autoincrement=False, nullable=True),
    sa.Column('folacin_unit', sa.VARCHAR(length=10), autoincrement=False, nullable=True),
    sa.Column('pantothenic', sa.NUMERIC(precision=10, scale=2), autoincrement=False, nullable=True),
    sa.Column('pantothenic_unit', sa.VARCHAR(length=10), autoincrement=False, nullable=True),
    sa.Column('biotin', sa.NUMERIC(precision=10, scale=2), autoincrement=False, nullable=True),
    sa.Column('biotin_unit', sa.VARCHAR(length=10), autoincrement=False, nullable=True),
    sa.Column('choline', sa.NUMERIC(precision=10, scale=2), autoincrement=False, nullable=True),
    sa.Column('choline_unit', sa.VARCHAR(length=10), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='vitamin_rni_pkey')
    )
    op.create_table('nutrition_pregnancy_inc',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('stage', sa.VARCHAR(length=16), autoincrement=False, nullable=False),
    sa.Column('sex', sa.VARCHAR(length=6), autoincrement=False, nullable=False),
    sa.Column('fat', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('fat_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('saturated_fat', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('saturated_fat_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('pufa', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('pufa_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('n3fa', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('n3fa_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('la', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('la_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('ala', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('ala_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('epa_dha', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('epa_dha_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('carbohydrate', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('carbohydrate_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('fiber_dietary', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('fiber_dietary_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('fructose', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('fructose_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('protein_ear', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('protein_ear_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('protein_rni', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('protein_rni_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('protein', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('protein_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='nutrition_pregnancy_inc_pkey')
    )
    op.create_table('water_rni',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('sex', sa.VARCHAR(length=6), autoincrement=False, nullable=False),
    sa.Column('age_start_years', sa.NUMERIC(precision=5, scale=2), autoincrement=False, nullable=False),
    sa.Column('drinking_ml', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('total_water_ml', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='water_rni_pkey')
    )
    op.create_table('other_dietary_spl_ul',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name_cn', sa.VARCHAR(length=32), autoincrement=False, nullable=False),
    sa.Column('spl', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('spl_unit', sa.CHAR(length=10), autoincrement=False, nullable=True),
    sa.Column('ul', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('ul_unit', sa.CHAR(length=10), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='other_dietary_spl_ul_pkey')
    )
    op.create_table('mineral_rni',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('age_start_years', sa.NUMERIC(precision=5, scale=2), autoincrement=False, nullable=False),
    sa.Column('sex', sa.VARCHAR(length=6), autoincrement=False, nullable=False),
    sa.Column('rec_type', sa.VARCHAR(length=3), autoincrement=False, nullable=False),
    sa.Column('calcium', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('calcium_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('phosphor', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('phosphor_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('kalium', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('kalium_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('natrium', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('natrium_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('magnesium', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('magnesium_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('chlorine', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('chlorine_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('iron', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('iron_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('iodine', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('iodine_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('zinc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('zinc_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('selenium', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('selenium_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('copper', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('copper_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('fluorine', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('fluorine_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('chromium', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('chromium_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('cobalt', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('cobalt_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('manganese', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('manganese_unit', sa.CHAR(length=10), server_default=sa.text("'mg'::bpchar"), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='mineral_rni_pkey')
    )
    op.create_table('nutrition_reference_main',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('sex', sa.VARCHAR(length=6), autoincrement=False, nullable=False),
    sa.Column('age_start_years', sa.NUMERIC(precision=5, scale=2), autoincrement=False, nullable=False),
    sa.Column('fat', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('fat_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('saturated_fat', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('saturated_fat_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('pufa', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('pufa_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('n3fa', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('n3fa_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('la', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('la_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('ala', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('ala_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('epa_dha', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('epa_dha_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('carbohydrate_ear', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('carbohydrate_ear_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('carbohydrate', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('carbohydrate_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('fiber_dietary', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('fiber_dietary_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('fructose', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('fructose_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('protein_ear', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('protein_ear_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('protein_rni', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('protein_rni_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('protein', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('protein_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='nutrition_reference_main_pkey')
    )
    op.drop_index(op.f('ix_user_purchases_user_id'), table_name='user_purchases')
    op.drop_index(op.f('ix_user_purchases_item_id'), table_name='user_purchases')
    op.drop_index(op.f('ix_user_purchases_id'), table_name='user_purchases')
    op.drop_table('user_purchases')
    op.drop_index(op.f('ix_user_titles_user_id'), table_name='user_titles')
    op.drop_index(op.f('ix_user_titles_level_id'), table_name='user_titles')
    op.drop_index(op.f('ix_user_titles_id'), table_name='user_titles')
    op.drop_table('user_titles')
    op.drop_index(op.f('ix_user_tasks_user_id'), table_name='user_tasks')
    op.drop_index(op.f('ix_user_tasks_task_id'), table_name='user_tasks')
    op.drop_index(op.f('ix_user_tasks_id'), table_name='user_tasks')
    op.drop_table('user_tasks')
    op.drop_index(op.f('ix_user_milestones_user_id'), table_name='user_milestones')
    op.drop_index(op.f('ix_user_milestones_milestone_id'), table_name='user_milestones')
    op.drop_index(op.f('ix_user_milestones_id'), table_name='user_milestones')
    op.drop_table('user_milestones')
    op.drop_index(op.f('ix_user_achievements_user_id'), table_name='user_achievements')
    op.drop_index(op.f('ix_user_achievements_id'), table_name='user_achievements')
    op.drop_index(op.f('ix_user_achievements_achievement_id'), table_name='user_achievements')
    op.drop_table('user_achievements')
    op.drop_index(op.f('ix_currency_transactions_user_id'), table_name='currency_transactions')
    op.drop_index(op.f('ix_currency_transactions_id'), table_name='currency_transactions')
    op.drop_index(op.f('ix_currency_transactions_currency_id'), table_name='currency_transactions')
    op.drop_table('currency_transactions')
    op.drop_index(op.f('ix_card_synthesis_ingredients_recipe_id'), table_name='card_synthesis_ingredients')
    op.drop_index(op.f('ix_card_synthesis_ingredients_id'), table_name='card_synthesis_ingredients')
    op.drop_index(op.f('ix_card_synthesis_ingredients_card_id'), table_name='card_synthesis_ingredients')
    op.drop_table('card_synthesis_ingredients')
    op.drop_index(op.f('ix_user_levels_user_id'), table_name='user_levels')
    op.drop_index(op.f('ix_user_levels_id'), table_name='user_levels')
    op.drop_table('user_levels')
    op.drop_index(op.f('ix_user_cards_user_id'), table_name='user_cards')
    op.drop_index(op.f('ix_user_cards_id'), table_name='user_cards')
    op.drop_index(op.f('ix_user_cards_card_id'), table_name='user_cards')
    op.drop_table('user_cards')
    op.drop_index(op.f('ix_user_attributes_user_id'), table_name='user_attributes')
    op.drop_index(op.f('ix_user_attributes_id'), table_name='user_attributes')
    op.drop_table('user_attributes')
    op.drop_index(op.f('ix_tasks_id'), table_name='tasks')
    op.drop_table('tasks')
    op.drop_index(op.f('ix_shop_items_id'), table_name='shop_items')
    op.drop_table('shop_items')
    op.drop_index(op.f('ix_milestones_id'), table_name='milestones')
    op.drop_table('milestones')
    op.drop_table('exercise_equipment')
    op.drop_table('exercise_body_parts')
    op.drop_index(op.f('ix_daily_checkins_user_id'), table_name='daily_checkins')
    op.drop_index(op.f('ix_daily_checkins_id'), table_name='daily_checkins')
    op.drop_table('daily_checkins')
    op.drop_index(op.f('ix_currencies_user_id'), table_name='currencies')
    op.drop_index(op.f('ix_currencies_id'), table_name='currencies')
    op.drop_table('currencies')
    op.drop_index(op.f('ix_card_synthesis_recipes_result_card_id'), table_name='card_synthesis_recipes')
    op.drop_index(op.f('ix_card_synthesis_recipes_id'), table_name='card_synthesis_recipes')
    op.drop_table('card_synthesis_recipes')
    op.drop_index(op.f('ix_achievements_id'), table_name='achievements')
    op.drop_table('achievements')
    op.drop_index(op.f('ix_cards_name'), table_name='cards')
    op.drop_index(op.f('ix_cards_id'), table_name='cards')
    op.drop_table('cards')
    # ### end Alembic commands ### 