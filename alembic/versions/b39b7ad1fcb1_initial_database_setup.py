"""initial database setup

Revision ID: b39b7ad1fcb1
Revises: f7787add14f1
Create Date: 2025-04-04 23:30:15.123707

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'b39b7ad1fcb1'
down_revision = 'f7787add14f1'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('share_tracks',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('shared_by', sa.Integer(), nullable=True),
    sa.Column('scanned_by', sa.Integer(), nullable=True),
    sa.Column('share_type', sa.String(), nullable=False),
    sa.Column('page', sa.String(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['scanned_by'], ['users.id'], ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['shared_by'], ['users.id'], ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_share_tracks_id'), 'share_tracks', ['id'], unique=False)
    op.create_table('user_settings',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('notification_enabled', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id')
    )
    op.create_index(op.f('ix_user_settings_id'), 'user_settings', ['id'], unique=False)
    op.add_column('users', sa.Column('email', sa.String(), nullable=True))
    op.add_column('users', sa.Column('unionid', sa.String(), nullable=True))
    op.add_column('users', sa.Column('session_key', sa.String(), nullable=True))
    op.add_column('users', sa.Column('avatar_url', sa.String(), nullable=True))
    op.add_column('users', sa.Column('country', sa.String(), nullable=True))
    op.add_column('users', sa.Column('province', sa.String(), nullable=True))
    op.add_column('users', sa.Column('city', sa.String(), nullable=True))
    op.add_column('users', sa.Column('age', sa.Integer(), nullable=True))
    op.add_column('users', sa.Column('activity_level', sa.SmallInteger(), nullable=True))
    op.add_column('users', sa.Column('bmi', sa.Float(), nullable=True))
    op.add_column('users', sa.Column('tedd', sa.Integer(), nullable=True))
    op.add_column('users', sa.Column('completed', sa.Boolean(), nullable=True))
    op.add_column('users', sa.Column('is_superuser', sa.Boolean(), nullable=True))
    op.alter_column('users', 'height',
               existing_type=sa.INTEGER(),
               type_=sa.Float(),
               existing_nullable=True)
    op.alter_column('users', 'weight',
               existing_type=sa.INTEGER(),
               type_=sa.Float(),
               existing_nullable=True)
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_unionid'), 'users', ['unionid'], unique=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_users_unionid'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.alter_column('users', 'weight',
               existing_type=sa.Float(),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.alter_column('users', 'height',
               existing_type=sa.Float(),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.drop_column('users', 'is_superuser')
    op.drop_column('users', 'completed')
    op.drop_column('users', 'tedd')
    op.drop_column('users', 'bmi')
    op.drop_column('users', 'activity_level')
    op.drop_column('users', 'age')
    op.drop_column('users', 'city')
    op.drop_column('users', 'province')
    op.drop_column('users', 'country')
    op.drop_column('users', 'avatar_url')
    op.drop_column('users', 'session_key')
    op.drop_column('users', 'unionid')
    op.drop_column('users', 'email')
    op.drop_index(op.f('ix_user_settings_id'), table_name='user_settings')
    op.drop_table('user_settings')
    op.drop_index(op.f('ix_share_tracks_id'), table_name='share_tracks')
    op.drop_table('share_tracks')
    # ### end Alembic commands ### 