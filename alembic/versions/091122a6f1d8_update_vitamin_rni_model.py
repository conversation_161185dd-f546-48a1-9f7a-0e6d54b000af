"""update_vitamin_rni_model

Revision ID: 091122a6f1d8
Revises: workout_models_migration
Create Date: 2025-04-20 17:38:36.427536

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '091122a6f1d8'
down_revision = 'workout_models_migration'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('other_dietary_spl_ul')
    op.drop_table('vitamin_rni')
    op.drop_table('water_pregnancy_inc')
    op.drop_table('mineral_rni')
    op.drop_table('water_rni')
    op.drop_table('vitamin_pregnancy_inc')
    op.drop_table('nutrition_reference_main')
    op.drop_table('mineral_pregnancy_inc')
    op.drop_table('nutrition_pregnancy_inc')
    op.drop_index('idx_exercise_name', table_name='exercises')
    op.create_index(op.f('ix_exercises_en_name'), 'exercises', ['en_name'], unique=False)
    op.create_index(op.f('ix_exercises_id'), 'exercises', ['id'], unique=False)
    op.create_index(op.f('ix_exercises_name'), 'exercises', ['name'], unique=False)
    op.drop_index('idx_food_item_nutrients_category', table_name='food_item_nutrient_intakes')
    op.drop_index('idx_food_item_nutrients_food_item_id', table_name='food_item_nutrient_intakes')
    op.create_index(op.f('ix_food_item_nutrient_intakes_category'), 'food_item_nutrient_intakes', ['category'], unique=False)
    op.create_index(op.f('ix_food_item_nutrient_intakes_food_item_id'), 'food_item_nutrient_intakes', ['food_item_id'], unique=False)
    op.create_index(op.f('ix_food_item_nutrient_intakes_id'), 'food_item_nutrient_intakes', ['id'], unique=False)
    op.drop_column('food_item_nutrient_intakes', 'updated_at')
    op.drop_column('food_item_nutrient_intakes', 'created_at')
    op.alter_column('food_items', 'lights',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
    op.alter_column('food_items', 'warnings',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
    op.alter_column('food_items', 'warning_scenes',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
    op.drop_index('idx_food_items_food_id', table_name='food_items')
    op.drop_index('idx_food_items_meal_record_id', table_name='food_items')
    op.create_index(op.f('ix_food_items_id'), 'food_items', ['id'], unique=False)
    op.create_index(op.f('ix_food_items_meal_record_id'), 'food_items', ['meal_record_id'], unique=False)
    op.drop_index('idx_food_nutrient_values_category', table_name='food_nutrient_values')
    op.drop_index('idx_food_nutrient_values_food_id', table_name='food_nutrient_values')
    op.create_index(op.f('ix_food_nutrient_values_category'), 'food_nutrient_values', ['category'], unique=False)
    op.create_index(op.f('ix_food_nutrient_values_food_id'), 'food_nutrient_values', ['food_id'], unique=False)
    op.create_index(op.f('ix_food_nutrient_values_id'), 'food_nutrient_values', ['id'], unique=False)
    op.drop_column('food_nutrient_values', 'updated_at')
    op.drop_column('food_nutrient_values', 'created_at')
    op.alter_column('food_recognitions', 'user_id',
               existing_type=sa.INTEGER(),
               type_=sa.String(length=36),
               existing_nullable=True)
    op.alter_column('food_recognitions', 'recognition_result',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
    op.alter_column('food_recognitions', 'matched_foods',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
    op.alter_column('food_recognitions', 'nutrition_totals',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
    op.drop_index('idx_food_recognitions_meal_record_id', table_name='food_recognitions')
    op.drop_index('idx_food_recognitions_user_id', table_name='food_recognitions')
    op.create_index(op.f('ix_food_recognitions_id'), 'food_recognitions', ['id'], unique=False)
    op.create_index(op.f('ix_food_recognitions_user_id'), 'food_recognitions', ['user_id'], unique=False)
    op.drop_index('idx_food_units_food_id', table_name='food_units')
    op.create_index(op.f('ix_food_units_food_id'), 'food_units', ['food_id'], unique=False)
    op.create_index(op.f('ix_food_units_id'), 'food_units', ['id'], unique=False)
    op.drop_column('food_units', 'updated_at')
    op.drop_column('food_units', 'created_at')
    op.drop_index('idx_food_category', table_name='foods')
    op.drop_index('idx_food_category_code', table_name='foods')
    op.drop_constraint('idx_food_code', 'foods', type_='unique')
    op.drop_constraint('idx_food_name', 'foods', type_='unique')
    op.drop_index('idx_food_type', table_name='foods')
    op.create_index(op.f('ix_foods_category'), 'foods', ['category'], unique=False)
    op.create_index(op.f('ix_foods_code'), 'foods', ['code'], unique=True)
    op.create_index(op.f('ix_foods_food_type'), 'foods', ['food_type'], unique=False)
    op.create_index(op.f('ix_foods_id'), 'foods', ['id'], unique=False)
    op.create_index(op.f('ix_foods_name'), 'foods', ['name'], unique=False)
    op.drop_column('foods', 'category_code')
    op.drop_column('foods', 'created_at')
    op.drop_index('idx_health_recommendations_meal_record_id', table_name='health_recommendations')
    op.create_index(op.f('ix_health_recommendations_id'), 'health_recommendations', ['id'], unique=False)
    op.create_index(op.f('ix_health_recommendations_meal_record_id'), 'health_recommendations', ['meal_record_id'], unique=False)
    op.drop_column('health_recommendations', 'updated_at')
    op.drop_index('idx_meal_records_date', table_name='meal_records')
    op.drop_index('idx_meal_records_user_id', table_name='meal_records')
    op.create_index(op.f('ix_meal_records_date'), 'meal_records', ['date'], unique=False)
    op.create_index(op.f('ix_meal_records_id'), 'meal_records', ['id'], unique=False)
    op.create_index(op.f('ix_meal_records_user_id'), 'meal_records', ['user_id'], unique=False)
    op.create_index(op.f('ix_muscles_id'), 'muscles', ['id'], unique=False)
    op.drop_column('muscles', 'updated_at')
    op.drop_column('muscles', 'created_at')
    op.alter_column('nutritional_profiles', 'lights',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
    op.alter_column('nutritional_profiles', 'warnings',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
    op.alter_column('nutritional_profiles', 'warning_scenes',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
    op.create_index(op.f('ix_nutritional_profiles_id'), 'nutritional_profiles', ['id'], unique=False)
    op.drop_column('nutritional_profiles', 'created_at')
    op.create_index(op.f('ix_share_tracks_id'), 'share_tracks', ['id'], unique=False)
    op.alter_column('training_plans', 'description',
               existing_type=sa.VARCHAR(),
               type_=sa.Text(),
               existing_nullable=True)
    op.drop_column('training_plans', 'name')
    op.drop_column('training_plans', 'plan_data')
    op.alter_column('user_favorite_exercises', 'user_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.alter_column('user_favorite_exercises', 'exercise_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.alter_column('user_favorite_exercises', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('user_favorite_exercises', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.drop_index('idx_favorite_exercises_active', table_name='user_favorite_exercises')
    op.drop_index('idx_favorite_exercises_exercise_id', table_name='user_favorite_exercises')
    op.drop_index('idx_favorite_exercises_user_id', table_name='user_favorite_exercises')
    op.drop_constraint('unique_user_exercise', 'user_favorite_exercises', type_='unique')
    op.create_index(op.f('ix_user_favorite_exercises_id'), 'user_favorite_exercises', ['id'], unique=False)
    op.alter_column('user_favorite_foods', 'user_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.alter_column('user_favorite_foods', 'food_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.alter_column('user_favorite_foods', 'created_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('user_favorite_foods', 'updated_at',
               existing_type=postgresql.TIMESTAMP(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.drop_index('idx_favorite_foods_active', table_name='user_favorite_foods')
    op.drop_index('idx_favorite_foods_food_id', table_name='user_favorite_foods')
    op.drop_index('idx_favorite_foods_user_id', table_name='user_favorite_foods')
    op.drop_constraint('unique_user_food', 'user_favorite_foods', type_='unique')
    op.create_index(op.f('ix_user_favorite_foods_id'), 'user_favorite_foods', ['id'], unique=False)
    op.create_index(op.f('ix_user_settings_id'), 'user_settings', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_user_settings_id'), table_name='user_settings')
    op.drop_index(op.f('ix_user_favorite_foods_id'), table_name='user_favorite_foods')
    op.create_unique_constraint('unique_user_food', 'user_favorite_foods', ['user_id', 'food_id'])
    op.create_index('idx_favorite_foods_user_id', 'user_favorite_foods', ['user_id'], unique=False)
    op.create_index('idx_favorite_foods_food_id', 'user_favorite_foods', ['food_id'], unique=False)
    op.create_index('idx_favorite_foods_active', 'user_favorite_foods', ['is_active'], unique=False)
    op.alter_column('user_favorite_foods', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('user_favorite_foods', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('user_favorite_foods', 'food_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.alter_column('user_favorite_foods', 'user_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.drop_index(op.f('ix_user_favorite_exercises_id'), table_name='user_favorite_exercises')
    op.create_unique_constraint('unique_user_exercise', 'user_favorite_exercises', ['user_id', 'exercise_id'])
    op.create_index('idx_favorite_exercises_user_id', 'user_favorite_exercises', ['user_id'], unique=False)
    op.create_index('idx_favorite_exercises_exercise_id', 'user_favorite_exercises', ['exercise_id'], unique=False)
    op.create_index('idx_favorite_exercises_active', 'user_favorite_exercises', ['is_active'], unique=False)
    op.alter_column('user_favorite_exercises', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('user_favorite_exercises', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('user_favorite_exercises', 'exercise_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.alter_column('user_favorite_exercises', 'user_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.add_column('training_plans', sa.Column('plan_data', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=False))
    op.add_column('training_plans', sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.alter_column('training_plans', 'description',
               existing_type=sa.Text(),
               type_=sa.VARCHAR(),
               existing_nullable=True)
    op.drop_index(op.f('ix_share_tracks_id'), table_name='share_tracks')
    op.add_column('nutritional_profiles', sa.Column('created_at', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True))
    op.drop_index(op.f('ix_nutritional_profiles_id'), table_name='nutritional_profiles')
    op.alter_column('nutritional_profiles', 'warning_scenes',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.alter_column('nutritional_profiles', 'warnings',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.alter_column('nutritional_profiles', 'lights',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.add_column('muscles', sa.Column('created_at', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True))
    op.add_column('muscles', sa.Column('updated_at', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True))
    op.drop_index(op.f('ix_muscles_id'), table_name='muscles')
    op.drop_index(op.f('ix_meal_records_user_id'), table_name='meal_records')
    op.drop_index(op.f('ix_meal_records_id'), table_name='meal_records')
    op.drop_index(op.f('ix_meal_records_date'), table_name='meal_records')
    op.create_index('idx_meal_records_user_id', 'meal_records', ['user_id'], unique=False)
    op.create_index('idx_meal_records_date', 'meal_records', ['date'], unique=False)
    op.add_column('health_recommendations', sa.Column('updated_at', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True))
    op.drop_index(op.f('ix_health_recommendations_meal_record_id'), table_name='health_recommendations')
    op.drop_index(op.f('ix_health_recommendations_id'), table_name='health_recommendations')
    op.create_index('idx_health_recommendations_meal_record_id', 'health_recommendations', ['meal_record_id'], unique=False)
    op.add_column('foods', sa.Column('created_at', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True))
    op.add_column('foods', sa.Column('category_code', sa.INTEGER(), autoincrement=False, nullable=True))
    op.drop_index(op.f('ix_foods_name'), table_name='foods')
    op.drop_index(op.f('ix_foods_id'), table_name='foods')
    op.drop_index(op.f('ix_foods_food_type'), table_name='foods')
    op.drop_index(op.f('ix_foods_code'), table_name='foods')
    op.drop_index(op.f('ix_foods_category'), table_name='foods')
    op.create_index('idx_food_type', 'foods', ['food_type'], unique=False)
    op.create_unique_constraint('idx_food_name', 'foods', ['name'])
    op.create_unique_constraint('idx_food_code', 'foods', ['code'])
    op.create_index('idx_food_category_code', 'foods', ['category_code'], unique=False)
    op.create_index('idx_food_category', 'foods', ['category'], unique=False)
    op.add_column('food_units', sa.Column('created_at', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True))
    op.add_column('food_units', sa.Column('updated_at', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True))
    op.drop_index(op.f('ix_food_units_id'), table_name='food_units')
    op.drop_index(op.f('ix_food_units_food_id'), table_name='food_units')
    op.create_index('idx_food_units_food_id', 'food_units', ['food_id'], unique=False)
    op.drop_index(op.f('ix_food_recognitions_user_id'), table_name='food_recognitions')
    op.drop_index(op.f('ix_food_recognitions_id'), table_name='food_recognitions')
    op.create_index('idx_food_recognitions_user_id', 'food_recognitions', ['user_id'], unique=False)
    op.create_index('idx_food_recognitions_meal_record_id', 'food_recognitions', ['meal_record_id'], unique=False)
    op.alter_column('food_recognitions', 'nutrition_totals',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.alter_column('food_recognitions', 'matched_foods',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.alter_column('food_recognitions', 'recognition_result',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.alter_column('food_recognitions', 'user_id',
               existing_type=sa.String(length=36),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.add_column('food_nutrient_values', sa.Column('created_at', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True))
    op.add_column('food_nutrient_values', sa.Column('updated_at', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True))
    op.drop_index(op.f('ix_food_nutrient_values_id'), table_name='food_nutrient_values')
    op.drop_index(op.f('ix_food_nutrient_values_food_id'), table_name='food_nutrient_values')
    op.drop_index(op.f('ix_food_nutrient_values_category'), table_name='food_nutrient_values')
    op.create_index('idx_food_nutrient_values_food_id', 'food_nutrient_values', ['food_id'], unique=False)
    op.create_index('idx_food_nutrient_values_category', 'food_nutrient_values', ['category'], unique=False)
    op.drop_index(op.f('ix_food_items_meal_record_id'), table_name='food_items')
    op.drop_index(op.f('ix_food_items_id'), table_name='food_items')
    op.create_index('idx_food_items_meal_record_id', 'food_items', ['meal_record_id'], unique=False)
    op.create_index('idx_food_items_food_id', 'food_items', ['food_id'], unique=False)
    op.alter_column('food_items', 'warning_scenes',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.alter_column('food_items', 'warnings',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.alter_column('food_items', 'lights',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.add_column('food_item_nutrient_intakes', sa.Column('created_at', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True))
    op.add_column('food_item_nutrient_intakes', sa.Column('updated_at', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True))
    op.drop_index(op.f('ix_food_item_nutrient_intakes_id'), table_name='food_item_nutrient_intakes')
    op.drop_index(op.f('ix_food_item_nutrient_intakes_food_item_id'), table_name='food_item_nutrient_intakes')
    op.drop_index(op.f('ix_food_item_nutrient_intakes_category'), table_name='food_item_nutrient_intakes')
    op.create_index('idx_food_item_nutrients_food_item_id', 'food_item_nutrient_intakes', ['food_item_id'], unique=False)
    op.create_index('idx_food_item_nutrients_category', 'food_item_nutrient_intakes', ['category'], unique=False)
    op.drop_index(op.f('ix_exercises_name'), table_name='exercises')
    op.drop_index(op.f('ix_exercises_id'), table_name='exercises')
    op.drop_index(op.f('ix_exercises_en_name'), table_name='exercises')
    op.create_index('idx_exercise_name', 'exercises', ['name'], unique=False)
    op.create_table('nutrition_pregnancy_inc',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('stage', sa.VARCHAR(length=16), autoincrement=False, nullable=False),
    sa.Column('sex', sa.VARCHAR(length=6), autoincrement=False, nullable=False),
    sa.Column('fat', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('fat_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('saturated_fat', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('saturated_fat_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('pufa', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('pufa_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('n3fa', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('n3fa_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('la', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('la_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('ala', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('ala_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('epa_dha', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('epa_dha_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('carbohydrate', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('carbohydrate_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('fiber_dietary', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('fiber_dietary_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('fructose', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('fructose_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('protein_ear', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('protein_ear_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('protein_rni', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('protein_rni_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('protein', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('protein_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='nutrition_pregnancy_inc_pkey')
    )
    op.create_table('mineral_pregnancy_inc',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('stage', sa.VARCHAR(length=16), autoincrement=False, nullable=False),
    sa.Column('calcium_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('phosphor_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('kalium_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('natrium_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('magnesium_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('chlorine_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('iron_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('iodine_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('zinc_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('selenium_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('copper_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('fluorine_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('chromium_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('cobalt_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='mineral_pregnancy_inc_pkey')
    )
    op.create_table('nutrition_reference_main',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('sex', sa.VARCHAR(length=6), autoincrement=False, nullable=False),
    sa.Column('age_start_years', sa.NUMERIC(precision=5, scale=2), autoincrement=False, nullable=False),
    sa.Column('fat', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('fat_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('saturated_fat', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('saturated_fat_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('pufa', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('pufa_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('n3fa', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('n3fa_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('la', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('la_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('ala', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('ala_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('epa_dha', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('epa_dha_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('carbohydrate_ear', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('carbohydrate_ear_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('carbohydrate', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('carbohydrate_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('fiber_dietary', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('fiber_dietary_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('fructose', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('fructose_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('protein_ear', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('protein_ear_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('protein_rni', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('protein_rni_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('protein', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('protein_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='nutrition_reference_main_pkey')
    )
    op.create_table('vitamin_pregnancy_inc',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('stage', sa.VARCHAR(length=16), autoincrement=False, nullable=False),
    sa.Column('vitamin_d_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('thiamine_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('lactoflavin_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('notes', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('vitamin_a_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('vitamin_e_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('vitamin_b6_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('vitamin_b12_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('vitamin_c_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('niacin_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('folacin_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('pantothenic_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('biotin_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('choline_inc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='vitamin_pregnancy_inc_pkey')
    )
    op.create_table('water_rni',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('sex', sa.VARCHAR(length=6), autoincrement=False, nullable=False),
    sa.Column('age_start_years', sa.NUMERIC(precision=5, scale=2), autoincrement=False, nullable=False),
    sa.Column('drinking_ml', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('total_water_ml', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='water_rni_pkey')
    )
    op.create_table('mineral_rni',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('age_start_years', sa.NUMERIC(precision=5, scale=2), autoincrement=False, nullable=False),
    sa.Column('sex', sa.VARCHAR(length=6), autoincrement=False, nullable=False),
    sa.Column('rec_type', sa.VARCHAR(length=3), autoincrement=False, nullable=False),
    sa.Column('calcium', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('calcium_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('phosphor', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('phosphor_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('kalium', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('kalium_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('natrium', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('natrium_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('magnesium', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('magnesium_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('chlorine', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('chlorine_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('iron', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('iron_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('iodine', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('iodine_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('zinc', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('zinc_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('selenium', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('selenium_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('copper', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('copper_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('fluorine', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('fluorine_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('chromium', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('chromium_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('cobalt', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('cobalt_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('manganese', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('manganese_unit', sa.CHAR(length=10), server_default=sa.text("'mg'::bpchar"), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='mineral_rni_pkey')
    )
    op.create_table('water_pregnancy_inc',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('stage', sa.VARCHAR(length=16), autoincrement=False, nullable=False),
    sa.Column('sex', sa.VARCHAR(length=6), autoincrement=False, nullable=False),
    sa.Column('drinking_inc_ml', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('total_water_inc_ml', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='water_pregnancy_inc_pkey')
    )
    op.create_table('vitamin_rni',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('age_start_years', sa.NUMERIC(precision=5, scale=2), autoincrement=False, nullable=False),
    sa.Column('sex', sa.VARCHAR(length=6), autoincrement=False, nullable=False),
    sa.Column('rec_type', sa.VARCHAR(length=3), autoincrement=False, nullable=False),
    sa.Column('vitamin_a', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('vitamin_a_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('vitamin_d', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('vitamin_d_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('vitamin_e', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('vitamin_e_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('thiamine', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('thiamine_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('lactoflavin', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('lactoflavin_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('vitamin_b6', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('vitamin_b6_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('vitamin_b12', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('vitamin_b12_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('vitamin_c', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('vitamin_c_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('niacin', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('niacin_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('folacin', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('folacin_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('pantothenic', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('pantothenic_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('biotin', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('biotin_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.Column('choline', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('choline_unit', sa.CHAR(length=10), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='vitamin_rni_pkey')
    )
    op.create_table('other_dietary_spl_ul',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name_cn', sa.VARCHAR(length=32), autoincrement=False, nullable=False),
    sa.Column('spl', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('spl_unit', sa.CHAR(length=10), autoincrement=False, nullable=True),
    sa.Column('ul', sa.NUMERIC(), autoincrement=False, nullable=True),
    sa.Column('ul_unit', sa.CHAR(length=10), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='other_dietary_spl_ul_pkey')
    )
    # ### end Alembic commands ### 