"""create initial tables

Revision ID: 5ead2be63897
Revises: afdb01e7005c
Create Date: 2025-04-08 14:45:42.907922

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '5ead2be63897'
down_revision = 'afdb01e7005c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_exercises_id', table_name='exercises')
    op.drop_table('exercises')
    op.drop_index('ix_exercise_details_id', table_name='exercise_details')
    op.drop_table('exercise_details')
    op.drop_index('ix_muscles_id', table_name='muscles')
    op.drop_table('muscles')
    op.drop_index('ix_body_parts_id', table_name='body_parts')
    op.drop_table('body_parts')
    op.drop_index('ix_equipment_id', table_name='equipment')
    op.drop_table('equipment')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('equipment',
    sa.Column('name', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='equipment_pkey'),
    sa.UniqueConstraint('name', name='equipment_name_key')
    )
    op.create_index('ix_equipment_id', 'equipment', ['id'], unique=False)
    op.create_table('body_parts',
    sa.Column('name', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='body_parts_pkey'),
    sa.UniqueConstraint('name', name='body_parts_name_key')
    )
    op.create_index('ix_body_parts_id', 'body_parts', ['id'], unique=False)
    op.create_table('muscles',
    sa.Column('name', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('en_name', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='muscles_pkey'),
    sa.UniqueConstraint('en_name', name='muscles_en_name_key')
    )
    op.create_index('ix_muscles_id', 'muscles', ['id'], unique=False)
    op.create_table('exercise_details',
    sa.Column('exercise_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('target_muscles_id', postgresql.ARRAY(sa.INTEGER()), autoincrement=False, nullable=False),
    sa.Column('synergist_muscles_id', postgresql.ARRAY(sa.INTEGER()), autoincrement=False, nullable=True),
    sa.Column('ex_instructions', postgresql.ARRAY(sa.TEXT()), autoincrement=False, nullable=True),
    sa.Column('exercise_tips', postgresql.ARRAY(sa.TEXT()), autoincrement=False, nullable=True),
    sa.Column('video_file', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['exercise_id'], ['exercises.id'], name='exercise_details_exercise_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='exercise_details_pkey')
    )
    op.create_index('ix_exercise_details_id', 'exercise_details', ['id'], unique=False)
    op.create_table('exercises',
    sa.Column('name', sa.VARCHAR(length=100), autoincrement=False, nullable=False),
    sa.Column('en_name', sa.VARCHAR(length=100), autoincrement=False, nullable=True),
    sa.Column('body_part_id', postgresql.ARRAY(sa.INTEGER()), autoincrement=False, nullable=False),
    sa.Column('equipment_id', postgresql.ARRAY(sa.INTEGER()), autoincrement=False, nullable=False),
    sa.Column('image_name', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('gif_url', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('level', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('sort_priority', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('user_id', sa.VARCHAR(length=50), autoincrement=False, nullable=True),
    sa.Column('exercise_type', sa.VARCHAR(length=50), autoincrement=False, nullable=True),
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='exercises_pkey')
    )
    op.create_index('ix_exercises_id', 'exercises', ['id'], unique=False)
    # ### end Alembic commands ### 