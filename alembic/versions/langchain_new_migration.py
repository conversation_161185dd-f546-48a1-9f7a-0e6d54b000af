"""add langchain related tables

Revision ID: langchain_new_migration
Revises: f7787add14f1
Create Date: 2025-04-18

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from sqlalchemy.exc import ProgrammingError

# revision identifiers, used by Alembic.
revision = 'langchain_new_migration'
down_revision = 'f7787add14f1'  # 使用当前已应用的迁移作为基础
branch_labels = None
depends_on = None


def upgrade():
    # 创建消息角色枚举类型（如果不存在）
    try:
        op.execute("CREATE TYPE messagerole AS ENUM ('user', 'assistant', 'system', 'tool')")
    except ProgrammingError:
        # 枚举类型已经存在，跳过创建
        op.get_bind().execute("ROLLBACK")

    # 创建会话表
    try:
        op.create_table(
            'conversations',
            sa.Column('id', sa.Integer(), nullable=False),
            sa.Column('user_id', sa.Integer(), nullable=False),
            sa.Column('session_id', sa.String(), nullable=False),
            sa.Column('start_time', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
            sa.Column('last_active', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
            sa.Column('is_active', sa.Boolean(), default=True, nullable=True),
            sa.Column('meta_info', postgresql.JSON(astext_type=sa.Text()), nullable=True),
            sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
            sa.PrimaryKeyConstraint('id'),
            sa.UniqueConstraint('session_id')
        )
        op.create_index(op.f('ix_conversations_session_id'), 'conversations', ['session_id'], unique=True)
        op.create_index(op.f('ix_conversations_user_id'), 'conversations', ['user_id'], unique=False)
    except Exception as e:
        print(f"创建conversations表失败: {e}")

    # 创建消息表
    try:
        op.create_table(
            'messages',
            sa.Column('id', sa.Integer(), nullable=False),
            sa.Column('conversation_id', sa.Integer(), nullable=False),
            sa.Column('content', sa.Text(), nullable=False),
            sa.Column('role', sa.Enum('user', 'assistant', 'system', 'tool', name='messagerole'), nullable=False),
            sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
            sa.Column('meta_info', postgresql.JSON(astext_type=sa.Text()), nullable=True),
            sa.ForeignKeyConstraint(['conversation_id'], ['conversations.id'], ),
            sa.PrimaryKeyConstraint('id')
        )
        op.create_index(op.f('ix_messages_conversation_id'), 'messages', ['conversation_id'], unique=False)
    except Exception as e:
        print(f"创建messages表失败: {e}")

    # 创建QA对表
    try:
        op.create_table(
            'qa_pairs',
            sa.Column('id', sa.Integer(), nullable=False),
            sa.Column('user_id', sa.Integer(), nullable=False),
            sa.Column('session_id', sa.String(), nullable=True),
            sa.Column('question', sa.Text(), nullable=False),
            sa.Column('answer', sa.Text(), nullable=False),
            sa.Column('intent', sa.String(50), nullable=True),
            sa.Column('retrieved_docs', postgresql.JSON(astext_type=sa.Text()), nullable=True),
            sa.Column('tool_used', sa.String(50), nullable=True),
            sa.Column('feedback_score', sa.Integer(), nullable=True),
            sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
            sa.Column('meta_info', postgresql.JSON(astext_type=sa.Text()), nullable=True),
            sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
            sa.PrimaryKeyConstraint('id')
        )
        op.create_index(op.f('ix_qa_pairs_intent'), 'qa_pairs', ['intent'], unique=False)
        op.create_index(op.f('ix_qa_pairs_session_id'), 'qa_pairs', ['session_id'], unique=False)
        op.create_index(op.f('ix_qa_pairs_user_id'), 'qa_pairs', ['user_id'], unique=False)
    except Exception as e:
        print(f"创建qa_pairs表失败: {e}")


def downgrade():
    # 删除表
    op.drop_table('qa_pairs')
    op.drop_table('messages')
    op.drop_table('conversations')
    
    # 删除枚举类型
    op.execute('DROP TYPE IF EXISTS messagerole') 