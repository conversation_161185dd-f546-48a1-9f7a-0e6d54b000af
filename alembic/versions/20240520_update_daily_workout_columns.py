"""Update daily workout columns

Revision ID: 20240520_update_daily_workout
Revises: 20240520_fix_daily_workout
Create Date: 2024-05-20 14:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '20240520_update_daily_workout'
down_revision = '20240520_fix_daily_workout'
branch_labels = None
depends_on = None


def upgrade():
    # 使用单独的连接执行每个操作，避免事务失败影响后续操作
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    
    # 检查 daily_workouts 表中是否存在 user_id 列
    daily_workouts_columns = [col['name'] for col in inspector.get_columns('daily_workouts')]
    
    # 1. 添加 user_id 列
    if 'user_id' not in daily_workouts_columns:
        try:
            conn.execute(sa.text("ALTER TABLE daily_workouts ADD COLUMN user_id INTEGER"))
            conn.execute(sa.text(
                "ALTER TABLE daily_workouts ADD CONSTRAINT fk_daily_workouts_user_id " +
                "FOREIGN KEY (user_id) REFERENCES users (id)"
            ))
            print("成功添加 daily_workouts.user_id 列和外键约束")
        except Exception as e:
            print(f"添加 daily_workouts.user_id 列时出错: {e}")
    else:
        print("daily_workouts 表中已存在 user_id 列，无需添加")
    
    # 2. 添加 title 列
    if 'title' not in daily_workouts_columns:
        try:
            conn.execute(sa.text("ALTER TABLE daily_workouts ADD COLUMN title VARCHAR(100)"))
            print("成功添加 daily_workouts.title 列")
        except Exception as e:
            print(f"添加 daily_workouts.title 列时出错: {e}")
    else:
        print("daily_workouts 表中已存在 title 列，无需添加")
    
    # 3. 添加 content 列
    if 'content' not in daily_workouts_columns:
        try:
            conn.execute(sa.text("ALTER TABLE daily_workouts ADD COLUMN content VARCHAR(1000)"))
            print("成功添加 daily_workouts.content 列")
        except Exception as e:
            print(f"添加 daily_workouts.content 列时出错: {e}")
    else:
        print("daily_workouts 表中已存在 content 列，无需添加")
    
    # 4. 添加 status 列
    if 'status' not in daily_workouts_columns:
        try:
            # 先创建枚举类型
            conn.execute(sa.text(
                "DO $$ BEGIN " +
                "    CREATE TYPE dailyworkoutstatus AS ENUM ('ACTIVE', 'DELETED'); " +
                "EXCEPTION " +
                "    WHEN duplicate_object THEN null; " +
                "END $$;"
            ))
            # 添加 status 列
            conn.execute(sa.text("ALTER TABLE daily_workouts ADD COLUMN status dailyworkoutstatus DEFAULT 'ACTIVE'"))
            print("成功添加 daily_workouts.status 列")
        except Exception as e:
            print(f"添加 daily_workouts.status 列时出错: {e}")
    else:
        print("daily_workouts 表中已存在 status 列，无需添加")


def downgrade():
    # 使用单独的连接执行每个操作，避免事务失败影响后续操作
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    
    # 检查 daily_workouts 表中是否存在相关列
    daily_workouts_columns = [col['name'] for col in inspector.get_columns('daily_workouts')]
    
    # 1. 删除 status 列
    if 'status' in daily_workouts_columns:
        try:
            conn.execute(sa.text("ALTER TABLE daily_workouts DROP COLUMN status"))
            print("成功删除 daily_workouts.status 列")
        except Exception as e:
            print(f"删除 daily_workouts.status 列时出错: {e}")
    
    # 2. 删除 content 列
    if 'content' in daily_workouts_columns:
        try:
            conn.execute(sa.text("ALTER TABLE daily_workouts DROP COLUMN content"))
            print("成功删除 daily_workouts.content 列")
        except Exception as e:
            print(f"删除 daily_workouts.content 列时出错: {e}")
    
    # 3. 删除 title 列
    if 'title' in daily_workouts_columns:
        try:
            conn.execute(sa.text("ALTER TABLE daily_workouts DROP COLUMN title"))
            print("成功删除 daily_workouts.title 列")
        except Exception as e:
            print(f"删除 daily_workouts.title 列时出错: {e}")
    
    # 4. 删除 user_id 列
    if 'user_id' in daily_workouts_columns:
        try:
            conn.execute(sa.text("ALTER TABLE daily_workouts DROP CONSTRAINT IF EXISTS fk_daily_workouts_user_id"))
            conn.execute(sa.text("ALTER TABLE daily_workouts DROP COLUMN user_id"))
            print("成功删除 daily_workouts.user_id 列和外键约束")
        except Exception as e:
            print(f"删除 daily_workouts.user_id 列时出错: {e}")
