"""Add scene field to share_tracks table

Revision ID: 8b829c008417
Revises: b39b7ad1fcb1
Create Date: 2025-04-07 02:30:51.290975

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '8b829c008417'
down_revision = 'b39b7ad1fcb1'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('share_tracks', sa.Column('scene', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('share_tracks', 'scene')
    # ### end Alembic commands ### 