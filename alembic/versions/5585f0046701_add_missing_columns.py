"""add missing columns

Revision ID: 5585f0046701
Revises: ed0d95aa87d7
Create Date: 2025-04-18 15:28:36.120682

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '5585f0046701'
down_revision = 'ed0d95aa87d7'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('conversations',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('session_id', sa.String(), nullable=False),
    sa.Column('start_time', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('last_active', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('is_active', sa.<PERSON>(), nullable=True),
    sa.Column('meta_info', sa.<PERSON>(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_conversations_id'), 'conversations', ['id'], unique=False)
    op.create_index(op.f('ix_conversations_session_id'), 'conversations', ['session_id'], unique=True)
    op.create_index(op.f('ix_conversations_user_id'), 'conversations', ['user_id'], unique=False)
    op.create_table('feedbacks',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('type', sa.String(), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('rating', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_feedbacks_id'), 'feedbacks', ['id'], unique=False)
    op.create_table('qa_pairs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('session_id', sa.String(), nullable=True),
    sa.Column('question', sa.Text(), nullable=False),
    sa.Column('answer', sa.Text(), nullable=False),
    sa.Column('intent', sa.String(length=50), nullable=True),
    sa.Column('retrieved_docs', sa.JSON(), nullable=True),
    sa.Column('tool_used', sa.String(length=50), nullable=True),
    sa.Column('feedback_score', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('meta_info', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_qa_pairs_id'), 'qa_pairs', ['id'], unique=False)
    op.create_index(op.f('ix_qa_pairs_intent'), 'qa_pairs', ['intent'], unique=False)
    op.create_index(op.f('ix_qa_pairs_session_id'), 'qa_pairs', ['session_id'], unique=False)
    op.create_index(op.f('ix_qa_pairs_user_id'), 'qa_pairs', ['user_id'], unique=False)
    op.create_table('training_plans',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.String(), nullable=True),
    sa.Column('plan_data', sa.JSON(), nullable=False),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_training_plans_id'), 'training_plans', ['id'], unique=False)
    op.create_table('user_stats',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('total_workouts', sa.Integer(), nullable=True),
    sa.Column('total_minutes', sa.Integer(), nullable=True),
    sa.Column('total_calories', sa.Float(), nullable=True),
    sa.Column('total_distance', sa.Float(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id')
    )
    op.create_index(op.f('ix_user_stats_id'), 'user_stats', ['id'], unique=False)
    op.create_table('messages',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('conversation_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('role', sa.Enum('USER', 'ASSISTANT', 'SYSTEM', 'TOOL', name='messagerole'), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('meta_info', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['conversation_id'], ['conversations.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_messages_conversation_id'), 'messages', ['conversation_id'], unique=False)
    op.create_index(op.f('ix_messages_id'), 'messages', ['id'], unique=False)
    op.create_index(op.f('ix_messages_user_id'), 'messages', ['user_id'], unique=False)
    op.create_index(op.f('ix_body_parts_id'), 'body_parts', ['id'], unique=False)
    op.drop_column('body_parts', 'updated_at')
    op.drop_column('body_parts', 'created_at')
    op.create_index(op.f('ix_equipment_id'), 'equipment', ['id'], unique=False)
    op.drop_column('equipment', 'updated_at')
    op.drop_column('equipment', 'created_at')
    op.drop_index('idx_exercise_detail_exercise_id', table_name='exercise_details')
    op.create_index(op.f('ix_exercise_details_exercise_id'), 'exercise_details', ['exercise_id'], unique=False)
    op.create_index(op.f('ix_exercise_details_id'), 'exercise_details', ['id'], unique=False)
    
    # 先将非数字值设为NULL，然后再转换类型
    op.execute("UPDATE exercises SET user_id = NULL WHERE user_id !~ '^[0-9]+$'")
    op.execute("ALTER TABLE exercises ALTER COLUMN user_id TYPE INTEGER USING NULLIF(user_id, '')::integer")
    
    # 添加language字段到users表
    op.add_column('users', sa.Column('language', sa.String(), nullable=True))
    op.add_column('users', sa.Column('last_login', sa.DateTime(timezone=True), nullable=True))
    op.alter_column('users', 'openid',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.drop_constraint('users_email_key', 'users', type_='unique')
    op.drop_constraint('users_openid_key', 'users', type_='unique')
    op.drop_constraint('users_phone_key', 'users', type_='unique')
    op.drop_constraint('users_unionid_key', 'users', type_='unique')
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_openid'), 'users', ['openid'], unique=True)
    op.create_index(op.f('ix_users_phone'), 'users', ['phone'], unique=True)
    op.create_index(op.f('ix_users_unionid'), 'users', ['unionid'], unique=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_users_unionid'), table_name='users')
    op.drop_index(op.f('ix_users_phone'), table_name='users')
    op.drop_index(op.f('ix_users_openid'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.create_unique_constraint('users_unionid_key', 'users', ['unionid'])
    op.create_unique_constraint('users_phone_key', 'users', ['phone'])
    op.create_unique_constraint('users_openid_key', 'users', ['openid'])
    op.create_unique_constraint('users_email_key', 'users', ['email'])
    op.alter_column('users', 'openid',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.drop_column('users', 'last_login')
    op.drop_column('users', 'language')
    op.drop_index(op.f('ix_user_settings_id'), table_name='user_settings')
    op.drop_index(op.f('ix_user_favorite_foods_id'), table_name='user_favorite_foods')
    op.create_unique_constraint('unique_user_food', 'user_favorite_foods', ['user_id', 'food_id'])
    op.create_index('idx_favorite_foods_user_id', 'user_favorite_foods', ['user_id'], unique=False)
    op.create_index('idx_favorite_foods_food_id', 'user_favorite_foods', ['food_id'], unique=False)
    op.create_index('idx_favorite_foods_active', 'user_favorite_foods', ['is_active'], unique=False)
    op.alter_column('user_favorite_foods', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('user_favorite_foods', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('user_favorite_foods', 'food_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.alter_column('user_favorite_foods', 'user_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.drop_index(op.f('ix_user_favorite_exercises_id'), table_name='user_favorite_exercises')
    op.create_unique_constraint('unique_user_exercise', 'user_favorite_exercises', ['user_id', 'exercise_id'])
    op.create_index('idx_favorite_exercises_user_id', 'user_favorite_exercises', ['user_id'], unique=False)
    op.create_index('idx_favorite_exercises_exercise_id', 'user_favorite_exercises', ['exercise_id'], unique=False)
    op.create_index('idx_favorite_exercises_active', 'user_favorite_exercises', ['is_active'], unique=False)
    op.alter_column('user_favorite_exercises', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('user_favorite_exercises', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(),
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('user_favorite_exercises', 'exercise_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.alter_column('user_favorite_exercises', 'user_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.drop_index(op.f('ix_share_tracks_id'), table_name='share_tracks')
    op.add_column('nutritional_profiles', sa.Column('created_at', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True))
    op.drop_index(op.f('ix_nutritional_profiles_id'), table_name='nutritional_profiles')
    op.alter_column('nutritional_profiles', 'warning_scenes',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.alter_column('nutritional_profiles', 'warnings',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.alter_column('nutritional_profiles', 'lights',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.add_column('muscles', sa.Column('created_at', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True))
    op.add_column('muscles', sa.Column('updated_at', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True))
    op.drop_index(op.f('ix_muscles_id'), table_name='muscles')
    op.drop_index(op.f('ix_meal_records_user_id'), table_name='meal_records')
    op.drop_index(op.f('ix_meal_records_id'), table_name='meal_records')
    op.drop_index(op.f('ix_meal_records_date'), table_name='meal_records')
    op.create_index('idx_meal_records_user_id', 'meal_records', ['user_id'], unique=False)
    op.create_index('idx_meal_records_date', 'meal_records', ['date'], unique=False)
    op.add_column('health_recommendations', sa.Column('updated_at', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True))
    op.drop_index(op.f('ix_health_recommendations_meal_record_id'), table_name='health_recommendations')
    op.drop_index(op.f('ix_health_recommendations_id'), table_name='health_recommendations')
    op.create_index('idx_health_recommendations_meal_record_id', 'health_recommendations', ['meal_record_id'], unique=False)
    op.add_column('foods', sa.Column('category_code', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('foods', sa.Column('created_at', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True))
    op.drop_index(op.f('ix_foods_name'), table_name='foods')
    op.drop_index(op.f('ix_foods_id'), table_name='foods')
    op.drop_index(op.f('ix_foods_food_type'), table_name='foods')
    op.drop_index(op.f('ix_foods_code'), table_name='foods')
    op.drop_index(op.f('ix_foods_category'), table_name='foods')
    op.create_index('idx_food_type', 'foods', ['food_type'], unique=False)
    op.create_unique_constraint('idx_food_name', 'foods', ['name'])
    op.create_unique_constraint('idx_food_code', 'foods', ['code'])
    op.create_index('idx_food_category_code', 'foods', ['category_code'], unique=False)
    op.create_index('idx_food_category', 'foods', ['category'], unique=False)
    op.alter_column('foods', 'code',
               existing_type=sa.String(length=64),
               type_=sa.VARCHAR(length=128),
               existing_nullable=False)
    op.add_column('food_units', sa.Column('created_at', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True))
    op.add_column('food_units', sa.Column('updated_at', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True))
    op.drop_index(op.f('ix_food_units_id'), table_name='food_units')
    op.drop_index(op.f('ix_food_units_food_id'), table_name='food_units')
    op.create_index('idx_food_units_food_id', 'food_units', ['food_id'], unique=False)
    op.drop_index(op.f('ix_food_recognitions_user_id'), table_name='food_recognitions')
    op.drop_index(op.f('ix_food_recognitions_id'), table_name='food_recognitions')
    op.create_index('idx_food_recognitions_user_id', 'food_recognitions', ['user_id'], unique=False)
    op.create_index('idx_food_recognitions_meal_record_id', 'food_recognitions', ['meal_record_id'], unique=False)
    op.alter_column('food_recognitions', 'nutrition_totals',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.alter_column('food_recognitions', 'matched_foods',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.alter_column('food_recognitions', 'recognition_result',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.alter_column('food_recognitions', 'user_id',
               existing_type=sa.String(length=36),
               type_=sa.INTEGER(),
               existing_nullable=True)
    op.add_column('food_nutrient_values', sa.Column('created_at', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True))
    op.add_column('food_nutrient_values', sa.Column('updated_at', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True))
    op.drop_index(op.f('ix_food_nutrient_values_id'), table_name='food_nutrient_values')
    op.drop_index(op.f('ix_food_nutrient_values_food_id'), table_name='food_nutrient_values')
    op.drop_index(op.f('ix_food_nutrient_values_category'), table_name='food_nutrient_values')
    op.create_index('idx_food_nutrient_values_food_id', 'food_nutrient_values', ['food_id'], unique=False)
    op.create_index('idx_food_nutrient_values_category', 'food_nutrient_values', ['category'], unique=False)
    op.drop_index(op.f('ix_food_items_meal_record_id'), table_name='food_items')
    op.drop_index(op.f('ix_food_items_id'), table_name='food_items')
    op.create_index('idx_food_items_meal_record_id', 'food_items', ['meal_record_id'], unique=False)
    op.create_index('idx_food_items_food_id', 'food_items', ['food_id'], unique=False)
    op.alter_column('food_items', 'warning_scenes',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.alter_column('food_items', 'warnings',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.alter_column('food_items', 'lights',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.add_column('food_item_nutrient_intakes', sa.Column('created_at', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True))
    op.add_column('food_item_nutrient_intakes', sa.Column('updated_at', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True))
    op.drop_index(op.f('ix_food_item_nutrient_intakes_id'), table_name='food_item_nutrient_intakes')
    op.drop_index(op.f('ix_food_item_nutrient_intakes_food_item_id'), table_name='food_item_nutrient_intakes')
    op.drop_index(op.f('ix_food_item_nutrient_intakes_category'), table_name='food_item_nutrient_intakes')
    op.create_index('idx_food_item_nutrients_food_item_id', 'food_item_nutrient_intakes', ['food_item_id'], unique=False)
    op.create_index('idx_food_item_nutrients_category', 'food_item_nutrient_intakes', ['category'], unique=False)
    op.drop_index(op.f('ix_exercises_name'), table_name='exercises')
    op.drop_index(op.f('ix_exercises_id'), table_name='exercises')
    op.drop_index(op.f('ix_exercises_en_name'), table_name='exercises')
    op.create_index('idx_exercise_name', 'exercises', ['name'], unique=False)
    op.alter_column('exercises', 'user_id',
               existing_type=sa.Integer(),
               type_=sa.VARCHAR(length=50),
               existing_nullable=True)
    op.drop_index(op.f('ix_exercise_details_id'), table_name='exercise_details')
    op.drop_index(op.f('ix_exercise_details_exercise_id'), table_name='exercise_details')
    op.create_index('idx_exercise_detail_exercise_id', 'exercise_details', ['exercise_id'], unique=False)
    op.add_column('equipment', sa.Column('created_at', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True))
    op.add_column('equipment', sa.Column('updated_at', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True))
    op.drop_index(op.f('ix_equipment_id'), table_name='equipment')
    op.add_column('body_parts', sa.Column('created_at', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True))
    op.add_column('body_parts', sa.Column('updated_at', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True))
    op.drop_index(op.f('ix_body_parts_id'), table_name='body_parts')
    op.drop_index(op.f('ix_messages_user_id'), table_name='messages')
    op.drop_index(op.f('ix_messages_id'), table_name='messages')
    op.drop_index(op.f('ix_messages_conversation_id'), table_name='messages')
    op.drop_table('messages')
    op.drop_index(op.f('ix_user_stats_id'), table_name='user_stats')
    op.drop_table('user_stats')
    op.drop_index(op.f('ix_training_plans_id'), table_name='training_plans')
    op.drop_table('training_plans')
    op.drop_index(op.f('ix_qa_pairs_user_id'), table_name='qa_pairs')
    op.drop_index(op.f('ix_qa_pairs_session_id'), table_name='qa_pairs')
    op.drop_index(op.f('ix_qa_pairs_intent'), table_name='qa_pairs')
    op.drop_index(op.f('ix_qa_pairs_id'), table_name='qa_pairs')
    op.drop_table('qa_pairs')
    op.drop_index(op.f('ix_feedbacks_id'), table_name='feedbacks')
    op.drop_table('feedbacks')
    op.drop_index(op.f('ix_conversations_user_id'), table_name='conversations')
    op.drop_index(op.f('ix_conversations_session_id'), table_name='conversations')
    op.drop_index(op.f('ix_conversations_id'), table_name='conversations')
    op.drop_table('conversations')
    # ### end Alembic commands ### 