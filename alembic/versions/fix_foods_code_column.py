"""Fix foods code column length

Revision ID: fix_foods_code_column
Revises: 091122a6f1d8
Create Date: 2023-06-18 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'fix_foods_code_column'
down_revision = '091122a6f1d8'
branch_labels = None
depends_on = None


def upgrade():
    # 保持foods表中的code列为VARCHAR(128)
    op.alter_column('foods', 'code',
               existing_type=sa.String(length=64),
               type_=sa.String(length=128),
               existing_nullable=False)


def downgrade():
    # 恢复为VARCHAR(64)
    op.alter_column('foods', 'code',
               existing_type=sa.String(length=128),
               type_=sa.String(length=64),
               existing_nullable=False) 