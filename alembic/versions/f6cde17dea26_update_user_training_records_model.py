"""update user training records model

Revision ID: f6cde17dea26
Revises: 9b4764bcf4f0
Create Date: 2025-04-24 23:06:22.348702

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'f6cde17dea26'
down_revision = '9b4764bcf4f0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_workout_exercises_exercise_id', table_name='workout_exercises')
    op.drop_index('ix_workout_exercises_id', table_name='workout_exercises')
    op.drop_index('ix_workout_exercises_superset_group', table_name='workout_exercises')
    op.drop_index('ix_workout_exercises_workout_id', table_name='workout_exercises')
    op.drop_table('workout_exercises')
    op.drop_index('ix_meal_records_date', table_name='meal_records')
    op.drop_index('ix_meal_records_id', table_name='meal_records')
    op.drop_index('ix_meal_records_user_id', table_name='meal_records')
    op.drop_table('meal_records')
    op.drop_index('idx_training_records_date', table_name='training_records')
    op.drop_index('idx_training_records_exercise_id', table_name='training_records')
    op.drop_index('idx_training_records_user_date', table_name='training_records')
    op.drop_index('idx_training_records_user_id', table_name='training_records')
    op.drop_table('training_records')
    op.drop_index('ix_share_tracks_id', table_name='share_tracks')
    op.drop_table('share_tracks')
    op.drop_index('ix_food_item_nutrient_intakes_category', table_name='food_item_nutrient_intakes')
    op.drop_index('ix_food_item_nutrient_intakes_food_item_id', table_name='food_item_nutrient_intakes')
    op.drop_index('ix_food_item_nutrient_intakes_id', table_name='food_item_nutrient_intakes')
    op.drop_table('food_item_nutrient_intakes')
    op.drop_index('ix_food_items_id', table_name='food_items')
    op.drop_index('ix_food_items_meal_record_id', table_name='food_items')
    op.drop_table('food_items')
    op.drop_index('ix_health_recommendations_id', table_name='health_recommendations')
    op.drop_index('ix_health_recommendations_meal_record_id', table_name='health_recommendations')
    op.drop_table('health_recommendations')
    op.drop_index('ix_food_recognitions_id', table_name='food_recognitions')
    op.drop_index('ix_food_recognitions_user_id', table_name='food_recognitions')
    op.drop_table('food_recognitions')
    op.drop_index('ix_workouts_id', table_name='workouts')
    op.drop_index('ix_workouts_training_plan_id', table_name='workouts')
    op.drop_table('workouts')
    op.alter_column('foods', 'code',
               existing_type=sa.VARCHAR(length=128),
               type_=sa.String(length=64),
               existing_nullable=False)
    op.add_column('user_training_records', sa.Column('training_age', sa.Integer(), nullable=True))
    op.add_column('user_training_records', sa.Column('preferred_training_days', sa.Integer(), nullable=True))
    op.add_column('user_training_records', sa.Column('squat_max', sa.Float(), nullable=True))
    op.add_column('user_training_records', sa.Column('bench_press_max', sa.Float(), nullable=True))
    op.add_column('user_training_records', sa.Column('deadlift_max', sa.Float(), nullable=True))
    op.add_column('user_training_records', sa.Column('preferred_exercise_types', sa.JSON(), nullable=True))
    op.add_column('user_training_records', sa.Column('avoided_exercise_types', sa.JSON(), nullable=True))
    op.add_column('user_training_records', sa.Column('fitness_goals', sa.JSON(), nullable=True))
    op.add_column('user_training_records', sa.Column('target_muscle_groups', sa.JSON(), nullable=True))
    op.add_column('user_training_records', sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True))
    op.add_column('user_training_records', sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True))
    op.add_column('user_training_records', sa.Column('injuries', sa.JSON(), nullable=True))
    op.add_column('user_training_records', sa.Column('medical_conditions', sa.JSON(), nullable=True))
    op.alter_column('user_training_records', 'exercises_data',
               existing_type=postgresql.JSON(astext_type=sa.Text()),
               nullable=True)
    op.drop_index('ix_user_training_records_user_id', table_name='user_training_records')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('ix_user_training_records_user_id', 'user_training_records', ['user_id'], unique=False)
    op.alter_column('user_training_records', 'exercises_data',
               existing_type=postgresql.JSON(astext_type=sa.Text()),
               nullable=False)
    op.drop_column('user_training_records', 'medical_conditions')
    op.drop_column('user_training_records', 'injuries')
    op.drop_column('user_training_records', 'updated_at')
    op.drop_column('user_training_records', 'created_at')
    op.drop_column('user_training_records', 'target_muscle_groups')
    op.drop_column('user_training_records', 'fitness_goals')
    op.drop_column('user_training_records', 'avoided_exercise_types')
    op.drop_column('user_training_records', 'preferred_exercise_types')
    op.drop_column('user_training_records', 'deadlift_max')
    op.drop_column('user_training_records', 'bench_press_max')
    op.drop_column('user_training_records', 'squat_max')
    op.drop_column('user_training_records', 'preferred_training_days')
    op.drop_column('user_training_records', 'training_age')
    op.alter_column('foods', 'code',
               existing_type=sa.String(length=64),
               type_=sa.VARCHAR(length=128),
               existing_nullable=False)
    op.create_table('workouts',
    sa.Column('id', sa.INTEGER(), server_default=sa.text("nextval('workouts_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('training_plan_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('name', sa.VARCHAR(length=100), autoincrement=False, nullable=False),
    sa.Column('day_of_week', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('day_number', sa.SMALLINT(), autoincrement=False, nullable=False),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('estimated_duration', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['training_plan_id'], ['training_plans.id'], name='workouts_training_plan_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='workouts_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_index('ix_workouts_training_plan_id', 'workouts', ['training_plan_id'], unique=False)
    op.create_index('ix_workouts_id', 'workouts', ['id'], unique=False)
    op.create_table('food_recognitions',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.VARCHAR(length=36), autoincrement=False, nullable=True),
    sa.Column('meal_date', sa.DATE(), autoincrement=False, nullable=False),
    sa.Column('meal_type', sa.VARCHAR(length=20), autoincrement=False, nullable=False),
    sa.Column('image_url', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('thumb_image_url', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('secure_path', sa.VARCHAR(length=100), autoincrement=False, nullable=True),
    sa.Column('status', sa.VARCHAR(length=20), server_default=sa.text("'processing'::character varying"), autoincrement=False, nullable=True),
    sa.Column('recognition_result', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('matched_foods', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('nutrition_totals', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('meal_record_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('user_modified', sa.BOOLEAN(), server_default=sa.text('false'), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['meal_record_id'], ['meal_records.id'], name='food_recognitions_meal_record_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='food_recognitions_pkey')
    )
    op.create_index('ix_food_recognitions_user_id', 'food_recognitions', ['user_id'], unique=False)
    op.create_index('ix_food_recognitions_id', 'food_recognitions', ['id'], unique=False)
    op.create_table('health_recommendations',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('meal_record_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('recommendation_text', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('recommendation_type', sa.VARCHAR(length=32), autoincrement=False, nullable=True),
    sa.Column('priority', sa.INTEGER(), server_default=sa.text('0'), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['meal_record_id'], ['meal_records.id'], name='health_recommendations_meal_record_id_fkey', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name='health_recommendations_pkey')
    )
    op.create_index('ix_health_recommendations_meal_record_id', 'health_recommendations', ['meal_record_id'], unique=False)
    op.create_index('ix_health_recommendations_id', 'health_recommendations', ['id'], unique=False)
    op.create_table('food_items',
    sa.Column('id', sa.INTEGER(), server_default=sa.text("nextval('food_items_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('meal_record_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('food_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('name', sa.VARCHAR(length=100), autoincrement=False, nullable=False),
    sa.Column('quantity', sa.DOUBLE_PRECISION(precision=53), server_default=sa.text('1.0'), autoincrement=False, nullable=True),
    sa.Column('unit_name', sa.VARCHAR(length=20), server_default=sa.text("'份'::character varying"), autoincrement=False, nullable=True),
    sa.Column('weight', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=False),
    sa.Column('category', sa.VARCHAR(length=50), autoincrement=False, nullable=True),
    sa.Column('cuisine_type', sa.VARCHAR(length=50), autoincrement=False, nullable=True),
    sa.Column('cuisine_type_detail', sa.VARCHAR(length=100), autoincrement=False, nullable=True),
    sa.Column('image_url', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('health_light', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('lights', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('warnings', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('warning_scenes', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('calory', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('protein', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('fat', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('carbohydrate', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('protein_fraction', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('fat_fraction', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('carb_fraction', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('is_custom', sa.BOOLEAN(), server_default=sa.text('false'), autoincrement=False, nullable=True),
    sa.Column('is_takeout', sa.BOOLEAN(), server_default=sa.text('false'), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['food_id'], ['foods.id'], name='food_items_food_id_fkey'),
    sa.ForeignKeyConstraint(['meal_record_id'], ['meal_records.id'], name='food_items_meal_record_id_fkey', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name='food_items_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_index('ix_food_items_meal_record_id', 'food_items', ['meal_record_id'], unique=False)
    op.create_index('ix_food_items_id', 'food_items', ['id'], unique=False)
    op.create_table('food_item_nutrient_intakes',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('food_item_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('name_en', sa.VARCHAR(length=64), autoincrement=False, nullable=False),
    sa.Column('name_cn', sa.VARCHAR(length=64), autoincrement=False, nullable=False),
    sa.Column('value', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('unit', sa.VARCHAR(length=16), autoincrement=False, nullable=True),
    sa.Column('unit_name', sa.VARCHAR(length=16), autoincrement=False, nullable=True),
    sa.Column('nrv_percentage', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('category', sa.VARCHAR(length=16), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['food_item_id'], ['food_items.id'], name='food_item_nutrient_intakes_food_item_id_fkey', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name='food_item_nutrient_intakes_pkey')
    )
    op.create_index('ix_food_item_nutrient_intakes_id', 'food_item_nutrient_intakes', ['id'], unique=False)
    op.create_index('ix_food_item_nutrient_intakes_food_item_id', 'food_item_nutrient_intakes', ['food_item_id'], unique=False)
    op.create_index('ix_food_item_nutrient_intakes_category', 'food_item_nutrient_intakes', ['category'], unique=False)
    op.create_table('share_tracks',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('shared_by', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('scanned_by', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('share_type', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('page', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('scene', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('qrcode_url', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('is_active', sa.BOOLEAN(), server_default=sa.text('true'), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['scanned_by'], ['users.id'], name='share_tracks_scanned_by_fkey', ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['shared_by'], ['users.id'], name='share_tracks_shared_by_fkey', ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id', name='share_tracks_pkey')
    )
    op.create_index('ix_share_tracks_id', 'share_tracks', ['id'], unique=False)
    op.create_table('training_records',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('exercise_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('sets', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=False),
    sa.Column('total_sets', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('total_reps', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('total_weight', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('total_duration', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('total_distance', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('note', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('date', sa.DATE(), server_default=sa.text('CURRENT_DATE'), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='training_records_pkey')
    )
    op.create_index('idx_training_records_user_id', 'training_records', ['user_id'], unique=False)
    op.create_index('idx_training_records_user_date', 'training_records', ['user_id', 'date'], unique=False)
    op.create_index('idx_training_records_exercise_id', 'training_records', ['exercise_id'], unique=False)
    op.create_index('idx_training_records_date', 'training_records', ['date'], unique=False)
    op.create_table('meal_records',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('date', sa.DATE(), autoincrement=False, nullable=False),
    sa.Column('meal_type', sa.VARCHAR(length=20), autoincrement=False, nullable=False),
    sa.Column('image_url', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('file_id', sa.VARCHAR(length=100), autoincrement=False, nullable=True),
    sa.Column('thumb_image_url', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('total_calory', sa.DOUBLE_PRECISION(precision=53), server_default=sa.text('0'), autoincrement=False, nullable=True),
    sa.Column('total_protein', sa.DOUBLE_PRECISION(precision=53), server_default=sa.text('0'), autoincrement=False, nullable=True),
    sa.Column('total_fat', sa.DOUBLE_PRECISION(precision=53), server_default=sa.text('0'), autoincrement=False, nullable=True),
    sa.Column('total_carbohydrate', sa.DOUBLE_PRECISION(precision=53), server_default=sa.text('0'), autoincrement=False, nullable=True),
    sa.Column('is_ai_recognized', sa.BOOLEAN(), server_default=sa.text('false'), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name='meal_records_user_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='meal_records_pkey')
    )
    op.create_index('ix_meal_records_user_id', 'meal_records', ['user_id'], unique=False)
    op.create_index('ix_meal_records_id', 'meal_records', ['id'], unique=False)
    op.create_index('ix_meal_records_date', 'meal_records', ['date'], unique=False)
    op.create_table('workout_exercises',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('workout_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('exercise_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('sets', sa.SMALLINT(), autoincrement=False, nullable=False),
    sa.Column('reps', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('rest_seconds', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('order', sa.SMALLINT(), server_default=sa.text("'0'::smallint"), autoincrement=False, nullable=False),
    sa.Column('notes', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('exercise_type', sa.VARCHAR(length=50), server_default=sa.text("'weight_reps'::character varying"), autoincrement=False, nullable=False),
    sa.Column('superset_group', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('weight', sa.VARCHAR(length=50), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['exercise_id'], ['exercises.id'], name='workout_exercises_exercise_id_fkey'),
    sa.ForeignKeyConstraint(['workout_id'], ['workouts.id'], name='workout_exercises_workout_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='workout_exercises_pkey')
    )
    op.create_index('ix_workout_exercises_workout_id', 'workout_exercises', ['workout_id'], unique=False)
    op.create_index('ix_workout_exercises_superset_group', 'workout_exercises', ['superset_group'], unique=False)
    op.create_index('ix_workout_exercises_id', 'workout_exercises', ['id'], unique=False)
    op.create_index('ix_workout_exercises_exercise_id', 'workout_exercises', ['exercise_id'], unique=False)
    # ### end Alembic commands ### 