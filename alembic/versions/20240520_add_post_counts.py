"""Add post counts columns

Revision ID: 20240520_add_post_counts
Revises: 20240520_update_daily_workout
Create Date: 2024-05-20 15:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '20240520_add_post_counts'
down_revision = '20240520_update_daily_workout'
branch_labels = None
depends_on = None


def upgrade():
    # 使用单独的连接执行每个操作，避免事务失败影响后续操作
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    
    # 检查 posts 表中是否存在 like_count 列
    posts_columns = [col['name'] for col in inspector.get_columns('posts')]
    
    # 1. 添加 like_count 列
    if 'like_count' not in posts_columns:
        try:
            conn.execute(sa.text("ALTER TABLE posts ADD COLUMN like_count INTEGER DEFAULT 0"))
            print("成功添加 posts.like_count 列")
        except Exception as e:
            print(f"添加 posts.like_count 列时出错: {e}")
    else:
        print("posts 表中已存在 like_count 列，无需添加")
    
    # 2. 添加 comment_count 列
    if 'comment_count' not in posts_columns:
        try:
            conn.execute(sa.text("ALTER TABLE posts ADD COLUMN comment_count INTEGER DEFAULT 0"))
            print("成功添加 posts.comment_count 列")
        except Exception as e:
            print(f"添加 posts.comment_count 列时出错: {e}")
    else:
        print("posts 表中已存在 comment_count 列，无需添加")
    
    # 3. 添加 share_count 列
    if 'share_count' not in posts_columns:
        try:
            conn.execute(sa.text("ALTER TABLE posts ADD COLUMN share_count INTEGER DEFAULT 0"))
            print("成功添加 posts.share_count 列")
        except Exception as e:
            print(f"添加 posts.share_count 列时出错: {e}")
    else:
        print("posts 表中已存在 share_count 列，无需添加")


def downgrade():
    # 使用单独的连接执行每个操作，避免事务失败影响后续操作
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    
    # 检查 posts 表中是否存在相关列
    posts_columns = [col['name'] for col in inspector.get_columns('posts')]
    
    # 1. 删除 share_count 列
    if 'share_count' in posts_columns:
        try:
            conn.execute(sa.text("ALTER TABLE posts DROP COLUMN share_count"))
            print("成功删除 posts.share_count 列")
        except Exception as e:
            print(f"删除 posts.share_count 列时出错: {e}")
    
    # 2. 删除 comment_count 列
    if 'comment_count' in posts_columns:
        try:
            conn.execute(sa.text("ALTER TABLE posts DROP COLUMN comment_count"))
            print("成功删除 posts.comment_count 列")
        except Exception as e:
            print(f"删除 posts.comment_count 列时出错: {e}")
    
    # 3. 删除 like_count 列
    if 'like_count' in posts_columns:
        try:
            conn.execute(sa.text("ALTER TABLE posts DROP COLUMN like_count"))
            print("成功删除 posts.like_count 列")
        except Exception as e:
            print(f"删除 posts.like_count 列时出错: {e}")
