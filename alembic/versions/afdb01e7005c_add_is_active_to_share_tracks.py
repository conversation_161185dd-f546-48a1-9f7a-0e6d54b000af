"""add_is_active_to_share_tracks

Revision ID: afdb01e7005c
Revises: 906e3f73b039
Create Date: 2025-04-07 19:37:06.044949

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'afdb01e7005c'
down_revision = '906e3f73b039'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('share_tracks', sa.Column('is_active', sa.<PERSON>(), nullable=True))
    op.add_column('share_tracks', sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('share_tracks', 'updated_at')
    op.drop_column('share_tracks', 'is_active')
    # ### end Alembic commands ### 