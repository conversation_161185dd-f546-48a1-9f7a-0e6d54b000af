"""Add scene field to share_tracks table

Revision ID: cc7f0f9b8cda
Revises: b39b7ad1fcb1
Create Date: 2025-04-07 02:29:04.415187

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'cc7f0f9b8cda'
down_revision = 'b39b7ad1fcb1'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('share_tracks', sa.Column('scene', sa.String(), nullable=True))
    op.alter_column('users', 'gender',
               existing_type=postgresql.ENUM('MALE', 'FEMALE', 'OTHER', name='gender'),
               type_=sa.Integer(),
               existing_nullable=True)
    op.alter_column('users', 'activity_level',
               existing_type=sa.SMALLINT(),
               type_=sa.Integer(),
               existing_nullable=True)
    op.alter_column('users', 'experience_level',
               existing_type=postgresql.ENUM('BEGINNER', 'INTERMEDIATE', 'ADVANCED', name='experiencelevel'),
               type_=sa.Integer(),
               existing_nullable=True)
    op.alter_column('users', 'fitness_goal',
               existing_type=postgresql.ENUM('WEIGHT_LOSS', 'MUSCLE_GAIN', 'MAINTENANCE', 'ENDURANCE', name='fitnessgoal'),
               type_=sa.Integer(),
               existing_nullable=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('users', 'fitness_goal',
               existing_type=sa.Integer(),
               type_=postgresql.ENUM('WEIGHT_LOSS', 'MUSCLE_GAIN', 'MAINTENANCE', 'ENDURANCE', name='fitnessgoal'),
               existing_nullable=True)
    op.alter_column('users', 'experience_level',
               existing_type=sa.Integer(),
               type_=postgresql.ENUM('BEGINNER', 'INTERMEDIATE', 'ADVANCED', name='experiencelevel'),
               existing_nullable=True)
    op.alter_column('users', 'activity_level',
               existing_type=sa.Integer(),
               type_=sa.SMALLINT(),
               existing_nullable=True)
    op.alter_column('users', 'gender',
               existing_type=sa.Integer(),
               type_=postgresql.ENUM('MALE', 'FEMALE', 'OTHER', name='gender'),
               existing_nullable=True)
    op.drop_column('share_tracks', 'scene')
    # ### end Alembic commands ### 