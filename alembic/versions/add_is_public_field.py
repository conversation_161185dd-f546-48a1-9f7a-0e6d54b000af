"""add is_public field to exercise_details

Revision ID: 20240415_is_public
Revises: 
Create Date: 2024-04-15

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '20240415_is_public'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # 添加is_public字段到exercise_details表，默认值为True
    op.add_column('exercise_details', sa.<PERSON>umn('is_public', sa.<PERSON>(), server_default='true'))


def downgrade():
    # 删除is_public字段
    op.drop_column('exercise_details', 'is_public') 