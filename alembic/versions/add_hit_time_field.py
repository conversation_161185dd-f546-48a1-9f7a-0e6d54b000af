"""add hit_time field to exercises

Revision ID: 20240415_hit_time
Revises: 20240415_is_public
Create Date: 2024-04-15

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '20240415_hit_time'
down_revision = '20240415_is_public'
branch_labels = None
depends_on = None


def upgrade():
    # 添加hit_time字段到exercises表，默认值为0
    op.add_column('exercises', sa.Column('hit_time', sa.Integer(), server_default='0'))


def downgrade():
    # 删除hit_time字段
    op.drop_column('exercises', 'hit_time') 