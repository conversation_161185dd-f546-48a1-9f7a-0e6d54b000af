"""add_client_transfer_history

Revision ID: 42f4542f0984
Revises: 8c9074a2a600
Create Date: 2025-05-01 17:44:07.604310

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '42f4542f0984'
down_revision = '8c9074a2a600'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('client_transfer_history',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('client_relation_id', sa.Integer(), nullable=True),
    sa.Column('from_coach_id', sa.Integer(), nullable=True),
    sa.Column('to_coach_id', sa.Integer(), nullable=True),
    sa.Column('reason', sa.String(), nullable=True),
    sa.Column('transferred_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['client_relation_id'], ['client_relations.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['from_coach_id'], ['users.id'], ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['to_coach_id'], ['users.id'], ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_client_transfer_history_client_relation_id'), 'client_transfer_history', ['client_relation_id'], unique=False)
    op.create_index(op.f('ix_client_transfer_history_id'), 'client_transfer_history', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_client_transfer_history_id'), table_name='client_transfer_history')
    op.drop_index(op.f('ix_client_transfer_history_client_relation_id'), table_name='client_transfer_history')
    op.drop_table('client_transfer_history')
    # ### end Alembic commands ### 