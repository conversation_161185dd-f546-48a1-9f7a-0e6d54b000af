"""Add conversation models for LangChain integration

Revision ID: 2023_12_20_langchain_models
Revises: 39a240cb905a
Create Date: 2023-12-20

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '2023_12_20_langchain_models'
down_revision = '39a240cb905a'  # 指向最新的HEAD版本
branch_labels = None
depends_on = None


def upgrade():
    # 创建消息角色枚举类型
    op.execute("CREATE TYPE messagerole AS ENUM ('user', 'assistant', 'system', 'tool')")

    # 创建会话表
    op.create_table(
        'conversations',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('session_id', sa.String(), nullable=False),
        sa.Column('start_time', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('last_active', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('is_active', sa.Boolean(), default=True, nullable=True),
        sa.Column('meta_info', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('session_id')
    )
    op.create_index(op.f('ix_conversations_session_id'), 'conversations', ['session_id'], unique=True)
    op.create_index(op.f('ix_conversations_user_id'), 'conversations', ['user_id'], unique=False)

    # 创建消息表
    op.create_table(
        'messages',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('conversation_id', sa.Integer(), nullable=False),
        sa.Column('content', sa.Text(), nullable=False),
        sa.Column('role', sa.Enum('user', 'assistant', 'system', 'tool', name='messagerole'), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('meta_info', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.ForeignKeyConstraint(['conversation_id'], ['conversations.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_messages_conversation_id'), 'messages', ['conversation_id'], unique=False)

    # 创建QA对表
    op.create_table(
        'qa_pairs',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('session_id', sa.String(), nullable=True),
        sa.Column('question', sa.Text(), nullable=False),
        sa.Column('answer', sa.Text(), nullable=False),
        sa.Column('intent', sa.String(50), nullable=True),
        sa.Column('retrieved_docs', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column('tool_used', sa.String(50), nullable=True),
        sa.Column('feedback_score', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('meta_info', postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_qa_pairs_intent'), 'qa_pairs', ['intent'], unique=False)
    op.create_index(op.f('ix_qa_pairs_session_id'), 'qa_pairs', ['session_id'], unique=False)
    op.create_index(op.f('ix_qa_pairs_user_id'), 'qa_pairs', ['user_id'], unique=False)


def downgrade():
    # 删除表
    op.drop_table('qa_pairs')
    op.drop_table('messages')
    op.drop_table('conversations')
    
    # 删除枚举类型
    op.execute('DROP TYPE messagerole') 