"""add user_training_record table

Revision ID: user_training_record_migration
Revises: fix_foods_code_column
Create Date: 2023-06-18 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'user_training_record_migration'
down_revision = 'fix_foods_code_column'
branch_labels = None
depends_on = None


def upgrade():
    op.create_table('user_training_records',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('date', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('body_parts', postgresql.ARRAY(sa.String()), nullable=True),
        sa.Column('duration_minutes', sa.Integer(), nullable=True),
        sa.Column('exercises_data', postgresql.JSON(astext_type=sa.Text()), nullable=False),
        sa.Column('notes', sa.Text(), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_training_records_date'), 'user_training_records', ['date'], unique=False)
    op.create_index(op.f('ix_user_training_records_id'), 'user_training_records', ['id'], unique=False)
    op.create_index(op.f('ix_user_training_records_user_id'), 'user_training_records', ['user_id'], unique=False)


def downgrade():
    op.drop_index(op.f('ix_user_training_records_user_id'), table_name='user_training_records')
    op.drop_index(op.f('ix_user_training_records_id'), table_name='user_training_records')
    op.drop_index(op.f('ix_user_training_records_date'), table_name='user_training_records')
    op.drop_table('user_training_records') 