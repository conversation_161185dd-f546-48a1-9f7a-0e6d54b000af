"""Add workout_id, training fields and visibility to DailyWorkout, add visibility to Post, add type to Image

Revision ID: 7e7febfa2d32
Revises: d236fe4a90b1
Create Date: 2025-05-23 17:00:37.144026

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import table, column


# revision identifiers, used by Alembic.
revision = '7e7febfa2d32'
down_revision = 'd236fe4a90b1'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('daily_workouts', sa.Column('workout_id', sa.Integer(), nullable=True))
    op.add_column('daily_workouts', sa.Column('training_volume', sa.String(length=50), nullable=True))
    op.add_column('daily_workouts', sa.Column('training_sets', sa.Integer(), nullable=True))
    op.add_column('daily_workouts', sa.Column('training_date', sa.DateTime(), nullable=True))
    
    # 先添加可为空的visibility列
    op.add_column('daily_workouts', sa.Column('visibility', sa.String(length=20), nullable=True))
    # 设置默认值
    op.execute("UPDATE daily_workouts SET visibility = 'Everyone' WHERE visibility IS NULL")
    # 然后将列设置为非空
    op.alter_column('daily_workouts', 'visibility', nullable=False)
    
    # 为user_id列中的空值设置默认值（使用ID为1的用户作为默认值）
    op.execute("UPDATE daily_workouts SET user_id = 1 WHERE user_id IS NULL")
    # 然后将user_id列设置为非空
    op.alter_column('daily_workouts', 'user_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    
    op.create_foreign_key(None, 'daily_workouts', 'workouts', ['workout_id'], ['id'])
    
    # 先添加可为空的type列
    op.add_column('images', sa.Column('type', sa.String(length=10), nullable=True))
    # 设置默认值
    op.execute("UPDATE images SET type = 'image' WHERE type IS NULL")
    # 然后将列设置为非空
    op.alter_column('images', 'type', nullable=False)
    
    # 先添加可为空的visibility列
    op.add_column('posts', sa.Column('visibility', sa.String(length=20), nullable=True))
    # 设置默认值
    op.execute("UPDATE posts SET visibility = 'Everyone' WHERE visibility IS NULL")
    # 然后将列设置为非空
    op.alter_column('posts', 'visibility', nullable=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('posts', 'visibility')
    op.drop_column('images', 'type')
    op.drop_constraint(None, 'daily_workouts', type_='foreignkey')
    op.alter_column('daily_workouts', 'user_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.drop_column('daily_workouts', 'visibility')
    op.drop_column('daily_workouts', 'training_date')
    op.drop_column('daily_workouts', 'training_sets')
    op.drop_column('daily_workouts', 'training_volume')
    op.drop_column('daily_workouts', 'workout_id')
    # ### end Alembic commands ### 