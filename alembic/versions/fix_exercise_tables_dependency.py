"""fix exercise tables dependency

Revision ID: fix_dependency_202406
Revises: 20240415_hit_time
Create Date: 2024-06-01

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'fix_dependency_202406'
down_revision = '20240415_hit_time'
branch_labels = None
depends_on = None


def upgrade():
    """Fix the migration by dropping the tables in the correct order: first exercise_details, then exercises."""
    # Create a transaction to ensure atomicity of operations
    connection = op.get_bind()
    with connection.begin() as transaction:
        try:
            # Drop exercise_details first
            op.drop_index('ix_exercise_details_id', table_name='exercise_details')
            op.drop_table('exercise_details')
            
            # Then drop exercises
            op.drop_index('ix_exercises_id', table_name='exercises')
            op.drop_table('exercises')
            
            # Recreate tables in the correct order
            
            # Create exercises table
            op.create_table('exercises',
                sa.Column('id', sa.Integer(), nullable=False),
                sa.Column('name', sa.String(length=100), nullable=False),
                sa.Column('en_name', sa.String(length=100), nullable=True),
                sa.Column('body_part_id', sa.ARRAY(sa.Integer()), nullable=True),
                sa.Column('equipment_id', sa.ARRAY(sa.Integer()), nullable=True),
                sa.Column('image_name', sa.String(length=255), nullable=True),
                sa.Column('gif_url', sa.String(length=255), nullable=True),
                sa.Column('description', sa.Text(), nullable=True),
                sa.Column('level', sa.SmallInteger(), nullable=True),
                sa.Column('sort_priority', sa.Integer(), nullable=True, default=0),
                sa.Column('user_id', sa.String(length=50), nullable=True),
                sa.Column('exercise_type', sa.String(length=50), nullable=True),
                sa.Column('hit_time', sa.Integer(), nullable=True, server_default='0'),
                sa.Column('created_at', sa.DateTime(), nullable=True),
                sa.Column('updated_at', sa.DateTime(), nullable=True),
                sa.PrimaryKeyConstraint('id')
            )
            op.create_index('ix_exercises_id', 'exercises', ['id'], unique=False)
            op.create_index('idx_exercise_name_en_name', 'exercises', ['name', 'en_name'], unique=False)
            op.create_index('idx_exercise_level_hit_time', 'exercises', ['level', 'hit_time'], unique=False)
            op.create_index('idx_body_part_id_gin', 'exercises', ['body_part_id'], postgresql_using='gin')
            op.create_index('idx_equipment_id_gin', 'exercises', ['equipment_id'], postgresql_using='gin')
            
            # Then create exercise_details with the foreign key
            op.create_table('exercise_details',
                sa.Column('id', sa.Integer(), nullable=False),
                sa.Column('exercise_id', sa.Integer(), nullable=False),
                sa.Column('target_muscles_id', sa.ARRAY(sa.Integer()), nullable=True),
                sa.Column('synergist_muscles_id', sa.ARRAY(sa.Integer()), nullable=True),
                sa.Column('ex_instructions', sa.ARRAY(sa.Text()), nullable=True),
                sa.Column('exercise_tips', sa.ARRAY(sa.Text()), nullable=True),
                sa.Column('video_file', sa.String(length=255), nullable=True),
                sa.Column('is_public', sa.Boolean(), nullable=True, server_default='true'),
                sa.Column('created_at', sa.DateTime(), nullable=True),
                sa.Column('updated_at', sa.DateTime(), nullable=True),
                sa.ForeignKeyConstraint(['exercise_id'], ['exercises.id'], ),
                sa.PrimaryKeyConstraint('id')
            )
            op.create_index('ix_exercise_details_id', 'exercise_details', ['id'], unique=False)
            op.create_index('idx_target_muscles_gin', 'exercise_details', ['target_muscles_id'], postgresql_using='gin')
            op.create_index('idx_synergist_muscles_gin', 'exercise_details', ['synergist_muscles_id'], postgresql_using='gin')
            
        except Exception as e:
            # If anything goes wrong, roll back the transaction
            transaction.rollback()
            raise e


def downgrade():
    """Downgrade by dropping and recreating tables in the original order."""
    # Drop tables in correct order (details first)
    op.drop_index('ix_exercise_details_id', table_name='exercise_details')
    op.drop_table('exercise_details')
    op.drop_index('ix_exercises_id', table_name='exercises')
    op.drop_table('exercises') 