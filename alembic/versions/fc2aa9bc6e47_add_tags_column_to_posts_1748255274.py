"""add_tags_column_to_posts_1748255274

Revision ID: fc2aa9bc6e47
Revises: 7e7febfa2d32
Create Date: 2025-05-26 18:27:56.795726

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'fc2aa9bc6e47'
down_revision = '7e7febfa2d32'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('posts', sa.Column('tags', sa.ARRAY(sa.String()), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('posts', 'tags')
    # ### end Alembic commands ### 