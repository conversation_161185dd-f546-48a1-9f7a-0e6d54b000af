"""add health conditions and allergies fields to user

Revision ID: 906e3f73b039
Revises: 90bcfba9700c
Create Date: 2025-04-07 13:23:42.531197

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '906e3f73b039'
down_revision = '90bcfba9700c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('users', sa.Column('health_conditions', sa.ARRAY(sa.String()), nullable=True))
    op.add_column('users', sa.Column('allergies', sa.ARRAY(sa.String()), nullable=True))
    
    # 修改gender字段类型，使用USING子句明确指定转换方式
    # 假设MALE=1, FEMALE=2, OTHER=0
    op.execute("""
        ALTER TABLE users 
        ALTER COLUMN gender TYPE INTEGER 
        USING CASE 
            WHEN gender = 'MALE' THEN 1 
            WHEN gender = 'FEMALE' THEN 2 
            ELSE 0 
        END
    """)
    
    op.alter_column('users', 'activity_level',
               existing_type=sa.SMALLINT(),
               type_=sa.Integer(),
               existing_nullable=True)
               
    # 修改experience_level字段类型，使用USING子句明确指定转换方式
    op.execute("""
        ALTER TABLE users 
        ALTER COLUMN experience_level TYPE INTEGER 
        USING CASE 
            WHEN experience_level = 'BEGINNER' THEN 1 
            WHEN experience_level = 'INTERMEDIATE' THEN 2 
            WHEN experience_level = 'ADVANCED' THEN 3
            ELSE 1
        END
    """)
    
    # 修改fitness_goal字段类型，使用USING子句明确指定转换方式
    op.execute("""
        ALTER TABLE users 
        ALTER COLUMN fitness_goal TYPE INTEGER 
        USING CASE 
            WHEN fitness_goal = 'WEIGHT_LOSS' THEN 1 
            WHEN fitness_goal = 'MAINTENANCE' THEN 2
            WHEN fitness_goal = 'MUSCLE_GAIN' THEN 3
            WHEN fitness_goal = 'ENDURANCE' THEN 4
            ELSE 1
        END
    """)
    
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('users', 'fitness_goal',
               existing_type=sa.Integer(),
               type_=postgresql.ENUM('WEIGHT_LOSS', 'MUSCLE_GAIN', 'MAINTENANCE', 'ENDURANCE', name='fitnessgoal'),
               existing_nullable=True)
    op.alter_column('users', 'experience_level',
               existing_type=sa.Integer(),
               type_=postgresql.ENUM('BEGINNER', 'INTERMEDIATE', 'ADVANCED', name='experiencelevel'),
               existing_nullable=True)
    op.alter_column('users', 'activity_level',
               existing_type=sa.Integer(),
               type_=sa.SMALLINT(),
               existing_nullable=True)
    op.alter_column('users', 'gender',
               existing_type=sa.Integer(),
               type_=postgresql.ENUM('MALE', 'FEMALE', 'OTHER', name='gender'),
               existing_nullable=True)
    op.drop_column('users', 'allergies')
    op.drop_column('users', 'health_conditions')
    # ### end Alembic commands ### 