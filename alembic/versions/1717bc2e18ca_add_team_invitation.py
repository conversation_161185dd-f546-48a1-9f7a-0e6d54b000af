"""add_team_invitation

Revision ID: 1717bc2e18ca
Revises: 42f4542f0984
Create Date: 2025-05-01 17:45:34.832680

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '1717bc2e18ca'
down_revision = '42f4542f0984'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('team_invitations',
    sa.<PERSON>umn('id', sa.Integer(), nullable=False),
    sa.<PERSON>umn('team_id', sa.Integer(), nullable=True),
    sa.<PERSON>umn('inviter_id', sa.Integer(), nullable=True),
    sa.<PERSON>umn('invitee_id', sa.Integer(), nullable=True),
    sa.Column('role', sa.Integer(), nullable=True),
    sa.Column('status', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('expired_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['invitee_id'], ['users.id'], ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['inviter_id'], ['users.id'], ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['team_id'], ['teams.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_team_invitations_id'), 'team_invitations', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_team_invitations_id'), table_name='team_invitations')
    op.drop_table('team_invitations')
    # ### end Alembic commands ### 