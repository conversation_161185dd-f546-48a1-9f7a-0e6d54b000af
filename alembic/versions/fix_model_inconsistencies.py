"""fix model inconsistencies

Revision ID: fix_model_inconsistencies
Revises: add_date_fields_to_training_plan
Create Date: 2024-05-05 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'fix_model_inconsistencies'
down_revision = 'add_date_fields_to_training_plan'
branch_labels = None
depends_on = None


def upgrade():
    # 修复 comments 表
    op.add_column('comments', sa.Column('like_count', sa.Integer(), nullable=True))
    
    # 修复 reports 表
    op.add_column('reports', sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True))
    
    # 创建缺失的营养相关表
    # 注意：这里只创建基本结构，实际的列需要根据模型定义添加
    op.create_table(
        'vitamin_rni',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('age_start_years', sa.Numeric(precision=5, scale=2), nullable=False),
        sa.Column('sex', sa.String(10), nullable=False),
        sa.Column('rec_type', sa.String(10), nullable=False),
        sa.Column('vitamin_a', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('vitamin_a_unit', sa.String(10), nullable=True),
        sa.Column('vitamin_d', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('vitamin_d_unit', sa.String(10), nullable=True),
        sa.Column('vitamin_e', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('vitamin_e_unit', sa.String(10), nullable=True),
        sa.Column('thiamine', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('thiamine_unit', sa.String(10), nullable=True),
        sa.Column('lactoflavin', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('lactoflavin_unit', sa.String(10), nullable=True),
        sa.Column('vitamin_b6', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('vitamin_b6_unit', sa.String(10), nullable=True),
        sa.Column('vitamin_b12', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('vitamin_b12_unit', sa.String(10), nullable=True),
        sa.Column('vitamin_c', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('vitamin_c_unit', sa.String(10), nullable=True),
        sa.Column('niacin', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('niacin_unit', sa.String(10), nullable=True),
        sa.Column('folacin', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('folacin_unit', sa.String(10), nullable=True),
        sa.Column('pantothenic', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('pantothenic_unit', sa.String(10), nullable=True),
        sa.Column('biotin', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('biotin_unit', sa.String(10), nullable=True),
        sa.Column('choline', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('choline_unit', sa.String(10), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    op.create_table(
        'mineral_rni',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('age_start_years', sa.Numeric(precision=5, scale=2), nullable=False),
        sa.Column('sex', sa.String(10), nullable=False),
        sa.Column('rec_type', sa.String(10), nullable=False),
        sa.Column('calcium', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('calcium_unit', sa.String(10), nullable=True),
        sa.Column('phosphorus', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('phosphorus_unit', sa.String(10), nullable=True),
        sa.Column('potassium', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('potassium_unit', sa.String(10), nullable=True),
        sa.Column('sodium', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('sodium_unit', sa.String(10), nullable=True),
        sa.Column('magnesium', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('magnesium_unit', sa.String(10), nullable=True),
        sa.Column('iron', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('iron_unit', sa.String(10), nullable=True),
        sa.Column('zinc', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('zinc_unit', sa.String(10), nullable=True),
        sa.Column('selenium', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('selenium_unit', sa.String(10), nullable=True),
        sa.Column('copper', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('copper_unit', sa.String(10), nullable=True),
        sa.Column('manganese', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('manganese_unit', sa.String(10), nullable=True),
        sa.Column('iodine', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('iodine_unit', sa.String(10), nullable=True),
        sa.Column('fluorine', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('fluorine_unit', sa.String(10), nullable=True),
        sa.Column('molybdenum', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('molybdenum_unit', sa.String(10), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    op.create_table(
        'water_rni',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('age_start_years', sa.Numeric(precision=5, scale=2), nullable=False),
        sa.Column('sex', sa.String(10), nullable=False),
        sa.Column('rec_type', sa.String(10), nullable=False),
        sa.Column('water', sa.Numeric(precision=10, scale=2), nullable=True),
        sa.Column('water_unit', sa.String(10), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    # 创建 user_relations 表
    op.create_table(
        'user_relations',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('related_user_id', sa.Integer(), nullable=False),
        sa.Column('relation_type', sa.String(20), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['related_user_id'], ['users.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # 创建 images 表
    op.create_table(
        'images',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.Integer(), nullable=False),
        sa.Column('url', sa.String(255), nullable=False),
        sa.Column('original_filename', sa.String(255), nullable=True),
        sa.Column('file_size', sa.Integer(), nullable=True),
        sa.Column('width', sa.Integer(), nullable=True),
        sa.Column('height', sa.Integer(), nullable=True),
        sa.Column('mime_type', sa.String(50), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )


def downgrade():
    # 删除添加的列
    op.drop_column('comments', 'like_count')
    op.drop_column('reports', 'updated_at')
    
    # 删除创建的表
    op.drop_table('vitamin_rni')
    op.drop_table('mineral_rni')
    op.drop_table('water_rni')
    op.drop_table('user_relations')
    op.drop_table('images')
