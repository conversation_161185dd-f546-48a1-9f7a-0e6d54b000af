"""
添加 target_body_parts 和 training_scenario 字段到 Workout 模型
Revision ID: bb555779803d
Revises: workout_models_migration
Create Date: 2025-05-06 16:30:17
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'bb555779803d'
down_revision = 'workout_models_migration'
branch_labels = None
depends_on = None


def upgrade():
    # 添加 target_body_parts 字段
    op.add_column('workouts', sa.Column('target_body_parts', sa.ARRAY(sa.Integer()), nullable=True))
    
    # 添加 training_scenario 字段
    op.add_column('workouts', sa.Column('training_scenario', sa.String(20), nullable=True))


def downgrade():
    # 删除 training_scenario 字段
    op.drop_column('workouts', 'training_scenario')
    
    # 删除 target_body_parts 字段
    op.drop_column('workouts', 'target_body_parts')
