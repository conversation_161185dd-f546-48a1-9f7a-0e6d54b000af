"""sync_team_stats_model

Revision ID: 8c9074a2a600
Revises: 90cd3db280d0
Create Date: 2025-05-01 17:33:12.210952

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '8c9074a2a600'
down_revision = '90cd3db280d0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('set_records',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('workout_exercise_id', sa.Integer(), nullable=False),
    sa.Column('set_number', sa.Integer(), nullable=False),
    sa.Column('set_type', sa.String(length=20), nullable=False),
    sa.Column('weight', sa.Float(), nullable=True),
    sa.Column('reps', sa.Integer(), nullable=True),
    sa.Column('completed', sa.<PERSON>(), nullable=True),
    sa.<PERSON>umn('notes', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['workout_exercise_id'], ['workout_exercises.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_set_records_id'), 'set_records', ['id'], unique=False)
    op.drop_index('ix_client_transfer_history_id', table_name='client_transfer_history')
    op.drop_table('client_transfer_history')
    op.drop_index('ix_team_invitations_id', table_name='team_invitations')
    op.drop_table('team_invitations')
    op.add_column('client_relations', sa.Column('start_date', sa.DateTime(), nullable=True))
    op.add_column('client_relations', sa.Column('end_date', sa.DateTime(), nullable=True))
    op.add_column('client_relations', sa.Column('contract_type', sa.String(length=50), nullable=True))
    op.add_column('client_relations', sa.Column('contract_details', sa.Text(), nullable=True))
    op.add_column('client_relations', sa.Column('payment_status', sa.String(length=20), nullable=True))
    op.add_column('client_relations', sa.Column('is_auto_renew', sa.Boolean(), nullable=True))
    op.alter_column('client_relations', 'status',
               existing_type=sa.VARCHAR(length=20),
               nullable=False)
    op.drop_constraint('client_relations_team_id_fkey', 'client_relations', type_='foreignkey')
    op.drop_column('client_relations', 'team_id')
    op.add_column('team_memberships', sa.Column('permissions', sa.JSON(), nullable=True))
    op.add_column('team_memberships', sa.Column('notes', sa.Text(), nullable=True))
    op.add_column('team_memberships', sa.Column('created_at', sa.DateTime(), nullable=True))
    
    # 使用 USING 子句修改 role 字段类型
    op.execute('ALTER TABLE team_memberships ALTER COLUMN role TYPE INTEGER USING CASE WHEN role = \'OWNER\' THEN 1 WHEN role = \'ADMIN\' THEN 2 WHEN role = \'COACH\' THEN 3 WHEN role = \'MEMBER\' THEN 4 ELSE 0 END')
    
    # 使用 USING 子句修改 status 字段类型
    op.execute('ALTER TABLE team_memberships ALTER COLUMN status TYPE INTEGER USING CASE WHEN status = \'PENDING\' THEN 0 WHEN status = \'ACTIVE\' THEN 1 WHEN status = \'INACTIVE\' THEN 2 WHEN status = \'REJECTED\' THEN 3 ELSE 0 END')
    
    op.drop_constraint('uq_team_user', 'team_memberships', type_='unique')
    op.drop_column('team_memberships', 'joined_at')
    op.drop_constraint('team_stats_team_id_key', 'team_stats', type_='unique')
    op.create_index(op.f('ix_team_stats_team_id'), 'team_stats', ['team_id'], unique=False)
    op.drop_constraint('team_stats_team_id_fkey', 'team_stats', type_='foreignkey')
    op.create_foreign_key(None, 'team_stats', 'teams', ['team_id'], ['id'], ondelete='CASCADE')
    op.add_column('teams', sa.Column('logo_url', sa.String(), nullable=True))
    op.alter_column('teams', 'name',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.alter_column('teams', 'description',
               existing_type=sa.VARCHAR(),
               type_=sa.Text(),
               existing_nullable=True)
               
    # 使用 USING 子句修改 status 字段类型
    op.execute('ALTER TABLE teams ALTER COLUMN status TYPE INTEGER USING CASE WHEN status = \'ACTIVE\' THEN 1 WHEN status = \'INACTIVE\' THEN 2 WHEN status = \'SUSPENDED\' THEN 3 ELSE 1 END')
    
    op.drop_index('ix_teams_name', table_name='teams')
    op.add_column('training_plan_templates', sa.Column('fitness_goal', sa.Integer(), nullable=True))
    op.add_column('training_plan_templates', sa.Column('experience_level', sa.Integer(), nullable=True))
    op.add_column('training_plan_templates', sa.Column('template_data', sa.JSON(), nullable=False))
    op.alter_column('training_plan_templates', 'duration_weeks',
               existing_type=sa.INTEGER(),
               nullable=False)
    
    # 使用 USING 子句修改 equipment_required 字段类型
    op.execute("ALTER TABLE training_plan_templates ALTER COLUMN equipment_required TYPE JSON USING to_json(equipment_required::text)::json")
    
    op.drop_column('training_plan_templates', 'sessions_per_week')
    op.drop_column('training_plan_templates', 'target_audience')
    op.drop_column('training_plan_templates', 'difficulty_level')
    op.alter_column('workout_exercises', 'workout_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.alter_column('workout_exercises', 'exercise_id',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.drop_index('ix_workout_exercises_exercise_id', table_name='workout_exercises')
    op.drop_index('ix_workout_exercises_superset_group', table_name='workout_exercises')
    op.drop_index('ix_workout_exercises_workout_id', table_name='workout_exercises')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index('ix_workout_exercises_workout_id', 'workout_exercises', ['workout_id'], unique=False)
    op.create_index('ix_workout_exercises_superset_group', 'workout_exercises', ['superset_group'], unique=False)
    op.create_index('ix_workout_exercises_exercise_id', 'workout_exercises', ['exercise_id'], unique=False)
    op.alter_column('workout_exercises', 'exercise_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.alter_column('workout_exercises', 'workout_id',
               existing_type=sa.INTEGER(),
               nullable=False)
    op.add_column('training_plan_templates', sa.Column('difficulty_level', sa.INTEGER(), autoincrement=False, nullable=True))
    op.add_column('training_plan_templates', sa.Column('target_audience', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('training_plan_templates', sa.Column('sessions_per_week', sa.INTEGER(), autoincrement=False, nullable=True))
    op.alter_column('training_plan_templates', 'equipment_required',
               existing_type=sa.JSON(),
               type_=postgresql.ARRAY(sa.INTEGER()),
               existing_nullable=True)
    op.alter_column('training_plan_templates', 'duration_weeks',
               existing_type=sa.INTEGER(),
               nullable=True)
    op.drop_column('training_plan_templates', 'template_data')
    op.drop_column('training_plan_templates', 'experience_level')
    op.drop_column('training_plan_templates', 'fitness_goal')
    op.create_index('ix_teams_name', 'teams', ['name'], unique=False)
    op.alter_column('teams', 'status',
               existing_type=sa.Integer(),
               type_=sa.VARCHAR(length=20),
               existing_nullable=True)
    op.alter_column('teams', 'description',
               existing_type=sa.Text(),
               type_=sa.VARCHAR(),
               existing_nullable=True)
    op.alter_column('teams', 'name',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.drop_column('teams', 'logo_url')
    op.drop_constraint(None, 'team_stats', type_='foreignkey')
    op.create_foreign_key('team_stats_team_id_fkey', 'team_stats', 'teams', ['team_id'], ['id'])
    op.drop_index(op.f('ix_team_stats_team_id'), table_name='team_stats')
    op.create_unique_constraint('team_stats_team_id_key', 'team_stats', ['team_id'])
    op.add_column('team_memberships', sa.Column('joined_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True))
    op.create_unique_constraint('uq_team_user', 'team_memberships', ['team_id', 'user_id'])
    op.alter_column('team_memberships', 'status',
               existing_type=sa.Integer(),
               type_=sa.VARCHAR(length=20),
               existing_nullable=True)
    op.alter_column('team_memberships', 'role',
               existing_type=sa.Integer(),
               type_=sa.VARCHAR(length=20),
               existing_nullable=True)
    op.drop_column('team_memberships', 'created_at')
    op.drop_column('team_memberships', 'notes')
    op.drop_column('team_memberships', 'permissions')
    op.add_column('client_relations', sa.Column('team_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.create_foreign_key('client_relations_team_id_fkey', 'client_relations', 'teams', ['team_id'], ['id'])
    op.alter_column('client_relations', 'status',
               existing_type=sa.VARCHAR(length=20),
               nullable=True)
    op.drop_column('client_relations', 'is_auto_renew')
    op.drop_column('client_relations', 'payment_status')
    op.drop_column('client_relations', 'contract_details')
    op.drop_column('client_relations', 'contract_type')
    op.drop_column('client_relations', 'end_date')
    op.drop_column('client_relations', 'start_date')
    op.create_table('team_invitations',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('team_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('inviter_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('invitee_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('role', sa.VARCHAR(length=20), autoincrement=False, nullable=True),
    sa.Column('status', sa.VARCHAR(length=20), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('expired_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['invitee_id'], ['users.id'], name='team_invitations_invitee_id_fkey'),
    sa.ForeignKeyConstraint(['inviter_id'], ['users.id'], name='team_invitations_inviter_id_fkey'),
    sa.ForeignKeyConstraint(['team_id'], ['teams.id'], name='team_invitations_team_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='team_invitations_pkey')
    )
    op.create_index('ix_team_invitations_id', 'team_invitations', ['id'], unique=False)
    op.create_table('client_transfer_history',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('client_relation_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('from_coach_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('to_coach_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('reason', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('transferred_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['client_relation_id'], ['client_relations.id'], name='client_transfer_history_client_relation_id_fkey'),
    sa.ForeignKeyConstraint(['from_coach_id'], ['users.id'], name='client_transfer_history_from_coach_id_fkey'),
    sa.ForeignKeyConstraint(['to_coach_id'], ['users.id'], name='client_transfer_history_to_coach_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='client_transfer_history_pkey')
    )
    op.create_index('ix_client_transfer_history_id', 'client_transfer_history', ['id'], unique=False)
    op.drop_index(op.f('ix_set_records_id'), table_name='set_records')
    op.drop_table('set_records')
    # ### end Alembic commands ### 