"""add date fields to training plan

Revision ID: add_date_fields_to_training_plan
Revises:
Create Date: 2023-07-01 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_date_fields_to_training_plan'
down_revision = '20240520_add_post_counts'  # 使用最新的头版本
branch_labels = None
depends_on = None


def upgrade():
    # 添加 start_date 和 end_date 字段到 training_plans 表
    op.add_column('training_plans', sa.Column('start_date', sa.DateTime(timezone=True), nullable=True))
    op.add_column('training_plans', sa.Column('end_date', sa.DateTime(timezone=True), nullable=True))

    # 添加 scheduled_date 字段到 workouts 表
    op.add_column('workouts', sa.Column('scheduled_date', sa.DateTime(timezone=True), nullable=True))

    # 创建 scheduled_date 索引
    op.create_index(op.f('ix_workouts_scheduled_date'), 'workouts', ['scheduled_date'], unique=False)

    # 创建 start_date 索引
    op.create_index(op.f('ix_training_plans_start_date'), 'training_plans', ['start_date'], unique=False)


def downgrade():
    # 删除索引
    op.drop_index(op.f('ix_training_plans_start_date'), table_name='training_plans')
    op.drop_index(op.f('ix_workouts_scheduled_date'), table_name='workouts')

    # 删除字段
    op.drop_column('workouts', 'scheduled_date')
    op.drop_column('training_plans', 'end_date')
    op.drop_column('training_plans', 'start_date')
