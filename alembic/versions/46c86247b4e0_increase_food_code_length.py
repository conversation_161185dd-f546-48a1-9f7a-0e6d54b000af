"""increase_food_code_length

Revision ID: 46c86247b4e0
Revises: f6cde17dea26
Create Date: 2025-04-24 23:39:14.876142

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '46c86247b4e0'
down_revision = 'f6cde17dea26'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('foods', 'code',
               existing_type=sa.VARCHAR(length=64),
               type_=sa.VARCHAR(length=128),
               existing_nullable=True)
    op.drop_index('ix_share_tracks_id', table_name='share_tracks')
    op.drop_table('share_tracks')
    op.drop_index('ix_workout_exercises_exercise_id', table_name='workout_exercises')
    op.drop_index('ix_workout_exercises_id', table_name='workout_exercises')
    op.drop_index('ix_workout_exercises_superset_group', table_name='workout_exercises')
    op.drop_index('ix_workout_exercises_workout_id', table_name='workout_exercises')
    op.drop_table('workout_exercises')
    op.drop_index('idx_training_records_date', table_name='training_records')
    op.drop_index('idx_training_records_exercise_id', table_name='training_records')
    op.drop_index('idx_training_records_user_date', table_name='training_records')
    op.drop_index('idx_training_records_user_id', table_name='training_records')
    op.drop_table('training_records')
    op.drop_index('ix_workouts_id', table_name='workouts')
    op.drop_index('ix_workouts_training_plan_id', table_name='workouts')
    op.drop_table('workouts')
    op.alter_column('user_training_records', 'preferred_exercise_types',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
    op.alter_column('user_training_records', 'avoided_exercise_types',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
    op.alter_column('user_training_records', 'fitness_goals',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
    op.alter_column('user_training_records', 'target_muscle_groups',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
    op.alter_column('user_training_records', 'injuries',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
    op.alter_column('user_training_records', 'medical_conditions',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True)
    op.drop_index('ix_user_training_records_user_id', table_name='user_training_records')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('foods', 'code',
               existing_type=sa.VARCHAR(length=128),
               type_=sa.VARCHAR(length=64),
               existing_nullable=True)
    op.create_index('ix_user_training_records_user_id', 'user_training_records', ['user_id'], unique=False)
    op.alter_column('user_training_records', 'medical_conditions',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.alter_column('user_training_records', 'injuries',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.alter_column('user_training_records', 'target_muscle_groups',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.alter_column('user_training_records', 'fitness_goals',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.alter_column('user_training_records', 'avoided_exercise_types',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.alter_column('user_training_records', 'preferred_exercise_types',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True)
    op.create_table('workouts',
    sa.Column('id', sa.INTEGER(), server_default=sa.text("nextval('workouts_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('training_plan_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('name', sa.VARCHAR(length=100), autoincrement=False, nullable=False),
    sa.Column('day_of_week', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('day_number', sa.SMALLINT(), autoincrement=False, nullable=False),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('estimated_duration', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['training_plan_id'], ['training_plans.id'], name='workouts_training_plan_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='workouts_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_index('ix_workouts_training_plan_id', 'workouts', ['training_plan_id'], unique=False)
    op.create_index('ix_workouts_id', 'workouts', ['id'], unique=False)
    op.create_table('training_records',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('exercise_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('sets', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=False),
    sa.Column('total_sets', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('total_reps', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('total_weight', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('total_duration', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('total_distance', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True),
    sa.Column('note', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('date', sa.DATE(), server_default=sa.text('CURRENT_DATE'), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name='training_records_pkey')
    )
    op.create_index('idx_training_records_user_id', 'training_records', ['user_id'], unique=False)
    op.create_index('idx_training_records_user_date', 'training_records', ['user_id', 'date'], unique=False)
    op.create_index('idx_training_records_exercise_id', 'training_records', ['exercise_id'], unique=False)
    op.create_index('idx_training_records_date', 'training_records', ['date'], unique=False)
    op.create_table('workout_exercises',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('workout_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('exercise_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('sets', sa.SMALLINT(), autoincrement=False, nullable=False),
    sa.Column('reps', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('rest_seconds', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('order', sa.SMALLINT(), server_default=sa.text("'0'::smallint"), autoincrement=False, nullable=False),
    sa.Column('notes', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('exercise_type', sa.VARCHAR(length=50), server_default=sa.text("'weight_reps'::character varying"), autoincrement=False, nullable=False),
    sa.Column('superset_group', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('weight', sa.VARCHAR(length=50), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['exercise_id'], ['exercises.id'], name='workout_exercises_exercise_id_fkey'),
    sa.ForeignKeyConstraint(['workout_id'], ['workouts.id'], name='workout_exercises_workout_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='workout_exercises_pkey')
    )
    op.create_index('ix_workout_exercises_workout_id', 'workout_exercises', ['workout_id'], unique=False)
    op.create_index('ix_workout_exercises_superset_group', 'workout_exercises', ['superset_group'], unique=False)
    op.create_index('ix_workout_exercises_id', 'workout_exercises', ['id'], unique=False)
    op.create_index('ix_workout_exercises_exercise_id', 'workout_exercises', ['exercise_id'], unique=False)
    op.create_table('share_tracks',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('shared_by', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('scanned_by', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('share_type', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('page', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('scene', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('qrcode_url', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('is_active', sa.BOOLEAN(), server_default=sa.text('true'), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['scanned_by'], ['users.id'], name='share_tracks_scanned_by_fkey', ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['shared_by'], ['users.id'], name='share_tracks_shared_by_fkey', ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id', name='share_tracks_pkey')
    )
    op.create_index('ix_share_tracks_id', 'share_tracks', ['id'], unique=False)
    # ### end Alembic commands ### 