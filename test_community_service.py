#!/usr/bin/env python3
from sqlalchemy.orm import Session
import asyncio
import traceback
import json
from app.db.session import SessionLocal
from app.services.community_service import CommunityService
from app.models.community import Post, PostStatus, Image, PostLike
from app.models.user import User
from app.models.daily_workout import DailyWorkout, DailyWorkoutStatus

async def test_get_posts():
    db = SessionLocal()
    try:
        print("\n===== 开始社区服务测试 =====")
        service = CommunityService(db)
        print("服务实例化成功")
        
        # 检查 get_daily_workout 方法
        print("\n-- 测试 get_daily_workout 方法 --")
        try:
            # 获取一个有效的daily_workout_id
            workout = db.query(DailyWorkout).filter(
                DailyWorkout.status == DailyWorkoutStatus.ACTIVE
            ).first()
            
            if workout:
                print(f"找到一个有效的DailyWorkout: ID={workout.id}")
                workout_result = await service.get_daily_workout(workout_id=workout.id)
                if workout_result:
                    print(f"成功获取DailyWorkout: ID={workout_result.id}")
                else:
                    print("未能获取DailyWorkout")
            else:
                print("数据库中没有找到活动状态的DailyWorkout")
        except Exception as e:
            print(f"测试get_daily_workout时出错: {str(e)}")
            traceback.print_exc()
        
        # 测试get_posts方法
        print("\n-- 测试 get_posts 方法 --")
        try:
            print("尝试调用get_posts方法...")
            # 检查数据库中是否有帖子
            posts = db.query(Post).filter(Post.status == PostStatus.ACTIVE).all()
            print(f"数据库中有 {len(posts)} 个活动帖子")
            
            # 尝试直接获取一个帖子
            if posts:
                print(f"示例帖子: ID={posts[0].id}, 标题={posts[0].title}")
                
                # 测试get_post方法
                print("\n-- 测试 get_post 方法 --")
                try:
                    print(f"尝试调用get_post方法获取帖子ID={posts[0].id}...")
                    post = await service.get_post(id=posts[0].id, current_user_id=None)
                    if post:
                        print(f"成功获取帖子: ID={post['id']}, 标题={post['title']}")
                        print(f"帖子字段: {list(post.keys())}")
                    else:
                        print("未能获取帖子")
                except Exception as e:
                    print(f"调用get_post时出错: {str(e)}")
                    traceback.print_exc()
                
                # 测试_get_post_images方法
                print("\n-- 测试 _get_post_images 方法 --")
                try:
                    print(f"尝试获取帖子ID={posts[0].id}的图片...")
                    images = await service._get_post_images(posts[0].id)
                    print(f"获取到 {len(images)} 张图片")
                    # 检查数据库中是否有图片
                    db_images = db.query(Image).filter(Image.post_id == posts[0].id).all()
                    print(f"数据库中有 {len(db_images)} 张图片与此帖子关联")
                except Exception as e:
                    print(f"获取帖子图片时出错: {str(e)}")
                    traceback.print_exc()
            
            # 测试get_posts方法
            print("\n-- 测试 get_posts 方法 --")
            try:
                result = await service.get_posts(skip=0, limit=20, current_user_id=None)
                print(f"调用get_posts成功，返回数据结构: {result.keys()}")
                print(f"返回的帖子数量: {len(result['items'])}")
                if result['items']:
                    print(f"第一个帖子字段: {list(result['items'][0].keys())}")
            except Exception as e:
                print(f"调用get_posts时出错: {str(e)}")
                traceback.print_exc()
            
        except Exception as e:
            print(f"测试过程中出错: {str(e)}")
            traceback.print_exc()
    except Exception as e:
        print(f"创建服务时出错: {str(e)}")
        traceback.print_exc()
    finally:
        db.close()
        print("\n===== 测试结束 =====")

if __name__ == "__main__":
    asyncio.run(test_get_posts()) 