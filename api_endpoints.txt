
科学健身 API 路由列表
================================================================================
HTTP Method Path                                     Name                          
--------------------------------------------------------------------------------
GET        /                                        read_root                     
GET        /admin/                                  admin_dashboard               
GET        /admin/settings                          list_settings                 
GET        /admin/share_tracks                      list_share_tracks             
GET        /admin/users                             list_users                    
GET        /admin/users/{user_id}                   user_detail                   
POST       /api/v1/ai-assistant/ai_chat             chat                          
GET        /api/v1/ai-assistant/ai_conversations/{conversation_id} get_conversation              
GET        /api/v1/ai-assistant/conversations       get_user_conversations        
DELETE     /api/v1/ai-assistant/conversations/{session_id} delete_conversation           
GET        /api/v1/ai-assistant/conversations/{session_id}/messages get_conversation_messages     
POST       /api/v1/ai-assistant/message             create_message                
POST       /api/v1/ai-food/analyze-base64-for-chat  analyze_base64_food_image_for_chat
POST       /api/v1/ai-food/analyze-for-chat         analyze_food_image_for_chat   
POST       /api/v1/auth/login/wechat                wechat_login                  
POST       /api/v1/auth/logout                      logout                        
POST       /api/v1/auth/refresh-token               refresh_token                 
POST       /api/v1/auth/wechat/code2session         wechat_code2session           
POST       /api/v1/auth/wechat/phone                bind_wechat_phone             
POST       /api/v1/chat/chat                        chat                          
GET        /api/v1/chat/conversations               get_conversations             
GET        /api/v1/chat/conversations/{conversation_id} get_conversation              
DELETE     /api/v1/chat/conversations/{session_id}  delete_conversation           
GET        /api/v1/chat/conversations/{session_id}/messages get_conversation_messages     
GET        /api/v1/chat/conversations/{session_id}/messages/since/{message_id} get_new_messages_since        
POST       /api/v1/chat/message                     send_message                  
PUT        /api/v1/chat/messages/{message_id}       update_message                
GET        /api/v1/chat/recent-conversations        get_recent_conversations      
GET        /api/v1/chat/recent-messages             get_recent_messages           
POST       /api/v1/chat/sessions/{session_id}/messages add_message                   
GET        /api/v1/chat/stream/{session_id}         websocket_stream_info         
GET        /api/v1/chat/stream/{session_id}/connect wechat_websocket_connect      
POST       /api/v1/chat/stream/{session_id}/message send_stream_message           
GET        /api/v1/chat/stream/{session_id}/poll    poll_new_messages             
POST       /api/v1/chat/update_user_info            update_user_info              
PUT        /api/v1/community/comments/{comment_id}  update_comment                
DELETE     /api/v1/community/comments/{comment_id}  delete_comment                
POST       /api/v1/community/comments/{comment_id}/like/ like_comment                  
POST       /api/v1/community/comments/{comment_id}/report/ report_comment                
POST       /api/v1/community/daily-workouts/        create_daily_workout          
GET        /api/v1/community/daily-workouts/        get_daily_workouts            
GET        /api/v1/community/daily-workouts/search/ search_daily_workouts         
PUT        /api/v1/community/daily-workouts/{workout_id} update_daily_workout          
DELETE     /api/v1/community/daily-workouts/{workout_id} delete_daily_workout          
GET        /api/v1/community/daily-workouts/{workout_id} get_daily_workout             
POST       /api/v1/community/images/                create_image                  
PUT        /api/v1/community/images/{image_id}      update_image                  
DELETE     /api/v1/community/images/{image_id}      delete_image                  
GET        /api/v1/community/notifications/         get_notifications             
GET        /api/v1/community/notifications/filter/  filter_notifications          
PATCH      /api/v1/community/notifications/read-all/ mark_all_notifications_as_read
DELETE     /api/v1/community/notifications/{notification_id} delete_notification           
PATCH      /api/v1/community/notifications/{notification_id}/read/ mark_notification_as_read     
POST       /api/v1/community/posts/                 create_post                   
GET        /api/v1/community/posts/                 get_posts                     
GET        /api/v1/community/posts/search/          search_posts                  
PUT        /api/v1/community/posts/{post_id}        update_post                   
DELETE     /api/v1/community/posts/{post_id}        delete_post                   
GET        /api/v1/community/posts/{post_id}        get_post                      
POST       /api/v1/community/posts/{post_id}/comments/ create_comment                
POST       /api/v1/community/posts/{post_id}/like/  like_post                     
POST       /api/v1/community/posts/{post_id}/report/ report_post                   
POST       /api/v1/community/users/{user_id}/follow/ follow_user                   
DELETE     /api/v1/community/users/{user_id}/follow/ unfollow_user                 
GET        /api/v1/community/users/{user_id}/followers/ get_followers                 
GET        /api/v1/community/users/{user_id}/following/ get_following                 
POST       /api/v1/community/workouts/{workout_id}/share share_workout                 
GET        /api/v1/docs                             custom_swagger_ui_html        
GET        /api/v1/exercise/avatar/image/exercises/{filename} get_exercise_avatar_style     
GET        /api/v1/exercise/body-parts/             get_body_parts                
POST       /api/v1/exercise/body-parts/             create_body_part              
GET        /api/v1/exercise/equipment/              get_equipment                 
POST       /api/v1/exercise/equipment/              create_equipment              
GET        /api/v1/exercise/exercises/              get_exercises                 
POST       /api/v1/exercise/exercises/              create_exercise               
GET        /api/v1/exercise/exercises/search        search_exercises              
GET        /api/v1/exercise/exercises/{exercise_id} get_exercise                  
PUT        /api/v1/exercise/exercises/{exercise_id} update_exercise               
DELETE     /api/v1/exercise/exercises/{exercise_id} delete_exercise               
GET        /api/v1/exercise/exercises/{exercise_id}/detail get_exercise_detail           
POST       /api/v1/exercise/exercises/{exercise_id}/detail create_exercise_detail        
PUT        /api/v1/exercise/exercises/{exercise_id}/detail update_exercise_detail        
POST       /api/v1/exercise/exercises/{exercise_id}/hit hit_exercise                  
GET        /api/v1/exercise/gif/{filename}          get_exercise_gif              
GET        /api/v1/exercise/image/direct/{filename} get_exercise_image_direct     
GET        /api/v1/exercise/image/exercises/{filename} get_exercise_image_by_path    
GET        /api/v1/exercise/image/{filename}        get_exercise_image            
GET        /api/v1/exercise/image_file/{filename}   get_exercise_image_file       
GET        /api/v1/exercise/muscles/                get_muscles                   
POST       /api/v1/exercise/muscles/                create_muscle                 
GET        /api/v1/exercise/video/{filename}        get_exercise_video            
GET        /api/v1/exercise/video_wx/{filename}     get_exercise_video_wx         
OPTIONS    /api/v1/food-recognition                 food_recognition_root         
POST       /api/v1/food-recognition                 food_recognition_root         
GET        /api/v1/food-recognition                 food_recognition_root         
GET        /api/v1/food-recognition/                read_food_recognitions        
POST       /api/v1/food-recognition/                create_food_recognition       
POST       /api/v1/food-recognition/analyze         analyze_food_image            
POST       /api/v1/food-recognition/analyze-base64  analyze_base64_food_image     
POST       /api/v1/food-recognition/analyze-smart   analyze_food_smart            
GET        /api/v1/food-recognition/date-range      get_recognitions_by_date_range
GET        /api/v1/food-recognition/image/{secure_path}/{date}/{filename} get_food_image                
GET        /api/v1/food-recognition/pending         get_pending_recognitions      
GET        /api/v1/food-recognition/stats           get_recognition_stats         
GET        /api/v1/food-recognition/status/{status} get_recognitions_by_status    
GET        /api/v1/food-recognition/{recognition_id} read_food_recognition         
PUT        /api/v1/food-recognition/{recognition_id} update_food_recognition       
DELETE     /api/v1/food-recognition/{recognition_id} delete_food_recognition       
POST       /api/v1/food-recognition/{recognition_id}/confirm confirm_recognition           
POST       /api/v1/food-recognition/{recognition_id}/debug-confirm debug_confirm_recognition     
POST       /api/v1/food-recognition/{recognition_id}/reject reject_recognition            
GET        /api/v1/food/                            read_foods                    
POST       /api/v1/food/                            create_food                   
GET        /api/v1/food/categories                  read_food_categories          
GET        /api/v1/food/code/{code}                 read_food_by_code             
POST       /api/v1/food/nutrients                   get_nutrients                 
GET        /api/v1/food/search                      search_foods_by_name          
GET        /api/v1/food/types                       read_food_types               
GET        /api/v1/food/{food_id}                   read_food                     
PUT        /api/v1/food/{food_id}                   update_food                   
DELETE     /api/v1/food/{food_id}                   delete_food                   
PATCH, DELETE, GET, POST, PUT, OPTIONS, HEAD /api/v1/food_recognition{path:path}      redirect_food_recognition     
GET        /api/v1/gamification/achievement/all     get_all_achievements          
GET        /api/v1/gamification/achievement/by-category/{category} get_achievements_by_category  
POST       /api/v1/gamification/achievement/claim-milestone/{milestone_id} claim_milestone_reward        
POST       /api/v1/gamification/achievement/claim/{achievement_id} claim_achievement_reward      
GET        /api/v1/gamification/achievement/completed get_user_completed_achievements
GET        /api/v1/gamification/achievement/completed-milestones get_user_completed_milestones 
GET        /api/v1/gamification/achievement/milestone-progress get_user_milestone_progress   
GET        /api/v1/gamification/achievement/milestones get_all_milestones            
GET        /api/v1/gamification/achievement/progress get_user_achievement_progress 
POST       /api/v1/gamification/achievement/trigger-event/{event_type} trigger_achievement_event     
POST       /api/v1/gamification/achievement/update-milestone/{milestone_id} update_milestone_active_date  
GET        /api/v1/gamification/card/all            get_all_cards                 
GET        /api/v1/gamification/card/by-type/{card_type} get_cards_by_type             
GET        /api/v1/gamification/card/detail/{card_id} get_card_detail               
GET        /api/v1/gamification/card/effects        get_card_effects              
GET        /api/v1/gamification/card/equipped       get_equipped_cards            
GET        /api/v1/gamification/card/synthesis-recipe/{recipe_id} get_synthesis_recipe_detail   
GET        /api/v1/gamification/card/synthesis-recipes get_synthesis_recipes         
POST       /api/v1/gamification/card/synthesize/{recipe_id} synthesize_card               
POST       /api/v1/gamification/card/toggle-equip/{card_id} toggle_card_equip_status      
GET        /api/v1/gamification/card/user-cards     get_user_cards                
GET        /api/v1/gamification/currency/balance    get_user_currency             
GET        /api/v1/gamification/currency/check-daily-limit check_daily_earning_limit     
GET        /api/v1/gamification/currency/daily-transactions get_daily_transactions        
POST       /api/v1/gamification/currency/purchase   purchase_item                 
GET        /api/v1/gamification/currency/purchases  get_user_purchases            
GET        /api/v1/gamification/currency/shop       get_shop_items                
GET        /api/v1/gamification/currency/shop/{item_id} get_shop_item_detail          
GET        /api/v1/gamification/currency/transactions get_user_transactions         
GET        /api/v1/gamification/level/              get_user_level                
GET        /api/v1/gamification/level/active-title  get_user_active_title         
POST       /api/v1/gamification/level/add-diet-experience add_diet_experience           
POST       /api/v1/gamification/level/add-exercise-experience add_exercise_experience       
GET        /api/v1/gamification/level/attributes    get_user_attributes           
GET        /api/v1/gamification/level/detail        get_user_level_detail         
POST       /api/v1/gamification/level/set-active-title/{title_id} set_active_title              
GET        /api/v1/gamification/level/titles        get_user_titles               
GET        /api/v1/gamification/task/active         get_user_active_tasks         
GET        /api/v1/gamification/task/all            get_all_tasks                 
GET        /api/v1/gamification/task/by-category/{category} get_tasks_by_category         
GET        /api/v1/gamification/task/by-type/{task_type} get_tasks_by_type             
GET        /api/v1/gamification/task/check-daily-checkin check_daily_checkin           
GET        /api/v1/gamification/task/checkin-history get_checkin_history           
POST       /api/v1/gamification/task/claim/{task_id} claim_task_reward             
GET        /api/v1/gamification/task/completed      get_user_completed_tasks      
POST       /api/v1/gamification/task/daily-checkin  create_daily_checkin          
POST       /api/v1/gamification/task/generate-daily generate_daily_tasks          
POST       /api/v1/gamification/task/generate-weekly generate_weekly_tasks         
POST       /api/v1/gamification/task/trigger-event/{event_type} trigger_task_event            
POST       /api/v1/gamification/task/update-progress update_task_progress          
GET        /api/v1/llm-logs/by-date/{date}          get_logs_by_date              
GET        /api/v1/llm-logs/by-user/{user_id}       get_logs_by_user              
POST       /api/v1/llm-logs/clean                   clean_old_logs                
GET        /api/v1/llm-logs/details                 get_log_details               
GET        /api/v1/llm-logs/image                   get_log_image                 
GET        /api/v1/llm-logs/qa-pairs/by-user/{user_id} get_qa_pairs_by_user          
GET        /api/v1/meal/                            read_user_meals               
POST       /api/v1/meal/                            create_meal                   
GET        /api/v1/meal/daily                       read_daily_nutrition          
DELETE     /api/v1/meal/food-items/{food_item_id}   remove_food_item              
GET        /api/v1/meal/food-items/{food_item_id}   read_food_item                
PUT        /api/v1/meal/food-items/{food_item_id}   update_food_item              
GET        /api/v1/meal/{meal_id}                   read_meal                     
PUT        /api/v1/meal/{meal_id}                   update_meal                   
DELETE     /api/v1/meal/{meal_id}                   delete_meal                   
POST       /api/v1/meal/{meal_id}/food-items        add_food_item                 
POST       /api/v1/nutrient/daily-target            get_daily_nutrient_target     
GET        /api/v1/nutrient/recommendation          get_nutrient_recommendation   
GET        /api/v1/openapi.json                     get_openapi_endpoint          
GET        /api/v1/ping                             ping                          
DELETE     /api/v1/qrcode/cleanup                   cleanup_expired_qrcodes       
POST       /api/v1/qrcode/generate                  create_qrcode                 
GET        /api/v1/qrcode/image/{secure_path}/{filename} get_qrcode_image              
GET        /api/v1/share/scan/{share_code}          track_scan                    
POST       /api/v1/share/track                      track_share                   
PUT        /api/v1/team/clients/training-plans/{plan_id} update_client_training_plan   
GET        /api/v1/team/clients/{client_relation_id}/stats get_client_statistics         
POST       /api/v1/team/clients/{client_relation_id}/training-plans/ create_client_training_plan   
GET        /api/v1/team/clients/{client_relation_id}/training-plans/ list_client_training_plans    
PUT        /api/v1/team/sessions/{session_id}/complete complete_training_session     
POST       /api/v1/team/sessions/{session_id}/exercises/{exercise_id}/records record_exercise_set           
POST       /api/v1/team/sessions/{session_id}/start start_training_session        
POST       /api/v1/team/teams/                      create_team                   
GET        /api/v1/team/teams/                      list_user_teams               
DELETE     /api/v1/team/teams/invitations/{invitation_id} cancel_invitation             
PUT        /api/v1/team/teams/invitations/{invitation_id}/accept accept_team_invitation        
PUT        /api/v1/team/teams/invitations/{invitation_id}/reject reject_team_invitation        
GET        /api/v1/team/teams/{team_id}             get_team_detail               
PUT        /api/v1/team/teams/{team_id}             update_team_info              
DELETE     /api/v1/team/teams/{team_id}             delete_team                   
GET        /api/v1/team/teams/{team_id}/clients     list_team_clients             
POST       /api/v1/team/teams/{team_id}/clients/    assign_team_client            
GET        /api/v1/team/teams/{team_id}/clients/{client_relation_id} get_client_detail             
PUT        /api/v1/team/teams/{team_id}/clients/{client_relation_id}/deactivate deactivate_client             
PUT        /api/v1/team/teams/{team_id}/clients/{client_relation_id}/reactivate reactivate_client             
POST       /api/v1/team/teams/{team_id}/clients/{client_relation_id}/transfer transfer_team_client          
GET        /api/v1/team/teams/{team_id}/invitations list_team_invitations         
POST       /api/v1/team/teams/{team_id}/invitations/ create_team_invitation        
GET        /api/v1/team/teams/{team_id}/members     list_team_members             
POST       /api/v1/team/teams/{team_id}/members/    add_team_member               
PUT        /api/v1/team/teams/{team_id}/members/{user_id} update_member_role            
DELETE     /api/v1/team/teams/{team_id}/members/{user_id} remove_team_member            
GET        /api/v1/team/teams/{team_id}/stats       get_team_statistics           
POST       /api/v1/team/teams/{team_id}/templates/  create_plan_template          
GET        /api/v1/team/teams/{team_id}/templates/  list_plan_templates           
GET        /api/v1/team/training-plans/{plan_id}/sessions list_training_sessions        
GET        /api/v1/team/user/invitations            list_user_invitations         
GET        /api/v1/training-plan/                   get_user_plans                
POST       /api/v1/training-plan/daily              generate_daily_workout        
POST       /api/v1/training-plan/generate           generate_training_plan        
POST       /api/v1/training-plan/templates/{template_id}/create create_from_template          
GET        /api/v1/training-plan/workouts/by-date   get_workouts_by_date          
POST       /api/v1/training-plan/workouts/{workout_id}/status update_workout_status         
GET        /api/v1/training-plan/{plan_id}          get_plan_detail               
DELETE     /api/v1/training-plan/{plan_id}          delete_plan                   
PUT        /api/v1/training-plan/{plan_id}          update_training_plan          
POST       /api/v1/training-plan/{plan_id}/status   update_plan_status            
POST       /api/v1/training-plan/{plan_id}/template create_template               
GET        /api/v1/training-plan/{plan_id}/workouts get_plan_workouts             
GET        /api/v1/training-plan/{plan_id}/workouts/exercises get_plan_workouts_with_exercises
POST       /api/v1/user/avatar                      upload_avatar                 
GET        /api/v1/user/avatar/image/{secure_path}/{filename} get_avatar_image              
GET        /api/v1/user/check                       check_user                    
GET        /api/v1/user/exists                      check_user_exists             
GET        /api/v1/user/food-image/{secure_path}/{date}/{filename} get_food_image                
GET        /api/v1/user/profile                     get_user_profile              
POST       /api/v1/user/profile                     update_user_profile           
GET        /api/v1/user/settings                    get_user_settings             
POST       /api/v1/user/settings                    update_user_settings          
POST       /api/v1/user/settings/ai-character       update_ai_character_type      
GET        /api/v1/user/settings/ai-character       get_ai_character_type         
POST       /api/v1/user/upload-food-image           upload_food_image             
POST       /api/v1/video/cleanup                    cleanup_old_files             
GET        /api/v1/video/download/{filename}        download_file                 
POST       /api/v1/video/process_video              process_video                 
GET        /api/v1/workout/by-date/{date}           get_workouts_by_date          
GET        /api/v1/workout/{workout_id}             get_workout_detail            
POST       /api/v1/workout/{workout_id}/link-session/{session_id} link_with_training_session    
GET        /api/v1/workout/{workout_id}/statistics  get_workout_statistics        
PUT        /api/v1/workout/{workout_id}/status      update_workout_status         
PUT        /api/v1/workout/{workout_id}/update-full update_workout_full           
GET        /api/v1/workouts/by-date/{date}          get_workouts_by_date          
GET        /api/v1/workouts/{workout_id}            get_workout_detail            
POST       /api/v1/workouts/{workout_id}/link-session/{session_id} link_with_training_session    
GET        /api/v1/workouts/{workout_id}/statistics get_workout_statistics        
PUT        /api/v1/workouts/{workout_id}/status     update_workout_status         
PUT        /api/v1/workouts/{workout_id}/update-full update_workout_full           
GET        /api/v2/chat/conversations               get_conversations             
DELETE     /api/v2/chat/conversations/{session_id}  delete_conversation           
GET        /api/v2/chat/conversations/{session_id}/messages/since/{message_id} get_new_messages_since        
POST       /api/v2/chat/generate_training_plan      generate_training_plan        
POST       /api/v2/chat/message                     send_message                  
PUT        /api/v2/chat/messages/{message_id}       update_message                
GET        /api/v2/chat/poll/{session_id}           poll_new_messages             
GET        /api/v2/chat/recent-conversations        get_recent_conversations      
GET        /api/v2/chat/recent-messages             get_recent_messages           
GET        /api/v2/chat/sessions/{session_id}/messages get_conversation_messages     
POST       /api/v2/chat/sessions/{session_id}/messages add_message                   
GET        /api/v2/chat/stream/{session_id}         websocket_stream_info         
GET        /api/v2/chat/stream/{session_id}/connect wechat_websocket_connect      
POST       /api/v2/chat/stream/{session_id}/message send_stream_message           
POST       /api/v2/chat/update_user_info            update_user_info              
GET        /health                                  health_check                  
GET        /meal-images/{filename}                  get_food_image_by_filename    


按标签分组的端点
================================================================================

admin:
--------------------------------------------------------------------------------
HTTP Method Path                                     Summary                       
--------------------------------------------------------------------------------
GET        /admin/                                                                
GET        /admin/settings                                                        
GET        /admin/share_tracks                                                    
GET        /admin/users                                                           
GET        /admin/users/{user_id}                                                 

ai-assistant:
--------------------------------------------------------------------------------
HTTP Method Path                                     Summary                       
--------------------------------------------------------------------------------
POST       /api/v1/ai-assistant/ai_chat                                           
GET        /api/v1/ai-assistant/ai_conversations/{conversation_id}                               
GET        /api/v1/ai-assistant/conversations                                     
DELETE     /api/v1/ai-assistant/conversations/{session_id}                               
GET        /api/v1/ai-assistant/conversations/{session_id}/messages                               
POST       /api/v1/ai-assistant/message                                           

ai-food-recognition:
--------------------------------------------------------------------------------
HTTP Method Path                                     Summary                       
--------------------------------------------------------------------------------
POST       /api/v1/ai-food/analyze-base64-for-chat                                
POST       /api/v1/ai-food/analyze-for-chat                                       

auth:
--------------------------------------------------------------------------------
HTTP Method Path                                     Summary                       
--------------------------------------------------------------------------------
POST       /api/v1/auth/login/wechat                                              
POST       /api/v1/auth/logout                                                    
POST       /api/v1/auth/refresh-token                                             
POST       /api/v1/auth/wechat/code2session                                       
POST       /api/v1/auth/wechat/phone                                              

chat:
--------------------------------------------------------------------------------
HTTP Method Path                                     Summary                       
--------------------------------------------------------------------------------
POST       /api/v1/chat/chat                                                      
GET        /api/v1/chat/conversations                                             
GET        /api/v1/chat/conversations/{conversation_id}                               
DELETE     /api/v1/chat/conversations/{session_id}                                
GET        /api/v1/chat/conversations/{session_id}/messages                               
GET        /api/v1/chat/conversations/{session_id}/messages/since/{message_id}                               
POST       /api/v1/chat/message                                                   
PUT        /api/v1/chat/messages/{message_id}                                     
GET        /api/v1/chat/recent-conversations                                      
GET        /api/v1/chat/recent-messages                                           
POST       /api/v1/chat/sessions/{session_id}/messages                               
GET        /api/v1/chat/stream/{session_id}                                       
GET        /api/v1/chat/stream/{session_id}/connect                               
POST       /api/v1/chat/stream/{session_id}/message                               
GET        /api/v1/chat/stream/{session_id}/poll                                  
POST       /api/v1/chat/update_user_info                                          
GET        /api/v2/chat/conversations                                             
DELETE     /api/v2/chat/conversations/{session_id}                                
GET        /api/v2/chat/conversations/{session_id}/messages/since/{message_id}                               
POST       /api/v2/chat/generate_training_plan                                    
POST       /api/v2/chat/message                                                   
PUT        /api/v2/chat/messages/{message_id}                                     
GET        /api/v2/chat/poll/{session_id}                                         
GET        /api/v2/chat/recent-conversations                                      
GET        /api/v2/chat/recent-messages                                           
GET        /api/v2/chat/sessions/{session_id}/messages                               
POST       /api/v2/chat/sessions/{session_id}/messages                               
GET        /api/v2/chat/stream/{session_id}                                       
GET        /api/v2/chat/stream/{session_id}/connect                               
POST       /api/v2/chat/stream/{session_id}/message                               
POST       /api/v2/chat/update_user_info                                          

community:
--------------------------------------------------------------------------------
HTTP Method Path                                     Summary                       
--------------------------------------------------------------------------------
PUT        /api/v1/community/comments/{comment_id}                                
DELETE     /api/v1/community/comments/{comment_id}                                
POST       /api/v1/community/comments/{comment_id}/like/                               
POST       /api/v1/community/comments/{comment_id}/report/                               
POST       /api/v1/community/daily-workouts/                                      
GET        /api/v1/community/daily-workouts/                                      
GET        /api/v1/community/daily-workouts/search/                               
PUT        /api/v1/community/daily-workouts/{workout_id}                               
DELETE     /api/v1/community/daily-workouts/{workout_id}                               
GET        /api/v1/community/daily-workouts/{workout_id}                               
POST       /api/v1/community/images/                                              
PUT        /api/v1/community/images/{image_id}                                    
DELETE     /api/v1/community/images/{image_id}                                    
GET        /api/v1/community/notifications/                                       
GET        /api/v1/community/notifications/filter/                                
PATCH      /api/v1/community/notifications/read-all/                               
DELETE     /api/v1/community/notifications/{notification_id}                               
PATCH      /api/v1/community/notifications/{notification_id}/read/                               
POST       /api/v1/community/posts/                                               
GET        /api/v1/community/posts/                                               
GET        /api/v1/community/posts/search/                                        
PUT        /api/v1/community/posts/{post_id}                                      
DELETE     /api/v1/community/posts/{post_id}                                      
GET        /api/v1/community/posts/{post_id}                                      
POST       /api/v1/community/posts/{post_id}/comments/                               
POST       /api/v1/community/posts/{post_id}/like/                                
POST       /api/v1/community/posts/{post_id}/report/                               
POST       /api/v1/community/users/{user_id}/follow/                               
DELETE     /api/v1/community/users/{user_id}/follow/                               
GET        /api/v1/community/users/{user_id}/followers/                               
GET        /api/v1/community/users/{user_id}/following/                               
POST       /api/v1/community/workouts/{workout_id}/share                               

exercise:
--------------------------------------------------------------------------------
HTTP Method Path                                     Summary                       
--------------------------------------------------------------------------------
GET        /api/v1/exercise/avatar/image/exercises/{filename}                               
GET        /api/v1/exercise/body-parts/                                           
POST       /api/v1/exercise/body-parts/                                           
GET        /api/v1/exercise/equipment/                                            
POST       /api/v1/exercise/equipment/                                            
GET        /api/v1/exercise/exercises/                                            
POST       /api/v1/exercise/exercises/                                            
GET        /api/v1/exercise/exercises/search                                      
GET        /api/v1/exercise/exercises/{exercise_id}                               
PUT        /api/v1/exercise/exercises/{exercise_id}                               
DELETE     /api/v1/exercise/exercises/{exercise_id}                               
GET        /api/v1/exercise/exercises/{exercise_id}/detail                               
POST       /api/v1/exercise/exercises/{exercise_id}/detail                               
PUT        /api/v1/exercise/exercises/{exercise_id}/detail                               
POST       /api/v1/exercise/exercises/{exercise_id}/hit                               
GET        /api/v1/exercise/gif/{filename}                                        
GET        /api/v1/exercise/image/direct/{filename}                               
GET        /api/v1/exercise/image/exercises/{filename}                               
GET        /api/v1/exercise/image/{filename}                                      
GET        /api/v1/exercise/image_file/{filename}                                 
GET        /api/v1/exercise/muscles/                                              
POST       /api/v1/exercise/muscles/                                              
GET        /api/v1/exercise/video/{filename}                                      
GET        /api/v1/exercise/video_wx/{filename}                                   

food:
--------------------------------------------------------------------------------
HTTP Method Path                                     Summary                       
--------------------------------------------------------------------------------
GET        /api/v1/food/                                                          
POST       /api/v1/food/                                                          
GET        /api/v1/food/categories                                                
GET        /api/v1/food/code/{code}                                               
POST       /api/v1/food/nutrients                                                 
GET        /api/v1/food/search                                                    
GET        /api/v1/food/types                                                     
GET        /api/v1/food/{food_id}                                                 
PUT        /api/v1/food/{food_id}                                                 
DELETE     /api/v1/food/{food_id}                                                 

food-recognition:
--------------------------------------------------------------------------------
HTTP Method Path                                     Summary                       
--------------------------------------------------------------------------------
GET        /api/v1/food-recognition/                                              
POST       /api/v1/food-recognition/                                              
POST       /api/v1/food-recognition/analyze                                       
POST       /api/v1/food-recognition/analyze-base64                                
POST       /api/v1/food-recognition/analyze-smart                                 
GET        /api/v1/food-recognition/date-range                                    
GET        /api/v1/food-recognition/image/{secure_path}/{date}/{filename}                               
GET        /api/v1/food-recognition/pending                                       
GET        /api/v1/food-recognition/stats                                         
GET        /api/v1/food-recognition/status/{status}                               
GET        /api/v1/food-recognition/{recognition_id}                               
PUT        /api/v1/food-recognition/{recognition_id}                               
DELETE     /api/v1/food-recognition/{recognition_id}                               
POST       /api/v1/food-recognition/{recognition_id}/confirm                               
POST       /api/v1/food-recognition/{recognition_id}/debug-confirm                               
POST       /api/v1/food-recognition/{recognition_id}/reject                               

gamification:
--------------------------------------------------------------------------------
HTTP Method Path                                     Summary                       
--------------------------------------------------------------------------------
GET        /api/v1/gamification/achievement/all                                   
GET        /api/v1/gamification/achievement/by-category/{category}                               
POST       /api/v1/gamification/achievement/claim-milestone/{milestone_id}                               
POST       /api/v1/gamification/achievement/claim/{achievement_id}                               
GET        /api/v1/gamification/achievement/completed                               
GET        /api/v1/gamification/achievement/completed-milestones                               
GET        /api/v1/gamification/achievement/milestone-progress                               
GET        /api/v1/gamification/achievement/milestones                               
GET        /api/v1/gamification/achievement/progress                               
POST       /api/v1/gamification/achievement/trigger-event/{event_type}                               
POST       /api/v1/gamification/achievement/update-milestone/{milestone_id}                               
GET        /api/v1/gamification/card/all                                          
GET        /api/v1/gamification/card/by-type/{card_type}                               
GET        /api/v1/gamification/card/detail/{card_id}                               
GET        /api/v1/gamification/card/effects                                      
GET        /api/v1/gamification/card/equipped                                     
GET        /api/v1/gamification/card/synthesis-recipe/{recipe_id}                               
GET        /api/v1/gamification/card/synthesis-recipes                               
POST       /api/v1/gamification/card/synthesize/{recipe_id}                               
POST       /api/v1/gamification/card/toggle-equip/{card_id}                               
GET        /api/v1/gamification/card/user-cards                                   
GET        /api/v1/gamification/currency/balance                                  
GET        /api/v1/gamification/currency/check-daily-limit                               
GET        /api/v1/gamification/currency/daily-transactions                               
POST       /api/v1/gamification/currency/purchase                                 
GET        /api/v1/gamification/currency/purchases                                
GET        /api/v1/gamification/currency/shop                                     
GET        /api/v1/gamification/currency/shop/{item_id}                               
GET        /api/v1/gamification/currency/transactions                               
GET        /api/v1/gamification/level/                                            
GET        /api/v1/gamification/level/active-title                                
POST       /api/v1/gamification/level/add-diet-experience                               
POST       /api/v1/gamification/level/add-exercise-experience                               
GET        /api/v1/gamification/level/attributes                                  
GET        /api/v1/gamification/level/detail                                      
POST       /api/v1/gamification/level/set-active-title/{title_id}                               
GET        /api/v1/gamification/level/titles                                      
GET        /api/v1/gamification/task/active                                       
GET        /api/v1/gamification/task/all                                          
GET        /api/v1/gamification/task/by-category/{category}                               
GET        /api/v1/gamification/task/by-type/{task_type}                               
GET        /api/v1/gamification/task/check-daily-checkin                               
GET        /api/v1/gamification/task/checkin-history                               
POST       /api/v1/gamification/task/claim/{task_id}                               
GET        /api/v1/gamification/task/completed                                    
POST       /api/v1/gamification/task/daily-checkin                                
POST       /api/v1/gamification/task/generate-daily                               
POST       /api/v1/gamification/task/generate-weekly                               
POST       /api/v1/gamification/task/trigger-event/{event_type}                               
POST       /api/v1/gamification/task/update-progress                               

gamification-achievement:
--------------------------------------------------------------------------------
HTTP Method Path                                     Summary                       
--------------------------------------------------------------------------------
GET        /api/v1/gamification/achievement/all                                   
GET        /api/v1/gamification/achievement/by-category/{category}                               
POST       /api/v1/gamification/achievement/claim-milestone/{milestone_id}                               
POST       /api/v1/gamification/achievement/claim/{achievement_id}                               
GET        /api/v1/gamification/achievement/completed                               
GET        /api/v1/gamification/achievement/completed-milestones                               
GET        /api/v1/gamification/achievement/milestone-progress                               
GET        /api/v1/gamification/achievement/milestones                               
GET        /api/v1/gamification/achievement/progress                               
POST       /api/v1/gamification/achievement/trigger-event/{event_type}                               
POST       /api/v1/gamification/achievement/update-milestone/{milestone_id}                               

gamification-card:
--------------------------------------------------------------------------------
HTTP Method Path                                     Summary                       
--------------------------------------------------------------------------------
GET        /api/v1/gamification/card/all                                          
GET        /api/v1/gamification/card/by-type/{card_type}                               
GET        /api/v1/gamification/card/detail/{card_id}                               
GET        /api/v1/gamification/card/effects                                      
GET        /api/v1/gamification/card/equipped                                     
GET        /api/v1/gamification/card/synthesis-recipe/{recipe_id}                               
GET        /api/v1/gamification/card/synthesis-recipes                               
POST       /api/v1/gamification/card/synthesize/{recipe_id}                               
POST       /api/v1/gamification/card/toggle-equip/{card_id}                               
GET        /api/v1/gamification/card/user-cards                                   

gamification-currency:
--------------------------------------------------------------------------------
HTTP Method Path                                     Summary                       
--------------------------------------------------------------------------------
GET        /api/v1/gamification/currency/balance                                  
GET        /api/v1/gamification/currency/check-daily-limit                               
GET        /api/v1/gamification/currency/daily-transactions                               
POST       /api/v1/gamification/currency/purchase                                 
GET        /api/v1/gamification/currency/purchases                                
GET        /api/v1/gamification/currency/shop                                     
GET        /api/v1/gamification/currency/shop/{item_id}                               
GET        /api/v1/gamification/currency/transactions                               

gamification-level:
--------------------------------------------------------------------------------
HTTP Method Path                                     Summary                       
--------------------------------------------------------------------------------
GET        /api/v1/gamification/level/                                            
GET        /api/v1/gamification/level/active-title                                
POST       /api/v1/gamification/level/add-diet-experience                               
POST       /api/v1/gamification/level/add-exercise-experience                               
GET        /api/v1/gamification/level/attributes                                  
GET        /api/v1/gamification/level/detail                                      
POST       /api/v1/gamification/level/set-active-title/{title_id}                               
GET        /api/v1/gamification/level/titles                                      

gamification-task:
--------------------------------------------------------------------------------
HTTP Method Path                                     Summary                       
--------------------------------------------------------------------------------
GET        /api/v1/gamification/task/active                                       
GET        /api/v1/gamification/task/all                                          
GET        /api/v1/gamification/task/by-category/{category}                               
GET        /api/v1/gamification/task/by-type/{task_type}                               
GET        /api/v1/gamification/task/check-daily-checkin                               
GET        /api/v1/gamification/task/checkin-history                               
POST       /api/v1/gamification/task/claim/{task_id}                               
GET        /api/v1/gamification/task/completed                                    
POST       /api/v1/gamification/task/daily-checkin                                
POST       /api/v1/gamification/task/generate-daily                               
POST       /api/v1/gamification/task/generate-weekly                               
POST       /api/v1/gamification/task/trigger-event/{event_type}                               
POST       /api/v1/gamification/task/update-progress                               

health:
--------------------------------------------------------------------------------
HTTP Method Path                                     Summary                       
--------------------------------------------------------------------------------
GET        /api/v1/ping                                                           

llm-logs:
--------------------------------------------------------------------------------
HTTP Method Path                                     Summary                       
--------------------------------------------------------------------------------
GET        /api/v1/llm-logs/by-date/{date}                                        
GET        /api/v1/llm-logs/by-user/{user_id}                                     
POST       /api/v1/llm-logs/clean                                                 
GET        /api/v1/llm-logs/details                                               
GET        /api/v1/llm-logs/image                                                 
GET        /api/v1/llm-logs/qa-pairs/by-user/{user_id}                               

meal:
--------------------------------------------------------------------------------
HTTP Method Path                                     Summary                       
--------------------------------------------------------------------------------
GET        /api/v1/meal/                                                          
POST       /api/v1/meal/                                                          
GET        /api/v1/meal/daily                                                     
DELETE     /api/v1/meal/food-items/{food_item_id}                                 
GET        /api/v1/meal/food-items/{food_item_id}                                 
PUT        /api/v1/meal/food-items/{food_item_id}                                 
GET        /api/v1/meal/{meal_id}                                                 
PUT        /api/v1/meal/{meal_id}                                                 
DELETE     /api/v1/meal/{meal_id}                                                 
POST       /api/v1/meal/{meal_id}/food-items                                      

nutrient:
--------------------------------------------------------------------------------
HTTP Method Path                                     Summary                       
--------------------------------------------------------------------------------
POST       /api/v1/nutrient/daily-target                                          
GET        /api/v1/nutrient/recommendation                                        

qrcode:
--------------------------------------------------------------------------------
HTTP Method Path                                     Summary                       
--------------------------------------------------------------------------------
DELETE     /api/v1/qrcode/cleanup                                                 
POST       /api/v1/qrcode/generate                                                
GET        /api/v1/qrcode/image/{secure_path}/{filename}                               

share:
--------------------------------------------------------------------------------
HTTP Method Path                                     Summary                       
--------------------------------------------------------------------------------
GET        /api/v1/share/scan/{share_code}                                        
POST       /api/v1/share/track                                                    

team:
--------------------------------------------------------------------------------
HTTP Method Path                                     Summary                       
--------------------------------------------------------------------------------
PUT        /api/v1/team/clients/training-plans/{plan_id}                               
GET        /api/v1/team/clients/{client_relation_id}/stats                               
POST       /api/v1/team/clients/{client_relation_id}/training-plans/                               
GET        /api/v1/team/clients/{client_relation_id}/training-plans/                               
PUT        /api/v1/team/sessions/{session_id}/complete                               
POST       /api/v1/team/sessions/{session_id}/exercises/{exercise_id}/records                               
POST       /api/v1/team/sessions/{session_id}/start                               
POST       /api/v1/team/teams/                                                    
GET        /api/v1/team/teams/                                                    
DELETE     /api/v1/team/teams/invitations/{invitation_id}                               
PUT        /api/v1/team/teams/invitations/{invitation_id}/accept                               
PUT        /api/v1/team/teams/invitations/{invitation_id}/reject                               
GET        /api/v1/team/teams/{team_id}                                           
PUT        /api/v1/team/teams/{team_id}                                           
DELETE     /api/v1/team/teams/{team_id}                                           
GET        /api/v1/team/teams/{team_id}/clients                                   
POST       /api/v1/team/teams/{team_id}/clients/                                  
GET        /api/v1/team/teams/{team_id}/clients/{client_relation_id}                               
PUT        /api/v1/team/teams/{team_id}/clients/{client_relation_id}/deactivate                               
PUT        /api/v1/team/teams/{team_id}/clients/{client_relation_id}/reactivate                               
POST       /api/v1/team/teams/{team_id}/clients/{client_relation_id}/transfer                               
GET        /api/v1/team/teams/{team_id}/invitations                               
POST       /api/v1/team/teams/{team_id}/invitations/                               
GET        /api/v1/team/teams/{team_id}/members                                   
POST       /api/v1/team/teams/{team_id}/members/                                  
PUT        /api/v1/team/teams/{team_id}/members/{user_id}                               
DELETE     /api/v1/team/teams/{team_id}/members/{user_id}                               
GET        /api/v1/team/teams/{team_id}/stats                                     
POST       /api/v1/team/teams/{team_id}/templates/                                
GET        /api/v1/team/teams/{team_id}/templates/                                
GET        /api/v1/team/training-plans/{plan_id}/sessions                               
GET        /api/v1/team/user/invitations                                          

training-plan:
--------------------------------------------------------------------------------
HTTP Method Path                                     Summary                       
--------------------------------------------------------------------------------
GET        /api/v1/training-plan/                                                 
POST       /api/v1/training-plan/daily                                            
POST       /api/v1/training-plan/generate                                         
POST       /api/v1/training-plan/templates/{template_id}/create                               
GET        /api/v1/training-plan/workouts/by-date                                 
POST       /api/v1/training-plan/workouts/{workout_id}/status                               
GET        /api/v1/training-plan/{plan_id}                                        
DELETE     /api/v1/training-plan/{plan_id}                                        
PUT        /api/v1/training-plan/{plan_id}                                        
POST       /api/v1/training-plan/{plan_id}/status                                 
POST       /api/v1/training-plan/{plan_id}/template                               
GET        /api/v1/training-plan/{plan_id}/workouts                               
GET        /api/v1/training-plan/{plan_id}/workouts/exercises                               

users:
--------------------------------------------------------------------------------
HTTP Method Path                                     Summary                       
--------------------------------------------------------------------------------
POST       /api/v1/user/avatar                                                    
GET        /api/v1/user/avatar/image/{secure_path}/{filename}                               
GET        /api/v1/user/check                                                     
GET        /api/v1/user/exists                                                    
GET        /api/v1/user/food-image/{secure_path}/{date}/{filename}                               
GET        /api/v1/user/profile                                                   
POST       /api/v1/user/profile                                                   
GET        /api/v1/user/settings                                                  
POST       /api/v1/user/settings                                                  
POST       /api/v1/user/settings/ai-character                                     
GET        /api/v1/user/settings/ai-character                                     
POST       /api/v1/user/upload-food-image                                         

video-processing:
--------------------------------------------------------------------------------
HTTP Method Path                                     Summary                       
--------------------------------------------------------------------------------
POST       /api/v1/video/cleanup                                                  
GET        /api/v1/video/download/{filename}                                      
POST       /api/v1/video/process_video                                            

workout:
--------------------------------------------------------------------------------
HTTP Method Path                                     Summary                       
--------------------------------------------------------------------------------
GET        /api/v1/workout/by-date/{date}                                         
GET        /api/v1/workout/{workout_id}                                           
POST       /api/v1/workout/{workout_id}/link-session/{session_id}                               
GET        /api/v1/workout/{workout_id}/statistics                                
PUT        /api/v1/workout/{workout_id}/status                                    
PUT        /api/v1/workout/{workout_id}/update-full                               
GET        /api/v1/workouts/by-date/{date}                                        
GET        /api/v1/workouts/{workout_id}                                          
POST       /api/v1/workouts/{workout_id}/link-session/{session_id}                               
GET        /api/v1/workouts/{workout_id}/statistics                               
PUT        /api/v1/workouts/{workout_id}/status                                   
PUT        /api/v1/workouts/{workout_id}/update-full                               

未分类:
--------------------------------------------------------------------------------
HTTP Method Path                                     Summary                       
--------------------------------------------------------------------------------
GET        /                                                                      
GET        /api/v1/docs                                                           
OPTIONS    /api/v1/food-recognition                                               
POST       /api/v1/food-recognition                                               
GET        /api/v1/food-recognition                                               
PATCH, DELETE, GET, POST, PUT, OPTIONS, HEAD /api/v1/food_recognition{path:path}                                    
GET        /api/v1/openapi.json                                                   
GET        /health                                                                
GET        /meal-images/{filename}                                                

总计: 286 个端点

