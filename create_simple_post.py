import asyncio
from sqlalchemy.orm import Session
from datetime import datetime

from app import models, schemas
from app.db.session import SessionLocal
from app.services.community_service import CommunityService
from app.schemas.community import PostCreate

async def create_simple_post():
    """为ID为1的用户创建不关联训练的简单帖子"""
    print("正在创建简单帖子...")
    db = SessionLocal()
    
    try:
        # 检查用户是否存在
        user = db.query(models.User).filter(models.User.id == 1).first()
        if not user:
            print("错误: ID为1的用户不存在")
            return
            
        # 创建社区服务实例
        community_service = CommunityService(db)
        
        # 创建帖子
        post_data = PostCreate(
            title="测试力量训练记录",
            content="今天完成了深蹲和卧推训练，感觉很不错！",
            visibility="Everyone",
            tags=["力量训练", "腿部", "胸部"]
        )
        
        post = await community_service.create_post_with_workout(1, post_data)
        print(f"成功创建示例帖子! Post ID: {post.id}")
        
    except Exception as e:
        print(f"创建帖子时出错: {str(e)}")
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(create_simple_post()) 