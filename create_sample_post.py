import asyncio
from sqlalchemy.orm import Session
from datetime import datetime

from app import models, schemas
from app.db.session import SessionLocal
from app.services.workout_summary_service import WorkoutSummaryService
from app.services.community_service import CommunityService
from app.schemas.community import WorkoutShareCreate, WorkoutExerciseItem, WorkoutExerciseSet, WorkoutDataSchema

async def create_sample_workout_share():
    """为ID为1的用户创建示例训练分享帖子"""
    print("正在创建示例训练分享帖子...")
    db = SessionLocal()
    
    try:
        # 检查用户是否存在
        user = db.query(models.User).filter(models.User.id == 1).first()
        if not user:
            print("错误: ID为1的用户不存在")
            return
        
        # 创建训练数据
        workout_data = WorkoutDataSchema(
            exercises=[
                WorkoutExerciseItem(
                    name="深蹲",
                    imageUrl="https://sciencefit.site/data/exercises/images/squat.png",
                    category="腿部",
                    sets=[
                        WorkoutExerciseSet(
                            type="normal",
                            weight=60.0,
                            reps=12,
                            completed=True
                        ),
                        WorkoutExerciseSet(
                            type="normal",
                            weight=70.0,
                            reps=10,
                            completed=True
                        ),
                        WorkoutExerciseSet(
                            type="normal",
                            weight=80.0,
                            reps=8,
                            completed=True
                        )
                    ],
                    hasCompletedSets=True,
                    totalVolume=1880,
                    completedSets=3
                ),
                WorkoutExerciseItem(
                    name="卧推",
                    imageUrl="https://sciencefit.site/data/exercises/images/bench_press.png",
                    category="胸部",
                    sets=[
                        WorkoutExerciseSet(
                            type="normal",
                            weight=50.0,
                            reps=12,
                            completed=True
                        ),
                        WorkoutExerciseSet(
                            type="normal",
                            weight=60.0,
                            reps=10,
                            completed=True
                        ),
                        WorkoutExerciseSet(
                            type="normal",
                            weight=70.0,
                            reps=8,
                            completed=True
                        )
                    ],
                    hasCompletedSets=True,
                    totalVolume=1560,
                    completedSets=3
                )
            ],
            duration_seconds=3600,  # 1小时
            total_sets=6,
            total_volume=3440
        )
        
        # 创建分享数据
        share_data = WorkoutShareCreate(
            title="我的力量训练记录",
            content="今天完成了深蹲和卧推训练，感觉很不错！",
            visibility="Everyone",
            tags=["力量训练", "腿部", "胸部"],
            workout_data=workout_data
        )
        
        # 创建服务实例
        workout_summary_service = WorkoutSummaryService()
        community_service = CommunityService(db)
        
        # 创建或更新Workout和DailyWorkout
        result = await workout_summary_service.create_or_update_workout_and_summary(
            db=db,
            user_id=1,
            workout_id=None,
            data={
                "title": share_data.title,
                "content": share_data.content,
                "workout_data": share_data.workout_data.dict(),
                "visibility": share_data.visibility,
                "tags": share_data.tags
            }
        )
        
        workout = result.get("workout")
        daily_workout = result.get("daily_workout")
        
        if not daily_workout:
            print("错误: 无法创建训练记录")
            return
        
        # 创建Post并关联DailyWorkout
        post_data = schemas.community.PostCreate(
            title=share_data.title,
            content=share_data.content,
            daily_workout_id=daily_workout.id,
            visibility=share_data.visibility,
            tags=share_data.tags
        )
        
        post = await community_service.create_post_with_workout(1, post_data)
        print(f"成功创建示例训练分享帖子! Post ID: {post.id}")
        print(f"关联的DailyWorkout ID: {daily_workout.id}")
        print(f"关联的Workout ID: {workout.id}")
        
    except Exception as e:
        print(f"创建示例训练分享帖子时出错: {str(e)}")
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(create_sample_workout_share()) 