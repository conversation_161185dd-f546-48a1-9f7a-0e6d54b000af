# LLM工具链和模型
langchain<0.2.0
langchain-core<0.2.0
langchain-openai<0.2.0
langchain-community<0.2.0

# 现有依赖
fastapi>=0.68.0
uvicorn>=0.15.0
python-dotenv>=0.19.0
pydantic>=1.8.2,<2.0.0  # 明确指定 pydantic v1
pydantic-settings<2.0.0  # 与 pydantic v1 兼容的版本
sqlalchemy>=1.4.0
psycopg2-binary>=2.9.0
starlette>=0.14.2
python-jose>=3.3.0
passlib>=1.7.4
bcrypt>=3.2.0

# 向量存储
faiss-cpu>=1.7.4
numpy>=1.20.0
scipy>=1.7.0

# 实用工具
python-multipart>=0.0.5
requests>=2.26.0
tenacity>=8.0.1
typing-extensions>=4.0.0
loguru>=0.6.0