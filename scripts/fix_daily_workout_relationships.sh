#!/bin/bash

# 激活虚拟环境
source venv/bin/activate

# 设置颜色
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}开始修复 DailyWorkout 关系...${NC}"

# 应用数据库迁移
echo -e "${YELLOW}应用数据库迁移...${NC}"
alembic upgrade head

if [ $? -eq 0 ]; then
    echo -e "${GREEN}数据库迁移成功!${NC}"
else
    echo -e "${RED}数据库迁移失败!${NC}"
    exit 1
fi

# 运行测试
echo -e "${YELLOW}运行测试...${NC}"
python tests/test_daily_workout_relationships.py

if [ $? -eq 0 ]; then
    echo -e "${GREEN}测试通过!${NC}"
else
    echo -e "${RED}测试失败!${NC}"
    exit 1
fi

echo -e "${GREEN}DailyWorkout 关系修复完成!${NC}"
