#!/usr/bin/env python3
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Float, Boolean, Text, JSON
from sqlalchemy.sql import func
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 获取数据库URL
DATABASE_URL = os.getenv("POSTGRES_DSN")
if not DATABASE_URL:
    raise ValueError("未找到POSTGRES_DSN环境变量")

# 创建引擎
engine = create_engine(DATABASE_URL)

# 创建基类
Base = declarative_base()

# 定义用户训练记录模型
class UserTrainingRecord(Base):
    __tablename__ = "user_training_record"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("user.id"), nullable=False)
    
    # 基础信息
    training_age = Column(Integer, default=0)  # 训练年龄（月）
    preferred_training_days = Column(Integer, default=3)  # 每周首选训练天数
    
    # 体能和力量指标
    squat_max = Column(Float, nullable=True)  # 深蹲最大重量
    bench_press_max = Column(Float, nullable=True)  # 卧推最大重量
    deadlift_max = Column(Float, nullable=True)  # 硬拉最大重量
    
    # 训练偏好
    preferred_exercise_types = Column(JSON, nullable=True)  # 首选运动类型
    avoided_exercise_types = Column(JSON, nullable=True)  # 避免的运动类型
    
    # 目标数据
    fitness_goals = Column(JSON, nullable=True)  # 健身目标
    target_muscle_groups = Column(JSON, nullable=True)  # 目标肌肉群
    
    # 时间记录
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 其他健康指标
    injuries = Column(JSON, nullable=True)  # 伤病记录
    medical_conditions = Column(JSON, nullable=True)  # 医疗状况

def create_tables():
    """创建所有定义的表"""
    Base.metadata.create_all(bind=engine)
    print("用户训练记录表已创建！")

if __name__ == "__main__":
    create_tables() 