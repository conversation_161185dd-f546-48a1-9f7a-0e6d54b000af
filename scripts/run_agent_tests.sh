#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=======================================${NC}"
echo -e "${YELLOW}   运行聊天助手功能流程测试脚本   ${NC}"
echo -e "${YELLOW}=======================================${NC}"
echo ""

# 创建测试结果目录
RESULTS_DIR="test_results"
mkdir -p $RESULTS_DIR

# 获取当前时间戳
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="$RESULTS_DIR/agent_tests_$TIMESTAMP.log"

echo -e "${YELLOW}测试结果将保存到: ${NC}$LOG_FILE"
echo ""

# 运行测试并记录日志
run_test() {
    TEST_FILE=$1
    TEST_NAME=$2

    echo -e "${YELLOW}运行测试: ${NC}$TEST_NAME"
    echo -e "${YELLOW}测试文件: ${NC}$TEST_FILE"
    echo ""

    # 运行测试并捕获输出
    python -m pytest $TEST_FILE -v --asyncio-mode=auto | tee -a $LOG_FILE

    # 检查测试结果
    if [ ${PIPESTATUS[0]} -eq 0 ]; then
        echo -e "${GREEN}测试通过: ${NC}$TEST_NAME"
    else
        echo -e "${RED}测试失败: ${NC}$TEST_NAME"
    fi
    echo ""
}

# 运行意图识别和执行测试
echo -e "${YELLOW}=======================================${NC}"
echo -e "${YELLOW}   1. 测试意图识别和执行   ${NC}"
echo -e "${YELLOW}=======================================${NC}"
run_test "tests/test_agent_flow.py" "意图识别和执行测试"

# 运行会话流程和中断处理测试
echo -e "${YELLOW}=======================================${NC}"
echo -e "${YELLOW}   2. 测试会话流程和中断处理   ${NC}"
echo -e "${YELLOW}=======================================${NC}"
run_test "tests/test_conversation_flow.py" "会话流程和中断处理测试"

# 运行意图识别单元测试
echo -e "${YELLOW}=======================================${NC}"
echo -e "${YELLOW}   3. 测试意图识别单元   ${NC}"
echo -e "${YELLOW}=======================================${NC}"
run_test "tests/test_intent_recognition.py" "意图识别单元测试"

# 生成测试报告摘要
echo -e "${YELLOW}=======================================${NC}"
echo -e "${YELLOW}   测试报告摘要   ${NC}"
echo -e "${YELLOW}=======================================${NC}"

# 统计测试结果
TOTAL_TESTS=$(grep -c "PASSED\|FAILED\|SKIPPED\|XFAILED\|XPASSED" $LOG_FILE)
PASSED_TESTS=$(grep -c "PASSED" $LOG_FILE)
FAILED_TESTS=$(grep -c "FAILED" $LOG_FILE)
SKIPPED_TESTS=$(grep -c "SKIPPED" $LOG_FILE)

echo -e "总测试数: $TOTAL_TESTS"
echo -e "${GREEN}通过: $PASSED_TESTS${NC}"
if [ $FAILED_TESTS -gt 0 ]; then
    echo -e "${RED}失败: $FAILED_TESTS${NC}"
else
    echo -e "失败: $FAILED_TESTS"
fi
echo -e "跳过: $SKIPPED_TESTS"
echo ""

# 显示失败的测试
if [ $FAILED_TESTS -gt 0 ]; then
    echo -e "${RED}失败的测试:${NC}"
    grep -A 3 "FAILED" $LOG_FILE
    echo ""
fi

echo -e "${YELLOW}测试完成!${NC}"
echo -e "详细日志已保存到: $LOG_FILE"
