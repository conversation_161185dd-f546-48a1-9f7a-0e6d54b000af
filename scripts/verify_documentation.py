#!/usr/bin/env python3
"""
文档验证脚本

验证智能健身AI助手系统文档的完整性和准确性
"""

import os
import sys
import re
from pathlib import Path
from typing import List, Dict, Any

def check_file_exists(file_path: str) -> bool:
    """检查文件是否存在"""
    return Path(file_path).exists()

def check_markdown_links(file_path: str) -> List[str]:
    """检查Markdown文件中的链接"""
    broken_links = []

    if not check_file_exists(file_path):
        return [f"文件不存在: {file_path}"]

    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # 查找相对链接（排除代码块中的内容）
    # 首先移除代码块
    code_block_pattern = r'```.*?```'
    content_without_code = re.sub(code_block_pattern, '', content, flags=re.DOTALL)

    # 移除行内代码
    inline_code_pattern = r'`[^`]*`'
    content_without_code = re.sub(inline_code_pattern, '', content_without_code)

    # 查找链接
    link_pattern = r'\[([^\]]+)\]\(([^)]+)\)'
    matches = re.findall(link_pattern, content_without_code)

    base_dir = Path(file_path).parent

    for link_text, link_url in matches:
        if link_url.startswith('http'):
            continue  # 跳过外部链接

        if link_url.startswith('#'):
            continue  # 跳过锚点链接

        # 检查相对路径
        target_path = base_dir / link_url
        if not target_path.exists():
            broken_links.append(f"断开的链接: [{link_text}]({link_url}) 在 {file_path}")

    return broken_links

def verify_main_documentation():
    """验证主文档"""
    print("🔍 验证主文档...")

    main_doc = "docs/agent/智能健身AI助手: 重构实现文档.md"

    if not check_file_exists(main_doc):
        print(f"❌ 主文档不存在: {main_doc}")
        return False

    print(f"✅ 主文档存在: {main_doc}")

    # 检查链接
    broken_links = check_markdown_links(main_doc)
    if broken_links:
        print("❌ 发现断开的链接:")
        for link in broken_links:
            print(f"   {link}")
        return False

    print("✅ 主文档链接检查通过")
    return True

def verify_sub_documentation():
    """验证子模块文档"""
    print("\n🔍 验证子模块文档...")

    expected_docs = [
        "docs/agent/recon/architecture-overview.md",
        "docs/agent/recon/api-endpoints.md",
        "docs/agent/recon/ai-assistant-v2.md",
        "docs/agent/recon/conversation-legacy.md",
        "docs/agent/recon/state-management.md",
        "docs/agent/recon/intent-processing.md",
        "docs/agent/recon/llm-integration.md",
        "docs/agent/recon/data-models.md",
        "docs/agent/recon/deployment-guide.md"
    ]

    missing_docs = []
    existing_docs = []

    for doc in expected_docs:
        if check_file_exists(doc):
            existing_docs.append(doc)
            print(f"✅ {doc}")
        else:
            missing_docs.append(doc)
            print(f"❌ {doc}")

    if missing_docs:
        print(f"\n❌ 缺失 {len(missing_docs)} 个子模块文档")
        return False

    print(f"\n✅ 所有 {len(existing_docs)} 个子模块文档都存在")

    # 检查每个文档的链接
    total_broken_links = []
    for doc in existing_docs:
        broken_links = check_markdown_links(doc)
        total_broken_links.extend(broken_links)

    if total_broken_links:
        print(f"\n❌ 发现 {len(total_broken_links)} 个断开的链接:")
        for link in total_broken_links:
            print(f"   {link}")
        return False

    print("✅ 所有子模块文档链接检查通过")
    return True

def verify_code_references():
    """验证代码引用的准确性"""
    print("\n🔍 验证代码引用...")

    # 检查关键文件是否存在
    key_files = [
        "app/api/v2/endpoints/chat.py",
        "app/services/ai_assistant/conversation/orchestrator.py",
        "app/services/ai_assistant/conversation/states/base.py",
        "app/services/ai_assistant/conversation/states/manager.py",
        "app/services/ai_assistant/conversation/states/idle.py",
        "app/services/ai_assistant/conversation/states/fitness_advice.py",
        "app/services/ai_assistant/llm/proxy.py",
        "app/services/ai_assistant/llm/factory.py",
        "app/services/ai_assistant/llm/providers/qwen_proxy.py",
        "app/services/conversation/orchestrator.py",  # 旧版系统
    ]

    missing_files = []
    existing_files = []

    for file_path in key_files:
        if check_file_exists(file_path):
            existing_files.append(file_path)
            print(f"✅ {file_path}")
        else:
            missing_files.append(file_path)
            print(f"❌ {file_path}")

    if missing_files:
        print(f"\n⚠️  {len(missing_files)} 个引用的文件不存在（可能是正常的）")
        for file_path in missing_files:
            print(f"   {file_path}")

    print(f"\n✅ {len(existing_files)} 个关键文件存在")
    return True

def verify_documentation_structure():
    """验证文档结构"""
    print("\n🔍 验证文档结构...")

    # 检查目录结构
    required_dirs = [
        "docs",
        "docs/agent",
        "docs/agent/recon"
    ]

    for dir_path in required_dirs:
        if not Path(dir_path).is_dir():
            print(f"❌ 目录不存在: {dir_path}")
            return False
        print(f"✅ {dir_path}/")

    print("✅ 文档目录结构正确")
    return True

def generate_summary():
    """生成文档摘要"""
    print("\n📊 文档摘要:")

    # 统计文档数量和大小
    doc_files = list(Path("docs/agent/recon").glob("*.md"))
    doc_files.append(Path("docs/agent/智能健身AI助手: 重构实现文档.md"))

    total_lines = 0
    total_size = 0

    print(f"📄 文档列表:")
    for doc_file in doc_files:
        if doc_file.exists():
            with open(doc_file, 'r', encoding='utf-8') as f:
                lines = len(f.readlines())
            size = doc_file.stat().st_size
            total_lines += lines
            total_size += size
            print(f"   {doc_file.name}: {lines} 行, {size/1024:.1f} KB")

    print(f"\n📈 总计:")
    print(f"   文档数量: {len(doc_files)}")
    print(f"   总行数: {total_lines}")
    print(f"   总大小: {total_size/1024:.1f} KB")

def main():
    """主函数"""
    print("🚀 智能健身AI助手系统文档验证")
    print("=" * 50)

    # 切换到项目根目录
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    os.chdir(project_root)

    print(f"📁 工作目录: {os.getcwd()}")

    # 执行验证
    results = []

    results.append(verify_documentation_structure())
    results.append(verify_main_documentation())
    results.append(verify_sub_documentation())
    results.append(verify_code_references())

    # 生成摘要
    generate_summary()

    # 总结
    print("\n" + "=" * 50)
    passed = sum(results)
    total = len(results)

    if passed == total:
        print(f"🎉 文档验证完成! 所有 {total} 项检查都通过了!")
        print("\n✨ 文档系统完整且准确，可以安全使用。")
        return 0
    else:
        print(f"⚠️  文档验证完成! {passed}/{total} 项检查通过。")
        print("\n🔧 请修复上述问题后重新验证。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
