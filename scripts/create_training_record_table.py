"""
创建UserTrainingRecord表的脚本
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy import create_engine, Column, Integer, String, DateTime, Text, ForeignKey, JSON, ARRAY
from sqlalchemy.sql import func
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.dialects.postgresql import ARRAY, JSON

# 从app中导入配置
from app.core.config import settings

# 创建数据库连接
engine = create_engine(settings.SQLALCHEMY_DATABASE_URI)
Base = declarative_base()

# 定义UserTrainingRecord模型
class UserTrainingRecord(Base):
    """用户训练记录模型"""
    __tablename__ = "user_training_records"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    date = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    body_parts = Column(ARRAY(String), nullable=True)  # 训练的身体部位
    duration_minutes = Column(Integer, nullable=True)  # 训练时长(分钟)
    exercises_data = Column(JSON, nullable=False)  # 训练的动作详情JSON格式
    notes = Column(Text, nullable=True)  # 训练笔记

def main():
    """创建表和索引"""
    # 检查表是否已存在
    if not engine.has_table("user_training_records"):
        print("正在创建user_training_records表...")
        # 创建表
        UserTrainingRecord.__table__.create(engine)
        print("表创建成功!")
    else:
        print("user_training_records表已存在，无需创建")

if __name__ == "__main__":
    main() 