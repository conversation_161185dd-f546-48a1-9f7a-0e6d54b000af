#!/usr/bin/env python3
"""
测试训练模型重构的脚本
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试模型导入是否正常"""
    try:
        print("测试模型导入...")
        
        # 测试新的模型导入
        from app.models.training_template import WorkoutTemplate
        print("✓ WorkoutTemplate 导入成功")
        
        from app.models.workout_exercise import WorkoutExercise
        print("✓ WorkoutExercise 导入成功")
        
        from app.models.set_record import SetRecord
        print("✓ SetRecord 导入成功")
        
        # 测试API端点导入
        from app.api.endpoints.training_template import router as template_router
        print("✓ training_template API 导入成功")
        
        from app.api.endpoints.user_training import router as training_router
        print("✓ user_training API 导入成功")
        
        print("所有导入测试通过！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_model_relationships():
    """测试模型关系是否正确定义"""
    try:
        print("\n测试模型关系...")
        
        from app.models.training_template import WorkoutTemplate
        from app.models.workout_exercise import WorkoutExercise
        from app.models.set_record import SetRecord
        
        # 检查 WorkoutTemplate 的关系
        template = WorkoutTemplate()
        assert hasattr(template, 'template_exercises'), "WorkoutTemplate 缺少 template_exercises 关系"
        print("✓ WorkoutTemplate.template_exercises 关系存在")
        
        # 检查 WorkoutExercise 的关系
        exercise = WorkoutExercise()
        assert hasattr(exercise, 'workout_template'), "WorkoutExercise 缺少 workout_template 关系"
        assert hasattr(exercise, 'set_records'), "WorkoutExercise 缺少 set_records 关系"
        assert hasattr(exercise, 'template_id'), "WorkoutExercise 缺少 template_id 字段"
        print("✓ WorkoutExercise 关系和字段存在")
        
        # 检查 SetRecord 的关系
        set_record = SetRecord()
        assert hasattr(set_record, 'workout_exercise'), "SetRecord 缺少 workout_exercise 关系"
        print("✓ SetRecord.workout_exercise 关系存在")
        
        print("所有模型关系测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 模型关系测试失败: {e}")
        return False

def test_api_endpoints():
    """测试API端点是否正确定义"""
    try:
        print("\n测试API端点...")
        
        from app.api.endpoints.training_template import (
            get_workout_templates,
            get_workout_template,
            create_workout_template,
            delete_workout_template,
            apply_workout_template
        )
        print("✓ training_template 所有端点函数存在")
        
        from app.api.endpoints.user_training import (
            get_user_training_records,
            create_training_record,
            delete_training_record,
            batch_delete_training_records,
            update_set_record
        )
        print("✓ user_training 所有端点函数存在")
        
        print("所有API端点测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ API端点测试失败: {e}")
        return False

def test_migration_script():
    """测试迁移脚本是否可以导入"""
    try:
        print("\n测试迁移脚本...")
        
        from app.db.migrations.migrate_training_models import (
            migrate_table_structure,
            migrate_training_templates_to_workout_exercises,
            migrate_user_training_records_to_set_records,
            run_migration,
            rollback_migration
        )
        print("✓ 迁移脚本所有函数存在")
        
        print("迁移脚本测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 迁移脚本测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("开始训练模型重构测试...\n")
    
    tests = [
        test_imports,
        test_model_relationships,
        test_api_endpoints,
        test_migration_script
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！模型重构准备就绪。")
        print("\n下一步:")
        print("1. 运行数据迁移: python app/db/migrations/migrate_training_models.py")
        print("2. 更新路由配置以使用新的API端点")
        print("3. 测试API功能")
        return True
    else:
        print("❌ 部分测试失败，请检查错误信息并修复。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
