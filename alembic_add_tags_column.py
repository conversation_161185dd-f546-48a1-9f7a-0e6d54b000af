import os
import sys
import subprocess
import time
from datetime import datetime

def main():
    print("创建添加tags列的Alembic迁移...")
    
    # 生成迁移脚本
    migration_name = f"add_tags_column_to_posts_{int(time.time())}"
    result = subprocess.run(
        [sys.executable, "-m", "alembic", "revision", "--autogenerate", "-m", migration_name],
        cwd="/home/<USER>/backend",
        capture_output=True,
        text=True
    )
    
    print("迁移脚本生成结果:")
    print(result.stdout)
    
    if result.returncode != 0:
        print("错误输出:")
        print(result.stderr)
        print("生成迁移脚本失败!")
        return
    
    # 运行迁移
    print("运行迁移...")
    result = subprocess.run(
        [sys.executable, "-m", "alembic", "upgrade", "head"],
        cwd="/home/<USER>/backend",
        capture_output=True,
        text=True
    )
    
    print("迁移执行结果:")
    print(result.stdout)
    
    if result.returncode != 0:
        print("错误输出:")
        print(result.stderr)
        print("执行迁移失败!")
        return
    
    print("迁移完成！")

if __name__ == "__main__":
    main() 