# HTTP服务器 - 重定向到HTTPS
server {
    listen 80;
    server_name sciencefit.site;

    # 将HTTP请求重定向到HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

# HTTPS服务器
server {
    listen 443 ssl;
    server_name sciencefit.site;

    # SSL证书配置
    ssl_certificate /etc/nginx/conf.d/sciencefit.site_bundle.crt;
    ssl_certificate_key /etc/nginx/conf.d/sciencefit.site.key;
    ssl_session_timeout 5m;
    ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:HIGH:!aNULL:!MD5:!RC4:!DHE;
    ssl_prefer_server_ciphers on;

    # 通用CORS和缓存设置
    set $cors_origin '*';
    set $cors_methods 'GET, OPTIONS';
    set $cors_headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
    set $cache_control 'public, no-transform';
    # 方案1: 专用WebSocket路径配置 (推荐)
    location /api/v1/chat/stream {
	    proxy_pass http://127.0.0.1:8000;
	    proxy_http_version 1.1;
	    proxy_set_header Upgrade $http_upgrade;
	    proxy_set_header Connection "upgrade";
	    proxy_set_header Host $host;
	    proxy_set_header X-Real-IP $remote_addr;
	    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
	    proxy_set_header X-Forwarded-Proto $scheme;

	    # WebSocket连接可能需要更长的超时时间
	    proxy_connect_timeout 600s;
	    proxy_send_timeout 600s;
	    proxy_read_timeout 600s;
	    proxy_buffers 8 32k;
            proxy_buffer_size 64k;
    }
    # 新增：测试直接访问路径
    location /test_image/ {
        proxy_pass http://127.0.0.1:8000/static/test_images/;
        expires 7d;
        add_header Cache-Control "public";
    }

    # 安全设置 - 禁止访问隐藏文件
    location ~ /\. {
        deny all;
    }

    # 新增：类似avatar格式的动作库图片直接访问路径
    location ~ ^/api/v1/exercise/direct/([^/]+)$ {
        include /etc/nginx/mime.types;
        alias /data/exercises/images/$1;
        expires 7d;
        add_header Cache-Control "public";

        # 允许跨域访问
        add_header Access-Control-Allow-Origin $cors_origin;
        add_header Access-Control-Allow-Methods $cors_methods;
        add_header Access-Control-Allow-Headers $cors_headers;
    }

    # 统一处理所有静态资源类型
    # 处理/data/exercises目录下的资源
    location ^~ /data/exercises/ {
        root /;
        expires 7d;
        add_header Cache-Control $cache_control;
        try_files $uri @backend;

        # 允许跨域访问
        add_header Access-Control-Allow-Origin $cors_origin;
        add_header Access-Control-Allow-Methods $cors_methods;
        add_header Access-Control-Allow-Headers $cors_headers;
    }

    # 动作库图片资源
    location ~ ^/api/v1/exercise/image/([^/]+)$ {
        alias /data/exercises/images/$1;
        expires 7d;
        add_header Cache-Control $cache_control;

        # 允许跨域访问
        add_header Access-Control-Allow-Origin $cors_origin;
        add_header Access-Control-Allow-Methods $cors_methods;
        add_header Access-Control-Allow-Headers $cors_headers;

        # 添加尝试后端服务
        try_files $uri @backend;
    }

    # 新增：更简洁明确的动作库图片直接访问路径
    location ~ ^/exercises/image/([^/]+)$ {
        alias /data/exercises/images/$1;
        expires 7d;
        add_header Cache-Control $cache_control;
        add_header Access-Control-Allow-Origin $cors_origin;
        add_header Access-Control-Allow-Methods $cors_methods;
        add_header Access-Control-Allow-Headers $cors_headers;
    }

    # 新增：支持新的动作库图片路由格式
    location ~ ^/api/v1/exercise/image/(exercises|direct)/([^/]+)$ {
        alias /data/exercises/images/$2;
        expires 7d;
        add_header Cache-Control $cache_control;

        # 允许跨域访问
        add_header Access-Control-Allow-Origin $cors_origin;
        add_header Access-Control-Allow-Methods $cors_methods;
        add_header Access-Control-Allow-Headers $cors_headers;

        # 添加尝试后端服务
        try_files $uri @backend;
    }

    # 动作库GIF资源
    location ~ ^/api/v1/exercise/gif/([^/]+)$ {
        alias /data/exercises/gifs/$1;
        expires 7d;
        add_header Cache-Control $cache_control;

        # 允许跨域访问
        add_header Access-Control-Allow-Origin $cors_origin;
        add_header Access-Control-Allow-Methods $cors_methods;
        add_header Access-Control-Allow-Headers $cors_headers;
    }

    # 动作库视频资源
    location ~ ^/api/v1/exercise/video/([^/]+)$ {
        alias /data/exercises/videos/$1;
        expires 7d;
        add_header Cache-Control $cache_control;

        # 视频流媒体支持
        mp4;
        mp4_buffer_size 1m;
        mp4_max_buffer_size 5m;

        # 允许跨域访问
        add_header Access-Control-Allow-Origin $cors_origin;
        add_header Access-Control-Allow-Methods $cors_methods;
        add_header Access-Control-Allow-Headers $cors_headers;
    }

    # 新增：GIF文件直接访问路径
    location ~ ^/exercises/gif/([^/]+)$ {
        alias /data/exercises/gifs/$1;
        expires 7d;
        add_header Cache-Control $cache_control;
        add_header Access-Control-Allow-Origin $cors_origin;
        add_header Access-Control-Allow-Methods $cors_methods;
        add_header Access-Control-Allow-Headers $cors_headers;
    }

    # 新增：视频文件直接访问路径
    location ~ ^/exercises/video/([^/]+)$ {
        alias /data/exercises/videos/$1;
        expires 7d;
        add_header Cache-Control $cache_control;
        add_header Access-Control-Allow-Origin $cors_origin;
        add_header Access-Control-Allow-Methods $cors_methods;
        add_header Access-Control-Allow-Headers $cors_headers;
        # 视频流媒体支持
        mp4;
        mp4_buffer_size 1m;
        mp4_max_buffer_size 5m;
    }

    # 处理/data下的图片和媒体文件
    location ~* ^/data/.*\.(jpe?g|png|gif|mp4|webm)$ {
        root /;
        expires 7d;
        add_header Cache-Control $cache_control;
        try_files $uri @backend;

        # 允许跨域访问
        add_header Access-Control-Allow-Origin $cors_origin;
        add_header Access-Control-Allow-Methods $cors_methods;
        add_header Access-Control-Allow-Headers $cors_headers;

        # 防盗链
        valid_referers none blocked sciencefit.site *.sciencefit.site;
        if ($invalid_referer) {
            return 403;
        }

        # 视频文件支持
        location ~* \.(mp4|webm)$ {
            mp4;
            mp4_buffer_size 1m;
            mp4_max_buffer_size 5m;
        }
    }

    # 添加食物图片访问支持
    location /food/ {
        root /data/;
        expires 7d;
        add_header Cache-Control $cache_control;
        add_header Access-Control-Allow-Origin $cors_origin;
        add_header Access-Control-Allow-Methods $cors_methods;
        add_header Access-Control-Allow-Headers $cors_headers;
    }

    # 添加肌肉图片直接访问支持
    location /muscles/ {
        alias /data/muscles/;
        expires 7d;
        add_header Cache-Control $cache_control;
        add_header Access-Control-Allow-Origin $cors_origin;
        add_header Access-Control-Allow-Methods $cors_methods;
        add_header Access-Control-Allow-Headers $cors_headers;
    }

    # 用户头像路由 - 支持新的安全路径格式
    location ~ ^/api/v1/user/avatar/image/user_([a-zA-Z0-9_]+)/([^/]+)$ {
        include /etc/nginx/mime.types;
        alias /data/users/user_$1/avatar/$2;
        expires 7d;
        add_header Cache-Control "public";
        try_files $uri @backend;

        # 超时设置
        proxy_connect_timeout 600s;
        proxy_send_timeout 600s;
        proxy_read_timeout 600s;
        send_timeout 600s;
    }

    # 新增：动作库图片访问路由 - 类似avatar格式
    location ~ ^/api/v1/exercise/avatar/image/exercises/([^/]+)$ {
        include /etc/nginx/mime.types;
        alias /data/exercises/images/$1;
        expires 7d;
        add_header Cache-Control "public";
        try_files $uri @backend;

        # 超时设置
        proxy_connect_timeout 600s;
        proxy_send_timeout 600s;
        proxy_read_timeout 600s;
        send_timeout 600s;
    }

    # 小程序码路由
    location ~ ^/api/v1/qrcode/image/([^/]+)/([^/]+)$ {
        include /etc/nginx/mime.types;
        alias /data/users/$1/qrcode/$2;
        expires 7d;
        add_header Cache-Control "public";
        try_files $uri @backend;

        # 超时设置
        proxy_connect_timeout 600s;
        proxy_send_timeout 600s;
        proxy_read_timeout 600s;
        send_timeout 600s;
    }

    # 小程序码路由 - 支持新的安全路径格式
    location ~ ^/api/v1/qrcode/image/user_([a-zA-Z0-9_]+)/([^/]+)$ {
        include /etc/nginx/mime.types;
        alias /data/users/user_$1/qrcode/$2;
        expires 7d;
        add_header Cache-Control "public";
        try_files $uri @backend;

        # 超时设置
        proxy_connect_timeout 600s;
        proxy_send_timeout 600s;
        proxy_read_timeout 600s;
        send_timeout 600s;
    }

    # 静态资源目录
    location /static/ {
        alias /static/;
        expires 7d;
        try_files $uri @backend;

        # 允许跨域访问
        add_header Access-Control-Allow-Origin $cors_origin;
        add_header Access-Control-Allow-Methods $cors_methods;
        add_header Access-Control-Allow-Headers $cors_headers;
    }

    # 动作库静态资源直接访问 - 新增配置
    location /static/exercises/images/ {
        alias /data/exercises/images/;
        expires 7d;
        add_header Cache-Control $cache_control;
        add_header Access-Control-Allow-Origin $cors_origin;
        add_header Access-Control-Allow-Methods $cors_methods;
        add_header Access-Control-Allow-Headers $cors_headers;
    }

    # 动作库GIF静态资源直接访问 - 新增配置
    location /static/exercises/gifs/ {
        alias /data/exercises/gifs/;
        expires 7d;
        add_header Cache-Control $cache_control;
        add_header Access-Control-Allow-Origin $cors_origin;
        add_header Access-Control-Allow-Methods $cors_methods;
        add_header Access-Control-Allow-Headers $cors_headers;
    }

    # 动作库视频静态资源直接访问 - 新增配置
    location /static/exercises/videos/ {
        alias /data/exercises/videos/;
        expires 7d;
        add_header Cache-Control $cache_control;
        add_header Access-Control-Allow-Origin $cors_origin;
        add_header Access-Control-Allow-Methods $cors_methods;
        add_header Access-Control-Allow-Headers $cors_headers;
        # 视频流媒体支持
        mp4;
        mp4_buffer_size 1m;
        mp4_max_buffer_size 5m;
    }

    # API 反向代理
    location /data/api/ {
        rewrite ^/data/api/(.*)$ /api/$1 break;
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # API 请求
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }

    # 后备处理
    location @backend {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }

    # 默认路由
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }
}
