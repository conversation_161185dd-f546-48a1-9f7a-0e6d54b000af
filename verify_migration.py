#!/usr/bin/env python3
"""
验证数据迁移结果的脚本
"""

import logging
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 数据库连接配置
DATABASE_URL = "postgresql://postgres:!scienceFit0219@127.0.0.1:5432/fitness_db"

def get_db_session():
    """获取数据库会话"""
    engine = create_engine(DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    return SessionLocal()

def verify_table_structure():
    """验证表结构"""
    db = get_db_session()
    try:
        logger.info("验证表结构...")
        
        # 检查 workout_templates 表
        result = db.execute(text("""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = 'workout_templates'
            ORDER BY ordinal_position
        """))
        columns = result.fetchall()
        
        logger.info("workout_templates 表结构:")
        for col_name, col_type in columns:
            logger.info(f"  - {col_name}: {col_type}")
        
        # 检查 workout_exercises 表是否有 template_id 字段
        result = db.execute(text("""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = 'workout_exercises' AND column_name = 'template_id'
        """))
        template_id_col = result.fetchone()
        
        if template_id_col:
            logger.info("✓ workout_exercises.template_id 字段存在")
        else:
            logger.error("❌ workout_exercises.template_id 字段不存在")
        
        # 检查索引
        result = db.execute(text("""
            SELECT indexname 
            FROM pg_indexes 
            WHERE tablename = 'workout_exercises' AND indexname = 'idx_workout_exercises_template_id'
        """))
        index = result.fetchone()
        
        if index:
            logger.info("✓ idx_workout_exercises_template_id 索引存在")
        else:
            logger.error("❌ idx_workout_exercises_template_id 索引不存在")
        
    except Exception as e:
        logger.error(f"验证表结构失败: {str(e)}")
    finally:
        db.close()

def verify_template_migration():
    """验证训练模板迁移"""
    db = get_db_session()
    try:
        logger.info("验证训练模板迁移...")
        
        # 统计模板数量
        result = db.execute(text("SELECT COUNT(*) FROM workout_templates"))
        template_count = result.fetchone()[0]
        logger.info(f"训练模板总数: {template_count}")
        
        # 统计关联的 WorkoutExercise 数量
        result = db.execute(text("""
            SELECT COUNT(*) FROM workout_exercises 
            WHERE template_id IS NOT NULL
        """))
        template_exercise_count = result.fetchone()[0]
        logger.info(f"模板关联的训练动作数量: {template_exercise_count}")
        
        # 检查是否还有 exercises JSON 数据
        result = db.execute(text("""
            SELECT COUNT(*) FROM workout_templates 
            WHERE exercises IS NOT NULL
        """))
        json_count = result.fetchone()[0]
        
        if json_count == 0:
            logger.info("✓ 所有 exercises JSON 数据已清空")
        else:
            logger.warning(f"⚠️ 还有 {json_count} 个模板包含 exercises JSON 数据")
        
        # 显示一些示例数据
        result = db.execute(text("""
            SELECT wt.id, wt.name, COUNT(we.id) as exercise_count
            FROM workout_templates wt
            LEFT JOIN workout_exercises we ON wt.id = we.template_id
            GROUP BY wt.id, wt.name
            LIMIT 5
        """))
        templates = result.fetchall()
        
        logger.info("模板示例:")
        for template_id, name, exercise_count in templates:
            logger.info(f"  - 模板 {template_id}: {name} ({exercise_count} 个动作)")
        
    except Exception as e:
        logger.error(f"验证训练模板迁移失败: {str(e)}")
    finally:
        db.close()

def verify_user_training_migration():
    """验证用户训练记录迁移"""
    db = get_db_session()
    try:
        logger.info("验证用户训练记录迁移...")
        
        # 统计原始记录数量
        result = db.execute(text("SELECT COUNT(*) FROM user_training_plan_records"))
        original_count = result.fetchone()[0]
        logger.info(f"原始用户训练记录数量: {original_count}")
        
        # 统计迁移后的 WorkoutExercise 数量（独立的）
        result = db.execute(text("""
            SELECT COUNT(*) FROM workout_exercises 
            WHERE workout_id IS NULL AND template_id IS NULL AND daily_workout_id IS NULL
        """))
        migrated_exercise_count = result.fetchone()[0]
        logger.info(f"迁移后的独立训练动作数量: {migrated_exercise_count}")
        
        # 统计 SetRecord 数量
        result = db.execute(text("""
            SELECT COUNT(*) FROM set_records sr
            JOIN workout_exercises we ON sr.workout_exercise_id = we.id
            WHERE we.workout_id IS NULL AND we.template_id IS NULL AND we.daily_workout_id IS NULL
        """))
        set_record_count = result.fetchone()[0]
        logger.info(f"迁移后的组记录数量: {set_record_count}")
        
        # 显示一些示例数据
        result = db.execute(text("""
            SELECT we.id, e.name, COUNT(sr.id) as set_count
            FROM workout_exercises we
            JOIN exercises e ON we.exercise_id = e.id
            LEFT JOIN set_records sr ON we.id = sr.workout_exercise_id
            WHERE we.workout_id IS NULL AND we.template_id IS NULL AND we.daily_workout_id IS NULL
            GROUP BY we.id, e.name
            LIMIT 5
        """))
        exercises = result.fetchall()
        
        logger.info("迁移后的训练记录示例:")
        for exercise_id, name, set_count in exercises:
            logger.info(f"  - 动作 {exercise_id}: {name} ({set_count} 组)")
        
    except Exception as e:
        logger.error(f"验证用户训练记录迁移失败: {str(e)}")
    finally:
        db.close()

def verify_data_integrity():
    """验证数据完整性"""
    db = get_db_session()
    try:
        logger.info("验证数据完整性...")
        
        # 检查外键约束
        result = db.execute(text("""
            SELECT COUNT(*) FROM workout_exercises we
            LEFT JOIN workout_templates wt ON we.template_id = wt.id
            WHERE we.template_id IS NOT NULL AND wt.id IS NULL
        """))
        orphaned_count = result.fetchone()[0]
        
        if orphaned_count == 0:
            logger.info("✓ 所有 template_id 外键约束正常")
        else:
            logger.error(f"❌ 发现 {orphaned_count} 个孤立的 template_id 引用")
        
        # 检查 SetRecord 的外键约束
        result = db.execute(text("""
            SELECT COUNT(*) FROM set_records sr
            LEFT JOIN workout_exercises we ON sr.workout_exercise_id = we.id
            WHERE we.id IS NULL
        """))
        orphaned_sets = result.fetchone()[0]
        
        if orphaned_sets == 0:
            logger.info("✓ 所有 SetRecord 外键约束正常")
        else:
            logger.error(f"❌ 发现 {orphaned_sets} 个孤立的 SetRecord")
        
    except Exception as e:
        logger.error(f"验证数据完整性失败: {str(e)}")
    finally:
        db.close()

def main():
    """运行所有验证"""
    logger.info("开始验证数据迁移结果...")
    
    verify_table_structure()
    print()
    verify_template_migration()
    print()
    verify_user_training_migration()
    print()
    verify_data_integrity()
    
    logger.info("🎉 数据迁移验证完成！")

if __name__ == "__main__":
    main()
