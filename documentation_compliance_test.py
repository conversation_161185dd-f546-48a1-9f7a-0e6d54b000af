#!/usr/bin/env python3
"""
文档规范一致性测试系统

根据docs/agent/agent.md和docs/agent/agent_flow.md验证系统实现的一致性
"""

import os
import json
import time
import importlib
import inspect
from typing import Dict, Any, List, Optional, Set
from dataclasses import dataclass
from enum import Enum

class ComplianceResult(Enum):
    COMPLIANT = "COMPLIANT"
    NON_COMPLIANT = "NON_COMPLIANT"
    PARTIALLY_COMPLIANT = "PARTIALLY_COMPLIANT"
    NOT_FOUND = "NOT_FOUND"

@dataclass
class ComplianceCheck:
    """合规性检查项"""
    name: str
    description: str
    requirement: str
    check_type: str  # "module", "class", "method", "flow"
    expected_items: List[str]
    optional_items: Optional[List[str]] = None

@dataclass
class ComplianceReport:
    """合规性报告"""
    check_name: str
    result: ComplianceResult
    found_items: List[str]
    missing_items: List[str]
    details: str

class DocumentationComplianceTester:
    """文档规范一致性测试器"""
    
    def __init__(self):
        self.compliance_reports: List[ComplianceReport] = []
        self._initialize_compliance_checks()
    
    def _initialize_compliance_checks(self):
        """初始化合规性检查项"""
        self.compliance_checks = [
            # 核心架构组件检查
            ComplianceCheck(
                name="conversation_service_architecture",
                description="ConversationService核心架构组件",
                requirement="文档要求ConversationService作为核心编排者",
                check_type="class",
                expected_items=[
                    "app.services.conversation.orchestrator.ConversationService",
                    "app.services.conversation.intent_handler.IntentHandler",
                    "app.services.conversation.user_profile_manager.UserProfileManager",
                    "app.services.conversation.training_param_manager.TrainingParamManager",
                    "app.services.conversation.parameter_extractor.ParameterExtractor"
                ]
            ),
            
            # 意图识别组件检查
            ComplianceCheck(
                name="intent_recognition_components",
                description="意图识别相关组件",
                requirement="文档要求完整的意图识别系统",
                check_type="class",
                expected_items=[
                    "app.services.intent_recognition.enhanced_recognizer.EnhancedIntentRecognizer",
                    "app.services.conversation.intent_recognizer.IntentRecognizer"
                ]
            ),
            
            # 状态管理组件检查
            ComplianceCheck(
                name="state_management_components",
                description="状态管理相关组件",
                requirement="文档要求完整的状态管理机制",
                check_type="class",
                expected_items=[
                    "app.services.conversation.conversation_state_manager.ConversationStateManager",
                    "app.services.conversation.interruption_handler.InterruptionHandler",
                    "app.services.conversation.pending_request_manager.PendingRequestManager"
                ]
            ),
            
            # 核心方法检查
            ComplianceCheck(
                name="conversation_service_methods",
                description="ConversationService核心方法",
                requirement="文档要求的核心处理方法",
                check_type="method",
                expected_items=[
                    "process_message_stream",
                    "_handle_user_profile_collection",
                    "_handle_training_param_collection",
                    "_handle_intent_execution",
                    "_check_message_relevance",
                    "_analyze_continuation_response"
                ]
            ),
            
            # 用户信息管理方法检查
            ComplianceCheck(
                name="user_profile_manager_methods",
                description="UserProfileManager核心方法",
                requirement="文档要求的用户信息管理方法",
                check_type="method",
                expected_items=[
                    "get_missing_fields",
                    "validate_and_parse",
                    "generate_query_message",
                    "update_user_info"
                ]
            ),
            
            # 训练参数管理方法检查
            ComplianceCheck(
                name="training_param_manager_methods",
                description="TrainingParamManager核心方法",
                requirement="文档要求的训练参数管理方法",
                check_type="method",
                expected_items=[
                    "extract_training_parameters",
                    "check_required_training_params",
                    "get_training_prompt_message"
                ]
            ),
            
            # 意图处理方法检查
            ComplianceCheck(
                name="intent_handler_methods",
                description="IntentHandler意图处理方法",
                requirement="文档要求的意图处理方法",
                check_type="method",
                expected_items=[
                    "handle_exercise_intent",
                    "handle_training_plan_intent",
                    "handle_fitness_advice_intent",
                    "handle_general_chat"
                ]
            ),
            
            # API端点检查
            ComplianceCheck(
                name="api_endpoints",
                description="聊天API端点",
                requirement="文档要求的API端点",
                check_type="module",
                expected_items=[
                    "app.api.v2.endpoints.chat",
                    "/api/v2/chat/message",
                    "/api/v2/chat/conversations",
                    "/api/v2/chat/stream"
                ]
            ),
            
            # 数据模型检查
            ComplianceCheck(
                name="data_models",
                description="核心数据模型",
                requirement="文档要求的数据模型",
                check_type="class",
                expected_items=[
                    "app.models.conversation.Conversation",
                    "app.models.message.Message",
                    "app.models.qa_pair.QAPair",
                    "app.models.training_plan.TrainingPlan"
                ]
            ),
            
            # LLM服务检查
            ComplianceCheck(
                name="llm_services",
                description="LLM相关服务",
                requirement="文档要求的LLM服务组件",
                check_type="class",
                expected_items=[
                    "app.services.llm_proxy_service.LLMProxyService",
                    "app.services.llm_log_service.LLMLogService",
                    "app.services.memory_service.MemoryService"
                ]
            ),
            
            # 工具服务检查
            ComplianceCheck(
                name="tool_services",
                description="工具和辅助服务",
                requirement="文档要求的工具服务",
                check_type="class",
                expected_items=[
                    "app.services.sql_tool_service.SQLToolService",
                    "app.services.tool_registrar.ToolRegistrar",
                    "app.services.training_plan_service.TrainingPlanService"
                ]
            )
        ]
    
    def run_compliance_tests(self) -> Dict[str, Any]:
        """运行所有合规性测试"""
        print("🔍 开始文档规范一致性测试")
        print("=" * 80)
        
        start_time = time.time()
        
        for check in self.compliance_checks:
            print(f"\n📋 检查: {check.name}")
            print(f"描述: {check.description}")
            
            if check.check_type == "class":
                result = self._check_classes(check)
            elif check.check_type == "method":
                result = self._check_methods(check)
            elif check.check_type == "module":
                result = self._check_modules(check)
            elif check.check_type == "flow":
                result = self._check_flows(check)
            else:
                result = ComplianceReport(
                    check_name=check.name,
                    result=ComplianceResult.NOT_FOUND,
                    found_items=[],
                    missing_items=check.expected_items,
                    details="未知的检查类型"
                )
            
            self.compliance_reports.append(result)
            self._print_check_result(result)
        
        total_time = time.time() - start_time
        report = self._generate_compliance_report(total_time)
        
        return report
    
    def _check_classes(self, check: ComplianceCheck) -> ComplianceReport:
        """检查类的存在性"""
        found_items = []
        missing_items = []
        
        for item in check.expected_items:
            try:
                # 解析模块路径和类名
                parts = item.split('.')
                module_path = '.'.join(parts[:-1])
                class_name = parts[-1]
                
                # 尝试导入模块
                module = importlib.import_module(module_path)
                
                # 检查类是否存在
                if hasattr(module, class_name):
                    found_items.append(item)
                else:
                    missing_items.append(item)
                    
            except ImportError:
                missing_items.append(item)
            except Exception as e:
                missing_items.append(f"{item} (错误: {str(e)})")
        
        # 确定合规性结果
        if not missing_items:
            result = ComplianceResult.COMPLIANT
        elif found_items:
            result = ComplianceResult.PARTIALLY_COMPLIANT
        else:
            result = ComplianceResult.NON_COMPLIANT
        
        return ComplianceReport(
            check_name=check.name,
            result=result,
            found_items=found_items,
            missing_items=missing_items,
            details=f"找到 {len(found_items)}/{len(check.expected_items)} 个必需组件"
        )
    
    def _check_methods(self, check: ComplianceCheck) -> ComplianceReport:
        """检查方法的存在性"""
        found_items = []
        missing_items = []
        
        # 根据检查名称确定要检查的类
        target_classes = self._get_target_classes_for_method_check(check.name)
        
        if not target_classes:
            return ComplianceReport(
                check_name=check.name,
                result=ComplianceResult.NOT_FOUND,
                found_items=[],
                missing_items=check.expected_items,
                details="未找到目标类进行方法检查"
            )
        
        for class_path in target_classes:
            try:
                # 导入并检查类
                parts = class_path.split('.')
                module_path = '.'.join(parts[:-1])
                class_name = parts[-1]
                
                module = importlib.import_module(module_path)
                target_class = getattr(module, class_name)
                
                # 检查方法
                for method_name in check.expected_items:
                    if hasattr(target_class, method_name):
                        found_items.append(f"{class_name}.{method_name}")
                    else:
                        missing_items.append(f"{class_name}.{method_name}")
                        
            except Exception as e:
                for method_name in check.expected_items:
                    missing_items.append(f"{class_path}.{method_name} (错误: {str(e)})")
        
        # 确定合规性结果
        if not missing_items:
            result = ComplianceResult.COMPLIANT
        elif found_items:
            result = ComplianceResult.PARTIALLY_COMPLIANT
        else:
            result = ComplianceResult.NON_COMPLIANT
        
        return ComplianceReport(
            check_name=check.name,
            result=result,
            found_items=found_items,
            missing_items=missing_items,
            details=f"找到 {len(found_items)} 个方法，缺失 {len(missing_items)} 个方法"
        )
    
    def _check_modules(self, check: ComplianceCheck) -> ComplianceReport:
        """检查模块的存在性"""
        found_items = []
        missing_items = []
        
        for item in check.expected_items:
            if item.startswith('/'):
                # API端点检查
                # 这里简化处理，实际应该检查路由注册
                if self._check_api_endpoint_exists(item):
                    found_items.append(item)
                else:
                    missing_items.append(item)
            else:
                # 模块检查
                try:
                    importlib.import_module(item)
                    found_items.append(item)
                except ImportError:
                    missing_items.append(item)
        
        # 确定合规性结果
        if not missing_items:
            result = ComplianceResult.COMPLIANT
        elif found_items:
            result = ComplianceResult.PARTIALLY_COMPLIANT
        else:
            result = ComplianceResult.NON_COMPLIANT
        
        return ComplianceReport(
            check_name=check.name,
            result=result,
            found_items=found_items,
            missing_items=missing_items,
            details=f"找到 {len(found_items)}/{len(check.expected_items)} 个模块/端点"
        )
    
    def _check_flows(self, check: ComplianceCheck) -> ComplianceReport:
        """检查流程的实现"""
        # 流程检查比较复杂，这里简化处理
        return ComplianceReport(
            check_name=check.name,
            result=ComplianceResult.PARTIALLY_COMPLIANT,
            found_items=[],
            missing_items=[],
            details="流程检查需要运行时验证"
        )
    
    def _get_target_classes_for_method_check(self, check_name: str) -> List[str]:
        """根据检查名称获取目标类"""
        class_mapping = {
            "conversation_service_methods": ["app.services.conversation.orchestrator.ConversationService"],
            "user_profile_manager_methods": ["app.services.conversation.user_profile_manager.UserProfileManager"],
            "training_param_manager_methods": ["app.services.conversation.training_param_manager.TrainingParamManager"],
            "intent_handler_methods": ["app.services.conversation.intent_handler.IntentHandler"]
        }
        
        return class_mapping.get(check_name, [])
    
    def _check_api_endpoint_exists(self, endpoint: str) -> bool:
        """检查API端点是否存在"""
        # 简化检查，实际应该解析路由配置
        try:
            if "chat" in endpoint:
                # 检查聊天相关的路由文件
                import app.api.v2.endpoints.chat
                return True
        except ImportError:
            pass
        
        return False
    
    def _print_check_result(self, result: ComplianceReport):
        """打印检查结果"""
        if result.result == ComplianceResult.COMPLIANT:
            print(f"  ✅ 完全合规")
        elif result.result == ComplianceResult.PARTIALLY_COMPLIANT:
            print(f"  ⚠️ 部分合规")
        elif result.result == ComplianceResult.NON_COMPLIANT:
            print(f"  ❌ 不合规")
        else:
            print(f"  🚫 未找到")
        
        print(f"  详情: {result.details}")
        
        if result.found_items:
            print(f"  找到: {', '.join(result.found_items[:3])}{'...' if len(result.found_items) > 3 else ''}")
        
        if result.missing_items:
            print(f"  缺失: {', '.join(result.missing_items[:3])}{'...' if len(result.missing_items) > 3 else ''}")
    
    def _generate_compliance_report(self, total_time: float) -> Dict[str, Any]:
        """生成合规性报告"""
        total_checks = len(self.compliance_reports)
        compliant_checks = len([r for r in self.compliance_reports if r.result == ComplianceResult.COMPLIANT])
        partial_checks = len([r for r in self.compliance_reports if r.result == ComplianceResult.PARTIALLY_COMPLIANT])
        non_compliant_checks = len([r for r in self.compliance_reports if r.result == ComplianceResult.NON_COMPLIANT])
        not_found_checks = len([r for r in self.compliance_reports if r.result == ComplianceResult.NOT_FOUND])
        
        compliance_score = (compliant_checks + partial_checks * 0.5) / total_checks * 100 if total_checks > 0 else 0
        
        report = {
            "summary": {
                "total_checks": total_checks,
                "compliant": compliant_checks,
                "partially_compliant": partial_checks,
                "non_compliant": non_compliant_checks,
                "not_found": not_found_checks,
                "compliance_score": compliance_score,
                "total_duration": total_time
            },
            "details": [
                {
                    "check_name": r.check_name,
                    "result": r.result.value,
                    "found_items": r.found_items,
                    "missing_items": r.missing_items,
                    "details": r.details
                }
                for r in self.compliance_reports
            ]
        }
        
        return report

def main():
    """主函数"""
    print("🔍 启动文档规范一致性测试系统")
    print("=" * 80)
    
    tester = DocumentationComplianceTester()
    report = tester.run_compliance_tests()
    
    # 打印测试报告
    print("\n" + "=" * 80)
    print("📊 文档合规性报告摘要")
    print("=" * 80)
    
    summary = report["summary"]
    print(f"总检查项: {summary['total_checks']}")
    print(f"完全合规: {summary['compliant']} ✅")
    print(f"部分合规: {summary['partially_compliant']} ⚠️")
    print(f"不合规: {summary['non_compliant']} ❌")
    print(f"未找到: {summary['not_found']} 🚫")
    print(f"合规分数: {summary['compliance_score']:.1f}%")
    print(f"总耗时: {summary['total_duration']:.2f}秒")
    
    # 保存详细报告
    with open("compliance_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 详细报告已保存到: compliance_report.json")
    
    return summary['compliance_score'] >= 70  # 70%以上合规分数认为合格

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
