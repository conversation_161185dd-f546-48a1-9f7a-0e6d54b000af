-- 创建导入食品数据的函数
CREATE OR REPLACE FUNCTION import_food_from_json(json_data JSONB) 
RETURNS INTEGER AS $$
DECLARE
    v_food_id INTEGER;
    v_food_name TEXT;
    v_existing_id INTEGER;
    nutrient_data JSONB;
    unit_data JSONB;
    nutrient_item JSONB;
    unit_item JSONB;
    category_name TEXT;
BEGIN
    -- 提取食品名称
    v_food_name := json_data->'food'->>'name';
    
    -- 检查食品名称是否已存在
    SELECT id INTO v_existing_id FROM foods WHERE name = v_food_name;
    
    -- 如果名称已存在，直接返回已存在食品ID
    IF v_existing_id IS NOT NULL THEN
        RETURN v_existing_id;
    END IF;
    
    -- 插入食品基本信息
    INSERT INTO foods (
        id, name, code, category, category_code, food_type, goods_id, 
        thumb_image_url, large_image_url, is_liquid, 
        updated_at, can_revise
    ) VALUES (
        (json_data->'food'->>'id')::INTEGER,
        v_food_name,
        json_data->'food'->>'code',
        json_data->'food'->>'category',
        (json_data->'food'->>'category_code')::INTEGER,
        json_data->'food'->>'food_type',
        (json_data->'food'->>'goods_id')::INTEGER,
        json_data->'food'->>'thumb_image_url',
        json_data->'food'->>'large_image_url',
        (json_data->'food'->>'is_liquid')::BOOLEAN,
        (json_data->'food'->>'updated_at')::TIMESTAMP,
        (json_data->'food'->>'can_revise')::BOOLEAN
    )
    ON CONFLICT (id) DO UPDATE SET
        name = EXCLUDED.name,
        code = EXCLUDED.code,
        category = EXCLUDED.category,
        category_code = EXCLUDED.category_code,
        food_type = EXCLUDED.food_type,
        goods_id = EXCLUDED.goods_id,
        thumb_image_url = EXCLUDED.thumb_image_url,
        large_image_url = EXCLUDED.large_image_url,
        is_liquid = EXCLUDED.is_liquid,
        updated_at = EXCLUDED.updated_at,
        can_revise = EXCLUDED.can_revise
    RETURNING id INTO v_food_id;
    
    -- 插入营养概况
    INSERT INTO nutritional_profiles (
        food_id, health_light, lights, warnings, warning_scenes,
        calory, protein_fraction, fat_fraction, carb_fraction
    ) VALUES (
        v_food_id,
        (json_data->'nutritional_profile'->'red_green_section'->>'health_light')::INTEGER,
        json_data->'nutritional_profile'->'red_green_section'->'lights',
        json_data->'nutritional_profile'->'red_green_section'->'warnings',
        json_data->'nutritional_profile'->'red_green_section'->'warning_scenes',
        (json_data->'nutritional_profile'->'nutrients_section'->>'calory')::FLOAT,
        (json_data->'nutritional_profile'->'nutrients_section'->'energy_fractions'->>'protein')::FLOAT / 100,
        (json_data->'nutritional_profile'->'nutrients_section'->'energy_fractions'->>'fat')::FLOAT / 100,
        (json_data->'nutritional_profile'->'nutrients_section'->'energy_fractions'->>'carbohydrate')::FLOAT / 100
    )
    ON CONFLICT (food_id) DO UPDATE SET
        health_light = EXCLUDED.health_light,
        lights = EXCLUDED.lights,
        warnings = EXCLUDED.warnings,
        warning_scenes = EXCLUDED.warning_scenes,
        calory = EXCLUDED.calory,
        protein_fraction = EXCLUDED.protein_fraction,
        fat_fraction = EXCLUDED.fat_fraction,
        carb_fraction = EXCLUDED.carb_fraction;
    
    -- 删除旧的营养素数据
    DELETE FROM food_nutrient_values WHERE food_id = v_food_id;
    
    -- 插入主要营养素
    FOR nutrient_item IN SELECT * FROM jsonb_array_elements(json_data->'nutritional_profile'->'nutrients_section'->'values'->'main')
    LOOP
        INSERT INTO food_nutrient_values (
            food_id, name_en, name_cn, value, unit, unit_name, precision, nrv, category
        ) VALUES (
            v_food_id,
            nutrient_item->>'name_en',
            nutrient_item->>'name',
            (nutrient_item->>'value')::FLOAT,
            nutrient_item->>'unit',
            nutrient_item->>'unit_name',
            (nutrient_item->>'precision')::INTEGER,
            (nutrient_item->>'nrv')::FLOAT,
            'main'
        );
    END LOOP;
    
    -- 插入维生素
    FOR nutrient_item IN SELECT * FROM jsonb_array_elements(json_data->'nutritional_profile'->'nutrients_section'->'values'->'vitamin')
    LOOP
        INSERT INTO food_nutrient_values (
            food_id, name_en, name_cn, value, unit, unit_name, precision, nrv, category
        ) VALUES (
            v_food_id,
            nutrient_item->>'name_en',
            nutrient_item->>'name',
            (nutrient_item->>'value')::FLOAT,
            nutrient_item->>'unit',
            nutrient_item->>'unit_name',
            (nutrient_item->>'precision')::INTEGER,
            (nutrient_item->>'nrv')::FLOAT,
            'vitamin'
        );
    END LOOP;
    
    -- 插入矿物质
    FOR nutrient_item IN SELECT * FROM jsonb_array_elements(json_data->'nutritional_profile'->'nutrients_section'->'values'->'minerals')
    LOOP
        INSERT INTO food_nutrient_values (
            food_id, name_en, name_cn, value, unit, unit_name, precision, nrv, category
        ) VALUES (
            v_food_id,
            nutrient_item->>'name_en',
            nutrient_item->>'name',
            (nutrient_item->>'value')::FLOAT,
            nutrient_item->>'unit',
            nutrient_item->>'unit_name',
            (nutrient_item->>'precision')::INTEGER,
            (nutrient_item->>'nrv')::FLOAT,
            'minerals'
        );
    END LOOP;
    
    -- 插入其他营养素
    FOR nutrient_item IN SELECT * FROM jsonb_array_elements(json_data->'nutritional_profile'->'nutrients_section'->'values'->'other')
    LOOP
        INSERT INTO food_nutrient_values (
            food_id, name_en, name_cn, value, unit, unit_name, precision, nrv, category
        ) VALUES (
            v_food_id,
            nutrient_item->>'name_en',
            nutrient_item->>'name',
            (nutrient_item->>'value')::FLOAT,
            nutrient_item->>'unit',
            nutrient_item->>'unit_name',
            (nutrient_item->>'precision')::INTEGER,
            (nutrient_item->>'nrv')::FLOAT,
            'other'
        );
    END LOOP;
    
    -- 删除旧的单位数据
    DELETE FROM food_units WHERE food_id = v_food_id;
    
    -- 插入计量单位
    FOR unit_item IN SELECT * FROM jsonb_array_elements(json_data->'nutritional_profile'->'nutrients_section'->'units')
    LOOP
        INSERT INTO food_units (
            food_id, unit_name, weight, eat_weight, is_default
        ) VALUES (
            v_food_id,
            unit_item->>'unit_name',
            (unit_item->>'weight')::FLOAT,
            (unit_item->>'eat_weight')::FLOAT,
            (unit_item->>'id')::INTEGER = 0  -- 假设id=0的是默认单位
        );
    END LOOP;
    
    RETURN v_food_id;
END;
$$ LANGUAGE plpgsql;