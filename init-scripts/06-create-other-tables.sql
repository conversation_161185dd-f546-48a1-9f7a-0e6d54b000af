-- 创建训练计划表
CREATE TABLE IF NOT EXISTS training_plans (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id),
    name VARCHAR NOT NULL,
    description VARCHAR,
    plan_data JSON NOT NULL,
    status VARCHAR DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
CREATE INDEX IF NOT EXISTS ix_training_plans_id ON training_plans(id);

-- 创建对话表
CREATE TABLE IF NOT EXISTS conversations (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id),
    session_id VARCHAR NOT NULL UNIQUE,
    start_time TIMESTAMP WITH TIME ZONE DEFAULT now(),
    last_active TIMESTAMP WITH TIME ZONE DEFAULT now(),
    is_active BOOLEAN DEFAULT TRUE,
    meta_info JSON
);
CREATE INDEX IF NOT EXISTS ix_conversations_id ON conversations(id);
CREATE INDEX IF NOT EXISTS ix_conversations_user_id ON conversations(user_id);
CREATE INDEX IF NOT EXISTS ix_conversations_session_id ON conversations(session_id);

-- 创建消息角色类型 (如果不存在)
DO $$ BEGIN
    CREATE TYPE messagerole AS ENUM ('USER', 'ASSISTANT', 'SYSTEM', 'TOOL');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- 创建消息表
CREATE TABLE IF NOT EXISTS messages (
    id SERIAL PRIMARY KEY,
    conversation_id INTEGER NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- 确保添加了外键和级联删除
    content TEXT NOT NULL,
    role messagerole NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    meta_info JSON
);
CREATE INDEX IF NOT EXISTS ix_messages_id ON messages(id);
CREATE INDEX IF NOT EXISTS ix_messages_conversation_id ON messages(conversation_id);
CREATE INDEX IF NOT EXISTS ix_messages_user_id ON messages(user_id);

-- 创建问答对表
CREATE TABLE IF NOT EXISTS qa_pairs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id),
    session_id VARCHAR,
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    intent VARCHAR(50),
    retrieved_docs JSON,
    tool_used VARCHAR(50),
    feedback_score INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    meta_info JSON
);
CREATE INDEX IF NOT EXISTS ix_qa_pairs_id ON qa_pairs(id);
CREATE INDEX IF NOT EXISTS ix_qa_pairs_user_id ON qa_pairs(user_id);
CREATE INDEX IF NOT EXISTS ix_qa_pairs_session_id ON qa_pairs(session_id);
CREATE INDEX IF NOT EXISTS ix_qa_pairs_intent ON qa_pairs(intent);

-- 创建反馈表
CREATE TABLE IF NOT EXISTS feedbacks (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    type VARCHAR NOT NULL,
    content TEXT NOT NULL,
    rating INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
CREATE INDEX IF NOT EXISTS ix_feedbacks_id ON feedbacks(id); 