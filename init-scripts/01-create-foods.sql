-- 创建食品基本信息表
CREATE TABLE IF NOT EXISTS foods (
    id SERIAL PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(128) NOT NULL,
    code VARCHAR(64) NOT NULL UNIQUE,
    category VARCHAR(64),
    food_type VARCHAR(32),
    goods_id INTEGER,
    thumb_image_url VARCHAR,
    large_image_url VARCHAR,
    is_liquid BOOLEAN DEFAULT FALSE,
    hot INTEGER DEFAULT 0,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    can_revise BOOLEAN DEFAULT FALSE
);
CREATE INDEX IF NOT EXISTS ix_foods_id ON foods(id);
CREATE INDEX IF NOT EXISTS ix_foods_name ON foods(name);
CREATE INDEX IF NOT EXISTS ix_foods_code ON foods(code);
CREATE INDEX IF NOT EXISTS ix_foods_category ON foods(category);
CREATE INDEX IF NOT EXISTS ix_foods_food_type ON foods(food_type);

-- 创建食品营养概况表
CREATE TABLE IF NOT EXISTS nutritional_profiles (
    id SERIAL PRIMARY KEY,
    food_id INTEGER UNIQUE REFERENCES foods(id) ON DELETE CASCADE,
    health_light INTEGER,
    lights JSON,
    warnings JSON,
    warning_scenes JSON,
    calory FLOAT,
    protein_fraction FLOAT,
    fat_fraction FLOAT,
    carb_fraction FLOAT,
    food_rank INTEGER,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
CREATE INDEX IF NOT EXISTS ix_nutritional_profiles_id ON nutritional_profiles(id);

-- 创建食品营养素明细表
CREATE TABLE IF NOT EXISTS food_nutrient_values (
    id SERIAL PRIMARY KEY,
    food_id INTEGER REFERENCES foods(id) ON DELETE CASCADE,
    name_en VARCHAR(64) NOT NULL,
    name_cn VARCHAR(64) NOT NULL,
    value FLOAT,
    unit VARCHAR(16),
    unit_name VARCHAR(16),
    precision INTEGER,
    nrv FLOAT,
    category VARCHAR(16) NOT NULL
);
CREATE INDEX IF NOT EXISTS ix_food_nutrient_values_id ON food_nutrient_values(id);
CREATE INDEX IF NOT EXISTS ix_food_nutrient_values_food_id ON food_nutrient_values(food_id);
CREATE INDEX IF NOT EXISTS ix_food_nutrient_values_category ON food_nutrient_values(category);

-- 创建食品计量单位表
CREATE TABLE IF NOT EXISTS food_units (
    id SERIAL PRIMARY KEY,
    food_id INTEGER REFERENCES foods(id) ON DELETE CASCADE,
    unit_name VARCHAR(32) NOT NULL,
    weight FLOAT NOT NULL,
    eat_weight FLOAT NOT NULL,
    is_default BOOLEAN DEFAULT FALSE
);
CREATE INDEX IF NOT EXISTS ix_food_units_id ON food_units(id);
CREATE INDEX IF NOT EXISTS ix_food_units_food_id ON food_units(food_id);