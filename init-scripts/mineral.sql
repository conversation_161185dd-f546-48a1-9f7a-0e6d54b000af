-- 1. 重建并增加每列单位字段的 膳食矿物质推荐摄入量表
DROP TABLE IF EXISTS public.mineral_rni;
CREATE TABLE public.mineral_rni (
  id                SERIAL       PRIMARY KEY,
  age_start_years   NUMERIC(5,2) NOT NULL,    -- 起始年龄（年）
  sex               VARCHAR(6)   NOT NULL,    -- 'male','female','both'
  rec_type          VARCHAR(3)   NOT NULL,    -- 'AI' 或 'RNI'

  calcium          NUMERIC,       -- 钙
  calcium_unit     CHAR(10) NOT NULL,

  phosphor         NUMERIC,       -- 磷
  phosphor_unit    CHAR(10) NOT NULL,

  kalium           NUMERIC,       -- 钾
  kalium_unit      CHAR(10) NOT NULL,

  natrium          NUMERIC,       -- 钠
  natrium_unit     CHAR(10) NOT NULL,

  magnesium        NUMERIC,       -- 镁
  magnesium_unit   CHAR(10) NOT NULL,

  chlorine         NUMERIC,       -- 氯
  chlorine_unit    CHAR(10) NOT NULL,

  iron             NUMERIC,       -- 铁
  iron_unit        CHAR(10) NOT NULL,

  iodine           NUMERIC,       -- 碘
  iodine_unit      CHAR(10) NOT NULL,

  zinc             NUMERIC,       -- 锌
  zinc_unit        CHAR(10) NOT NULL,

  selenium         NUMERIC,       -- 硒
  selenium_unit    CHAR(10) NOT NULL,

  copper           NUMERIC,       -- 铜
  copper_unit      CHAR(10) NOT NULL,

  fluorine         NUMERIC,       -- 氟
  fluorine_unit    CHAR(10) NOT NULL,

  chromium         NUMERIC,       -- 铬
  chromium_unit    CHAR(10) NOT NULL,

  cobalt           NUMERIC,       -- 钴
  cobalt_unit      CHAR(10) NOT NULL,

  manganese        NUMERIC,       -- 锰
  manganese_unit   CHAR(10) NOT NULL DEFAULT 'mg'
);

-- 2. 插入 24 条年龄／性别矿物质 RNI/AI 数据（单位已简化为 mg 或 μg）
INSERT INTO public.mineral_rni (
  age_start_years, sex, rec_type,
  calcium, calcium_unit,
  phosphor, phosphor_unit,
  kalium, kalium_unit,
  natrium, natrium_unit,
  magnesium, magnesium_unit,
  chlorine, chlorine_unit,
  iron, iron_unit,
  iodine, iodine_unit,
  zinc, zinc_unit,
  selenium, selenium_unit,
  copper, copper_unit,
  fluorine, fluorine_unit,
  chromium, chromium_unit,
  cobalt, cobalt_unit
) VALUES
  -- 0~ 岁 （AI，共用）
  (0.00, 'both', 'AI', 200, 'mg', 105, 'mg', 400, 'mg', 80, 'mg', 20, 'mg', 120, 'mg', 0.3, 'mg', 85, 'μg', 1.5, 'mg', 15, 'μg', 0.3, 'mg', 0.01, 'mg', 0.2, 'μg', 3, 'μg'),
  -- 0.5~ 岁 （AI，共用）
  (0.50, 'both', 'AI', 350, 'mg', 180, 'mg', 600, 'mg', 180, 'mg', 65, 'mg', 450, 'mg', 10.0, 'mg', 115, 'μg', 3.2, 'mg', 20, 'μg', 0.3, 'mg', 0.23, 'mg', 5.0, 'μg', 6, 'μg'),

  -- 1~ 岁
  (1.00, 'male',   'RNI', 500, 'mg', 300, 'mg', 900, 'mg', 500, 'mg', 140, 'mg', 800, 'mg', 10.0, 'mg', 90, 'μg', 4.0, 'mg', 25, 'μg', 0.3, 'mg', 0.60, 'mg', 15.0, 'μg', 10, 'μg'),
  (1.00, 'female', 'RNI', 500, 'mg', 300, 'mg', 900, 'mg', 500, 'mg', 140, 'mg', 800, 'mg', 10.0, 'mg', 90, 'μg', 4.0, 'mg', 25, 'μg', 0.3, 'mg', 0.60, 'mg', 15.0, 'μg', 10, 'μg'),

  -- 4~ 岁
  (4.00, 'male',   'RNI', 600, 'mg', 350, 'mg', 1100, 'mg', 800, 'mg', 160, 'mg', 1200, 'mg', 10.0, 'mg', 90, 'μg', 5.5, 'mg', 30, 'μg', 0.4, 'mg', 0.70, 'mg', 15.0, 'μg', 12, 'μg'),
  (4.00, 'female', 'RNI', 600, 'mg', 350, 'mg', 1100, 'mg', 800, 'mg', 160, 'mg', 1200, 'mg', 10.0, 'mg', 90, 'μg', 5.5, 'mg', 30, 'μg', 0.4, 'mg', 0.70, 'mg', 15.0, 'μg', 12, 'μg'),

  -- 7~ 岁
  (7.00, 'male',   'RNI', 800, 'mg', 440, 'mg', 1300, 'mg', 900, 'mg', 200, 'mg', 1400, 'mg', 12.0, 'mg', 90, 'μg', 7.0, 'mg', 40, 'μg', 0.5, 'mg', 0.90, 'mg', 20.0, 'μg', 15, 'μg'),
  (7.00, 'female', 'RNI', 800, 'mg', 440, 'mg', 1300, 'mg', 900, 'mg', 200, 'mg', 1400, 'mg', 12.0, 'mg', 90, 'μg', 7.0, 'mg', 40, 'μg', 0.5, 'mg', 0.90, 'mg', 20.0, 'μg', 15, 'μg'),

  -- 9~ 岁
  (9.00, 'male',   'RNI', 1000, 'mg', 550, 'mg', 1600, 'mg', 1100, 'mg', 250, 'mg', 1700, 'mg', 16.0, 'mg', 90, 'μg', 7.0, 'mg', 45, 'μg', 0.6, 'mg', 1.10, 'mg', 25.0, 'μg', 20, 'μg'),
  (9.00, 'female', 'RNI', 1000, 'mg', 550, 'mg', 1600, 'mg', 1100, 'mg', 250, 'mg', 1700, 'mg', 16.0, 'mg', 90, 'μg', 7.0, 'mg', 45, 'μg', 0.6, 'mg', 1.10, 'mg', 25.0, 'μg', 20, 'μg'),

  -- 12~ 岁
  (12.00,'male',   'RNI', 1000, 'mg', 700, 'mg', 1800, 'mg', 1400, 'mg', 320, 'mg', 2200, 'mg', 16.0, 'mg', 110,'μg', 8.5,'mg', 60,'μg', 0.7,'mg', 1.40,'mg', 33.0,'μg', 25,'μg'),
  (12.00,'female', 'RNI', 1000, 'mg', 700, 'mg', 1800, 'mg', 1400, 'mg', 320, 'mg', 2200, 'mg', 16.0, 'mg', 110,'μg', 8.5,'mg', 60,'μg', 0.7,'mg', 1.40,'mg', 33.0,'μg', 25,'μg'),

  -- 15~ 岁
  (15.00,'male',   'RNI', 1000, 'mg', 720, 'mg', 2000, 'mg', 1600, 'mg', 330, 'mg', 2500, 'mg', 16.0, 'mg', 120,'μg',11.5,'mg', 60,'μg', 0.8,'mg', 1.50,'mg', 35.0,'μg', 25,'μg'),
  (15.00,'female','RNI', 1000, 'mg', 720, 'mg', 2000, 'mg', 1600, 'mg', 330, 'mg', 2500, 'mg', 16.0, 'mg', 120,'μg',11.5,'mg', 60,'μg', 0.8,'mg', 1.50,'mg', 35.0,'μg', 25,'μg'),

  -- 18~ 岁
  (18.00,'male',   'RNI',  800, 'mg', 720, 'mg', 2000, 'mg', 1500, 'mg', 330, 'mg', 2300, 'mg', 12.0, 'mg', 120,'μg',12.0,'mg', 60,'μg', 0.8,'mg', 1.50,'mg', 35.0,'μg', 25,'μg'),
  (18.00,'female','RNI',  800,'mg', 720,'mg', 2000,'mg', 1500,'mg', 330,'mg', 2300,'mg', 12.0,'mg', 120,'μg',12.0,'mg', 60,'μg', 0.8,'mg', 1.50,'mg', 35.0,'μg', 25,'μg'),

  -- 30~ 岁
  (30.00,'male',   'RNI',  800,'mg', 710,'mg', 2000,'mg', 1500,'mg', 320,'mg', 2300,'mg', 12.0,'mg', 120,'μg',12.0,'mg', 60,'μg', 0.8,'mg', 1.50,'mg', 35.0,'μg', 25,'μg'),
  (30.00,'female','RNI',  800,'mg', 710,'mg', 2000,'mg', 1500,'mg', 320,'mg', 2300,'mg', 12.0,'mg', 120,'μg',12.0,'mg', 60,'μg', 0.8,'mg', 1.50,'mg', 35.0,'μg', 25,'μg'),

  -- 50~ 岁
  (50.00,'male',   'RNI',  800,'mg', 710,'mg', 2000,'mg', 1500,'mg', 320,'mg', 2300,'mg', 12.0,'mg', 120,'μг',12.0,'mg', 60,'μг', 0.8,'mg', 1.50,'mg', 30.0,'μг', 25,'μг'),
  (50.00,'female','RNI',  800,'mg', 710,'mg', 2000,'mg', 1500,'mg', 320,'mg', 2300,'mg', 12.0,'mg', 120,'μг',12.0,'mg', 60,'μг', 0.8,'mg', 1.50,'mg', 30.0,'μг', 25,'μг'),

  -- 65~ 岁
  (65.00,'male',   'RNI',  800,'mg', 680,'mg', 2000,'mg', 1400,'mg', 310,'mg', 2200,'mg', 12.0,'mg', 120,'μг',12.0,'mg', 60,'μг', 0.8,'mg', 1.50,'mg', 30.0,'μг', 25,'μг'),
  (65.00,'female','RNI',  800,'mg', 680,'mg', 2000,'mg', 1400,'mg', 310,'mg', 2200,'mg', 12.0,'mg', 120,'μг',12.0,'mg', 60,'μг', 0.8,'mg', 1.50,'mg', 30.0,'μг', 25,'μг'),

  -- 75~ 岁
  (75.00,'male',   'RNI',  800,'mg', 680,'mg', 2000,'mg', 1400,'mg', 300,'mg', 2200,'mg', 12.0,'mg', 120,'μг',12.0,'mg', 60,'μг', 0.7,'mg', 1.50,'mg', 30.0,'μг', 25,'μг'),
  (75.00,'female','RNI',  800,'mg', 680,'mg', 2000,'mg', 1400,'mg', 300,'mg', 2200,'mg', 12.0,'mg', 120,'μг',12.0,'mg', 60,'μг', 0.7,'mg', 1.50,'mg', 30.0,'μг', 25,'μг');


CREATE TABLE IF NOT EXISTS public.mineral_pregnancy_inc (
  id              SERIAL      PRIMARY KEY,
  stage           VARCHAR(16) NOT NULL,    -- 'early','mid','late','lactation'
  calcium_inc     NUMERIC,               -- mg/d
  phosphor_inc    NUMERIC,               -- mg/d
  kalium_inc      NUMERIC,               -- mg/d
  natrium_inc     NUMERIC,               -- mg/d
  magnesium_inc   NUMERIC,               -- mg/d
  chlorine_inc    NUMERIC,               -- mg/d
  iron_inc        NUMERIC,               -- mg/d
  iodine_inc      NUMERIC,               -- μg/d
  zinc_inc        NUMERIC,               -- mg/d
  selenium_inc    NUMERIC,               -- μg/d
  copper_inc      NUMERIC,               -- mg/d
  fluorine_inc    NUMERIC,               -- mg/d
  chromium_inc    NUMERIC,               -- μg/d
  cobalt_inc      NUMERIC                -- μg/d
);

-- 4. 插入：孕期/哺乳期增量数据
INSERT INTO public.mineral_pregnancy_inc (
  stage, calcium_inc, phosphor_inc, kalium_inc, natrium_inc,
  magnesium_inc, chlorine_inc, iron_inc, iodine_inc, zinc_inc,
  selenium_inc, copper_inc, fluorine_inc, chromium_inc, cobalt_inc
) VALUES
  ('early',      0,   0,    0,   0,  40,  0,   NULL,  0, 110,   NULL, 2.0,  5.0, 0.0,   0),
  ('mid',        0,   0,    0,   0,  40,  0,   NULL,  7, 110,   NULL, 2.0,  5.0, 0.0,   0),
  ('late',       0,   0,    0,   0,  40,  0,   NULL, 11, 110,   NULL, 2.0,  5.0, 0.0,   0),
  ('lactation',  0,   0,  400,   0,   0,  0,   NULL,  6, 120,   NULL, 4.5, 18.0, 0.7,   5);