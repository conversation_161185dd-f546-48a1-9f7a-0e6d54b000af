-- 创建健身动作基本信息表
CREATE TABLE IF NOT EXISTS exercises (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    en_name VARCHAR(100),
    body_part_id INTEGER[],
    equipment_id INTEGER[],
    image_name VARCHAR(255),
    gif_url VARCHAR(255),
    description TEXT,
    level SMALLINT,
    sort_priority INTEGER DEFAULT 0,
    user_id INTEGER,
    exercise_type VARCHAR(50),
    hit_time INTEGER DEFAULT 0,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT (now() at time zone 'utc'),
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT (now() at time zone 'utc')
);
CREATE INDEX IF NOT EXISTS ix_exercises_id ON exercises(id);
CREATE INDEX IF NOT EXISTS ix_exercises_name ON exercises(name);
CREATE INDEX IF NOT EXISTS ix_exercises_en_name ON exercises(en_name);
CREATE INDEX IF NOT EXISTS ix_exercise_name_en_name ON exercises(name, en_name);
CREATE INDEX IF NOT EXISTS ix_exercise_level_hit_time ON exercises(level, hit_time);
CREATE INDEX IF NOT EXISTS idx_exercise_name_lower ON exercises(lower(name));
CREATE INDEX IF NOT EXISTS idx_exercise_en_name_lower ON exercises(lower(en_name));
CREATE INDEX IF NOT EXISTS idx_popular_exercises ON exercises(hit_time DESC) WHERE hit_time > 0;
CREATE INDEX IF NOT EXISTS idx_exercise_sort_priority ON exercises(sort_priority DESC, id ASC);
CREATE INDEX IF NOT EXISTS idx_body_part_id_gin ON exercises USING GIN (body_part_id);
CREATE INDEX IF NOT EXISTS idx_equipment_id_gin ON exercises USING GIN (equipment_id);

-- 创建健身动作详细信息表
CREATE TABLE IF NOT EXISTS exercise_details (
    id SERIAL PRIMARY KEY,
    exercise_id INTEGER NOT NULL REFERENCES exercises(id),
    target_muscles_id INTEGER[],
    synergist_muscles_id INTEGER[],
    ex_instructions TEXT[],
    exercise_tips TEXT[],
    video_file VARCHAR(255),
    is_public BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT (now() at time zone 'utc'),
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT (now() at time zone 'utc')
);
CREATE INDEX IF NOT EXISTS ix_exercise_details_id ON exercise_details(id);
CREATE INDEX IF NOT EXISTS ix_exercise_details_exercise_id ON exercise_details(exercise_id);
CREATE INDEX IF NOT EXISTS idx_exercise_detail_public ON exercise_details(exercise_id, is_public);
CREATE INDEX IF NOT EXISTS idx_target_muscles_gin ON exercise_details USING GIN (target_muscles_id);
CREATE INDEX IF NOT EXISTS idx_synergist_muscles_gin ON exercise_details USING GIN (synergist_muscles_id);

-- 创建肌肉信息表
CREATE TABLE IF NOT EXISTS muscles (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    en_name VARCHAR(50) NOT NULL UNIQUE
);
CREATE INDEX IF NOT EXISTS ix_muscles_id ON muscles(id);
CREATE INDEX IF NOT EXISTS idx_muscle_name ON muscles(name);
CREATE INDEX IF NOT EXISTS idx_muscle_name_lower ON muscles(lower(name));

-- 创建身体部位表
CREATE TABLE IF NOT EXISTS body_parts (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE
);
CREATE INDEX IF NOT EXISTS ix_body_parts_id ON body_parts(id);
CREATE INDEX IF NOT EXISTS idx_body_part_name_lower ON body_parts(lower(name));

-- 创建器材表
CREATE TABLE IF NOT EXISTS equipment (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE
);
CREATE INDEX IF NOT EXISTS ix_equipment_id ON equipment(id);
CREATE INDEX IF NOT EXISTS idx_equipment_name_lower ON equipment(lower(name)); 