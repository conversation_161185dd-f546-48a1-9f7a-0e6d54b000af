-- 创建用户收藏动作表
CREATE TABLE IF NOT EXISTS user_favorite_exercises (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    exercise_id INTEGER NOT NULL REFERENCES exercises(id) ON DELETE CASCADE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    is_active BOOLEAN DEFAULT TRUE
);
CREATE INDEX IF NOT EXISTS ix_user_favorite_exercises_id ON user_favorite_exercises(id);
CREATE INDEX IF NOT EXISTS idx_favorite_exercises_user_id ON user_favorite_exercises(user_id);
CREATE INDEX IF NOT EXISTS idx_favorite_exercises_exercise_id ON user_favorite_exercises(exercise_id);
CREATE INDEX IF NOT EXISTS idx_favorite_exercises_active ON user_favorite_exercises(is_active);

-- 创建用户收藏食物表
CREATE TABLE IF NOT EXISTS user_favorite_foods (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    food_id INTEGER NOT NULL REFERENCES foods(id) ON DELETE CASCADE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    is_active BOOLEAN DEFAULT TRUE
);
CREATE INDEX IF NOT EXISTS ix_user_favorite_foods_id ON user_favorite_foods(id);
CREATE INDEX IF NOT EXISTS idx_favorite_foods_user_id ON user_favorite_foods(user_id);
CREATE INDEX IF NOT EXISTS idx_favorite_foods_food_id ON user_favorite_foods(food_id);
CREATE INDEX IF NOT EXISTS idx_favorite_foods_active ON user_favorite_foods(is_active); 