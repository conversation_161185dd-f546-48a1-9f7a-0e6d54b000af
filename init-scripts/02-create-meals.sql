-- 创建餐食记录表
CREATE TABLE IF NOT EXISTS meal_records (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    date DATE NOT NULL,
    meal_type VARCHAR(20) NOT NULL,
    image_url VARCHAR,
    file_id VARCHAR(100),
    thumb_image_url VARCHAR,
    total_calory FLOAT DEFAULT 0,
    total_protein FLOAT DEFAULT 0,
    total_fat FLOAT DEFAULT 0,
    total_carbohydrate FLOAT DEFAULT 0,
    is_ai_recognized BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
CREATE INDEX IF NOT EXISTS ix_meal_records_id ON meal_records(id);
CREATE INDEX IF NOT EXISTS ix_meal_records_user_id ON meal_records(user_id);
CREATE INDEX IF NOT EXISTS ix_meal_records_date ON meal_records(date);

-- 创建食物项表
CREATE TABLE IF NOT EXISTS food_items (
    id SERIAL PRIMARY KEY,
    meal_record_id INTEGER REFERENCES meal_records(id) ON DELETE CASCADE,
    food_id INTEGER REFERENCES foods(id),
    name VARCHAR(100) NOT NULL,
    quantity FLOAT DEFAULT 1.0,
    unit_name VARCHAR(20) DEFAULT '份',
    weight FLOAT NOT NULL,
    category VARCHAR(50),
    cuisine_type VARCHAR(50),
    cuisine_type_detail VARCHAR(100),
    image_url VARCHAR,
    health_light INTEGER,
    lights JSON,
    warnings JSON,
    warning_scenes JSON,
    calory FLOAT,
    protein FLOAT,
    fat FLOAT,
    carbohydrate FLOAT,
    protein_fraction FLOAT,
    fat_fraction FLOAT,
    carb_fraction FLOAT,
    is_custom BOOLEAN DEFAULT FALSE,
    is_takeout BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
CREATE INDEX IF NOT EXISTS ix_food_items_id ON food_items(id);
CREATE INDEX IF NOT EXISTS ix_food_items_meal_record_id ON food_items(meal_record_id);
CREATE INDEX IF NOT EXISTS ix_food_items_food_id ON food_items(food_id);

-- 创建食物营养素摄入详情表
CREATE TABLE IF NOT EXISTS food_item_nutrient_intakes (
    id SERIAL PRIMARY KEY,
    food_item_id INTEGER REFERENCES food_items(id) ON DELETE CASCADE,
    name_en VARCHAR(64) NOT NULL,
    name_cn VARCHAR(64) NOT NULL,
    value FLOAT,
    unit VARCHAR(16),
    unit_name VARCHAR(16),
    nrv_percentage FLOAT,
    category VARCHAR(16) NOT NULL
);
CREATE INDEX IF NOT EXISTS ix_food_item_nutrient_intakes_id ON food_item_nutrient_intakes(id);
CREATE INDEX IF NOT EXISTS ix_food_item_nutrient_intakes_food_item_id ON food_item_nutrient_intakes(food_item_id);
CREATE INDEX IF NOT EXISTS ix_food_item_nutrient_intakes_category ON food_item_nutrient_intakes(category);

-- 创建健康建议表
CREATE TABLE IF NOT EXISTS health_recommendations (
    id SERIAL PRIMARY KEY,
    meal_record_id INTEGER REFERENCES meal_records(id) ON DELETE CASCADE,
    recommendation_text TEXT NOT NULL,
    recommendation_type VARCHAR(32),
    priority INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
CREATE INDEX IF NOT EXISTS ix_health_recommendations_id ON health_recommendations(id);
CREATE INDEX IF NOT EXISTS ix_health_recommendations_meal_record_id ON health_recommendations(meal_record_id);

-- 创建食物识别临时记录表
CREATE TABLE IF NOT EXISTS food_recognitions (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(36),
    meal_date DATE NOT NULL,
    meal_type VARCHAR(20) NOT NULL,
    image_url VARCHAR(255),
    thumb_image_url VARCHAR(255),
    secure_path VARCHAR(100),
    status VARCHAR(20) DEFAULT 'processing',
    recognition_result JSON,
    matched_foods JSON,
    nutrition_totals JSON,
    meal_record_id INTEGER REFERENCES meal_records(id),
    user_modified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT (now() at time zone 'utc'),
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT (now() at time zone 'utc')
);
CREATE INDEX IF NOT EXISTS ix_food_recognitions_id ON food_recognitions(id);
CREATE INDEX IF NOT EXISTS ix_food_recognitions_user_id ON food_recognitions(user_id);
CREATE INDEX IF NOT EXISTS ix_food_recognitions_meal_record_id ON food_recognitions(meal_record_id);