#!/usr/bin/env python3
import json
import os
import re
import shutil
import psycopg2
from psycopg2.extras import Json
import logging
from pathlib import Path
import sys

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 文件路径常量
EXERCISES_DIR = '/data/exercises'
MUSCLES_DIR = '/data/muscles'

# 修改为docker容器中的PostgreSQL连接信息
DB_INFO = {
    'dbname': 'fitness_db',
    'user': 'postgres',
    'password': 'postgres',
    'host': 'db',  # Docker容器名称
    'port': '5432'
}

# 如果不是在Docker环境中运行，则使用本地配置
if os.environ.get('RUN_ENV') != 'docker':
    # 使用本地配置
    DB_INFO = {
        'dbname': 'fitness_db',
        'user': 'postgres',
        'password': 'postgres',
        'host': 'localhost',  # 本地主机
        'port': '5432'
    }
    
    # 尝试从环境变量获取数据库URL
    if os.environ.get('DATABASE_URL'):
        # 解析数据库URL
        def parse_db_url(url):
            # 格式: postgresql://username:password@host:port/dbname
            if not url.startswith('postgresql://'):
                raise ValueError(f"不支持的数据库URL格式: {url}")
            
            auth_host, dbname = url.replace('postgresql://', '').split('/', 1)
            auth, host = auth_host.split('@', 1)
            
            if ':' in auth:
                user, password = auth.split(':', 1)
            else:
                user, password = auth, ''
                
            if ':' in host:
                host, port = host.split(':', 1)
            else:
                host, port = host, '5432'
                
            return {
                'dbname': dbname,
                'user': user,
                'password': password,
                'host': host,
                'port': port
            }

        # 解析数据库连接信息
        try:
            DB_INFO = parse_db_url(os.environ.get('DATABASE_URL'))
            logger.info(f"使用环境变量中的数据库URL: {os.environ.get('DATABASE_URL')}")
        except Exception as e:
            logger.warning(f"解析数据库URL时出错: {str(e)}，使用默认配置")


def clean_filename(filename):
    """清理文件名中的非法字符
    
    Args:
        filename: 原始文件名
        
    Returns:
        清理后的文件名
    """
    if not filename:
        return filename
    
    # 去除路径，只保留文件名
    basename = os.path.basename(filename)
    
    # 替换中文括号和其他非法字符为'-'，而不是删除
    clean_name = re.sub(r'[（）\(\)\[\]\{\}<>:"\|?*]', '', basename)
    # 替换空格
    clean_name = clean_name.replace(' ', '_')
    
    # 如果清理后的文件名与原文件名不同，记录日志
    if clean_name != basename:
        logger.info(f"文件名已清理: {basename} -> {clean_name}")
    
    return clean_name


def rename_file_if_exists(old_path, new_path):
    """重命名文件如果存在
    
    Args:
        old_path: 原始文件路径
        new_path: 新文件路径
        
    Returns:
        bool: 是否成功重命名
    """
    if not old_path or not new_path or old_path == new_path:
        return False
    
    try:
        # 检查源文件是否存在
        if os.path.exists(old_path):
            # 确保目标目录存在
            os.makedirs(os.path.dirname(new_path), exist_ok=True)
            
            # 重命名文件
            shutil.move(old_path, new_path)
            logger.info(f"文件已重命名: {old_path} -> {new_path}")
            return True
        else:
            logger.warning(f"源文件不存在，无法重命名: {old_path}")
            return False
    except Exception as e:
        logger.error(f"重命名文件时出错: {str(e)}")
        return False


def process_file_path(file_path, base_dir):
    """处理文件路径，清理文件名并重命名文件
    
    Args:
        file_path: 原始文件路径
        base_dir: 基础目录
        
    Returns:
        处理后的文件路径（保留路径结构，只修改文件名部分）
    """
    if not file_path:
        return None
    
    # 获取原始文件的绝对路径
    abs_path = file_path
    if not os.path.isabs(file_path):
        abs_path = os.path.join(base_dir, file_path)
    
    # 分离路径和文件名
    dir_path = os.path.dirname(abs_path)
    basename = os.path.basename(abs_path)
    
    # 只清理文件名部分
    clean_name = clean_filename(basename)
    
    # 构造新的绝对路径
    new_abs_path = os.path.join(dir_path, clean_name)
    
    # 重命名文件
    rename_file_if_exists(abs_path, new_abs_path)
    
    # 保持原始路径结构，只替换文件名部分
    if os.path.isabs(file_path):
        # 如果原始路径是绝对路径，则返回新的绝对路径
        return new_abs_path
    else:
        # 如果原始路径是相对路径，则返回相应的相对路径
        rel_dir = os.path.dirname(file_path)
        return os.path.join(rel_dir, clean_name) if rel_dir else clean_name


class ExerciseDataImporter:
    """健身动作数据导入工具"""
    
    def __init__(self, dbname=None, user=None, password=None, host=None, port=None):
        """初始化数据库连接"""
        # 优先使用传入的参数，否则使用默认配置
        self.db_info = {
            'dbname': dbname or DB_INFO['dbname'],
            'user': user or DB_INFO['user'],
            'password': password or DB_INFO['password'],
            'host': host or DB_INFO['host'],
            'port': port or DB_INFO['port']
        }
        
        self.conn = None
        self.cursor = None
        self.connect()
    
    def connect(self):
        """连接数据库"""
        try:
            logger.info(f"正在连接数据库: {self.db_info['host']}:{self.db_info['port']}/{self.db_info['dbname']}")
            self.conn = psycopg2.connect(
                dbname=self.db_info['dbname'],
                user=self.db_info['user'],
                password=self.db_info['password'],
                host=self.db_info['host'],
                port=self.db_info['port']
            )
            self.cursor = self.conn.cursor()
            logger.info("数据库连接成功")
        except Exception as e:
            logger.error(f"连接数据库失败: {str(e)}")
            raise
    
    def close(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
        logger.info("数据库连接已关闭")
    
    def import_exercise_from_json_file(self, exercise_file, detail_file=None, base_dir_exercises=EXERCISES_DIR, base_dir_muscles=MUSCLES_DIR):
        """从JSON文件导入健身动作数据
        
        Args:
            exercise_file: 健身动作基本信息JSON文件路径
            detail_file: 健身动作详情JSON文件路径
            base_dir_exercises: 健身动作资源文件根目录
            base_dir_muscles: 肌肉图片资源文件根目录
        """
        try:
            # 导入健身动作基本信息
            if exercise_file and os.path.exists(exercise_file):
                logger.info(f"开始从 {exercise_file} 导入健身动作基本信息...")
                
                # 读取JSON文件
                data = []
                with open(exercise_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line:  # 跳过空行
                            try:
                                item = json.loads(line)
                                
                                # 处理图片和GIF URL中的非法字符
                                if 'image_name' in item and item['image_name']:
                                    clean_name = process_file_path(item['image_name'], base_dir_exercises)
                                    item['image_name'] = clean_name
                                
                                if 'gif_url' in item and item['gif_url']:
                                    clean_name = process_file_path(item['gif_url'], base_dir_exercises)
                                    item['gif_url'] = clean_name
                                
                                data.append(item)
                            except json.JSONDecodeError as e:
                                logger.warning(f"解析JSON行时出错: {line[:50]}..., 错误: {str(e)}")
                                continue
                
                logger.info(f"从文件读取到 {len(data)} 条健身动作数据")
                
                # 导入数据
                success_count = 0
                for item in data:
                    try:
                        self.cursor.execute(
                            "SELECT import_exercise_from_json(%s)",
                            (Json(item),)
                        )
                        exercise_id = self.cursor.fetchone()[0]
                        self.conn.commit()  # 每条记录单独提交
                        success_count += 1
                        if success_count % 100 == 0:
                            logger.info(f"已导入 {success_count} 条数据...")
                    except Exception as e:
                        self.conn.rollback()  # 出错时回滚事务
                        logger.error(f"导入失败: {str(e)}, 数据: {item.get('name', '未知')}")
                        continue
                
                logger.info(f"健身动作基本信息导入完成，成功导入 {success_count} 条数据")
            
            # 导入健身动作详情
            if detail_file and os.path.exists(detail_file):
                logger.info(f"开始从 {detail_file} 导入健身动作详情...")
                
                # 读取JSON文件
                data = []
                with open(detail_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line:  # 跳过空行
                            try:
                                item = json.loads(line)
                                
                                # 处理视频文件路径中的非法字符
                                if 'video_file' in item and item['video_file']:
                                    clean_name = process_file_path(item['video_file'], base_dir_exercises)
                                    item['video_file'] = clean_name
                                
                                data.append(item)
                            except json.JSONDecodeError as e:
                                logger.warning(f"解析JSON行时出错: {line[:50]}..., 错误: {str(e)}")
                                continue
                
                logger.info(f"从文件读取到 {len(data)} 条健身动作详情数据")
                
                # 导入数据
                success_count = 0
                for item in data:
                    try:
                        self.cursor.execute(
                            "SELECT import_exercise_detail_from_json(%s)",
                            (Json(item),)
                        )
                        detail_id = self.cursor.fetchone()[0]
                        self.conn.commit()  # 每条记录单独提交
                        success_count += 1
                        if success_count % 100 == 0:
                            logger.info(f"已导入 {success_count} 条详情数据...")
                    except Exception as e:
                        self.conn.rollback()  # 出错时回滚事务
                        logger.error(f"导入详情失败: {str(e)}, 动作ID: {item.get('id', '未知')}")
                        continue
                
                logger.info(f"健身动作详情导入完成，成功导入 {success_count} 条数据")
        
        except Exception as e:
            self.conn.rollback()
            logger.error(f"导入过程中发生错误: {str(e)}")
            raise
    
    def test_connection(self):
        """测试数据库连接"""
        try:
            self.cursor.execute("SELECT 1")
            result = self.cursor.fetchone()
            if result[0] == 1:
                logger.info("数据库连接正常")
                return True
            return False
        except Exception as e:
            logger.error(f"数据库连接测试失败: {str(e)}")
            return False


def main():
    """主函数，处理命令行参数并执行导入"""
    import argparse
    
    parser = argparse.ArgumentParser(description='导入健身动作数据到数据库')
    parser.add_argument('--host', default=None, help='数据库主机')
    parser.add_argument('--port', default=None, help='数据库端口')
    parser.add_argument('--dbname', default=None, help='数据库名称')
    parser.add_argument('--user', default=None, help='数据库用户名')
    parser.add_argument('--password', default=None, help='数据库密码')
    parser.add_argument('--exercise-file', help='健身动作基本信息JSON文件路径')
    parser.add_argument('--detail-file', help='健身动作详情JSON文件路径')
    parser.add_argument('--exercises-dir', help='健身动作资源文件根目录', default=EXERCISES_DIR)
    parser.add_argument('--muscles-dir', help='肌肉图片资源文件根目录', default=MUSCLES_DIR)
    
    args = parser.parse_args()
    
    # 设置资源路径变量
    exercises_dir = args.exercises_dir
    muscles_dir = args.muscles_dir
    
    # 默认文件路径
    exercise_file = args.exercise_file or "app/data/missing_exercise_simple.json"
    detail_file = args.detail_file or "app/data/missing_exercise_detail.json"
    
    # 创建导入器
    try:
        importer = ExerciseDataImporter(
            dbname=args.dbname,
            user=args.user,
            password=args.password,
            host=args.host,
            port=args.port
        )
        
        # 测试连接
        if not importer.test_connection():
            logger.error("无法连接到数据库，请检查连接参数")
            return
        
        # 导入数据，传入资源路径
        importer.import_exercise_from_json_file(
            exercise_file, 
            detail_file,
            base_dir_exercises=exercises_dir,
            base_dir_muscles=muscles_dir
        )
        logger.info("数据导入完成")
    
    except Exception as e:
        logger.error(f"导入过程中发生错误: {str(e)}")
    
    finally:
        if 'importer' in locals():
            importer.close()


if __name__ == "__main__":
    main() 