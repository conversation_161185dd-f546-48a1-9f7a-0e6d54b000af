DROP TABLE IF EXISTS public.vitamin_rni;
CREATE TABLE public.vitamin_rni (
id                SERIAL       PRIMARY KEY,
age_start_years   NUMERIC(5,2) NOT NULL,    -- 起始年龄（年）
sex               VARCHAR(6)   NOT NULL,    -- 'male','female','both'
rec_type          VARCHAR(3)   NOT NULL,    -- 'AI' 或 'RNI'

vitamin_a         NUMERIC,                 -- μg
vitamin_a_unit    CHAR(10)     NOT NULL,

vitamin_d         NUMERIC,                 -- μg
vitamin_d_unit    CHAR(10)     NOT NULL,

vitamin_e         NUMERIC,                 -- mg
vitamin_e_unit    CHAR(10)     NOT NULL,

thiamine          NUMERIC,                 -- mg
thiamine_unit     CHAR(10)     NOT NULL,

lactoflavin       NUMERIC,                 -- mg
lactoflavin_unit  CHAR(10)     NOT NULL,

vitamin_b6        NUMERIC,                 -- mg
vitamin_b6_unit   CHAR(10)     NOT NULL,

vitamin_b12       NUMERIC,                 -- μg
vitamin_b12_unit  CHAR(10)     NOT NULL,

vitamin_c         NUMERIC,                 -- mg
vitamin_c_unit    CHAR(10)     NOT NULL,

niacin            NUMERIC,                 -- mg
niacin_unit       CHAR(10)     NOT NULL,

folacin           NUMERIC,                 -- μg
folacin_unit      CHAR(10)     NOT NULL,

pantothenic       NUMERIC,                 -- mg
pantothenic_unit  CHAR(10)     NOT NULL,

biotin            NUMERIC,                 -- μg
biotin_unit       CHAR(10)     NOT NULL,

choline           NUMERIC,                 -- mg
choline_unit      CHAR(10)     NOT NULL
);

-- 2. 批量插入 24 行数据（单元格单位已分别指定并简化）
INSERT INTO public.vitamin_rni (
age_start_years, sex, rec_type,
vitamin_a, vitamin_a_unit,
vitamin_d, vitamin_d_unit,
vitamin_e, vitamin_e_unit,
thiamine, thiamine_unit,
lactoflavin, lactoflavin_unit,
vitamin_b6, vitamin_b6_unit,
vitamin_b12, vitamin_b12_unit,
vitamin_c, vitamin_c_unit,
niacin, niacin_unit,
folacin, folacin_unit,
pantothenic, pantothenic_unit,
biotin, biotin_unit,
choline, choline_unit
) VALUES
(0.00, 'both',   'AI', 300, 'μg', 10, 'μg',  3, 'mg', 0.1, 'mg',  0.4, 'mg',  1, 'mg',   0.1, 'μg',   65, 'mg',  0.3, 'mg',  1.7, 'μg', 5, 'mg',   120, 'μg', 40, 'mg'),
(0.50, 'both',   'AI', 350, 'μg', 10, 'μg',  4, 'mg', 0.3, 'mg',  0.6, 'mg',  2, 'mg',   0.3, 'μg',  100, 'mg',  0.6, 'mg',  1.9, 'μg',10, 'mg',   140, 'μg', 40, 'mg'),
(1.00, 'male',   'RNI',340, 'μg', 10, 'μg',  6, 'mg', 0.6, 'mg',  0.7, 'mg',  5, 'mg',   0.6, 'μg',  160, 'mg',  1.0, 'mg',  2.1, 'μg',17, 'mg',   170, 'μg', 40, 'mg'),
(1.00, 'female', 'RNI',330, 'μg', 10, 'μg',  6, 'mg', 0.6, 'mg',  0.7, 'mg',  5, 'mg',   0.6, 'μg',  160, 'mg',  1.0, 'mg',  2.1, 'μg',17, 'mg',   170, 'μg', 40, 'mg'),
(4.00, 'male',   'RNI',390, 'μg', 10, 'μg',  7, 'mg', 0.9, 'mg',  0.9, 'mg',  7, 'mg',   0.7, 'μg',  190, 'mg',  1.2, 'mg',  2.5, 'μg',20, 'mg',   200, 'μg', 50, 'mg'),
(4.00, 'female', 'RNI',380, 'μg', 10, 'μg',  7, 'mg', 0.9, 'mg',  0.9, 'mg',  7, 'mg',   0.7, 'μg',  190, 'mg',  1.2, 'mg',  2.5, 'μg',20, 'mg',   200, 'μg', 50, 'mg'),
(7.00, 'male',   'RNI',430, 'μg', 10, 'μg',  9, 'mg', 1.0, 'mg',  0.9, 'mg',  9, 'mg',   0.8, 'μg',  240, 'mg',  1.4, 'mg',  3.1, 'μg',25, 'mg',   250, 'μg', 60, 'mg'),
(7.00, 'female', 'RNI',390, 'μg', 10, 'μg',  9, 'mg', 1.0, 'mg',  0.9, 'mg',  9, 'mg',   0.8, 'μg',  240, 'mg',  1.4, 'mg',  3.1, 'μg',25, 'mg',   250, 'μg', 60, 'mg'),
(9.00, 'male',   'RNI',560, 'μg', 10, 'μg', 11, 'mg', 1.1, 'mg',  1.0, 'mg', 10, 'mg',   1.0, 'μg',  290, 'mg',  1.8, 'mg',  3.8, 'μg',30, 'mg',   300, 'μg', 75, 'mg'),
(9.00, 'female', 'RNI',540, 'μg', 10, 'μg', 11, 'mg', 1.1, 'mg',  1.0, 'mg', 10, 'mg',   1.0, 'μg',  290, 'mg',  1.8, 'mg',  3.8, 'μg',30, 'mg',   300, 'μg', 75, 'mg'),
(12.00,'male',   'RNI',780, 'μg', 10, 'μg', 13, 'mg', 1.4, 'mg',  1.2, 'mg', 13, 'mg',   1.3, 'μg',  370, 'mg',  2.0, 'mg',  4.9, 'μg',35, 'mg',   380, 'μg', 95, 'mg'),
(12.00,'female', 'RNI',730, 'μg', 10, 'μg', 13, 'mg', 1.4, 'mg',  1.2, 'mg', 13, 'mg',   1.3, 'μg',  370, 'mg',  2.0, 'mg',  4.9, 'μg',35, 'mg',   380, 'μg', 95, 'mg'),
(15.00,'male',   'RNI',810, 'μg', 10, 'μg', 14, 'mg', 1.6, 'mg',  1.3, 'mg', 15, 'mg',   1.4, 'μg',  400, 'mg',  2.5, 'mg',  5.0, 'μg',40, 'mg',   450, 'μg',100,'mg'),
(15.00,'female', 'RNI',670, 'μg', 10, 'μg', 14, 'mg', 1.6, 'mg',  1.3, 'mg', 15, 'mg',   1.4, 'μg',  400, 'mg',  2.5, 'mg',  5.0, 'μg',40, 'mg',   450, 'μg',100,'mg'),
(18.00,'male',   'RNI',770, 'μg', 10, 'μg', 14, 'mg', 1.4, 'mg',  1.2, 'mg', 15, 'mg',   1.4, 'μg',  400, 'mg',  2.4, 'mg',  5.0, 'μg',40, 'mg',   450, 'μg',100,'mg'),
(18.00,'female', 'RNI',660, 'μg', 10, 'μg', 14, 'mg', 1.4, 'mg',  1.2, 'mg', 15, 'mg',   1.4, 'μg',  400, 'mg',  2.4, 'mg',  5.0, 'μg',40, 'mg',   450, 'μg',100,'mg'),
(30.00,'male',   'RNI',770, 'μg', 10, 'μg', 14, 'mg', 1.4, 'mg',  1.2, 'mg', 15, 'mg',   1.4, 'μg',  400, 'mg',  2.4, 'mg',  5.0, 'μg',40, 'mg',   450, 'μg',100,'mg'),
(30.00,'female','RNI',660,'μg', 10,'μg',14,'mg',1.4,'mg',1.2,'mg',15,'mg',1.4,'μg',400,'mg',2.4,'mg',5.0,'μg',40,'mg',450,'μg',100,'mg'),
(50.00,'male',  'RNI',750,'μg', 10,'μg',14,'mg',1.4,'mg',1.2,'mg',15,'mg',1.6,'μg',400,'mg',2.4,'mg',5.0,'μg',40,'mg',450,'μg',100,'mg'),
(50.00,'female','RNI',660,'μg', 10,'μg',14,'mg',1.4,'mg',1.2,'mg',15,'mg',1.6,'μg',400,'mg',2.4,'mg',5.0,'μg',40,'mg',450,'μg',100,'mg'),
(65.00,'male',  'RNI',730,'μg',15,'μg',14,'mg',1.4,'mg',1.2,'mg',15,'mg',1.6,'μg',400,'mg',2.4,'mg',5.0,'μg',40,'mg',450,'μg',100,'mg'),
(65.00,'female','RNI',640,'μg',15,'μg',14,'mg',1.4,'mg',1.2,'mg',15,'mg',1.6,'μg',400,'mg',2.4,'mg',5.0,'μg',40,'mg',450,'μg',100,'mg'),
(75.00,'male',  'RNI',710,'μg',15,'μg',14,'mg',1.4,'mg',1.2,'mg',15,'mg',1.6,'μg',400,'mg',2.4,'mg',5.0,'μg',40,'mg',450,'μg',100,'mg'),
(75.00,'female','RNI',600,'μg',15,'μg',14,'mg',1.4,'mg',1.2,'mg',15,'mg',1.6,'μg',400,'mg',2.4,'mg',5.0,'μg',40,'mg',450,'μg',100,'mg');



DROP TABLE IF EXISTS public.vitamin_pregnancy_inc;
CREATE TABLE public.vitamin_pregnancy_inc (
  id               SERIAL       PRIMARY KEY,
  stage            VARCHAR(16)  NOT NULL,    -- 'early','mid','late','lactation'
  vitamin_d_inc    NUMERIC,               -- μg
  thiamine_inc     NUMERIC,               -- mg
  lactoflavin_inc  NUMERIC,               -- mg
  notes            TEXT,                  -- 其他说明
  unit             CHAR(10)    NOT NULL     -- 保持 vitamin_d_inc 的单位：'μg'
);

-- 4. 插入 4 行增量数据，并为 unit 填充 'μg'
INSERT INTO public.vitamin_pregnancy_inc (
  stage, vitamin_d_inc, thiamine_inc, lactoflavin_inc, notes, unit
) VALUES
  ('early',      0,    0,    0,  '其他维生素与成年女性相同', 'μg'),
  ('mid',       70,    0,    0,  '维生素D需求显著增加',       'μg'),
  ('late',      70,    0,    0,  '维生素D需求保持高位',       'μg'),
  ('lactation',600,    3,    5,  '维生素B1、B2需求增加',    'μg');