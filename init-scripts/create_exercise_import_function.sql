-- 创建导入健身动作基本信息的函数
CREATE OR REPLACE FUNCTION import_exercise_from_json(json_data JSONB) 
RETURNS INTEGER AS $$
DECLARE
    exercise_id INTEGER;
    v_exercise_name VARCHAR;
    v_image_name VARCHAR;
    v_gif_url VARCHAR;
BEGIN
    -- 处理健身动作名称
    IF json_data->>'name' IS NOT NULL THEN
        v_exercise_name := json_data->>'name';
    ELSIF json_data->'exercise'->>'name' IS NOT NULL THEN
        v_exercise_name := json_data->'exercise'->>'name';
    ELSE
        v_exercise_name := '未命名动作';
    END IF;
    
    -- 处理图片路径
    IF json_data->>'image_name' IS NOT NULL THEN
        v_image_name := json_data->>'image_name';
    ELSIF json_data->'exercise'->>'image_name' IS NOT NULL THEN
        v_image_name := json_data->'exercise'->>'image_name';
    ELSE
        v_image_name := NULL;
    END IF;
    
    -- 处理GIF路径
    IF json_data->>'gif_url' IS NOT NULL THEN
        v_gif_url := json_data->>'gif_url';
    ELSIF json_data->'exercise'->>'gif_url' IS NOT NULL THEN
        v_gif_url := json_data->'exercise'->>'gif_url';
    ELSE
        v_gif_url := NULL;
    END IF;

    -- 插入或更新健身动作基本信息
    INSERT INTO exercises (
        id, name, en_name, body_part_id, equipment_id, 
        image_name, gif_url, description, level, 
        sort_priority, user_id, exercise_type, hit_time
    ) VALUES (
        COALESCE((json_data->>'id')::INTEGER, nextval('exercises_id_seq')),
        v_exercise_name,
        json_data->>'en_name',
        CASE 
            WHEN json_data->'body_part_id' IS NULL OR json_data->'body_part_id' = 'null' THEN '{}'::INTEGER[] 
            ELSE (SELECT array_agg(value::INTEGER) FROM jsonb_array_elements_text(json_data->'body_part_id'))
        END,
        CASE 
            WHEN json_data->'equipment_id' IS NULL OR json_data->'equipment_id' = 'null' THEN '{}'::INTEGER[] 
            ELSE (SELECT array_agg(value::INTEGER) FROM jsonb_array_elements_text(json_data->'equipment_id'))
        END,
        v_image_name,
        v_gif_url,
        json_data->>'description',
        COALESCE((json_data->>'level')::SMALLINT, 1),
        COALESCE((json_data->>'sortPriority')::INTEGER, (json_data->>'sort_priority')::INTEGER, 0),
        COALESCE(json_data->>'user_id', 'ScienceFit'),
        COALESCE(json_data->>'exercise_type', 'weight_reps'),
        0
    )
    ON CONFLICT (id) DO UPDATE SET
        name = EXCLUDED.name,
        en_name = EXCLUDED.en_name,
        body_part_id = EXCLUDED.body_part_id,
        equipment_id = EXCLUDED.equipment_id,
        image_name = EXCLUDED.image_name,
        gif_url = EXCLUDED.gif_url,
        description = EXCLUDED.description,
        level = EXCLUDED.level,
        sort_priority = EXCLUDED.sort_priority,
        user_id = EXCLUDED.user_id,
        exercise_type = EXCLUDED.exercise_type
    RETURNING id INTO exercise_id;
    
    RETURN exercise_id;
END;
$$ LANGUAGE plpgsql;

-- 创建导入健身动作详情的函数
CREATE OR REPLACE FUNCTION import_exercise_detail_from_json(json_data JSONB) 
RETURNS INTEGER AS $$
DECLARE
    detail_id INTEGER;
    v_exercise_id INTEGER;
    v_video_file VARCHAR;
BEGIN
    -- 获取exercise_id
    IF json_data->>'exercise_id' IS NOT NULL THEN
        v_exercise_id := (json_data->>'exercise_id')::INTEGER;
    ELSIF json_data->>'id' IS NOT NULL THEN
        v_exercise_id := (json_data->>'id')::INTEGER;
    ELSE
        RAISE EXCEPTION '缺少exercise_id或id字段';
    END IF;
    
    -- 处理视频文件路径
    IF json_data->>'video_file' IS NOT NULL THEN
        v_video_file := json_data->>'video_file';
    ELSE
        v_video_file := NULL;
    END IF;

    -- 插入或更新健身动作详情
    INSERT INTO exercise_details (
        id, exercise_id, target_muscles_id, synergist_muscles_id,
        ex_instructions, exercise_tips, video_file, is_public
    ) VALUES (
        COALESCE((json_data->>'detail_id')::INTEGER, nextval('exercise_details_id_seq')),
        v_exercise_id,
        CASE 
            WHEN json_data->'target_muscles_id' IS NULL OR json_data->'target_muscles_id' = 'null' THEN '{}'::INTEGER[] 
            ELSE (SELECT array_agg(value::INTEGER) FROM jsonb_array_elements_text(json_data->'target_muscles_id'))
        END,
        CASE 
            WHEN json_data->'synergist_muscles_id' IS NULL OR json_data->'synergist_muscles_id' = 'null' THEN '{}'::INTEGER[] 
            ELSE (SELECT array_agg(value::INTEGER) FROM jsonb_array_elements_text(json_data->'synergist_muscles_id'))
        END,
        CASE 
            WHEN json_data->'ex_instructions' IS NULL OR json_data->'ex_instructions' = 'null' THEN '{}'::TEXT[] 
            ELSE (SELECT array_agg(value::TEXT) FROM jsonb_array_elements_text(json_data->'ex_instructions'))
        END,
        CASE 
            WHEN json_data->'exercise_tips' IS NULL OR json_data->'exercise_tips' = 'null' THEN '{}'::TEXT[] 
            ELSE (SELECT array_agg(value::TEXT) FROM jsonb_array_elements_text(json_data->'exercise_tips'))
        END,
        v_video_file,
        COALESCE((json_data->>'is_public')::BOOLEAN, TRUE)
    )
    ON CONFLICT (id) DO UPDATE SET
        exercise_id = EXCLUDED.exercise_id,
        target_muscles_id = EXCLUDED.target_muscles_id,
        synergist_muscles_id = EXCLUDED.synergist_muscles_id,
        ex_instructions = EXCLUDED.ex_instructions,
        exercise_tips = EXCLUDED.exercise_tips,
        video_file = EXCLUDED.video_file,
        is_public = EXCLUDED.is_public
    RETURNING id INTO detail_id;
    
    RETURN detail_id;
END;
$$ LANGUAGE plpgsql; 