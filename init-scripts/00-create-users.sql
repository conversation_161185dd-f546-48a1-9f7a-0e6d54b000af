-- 创建自定义类型 (如果需要且不存在)
-- 注意: Alembic 通常会处理这些，但在初始化脚本中包含可能是安全的
-- DO $$ BEGIN
--     CREATE TYPE gender AS ENUM ('UNKNOWN', 'MALE', 'FEMALE');
-- EXCEPTION
--     W<PERSON><PERSON> duplicate_object THEN null;
-- END $$;
-- DO $$ BEGIN
--     CREATE TYPE experiencelevel AS ENUM ('BEGINNER', 'INTERMEDIATE', 'ADVANCED');
-- EXCEPTION
--     WHEN duplicate_object THEN null;
-- END $$;
-- DO $$ BEGIN
--     CREATE TYPE fitnessgoal AS ENUM ('LOSE_WEIGHT', 'MAINTAIN', 'GAIN_MUSCLE');
-- EXCEPTION
--     WHEN duplicate_object THEN null;
-- END $$;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    email VARCHAR UNIQUE,
    phone VARCHAR UNIQUE,
    hashed_password VARCHAR,
    openid VARCHAR UNIQUE NOT NULL, -- 模型中 non-nullable
    unionid VARCHAR UNIQUE,
    session_key VARCHAR,
    nickname VARCHAR,
    avatar_url VARCHAR,
    country VARCHAR,
    province VARCHAR,
    city VARCHAR,
    gender INTEGER, -- 对应模型中的 Integer, 实际可能用自定义类型更好
    birthday TIMESTAMP WITHOUT TIME ZONE, -- 模型中是 DateTime, 无 timezone
    age INTEGER,
    height FLOAT,
    weight FLOAT,
    activity_level INTEGER DEFAULT 3,
    body_type VARCHAR,
    experience_level INTEGER, -- 对应模型中的 Integer
    fitness_goal INTEGER, -- 对应模型中的 Integer
    language VARCHAR, -- 新增
    health_conditions TEXT[], -- 使用 TEXT[] 更通用
    allergies TEXT[], -- 使用 TEXT[] 更通用
    bmi FLOAT,
    tedd INTEGER,
    completed BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    is_superuser BOOLEAN DEFAULT FALSE,
    last_login TIMESTAMP WITH TIME ZONE, -- 新增
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), -- 使用 now()
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() -- 使用 now()
);

-- 添加索引 (如果不存在)
CREATE INDEX IF NOT EXISTS ix_users_id ON users(id);
CREATE INDEX IF NOT EXISTS ix_users_email ON users(email);
CREATE INDEX IF NOT EXISTS ix_users_phone ON users(phone);
CREATE INDEX IF NOT EXISTS ix_users_openid ON users(openid);
CREATE INDEX IF NOT EXISTS ix_users_unionid ON users(unionid);


-- 创建用户设置表
CREATE TABLE IF NOT EXISTS user_settings (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL UNIQUE REFERENCES users(id) ON DELETE CASCADE,
    notification_enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(), -- 使用 now()
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() -- 使用 now()
);

-- 添加索引 (如果不存在)
CREATE INDEX IF NOT EXISTS ix_user_settings_id ON user_settings(id);


-- 创建用户统计表 (新增)
CREATE TABLE IF NOT EXISTS user_stats (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL UNIQUE REFERENCES users(id) ON DELETE CASCADE,
    total_workouts INTEGER DEFAULT 0,
    total_minutes INTEGER DEFAULT 0,
    total_calories FLOAT DEFAULT 0.0,
    total_distance FLOAT DEFAULT 0.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- 添加索引 (如果不存在)
CREATE INDEX IF NOT EXISTS ix_user_stats_id ON user_stats(id); 