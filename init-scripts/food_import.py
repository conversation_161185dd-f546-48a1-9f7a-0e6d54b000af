# app/scripts/import_food_data.py
import os
import json
import psycopg2
from psycopg2.extras import Json
import logging
from pathlib import Path
import sys
import csv

# 将项目根目录添加到sys.path
ROOT_DIR = Path(__file__).resolve().parents[2]
sys.path.append(str(ROOT_DIR))

# 导入项目配置
from app.core.config import settings

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 数据目录 - 从环境变量获取，默认为/data/clean_json
DATA_DIR = os.environ.get('DATA_DIR', '/data/overall_clean_json')

# 需要从图片URL中移除的前缀
IMAGE_URL_PREFIX = "cloud://sciencefit-3grq7tkic3524425.7363-sciencefit-3grq7tkic3524425-1328011169/sciencefit"

# 修改为docker容器中的PostgreSQL连接信息
DB_INFO = {
    'dbname': 'fitness_db',
    'user': 'postgres',
    'password': 'postgres',
    'host': 'db',  # Docker容器名称
    'port': '5432'
}

# 如果不是在Docker环境中运行，则使用.env中的配置
if os.environ.get('RUN_ENV') != 'docker':
    # 从配置文件获取数据库连接信息
    DATABASE_URL = settings.get_database_url

    # 解析数据库URL
    def parse_db_url(url):
        # 格式: postgresql://username:password@host:port/dbname
        if not url.startswith('postgresql://'):
            raise ValueError(f"不支持的数据库URL格式: {url}")
        
        auth_host, dbname = url.replace('postgresql://', '').split('/', 1)
        auth, host = auth_host.split('@', 1)
        
        if ':' in auth:
            user, password = auth.split(':', 1)
        else:
            user, password = auth, ''
            
        if ':' in host:
            host, port = host.split(':', 1)
        else:
            host, port = host, '5432'
            
        return {
            'dbname': dbname,
            'user': user,
            'password': password,
            'host': host,
            'port': port
        }

    # 解析数据库连接信息
    DB_INFO = parse_db_url(DATABASE_URL)

def process_image_url(url):
    """处理图片URL，移除指定前缀"""
    if url and isinstance(url, str) and url.startswith(IMAGE_URL_PREFIX):
        return url.replace(IMAGE_URL_PREFIX, "")
    return url

def create_import_function(conn):
    """在数据库中创建导入函数"""
    cursor = conn.cursor()
    
    # 检查函数是否已存在
    cursor.execute("SELECT EXISTS(SELECT 1 FROM pg_proc WHERE proname = 'import_food_from_json');")
    function_exists = cursor.fetchone()[0]
    
    if function_exists:
        logger.info("导入函数已存在，无需重新创建")
        return True
    
    # 读取SQL脚本内容
    sql_file_path = os.path.join(ROOT_DIR, "app/scripts/create_import_function.sql")
    try:
        with open(sql_file_path, 'r') as sql_file:
            sql_script = sql_file.read()
            
        # 执行SQL脚本
        cursor.execute(sql_script)
        conn.commit()
        logger.info("成功创建导入函数")
        return True
    except Exception as e:
        logger.error(f"创建导入函数时出错: {str(e)}")
        conn.rollback()
        return False

def check_food_exists(cursor, food_id):
    """检查食品ID是否已存在于数据库中"""
    cursor.execute("SELECT EXISTS(SELECT 1 FROM foods WHERE id = %s)", (food_id,))
    return cursor.fetchone()[0]

def check_food_code_exists(cursor, food_code):
    """检查食品代码是否已存在于数据库中"""
    cursor.execute("SELECT id FROM foods WHERE code = %s", (food_code,))
    result = cursor.fetchone()
    return result[0] if result else None

def update_food_category(cursor, food_id, category, category_code):
    """更新食品的分类信息"""
    try:
        cursor.execute(
            "UPDATE foods SET category = %s, category_code = %s WHERE id = %s",
            (category, category_code, food_id)
        )
        return True
    except Exception as e:
        logger.error(f"更新食品分类信息时出错: {str(e)}")
        return False

def import_food_data():
    """导入食品数据"""
    conn = None
    try:
        # 获取所有JSON文件
        food_files = list(Path(DATA_DIR).glob("*.json"))
        logger.info(f"找到 {len(food_files)} 个食品数据文件")
        
        # 尝试连接数据库并创建导入函数
        try:
            conn = psycopg2.connect(
                dbname=DB_INFO['dbname'],
                user=DB_INFO['user'],
                password=DB_INFO['password'],
                host=DB_INFO['host'],
                port=DB_INFO['port']
            )
            
            logger.info(f"已连接到数据库: {DB_INFO['host']}:{DB_INFO['port']}/{DB_INFO['dbname']}")
            
            # 创建导入函数
            if not create_import_function(conn):
                logger.error("无法创建导入函数，程序终止")
                return
                
            conn.close()
        except Exception as e:
            logger.error(f"连接数据库或创建函数时出错: {str(e)}")
            return
        
        success_count = 0
        update_count = 0
        error_count = 0
        
        # 逐个处理JSON文件
        for file_path in food_files:
            conn = None
            try:
                # 为每个文件建立新的数据库连接
                conn = psycopg2.connect(
                    dbname=DB_INFO['dbname'],
                    user=DB_INFO['user'],
                    password=DB_INFO['password'],
                    host=DB_INFO['host'],
                    port=DB_INFO['port']
                )
                cursor = conn.cursor()
                
                with open(file_path, 'r', encoding='utf-8') as f:
                    food_data = json.load(f)
                
                # 处理图片URL
                if 'food' in food_data:
                    if 'thumb_image_url' in food_data['food']:
                        food_data['food']['thumb_image_url'] = process_image_url(food_data['food']['thumb_image_url'])
                    if 'large_image_url' in food_data['food']:
                        food_data['food']['large_image_url'] = process_image_url(food_data['food']['large_image_url'])
                
                # 获取食品ID和代码
                food_id = food_data.get('id') or food_data.get('food', {}).get('id')
                food_code = food_data.get('food', {}).get('code')
                
                # 转换categoryCode为整数（如果存在）
                if 'food' in food_data and 'categoryCode' in food_data['food']:
                    try:
                        food_data['food']['category_code'] = int(food_data['food']['categoryCode'])
                    except (ValueError, TypeError):
                        logger.warning(f"无法将categoryCode转换为整数: {food_data['food'].get('categoryCode')}")
                        food_data['food']['category_code'] = None
                
                # 首先检查食品ID是否存在
                exists_by_id = food_id and check_food_exists(cursor, food_id)
                
                # 然后检查食品代码是否存在
                existing_id_by_code = None
                if food_code:
                    existing_id_by_code = check_food_code_exists(cursor, food_code)
                
                # 处理结果
                if exists_by_id:
                    # 如果食品ID存在，仅更新分类信息
                    category = food_data.get('food', {}).get('category')
                    category_code = food_data.get('food', {}).get('category_code')
                    
                    if category is not None and category_code is not None:
                        if update_food_category(cursor, food_id, category, category_code):
                            logger.info(f"已更新食品分类信息: {file_path.stem}, ID: {food_id}")
                            update_count += 1
                            conn.commit()
                        else:
                            conn.rollback()
                            error_count += 1
                elif existing_id_by_code:
                    # 如果食品代码存在但ID不同，也仅更新分类信息
                    category = food_data.get('food', {}).get('category')
                    category_code = food_data.get('food', {}).get('category_code')
                    
                    if category is not None and category_code is not None:
                        if update_food_category(cursor, existing_id_by_code, category, category_code):
                            logger.info(f"已更新食品分类信息(通过代码): {file_path.stem}, CODE: {food_code}, ID: {existing_id_by_code}")
                            update_count += 1
                            conn.commit()
                        else:
                            conn.rollback()
                            error_count += 1
                else:
                    # 食品不存在，导入完整数据
                    try:
                        cursor.execute("SELECT import_food_from_json(%s)", (Json(food_data),))
                        new_food_id = cursor.fetchone()[0]
                        
                        # 提交事务
                        conn.commit()
                        
                        logger.info(f"成功导入食品: {file_path.stem}, ID: {new_food_id}")
                        success_count += 1
                    except psycopg2.errors.UniqueViolation as e:
                        # 捕获唯一约束冲突错误并记录
                        logger.warning(f"导入食品时发生唯一约束冲突: {str(e)}")
                        conn.rollback()
                        error_count += 1
                
            except Exception as e:
                logger.error(f"处理文件 {file_path} 时出错: {str(e)}")
                if conn:
                    conn.rollback()
                error_count += 1
            finally:
                # 关闭连接
                if conn:
                    conn.close()
        
        logger.info(f"导入完成! 新导入: {success_count}, 更新: {update_count}, 失败: {error_count}")
        
    except Exception as e:
        logger.error(f"处理数据目录时出错: {str(e)}")

def import_food_hot_values(csv_file_path):
    """
    从CSV文件导入食物热度数据
    CSV文件格式：id,hot
    """
    conn = None
    try:
        # 连接数据库
        conn = psycopg2.connect(
            dbname=DB_INFO['dbname'],
            user=DB_INFO['user'],
            password=DB_INFO['password'],
            host=DB_INFO['host'],
            port=DB_INFO['port']
        )
        cursor = conn.cursor()
        
        logger.info(f"已连接到数据库: {DB_INFO['host']}:{DB_INFO['port']}/{DB_INFO['dbname']}")
        
        # 读取CSV文件
        with open(csv_file_path, 'r', encoding='utf-8') as csv_file:
            csv_reader = csv.DictReader(csv_file)
            
            success_count = 0
            error_count = 0
            
            for row in csv_reader:
                try:
                    food_id = int(row['id'])
                    hot_value = int(row['hot'])
                    
                    # 更新食品热度
                    cursor.execute(
                        "UPDATE foods SET hot = %s WHERE id = %s",
                        (hot_value, food_id)
                    )
                    
                    if cursor.rowcount > 0:
                        success_count += 1
                    else:
                        logger.warning(f"未找到ID为{food_id}的食品")
                        error_count += 1
                        
                except Exception as e:
                    logger.error(f"处理ID为{row.get('id')}的食品时出错: {str(e)}")
                    error_count += 1
            
            # 提交事务
            conn.commit()
            logger.info(f"热度值导入完成! 成功: {success_count}, 失败: {error_count}")
            
    except Exception as e:
        logger.error(f"导入热度值时出错: {str(e)}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

def import_all_hot_values_from_directory(directory_path):
    """
    导入目录下所有CSV文件中的热度值
    """
    if not os.path.exists(directory_path):
        logger.error(f"目录{directory_path}不存在")
        return
    
    csv_files = [f for f in os.listdir(directory_path) if f.endswith('.csv')]
    logger.info(f"在{directory_path}目录下找到{len(csv_files)}个CSV文件")
    
    for csv_file in csv_files:
        file_path = os.path.join(directory_path, csv_file)
        logger.info(f"开始处理文件: {file_path}")
        import_food_hot_values(file_path)

if __name__ == "__main__":
    import_food_data()
    
    # 导入热度值，可以指定CSV文件目录
    hot_values_dir = os.environ.get('HOT_VALUES_DIR', '/home/<USER>/backend/app/utils/')
    import_all_hot_values_from_directory(hot_values_dir)