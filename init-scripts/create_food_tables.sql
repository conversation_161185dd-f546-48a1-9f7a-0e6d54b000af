-- 食品基本信息表
CREATE TABLE foods (
    id SERIAL PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(128) NOT NULL,
    code VARCHAR(64) UNIQUE NOT NULL,
    category VARCHAR(64),
    food_type VARCHAR(32),
    goods_id INTEGER,
    thumb_image_url TEXT,
    large_image_url TEXT,
    is_liquid BOOLEAN DEFAULT FALSE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    can_revise BOOLEAN DEFAULT FALSE
);

CREATE INDEX idx_foods_name ON foods(name);
CREATE INDEX idx_foods_code ON foods(code);
CREATE INDEX idx_foods_category ON foods(category);
CREATE INDEX idx_foods_food_type ON foods(food_type);

-- 食品营养概况表
CREATE TABLE nutritional_profiles (
    id SERIAL PRIMARY KEY,
    food_id INTEGER UNIQUE REFERENCES foods(id) ON DELETE CASCADE,
    health_light INTEGER,
    lights JSONB,
    warnings JSONB,
    warning_scenes JSONB,
    calory FLOAT,
    protein_fraction FLOAT,
    fat_fraction FLOAT,
    carb_fraction FLOAT,
    food_rank INTEGER,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 食品营养素明细表
CREATE TABLE food_nutrient_values (
    id SERIAL PRIMARY KEY,
    food_id INTEGER REFERENCES foods(id) ON DELETE CASCADE,
    name_en VARCHAR(64) NOT NULL,
    name_cn VARCHAR(64) NOT NULL,
    value FLOAT,
    unit VARCHAR(16),
    unit_name VARCHAR(16),
    precision INTEGER,
    nrv FLOAT,
    category VARCHAR(16) NOT NULL
);

CREATE INDEX idx_food_nutrient_values_food_id ON food_nutrient_values(food_id);
CREATE INDEX idx_food_nutrient_values_category ON food_nutrient_values(category);

-- 食品计量单位表
CREATE TABLE food_units (
    id SERIAL PRIMARY KEY,
    food_id INTEGER REFERENCES foods(id) ON DELETE CASCADE,
    unit_name VARCHAR(32) NOT NULL,
    weight FLOAT NOT NULL,
    eat_weight FLOAT NOT NULL,
    is_default BOOLEAN DEFAULT FALSE
);

CREATE INDEX idx_food_units_food_id ON food_units(food_id);