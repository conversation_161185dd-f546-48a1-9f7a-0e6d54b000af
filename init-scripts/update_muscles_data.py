#!/usr/bin/env python3
import json
import psycopg2
import logging
import argparse

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def update_muscles_data(conn, detail_file):
    """根据动作ID更新肌肉数据
    
    Args:
        conn: 数据库连接
        detail_file: 健身动作详情JSON文件路径
    """
    cursor = conn.cursor()
    updated_count = 0
    
    try:
        # 读取JSON文件
        with open(detail_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line:  # 跳过空行
                    continue
                    
                try:
                    item = json.loads(line)
                    exercise_id = item.get('id')
                    
                    # 如果没有exercise_id，则跳过
                    if not exercise_id:
                        logger.warning(f"跳过没有exercise_id的记录: {line[:50]}...")
                        continue
                    
                    # 识别大小写字段
                    target_muscles = item.get('target_muscles_id', item.get('Target_muscles_id', []))
                    synergist_muscles = item.get('synergist_muscles_id', item.get('Synergist_muscles_id', []))
                    
                    # 更新数据库
                    cursor.execute(
                        """
                        UPDATE exercise_details 
                        SET target_muscles_id = %s, 
                            synergist_muscles_id = %s
                        WHERE exercise_id = %s
                        RETURNING id
                        """,
                        (target_muscles, synergist_muscles, exercise_id)
                    )
                    
                    # 获取结果
                    result = cursor.fetchone()
                    if result:
                        updated_count += 1
                        logger.info(f"已更新ID={exercise_id}的肌肉数据")
                    else:
                        logger.warning(f"未找到ID={exercise_id}的详情记录")
                    
                    # 提交事务
                    conn.commit()
                    
                except json.JSONDecodeError as e:
                    logger.warning(f"解析JSON行时出错: {line[:50]}..., 错误: {str(e)}")
                    continue
                except Exception as e:
                    conn.rollback()
                    logger.error(f"更新ID={exercise_id}时出错: {str(e)}")
                    continue
        
        logger.info(f"完成更新，共更新{updated_count}条记录")
    
    except Exception as e:
        logger.error(f"更新过程中发生错误: {str(e)}")
        raise
    finally:
        cursor.close()

def main():
    parser = argparse.ArgumentParser(description='根据动作ID更新肌肉数据')
    parser.add_argument('--host', default='localhost', help='数据库主机')
    parser.add_argument('--port', default='5432', help='数据库端口')
    parser.add_argument('--dbname', default='fitness_db', help='数据库名称')
    parser.add_argument('--user', default='postgres', help='数据库用户名')
    parser.add_argument('--password', default='!scienceFit0219', help='数据库密码')
    parser.add_argument('--detail-file', required=True, help='健身动作详情JSON文件路径')
    
    args = parser.parse_args()
    
    # 连接数据库
    try:
        conn = psycopg2.connect(
            dbname=args.dbname,
            user=args.user,
            password=args.password,
            host=args.host,
            port=args.port
        )
        
        # 更新数据
        update_muscles_data(conn, args.detail_file)
        
    except Exception as e:
        logger.error(f"操作失败: {str(e)}")
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    main()
    #python update_muscles_data.py --detail-file app/data/exercise_detail.json