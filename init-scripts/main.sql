-- 重建：综合主表 nutrition_reference_main，增加每列对应的 unit 字段
DROP TABLE IF EXISTS public.nutrition_reference_main;
CREATE TABLE public.nutrition_reference_main (
  id                     SERIAL       PRIMARY KEY,
  sex                    VARCHAR(6)   NOT NULL,  -- 'male','female'
  age_start_years        NUMERIC(5,2) NOT NULL,  -- 起始年龄（年）

  -- 脂肪及脂肪酸
  fat                    NUMERIC,               fat_unit            CHAR(10) NOT NULL, -- '%E'
  saturated_fat          NUMERIC,               saturated_fat_unit  CHAR(10) NOT NULL, -- '%E'
  pufa                   NUMERIC,               pufa_unit           CHAR(10) NOT NULL, -- '%E'
  n3fa                   NUMERIC,               n3fa_unit           CHAR(10) NOT NULL, -- '%E'
  la                     NUMERIC,               la_unit             CHAR(10) NOT NULL, -- '%E'
  ala                    NUMERIC,               ala_unit            CHAR(10) NOT NULL, -- '%E'
  epa_dha                NUMERIC,               epa_dha_unit        CHAR(10) NOT NULL, -- 'g'

  -- 碳水化合物
  carbohydrate_ear       NUMERIC,               carbohydrate_ear_unit CHAR(10) NOT NULL, -- 'g'
  carbohydrate           NUMERIC,               carbohydrate_unit     CHAR(10) NOT NULL, -- '%E'
  fiber_dietary          NUMERIC,               fiber_dietary_unit    CHAR(10) NOT NULL, -- 'g'
  fructose               NUMERIC,               fructose_unit         CHAR(10) NOT NULL, -- '%E'

  -- 蛋白质
  protein_ear            NUMERIC,               protein_ear_unit      CHAR(10) NOT NULL, -- 'g'
  protein_rni            NUMERIC,               protein_rni_unit      CHAR(10) NOT NULL, -- 'g'
  protein                NUMERIC,               protein_unit          CHAR(10) NOT NULL  -- '%E'
);

-- 插入：30 列，确保每行都有 30 值
INSERT INTO public.nutrition_reference_main (
  sex, age_start_years,
  fat, fat_unit,
  saturated_fat, saturated_fat_unit,
  pufa, pufa_unit,
  n3fa, n3fa_unit,
  la, la_unit,
  ala, ala_unit,
  epa_dha, epa_dha_unit,
  carbohydrate_ear, carbohydrate_ear_unit,
  carbohydrate, carbohydrate_unit,
  fiber_dietary, fiber_dietary_unit,
  fructose, fructose_unit,
  protein_ear, protein_ear_unit,
  protein_rni, protein_rni_unit,
  protein, protein_unit
) VALUES
  ('male',   0.00, 0.48,'%E', NULL,'%E', NULL,'%E', NULL,'%E', 0.08,'%E', 0.0090,'%E', 0.10,'g',  60,'g', NULL,'%E', NULL,'%E',  NULL,'%E', 9,'g', 9,'g', NULL,'%E'),
  ('female', 0.00, 0.48,'%E', NULL,'%E', NULL,'%E', NULL,'%E', 0.08,'%E', 0.0090,'%E', 0.10,'g',  60,'g', NULL,'%E', NULL,'%E',  NULL,'%E', 9,'g', 9,'g', NULL,'%E'),

  ('male',   0.50, 0.40,'%E', NULL,'%E', NULL,'%E', NULL,'%E', 0.06,'%E', 0.0067,'%E', 0.10,'g',  80,'g', NULL,'%E', NULL,'%E',  NULL,'%E', 17,'g',17,'g', NULL,'%E'),
  ('female', 0.50, 0.40,'%E', NULL,'%E', NULL,'%E', NULL,'%E', 0.06,'%E', 0.0067,'%E', 0.10,'g',  80,'g', NULL,'%E', NULL,'%E',  NULL,'%E', 17,'g',17,'g', NULL,'%E'),

  ('male',   1.00, 0.35,'%E', NULL,'%E', NULL,'%E', NULL,'%E', 0.04,'%E', 0.0060,'%E', 0.10,'g', 120,'g', 0.65,'%E', 10,'g',  NULL,'%E', 20,'g',25,'g', NULL,'%E'),
  ('female', 1.00, 0.35,'%E', NULL,'%E', NULL,'%E', NULL,'%E', 0.04,'%E', 0.0060,'%E', 0.10,'g', 120,'g', 0.65,'%E', 10,'g',  NULL,'%E', 20,'g',25,'g', NULL,'%E'),

  ('male',   4.00, 0.30,'%E', 0.08,'%E', NULL,'%E', NULL,'%E', 0.04,'%E', 0.0060,'%E', 0.20,'g', 120,'g', 0.65,'%E', 15,'g',0.10,'%E', 25,'g',30,'g',0.20,'%E'),
  ('female', 4.00, 0.30,'%E', 0.08,'%E', NULL,'%E', NULL,'%E', 0.04,'%E', 0.0060,'%E', 0.20,'g', 120,'g', 0.65,'%E', 15,'g',0.10,'%E', 25,'g',30,'g',0.20,'%E'),

  ('male',   7.00, 0.30,'%E', 0.08,'%E', NULL,'%E', NULL,'%E', 0.04,'%E', 0.0060,'%E', 0.20,'g', 120,'g', 0.65,'%E', 20,'g',0.10,'%E', 30,'g',40,'g',0.20,'%E'),
  ('female', 7.00, 0.30,'%E', 0.08,'%E', NULL,'%E', NULL,'%E', 0.04,'%E', 0.0060,'%E', 0.20,'g', 120,'g', 0.65,'%E', 20,'g',0.10,'%E', 30,'g',40,'g',0.20,'%E'),

  ('male',   9.00, 0.30,'%E', 0.08,'%E', NULL,'%E', NULL,'%E', 0.04,'%E', 0.0060,'%E', 0.20,'g', 120,'g', 0.65,'%E', 20,'g',0.10,'%E', 40,'g',45,'g',0.20,'%E'),
  ('female', 9.00, 0.30,'%E', 0.08,'%E', NULL,'%E', NULL,'%E', 0.04,'%E', 0.0060,'%E', 0.20,'g', 120,'g', 0.65,'%E', 20,'g',0.10,'%E', 40,'g',45,'g',0.20,'%E'),

  ('male',   12.00,0.30,'%E', 0.08,'%E', NULL,'%E', NULL,'%E', 0.04,'%E', 0.0060,'%E', 0.25,'g', 150,'g', 0.65,'%E', 25,'g',0.10,'%E', 55,'g',70,'g',0.20,'%E'),
  ('female', 12.00,0.30,'%E', 0.08,'%E', NULL,'%E', NULL,'%E', 0.04,'%E', 0.0060,'%E', 0.25,'g', 150,'g', 0.65,'%E', 25,'g',0.10,'%E', 50,'g',60,'g',0.20,'%E'),

  ('male',   15.00,0.30,'%E', 0.08,'%E', NULL,'%E', NULL,'%E', 0.04,'%E', 0.0060,'%E', 0.25,'g', 150,'g', 0.65,'%E', 30,'g',0.10,'%E', 60,'g',75,'g',0.20,'%E'),
  ('female', 15.00,0.30,'%E', 0.08,'%E', NULL,'%E', NULL,'%E', 0.04,'%E', 0.0060,'%E', 0.25,'g', 150,'g', 0.65,'%E', 30,'g',0.10,'%E', 50,'g',60,'g',0.20,'%E'),

  ('male',   18.00,0.30,'%E', 0.10,'%E', 0.09,'%E', 0.02,'%E', 0.04,'%E', 0.0060,'%E', 2.00,'g', 120,'g', 0.65,'%E', 30,'g',0.10,'%E', 60,'g',65,'g',0.20,'%E'),
  ('female', 18.00,0.30,'%E', 0.10,'%E', 0.09,'%E', 0.02,'%E', 0.04,'%E', 0.0060,'%E', 2.00,'g', 120,'g', 0.65,'%E', 30,'g',0.10,'%E', 50,'g',55,'g',0.20,'%E'),

  ('male',   30.00,0.30,'%E', 0.10,'%E', 0.09,'%E', 0.02,'%E', 0.04,'%E', 0.0060,'%E', 2.00,'g', 120,'g', 0.65,'%E', 30,'g',0.10,'%E', 60,'g',65,'g',0.20,'%E'),
  ('female', 30.00,0.30,'%E', 0.10,'%E', 0.09,'%E', 0.02,'%E', 0.04,'%E', 0.0060,'%E', 2.00,'g', 120,'g', 0.65,'%E', 30,'g',0.10,'%E', 50,'g',55,'g',0.20,'%E'),

  ('male',   50.00,0.30,'%E', 0.10,'%E', 0.09,'%E', 0.02,'%E', 0.04,'%E', 0.0060,'%E', 2.00,'g', 120,'g', 0.65,'%E', 30,'g',0.10,'%E', 60,'g',65,'g',0.20,'%E'),
  ('female', 50.00,0.30,'%E', 0.10,'%E', 0.09,'%E', 0.02,'%E', 0.04,'%E', 0.0060,'%E', 2.00,'g', 120,'g', 0.65,'%E', 30,'g',0.10,'%E', 50,'g',55,'g',0.20,'%E'),

  ('male',   65.00,0.30,'%E', 0.10,'%E', 0.09,'%E', 0.02,'%E', 0.04,'%E', 0.0060,'%E', 2.00,'g', 120,'g', 0.65,'%E', 30,'g',0.10,'%E', 60,'g',72,'g',0.20,'%E'),
  ('female', 65.00,0.30,'%E', 0.10,'%E', 0.09,'%E', 0.02,'%E', 0.04,'%E', 0.0060,'%E', 2.00,'g', 120,'g', 0.65,'%E', 30,'g',0.10,'%E', 50,'g',62,'g',0.20,'%E'),

  ('male',   75.00,0.30,'%E', 0.10,'%E', 0.09,'%E', 0.02,'%E', 0.04,'%E', 0.0060,'%E', 2.00,'g', 120,'g', 0.65,'%E', 30,'g',0.10,'%E', 60,'g',72,'g',0.20,'%E'),
  ('female', 75.00,0.30,'%E', 0.10,'%E', 0.09,'%E', 0.02,'%E', 0.04,'%E', 0.0060,'%E', 2.00,'g', 120,'g', 0.65,'%E', 30,'g',0.10,'%E', 50,'g',62,'g',0.20,'%E');


-- 3. 重建孕期/哺乳期增量表 nutrition_pregnancy_inc，增加 unit 字段
DROP TABLE IF EXISTS public.nutrition_pregnancy_inc;
CREATE TABLE public.nutrition_pregnancy_inc (
  id                    SERIAL      PRIMARY KEY,
  stage                 VARCHAR(16) NOT NULL,   -- 'early','mid','late','lactation'
  sex                   VARCHAR(6)  NOT NULL,   -- 'female'

  -- 脂肪及脂肪酸增量
  fat                   NUMERIC,                -- 总脂肪 AMDR 增量
  fat_unit              CHAR(10)     NOT NULL,  -- '%E'
  saturated_fat         NUMERIC,                -- 饱和脂肪 AMDR 增量
  saturated_fat_unit    CHAR(10)     NOT NULL,  -- '%E'
  pufa                  NUMERIC,                -- n‑6 PUFA AMDR 增量
  pufa_unit             CHAR(10)     NOT NULL,  -- '%E'
  n3fa                  NUMERIC,                -- n‑3 PUFA AMDR 增量
  n3fa_unit             CHAR(10)     NOT NULL,  -- '%E'
  la                    NUMERIC,                -- 亚油酸 AI 增量
  la_unit               CHAR(10)     NOT NULL,  -- '%E'
  ala                   NUMERIC,                -- α‑亚麻酸 AI 增量
  ala_unit              CHAR(10)     NOT NULL,  -- '%E'
  epa_dha               NUMERIC,                -- EPA+DHA 增量
  epa_dha_unit          CHAR(10)     NOT NULL,  -- 'g'

  -- 碳水化合物增量
  carbohydrate          NUMERIC,                -- EAR/AI 增量
  carbohydrate_unit     CHAR(10)     NOT NULL,  -- 'g'
  fiber_dietary         NUMERIC,                -- 膳食纤维 AI 增量
  fiber_dietary_unit    CHAR(10)     NOT NULL,  -- 'g'
  fructose              NUMERIC,                -- 添加糖 AMDR 增量
  fructose_unit         CHAR(10)     NOT NULL,  -- '%E'

  -- 蛋白质增量
  protein_ear           NUMERIC,                -- EAR 增量
  protein_ear_unit      CHAR(10)     NOT NULL,  -- 'g'
  protein_rni           NUMERIC,                -- RNI 增量
  protein_rni_unit      CHAR(10)     NOT NULL,  -- 'g'
  protein               NUMERIC,                -- AMDR 增量
  protein_unit          CHAR(10)     NOT NULL   -- '%E'
);

INSERT INTO public.nutrition_pregnancy_inc (
  stage, sex,
  fat, fat_unit,
  saturated_fat, saturated_fat_unit,
  pufa, pufa_unit,
  n3fa, n3fa_unit,
  la, la_unit,
  ala, ala_unit,
  epa_dha, epa_dha_unit,
  carbohydrate, carbohydrate_unit,
  fiber_dietary, fiber_dietary_unit,
  fructose, fructose_unit,
  protein_ear, protein_ear_unit,
  protein_rni, protein_rni_unit,
  protein, protein_unit
) VALUES
  ('early',     'female', 0, '%E', 0, '%E', 0, '%E', 0, '%E', 0, '%E', 0, '%E', 0.25, 'g',  10, 'g',  0, 'g', 0.10, '%E', NULL, 'g',  0, 'g', 0.20, '%E'),
  ('mid',       'female', 0, '%E', 0, '%E', 0, '%E', 0, '%E', 0, '%E', 0, '%E', 0.25, 'g',  20, 'g',  4, 'g', 0.10, '%E', NULL, 'g', 15, 'g', 0.20, '%E'),
  ('late',      'female', 0, '%E', 0, '%E', 0, '%E', 0, '%E', 0, '%E', 0, '%E', 0.25, 'g',  35, 'g',  4, 'g', 0.10, '%E', NULL, 'g', 30, 'g', 0.20, '%E'),
  ('lactation', 'female', 0, '%E', 0, '%E', 0, '%E', 0, '%E', 0, '%E', 0, '%E', 0.25, 'g',  50, 'g',  4, 'g', 0.10, '%E', NULL, 'g', 25, 'g', 0.20, '%E');
