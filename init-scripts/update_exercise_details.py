#!/usr/bin/env python3
"""
更新动作详情数据，添加is_public字段

此脚本用于为所有已有的动作详情数据添加is_public字段，默认值为True（公开）
"""

import os
import sys
import logging
from sqlalchemy.orm import Session

# 添加父目录到路径，以便可以导入app包
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

from app.db.session import SessionLocal
from app.models.exercise import ExerciseDetail

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def update_exercise_details():
    """更新所有动作详情数据，添加is_public字段"""
    db = SessionLocal()
    try:
        # 获取所有动作详情数据
        all_details = db.query(ExerciseDetail).all()
        count = len(all_details)
        logger.info(f"找到 {count} 条动作详情数据需要更新")
        
        # 更新每条记录
        updated = 0
        for detail in all_details:
            # 如果is_public字段不存在或为None，则设置为True
            if not hasattr(detail, 'is_public') or detail.is_public is None:
                detail.is_public = True
                updated += 1
        
        # 提交更改
        db.commit()
        logger.info(f"成功更新 {updated} 条动作详情数据")
        
    except Exception as e:
        db.rollback()
        logger.error(f"更新过程中发生错误: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    logger.info("开始运行更新脚本...")
    update_exercise_details()
    logger.info("脚本执行完成!") 