-- 创建食物相关表
\i /app/app/scripts/create_food_tables.sql

-- 添加热量列
\i /app/app/scripts/add_hot_column.sql

-- 导入函数
\i /app/app/scripts/create_import_function.sql

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    email VARCHAR UNIQUE,
    phone VARCHAR UNIQUE,
    hashed_password VARCHAR,
    openid VARCHAR UNIQUE,
    unionid VARCHAR UNIQUE,
    session_key VARCHAR,
    nickname VARCHAR,
    avatar_url VARCHAR,
    country VARCHAR,
    province VARCHAR,
    city VARCHAR,
    gender INTEGER,
    birthday TIMESTAMP,
    age INTEGER,
    height FLOAT,
    weight FLOAT,
    activity_level INTEGER DEFAULT 3,
    body_type VARCHAR,
    experience_level INTEGER,
    fitness_goal INTEGER,
    health_conditions VARCHAR[],
    allergies VARCHAR[],
    bmi FLOAT,
    tedd INTEGER,
    completed BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    is_superuser BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建用户设置表
CREATE TABLE IF NOT EXISTS user_settings (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL UNIQUE REFERENCES users(id) ON DELETE CASCADE,
    notification_enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建食品基本信息表
CREATE TABLE IF NOT EXISTS foods (
    id SERIAL PRIMARY KEY,
    name VARCHAR(128) NOT NULL,
    code VARCHAR(64) NOT NULL UNIQUE,
    category VARCHAR(64),
    food_type VARCHAR(32),
    goods_id INTEGER,
    thumb_image_url VARCHAR,
    large_image_url VARCHAR,
    is_liquid BOOLEAN DEFAULT FALSE,
    hot INTEGER DEFAULT 0,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    can_revise BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT idx_food_name UNIQUE (name),
    CONSTRAINT idx_food_code UNIQUE (code)
);
CREATE INDEX IF NOT EXISTS idx_food_name ON foods(name);
CREATE INDEX IF NOT EXISTS idx_food_code ON foods(code);
CREATE INDEX IF NOT EXISTS idx_food_category ON foods(category);
CREATE INDEX IF NOT EXISTS idx_food_type ON foods(food_type);

-- 创建食品营养概况表
CREATE TABLE IF NOT EXISTS nutritional_profiles (
    id SERIAL PRIMARY KEY,
    food_id INTEGER UNIQUE REFERENCES foods(id) ON DELETE CASCADE,
    health_light INTEGER,
    lights JSONB,
    warnings JSONB,
    warning_scenes JSONB,
    calory FLOAT,
    protein_fraction FLOAT,
    fat_fraction FLOAT,
    carb_fraction FLOAT,
    food_rank INTEGER,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建食品营养素明细表
CREATE TABLE IF NOT EXISTS food_nutrient_values (
    id SERIAL PRIMARY KEY,
    food_id INTEGER REFERENCES foods(id) ON DELETE CASCADE,
    name_en VARCHAR(64) NOT NULL,
    name_cn VARCHAR(64) NOT NULL,
    value FLOAT,
    unit VARCHAR(16),
    unit_name VARCHAR(16),
    precision INTEGER,
    nrv FLOAT,
    category VARCHAR(16) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX IF NOT EXISTS idx_food_nutrient_values_food_id ON food_nutrient_values(food_id);
CREATE INDEX IF NOT EXISTS idx_food_nutrient_values_category ON food_nutrient_values(category);

-- 创建食品计量单位表
CREATE TABLE IF NOT EXISTS food_units (
    id SERIAL PRIMARY KEY,
    food_id INTEGER REFERENCES foods(id) ON DELETE CASCADE,
    unit_name VARCHAR(32) NOT NULL,
    weight FLOAT NOT NULL,
    eat_weight FLOAT NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX IF NOT EXISTS idx_food_units_food_id ON food_units(food_id);

-- 创建餐食记录表
CREATE TABLE IF NOT EXISTS meal_records (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(100) REFERENCES users(id),
    date DATE NOT NULL,
    meal_type VARCHAR(20) NOT NULL,
    image_url VARCHAR,
    file_id VARCHAR(100),
    thumb_image_url VARCHAR,
    total_calory FLOAT DEFAULT 0,
    total_protein FLOAT DEFAULT 0,
    total_fat FLOAT DEFAULT 0,
    total_carbohydrate FLOAT DEFAULT 0,
    is_ai_recognized BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX IF NOT EXISTS idx_meal_records_user_id ON meal_records(user_id);
CREATE INDEX IF NOT EXISTS idx_meal_records_date ON meal_records(date);

-- 创建食物项表
CREATE TABLE IF NOT EXISTS food_items (
    id SERIAL PRIMARY KEY,
    meal_record_id INTEGER REFERENCES meal_records(id) ON DELETE CASCADE,
    food_id INTEGER REFERENCES foods(id),
    name VARCHAR(100) NOT NULL,
    quantity FLOAT DEFAULT 1.0,
    unit_name VARCHAR(20) DEFAULT '份',
    weight FLOAT NOT NULL,
    category VARCHAR(50),
    cuisine_type VARCHAR(50),
    cuisine_type_detail VARCHAR(100),
    image_url VARCHAR,
    health_light INTEGER,
    lights JSONB,
    warnings JSONB,
    warning_scenes JSONB,
    calory FLOAT,
    protein FLOAT,
    fat FLOAT,
    carbohydrate FLOAT,
    protein_fraction FLOAT,
    fat_fraction FLOAT,
    carb_fraction FLOAT,
    is_custom BOOLEAN DEFAULT FALSE,
    is_takeout BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX IF NOT EXISTS idx_food_items_meal_record_id ON food_items(meal_record_id);
CREATE INDEX IF NOT EXISTS idx_food_items_food_id ON food_items(food_id);

-- 创建食物营养素摄入详情表
CREATE TABLE IF NOT EXISTS food_item_nutrient_intakes (
    id SERIAL PRIMARY KEY,
    food_item_id INTEGER REFERENCES food_items(id) ON DELETE CASCADE,
    name_en VARCHAR(64) NOT NULL,
    name_cn VARCHAR(64) NOT NULL,
    value FLOAT,
    unit VARCHAR(16),
    unit_name VARCHAR(16),
    nrv_percentage FLOAT,
    category VARCHAR(16) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX IF NOT EXISTS idx_food_item_nutrients_food_item_id ON food_item_nutrient_intakes(food_item_id);
CREATE INDEX IF NOT EXISTS idx_food_item_nutrients_category ON food_item_nutrient_intakes(category);

-- 创建健康建议表
CREATE TABLE IF NOT EXISTS health_recommendations (
    id SERIAL PRIMARY KEY,
    meal_record_id INTEGER REFERENCES meal_records(id) ON DELETE CASCADE,
    recommendation_text VARCHAR NOT NULL,
    recommendation_type VARCHAR(32),
    priority INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX IF NOT EXISTS idx_health_recommendations_meal_record_id ON health_recommendations(meal_record_id);

-- 创建食物识别临时记录表
CREATE TABLE IF NOT EXISTS food_recognitions (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(36),
    meal_date DATE NOT NULL,
    meal_type VARCHAR(20) NOT NULL,
    image_url VARCHAR(255),
    thumb_image_url VARCHAR(255),
    secure_path VARCHAR(100),
    status VARCHAR(20) DEFAULT 'processing',
    recognition_result JSONB,
    matched_foods JSONB,
    nutrition_totals JSONB,
    meal_record_id INTEGER REFERENCES meal_records(id),
    user_modified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX IF NOT EXISTS idx_food_recognitions_user_id ON food_recognitions(user_id);
CREATE INDEX IF NOT EXISTS idx_food_recognitions_meal_record_id ON food_recognitions(meal_record_id);

-- 创建健身动作基本信息表
CREATE TABLE IF NOT EXISTS exercises (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    en_name VARCHAR(100),
    body_part_id INTEGER[],
    equipment_id INTEGER[],
    image_name VARCHAR(255),
    gif_url VARCHAR(255),
    description TEXT,
    level SMALLINT,
    sort_priority INTEGER DEFAULT 0,
    user_id VARCHAR(50),
    exercise_type VARCHAR(50),
    hit_time INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX IF NOT EXISTS idx_exercise_name ON exercises(name);
CREATE INDEX IF NOT EXISTS idx_exercise_name_en_name ON exercises(name, en_name);
CREATE INDEX IF NOT EXISTS idx_exercise_level_hit_time ON exercises(level, hit_time);
CREATE INDEX IF NOT EXISTS idx_exercise_name_lower ON exercises(lower(name));
CREATE INDEX IF NOT EXISTS idx_exercise_en_name_lower ON exercises(lower(en_name));
CREATE INDEX IF NOT EXISTS idx_popular_exercises ON exercises(hit_time DESC) WHERE hit_time > 0;
CREATE INDEX IF NOT EXISTS idx_exercise_sort_priority ON exercises(sort_priority DESC, id ASC);

-- 创建健身动作详细信息表
CREATE TABLE IF NOT EXISTS exercise_details (
    id SERIAL PRIMARY KEY,
    exercise_id INTEGER REFERENCES exercises(id) NOT NULL,
    target_muscles_id INTEGER[],
    synergist_muscles_id INTEGER[],
    ex_instructions TEXT[],
    exercise_tips TEXT[],
    video_file VARCHAR(255),
    is_public BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX IF NOT EXISTS idx_exercise_detail_exercise_id ON exercise_details(exercise_id);
CREATE INDEX IF NOT EXISTS idx_exercise_detail_public ON exercise_details(exercise_id, is_public);

-- 创建肌肉信息表
CREATE TABLE IF NOT EXISTS muscles (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    en_name VARCHAR(50) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX IF NOT EXISTS idx_muscle_name ON muscles(name);
CREATE INDEX IF NOT EXISTS idx_muscle_name_lower ON muscles(lower(name));

-- 创建身体部位表
CREATE TABLE IF NOT EXISTS body_parts (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX IF NOT EXISTS idx_body_part_name_lower ON body_parts(lower(name));

-- 创建器材表
CREATE TABLE IF NOT EXISTS equipment (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX IF NOT EXISTS idx_equipment_name_lower ON equipment(lower(name));

-- 创建分享追踪表
CREATE TABLE IF NOT EXISTS share_tracks (
    id SERIAL PRIMARY KEY,
    shared_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
    scanned_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
    share_type VARCHAR NOT NULL,
    page VARCHAR NOT NULL,
    scene VARCHAR,
    qrcode_url VARCHAR,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
); 