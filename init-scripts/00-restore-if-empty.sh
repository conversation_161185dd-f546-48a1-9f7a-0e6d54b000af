#!/bin/bash
set -e

# 检查users表中是否有记录
RECORD_COUNT=$(psql -U "$POSTGRES_USER" -d "$POSTGRES_DB" -t -c "SELECT COUNT(*) FROM pg_tables WHERE tablename = 'users';")

# 如果没有users表（即数据库是空的），则运行初始化脚本
if [ "$RECORD_COUNT" -eq "0" ]; then
    echo "数据库为空，开始初始化..."
    
    # 运行SQL初始化脚本
    psql -U "$POSTGRES_USER" -d "$POSTGRES_DB" -f /docker-entrypoint-initdb.d/setup.sql
    
    echo "数据库初始化完成！"
else
    echo "数据库已存在，跳过初始化步骤"
fi

# 设置正确的时区
echo "设置时区为Asia/Shanghai..."
psql -U "$POSTGRES_USER" -d "$POSTGRES_DB" -c "ALTER DATABASE \"$POSTGRES_DB\" SET timezone TO 'Asia/Shanghai';"
psql -U "$POSTGRES_USER" -d "$POSTGRES_DB" -c "SET timezone TO 'Asia/Shanghai';"

echo "初始化过程完成"
exit 0 