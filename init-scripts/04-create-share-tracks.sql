-- 创建分享追踪表
CREATE TABLE IF NOT EXISTS share_tracks (
    id SERIAL PRIMARY KEY,
    shared_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
    scanned_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
    share_type VARCHAR NOT NULL,
    page VARCHAR NOT NULL,
    scene VARCHAR,
    qrcode_url VARCHAR,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
-- 添加索引
CREATE INDEX IF NOT EXISTS ix_share_tracks_id ON share_tracks(id); 