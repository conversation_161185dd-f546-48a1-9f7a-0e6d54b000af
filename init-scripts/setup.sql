-- 导入函数
\i create_import_function.sql

-- 导入健身动作相关函数
\i create_exercise_import_function.sql

-- 创建用户相关表
\i 00-create-users.sql

-- 创建食品相关表
\i 01-create-foods.sql

-- 创建餐食相关表
\i 02-create-meals.sql

-- 创建健身相关表
\i 03-create-exercises.sql

-- 创建分享追踪表
\i 04-create-share-tracks.sql

--
-- 创建用户收藏关系表
\i 05-create-favorites.sql

-- 创建特殊索引
\echo '创建GIN索引(支持数组类型查询)'
CREATE INDEX IF NOT EXISTS idx_body_part_id_gin ON exercises USING gin(body_part_id);
CREATE INDEX IF NOT EXISTS idx_equipment_id_gin ON exercises USING gin(equipment_id);
CREATE INDEX IF NOT EXISTS idx_target_muscles_gin ON exercise_details USING gin(target_muscles_id);
CREATE INDEX IF NOT EXISTS idx_synergist_muscles_gin ON exercise_details USING gin(synergist_muscles_id);

\echo '初始化数据库表完成' 