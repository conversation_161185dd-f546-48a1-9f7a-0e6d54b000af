-- 1. 创建：饮水量参考摄入表
DROP TABLE IF EXISTS public.water_rni;
CREATE TABLE public.water_rni (
  id                SERIAL       PRIMARY KEY,
  sex               VARCHAR(6)   NOT NULL,    -- 'male','female'
  age_start_years   NUMERIC(5,2) NOT NULL,    -- 起始年龄（年）
  drinking_ml       INTEGER,                  -- 推荐饮水量 (mL/d)
  total_water_ml    INTEGER                   -- 总水摄入量 (mL/d)
);

INSERT INTO public.water_rni (sex, age_start_years, drinking_ml, total_water_ml) VALUES
  ('male',   0.00, NULL,  700),
  ('female', 0.00, NULL,  700),
  ('male',   0.50, NULL,  900),
  ('female', 0.50, NULL,  900),
  ('male',   1.00, NULL, 1300),
  ('female', 1.00, NULL, 1300),
  ('male',   4.00,  800, 1600),
  ('female', 4.00,  800, 1600),
  ('male',   7.00, 1000, 1800),
  ('female', 7.00, 1000, 1800),
  ('male',  12.00, 1300, 2300),
  ('female',12.00, 1100, 2000),
  ('male',  15.00, 1400, 2500),
  ('female',15.00, 1200, 2200),
  ('male',  18.00, 1700, 3000),
  ('female',18.00, 1500, 2700),
  ('male',  65.00, 1700, 3000),
  ('female',65.00, 1500, 2700)
;

-- 2. 创建：孕期／哺乳期饮水量增量表
DROP TABLE IF EXISTS public.water_pregnancy_inc;
CREATE TABLE public.water_pregnancy_inc (
  id                SERIAL      PRIMARY KEY,
  stage             VARCHAR(16) NOT NULL,    -- 'early','mid','late','lactation'
  sex               VARCHAR(6)  NOT NULL,    -- 这里均为 'female'
  drinking_inc_ml   INTEGER,                  -- 饮水量 增量 (mL/d)
  total_water_inc_ml INTEGER                  -- 总水摄入量 增量 (mL/d)
);

INSERT INTO public.water_pregnancy_inc (stage, sex, drinking_inc_ml, total_water_inc_ml) VALUES
  ('early',     'female',   0,   0),
  ('mid',       'female', 200, 300),
  ('late',      'female', 200, 300),
  ('lactation', 'female', 600,1100)
;


-- 3. 创建：其他膳食成分 SPL & UL 表
DROP TABLE IF EXISTS public.other_dietary_spl_ul;
CREATE TABLE public.other_dietary_spl_ul (
  id         SERIAL PRIMARY KEY,
  name_cn    VARCHAR(32) NOT NULL,  -- 成分中文名
  spl        NUMERIC,               -- 推荐最大摄入量 SPL
  spl_unit   CHAR(10),              -- SPL 单位
  ul         NUMERIC,               -- 可耐受最高摄入量 UL
  ul_unit    CHAR(10)               -- UL 单位
);

INSERT INTO public.other_dietary_spl_ul (name_cn, spl, spl_unit, ul, ul_unit) VALUES
  ('原形维A',      200, 'μg',    NULL,   NULL),
  ('花色苷',        50, 'mg',    NULL,   NULL),
  ('大豆异黄酮',    55, 'mg',   120,    'mg'),
  ('绿原酸',       200, 'mg',    NULL,   NULL),
  ('番茄红素',      15, 'mg',    70,    'mg'),
  ('叶黄素',        10, 'mg',    60,    'mg'),
  ('植物甾醇',     0.8, 'g',     2.4,   'g'),
  ('植物甾醇酯',    1.3, 'g',     3.9,   'g'),
  ('异构糖',       30,  'g',     NULL,   NULL),
  ('糖精',        100, 'mg',     NULL,   NULL),
  ('甜菊糖',       1.5, 'mg',     4.0,   'mg'),
  ('寡聚果糖',     10,  'g',     NULL,   NULL),
  ('β-胡萝卜素',    3.0, 'mg',     NULL,   NULL)
;
