<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="0" failures="2" skipped="0" tests="5" time="0.215" timestamp="2025-05-26T12:48:16.565984+08:00" hostname="VM-4-16-ubuntu"><testcase classname="tests.comprehensive.e2e.test_multi_turn_conversations.TestMultiTurnConversations" name="test_fitness_consultation_flow" time="0.006"><failure message="Failed: 对话轮次 1 处理失败: 响应内容长度 28 小于最小要求 50&#10;assert 28 &gt;= 50&#10; +  where 28 = len('下午好！我是您的健身教练助手。 今天有什么可以帮您的吗？')&#10; +  and   50 = ConversationTurn(user_message='你好，我想开始健身但不知道从哪里开始', expected_intent='fitness_advice', expected_keywords=['健身', '开始', '建议'], min_response_length=50, max_response_time=5.0, should_contain_user_info=False, expected_state='fitness_advice').min_response_length">tests/comprehensive/e2e/test_multi_turn_conversations.py:228: in _run_conversation_scenario
    self._validate_turn_response(turn, response, response_time)
tests/comprehensive/e2e/test_multi_turn_conversations.py:258: in _validate_turn_response
    assert len(response_content) &gt;= turn.min_response_length, \
E   AssertionError: 响应内容长度 28 小于最小要求 50
E   assert 28 &gt;= 50
E    +  where 28 = len('下午好！我是您的健身教练助手。 今天有什么可以帮您的吗？')
E    +  and   50 = ConversationTurn(user_message='你好，我想开始健身但不知道从哪里开始', expected_intent='fitness_advice', expected_keywords=['健身', '开始', '建议'], min_response_length=50, max_response_time=5.0, should_contain_user_info=False, expected_state='fitness_advice').min_response_length

During handling of the above exception, another exception occurred:
tests/comprehensive/e2e/test_multi_turn_conversations.py:181: in test_fitness_consultation_flow
    await self._run_conversation_scenario(fitness_consultation_scenario)
tests/comprehensive/e2e/test_multi_turn_conversations.py:243: in _run_conversation_scenario
    pytest.fail(f"对话轮次 {i+1} 处理失败: {str(e)}")
E   Failed: 对话轮次 1 处理失败: 响应内容长度 28 小于最小要求 50
E   assert 28 &gt;= 50
E    +  where 28 = len('下午好！我是您的健身教练助手。 今天有什么可以帮您的吗？')
E    +  and   50 = ConversationTurn(user_message='你好，我想开始健身但不知道从哪里开始', expected_intent='fitness_advice', expected_keywords=['健身', '开始', '建议'], min_response_length=50, max_response_time=5.0, should_contain_user_info=False, expected_state='fitness_advice').min_response_length</failure></testcase><testcase classname="tests.comprehensive.e2e.test_multi_turn_conversations.TestMultiTurnConversations" name="test_weight_loss_consultation" time="0.007"><failure message="Failed: 对话轮次 3 处理失败: 响应内容长度 69 小于最小要求 80&#10;assert 69 &gt;= 80&#10; +  where 69 = len(&quot;这是基于提示'\n        用户查询: 饮食方面有什么建议？\n    ...'生成的模拟文本。在实际应用中，这将由真实的语言模型生成。&quot;)&#10; +  and   80 = ConversationTurn(user_message='饮食方面有什么建议？', expected_intent='diet_advice', expected_keywords=['饮食', '建议'], min_response_length=80, max_response_time=5.0, should_contain_user_info=False, expected_state=None).min_response_length">tests/comprehensive/e2e/test_multi_turn_conversations.py:228: in _run_conversation_scenario
    self._validate_turn_response(turn, response, response_time)
tests/comprehensive/e2e/test_multi_turn_conversations.py:258: in _validate_turn_response
    assert len(response_content) &gt;= turn.min_response_length, \
E   AssertionError: 响应内容长度 69 小于最小要求 80
E   assert 69 &gt;= 80
E    +  where 69 = len("这是基于提示'\n        用户查询: 饮食方面有什么建议？\n    ...'生成的模拟文本。在实际应用中，这将由真实的语言模型生成。")
E    +  and   80 = ConversationTurn(user_message='饮食方面有什么建议？', expected_intent='diet_advice', expected_keywords=['饮食', '建议'], min_response_length=80, max_response_time=5.0, should_contain_user_info=False, expected_state=None).min_response_length

During handling of the above exception, another exception occurred:
tests/comprehensive/e2e/test_multi_turn_conversations.py:186: in test_weight_loss_consultation
    await self._run_conversation_scenario(weight_loss_scenario)
tests/comprehensive/e2e/test_multi_turn_conversations.py:243: in _run_conversation_scenario
    pytest.fail(f"对话轮次 {i+1} 处理失败: {str(e)}")
E   Failed: 对话轮次 3 处理失败: 响应内容长度 69 小于最小要求 80
E   assert 69 &gt;= 80
E    +  where 69 = len("这是基于提示'\n        用户查询: 饮食方面有什么建议？\n    ...'生成的模拟文本。在实际应用中，这将由真实的语言模型生成。")
E    +  and   80 = ConversationTurn(user_message='饮食方面有什么建议？', expected_intent='diet_advice', expected_keywords=['饮食', '建议'], min_response_length=80, max_response_time=5.0, should_contain_user_info=False, expected_state=None).min_response_length</failure></testcase><testcase classname="tests.comprehensive.e2e.test_multi_turn_conversations.TestMultiTurnConversations" name="test_intent_switching" time="0.010" /><testcase classname="tests.comprehensive.e2e.test_multi_turn_conversations.TestMultiTurnConversations" name="test_conversation_memory" time="0.007" /><testcase classname="tests.comprehensive.e2e.test_multi_turn_conversations.TestMultiTurnConversations" name="test_concurrent_conversations" time="0.010" /></testsuite></testsuites>