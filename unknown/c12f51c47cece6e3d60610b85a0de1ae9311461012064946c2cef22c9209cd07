"""Add view_count and reported_count to Post model

Revision ID: 3b2dda3de158
Revises: fc2aa9bc6e47
Create Date: 2025-05-26 22:13:42.002139

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '3b2dda3de158'
down_revision = 'fc2aa9bc6e47'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_training_records',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('training_age', sa.Integer(), nullable=True),
    sa.Column('preferred_training_days', sa.Integer(), nullable=True),
    sa.Column('squat_max', sa.Float(), nullable=True),
    sa.Column('bench_press_max', sa.Float(), nullable=True),
    sa.Column('deadlift_max', sa.Float(), nullable=True),
    sa.Column('preferred_exercise_types', sa.<PERSON>(), nullable=True),
    sa.Column('avoided_exercise_types', sa.JSON(), nullable=True),
    sa.Column('fitness_goals', sa.JSON(), nullable=True),
    sa.Column('target_muscle_groups', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('injuries', sa.JSON(), nullable=True),
    sa.Column('medical_conditions', sa.JSON(), nullable=True),
    sa.Column('date', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('body_parts', sa.ARRAY(sa.String()), nullable=True),
    sa.Column('duration_minutes', sa.Integer(), nullable=True),
    sa.Column('exercises_data', sa.JSON(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_training_records_date'), 'user_training_records', ['date'], unique=False)
    op.create_index(op.f('ix_user_training_records_id'), 'user_training_records', ['id'], unique=False)
    op.add_column('posts', sa.Column('view_count', sa.Integer(), nullable=True))
    op.add_column('posts', sa.Column('reported_count', sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('posts', 'reported_count')
    op.drop_column('posts', 'view_count')
    op.drop_index(op.f('ix_user_training_records_id'), table_name='user_training_records')
    op.drop_index(op.f('ix_user_training_records_date'), table_name='user_training_records')
    op.drop_table('user_training_records')
    # ### end Alembic commands ### 