<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="1" failures="0" skipped="0" tests="1" time="0.253" timestamp="2025-05-26T12:46:02.397005+08:00" hostname="VM-4-16-ubuntu"><testcase classname="" name="tests.comprehensive.performance.test_load_performance" time="0.000"><error message="collection failure">ImportError while importing test module '/home/<USER>/backend/tests/comprehensive/performance/test_load_performance.py'.
Hint: make sure your test modules/packages have valid Python names.
Traceback:
/usr/lib/python3.12/importlib/__init__.py:90: in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
tests/comprehensive/performance/test_load_performance.py:10: in &lt;module&gt;
    import psutil
E   ModuleNotFoundError: No module named 'psutil'</error></testcase></testsuite></testsuites>