"""
WebSocket测试客户端

提供WebSocket连接测试、流式响应测试、连接稳定性测试等功能
"""

import asyncio
import websockets
import json
import time
import logging
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class WebSocketTestResult:
    """WebSocket测试结果"""
    test_name: str
    success: bool
    response_time: float
    message_count: int
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class WebSocketTestClient:
    """WebSocket测试客户端"""
    
    def __init__(self, base_url: str = "ws://localhost:8000"):
        self.base_url = base_url
        self.test_results: List[WebSocketTestResult] = []
        self.connection_stats = {
            "total_connections": 0,
            "successful_connections": 0,
            "failed_connections": 0,
            "total_messages_sent": 0,
            "total_messages_received": 0
        }
    
    async def test_basic_connection(self, session_id: str, auth_token: str) -> WebSocketTestResult:
        """测试基本WebSocket连接"""
        test_name = "基本连接测试"
        start_time = time.time()
        
        try:
            uri = f"{self.base_url}/api/v2/chat/stream/{session_id}"
            headers = {"Authorization": f"Bearer {auth_token}"}
            
            async with websockets.connect(uri, extra_headers=headers) as websocket:
                self.connection_stats["total_connections"] += 1
                self.connection_stats["successful_connections"] += 1
                
                # 等待连接确认消息
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                data = json.loads(response)
                
                response_time = time.time() - start_time
                
                if data.get("event") == "connected":
                    result = WebSocketTestResult(
                        test_name=test_name,
                        success=True,
                        response_time=response_time,
                        message_count=1,
                        metadata=data
                    )
                else:
                    result = WebSocketTestResult(
                        test_name=test_name,
                        success=False,
                        response_time=response_time,
                        message_count=1,
                        error_message=f"未收到连接确认消息: {data}"
                    )
                
        except Exception as e:
            self.connection_stats["failed_connections"] += 1
            result = WebSocketTestResult(
                test_name=test_name,
                success=False,
                response_time=time.time() - start_time,
                message_count=0,
                error_message=str(e)
            )
        
        self.test_results.append(result)
        return result
    
    async def test_message_exchange(
        self, 
        session_id: str, 
        auth_token: str,
        test_message: str = "这是一个测试消息"
    ) -> WebSocketTestResult:
        """测试消息收发"""
        test_name = "消息收发测试"
        start_time = time.time()
        messages_received = 0
        
        try:
            uri = f"{self.base_url}/api/v2/chat/stream/{session_id}"
            headers = {"Authorization": f"Bearer {auth_token}"}
            
            async with websockets.connect(uri, extra_headers=headers) as websocket:
                # 等待连接确认
                await websocket.recv()
                
                # 发送测试消息
                message_data = {
                    "message": test_message,
                    "meta_info": {"test": True}
                }
                
                await websocket.send(json.dumps(message_data))
                self.connection_stats["total_messages_sent"] += 1
                
                # 收集响应
                responses = []
                timeout = 10.0  # 10秒超时
                
                try:
                    while True:
                        response = await asyncio.wait_for(websocket.recv(), timeout=timeout)
                        data = json.loads(response)
                        responses.append(data)
                        messages_received += 1
                        self.connection_stats["total_messages_received"] += 1
                        
                        # 如果收到完成信号，停止接收
                        if (data.get("event") == "done" or 
                            data.get("event") == "message" or
                            "完成" in str(data)):
                            break
                        
                        # 防止无限循环
                        if messages_received > 50:
                            break
                            
                except asyncio.TimeoutError:
                    # 超时不一定是错误，可能是正常的流式结束
                    pass
                
                response_time = time.time() - start_time
                
                # 验证是否收到有效响应
                has_valid_response = any(
                    response.get("event") in ["message", "chunk", "token"] or
                    "content" in response
                    for response in responses
                )
                
                result = WebSocketTestResult(
                    test_name=test_name,
                    success=has_valid_response and messages_received > 0,
                    response_time=response_time,
                    message_count=messages_received,
                    metadata={
                        "responses": responses,
                        "test_message": test_message
                    }
                )
                
        except Exception as e:
            result = WebSocketTestResult(
                test_name=test_name,
                success=False,
                response_time=time.time() - start_time,
                message_count=messages_received,
                error_message=str(e)
            )
        
        self.test_results.append(result)
        return result
    
    async def test_stream_response(
        self, 
        session_id: str, 
        auth_token: str,
        test_message: str = "请给我一些详细的健身建议"
    ) -> WebSocketTestResult:
        """测试流式响应"""
        test_name = "流式响应测试"
        start_time = time.time()
        stream_chunks = []
        
        try:
            uri = f"{self.base_url}/api/v2/chat/stream/{session_id}"
            headers = {"Authorization": f"Bearer {auth_token}"}
            
            async with websockets.connect(uri, extra_headers=headers) as websocket:
                # 等待连接确认
                await websocket.recv()
                
                # 发送测试消息
                message_data = {
                    "message": test_message,
                    "meta_info": {"test_stream": True}
                }
                
                await websocket.send(json.dumps(message_data))
                
                # 收集流式响应
                first_chunk_time = None
                last_chunk_time = None
                
                try:
                    while True:
                        response = await asyncio.wait_for(websocket.recv(), timeout=15.0)
                        data = json.loads(response)
                        
                        current_time = time.time()
                        if first_chunk_time is None:
                            first_chunk_time = current_time
                        last_chunk_time = current_time
                        
                        # 记录流式数据块
                        if data.get("event") in ["chunk", "token"] or "content" in data:
                            stream_chunks.append({
                                "timestamp": current_time,
                                "data": data,
                                "chunk_index": len(stream_chunks)
                            })
                        
                        # 检查是否结束
                        if (data.get("event") == "done" or 
                            data.get("event") == "message" or
                            len(stream_chunks) > 100):  # 防止无限循环
                            break
                            
                except asyncio.TimeoutError:
                    pass
                
                response_time = time.time() - start_time
                
                # 计算流式指标
                time_to_first_chunk = first_chunk_time - start_time if first_chunk_time else None
                total_stream_time = last_chunk_time - first_chunk_time if first_chunk_time and last_chunk_time else None
                
                result = WebSocketTestResult(
                    test_name=test_name,
                    success=len(stream_chunks) > 0,
                    response_time=response_time,
                    message_count=len(stream_chunks),
                    metadata={
                        "stream_chunks": len(stream_chunks),
                        "time_to_first_chunk": time_to_first_chunk,
                        "total_stream_time": total_stream_time,
                        "chunks_per_second": len(stream_chunks) / total_stream_time if total_stream_time else 0
                    }
                )
                
        except Exception as e:
            result = WebSocketTestResult(
                test_name=test_name,
                success=False,
                response_time=time.time() - start_time,
                message_count=len(stream_chunks),
                error_message=str(e)
            )
        
        self.test_results.append(result)
        return result
    
    async def test_connection_stability(
        self, 
        session_id: str, 
        auth_token: str,
        duration_seconds: int = 60
    ) -> WebSocketTestResult:
        """测试连接稳定性"""
        test_name = f"连接稳定性测试({duration_seconds}秒)"
        start_time = time.time()
        heartbeat_count = 0
        
        try:
            uri = f"{self.base_url}/api/v2/chat/stream/{session_id}"
            headers = {"Authorization": f"Bearer {auth_token}"}
            
            async with websockets.connect(uri, extra_headers=headers) as websocket:
                # 等待连接确认
                await websocket.recv()
                
                end_time = start_time + duration_seconds
                
                while time.time() < end_time:
                    try:
                        # 等待心跳或其他消息
                        response = await asyncio.wait_for(websocket.recv(), timeout=35.0)
                        data = json.loads(response)
                        
                        if data.get("event") == "heartbeat":
                            heartbeat_count += 1
                            logger.info(f"收到心跳 #{heartbeat_count}")
                        
                    except asyncio.TimeoutError:
                        # 如果长时间没有消息，发送一个测试消息
                        test_msg = {"message": "连接测试", "meta_info": {"stability_test": True}}
                        await websocket.send(json.dumps(test_msg))
                
                response_time = time.time() - start_time
                
                result = WebSocketTestResult(
                    test_name=test_name,
                    success=True,
                    response_time=response_time,
                    message_count=heartbeat_count,
                    metadata={
                        "heartbeat_count": heartbeat_count,
                        "duration": duration_seconds,
                        "heartbeat_rate": heartbeat_count / (duration_seconds / 60)  # 每分钟心跳数
                    }
                )
                
        except Exception as e:
            result = WebSocketTestResult(
                test_name=test_name,
                success=False,
                response_time=time.time() - start_time,
                message_count=heartbeat_count,
                error_message=str(e)
            )
        
        self.test_results.append(result)
        return result
    
    async def test_concurrent_connections(
        self, 
        session_ids: List[str], 
        auth_token: str,
        message: str = "并发测试消息"
    ) -> WebSocketTestResult:
        """测试并发连接"""
        test_name = f"并发连接测试({len(session_ids)}个连接)"
        start_time = time.time()
        
        async def single_connection_test(session_id: str):
            try:
                uri = f"{self.base_url}/api/v2/chat/stream/{session_id}"
                headers = {"Authorization": f"Bearer {auth_token}"}
                
                async with websockets.connect(uri, extra_headers=headers) as websocket:
                    # 等待连接确认
                    await websocket.recv()
                    
                    # 发送消息
                    message_data = {"message": f"{message} - {session_id}", "meta_info": {"concurrent_test": True}}
                    await websocket.send(json.dumps(message_data))
                    
                    # 等待响应
                    response = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                    return {"success": True, "session_id": session_id, "response": response}
                    
            except Exception as e:
                return {"success": False, "session_id": session_id, "error": str(e)}
        
        try:
            # 并发执行所有连接测试
            tasks = [single_connection_test(sid) for sid in session_ids]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            response_time = time.time() - start_time
            
            # 统计结果
            successful_connections = sum(1 for r in results if isinstance(r, dict) and r.get("success"))
            failed_connections = len(results) - successful_connections
            
            result = WebSocketTestResult(
                test_name=test_name,
                success=successful_connections > 0,
                response_time=response_time,
                message_count=len(results),
                metadata={
                    "total_connections": len(session_ids),
                    "successful_connections": successful_connections,
                    "failed_connections": failed_connections,
                    "success_rate": successful_connections / len(session_ids) * 100,
                    "detailed_results": results
                }
            )
            
        except Exception as e:
            result = WebSocketTestResult(
                test_name=test_name,
                success=False,
                response_time=time.time() - start_time,
                message_count=0,
                error_message=str(e)
            )
        
        self.test_results.append(result)
        return result
    
    def generate_test_report(self) -> str:
        """生成测试报告"""
        if not self.test_results:
            return "无测试结果"
        
        total_tests = len(self.test_results)
        successful_tests = sum(1 for r in self.test_results if r.success)
        success_rate = successful_tests / total_tests * 100
        
        avg_response_time = sum(r.response_time for r in self.test_results) / total_tests
        
        report = f"""
🔌 WebSocket测试报告
{'='*60}
📊 总体统计:
   - 总测试数: {total_tests}
   - 成功数: {successful_tests}
   - 失败数: {total_tests - successful_tests}
   - 成功率: {success_rate:.1f}%
   - 平均响应时间: {avg_response_time:.2f}秒

🌐 连接统计:
   - 总连接尝试: {self.connection_stats['total_connections']}
   - 成功连接: {self.connection_stats['successful_connections']}
   - 失败连接: {self.connection_stats['failed_connections']}
   - 发送消息数: {self.connection_stats['total_messages_sent']}
   - 接收消息数: {self.connection_stats['total_messages_received']}

📋 详细结果:
"""
        
        for i, result in enumerate(self.test_results, 1):
            status = "✅" if result.success else "❌"
            report += f"   {i}. {status} {result.test_name}\n"
            report += f"      响应时间: {result.response_time:.2f}秒\n"
            report += f"      消息数: {result.message_count}\n"
            
            if result.error_message:
                report += f"      错误: {result.error_message}\n"
            
            if result.metadata:
                for key, value in result.metadata.items():
                    if key not in ["responses", "detailed_results"]:  # 跳过大型数据
                        report += f"      {key}: {value}\n"
            
            report += "\n"
        
        report += f"📅 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        return report
    
    def clear_results(self):
        """清空测试结果"""
        self.test_results.clear()
        self.connection_stats = {
            "total_connections": 0,
            "successful_connections": 0,
            "failed_connections": 0,
            "total_messages_sent": 0,
            "total_messages_received": 0
        }


async def main():
    """主函数 - 运行WebSocket测试"""
    client = WebSocketTestClient()
    
    # 这里需要实际的认证token和session_id
    auth_token = "test_token_123"  # 替换为实际的token
    session_id = f"websocket_test_{int(time.time())}"
    
    print("🚀 开始WebSocket测试...")
    
    # 基本连接测试
    print("\n1. 测试基本连接...")
    result1 = await client.test_basic_connection(session_id, auth_token)
    print(f"   结果: {'成功' if result1.success else '失败'}")
    
    # 消息收发测试
    print("\n2. 测试消息收发...")
    result2 = await client.test_message_exchange(session_id, auth_token)
    print(f"   结果: {'成功' if result2.success else '失败'}")
    
    # 流式响应测试
    print("\n3. 测试流式响应...")
    result3 = await client.test_stream_response(session_id, auth_token)
    print(f"   结果: {'成功' if result3.success else '失败'}")
    
    # 生成报告
    print("\n" + "="*60)
    print(client.generate_test_report())


if __name__ == "__main__":
    asyncio.run(main())
