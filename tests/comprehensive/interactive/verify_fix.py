#!/usr/bin/env python3
"""
验证API端点修复的简单测试脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../../"))

from tests.comprehensive.interactive.gradio_test_app import ChatAPIClient


async def verify_fix():
    """验证API端点修复"""
    print("🔧 验证API端点修复")
    print("=" * 50)
    
    # 创建API客户端
    api_client = ChatAPIClient()
    
    # 测试健康检查
    print("📋 步骤1: 健康检查")
    health_result = await api_client.check_health()
    print(f"健康状态: {health_result['status']}")
    
    if health_result["status"] != "healthy":
        print("❌ API服务器不健康，无法继续测试")
        return False
    
    # 测试聊天API
    print("\n📋 步骤2: 测试聊天API")
    test_message = "你好，测试API端点修复"
    session_id = "fix_verification_test"
    
    result = await api_client.send_message(
        message=test_message,
        session_id=session_id,
        user_id=15
    )
    
    print(f"API调用结果:")
    print(f"  成功: {result['success']}")
    print(f"  状态码: {result.get('status_code', 'N/A')}")
    print(f"  响应时间: {result.get('response_time', 0):.3f}秒")
    
    if result["success"]:
        api_data = result["data"]
        response_content = api_data.get("response", api_data.get("response_content", ""))
        print(f"  意图: {api_data.get('intent', '未知')}")
        print(f"  置信度: {api_data.get('confidence', 0):.2f}")
        print(f"  响应长度: {len(response_content)}字符")
        print(f"  响应预览: {response_content[:100]}...")
        
        print("\n✅ API端点修复验证成功！")
        print("🎯 正确的API端点: /api/v2/chat/message")
        return True
    else:
        print(f"\n❌ API调用失败: {result.get('error', '未知错误')}")
        print("🔍 请检查API端点是否正确")
        return False


async def main():
    """主函数"""
    try:
        success = await verify_fix()
        if success:
            print("\n🎉 验证完成 - API端点修复成功！")
            print("📱 现在可以正常使用Gradio界面: http://localhost:7860")
        else:
            print("\n💥 验证失败 - 需要进一步检查")
        return success
    except Exception as e:
        print(f"\n💥 验证过程中发生异常: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
