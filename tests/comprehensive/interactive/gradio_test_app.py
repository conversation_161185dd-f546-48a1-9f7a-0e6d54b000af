"""
Gradio交互式测试应用 - 生产环境API集成版

通过HTTP请求直接调用生产环境API，确保测试与生产环境完全一致
避免代码重复，真实测试完整的API调用链路
"""

import gradio as gr
import asyncio
import json
import time
import pandas as pd
import sys
import os
import uuid
import subprocess
import uvicorn
import httpx
from typing import List, Dict, Any, Tuple, Optional
from datetime import datetime
from multiprocessing import Process
import plotly.graph_objects as go
import plotly.express as px
import threading
import signal
import atexit

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../../"))

# 导入配置和工具
from tests.utils.test_helpers import create_test_user_profile, load_test_scenarios
from tests.comprehensive.config.real_model_config import TEST_USER_PROFILES, CONVERSATION_SCENARIOS, RealModelTestConfig
from tests.utils.auth_helpers import TestAuthManager, get_test_auth_headers

# 导入数据库相关（仅在需要时使用）
try:
    from sqlalchemy.orm import Session
    from app.db.session import SessionLocal
    from app import models, crud, schemas
    from app.models.message import MessageRole
    from app.services.ai_assistant.conversation.orchestrator import conversation_orchestrator
    from app.core.chat_config import BAILIAN_APPS
    from tests.utils.test_helpers import create_test_user
    DB_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 数据库模块导入失败: {e}")
    DB_AVAILABLE = False
    Session = None


class APIServerManager:
    """API服务器管理器"""

    def __init__(self, host="127.0.0.1", port=8000):
        self.host = host
        self.port = port
        self.process = None
        self.is_running = False

    def start_server(self):
        """启动FastAPI服务器"""
        if self.is_running:
            print("⚠️ API服务器已在运行")
            return True

        try:
            def run_server():
                # 设置环境变量
                os.environ["ENABLE_REAL_MODELS"] = "true"

                # 启动服务器
                uvicorn.run(
                    "app.main:app",
                    host=self.host,
                    port=self.port,
                    reload=False,
                    log_level="warning",  # 减少日志输出
                    access_log=False
                )

            self.process = Process(target=run_server)
            self.process.daemon = True  # 设置为守护进程
            self.process.start()

            # 等待服务器启动
            self._wait_for_server_start()

            self.is_running = True
            print(f"✅ API服务器启动成功: http://{self.host}:{self.port}")

            # 注册清理函数
            atexit.register(self.stop_server)

            return True

        except Exception as e:
            print(f"❌ API服务器启动失败: {e}")
            return False

    def _wait_for_server_start(self, timeout=30):
        """等待服务器启动"""
        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                import requests
                response = requests.get(f"http://{self.host}:{self.port}/health", timeout=1)
                if response.status_code == 200:
                    return True
            except:
                pass
            time.sleep(1)

        return False

    def stop_server(self):
        """停止服务器"""
        if self.process and self.process.is_alive():
            self.process.terminate()
            self.process.join(timeout=5)
            if self.process.is_alive():
                self.process.kill()
            print("🛑 API服务器已停止")
        self.is_running = False

    def is_server_running(self) -> bool:
        """检查服务器是否运行"""
        try:
            import requests
            response = requests.get(f"http://{self.host}:{self.port}/health", timeout=2)
            return response.status_code == 200
        except:
            return False


class ChatAPIClient:
    """聊天API客户端"""

    def __init__(self, base_url: str = "http://127.0.0.1:8000"):
        self.base_url = base_url
        self.session_id = None
        self.test_user_id = 15
        self.auth_manager = TestAuthManager(self.test_user_id)

    def _generate_session_id(self) -> str:
        """生成会话ID"""
        return f"gradio_test_{uuid.uuid4().hex[:8]}"

    def _get_auth_headers(self) -> Dict[str, str]:
        """获取认证头"""
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }

        # 使用认证管理器获取token
        auth_headers = self.auth_manager.get_auth_headers()
        headers.update(auth_headers)

        return headers

    async def send_message(
        self,
        message: str,
        session_id: Optional[str] = None,
        user_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """调用 /api/v2/chat/message 接口"""

        url = f"{self.base_url}/api/v2/chat/message"

        # 构造请求数据
        payload = {
            "message": message,
            "session_id": session_id or self._generate_session_id()
        }

        # 如果指定了用户ID，添加到请求中
        if user_id:
            payload["user_id"] = user_id

        headers = self._get_auth_headers()

        start_time = time.time()

        async with httpx.AsyncClient(timeout=30.0) as client:
            try:
                response = await client.post(
                    url,
                    json=payload,
                    headers=headers
                )

                response_time = time.time() - start_time

                # 处理响应
                if response.status_code == 200:
                    return {
                        "success": True,
                        "data": response.json(),
                        "status_code": response.status_code,
                        "response_time": response_time,
                        "request_payload": payload
                    }
                else:
                    return {
                        "success": False,
                        "error": f"HTTP {response.status_code}: {response.text}",
                        "status_code": response.status_code,
                        "response_time": response_time,
                        "request_payload": payload
                    }

            except httpx.TimeoutException:
                return {
                    "success": False,
                    "error": "请求超时",
                    "status_code": 408,
                    "response_time": time.time() - start_time,
                    "request_payload": payload
                }
            except Exception as e:
                return {
                    "success": False,
                    "error": f"请求失败: {str(e)}",
                    "status_code": 500,
                    "response_time": time.time() - start_time,
                    "request_payload": payload
                }

    async def check_health(self) -> Dict[str, Any]:
        """检查API服务器健康状态"""
        url = f"{self.base_url}/health"

        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                start_time = time.time()
                response = await client.get(url)
                response_time = time.time() - start_time

                return {
                    "status": "healthy" if response.status_code == 200 else "unhealthy",
                    "status_code": response.status_code,
                    "response_time": response_time,
                    "details": response.json() if response.status_code == 200 else response.text
                }
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "response_time": 0
            }


class GradioTestApp:
    """Gradio测试应用主类 - 生产环境API集成版"""

    def __init__(self):
        self.conversation_history = {}
        self.test_results = []
        self.performance_metrics = []
        self.user_profiles = self._load_user_profiles()
        self.test_scenarios = self._load_test_scenarios()

        # API服务器和客户端
        self.api_server = APIServerManager()
        self.api_client = ChatAPIClient()

        # 测试用户ID
        self.test_user_id = 15

        # 初始化状态
        self.setup_complete = False
        self.initialization_status = "未开始"

        print("🚀 初始化Gradio生产环境测试应用...")

    async def initialize(self):
        """初始化应用"""
        try:
            self.initialization_status = "正在检查依赖..."
            print("📋 检查依赖...")
            await self._check_dependencies()

            self.initialization_status = "正在启动API服务器..."
            print("🚀 启动API服务器...")
            if not await self._start_api_server():
                raise Exception("API服务器启动失败")

            self.initialization_status = "正在等待服务器就绪..."
            print("⏳ 等待服务器就绪...")
            if not await self._wait_for_server():
                raise Exception("服务器启动超时")

            self.initialization_status = "正在验证功能..."
            print("🔍 验证功能...")
            if not await self._verify_functionality():
                raise Exception("功能验证失败")

            self.setup_complete = True
            self.initialization_status = "初始化完成"
            print("✅ 应用初始化完成")
            return True

        except Exception as e:
            self.initialization_status = f"初始化失败: {str(e)}"
            print(f"❌ 应用初始化失败: {e}")
            return False

    async def _check_dependencies(self):
        """检查依赖"""
        try:
            import requests
            import httpx
            print("✅ 依赖检查通过")
        except ImportError as e:
            raise Exception(f"缺少依赖: {e}")

    async def _start_api_server(self) -> bool:
        """启动API服务器"""
        try:
            # 检查服务器是否已经运行
            if self.api_server.is_server_running():
                print("✅ API服务器已在运行")
                return True

            # 启动服务器
            success = self.api_server.start_server()
            if success:
                print("✅ API服务器启动成功")
            return success
        except Exception as e:
            print(f"❌ API服务器启动失败: {e}")
            return False

    async def _wait_for_server(self, timeout=60) -> bool:
        """等待服务器就绪"""
        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                health_result = await self.api_client.check_health()
                if health_result["status"] == "healthy":
                    print("✅ 服务器健康检查通过")
                    return True
            except Exception:
                pass

            await asyncio.sleep(2)
            print("⏳ 等待服务器启动...")

        return False

    async def _verify_functionality(self) -> bool:
        """验证功能"""
        try:
            # 测试健康检查
            health_result = await self.api_client.check_health()
            if health_result["status"] != "healthy":
                print(f"❌ 健康检查失败: {health_result}")
                return False

            print("✅ 功能验证通过")
            return True

        except Exception as e:
            print(f"❌ 功能验证失败: {e}")
            return False

    def _initialize_database(self):
        """初始化数据库连接"""
        try:
            self.db_session = SessionLocal()
            print("✅ 数据库连接初始化成功")
        except Exception as e:
            print(f"❌ 数据库连接初始化失败: {e}")
            self.db_session = None

    def _initialize_test_user(self):
        """初始化或获取测试用户"""
        if not self.db_session:
            print("❌ 无法初始化测试用户：数据库连接失败")
            return

        try:
            # 尝试获取现有测试用户
            self.test_user = crud.crud_user.get(self.db_session, id=self.test_user_id)

            if not self.test_user:
                # 创建新的测试用户
                test_user_data = {
                    "openid": f"gradio_test_user_{self.test_user_id}",
                    "nickname": "Gradio测试用户",
                    "email": f"gradio_test_{self.test_user_id}@example.com",
                    "age": 25,
                    "gender": 1,
                    "height": 175,
                    "weight": 70,
                    "fitness_goal": 2,
                    "experience_level": 2,
                    "activity_level": 3
                }

                self.test_user = create_test_user(self.db_session, test_user_data)
                print(f"✅ 创建测试用户成功: ID={self.test_user.id}")
            else:
                print(f"✅ 使用现有测试用户: ID={self.test_user.id}")

        except Exception as e:
            print(f"❌ 测试用户初始化失败: {e}")
            self.test_user = None

    def get_db_session(self) -> Optional[Session]:
        """获取数据库会话"""
        if not self.db_session:
            self._initialize_database()
        return self.db_session

    def get_or_create_conversation(self, session_id: str) -> Tuple[Optional[models.Conversation], bool]:
        """获取或创建会话 - 与chat.py保持一致"""
        db = self.get_db_session()
        if not db or not self.test_user:
            return None, False

        try:
            conversation = crud.crud_conversation.get_by_session_id(db, session_id=session_id)
            is_new = False

            if not conversation:
                # 创建新会话
                conversation = crud.crud_conversation.create_with_session_id(
                    db,
                    obj_in=schemas.ConversationCreate(
                        session_id=session_id,
                        user_id=self.test_user.id,
                        meta_info={"gradio_test": True}
                    )
                )
                is_new = True
            elif conversation.user_id != self.test_user.id:
                print(f"⚠️ 会话不属于当前测试用户")
                return None, False

            return conversation, is_new
        except Exception as e:
            print(f"❌ 获取或创建会话失败: {e}")
            return None, False

    def create_user_message(self, conversation_id: int, content: str, meta_info: Optional[Dict[str, Any]] = None) -> Optional[models.Message]:
        """创建用户消息 - 与chat.py保持一致"""
        db = self.get_db_session()
        if not db or not self.test_user:
            return None

        try:
            return crud.crud_message.create_with_conversation(
                db,
                obj_in=schemas.MessageCreate(
                    content=content,
                    role=MessageRole.USER,
                    meta_info=meta_info or {},
                    user_id=self.test_user.id,
                    conversation_id=conversation_id
                ),
                conversation_id=conversation_id,
                user_id=self.test_user.id
            )
        except Exception as e:
            print(f"❌ 创建用户消息失败: {e}")
            return None

    def create_assistant_message(self, conversation_id: int, content: str, meta_info: Optional[Dict[str, Any]] = None) -> Optional[models.Message]:
        """创建AI助手消息 - 与chat.py保持一致"""
        db = self.get_db_session()
        if not db or not self.test_user:
            return None

        try:
            return crud.crud_message.create_with_conversation(
                db,
                obj_in=schemas.MessageCreate(
                    content=content,
                    role=MessageRole.ASSISTANT,
                    meta_info=meta_info or {},
                    user_id=self.test_user.id,
                    conversation_id=conversation_id
                ),
                conversation_id=conversation_id,
                user_id=self.test_user.id
            )
        except Exception as e:
            print(f"❌ 创建AI助手消息失败: {e}")
            return None

    def get_conversation_messages(self, session_id: str, limit: int = 20) -> List[Dict[str, Any]]:
        """获取会话消息历史 - 与chat.py保持一致"""
        db = self.get_db_session()
        if not db:
            return []

        try:
            conversation = crud.crud_conversation.get_by_session_id(db, session_id=session_id)
            if not conversation:
                return []

            messages = crud.crud_message.get_conversation_messages_desc(
                db, conversation_id=conversation.id, skip=0, limit=limit
            )

            # 格式化消息
            formatted_messages = []
            for message in reversed(messages):  # 按时间正序显示
                formatted_messages.append({
                    "id": message.id,
                    "role": message.role.value,
                    "content": message.content,
                    "created_at": message.created_at.isoformat() if message.created_at else None,
                    "meta_info": message.meta_info or {}
                })

            return formatted_messages
        except Exception as e:
            print(f"❌ 获取会话消息失败: {e}")
            return []

    def _load_user_profiles(self) -> Dict[str, Dict[str, Any]]:
        """加载用户档案"""
        profiles = {
            "初学者男性": create_test_user_profile("beginner_male"),
            "中级女性": create_test_user_profile("intermediate_female"),
            "高级运动员": create_test_user_profile("advanced_athlete"),
            "自定义": {}
        }

        # 合并真实模型测试配置中的用户档案
        for key, profile in TEST_USER_PROFILES.items():
            profiles[f"真实模型-{key}"] = profile

        return profiles

    def _load_test_scenarios(self) -> Dict[str, List[str]]:
        """加载测试场景"""
        scenarios = {
            "健身咨询": [
                "你好，我想开始健身",
                "我的目标是减肥10公斤",
                "请帮我制定训练计划",
                "深蹲的正确姿势是什么？"
            ],
            "营养建议": [
                "我想了解健身饮食",
                "增肌期间应该吃什么？",
                "蛋白质摄入量建议",
                "减脂期间的饮食计划"
            ],
            "训练指导": [
                "胸部训练动作推荐",
                "如何提高卧推重量？",
                "训练频率建议",
                "力量训练计划制定"
            ]
        }

        # 合并真实模型测试场景
        for scenario_name, scenario_data in CONVERSATION_SCENARIOS.items():
            messages = [turn["user_message"] for turn in scenario_data]
            scenarios[f"真实模型-{scenario_name}"] = messages

        return scenarios

    def _guess_intent_from_message(self, message: str) -> str:
        """从消息内容猜测意图"""
        message_lower = message.lower()

        if any(keyword in message_lower for keyword in ["饮食", "营养", "食物", "蛋白质", "吃"]):
            return "diet_advice"
        elif any(keyword in message_lower for keyword in ["训练", "计划", "制定", "安排"]):
            return "training_plan"
        elif any(keyword in message_lower for keyword in ["健身", "锻炼", "运动", "减肥", "增肌"]):
            return "fitness_advice"
        else:
            return "general_chat"

    def _get_system_status_text(self) -> str:
        """获取系统状态文本"""
        real_model_enabled = RealModelTestConfig.is_real_model_enabled()
        default_model = RealModelTestConfig.DEFAULT_MODEL

        status_text = f"""
        **系统状态**: 🟢 运行中 | **真实模型**: {'🟢 启用' if real_model_enabled else '🔴 禁用'} | **默认模型**: {default_model}
        """
        return status_text

    def _get_model_status(self) -> Dict[str, Any]:
        """获取模型状态"""
        return {
            "真实模型启用": RealModelTestConfig.is_real_model_enabled(),
            "默认模型": RealModelTestConfig.DEFAULT_MODEL,
            "可用模型": list(RealModelTestConfig.MODEL_CONFIGS.keys()),
            "模型配置": RealModelTestConfig.get_model_config(),
            "测试用户档案数量": len(TEST_USER_PROFILES),
            "测试场景数量": len(CONVERSATION_SCENARIOS)
        }

    async def chat_with_production_api(
        self,
        message: str,
        session_id: str,
        user_profile: str,
        custom_profile: str,
        history: List[Tuple[str, str]]
    ) -> Tuple[List[Tuple[str, str]], str, Dict[str, Any]]:
        """通过生产API进行对话 - 直接调用chat.py接口"""

        if not message.strip():
            return history, "", {}

        if not self.setup_complete:
            error_msg = f"应用未初始化完成: {self.initialization_status}"
            history.append((message, error_msg))
            return history, "", {"错误": error_msg}

        # 记录开始时间
        start_time = time.time()

        try:
            # 调用生产API - 直接使用chat.py的send_message接口
            result = await self.api_client.send_message(
                message=message,
                session_id=session_id,
                user_id=self.test_user_id
            )

            if result["success"]:
                api_data = result["data"]
                response_content = api_data.get("response", api_data.get("response_content", ""))

                # 更新对话历史
                history.append((message, response_content))

                # 记录性能指标
                self.performance_metrics.append({
                    "timestamp": datetime.now(),
                    "api_response_time": result.get("response_time", 0),
                    "total_response_time": time.time() - start_time,
                    "message_length": len(message),
                    "response_length": len(response_content),
                    "intent": api_data.get('intent', 'unknown'),
                    "confidence": api_data.get('confidence', 0),
                    "session_id": session_id,
                    "status_code": result["status_code"],
                    "success": True
                })

                # 准备元数据显示 - 显示完整的API响应信息
                metadata = {
                    "🚀 API调用": "成功",
                    "📊 HTTP状态码": result["status_code"],
                    "⏱️ API响应时间": f"{result.get('response_time', 0):.3f}秒",
                    "⏱️ 总响应时间": f"{time.time() - start_time:.3f}秒",
                    "🎯 识别意图": api_data.get('intent', '未知'),
                    "📈 置信度": f"{api_data.get('confidence', 0):.2f}",
                    "🔄 当前状态": api_data.get('current_state', '未知'),
                    "🆔 会话ID": session_id,
                    "👤 用户ID": self.test_user_id,
                    "💬 消息ID": api_data.get("message_id"),
                    "📝 请求负载": result.get("request_payload", {}),
                    "🔧 元数据": api_data.get("meta_info", {}),
                    "📊 API完整响应": api_data
                }

                return history, "", metadata

            else:
                error_msg = f"API调用失败: {result['error']}"
                history.append((message, error_msg))

                # 记录失败指标
                self.performance_metrics.append({
                    "timestamp": datetime.now(),
                    "api_response_time": result.get("response_time", 0),
                    "total_response_time": time.time() - start_time,
                    "message_length": len(message),
                    "session_id": session_id,
                    "status_code": result.get("status_code"),
                    "success": False,
                    "error": result["error"]
                })

                metadata = {
                    "🚀 API调用": "失败",
                    "❌ 错误信息": result["error"],
                    "📊 HTTP状态码": result.get("status_code"),
                    "⏱️ 响应时间": f"{time.time() - start_time:.3f}秒",
                    "🆔 会话ID": session_id,
                    "📝 请求负载": result.get("request_payload", {})
                }

                return history, "", metadata

        except Exception as e:
            error_msg = f"请求处理异常: {str(e)}"
            history.append((message, error_msg))

            # 记录异常指标
            self.performance_metrics.append({
                "timestamp": datetime.now(),
                "total_response_time": time.time() - start_time,
                "message_length": len(message),
                "session_id": session_id,
                "success": False,
                "error": str(e),
                "exception": True
            })

            metadata = {
                "🚀 API调用": "异常",
                "❌ 异常信息": str(e),
                "⏱️ 响应时间": f"{time.time() - start_time:.3f}秒",
                "🆔 会话ID": session_id
            }

            return history, "", metadata

    def _get_test_user_info(self) -> Dict[str, Any]:
        """获取测试用户信息"""
        if not self.test_user:
            return {}

        return {
            "user_id": str(self.test_user.id),
            "nickname": self.test_user.nickname,
            "age": self.test_user.age,
            "gender": self.test_user.gender,
            "height": self.test_user.height,
            "weight": self.test_user.weight,
            "fitness_goal": self.test_user.fitness_goal,
            "experience_level": self.test_user.experience_level,
            "activity_level": self.test_user.activity_level
        }

    async def run_scenario_test(
        self,
        scenario_name: str,
        user_profile: str,
        progress=gr.Progress()
    ) -> Tuple[str, pd.DataFrame]:
        """运行场景测试"""

        if scenario_name not in self.test_scenarios:
            return "未找到指定的测试场景", pd.DataFrame()

        messages = self.test_scenarios[scenario_name]
        user_info = self.user_profiles.get(user_profile, {})
        conversation_id = f"scenario_test_{int(time.time())}"

        results = []

        progress(0, desc="开始场景测试...")

        for i, message in enumerate(messages):
            progress((i + 1) / len(messages), desc=f"测试消息 {i + 1}/{len(messages)}")

            start_time = time.time()

            try:
                response = await conversation_orchestrator.process_message(
                    message=message,
                    conversation_id=conversation_id,
                    user_info=user_info
                )

                response_time = time.time() - start_time
                ai_response = response.get('response_content', '')

                results.append({
                    "轮次": i + 1,
                    "用户消息": message,
                    "AI响应": ai_response[:100] + "..." if len(ai_response) > 100 else ai_response,
                    "响应时间(秒)": round(response_time, 2),
                    "意图": response.get('intent', '未知'),
                    "置信度": round(response.get('confidence', 0), 2),
                    "状态": response.get('current_state', '未知'),
                    "状态": "成功" if ai_response else "失败"
                })

            except Exception as e:
                results.append({
                    "轮次": i + 1,
                    "用户消息": message,
                    "AI响应": f"错误: {str(e)}",
                    "响应时间(秒)": round(time.time() - start_time, 2),
                    "意图": "错误",
                    "置信度": 0,
                    "状态": "错误",
                    "状态": "失败"
                })

        # 生成测试报告
        df = pd.DataFrame(results)
        success_rate = len(df[df['状态'] == '成功']) / len(df) * 100
        avg_response_time = df['响应时间(秒)'].mean()

        report = f"""
        📊 场景测试报告: {scenario_name}
        👤 用户档案: {user_profile}
        🎯 成功率: {success_rate:.1f}%
        ⏱️ 平均响应时间: {avg_response_time:.2f}秒
        📝 总轮次: {len(messages)}
        ✅ 成功轮次: {len(df[df['状态'] == '成功'])}
        ❌ 失败轮次: {len(df[df['状态'] == '失败'])}
        """

        return report, df

    def generate_performance_chart(self) -> go.Figure:
        """生成性能图表"""
        if not self.performance_metrics:
            fig = go.Figure()
            fig.add_annotation(text="暂无性能数据", x=0.5, y=0.5, showarrow=False)
            return fig

        df = pd.DataFrame(self.performance_metrics)

        # 创建响应时间趋势图
        fig = go.Figure()

        fig.add_trace(go.Scatter(
            x=df['timestamp'],
            y=df['response_time'],
            mode='lines+markers',
            name='响应时间',
            line=dict(color='blue'),
            hovertemplate='时间: %{x}<br>响应时间: %{y:.2f}秒<extra></extra>'
        ))

        fig.update_layout(
            title='AI助手响应时间趋势',
            xaxis_title='时间',
            yaxis_title='响应时间 (秒)',
            hovermode='x unified'
        )

        return fig

    def generate_intent_distribution_chart(self) -> go.Figure:
        """生成意图分布图表"""
        if not self.performance_metrics:
            fig = go.Figure()
            fig.add_annotation(text="暂无意图数据", x=0.5, y=0.5, showarrow=False)
            return fig

        df = pd.DataFrame(self.performance_metrics)
        intent_counts = df['intent'].value_counts()

        fig = go.Figure(data=[
            go.Pie(
                labels=intent_counts.index,
                values=intent_counts.values,
                hole=0.3
            )
        ])

        fig.update_layout(title='意图识别分布')
        return fig

    def clear_conversation(self) -> Tuple[List, str]:
        """清空对话"""
        return [], ""

    def export_test_results(self) -> str:
        """导出测试结果"""
        if not self.performance_metrics:
            return "暂无测试数据可导出"

        df = pd.DataFrame(self.performance_metrics)
        filename = f"test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        df.to_csv(filename, index=False, encoding='utf-8-sig')

        return f"测试结果已导出到: {filename}"

    def create_interface(self):
        """创建Gradio界面"""

        if not self.setup_complete:
            # 如果未初始化完成，显示初始化界面
            return self._create_initialization_interface()

        # 创建完整的生产环境测试界面
        return self._create_production_interface()

    def _create_initialization_interface(self):
        """创建初始化界面"""
        with gr.Blocks(
            title="智能健身AI助手 - 生产环境测试平台 (初始化中)",
            theme=gr.themes.Soft()
        ) as interface:
            gr.Markdown("# 🚀 智能健身AI助手 - 生产环境测试平台")
            gr.Markdown("## 正在初始化...")
            gr.Markdown("### 🔧 此版本直接调用生产环境API，确保测试与生产环境完全一致")

            status_display = gr.Textbox(
                value=self.initialization_status,
                label="初始化状态",
                interactive=False
            )

            init_button = gr.Button("开始初始化", variant="primary")

            async def initialize_app():
                success = await self.initialize()
                if success:
                    return "✅ 初始化完成！请刷新页面使用完整功能。"
                else:
                    return f"❌ 初始化失败: {self.initialization_status}"

            init_button.click(
                fn=initialize_app,
                outputs=status_display
            )

        return interface

    def _create_production_interface(self):
        """创建生产环境测试界面"""
        with gr.Blocks(
            title="智能健身AI助手 - 生产环境测试平台",
            theme=gr.themes.Soft(),
            css="""
            .gradio-container {
                max-width: 1400px !important;
                margin: auto !important;
            }
            .chat-container {
                height: 500px !important;
            }
            .status-box {
                background-color: #f0f8ff;
                border: 1px solid #4CAF50;
                border-radius: 5px;
                padding: 10px;
            }
            """
        ) as interface:

            gr.Markdown("# 🏋️ 智能健身AI助手 - 生产环境测试平台")
            gr.Markdown("### 🚀 直接调用生产API，确保测试与生产环境完全一致")

            # 系统状态显示
            with gr.Row():
                system_status = gr.Markdown(self._get_system_status_text(), elem_classes=["status-box"])
                refresh_status_btn = gr.Button("刷新状态", size="sm")

            with gr.Tabs():

                # 实时对话测试 - 生产API版本
                with gr.TabItem("💬 实时对话 (生产API)"):
                    with gr.Row():
                        with gr.Column(scale=2):
                            chatbot = gr.Chatbot(
                                label="对话历史",
                                height=400,
                                show_label=True
                            )

                            with gr.Row():
                                msg_input = gr.Textbox(
                                    label="输入消息",
                                    placeholder="请输入您的消息...",
                                    scale=4
                                )
                                send_btn = gr.Button("发送", variant="primary", scale=1)

                            with gr.Row():
                                clear_btn = gr.Button("清空对话")
                                export_btn = gr.Button("导出对话")

                        with gr.Column(scale=1):
                            conversation_id_input = gr.Textbox(
                                label="会话ID",
                                value=f"test_{int(time.time())}",
                                interactive=True
                            )

                            user_profile_dropdown = gr.Dropdown(
                                choices=list(self.user_profiles.keys()),
                                value="初学者男性",
                                label="用户档案"
                            )

                            custom_profile_input = gr.Textbox(
                                label="自定义档案 (JSON格式)",
                                placeholder='{"age": 25, "gender": 1, "fitness_goal": 3}',
                                lines=3,
                                visible=False
                            )

                            metadata_json = gr.JSON(
                                label="响应元数据",
                                value={}
                            )

                    # 显示/隐藏自定义档案输入
                    def toggle_custom_profile(profile):
                        return gr.update(visible=(profile == "自定义"))

                    user_profile_dropdown.change(
                        toggle_custom_profile,
                        inputs=[user_profile_dropdown],
                        outputs=[custom_profile_input]
                    )

                    # 发送消息事件 - 使用生产API
                    async def send_message_wrapper(*args):
                        return await self.chat_with_production_api(*args)

                    send_btn.click(
                        send_message_wrapper,
                        inputs=[
                            msg_input, conversation_id_input,
                            user_profile_dropdown, custom_profile_input, chatbot
                        ],
                        outputs=[chatbot, msg_input, metadata_json]
                    )

                    msg_input.submit(
                        send_message_wrapper,
                        inputs=[
                            msg_input, conversation_id_input,
                            user_profile_dropdown, custom_profile_input, chatbot
                        ],
                        outputs=[chatbot, msg_input, metadata_json]
                    )

                    clear_btn.click(
                        self.clear_conversation,
                        outputs=[chatbot, msg_input]
                    )

                # 场景测试
                with gr.TabItem("🎯 场景测试"):
                    with gr.Row():
                        scenario_dropdown = gr.Dropdown(
                            choices=list(self.test_scenarios.keys()),
                            value=list(self.test_scenarios.keys())[0],
                            label="测试场景"
                        )

                        scenario_user_profile = gr.Dropdown(
                            choices=list(self.user_profiles.keys())[:-1],  # 排除自定义
                            value="初学者男性",
                            label="用户档案"
                        )

                        run_scenario_btn = gr.Button("运行场景测试", variant="primary")

                    scenario_report = gr.Textbox(
                        label="测试报告",
                        lines=10,
                        max_lines=15
                    )

                    scenario_results = gr.Dataframe(
                        label="详细结果",
                        interactive=False
                    )

                    async def run_scenario_wrapper(*args):
                        return await self.run_scenario_test(*args)

                    run_scenario_btn.click(
                        run_scenario_wrapper,
                        inputs=[scenario_dropdown, scenario_user_profile],
                        outputs=[scenario_report, scenario_results]
                    )

                # 性能监控
                with gr.TabItem("📊 性能监控"):
                    with gr.Row():
                        refresh_charts_btn = gr.Button("刷新图表", variant="primary")
                        export_data_btn = gr.Button("导出数据")

                    export_status = gr.Textbox(label="导出状态", interactive=False)

                    with gr.Row():
                        performance_chart = gr.Plot(label="响应时间趋势")
                        intent_chart = gr.Plot(label="意图分布")

                    def refresh_charts():
                        return (
                            self.generate_performance_chart(),
                            self.generate_intent_distribution_chart()
                        )

                    refresh_charts_btn.click(
                        refresh_charts,
                        outputs=[performance_chart, intent_chart]
                    )

                    export_data_btn.click(
                        self.export_test_results,
                        outputs=[export_status]
                    )

                # 真实模型测试
                with gr.TabItem("🤖 真实模型测试"):
                    with gr.Row():
                        with gr.Column():
                            gr.Markdown("### 🔧 模型配置")
                            model_status = gr.JSON(
                                label="模型状态",
                                value=self._get_model_status()
                            )

                            run_model_test_btn = gr.Button("运行模型测试", variant="primary")

                        with gr.Column():
                            gr.Markdown("### 📊 测试结果")
                            model_test_results = gr.JSON(
                                label="模型测试结果",
                                value={}
                            )

                    async def run_model_test():
                        return await self.test_integration.run_all_tests()

                    run_model_test_btn.click(
                        run_model_test,
                        outputs=[model_test_results]
                    )

                # 帮助文档
                with gr.TabItem("📖 使用说明"):
                    gr.Markdown("""
                    ## 使用说明

                    ### 💬 实时对话测试
                    - **用户档案**: 选择预设的用户档案或使用自定义JSON格式
                    - **会话ID**: 用于标识对话会话，相同ID的消息会保持上下文
                    - **响应元数据**: 显示AI助手的处理信息，包括意图、置信度等

                    ### 🎯 场景测试
                    - **测试场景**: 预设的对话场景，模拟真实用户交互
                    - **批量测试**: 自动运行整个场景的所有消息
                    - **测试报告**: 显示成功率、响应时间等关键指标

                    ### 📊 性能监控
                    - **响应时间趋势**: 监控AI助手的响应性能
                    - **意图分布**: 分析意图识别的分布情况
                    - **数据导出**: 导出详细的测试数据用于分析

                    ### 🔧 高级功能
                    - 支持多轮对话上下文保持
                    - 实时性能指标监控
                    - 可视化测试结果分析
                    - 测试数据导出和报告生成
                    """)

        return interface


async def main():
    """主函数"""
    print("🚀 启动智能健身AI助手 - 生产环境测试平台")

    app = GradioTestApp()

    # 尝试自动初始化
    print("🔧 尝试自动初始化...")
    success = await app.initialize()

    if success:
        print("✅ 自动初始化成功")
    else:
        print("⚠️ 自动初始化失败，将显示手动初始化界面")

    interface = app.create_interface()

    # 启动应用
    interface.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        debug=True
    )


if __name__ == "__main__":
    asyncio.run(main())
