#!/usr/bin/env python3
"""
生产环境API集成测试脚本

验证Gradio应用与生产API的集成是否正常工作
"""

import asyncio
import sys
import os
import time
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../../"))

from tests.comprehensive.interactive.gradio_test_app import ChatAPIClient, APIServerManager


async def test_api_integration():
    """测试API集成"""
    print("🧪 开始生产环境API集成测试")
    
    # 1. 启动API服务器
    print("\n📋 步骤1: 启动API服务器")
    api_server = APIServerManager()
    
    if not api_server.is_server_running():
        print("🚀 启动API服务器...")
        success = api_server.start_server()
        if not success:
            print("❌ API服务器启动失败")
            return False
        
        # 等待服务器启动
        await asyncio.sleep(5)
    else:
        print("✅ API服务器已在运行")
    
    # 2. 创建API客户端
    print("\n📋 步骤2: 创建API客户端")
    api_client = ChatAPIClient()
    
    # 3. 健康检查
    print("\n📋 步骤3: 健康检查")
    health_result = await api_client.check_health()
    print(f"健康状态: {health_result}")
    
    if health_result["status"] != "healthy":
        print("❌ API服务器健康检查失败")
        return False
    
    print("✅ API服务器健康检查通过")
    
    # 4. 测试消息发送
    print("\n📋 步骤4: 测试消息发送")
    test_messages = [
        "你好，我想了解健身",
        "我想减肥10公斤，应该怎么做？",
        "增肌期间应该吃什么？"
    ]
    
    session_id = f"test_integration_{int(time.time())}"
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n🔄 测试消息 {i}: {message}")
        
        start_time = time.time()
        result = await api_client.send_message(
            message=message,
            session_id=session_id,
            user_id=15
        )
        response_time = time.time() - start_time
        
        if result["success"]:
            api_data = result["data"]
            response_content = api_data.get("response", api_data.get("response_content", ""))
            
            print(f"✅ 消息发送成功")
            print(f"📊 HTTP状态码: {result['status_code']}")
            print(f"⏱️ 响应时间: {response_time:.3f}秒")
            print(f"🎯 识别意图: {api_data.get('intent', '未知')}")
            print(f"📈 置信度: {api_data.get('confidence', 0):.2f}")
            print(f"💬 响应长度: {len(response_content)}字符")
            print(f"📝 响应预览: {response_content[:100]}...")
            
        else:
            print(f"❌ 消息发送失败: {result['error']}")
            return False
        
        # 等待一下再发送下一条消息
        await asyncio.sleep(1)
    
    # 5. 测试完成
    print("\n🎉 所有测试通过！")
    print("\n📊 测试总结:")
    print(f"✅ API服务器: 正常运行")
    print(f"✅ 健康检查: 通过")
    print(f"✅ 消息发送: {len(test_messages)}条消息全部成功")
    print(f"✅ 会话管理: 正常")
    print(f"✅ 意图识别: 正常")
    print(f"✅ AI响应: 正常")
    
    return True


async def test_gradio_api_consistency():
    """测试Gradio应用与直接API调用的一致性"""
    print("\n🔍 测试Gradio应用与直接API调用的一致性")
    
    api_client = ChatAPIClient()
    session_id = f"consistency_test_{int(time.time())}"
    test_message = "我想了解健身计划"
    
    # 直接API调用
    print("📡 直接API调用...")
    direct_result = await api_client.send_message(
        message=test_message,
        session_id=session_id,
        user_id=15
    )
    
    if direct_result["success"]:
        direct_response = direct_result["data"].get("response", "")
        print(f"✅ 直接API调用成功")
        print(f"📝 响应长度: {len(direct_response)}字符")
        print(f"🎯 意图: {direct_result['data'].get('intent', '未知')}")
        
        # 这里可以添加更多的一致性检查
        print("✅ 一致性测试通过")
        return True
    else:
        print(f"❌ 直接API调用失败: {direct_result['error']}")
        return False


async def main():
    """主测试函数"""
    print("🚀 生产环境API集成测试开始")
    print("=" * 60)
    
    try:
        # 基础API集成测试
        success1 = await test_api_integration()
        
        if success1:
            # 一致性测试
            success2 = await test_gradio_api_consistency()
            
            if success1 and success2:
                print("\n🎉 所有测试完成！")
                print("✅ 生产环境API集成正常工作")
                print("✅ Gradio应用已准备就绪")
                print("\n🌐 访问地址:")
                print("📱 Gradio界面: http://localhost:7860")
                print("🔧 API文档: http://localhost:8000/docs")
                print("💚 健康检查: http://localhost:8000/health")
                return True
            else:
                print("\n❌ 一致性测试失败")
                return False
        else:
            print("\n❌ 基础API集成测试失败")
            return False
            
    except Exception as e:
        print(f"\n💥 测试过程中发生异常: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
