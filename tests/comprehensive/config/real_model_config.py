"""
真实模型集成测试配置
用于端到端测试时接入真实的LLM模型
"""
import os
from typing import Dict, Any

class RealModelTestConfig:
    """真实模型测试配置"""
    
    # 是否启用真实模型（默认False，避免意外调用）
    ENABLE_REAL_MODELS: bool = os.getenv("ENABLE_REAL_MODELS", "false").lower() == "true"
    
    # 模型配置
    MODEL_CONFIGS = {
        "qwen": {
            "api_key": os.getenv("QWEN_API_KEY", "sk-5f104bfa343e42a8be683b05f3d259a9"),
            "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
            "model": "qwen-max",
            "temperature": 0.7,
            "max_tokens": 2000,
            "timeout": 30
        },
        "deepseek": {
            "api_key": os.getenv("DEEPSEEK_API_KEY", "91cca13f-0e5f-428e-8f69-f0c7f9ec4218"),
            "base_url": "https://ark.cn-beijing.volces.com/api/v3",
            "model": "deepseek-r1-250120",
            "temperature": 0.7,
            "max_tokens": 2000,
            "timeout": 30
        }
    }
    
    # 默认使用的模型
    DEFAULT_MODEL = "qwen"
    
    # 测试用的提示词模板
    TEST_PROMPTS = {
        "fitness_advice": """
你是一位专业的健身教练。用户询问：{user_query}

请提供专业、详细的健身建议，回答应该：
1. 长度在50-200字之间
2. 包含具体的建议
3. 语言友好、专业
4. 针对用户的具体情况

请直接回答，不要包含"根据您的问题"等开头语。
        """,
        
        "diet_advice": """
你是一位专业的营养师。用户询问：{user_query}

请提供专业、详细的饮食建议，回答应该：
1. 长度在80-300字之间
2. 包含具体的营养建议
3. 语言友好、专业
4. 针对用户的具体情况

请直接回答，不要包含"根据您的问题"等开头语。
        """,
        
        "general_chat": """
你是一位友好的健身AI助手。用户说：{user_query}

请友好地回应，回答应该：
1. 长度在30-100字之间
2. 语言自然、友好
3. 如果涉及健身相关，可以简单引导

请直接回答。
        """
    }
    
    @classmethod
    def get_model_config(cls, model_name: str = None) -> Dict[str, Any]:
        """获取模型配置"""
        model_name = model_name or cls.DEFAULT_MODEL
        return cls.MODEL_CONFIGS.get(model_name, cls.MODEL_CONFIGS[cls.DEFAULT_MODEL])
    
    @classmethod
    def get_prompt_template(cls, intent: str) -> str:
        """获取提示词模板"""
        return cls.TEST_PROMPTS.get(intent, cls.TEST_PROMPTS["general_chat"])
    
    @classmethod
    def is_real_model_enabled(cls) -> bool:
        """检查是否启用真实模型"""
        return cls.ENABLE_REAL_MODELS and bool(cls.get_model_config()["api_key"])


class MockModelConfig:
    """Mock模型配置，用于不启用真实模型时的测试"""
    
    MOCK_RESPONSES = {
        "fitness_advice": "作为您的健身教练，我建议您从基础动作开始，如深蹲、俯卧撑和平板支撑。每周进行3-4次训练，每次30-45分钟。记住循序渐进，注意正确的动作姿势，避免受伤。配合适当的休息和营养补充，您会看到明显的进步。",
        
        "diet_advice": "均衡饮食是健身成功的关键。建议您每天摄入足够的蛋白质（体重每公斤1.2-2.0克），选择优质碳水化合物如燕麦、糙米，多吃新鲜蔬菜和水果。控制总热量摄入，少食多餐，避免高糖高脂食物。充足的水分摄入也很重要，每天至少2升水。",
        
        "general_chat": "您好！我是您的健身AI助手，很高兴为您提供帮助。有什么健身或营养方面的问题我都可以为您解答。",
        
        "training_plan": "根据您的情况，我为您制定了一个循序渐进的训练计划。第一周以适应性训练为主，包括基础力量训练和有氧运动。随着体能提升，我们会逐步增加训练强度和复杂度。"
    }
    
    @classmethod
    def get_mock_response(cls, intent: str, user_query: str = "") -> str:
        """获取Mock响应"""
        base_response = cls.MOCK_RESPONSES.get(intent, cls.MOCK_RESPONSES["general_chat"])
        
        # 根据用户查询调整响应
        if "减肥" in user_query:
            return base_response + " 对于减肥目标，建议结合有氧运动和力量训练，控制饮食热量摄入。"
        elif "增肌" in user_query:
            return base_response + " 对于增肌目标，重点进行力量训练，增加蛋白质摄入。"
        
        return base_response


# 测试用的用户档案
TEST_USER_PROFILES = {
    "beginner_male": {
        "age": 25,
        "gender": 1,
        "height": 175,
        "weight": 70,
        "fitness_goal": 3,  # 增肌
        "experience_level": 1,  # 初级
        "activity_level": 2,
        "health_conditions": [],
        "allergies": [],
        "user_id": "test_user_beginner_male",
        "is_test_user": True
    },
    "intermediate_female": {
        "age": 30,
        "gender": 2,
        "height": 165,
        "weight": 60,
        "fitness_goal": 1,  # 减肥
        "experience_level": 2,  # 中级
        "activity_level": 3,
        "health_conditions": [],
        "allergies": [],
        "user_id": "test_user_intermediate_female",
        "is_test_user": True
    },
    "advanced_athlete": {
        "age": 28,
        "gender": 1,
        "height": 180,
        "weight": 85,
        "fitness_goal": 3,  # 增肌
        "experience_level": 3,  # 高级
        "activity_level": 5,
        "health_conditions": [],
        "allergies": [],
        "user_id": "test_user_advanced_athlete",
        "is_test_user": True
    }
}

# 对话场景配置
CONVERSATION_SCENARIOS = {
    "fitness_consultation": [
        {
            "user_message": "你好，我想开始健身但不知道从哪里开始",
            "expected_intent": "fitness_advice",
            "expected_keywords": ["健身", "开始", "建议"],
            "min_response_length": 50,
            "max_response_time": 5.0
        },
        {
            "user_message": "我的目标是减肥10公斤，应该怎么做？",
            "expected_intent": "fitness_advice",
            "expected_keywords": ["减肥", "目标", "建议"],
            "min_response_length": 80,
            "max_response_time": 5.0
        },
        {
            "user_message": "请帮我制定一个训练计划",
            "expected_intent": "training_plan",
            "expected_keywords": ["训练计划", "制定"],
            "min_response_length": 100,
            "max_response_time": 10.0
        }
    ],
    
    "diet_consultation": [
        {
            "user_message": "饮食方面有什么建议？",
            "expected_intent": "diet_advice",
            "expected_keywords": ["饮食", "建议"],
            "min_response_length": 80,
            "max_response_time": 5.0
        },
        {
            "user_message": "我应该吃什么来增肌？",
            "expected_intent": "diet_advice",
            "expected_keywords": ["增肌", "饮食", "蛋白质"],
            "min_response_length": 80,
            "max_response_time": 5.0
        }
    ]
}
