"""
负载性能测试

测试AI助手系统在不同负载条件下的性能表现，包括响应时间、吞吐量、并发处理能力等
"""

import asyncio
import time
import statistics
import psutil
import gc
from typing import List, Dict, Any, Tuple
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import aiohttp
import json

from app.services.ai_assistant.conversation.orchestrator import conversation_orchestrator
from tests.utils.test_helpers import create_test_user_profile, generate_random_user_profile


@dataclass
class PerformanceMetrics:
    """性能指标数据结构"""
    test_name: str
    total_requests: int
    successful_requests: int
    failed_requests: int
    avg_response_time: float
    min_response_time: float
    max_response_time: float
    p95_response_time: float
    p99_response_time: float
    requests_per_second: float
    memory_usage_mb: float
    cpu_usage_percent: float
    error_rate: float


class PerformanceTestSuite:
    """性能测试套件"""
    
    def __init__(self):
        self.results: List[PerformanceMetrics] = []
        self.test_messages = [
            "你好，我想了解健身",
            "我的目标是减肥10公斤",
            "请帮我制定训练计划",
            "深蹲的正确姿势是什么？",
            "我应该如何安排训练频率？",
            "增肌期间的饮食建议",
            "如何提高卧推重量？",
            "有氧运动的最佳时间",
            "力量训练后的恢复方法",
            "健身新手常见错误"
        ]
    
    async def test_single_user_performance(
        self, 
        num_requests: int = 100,
        delay_between_requests: float = 0.1
    ) -> PerformanceMetrics:
        """测试单用户性能"""
        test_name = f"单用户性能测试({num_requests}请求)"
        
        user_profile = create_test_user_profile("beginner_male")
        conversation_id = f"perf_test_single_{int(time.time())}"
        
        response_times = []
        successful_requests = 0
        failed_requests = 0
        
        # 记录初始系统状态
        initial_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        start_time = time.time()
        
        for i in range(num_requests):
            message = self.test_messages[i % len(self.test_messages)]
            
            request_start = time.time()
            
            try:
                response = await conversation_orchestrator.process_message(
                    message=message,
                    conversation_id=conversation_id,
                    user_info=user_profile
                )
                
                request_time = time.time() - request_start
                response_times.append(request_time)
                
                if response and response.get('response_content'):
                    successful_requests += 1
                else:
                    failed_requests += 1
                    
            except Exception as e:
                failed_requests += 1
                response_times.append(time.time() - request_start)
                print(f"请求 {i+1} 失败: {e}")
            
            # 请求间延迟
            if delay_between_requests > 0:
                await asyncio.sleep(delay_between_requests)
        
        total_time = time.time() - start_time
        
        # 记录最终系统状态
        final_memory = psutil.Process().memory_info().rss / 1024 / 1024
        cpu_usage = psutil.cpu_percent(interval=1)
        
        # 计算性能指标
        metrics = PerformanceMetrics(
            test_name=test_name,
            total_requests=num_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            avg_response_time=statistics.mean(response_times) if response_times else 0,
            min_response_time=min(response_times) if response_times else 0,
            max_response_time=max(response_times) if response_times else 0,
            p95_response_time=statistics.quantiles(response_times, n=20)[18] if len(response_times) >= 20 else 0,
            p99_response_time=statistics.quantiles(response_times, n=100)[98] if len(response_times) >= 100 else 0,
            requests_per_second=num_requests / total_time,
            memory_usage_mb=final_memory - initial_memory,
            cpu_usage_percent=cpu_usage,
            error_rate=failed_requests / num_requests * 100
        )
        
        self.results.append(metrics)
        return metrics
    
    async def test_concurrent_users_performance(
        self, 
        num_users: int = 10,
        requests_per_user: int = 10
    ) -> PerformanceMetrics:
        """测试并发用户性能"""
        test_name = f"并发用户性能测试({num_users}用户x{requests_per_user}请求)"
        
        async def single_user_load(user_id: int):
            """单个用户的负载测试"""
            user_profile = generate_random_user_profile()
            conversation_id = f"perf_test_concurrent_{user_id}_{int(time.time())}"
            
            user_response_times = []
            user_successful = 0
            user_failed = 0
            
            for i in range(requests_per_user):
                message = self.test_messages[i % len(self.test_messages)]
                
                request_start = time.time()
                
                try:
                    response = await conversation_orchestrator.process_message(
                        message=f"{message} (用户{user_id})",
                        conversation_id=conversation_id,
                        user_info=user_profile
                    )
                    
                    request_time = time.time() - request_start
                    user_response_times.append(request_time)
                    
                    if response and response.get('response_content'):
                        user_successful += 1
                    else:
                        user_failed += 1
                        
                except Exception as e:
                    user_failed += 1
                    user_response_times.append(time.time() - request_start)
            
            return {
                "user_id": user_id,
                "response_times": user_response_times,
                "successful": user_successful,
                "failed": user_failed
            }
        
        # 记录初始系统状态
        initial_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        start_time = time.time()
        
        # 并发执行所有用户的负载测试
        tasks = [single_user_load(i) for i in range(num_users)]
        user_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        total_time = time.time() - start_time
        
        # 记录最终系统状态
        final_memory = psutil.Process().memory_info().rss / 1024 / 1024
        cpu_usage = psutil.cpu_percent(interval=1)
        
        # 汇总所有用户的结果
        all_response_times = []
        total_successful = 0
        total_failed = 0
        
        for result in user_results:
            if isinstance(result, dict):
                all_response_times.extend(result["response_times"])
                total_successful += result["successful"]
                total_failed += result["failed"]
            else:
                # 处理异常情况
                total_failed += requests_per_user
        
        total_requests = num_users * requests_per_user
        
        # 计算性能指标
        metrics = PerformanceMetrics(
            test_name=test_name,
            total_requests=total_requests,
            successful_requests=total_successful,
            failed_requests=total_failed,
            avg_response_time=statistics.mean(all_response_times) if all_response_times else 0,
            min_response_time=min(all_response_times) if all_response_times else 0,
            max_response_time=max(all_response_times) if all_response_times else 0,
            p95_response_time=statistics.quantiles(all_response_times, n=20)[18] if len(all_response_times) >= 20 else 0,
            p99_response_time=statistics.quantiles(all_response_times, n=100)[98] if len(all_response_times) >= 100 else 0,
            requests_per_second=total_requests / total_time,
            memory_usage_mb=final_memory - initial_memory,
            cpu_usage_percent=cpu_usage,
            error_rate=total_failed / total_requests * 100
        )
        
        self.results.append(metrics)
        return metrics
    
    async def test_stress_performance(
        self, 
        duration_seconds: int = 60,
        target_rps: int = 50
    ) -> PerformanceMetrics:
        """压力测试"""
        test_name = f"压力测试({duration_seconds}秒, 目标{target_rps}RPS)"
        
        response_times = []
        successful_requests = 0
        failed_requests = 0
        
        # 记录初始系统状态
        initial_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        start_time = time.time()
        end_time = start_time + duration_seconds
        
        request_interval = 1.0 / target_rps
        next_request_time = start_time
        
        while time.time() < end_time:
            current_time = time.time()
            
            if current_time >= next_request_time:
                # 生成随机用户和消息
                user_profile = generate_random_user_profile()
                message = self.test_messages[successful_requests % len(self.test_messages)]
                conversation_id = f"stress_test_{successful_requests}_{int(current_time)}"
                
                request_start = time.time()
                
                try:
                    response = await conversation_orchestrator.process_message(
                        message=message,
                        conversation_id=conversation_id,
                        user_info=user_profile
                    )
                    
                    request_time = time.time() - request_start
                    response_times.append(request_time)
                    
                    if response and response.get('response_content'):
                        successful_requests += 1
                    else:
                        failed_requests += 1
                        
                except Exception as e:
                    failed_requests += 1
                    response_times.append(time.time() - request_start)
                
                next_request_time += request_interval
            else:
                # 短暂等待
                await asyncio.sleep(0.001)
        
        total_time = time.time() - start_time
        total_requests = successful_requests + failed_requests
        
        # 记录最终系统状态
        final_memory = psutil.Process().memory_info().rss / 1024 / 1024
        cpu_usage = psutil.cpu_percent(interval=1)
        
        # 计算性能指标
        metrics = PerformanceMetrics(
            test_name=test_name,
            total_requests=total_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            avg_response_time=statistics.mean(response_times) if response_times else 0,
            min_response_time=min(response_times) if response_times else 0,
            max_response_time=max(response_times) if response_times else 0,
            p95_response_time=statistics.quantiles(response_times, n=20)[18] if len(response_times) >= 20 else 0,
            p99_response_time=statistics.quantiles(response_times, n=100)[98] if len(response_times) >= 100 else 0,
            requests_per_second=total_requests / total_time,
            memory_usage_mb=final_memory - initial_memory,
            cpu_usage_percent=cpu_usage,
            error_rate=failed_requests / total_requests * 100 if total_requests > 0 else 0
        )
        
        self.results.append(metrics)
        return metrics
    
    async def test_memory_leak(
        self, 
        num_iterations: int = 1000,
        gc_interval: int = 100
    ) -> PerformanceMetrics:
        """内存泄漏测试"""
        test_name = f"内存泄漏测试({num_iterations}次迭代)"
        
        memory_samples = []
        response_times = []
        successful_requests = 0
        failed_requests = 0
        
        user_profile = create_test_user_profile("beginner_male")
        
        start_time = time.time()
        
        for i in range(num_iterations):
            # 记录内存使用
            if i % 10 == 0:  # 每10次迭代记录一次内存
                memory_mb = psutil.Process().memory_info().rss / 1024 / 1024
                memory_samples.append(memory_mb)
            
            conversation_id = f"memory_test_{i}"
            message = self.test_messages[i % len(self.test_messages)]
            
            request_start = time.time()
            
            try:
                response = await conversation_orchestrator.process_message(
                    message=message,
                    conversation_id=conversation_id,
                    user_info=user_profile
                )
                
                request_time = time.time() - request_start
                response_times.append(request_time)
                
                if response and response.get('response_content'):
                    successful_requests += 1
                else:
                    failed_requests += 1
                    
            except Exception as e:
                failed_requests += 1
                response_times.append(time.time() - request_start)
            
            # 定期垃圾回收
            if i % gc_interval == 0:
                gc.collect()
        
        total_time = time.time() - start_time
        total_requests = num_iterations
        
        # 分析内存趋势
        initial_memory = memory_samples[0] if memory_samples else 0
        final_memory = memory_samples[-1] if memory_samples else 0
        memory_growth = final_memory - initial_memory
        
        # 计算性能指标
        metrics = PerformanceMetrics(
            test_name=test_name,
            total_requests=total_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            avg_response_time=statistics.mean(response_times) if response_times else 0,
            min_response_time=min(response_times) if response_times else 0,
            max_response_time=max(response_times) if response_times else 0,
            p95_response_time=statistics.quantiles(response_times, n=20)[18] if len(response_times) >= 20 else 0,
            p99_response_time=statistics.quantiles(response_times, n=100)[98] if len(response_times) >= 100 else 0,
            requests_per_second=total_requests / total_time,
            memory_usage_mb=memory_growth,
            cpu_usage_percent=psutil.cpu_percent(interval=1),
            error_rate=failed_requests / total_requests * 100
        )
        
        self.results.append(metrics)
        return metrics
    
    def generate_performance_report(self) -> str:
        """生成性能测试报告"""
        if not self.results:
            return "无性能测试结果"
        
        report = f"""
🚀 性能测试报告
{'='*80}

📊 测试概览:
   - 总测试数: {len(self.results)}
   - 测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}

"""
        
        for i, metrics in enumerate(self.results, 1):
            report += f"""
{i}. {metrics.test_name}
   {'─'*60}
   📈 请求统计:
      - 总请求数: {metrics.total_requests}
      - 成功请求: {metrics.successful_requests}
      - 失败请求: {metrics.failed_requests}
      - 错误率: {metrics.error_rate:.2f}%
   
   ⏱️ 响应时间:
      - 平均响应时间: {metrics.avg_response_time:.3f}秒
      - 最小响应时间: {metrics.min_response_time:.3f}秒
      - 最大响应时间: {metrics.max_response_time:.3f}秒
      - P95响应时间: {metrics.p95_response_time:.3f}秒
      - P99响应时间: {metrics.p99_response_time:.3f}秒
   
   🔥 吞吐量:
      - 每秒请求数: {metrics.requests_per_second:.2f} RPS
   
   💾 资源使用:
      - 内存使用: {metrics.memory_usage_mb:.2f} MB
      - CPU使用率: {metrics.cpu_usage_percent:.2f}%

"""
        
        # 性能评估
        report += self._generate_performance_assessment()
        
        return report
    
    def _generate_performance_assessment(self) -> str:
        """生成性能评估"""
        if not self.results:
            return ""
        
        # 计算平均指标
        avg_response_time = sum(r.avg_response_time for r in self.results) / len(self.results)
        avg_rps = sum(r.requests_per_second for r in self.results) / len(self.results)
        avg_error_rate = sum(r.error_rate for r in self.results) / len(self.results)
        
        assessment = f"""
🎯 性能评估:
   {'─'*60}
   平均响应时间: {avg_response_time:.3f}秒 {'✅' if avg_response_time < 2.0 else '⚠️' if avg_response_time < 5.0 else '❌'}
   平均吞吐量: {avg_rps:.2f} RPS {'✅' if avg_rps > 10 else '⚠️' if avg_rps > 5 else '❌'}
   平均错误率: {avg_error_rate:.2f}% {'✅' if avg_error_rate < 1 else '⚠️' if avg_error_rate < 5 else '❌'}

📋 性能建议:
"""
        
        if avg_response_time > 5.0:
            assessment += "   - 响应时间过长，建议优化LLM调用或增加缓存\n"
        
        if avg_rps < 10:
            assessment += "   - 吞吐量较低，建议优化并发处理能力\n"
        
        if avg_error_rate > 5:
            assessment += "   - 错误率较高，建议检查错误处理和系统稳定性\n"
        
        if all(r.memory_usage_mb > 100 for r in self.results):
            assessment += "   - 内存使用较高，建议检查内存泄漏\n"
        
        return assessment


async def main():
    """主函数 - 运行性能测试"""
    suite = PerformanceTestSuite()
    
    print("🚀 开始性能测试...")
    
    # 单用户性能测试
    print("\n1. 单用户性能测试...")
    await suite.test_single_user_performance(num_requests=50)
    
    # 并发用户性能测试
    print("\n2. 并发用户性能测试...")
    await suite.test_concurrent_users_performance(num_users=5, requests_per_user=10)
    
    # 压力测试
    print("\n3. 压力测试...")
    await suite.test_stress_performance(duration_seconds=30, target_rps=20)
    
    # 内存泄漏测试
    print("\n4. 内存泄漏测试...")
    await suite.test_memory_leak(num_iterations=100)
    
    # 生成报告
    print("\n" + "="*80)
    print(suite.generate_performance_report())


if __name__ == "__main__":
    asyncio.run(main())
