#!/usr/bin/env python3
"""
测试运行脚本

提供统一的测试执行入口，支持不同类型的测试运行和报告生成
"""

import os
import sys
import argparse
import subprocess
import time
from pathlib import Path
from typing import List, Dict, Any
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


class TestRunner:
    """测试运行器"""
    
    def __init__(self):
        self.project_root = project_root
        self.test_root = Path(__file__).parent
        self.results = {}
        
    def run_unit_tests(self, verbose: bool = False) -> Dict[str, Any]:
        """运行单元测试"""
        print("🧪 运行单元测试...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_root / "unit"),
            "-v" if verbose else "-q",
            "--tb=short",
            "--cov=app",
            "--cov-report=term-missing",
            "--cov-report=html:htmlcov/unit",
            "--junit-xml=test-results/unit-tests.xml"
        ]
        
        result = self._run_command(cmd, "单元测试")
        self.results["unit_tests"] = result
        return result
    
    def run_integration_tests(self, verbose: bool = False) -> Dict[str, Any]:
        """运行集成测试"""
        print("🔗 运行集成测试...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_root / "integration"),
            "-v" if verbose else "-q",
            "--tb=short",
            "--junit-xml=test-results/integration-tests.xml"
        ]
        
        result = self._run_command(cmd, "集成测试")
        self.results["integration_tests"] = result
        return result
    
    def run_e2e_tests(self, verbose: bool = False) -> Dict[str, Any]:
        """运行端到端测试"""
        print("🎯 运行端到端测试...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_root / "e2e"),
            "-v" if verbose else "-q",
            "--tb=short",
            "-s",  # 不捕获输出，便于查看进度
            "--junit-xml=test-results/e2e-tests.xml"
        ]
        
        result = self._run_command(cmd, "端到端测试")
        self.results["e2e_tests"] = result
        return result
    
    def run_performance_tests(self, verbose: bool = False) -> Dict[str, Any]:
        """运行性能测试"""
        print("🚀 运行性能测试...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_root / "performance"),
            "-v" if verbose else "-q",
            "--tb=short",
            "-s",
            "-m", "not slow",  # 默认跳过慢速测试
            "--junit-xml=test-results/performance-tests.xml"
        ]
        
        result = self._run_command(cmd, "性能测试")
        self.results["performance_tests"] = result
        return result
    
    def run_websocket_tests(self, verbose: bool = False) -> Dict[str, Any]:
        """运行WebSocket测试"""
        print("🔌 运行WebSocket测试...")
        
        # 首先检查服务是否运行
        if not self._check_service_running():
            print("⚠️  服务未运行，跳过WebSocket测试")
            return {"success": False, "message": "服务未运行"}
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_root / "interactive"),
            "-v" if verbose else "-q",
            "--tb=short",
            "-m", "websocket",
            "--junit-xml=test-results/websocket-tests.xml"
        ]
        
        result = self._run_command(cmd, "WebSocket测试")
        self.results["websocket_tests"] = result
        return result
    
    def run_interactive_tests(self) -> Dict[str, Any]:
        """运行交互式测试"""
        print("🎮 启动交互式测试界面...")
        
        try:
            # 启动Gradio测试应用
            gradio_script = self.test_root / "interactive" / "gradio_test_app.py"
            
            if gradio_script.exists():
                print(f"启动Gradio测试应用: {gradio_script}")
                subprocess.Popen([
                    "python", str(gradio_script)
                ])
                print("✅ Gradio测试应用已启动，请访问 http://localhost:7860")
                return {"success": True, "url": "http://localhost:7860"}
            else:
                print("❌ Gradio测试应用文件不存在")
                return {"success": False, "message": "测试应用文件不存在"}
                
        except Exception as e:
            print(f"❌ 启动交互式测试失败: {e}")
            return {"success": False, "message": str(e)}
    
    def run_all_tests(self, verbose: bool = False, skip_slow: bool = True) -> Dict[str, Any]:
        """运行所有测试"""
        print("🎯 运行完整测试套件...")
        
        # 创建结果目录
        os.makedirs("test-results", exist_ok=True)
        os.makedirs("htmlcov", exist_ok=True)
        
        start_time = time.time()
        
        # 按顺序运行测试
        test_functions = [
            ("单元测试", self.run_unit_tests),
            ("集成测试", self.run_integration_tests),
            ("端到端测试", self.run_e2e_tests),
        ]
        
        if not skip_slow:
            test_functions.append(("性能测试", self.run_performance_tests))
        
        all_passed = True
        
        for test_name, test_func in test_functions:
            print(f"\n{'='*60}")
            result = test_func(verbose)
            
            if not result.get("success", False):
                all_passed = False
                print(f"❌ {test_name}失败")
            else:
                print(f"✅ {test_name}通过")
        
        total_time = time.time() - start_time
        
        # 生成总结报告
        summary = self._generate_summary_report(total_time, all_passed)
        
        print(f"\n{'='*60}")
        print("📊 测试总结报告")
        print(f"{'='*60}")
        print(summary)
        
        return {
            "success": all_passed,
            "total_time": total_time,
            "summary": summary,
            "results": self.results
        }
    
    def _run_command(self, cmd: List[str], test_name: str) -> Dict[str, Any]:
        """运行命令并返回结果"""
        try:
            print(f"执行命令: {' '.join(cmd)}")
            
            result = subprocess.run(
                cmd,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            success = result.returncode == 0
            
            return {
                "success": success,
                "returncode": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "command": " ".join(cmd)
            }
            
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "error": "测试超时",
                "command": " ".join(cmd)
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "command": " ".join(cmd)
            }
    
    def _check_service_running(self, host: str = "localhost", port: int = 8000) -> bool:
        """检查服务是否运行"""
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex((host, port))
            sock.close()
            return result == 0
        except Exception:
            return False
    
    def _generate_summary_report(self, total_time: float, all_passed: bool) -> str:
        """生成总结报告"""
        report = f"""
测试执行完成！

⏱️  总执行时间: {total_time:.2f}秒
🎯 总体结果: {'✅ 全部通过' if all_passed else '❌ 存在失败'}

📋 详细结果:
"""
        
        for test_type, result in self.results.items():
            status = "✅ 通过" if result.get("success", False) else "❌ 失败"
            report += f"   {test_type}: {status}\n"
            
            if not result.get("success", False) and "error" in result:
                report += f"      错误: {result['error']}\n"
        
        report += f"""
📁 测试报告位置:
   - JUnit XML: test-results/
   - 覆盖率报告: htmlcov/
   - 详细日志: 控制台输出

🔧 后续建议:
"""
        
        if not all_passed:
            report += "   - 检查失败的测试用例\n"
            report += "   - 查看详细错误信息\n"
            report += "   - 修复问题后重新运行测试\n"
        else:
            report += "   - 所有测试通过，可以进行部署\n"
            report += "   - 定期运行测试确保代码质量\n"
        
        return report


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="智能健身AI助手测试运行器")
    
    parser.add_argument(
        "test_type",
        choices=["unit", "integration", "e2e", "performance", "websocket", "interactive", "all"],
        help="要运行的测试类型"
    )
    
    parser.add_argument(
        "-v", "--verbose",
        action="store_true",
        help="详细输出"
    )
    
    parser.add_argument(
        "--skip-slow",
        action="store_true",
        default=True,
        help="跳过慢速测试（默认启用）"
    )
    
    parser.add_argument(
        "--include-slow",
        action="store_true",
        help="包含慢速测试"
    )
    
    args = parser.parse_args()
    
    # 处理慢速测试选项
    skip_slow = args.skip_slow and not args.include_slow
    
    runner = TestRunner()
    
    print("🏋️ 智能健身AI助手测试运行器")
    print("="*60)
    
    # 根据参数运行相应的测试
    if args.test_type == "unit":
        result = runner.run_unit_tests(args.verbose)
    elif args.test_type == "integration":
        result = runner.run_integration_tests(args.verbose)
    elif args.test_type == "e2e":
        result = runner.run_e2e_tests(args.verbose)
    elif args.test_type == "performance":
        result = runner.run_performance_tests(args.verbose)
    elif args.test_type == "websocket":
        result = runner.run_websocket_tests(args.verbose)
    elif args.test_type == "interactive":
        result = runner.run_interactive_tests()
    elif args.test_type == "all":
        result = runner.run_all_tests(args.verbose, skip_slow)
    
    # 输出结果
    if result.get("success", False):
        print(f"\n✅ {args.test_type}测试完成")
        sys.exit(0)
    else:
        print(f"\n❌ {args.test_type}测试失败")
        if "error" in result:
            print(f"错误: {result['error']}")
        sys.exit(1)


if __name__ == "__main__":
    main()
