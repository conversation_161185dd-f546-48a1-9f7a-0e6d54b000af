"""
多轮对话端到端测试

测试完整的健身咨询对话场景，包括状态转换、上下文保持、意图切换等
"""

import pytest
import asyncio
import time
from typing import List, Dict, Any
from dataclasses import dataclass

from app.services.ai_assistant.conversation.orchestrator import conversation_orchestrator
from tests.utils.test_helpers import create_test_user_profile


@dataclass
class ConversationTurn:
    """对话轮次数据结构"""
    user_message: str
    expected_intent: str = None
    expected_keywords: List[str] = None
    min_response_length: int = 10
    max_response_time: float = 5.0
    should_contain_user_info: bool = False
    expected_state: str = None


@dataclass
class ConversationScenario:
    """对话场景数据结构"""
    name: str
    description: str
    user_profile: Dict[str, Any]
    turns: List[ConversationTurn]
    success_criteria: Dict[str, Any]


class TestMultiTurnConversations:
    """多轮对话测试类"""
    
    @pytest.fixture
    def fitness_consultation_scenario(self):
        """健身咨询场景"""
        return ConversationScenario(
            name="健身咨询完整流程",
            description="新用户从咨询到制定计划的完整对话流程",
            user_profile=create_test_user_profile("beginner_male"),
            turns=[
                ConversationTurn(
                    user_message="你好，我想开始健身但不知道从哪里开始",
                    expected_intent="fitness_advice",
                    expected_keywords=["健身", "开始", "建议"],
                    min_response_length=50,
                    expected_state="fitness_advice"
                ),
                ConversationTurn(
                    user_message="我的目标是增肌，现在体重70公斤",
                    expected_intent="fitness_advice",
                    expected_keywords=["增肌", "目标"],
                    should_contain_user_info=True,
                    expected_state="fitness_advice"
                ),
                ConversationTurn(
                    user_message="请帮我制定一个训练计划",
                    expected_intent="training_plan",
                    expected_keywords=["训练计划", "制定"],
                    min_response_length=100,
                    expected_state="training_plan"
                ),
                ConversationTurn(
                    user_message="深蹲的正确姿势是什么？",
                    expected_intent="exercise_action",
                    expected_keywords=["深蹲", "姿势", "正确"],
                    min_response_length=80,
                    expected_state="exercise_action"
                ),
                ConversationTurn(
                    user_message="谢谢你的建议，我明天就开始训练",
                    expected_intent="general_chat",
                    expected_keywords=["谢谢", "明天", "开始"],
                    expected_state="idle"
                )
            ],
            success_criteria={
                "all_turns_successful": True,
                "context_maintained": True,
                "appropriate_state_transitions": True,
                "user_info_utilized": True
            }
        )
    
    @pytest.fixture
    def weight_loss_scenario(self):
        """减肥咨询场景"""
        return ConversationScenario(
            name="减肥咨询场景",
            description="用户咨询减肥相关问题的对话流程",
            user_profile=create_test_user_profile("intermediate_female"),
            turns=[
                ConversationTurn(
                    user_message="我想减肥10公斤，应该怎么做？",
                    expected_intent="fitness_advice",
                    expected_keywords=["减肥", "10公斤"],
                    min_response_length=60
                ),
                ConversationTurn(
                    user_message="我现在每周跑步3次，够吗？",
                    expected_intent="fitness_advice",
                    expected_keywords=["跑步", "每周", "3次"],
                    should_contain_user_info=True
                ),
                ConversationTurn(
                    user_message="饮食方面有什么建议？",
                    expected_intent="diet_advice",
                    expected_keywords=["饮食", "建议"],
                    min_response_length=80
                ),
                ConversationTurn(
                    user_message="制定一个减肥训练计划",
                    expected_intent="training_plan",
                    expected_keywords=["减肥", "训练计划"],
                    min_response_length=100
                )
            ],
            success_criteria={
                "covers_exercise_and_diet": True,
                "personalized_advice": True,
                "plan_generation": True
            }
        )
    
    @pytest.fixture
    def intent_switching_scenario(self):
        """意图切换场景"""
        return ConversationScenario(
            name="意图快速切换",
            description="测试用户在不同意图间快速切换的处理能力",
            user_profile=create_test_user_profile("advanced_athlete"),
            turns=[
                ConversationTurn(
                    user_message="你好",
                    expected_intent="general_chat"
                ),
                ConversationTurn(
                    user_message="卧推的技巧",
                    expected_intent="exercise_action",
                    expected_keywords=["卧推", "技巧"]
                ),
                ConversationTurn(
                    user_message="制定力量训练计划",
                    expected_intent="training_plan",
                    expected_keywords=["力量训练", "计划"]
                ),
                ConversationTurn(
                    user_message="蛋白质摄入建议",
                    expected_intent="diet_advice",
                    expected_keywords=["蛋白质", "摄入"]
                ),
                ConversationTurn(
                    user_message="今天天气不错",
                    expected_intent="general_chat",
                    expected_keywords=["天气"]
                ),
                ConversationTurn(
                    user_message="回到训练话题，硬拉怎么做？",
                    expected_intent="exercise_action",
                    expected_keywords=["硬拉"]
                )
            ],
            success_criteria={
                "handles_rapid_switching": True,
                "maintains_context_across_switches": True,
                "appropriate_responses_for_each_intent": True
            }
        )
    
    @pytest.mark.asyncio
    async def test_fitness_consultation_flow(self, fitness_consultation_scenario):
        """测试健身咨询完整流程"""
        await self._run_conversation_scenario(fitness_consultation_scenario)
    
    @pytest.mark.asyncio
    async def test_weight_loss_consultation(self, weight_loss_scenario):
        """测试减肥咨询场景"""
        await self._run_conversation_scenario(weight_loss_scenario)
    
    @pytest.mark.asyncio
    async def test_intent_switching(self, intent_switching_scenario):
        """测试意图切换场景"""
        await self._run_conversation_scenario(intent_switching_scenario)
    
    async def _run_conversation_scenario(self, scenario: ConversationScenario):
        """运行对话场景测试"""
        conversation_id = f"test_{scenario.name}_{int(time.time())}"
        user_info = scenario.user_profile
        
        print(f"\n🎯 开始测试场景: {scenario.name}")
        print(f"📝 描述: {scenario.description}")
        print(f"👤 用户档案: {user_info}")
        
        conversation_history = []
        
        for i, turn in enumerate(scenario.turns):
            print(f"\n--- 第 {i+1} 轮对话 ---")
            print(f"👤 用户: {turn.user_message}")
            
            # 记录开始时间
            start_time = time.time()
            
            # 处理消息
            try:
                response = await conversation_orchestrator.process_message(
                    message=turn.user_message,
                    conversation_id=conversation_id,
                    user_info=user_info
                )
                
                # 记录响应时间
                response_time = time.time() - start_time
                
                print(f"🤖 AI助手: {response.get('response_content', '无响应内容')}")
                print(f"⏱️  响应时间: {response_time:.2f}秒")
                print(f"🎯 识别意图: {response.get('intent', '未知')}")
                print(f"📊 置信度: {response.get('confidence', 0):.2f}")
                
                # 验证响应
                self._validate_turn_response(turn, response, response_time)
                
                # 记录对话历史
                conversation_history.append({
                    "turn": i + 1,
                    "user_message": turn.user_message,
                    "ai_response": response.get('response_content', ''),
                    "intent": response.get('intent'),
                    "confidence": response.get('confidence'),
                    "response_time": response_time,
                    "state": response.get('current_state')
                })
                
            except Exception as e:
                print(f"❌ 第 {i+1} 轮对话失败: {str(e)}")
                pytest.fail(f"对话轮次 {i+1} 处理失败: {str(e)}")
        
        # 验证整体场景成功标准
        self._validate_scenario_success(scenario, conversation_history)
        
        print(f"\n✅ 场景 '{scenario.name}' 测试完成")
    
    def _validate_turn_response(self, turn: ConversationTurn, response: Dict[str, Any], response_time: float):
        """验证单轮对话响应"""
        # 验证响应时间
        assert response_time <= turn.max_response_time, \
            f"响应时间 {response_time:.2f}s 超过最大允许时间 {turn.max_response_time}s"
        
        # 验证响应内容长度
        response_content = response.get('response_content', '')
        assert len(response_content) >= turn.min_response_length, \
            f"响应内容长度 {len(response_content)} 小于最小要求 {turn.min_response_length}"
        
        # 验证预期意图
        if turn.expected_intent:
            actual_intent = response.get('intent')
            # 由于意图识别可能不完全准确，这里使用宽松的验证
            if actual_intent and actual_intent != turn.expected_intent:
                print(f"⚠️  意图不匹配: 预期 {turn.expected_intent}, 实际 {actual_intent}")
        
        # 验证关键词
        if turn.expected_keywords:
            response_lower = response_content.lower()
            missing_keywords = []
            for keyword in turn.expected_keywords:
                if keyword.lower() not in response_lower:
                    missing_keywords.append(keyword)
            
            if missing_keywords:
                print(f"⚠️  响应中缺少关键词: {missing_keywords}")
        
        # 验证用户信息使用
        if turn.should_contain_user_info:
            # 这里可以检查响应是否体现了个性化
            print("ℹ️  检查个性化响应...")
        
        # 验证状态
        if turn.expected_state:
            actual_state = response.get('current_state')
            if actual_state and actual_state != turn.expected_state:
                print(f"⚠️  状态不匹配: 预期 {turn.expected_state}, 实际 {actual_state}")
    
    def _validate_scenario_success(self, scenario: ConversationScenario, history: List[Dict[str, Any]]):
        """验证场景整体成功标准"""
        criteria = scenario.success_criteria
        
        # 检查所有轮次是否成功
        if criteria.get('all_turns_successful'):
            assert len(history) == len(scenario.turns), "对话轮次数量不匹配"
            
            for turn_data in history:
                assert turn_data['ai_response'], f"第 {turn_data['turn']} 轮缺少AI响应"
        
        # 检查上下文保持
        if criteria.get('context_maintained'):
            # 验证对话ID在所有轮次中保持一致
            conversation_ids = [turn.get('conversation_id') for turn in history if 'conversation_id' in turn]
            if conversation_ids:
                assert all(cid == conversation_ids[0] for cid in conversation_ids), "对话ID不一致"
        
        # 检查状态转换
        if criteria.get('appropriate_state_transitions'):
            states = [turn.get('state') for turn in history if turn.get('state')]
            print(f"📊 状态转换序列: {' → '.join(states)}")
        
        # 检查用户信息利用
        if criteria.get('user_info_utilized'):
            # 检查是否有个性化的响应
            personalized_responses = 0
            for turn_data in history:
                response = turn_data['ai_response']
                # 简单检查：响应长度较长且包含具体建议
                if len(response) > 50 and any(word in response for word in ['建议', '推荐', '适合']):
                    personalized_responses += 1
            
            assert personalized_responses > 0, "缺少个性化响应"
        
        print(f"✅ 场景成功标准验证通过")
    
    @pytest.mark.asyncio
    async def test_conversation_memory(self):
        """测试对话记忆功能"""
        conversation_id = "memory_test_001"
        user_info = create_test_user_profile("beginner_male")
        
        # 第一轮：提供个人信息
        response1 = await conversation_orchestrator.process_message(
            message="我25岁，男性，身高175cm，体重70kg，想要增肌",
            conversation_id=conversation_id,
            user_info=user_info
        )
        
        # 第二轮：询问相关建议（不重复个人信息）
        response2 = await conversation_orchestrator.process_message(
            message="给我一些训练建议",
            conversation_id=conversation_id,
            user_info=user_info
        )
        
        # 验证第二轮响应考虑了第一轮的信息
        response2_content = response2.get('response_content', '')
        assert len(response2_content) > 30, "第二轮响应过短"
        
        # 第三轮：测试更复杂的上下文
        response3 = await conversation_orchestrator.process_message(
            message="这个计划适合我吗？",
            conversation_id=conversation_id,
            user_info=user_info
        )
        
        response3_content = response3.get('response_content', '')
        assert len(response3_content) > 20, "第三轮响应过短"
        
        print("✅ 对话记忆测试通过")
    
    @pytest.mark.asyncio
    async def test_concurrent_conversations(self):
        """测试并发对话处理"""
        # 创建多个并发对话
        tasks = []
        for i in range(5):
            conversation_id = f"concurrent_test_{i}"
            user_info = create_test_user_profile("beginner_male")
            
            task = conversation_orchestrator.process_message(
                message=f"这是并发测试消息 {i}",
                conversation_id=conversation_id,
                user_info=user_info
            )
            tasks.append(task)
        
        # 等待所有任务完成
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 验证所有响应
        successful_responses = 0
        for i, response in enumerate(responses):
            if isinstance(response, Exception):
                print(f"❌ 并发任务 {i} 失败: {response}")
            else:
                assert 'response_content' in response, f"任务 {i} 响应格式错误"
                successful_responses += 1
        
        # 至少80%的请求应该成功
        success_rate = successful_responses / len(tasks)
        assert success_rate >= 0.8, f"并发成功率 {success_rate:.2f} 低于要求"
        
        print(f"✅ 并发对话测试通过，成功率: {success_rate:.2f}")


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
