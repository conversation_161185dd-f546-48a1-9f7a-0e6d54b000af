"""
对话协调器核心功能单元测试

测试ConversationOrchestrator的核心功能，包括消息处理、状态管理、意图识别等
"""

import pytest
import asyncio
import time
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any, AsyncGenerator, List, Optional, Union

from app.services.ai_assistant.conversation.orchestrator import ConversationOrchestrator
from app.services.ai_assistant.conversation.states.base import ConversationState
from app.services.ai_assistant.conversation.states.idle import IdleState
from app.services.ai_assistant.conversation.states.fitness_advice import FitnessAdviceState
from app.services.ai_assistant.llm.proxy import LLMProxy
from app.services.ai_assistant.knowledge.retriever import KnowledgeRetriever
from app.services.ai_assistant.common.cache import CacheService


class MockLLMProxy(LLMProxy):
    """Mock LLM代理，用于测试"""

    def __init__(self, responses: Dict[str, str] = None):
        self.responses = responses or {
            "default": "这是一个模拟的AI响应",
            "fitness": "这是关于健身的专业建议",
            "training": "这是训练计划相关的回复"
        }
        self.call_count = 0
        self.last_prompt = None

    async def chat(
        self,
        system_prompt: str,
        user_message: str,
        chat_history: Optional[List[Dict[str, str]]] = None,
        temperature: float = 0.7,
        max_tokens: int = 1024,
        model: Optional[str] = None,
        use_cache: bool = True,
        **kwargs
    ) -> str:
        """模拟对话响应"""
        self.call_count += 1
        self.last_prompt = f"{system_prompt}\n{user_message}"

        # 根据用户消息内容选择响应
        response = self.responses.get("default", "模拟响应")
        if "健身" in user_message or "fitness" in user_message.lower():
            response = self.responses.get("fitness", response)
        elif "训练" in user_message or "training" in user_message.lower():
            response = self.responses.get("training", response)

        return response

    async def get_embeddings(
        self,
        texts: Union[str, List[str]],
        model: Optional[str] = None,
        use_cache: bool = True,
        **kwargs
    ) -> List[List[float]]:
        """模拟嵌入向量生成"""
        import random

        # 确保texts是列表
        if isinstance(texts, str):
            texts = [texts]

        # 返回固定维度的随机向量
        return [[random.uniform(-1, 1) for _ in range(1536)] for _ in texts]

    async def astream(self, prompt: str, **kwargs) -> AsyncGenerator[str, None]:
        """模拟流式响应"""
        self.call_count += 1
        self.last_prompt = prompt

        # 根据提示词内容选择响应
        response = self.responses.get("default", "模拟响应")
        if "健身" in prompt or "fitness" in prompt.lower():
            response = self.responses.get("fitness", response)
        elif "训练" in prompt or "training" in prompt.lower():
            response = self.responses.get("training", response)

        # 模拟流式输出
        words = response.split()
        for word in words:
            yield word + " "
            await asyncio.sleep(0.01)  # 模拟网络延迟

    async def generate_text(self, prompt: str, **kwargs) -> str:
        """模拟文本生成"""
        chunks = []
        async for chunk in self.astream(prompt, **kwargs):
            chunks.append(chunk)
        return "".join(chunks).strip()


class MockKnowledgeRetriever(KnowledgeRetriever):
    """Mock知识检索器"""

    def __init__(self):
        self.knowledge_base = {
            "健身": ["健身需要循序渐进", "合理安排休息时间"],
            "减肥": ["控制饮食很重要", "有氧运动有助于减脂"],
            "增肌": ["力量训练是关键", "蛋白质摄入要充足"]
        }
        self.added_knowledge = []

    async def retrieve(self, query: str, limit: int = 5) -> list:
        """模拟知识检索"""
        results = []
        for keyword, knowledge in self.knowledge_base.items():
            if keyword in query:
                for item in knowledge[:limit]:
                    results.append({
                        "content": item,
                        "metadata": {"source": "测试知识库", "relevance": 0.9}
                    })
        return results

    async def add_knowledge(
        self,
        texts: List[str],
        metadatas: Optional[List[Dict[str, Any]]] = None,
        **kwargs
    ) -> List[str]:
        """模拟添加知识"""
        if metadatas is None:
            metadatas = [{} for _ in texts]

        ids = []
        for i, (text, metadata) in enumerate(zip(texts, metadatas)):
            doc_id = f"mock_kb_{len(self.added_knowledge) + i}"
            self.added_knowledge.append({
                "id": doc_id,
                "content": text,
                "metadata": metadata
            })
            ids.append(doc_id)

        return ids


class MockCacheService(CacheService):
    """Mock缓存服务"""

    def __init__(self):
        self.cache = {}
        self.hit_count = 0
        self.miss_count = 0

    async def get(self, key: str):
        """获取缓存"""
        if key in self.cache:
            self.hit_count += 1
            return self.cache[key]
        else:
            self.miss_count += 1
            return None

    async def set(self, key: str, value: Any, ttl: int = 3600):
        """设置缓存"""
        self.cache[key] = value

    async def delete(self, key: str):
        """删除缓存"""
        if key in self.cache:
            del self.cache[key]

    async def clear(self) -> bool:
        """清空缓存"""
        self.cache.clear()
        return True

    def get_stats(self):
        """获取缓存统计"""
        total = self.hit_count + self.miss_count
        hit_rate = self.hit_count / total if total > 0 else 0
        return {
            "hit_count": self.hit_count,
            "miss_count": self.miss_count,
            "hit_rate": hit_rate
        }


@pytest.fixture
def mock_llm_proxy():
    """提供Mock LLM代理"""
    return MockLLMProxy()


@pytest.fixture
def mock_knowledge_retriever():
    """提供Mock知识检索器"""
    return MockKnowledgeRetriever()


@pytest.fixture
def mock_cache_service():
    """提供Mock缓存服务"""
    return MockCacheService()


@pytest.fixture
def orchestrator(mock_llm_proxy, mock_knowledge_retriever, mock_cache_service):
    """创建测试用的对话协调器"""
    return ConversationOrchestrator(
        llm_proxy=mock_llm_proxy,
        knowledge_retriever=mock_knowledge_retriever,
        cache_service=mock_cache_service,
        use_bailian=False  # 禁用百炼，确保使用传入的mock_llm_proxy
    )


class TestConversationOrchestrator:
    """对话协调器测试类"""

    @pytest.mark.asyncio
    async def test_basic_message_processing(self, orchestrator, mock_llm_proxy):
        """测试基本消息处理功能"""
        # 准备测试数据
        message = "你好，我想了解健身"
        conversation_id = "test_conversation_001"
        user_info = {"user_id": "test_user", "age": 25, "gender": 1}

        # 处理消息
        response = await orchestrator.process_message(
            message=message,
            conversation_id=conversation_id,
            user_info=user_info
        )

        # 验证响应结构
        assert isinstance(response, dict)
        assert "response_content" in response
        assert "conversation_id" in response
        assert "intent" in response
        assert "confidence" in response

        # 验证响应内容
        assert len(response["response_content"]) > 0
        assert response["conversation_id"] == conversation_id

        # 验证协调器已正确初始化
        assert orchestrator.llm_proxy is not None
        assert orchestrator.cache_service is not None

        # 验证响应包含基本字段
        assert "timestamp" in response
        assert "message_id" in response

    @pytest.mark.asyncio
    async def test_stream_message_processing(self, orchestrator):
        """测试流式消息处理"""
        message = "我想制定一个减肥计划"
        conversation_id = "test_stream_001"
        user_info = {"user_id": "test_user", "fitness_goal": 1}

        # 收集流式响应
        chunks = []
        async for chunk in orchestrator.process_message_stream(
            user_input=message,
            conversation_id=conversation_id,
            meta_info=user_info
        ):
            chunks.append(chunk)

        # 验证流式响应
        assert len(chunks) > 0

        # 检查是否包含不同类型的响应
        chunk_types = set()
        for chunk in chunks:
            if isinstance(chunk, dict) and "type" in chunk:
                chunk_types.add(chunk["type"])

        # 应该包含元数据更新和消息内容
        assert len(chunk_types) > 0

    @pytest.mark.asyncio
    async def test_intent_recognition(self, orchestrator):
        """测试意图识别功能"""
        test_cases = [
            ("我想了解健身知识", "fitness_advice"),
            ("帮我制定训练计划", "training_plan"),
            ("深蹲怎么做？", "exercise_action"),
            ("你好", "general_chat")
        ]

        for message, expected_intent in test_cases:
            conversation_id = f"test_intent_{hash(message)}"

            response = await orchestrator.process_message(
                message=message,
                conversation_id=conversation_id
            )

            # 验证意图识别结果
            assert "intent" in response
            # 注意：由于使用Mock，实际意图可能不完全匹配，主要验证结构
            assert response["intent"] is not None

    @pytest.mark.asyncio
    async def test_state_management(self, orchestrator):
        """测试状态管理功能"""
        conversation_id = "test_state_001"

        # 第一条消息 - 应该在idle状态
        response1 = await orchestrator.process_message(
            message="你好",
            conversation_id=conversation_id
        )

        # 验证初始状态
        assert "current_state" in response1

        # 第二条消息 - 健身相关，可能转换状态
        response2 = await orchestrator.process_message(
            message="我想了解健身建议",
            conversation_id=conversation_id
        )

        # 验证状态可能发生变化
        assert "current_state" in response2
        assert response2["conversation_id"] == conversation_id

    @pytest.mark.asyncio
    async def test_user_context_handling(self, orchestrator):
        """测试用户上下文处理"""
        conversation_id = "test_context_001"
        user_info = {
            "user_id": "test_user",
            "age": 28,
            "gender": 1,
            "height": 175,
            "weight": 70,
            "fitness_goal": 3,  # 增肌
            "experience_level": 2  # 中级
        }

        response = await orchestrator.process_message(
            message="给我一些增肌建议",
            conversation_id=conversation_id,
            user_info=user_info
        )

        # 验证用户信息被正确处理
        assert response is not None
        assert "response_content" in response

        # 验证响应考虑了用户信息（通过检查LLM调用的提示词）
        llm_proxy = orchestrator.llm_proxy
        if hasattr(llm_proxy, 'last_prompt') and llm_proxy.last_prompt:
            # 提示词应该包含用户信息
            prompt = llm_proxy.last_prompt
            assert any(str(value) in prompt for value in user_info.values() if value is not None)

    @pytest.mark.asyncio
    async def test_caching_mechanism(self, orchestrator, mock_cache_service):
        """测试缓存机制"""
        message = "这是一个测试缓存的消息"
        conversation_id = "test_cache_001"

        # 第一次调用 - 应该缓存miss
        response1 = await orchestrator.process_message(
            message=message,
            conversation_id=conversation_id
        )

        # 第二次调用相同消息 - 应该缓存hit
        response2 = await orchestrator.process_message(
            message=message,
            conversation_id=conversation_id
        )

        # 验证缓存统计
        cache_stats = mock_cache_service.get_stats()
        assert cache_stats["hit_count"] >= 0  # 可能有缓存命中
        assert cache_stats["miss_count"] >= 0  # 可能有缓存未命中

    @pytest.mark.asyncio
    async def test_error_handling(self, orchestrator):
        """测试错误处理"""
        # 测试空消息
        response = await orchestrator.process_message(
            message=None,
            conversation_id="test_error_001"
        )

        assert response is not None
        assert "error" in response or "response_content" in response

        # 测试空会话ID
        response = await orchestrator.process_message(
            message="测试消息",
            conversation_id=""
        )

        assert response is not None

    @pytest.mark.asyncio
    async def test_knowledge_retrieval_integration(self, orchestrator, mock_knowledge_retriever):
        """测试知识检索集成"""
        message = "健身的基本原则是什么？"
        conversation_id = "test_knowledge_001"

        response = await orchestrator.process_message(
            message=message,
            conversation_id=conversation_id
        )

        # 验证知识检索被调用
        # 注意：这里需要根据实际实现调整验证逻辑
        assert response is not None
        assert "response_content" in response

    @pytest.mark.asyncio
    async def test_conversation_continuity(self, orchestrator):
        """测试对话连续性"""
        conversation_id = "test_continuity_001"

        # 模拟多轮对话
        messages = [
            "你好，我想开始健身",
            "我的目标是减肥",
            "我应该做什么运动？",
            "每周训练几次比较好？"
        ]

        responses = []
        for message in messages:
            response = await orchestrator.process_message(
                message=message,
                conversation_id=conversation_id
            )
            responses.append(response)

        # 验证所有响应都有效
        for response in responses:
            assert response is not None
            assert "response_content" in response
            assert response["conversation_id"] == conversation_id

        # 验证对话ID保持一致
        conversation_ids = [r["conversation_id"] for r in responses]
        assert all(cid == conversation_id for cid in conversation_ids)

    @pytest.mark.asyncio
    async def test_performance_metrics(self, orchestrator):
        """测试性能指标"""
        message = "给我一些健身建议"
        conversation_id = "test_performance_001"

        # 测量响应时间
        start_time = time.time()
        response = await orchestrator.process_message(
            message=message,
            conversation_id=conversation_id
        )
        end_time = time.time()

        response_time = end_time - start_time

        # 验证响应时间在合理范围内（考虑到这是单元测试，应该很快）
        assert response_time < 5.0  # 5秒内完成

        # 验证响应包含时间戳
        assert "timestamp" in response

        # 如果有处理时间信息，验证其合理性
        if "processing_time_ms" in response:
            assert response["processing_time_ms"] >= 0


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
