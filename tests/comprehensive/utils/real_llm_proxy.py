"""
真实LLM代理，用于端到端测试
"""
import asyncio
import time
import logging
from typing import Dict, Any, Optional
import httpx
from tests.comprehensive.config.real_model_config import RealModelTestConfig, MockModelConfig

logger = logging.getLogger(__name__)

class RealLLMProxy:
    """真实LLM代理，用于端到端测试"""
    
    def __init__(self, model_name: str = None):
        self.model_name = model_name or RealModelTestConfig.DEFAULT_MODEL
        self.config = RealModelTestConfig.get_model_config(self.model_name)
        self.use_real_model = RealModelTestConfig.is_real_model_enabled()
        
        if self.use_real_model:
            logger.info(f"使用真实模型: {self.model_name}")
        else:
            logger.info("使用Mock模型进行测试")
    
    async def generate_response(self, user_query: str, intent: str = "general_chat", 
                              user_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """生成响应"""
        start_time = time.time()
        
        try:
            if self.use_real_model:
                response = await self._call_real_model(user_query, intent, user_context)
            else:
                response = self._get_mock_response(user_query, intent)
            
            response_time = time.time() - start_time
            
            return {
                "content": response,
                "intent": intent,
                "response_time": response_time,
                "model_used": self.model_name if self.use_real_model else "mock",
                "success": True
            }
            
        except Exception as e:
            logger.error(f"生成响应失败: {str(e)}")
            response_time = time.time() - start_time
            
            # 降级到Mock响应
            fallback_response = self._get_mock_response(user_query, intent)
            
            return {
                "content": fallback_response,
                "intent": intent,
                "response_time": response_time,
                "model_used": "mock_fallback",
                "success": False,
                "error": str(e)
            }
    
    async def _call_real_model(self, user_query: str, intent: str, 
                              user_context: Dict[str, Any] = None) -> str:
        """调用真实模型"""
        prompt_template = RealModelTestConfig.get_prompt_template(intent)
        prompt = prompt_template.format(user_query=user_query)
        
        # 如果有用户上下文，添加到提示中
        if user_context:
            context_info = self._format_user_context(user_context)
            prompt = f"{context_info}\n\n{prompt}"
        
        headers = {
            "Authorization": f"Bearer {self.config['api_key']}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.config["model"],
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "temperature": self.config.get("temperature", 0.7),
            "max_tokens": self.config.get("max_tokens", 2000)
        }
        
        async with httpx.AsyncClient(timeout=self.config.get("timeout", 30)) as client:
            response = await client.post(
                f"{self.config['base_url']}/chat/completions",
                headers=headers,
                json=payload
            )
            
            if response.status_code != 200:
                raise Exception(f"API调用失败: {response.status_code} - {response.text}")
            
            result = response.json()
            
            if "choices" not in result or not result["choices"]:
                raise Exception("API响应格式错误")
            
            return result["choices"][0]["message"]["content"].strip()
    
    def _get_mock_response(self, user_query: str, intent: str) -> str:
        """获取Mock响应"""
        return MockModelConfig.get_mock_response(intent, user_query)
    
    def _format_user_context(self, user_context: Dict[str, Any]) -> str:
        """格式化用户上下文"""
        context_parts = []
        
        if user_context.get("age"):
            context_parts.append(f"年龄: {user_context['age']}岁")
        
        if user_context.get("gender"):
            gender_text = "男性" if user_context["gender"] == 1 else "女性"
            context_parts.append(f"性别: {gender_text}")
        
        if user_context.get("height") and user_context.get("weight"):
            context_parts.append(f"身高体重: {user_context['height']}cm, {user_context['weight']}kg")
        
        if user_context.get("fitness_goal"):
            goals = {1: "减肥", 2: "保持健康", 3: "增肌", 4: "提高体能"}
            goal_text = goals.get(user_context["fitness_goal"], "未知")
            context_parts.append(f"健身目标: {goal_text}")
        
        if user_context.get("experience_level"):
            levels = {1: "初级", 2: "中级", 3: "高级"}
            level_text = levels.get(user_context["experience_level"], "未知")
            context_parts.append(f"健身水平: {level_text}")
        
        if context_parts:
            return f"用户信息: {', '.join(context_parts)}"
        
        return ""


class TestLLMIntegration:
    """测试LLM集成的辅助类"""
    
    def __init__(self):
        self.llm_proxy = RealLLMProxy()
    
    async def test_basic_response(self) -> Dict[str, Any]:
        """测试基础响应"""
        test_query = "你好，我想了解健身"
        result = await self.llm_proxy.generate_response(test_query, "general_chat")
        
        return {
            "test_name": "基础响应测试",
            "query": test_query,
            "response_length": len(result["content"]),
            "response_time": result["response_time"],
            "model_used": result["model_used"],
            "success": result["success"],
            "content_preview": result["content"][:100] + "..." if len(result["content"]) > 100 else result["content"]
        }
    
    async def test_fitness_advice(self) -> Dict[str, Any]:
        """测试健身建议"""
        test_query = "我想减肥10公斤，应该怎么做？"
        user_context = {
            "age": 25,
            "gender": 1,
            "height": 175,
            "weight": 80,
            "fitness_goal": 1,
            "experience_level": 1
        }
        
        result = await self.llm_proxy.generate_response(test_query, "fitness_advice", user_context)
        
        return {
            "test_name": "健身建议测试",
            "query": test_query,
            "response_length": len(result["content"]),
            "response_time": result["response_time"],
            "model_used": result["model_used"],
            "success": result["success"],
            "has_keywords": any(keyword in result["content"] for keyword in ["减肥", "运动", "饮食", "训练"]),
            "content_preview": result["content"][:100] + "..." if len(result["content"]) > 100 else result["content"]
        }
    
    async def test_diet_advice(self) -> Dict[str, Any]:
        """测试饮食建议"""
        test_query = "增肌期间应该吃什么？"
        user_context = {
            "age": 28,
            "gender": 1,
            "height": 180,
            "weight": 75,
            "fitness_goal": 3,
            "experience_level": 2
        }
        
        result = await self.llm_proxy.generate_response(test_query, "diet_advice", user_context)
        
        return {
            "test_name": "饮食建议测试",
            "query": test_query,
            "response_length": len(result["content"]),
            "response_time": result["response_time"],
            "model_used": result["model_used"],
            "success": result["success"],
            "has_keywords": any(keyword in result["content"] for keyword in ["蛋白质", "营养", "饮食", "食物"]),
            "content_preview": result["content"][:100] + "..." if len(result["content"]) > 100 else result["content"]
        }
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        logger.info("开始LLM集成测试...")
        
        tests = [
            self.test_basic_response(),
            self.test_fitness_advice(),
            self.test_diet_advice()
        ]
        
        results = await asyncio.gather(*tests, return_exceptions=True)
        
        # 处理结果
        test_results = []
        for result in results:
            if isinstance(result, Exception):
                test_results.append({
                    "test_name": "未知测试",
                    "success": False,
                    "error": str(result)
                })
            else:
                test_results.append(result)
        
        # 计算总体统计
        total_tests = len(test_results)
        successful_tests = sum(1 for r in test_results if r.get("success", False))
        avg_response_time = sum(r.get("response_time", 0) for r in test_results) / total_tests
        
        return {
            "summary": {
                "total_tests": total_tests,
                "successful_tests": successful_tests,
                "success_rate": successful_tests / total_tests * 100,
                "avg_response_time": avg_response_time,
                "model_enabled": self.llm_proxy.use_real_model
            },
            "test_results": test_results
        }


# 异步测试运行器
async def run_llm_integration_test():
    """运行LLM集成测试"""
    test_integration = TestLLMIntegration()
    results = await test_integration.run_all_tests()
    
    print("\n" + "="*60)
    print("🤖 LLM集成测试结果")
    print("="*60)
    
    summary = results["summary"]
    print(f"📊 测试总数: {summary['total_tests']}")
    print(f"✅ 成功测试: {summary['successful_tests']}")
    print(f"📈 成功率: {summary['success_rate']:.1f}%")
    print(f"⏱️ 平均响应时间: {summary['avg_response_time']:.2f}秒")
    print(f"🔧 真实模型启用: {'是' if summary['model_enabled'] else '否'}")
    
    print("\n📋 详细测试结果:")
    for i, result in enumerate(results["test_results"], 1):
        status = "✅" if result.get("success", False) else "❌"
        print(f"\n{i}. {status} {result.get('test_name', '未知测试')}")
        
        if result.get("success", False):
            print(f"   📝 查询: {result.get('query', 'N/A')}")
            print(f"   📏 响应长度: {result.get('response_length', 0)}字符")
            print(f"   ⏱️ 响应时间: {result.get('response_time', 0):.2f}秒")
            print(f"   🤖 使用模型: {result.get('model_used', 'N/A')}")
            if result.get("content_preview"):
                print(f"   💬 响应预览: {result['content_preview']}")
        else:
            print(f"   ❌ 错误: {result.get('error', '未知错误')}")
    
    return results


if __name__ == "__main__":
    # 直接运行测试
    asyncio.run(run_llm_integration_test())
