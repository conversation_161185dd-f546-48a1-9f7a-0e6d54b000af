"""
pytest配置文件

提供测试所需的fixtures、配置和初始化设置
"""

import pytest
import asyncio
import os
import tempfile
from typing import Generator, AsyncGenerator
from unittest.mock import patch, MagicMock

# 设置测试环境变量
os.environ["TESTING"] = "true"
os.environ["DATABASE_URL"] = "postgresql://postgres:!scienceFit0219@localhost:5432/fitness_db"
os.environ["REDIS_URL"] = "redis://localhost:6379/1"

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient

from app.main import app
from app.db.base_class import Base
from app.db.session import get_db
from app import models
from tests.utils.test_helpers import create_test_user_profile


# 创建测试数据库引擎（使用现有PostgreSQL）
SQLALCHEMY_DATABASE_URL = os.getenv(
    "DATABASE_URL",
    "postgresql://postgres:!scienceFit0219@localhost:5432/fitness_db"
)
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    echo=False
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
def db_engine():
    """数据库引擎fixture"""
    try:
        # 尝试创建所有表
        Base.metadata.create_all(bind=engine)
    except Exception as e:
        # 如果创建失败（通常是因为SQLite不支持ARRAY类型），创建基本表
        print(f"Warning: Could not create all tables due to SQLite limitations: {e}")
        # 创建基本的测试表结构
        from sqlalchemy import text
        with engine.connect() as conn:
            # 创建用户表（包含所有必需字段）
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY,
                    email VARCHAR UNIQUE,
                    phone VARCHAR UNIQUE,
                    hashed_password VARCHAR,
                    openid VARCHAR UNIQUE NOT NULL,
                    unionid VARCHAR UNIQUE,
                    session_key VARCHAR,
                    nickname VARCHAR,
                    avatar_url VARCHAR,
                    country VARCHAR,
                    province VARCHAR,
                    city VARCHAR,
                    gender INTEGER,
                    birthday TIMESTAMP,
                    age INTEGER,
                    height REAL,
                    weight REAL,
                    activity_level INTEGER DEFAULT 3,
                    body_type VARCHAR,
                    experience_level INTEGER,
                    fitness_goal INTEGER,
                    language VARCHAR,
                    health_conditions TEXT,
                    allergies TEXT,
                    bmi REAL,
                    tedd INTEGER,
                    completed BOOLEAN DEFAULT 0,
                    is_active BOOLEAN DEFAULT 1,
                    is_superuser BOOLEAN DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP
                )
            """))

            # 创建对话表
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS conversations (
                    id INTEGER PRIMARY KEY,
                    user_id INTEGER NOT NULL,
                    session_id VARCHAR(255) UNIQUE NOT NULL,
                    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_active TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1,
                    meta_info TEXT,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """))

            # 创建消息表
            conn.execute(text("""
                CREATE TABLE IF NOT EXISTS messages (
                    id INTEGER PRIMARY KEY,
                    user_id INTEGER NOT NULL,
                    conversation_id VARCHAR(255) NOT NULL,
                    content TEXT NOT NULL,
                    role VARCHAR(20) NOT NULL,
                    intent VARCHAR(50),
                    confidence REAL,
                    meta_info TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """))

            conn.commit()

    yield engine

    # 清理
    try:
        Base.metadata.drop_all(bind=engine)
    except:
        pass


@pytest.fixture
def db_session(db_engine):
    """数据库会话fixture"""
    connection = db_engine.connect()
    transaction = connection.begin()
    session = TestingSessionLocal(bind=connection)

    yield session

    session.close()
    transaction.rollback()
    connection.close()


@pytest.fixture
def client(db_session):
    """测试客户端fixture"""
    def override_get_db():
        try:
            yield db_session
        finally:
            pass

    app.dependency_overrides[get_db] = override_get_db

    with TestClient(app) as test_client:
        yield test_client

    app.dependency_overrides.clear()


@pytest.fixture
def test_user_profiles():
    """测试用户档案fixture"""
    return {
        "beginner_male": create_test_user_profile("beginner_male"),
        "intermediate_female": create_test_user_profile("intermediate_female"),
        "advanced_athlete": create_test_user_profile("advanced_athlete"),
        "weight_loss_focused": create_test_user_profile("weight_loss_focused")
    }


@pytest.fixture
def mock_llm_responses():
    """Mock LLM响应fixture"""
    return {
        "fitness_advice": "这是关于健身的专业建议。根据您的情况，我建议您从基础训练开始...",
        "training_plan": "为您制定的训练计划如下：\n1. 热身运动（10分钟）\n2. 力量训练（30分钟）\n3. 有氧运动（20分钟）\n4. 拉伸放松（10分钟）",
        "exercise_action": "深蹲的正确姿势：\n1. 双脚与肩同宽\n2. 脚尖略向外\n3. 下蹲时膝盖不超过脚尖\n4. 保持背部挺直",
        "diet_advice": "关于饮食建议：\n1. 控制总热量摄入\n2. 增加蛋白质比例\n3. 选择复合碳水化合物\n4. 多吃蔬菜和水果",
        "general_chat": "您好！我是您的健身AI助手，很高兴为您提供帮助。请告诉我您的健身目标，我会为您提供专业的建议。"
    }


@pytest.fixture
def mock_conversation_orchestrator(mock_llm_responses):
    """Mock对话协调器fixture"""
    async def mock_process_message(message, conversation_id, user_info=None):
        # 简单的意图识别逻辑
        message_lower = message.lower()

        if any(word in message_lower for word in ["健身", "训练", "建议"]):
            intent = "fitness_advice"
        elif any(word in message_lower for word in ["计划", "制定"]):
            intent = "training_plan"
        elif any(word in message_lower for word in ["动作", "姿势", "怎么做"]):
            intent = "exercise_action"
        elif any(word in message_lower for word in ["饮食", "吃", "营养"]):
            intent = "diet_advice"
        else:
            intent = "general_chat"

        response_content = mock_llm_responses.get(intent, mock_llm_responses["general_chat"])

        return {
            "response_content": response_content,
            "conversation_id": conversation_id,
            "intent": intent,
            "confidence": 0.85,
            "current_state": "idle",
            "timestamp": 1640995200,
            "processing_time_ms": 150
        }

    async def mock_process_message_stream(user_input, conversation_id, user_id=None, meta_info=None):
        # 模拟流式响应
        response = await mock_process_message(user_input, conversation_id, meta_info)

        # 发送元数据更新
        yield {
            "type": "meta_info_update",
            "meta_info_update": {
                "intent": response["intent"],
                "confidence": response["confidence"],
                "processing": True
            }
        }

        # 模拟流式文本输出
        content = response["response_content"]
        words = content.split()

        for i, word in enumerate(words):
            yield {
                "type": "token",
                "content": word + " ",
                "role": "assistant"
            }

            # 模拟网络延迟
            await asyncio.sleep(0.01)

        # 发送完整消息
        yield {
            "type": "message",
            "content": content,
            "role": "assistant",
            "intent": response["intent"]
        }

        # 发送完成信号
        yield {
            "type": "meta_info_update",
            "meta_info_update": {
                "processing": False,
                "completed": True
            }
        }

    mock_orchestrator = MagicMock()
    mock_orchestrator.process_message = mock_process_message
    mock_orchestrator.process_message_stream = mock_process_message_stream

    return mock_orchestrator


@pytest.fixture
def mock_redis():
    """Mock Redis fixture"""
    class MockRedis:
        def __init__(self):
            self.data = {}

        async def get(self, key):
            return self.data.get(key)

        async def set(self, key, value, ex=None):
            self.data[key] = value

        async def delete(self, key):
            if key in self.data:
                del self.data[key]

        async def exists(self, key):
            return key in self.data

        def clear(self):
            self.data.clear()

    return MockRedis()


@pytest.fixture
def temp_directory():
    """临时目录fixture"""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield temp_dir


@pytest.fixture
def test_config():
    """测试配置fixture"""
    return {
        "testing": True,
        "database_url": "sqlite:///./test.db",
        "redis_url": "redis://localhost:6379/1",
        "llm_provider": "mock",
        "cache_ttl": 300,
        "max_conversation_history": 10,
        "response_timeout": 30,
        "max_concurrent_requests": 100
    }


@pytest.fixture(autouse=True)
def setup_test_environment(monkeypatch):
    """自动设置测试环境"""
    # 设置测试环境变量
    monkeypatch.setenv("TESTING", "true")
    monkeypatch.setenv("LOG_LEVEL", "DEBUG")

    # Mock外部服务
    with patch("app.services.ai_assistant.llm.providers.qwen_proxy.QwenLLMProxy") as mock_qwen:
        # 配置Mock LLM
        mock_instance = MagicMock()
        mock_instance.astream.return_value = iter(["这是", "模拟的", "流式", "响应"])
        mock_instance.generate_text.return_value = "这是模拟的响应"
        mock_qwen.return_value = mock_instance

        yield


@pytest.fixture
def performance_test_config():
    """性能测试配置fixture"""
    return {
        "single_user_requests": 50,
        "concurrent_users": 5,
        "requests_per_user": 10,
        "stress_test_duration": 30,
        "stress_test_rps": 20,
        "memory_test_iterations": 100,
        "max_response_time": 5.0,
        "min_success_rate": 95.0
    }


@pytest.fixture
def websocket_test_config():
    """WebSocket测试配置fixture"""
    return {
        "base_url": "ws://localhost:8000",
        "connection_timeout": 10,
        "message_timeout": 15,
        "heartbeat_interval": 30,
        "max_message_size": 1024 * 1024,  # 1MB
        "test_messages": [
            "WebSocket连接测试",
            "流式响应测试消息",
            "长时间连接稳定性测试"
        ]
    }


# 测试标记
pytest_plugins = []

def pytest_configure(config):
    """pytest配置"""
    config.addinivalue_line(
        "markers", "unit: 单元测试"
    )
    config.addinivalue_line(
        "markers", "integration: 集成测试"
    )
    config.addinivalue_line(
        "markers", "e2e: 端到端测试"
    )
    config.addinivalue_line(
        "markers", "performance: 性能测试"
    )
    config.addinivalue_line(
        "markers", "slow: 慢速测试"
    )
    config.addinivalue_line(
        "markers", "websocket: WebSocket测试"
    )


def pytest_collection_modifyitems(config, items):
    """修改测试收集"""
    for item in items:
        # 为性能测试添加slow标记
        if "performance" in item.nodeid:
            item.add_marker(pytest.mark.slow)

        # 为WebSocket测试添加标记
        if "websocket" in item.nodeid:
            item.add_marker(pytest.mark.websocket)


@pytest.fixture(scope="session", autouse=True)
def setup_test_logging():
    """设置测试日志"""
    import logging

    # 配置测试日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # 减少第三方库的日志级别
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("asyncio").setLevel(logging.WARNING)


@pytest.fixture
def cleanup_test_data():
    """清理测试数据fixture"""
    created_resources = []

    def register_resource(resource):
        created_resources.append(resource)

    yield register_resource

    # 清理资源
    for resource in created_resources:
        try:
            if hasattr(resource, 'cleanup'):
                resource.cleanup()
            elif hasattr(resource, 'close'):
                resource.close()
        except Exception as e:
            print(f"清理资源时出错: {e}")


# 异步测试支持
@pytest.fixture
def async_test():
    """异步测试支持fixture"""
    def _async_test(coro):
        return asyncio.get_event_loop().run_until_complete(coro)
    return _async_test
