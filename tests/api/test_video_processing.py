import pytest
import os
import shutil
from fastapi.testclient import TestClient
from app.main import app
from app.services.video_processing_service import VideoProcessingService, VideoQuality, VideoOutputFormat

client = TestClient(app)

# 测试数据
TEST_VIDEO_PATH = "tests/data/test_video.mp4"

@pytest.fixture(scope="module", autouse=True)
def setup_and_teardown():
    """设置和清理测试环境"""
    # 确保测试目录存在
    os.makedirs("tests/data", exist_ok=True)
    
    # 确保视频处理服务的目录存在
    VideoProcessingService.initialize()
    
    # 创建测试视频文件（如果不存在）
    if not os.path.exists(TEST_VIDEO_PATH):
        # 创建一个简单的测试视频文件
        create_test_video()
    
    yield
    
    # 清理测试文件
    # 注意：在实际测试中可能需要保留这些文件以便检查
    # if os.path.exists(TEST_VIDEO_PATH):
    #     os.remove(TEST_VIDEO_PATH)

def create_test_video():
    """创建测试视频文件"""
    # 这里我们可以使用FFmpeg创建一个简单的测试视频
    # 或者复制一个已有的视频文件
    try:
        import subprocess
        
        # 使用FFmpeg创建一个简单的测试视频
        cmd = [
            "ffmpeg",
            "-f", "lavfi",
            "-i", "testsrc=duration=5:size=640x480:rate=30",
            "-c:v", "libx264",
            "-pix_fmt", "yuv420p",
            "-y",
            TEST_VIDEO_PATH
        ]
        
        subprocess.run(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            check=True
        )
        
        print(f"已创建测试视频: {TEST_VIDEO_PATH}")
        
    except Exception as e:
        print(f"创建测试视频失败: {str(e)}")
        # 如果FFmpeg不可用，可以提供一个错误信息
        with open(TEST_VIDEO_PATH, "wb") as f:
            f.write(b"This is a placeholder for a test video file.")

def test_process_video_mp4():
    """测试视频处理 - MP4格式"""
    # 跳过测试如果测试视频不存在
    if not os.path.exists(TEST_VIDEO_PATH):
        pytest.skip("测试视频文件不存在")
    
    # 打开测试视频文件
    with open(TEST_VIDEO_PATH, "rb") as f:
        video_data = f.read()
    
    # 发送请求
    response = client.post(
        "/api/v1/video/process_video",
        files={"video": ("test_video.mp4", video_data, "video/mp4")},
        data={
            "quality": "medium",
            "outputFormat": "mp4",
            "title": "Test Video",
            "description": "This is a test video"
        }
    )
    
    # 检查响应
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "fileUrl" in data
    assert data["message"] == "视频已成功转换为MP4格式"

def test_process_video_gif():
    """测试视频处理 - GIF格式"""
    # 跳过测试如果测试视频不存在
    if not os.path.exists(TEST_VIDEO_PATH):
        pytest.skip("测试视频文件不存在")
    
    # 打开测试视频文件
    with open(TEST_VIDEO_PATH, "rb") as f:
        video_data = f.read()
    
    # 发送请求
    response = client.post(
        "/api/v1/video/process_video",
        files={"video": ("test_video.mp4", video_data, "video/mp4")},
        data={
            "quality": "low",
            "outputFormat": "gif",
            "title": "Test GIF",
            "description": "This is a test GIF"
        }
    )
    
    # 检查响应
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "success"
    assert "fileUrl" in data
    assert data["message"] == "视频已成功转换为GIF格式"

def test_download_file():
    """测试文件下载"""
    # 首先处理一个视频以获取文件URL
    if not os.path.exists(TEST_VIDEO_PATH):
        pytest.skip("测试视频文件不存在")
    
    # 打开测试视频文件
    with open(TEST_VIDEO_PATH, "rb") as f:
        video_data = f.read()
    
    # 处理视频
    response = client.post(
        "/api/v1/video/process_video",
        files={"video": ("test_video.mp4", video_data, "video/mp4")},
        data={
            "quality": "low",
            "outputFormat": "mp4",
            "title": "Test Download",
            "description": "This is a test for download"
        }
    )
    
    # 获取文件URL
    assert response.status_code == 200
    data = response.json()
    file_url = data["fileUrl"]
    
    # 从URL中提取文件名
    filename = file_url.split("/")[-1]
    
    # 下载文件
    download_response = client.get(f"/api/v1/video/download/{filename}")
    
    # 检查响应
    assert download_response.status_code == 200
    assert download_response.headers["content-type"] == "video/mp4"
    assert len(download_response.content) > 0
