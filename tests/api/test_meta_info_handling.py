"""
测试元数据处理和意图更新逻辑

这个测试模块验证元数据管理、意图转换和参数收集状态管理的模块化实现
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from app.main import app
from app.services.meta_info_manager import MetaInfoManager
from app.services.intent_transition_manager import IntentTransitionManager
from app.services.parameter_collection_manager import ParameterCollectionManager
from app.db.session import SessionLocal
from app import crud, models
from app.tests.utils.user import create_random_user
from app.tests.utils.utils import random_lower_string


def test_merge_meta_info_basic():
    """测试基本的元数据合并功能"""
    # 基础元数据
    base_meta_info = {
        "intent": "recommend_exercise",
        "confidence": 0.9,
        "training_params": {
            "body_part": "胸部",
            "scenario": "健身房"
        }
    }

    # 要合并的元数据
    update_meta_info = {
        "training_params": {
            "equipment": "哑铃"
        }
    }

    # 合并元数据
    result = MetaInfoManager.merge_meta_info(base_meta_info, update_meta_info)

    # 验证结果
    assert result["intent"] == "recommend_exercise"
    assert result["confidence"] == 0.9
    assert result["training_params"]["body_part"] == "胸部"
    assert result["training_params"]["scenario"] == "健身房"
    assert result["training_params"]["equipment"] == "哑铃"


def test_merge_meta_info_intent_change():
    """测试意图变更时的元数据处理"""
    # 基础元数据 - 训练相关意图
    base_meta_info = {
        "intent": "recommend_exercise",
        "confidence": 0.9,
        "training_params": {
            "body_part": "胸部",
            "scenario": "健身房"
        },
        "collecting_training_params": True,
        "asking_param": "equipment"
    }

    # 要合并的元数据 - 饮食相关意图
    update_meta_info = {
        "intent": "diet_advice",
        "confidence": 0.85
    }

    # 合并元数据
    result = MetaInfoManager.merge_meta_info(base_meta_info, update_meta_info)

    # 验证结果 - 应该重置大部分元数据，因为意图类别变更
    assert result["intent"] == "diet_advice"
    assert "training_params" not in result
    assert "collecting_training_params" not in result
    assert "asking_param" not in result


def test_merge_meta_info_same_category_intent_change():
    """测试同类别意图变更时的元数据处理"""
    # 基础元数据 - 推荐动作
    base_meta_info = {
        "intent": "recommend_exercise",
        "confidence": 0.9,
        "training_params": {
            "body_part": "胸部",
            "scenario": "健身房",
            "specific_param": "value"
        }
    }

    # 要合并的元数据 - 训练计划
    update_meta_info = {
        "intent": "daily_workout_plan",
        "confidence": 0.85
    }

    # 合并元数据
    result = MetaInfoManager.merge_meta_info(base_meta_info, update_meta_info)

    # 验证结果 - 应该保留通用训练参数
    assert result["intent"] == "daily_workout_plan"
    assert "training_params" in result
    assert result["training_params"]["body_part"] == "胸部"
    assert result["training_params"]["scenario"] == "健身房"


def test_merge_meta_info_collecting_params():
    """测试在参数收集状态下的元数据处理"""
    # 基础元数据 - 正在收集参数
    base_meta_info = {
        "intent": "recommend_exercise",
        "confidence": 0.9,
        "collecting_training_params": True,
        "asking_param": "body_part",
        "training_params": {}
    }

    # 要合并的元数据 - 新意图但置信度不高
    update_meta_info = {
        "intent": "general_chat",
        "confidence": 0.7
    }

    # 合并元数据，保持原始意图
    result = MetaInfoManager.merge_meta_info(base_meta_info, update_meta_info, "recommend_exercise")

    # 验证结果 - 应该保持原始意图
    assert result["intent"] == "recommend_exercise"
    assert result["collecting_training_params"] == True
    assert result["asking_param"] == "body_part"


def test_merge_meta_info_forced_intent_switch():
    """测试强制意图切换"""
    # 基础元数据 - 正在收集参数
    base_meta_info = {
        "intent": "recommend_exercise",
        "confidence": 0.9,
        "collecting_training_params": True,
        "asking_param": "body_part",
        "training_params": {
            "scenario": "健身房"
        }
    }

    # 要合并的元数据 - 强制切换到一般聊天
    update_meta_info = {
        "intent": "general_chat",
        "confidence": 0.95  # 高置信度
    }

    # 合并元数据，不保持原始意图
    result = MetaInfoManager.merge_meta_info(base_meta_info, update_meta_info)

    # 验证结果 - 应该切换到新意图并重置参数收集状态
    assert result["intent"] == "general_chat"
    assert "collecting_training_params" not in result
    assert "asking_param" not in result
    assert "training_params" not in result


def test_merge_meta_info_body_part_preservation():
    """测试body_part参数的保留逻辑"""
    # 基础元数据 - 包含body_part
    base_meta_info = {
        "intent": "recommend_exercise",
        "training_params": {
            "body_part": "胸部",
            "scenario": "健身房"
        }
    }

    # 要合并的元数据 - 不包含body_part
    update_meta_info = {
        "training_params": {
            "equipment": "哑铃",
            "body_part": ""  # 空值
        }
    }

    # 合并元数据
    result = MetaInfoManager.merge_meta_info(base_meta_info, update_meta_info)

    # 验证结果 - 应该保留原始body_part
    assert result["training_params"]["body_part"] == "胸部"
    assert result["training_params"]["equipment"] == "哑铃"


def test_intent_transition_manager_check_forced_intent_switch():
    """测试意图转换管理器的强制意图切换检测"""
    # 测试包含强制切换关键词的消息
    message1 = "我想停止当前的对话"
    result1 = IntentTransitionManager.check_forced_intent_switch(message1)

    # 验证结果 - 应该检测到强制切换
    assert result1 is not None
    assert result1[0] == "general_chat"  # 目标意图
    assert result1[1] == 0.95  # 置信度

    # 测试不包含强制切换关键词的消息
    message2 = "我想继续讨论训练计划"
    result2 = IntentTransitionManager.check_forced_intent_switch(message2)

    # 验证结果 - 不应该检测到强制切换
    assert result2 is None


def test_intent_transition_manager_handle_forced_intent_switch():
    """测试意图转换管理器的强制意图切换处理"""
    # 原始元数据
    meta_info = {
        "intent": "recommend_exercise",
        "confidence": 0.9,
        "collecting_training_params": True,
        "asking_param": "body_part",
        "training_params": {
            "scenario": "健身房"
        },
        "user_id": 123,
        "session_id": "test_session"
    }

    # 处理强制意图切换
    result = IntentTransitionManager.handle_forced_intent_switch(
        meta_info, "general_chat", 0.95
    )

    # 验证结果 - 应该重置大部分元数据，但保留基本信息
    assert result["intent"] == "general_chat"
    assert result["confidence"] == 0.95
    assert "collecting_training_params" not in result
    assert "asking_param" not in result
    assert "training_params" not in result
    assert result["user_id"] == 123
    assert result["session_id"] == "test_session"


def test_parameter_collection_manager_get_current_collection_state():
    """测试参数收集状态管理器的当前状态获取"""
    # 测试训练参数收集状态
    meta_info1 = {
        "collecting_training_params": True,
        "asking_param": "body_part"
    }
    state1, param1 = ParameterCollectionManager.get_current_collection_state(meta_info1)

    # 验证结果
    assert state1 == "training_params"
    assert param1 == "body_part"

    # 测试用户信息收集状态
    meta_info2 = {
        "waiting_for_info": {
            "field": "age"
        }
    }
    state2, param2 = ParameterCollectionManager.get_current_collection_state(meta_info2)

    # 验证结果
    assert state2 == "user_info"
    assert param2 == "age"

    # 测试无收集状态
    meta_info3 = {
        "intent": "general_chat"
    }
    state3, param3 = ParameterCollectionManager.get_current_collection_state(meta_info3)

    # 验证结果
    assert state3 == "none"
    assert param3 is None


def test_parameter_collection_manager_start_training_param_collection():
    """测试参数收集状态管理器的训练参数收集启动"""
    # 原始元数据
    meta_info = {
        "intent": "recommend_exercise",
        "confidence": 0.9
    }

    # 启动训练参数收集
    result = ParameterCollectionManager.start_training_param_collection(
        meta_info, "body_part"
    )

    # 验证结果
    assert result["collecting_training_params"] == True
    assert result["asking_param"] == "body_part"
    assert "training_params" in result
    assert result["intent"] == "recommend_exercise"
