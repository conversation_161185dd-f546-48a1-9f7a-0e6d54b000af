"""
意图识别系统集成测试
"""

import pytest
from unittest.mock import MagicMock, patch, AsyncMock
from app.services.intent_recognition import EnhancedIntentRecognizer
from app.services.llm_proxy_service import LLMProxyService

@pytest.mark.integration
class TestIntentRecognitionIntegration:
    
    @pytest.fixture
    def db_session(self):
        from app.db.session import get_db
        db = next(get_db())
        yield db
        db.close()
    
    @pytest.fixture
    def llm_proxy(self):
        # 创建LLMProxyService的模拟对象
        mock = MagicMock(spec=LLMProxyService)
        mock.aget_chat_response = AsyncMock()
        mock.stream_chat = AsyncMock()
        return mock
    
    @pytest.fixture
    def enhanced_recognizer(self, llm_proxy):
        # 创建增强版意图识别器实例
        return EnhancedIntentRecognizer(llm_proxy)
    
    @pytest.mark.asyncio
    async def test_training_plan_intent_recognition(self, enhanced_recognizer):
        """测试训练计划意图识别"""
        # 设置模拟响应
        enhanced_recognizer.model_service.get_response = AsyncMock(return_value="A")
        
        # 测试单日训练计划意图
        result = await enhanced_recognizer.recognize_intent("我想做一个胸部训练计划")
        
        # 验证结果
        assert result.intent == "daily_workout_plan"
        assert result.confidence >= 0.8
        assert "body_part" in result.parameters
        assert "胸部" in str(result.parameters["body_part"])
    
    @pytest.mark.asyncio
    async def test_recommend_exercise_intent_recognition(self, enhanced_recognizer):
        """测试推荐动作意图识别"""
        # 设置模拟响应
        enhanced_recognizer.model_service.get_response = AsyncMock(return_value="D")
        
        # 测试推荐动作意图
        result = await enhanced_recognizer.recognize_intent("推荐一些练背的动作")
        
        # 验证结果
        assert result.intent == "recommend_exercise"
        assert result.confidence >= 0.8
        assert "body_part" in result.parameters
        assert "背部" in str(result.parameters["body_part"])
    
    @pytest.mark.asyncio
    async def test_diet_advice_intent_recognition(self, enhanced_recognizer):
        """测试饮食建议意图识别"""
        # 设置模拟响应
        enhanced_recognizer.model_service.get_response = AsyncMock(return_value="H")
        
        # 测试饮食建议意图
        result = await enhanced_recognizer.recognize_intent("减肥期间应该吃什么")
        
        # 验证结果
        assert result.intent == "diet_advice"
        assert result.confidence >= 0.8
        assert "training_goal" in result.parameters
        assert "减脂" in str(result.parameters["training_goal"])
    
    @pytest.mark.asyncio
    async def test_fitness_qa_intent_recognition(self, enhanced_recognizer):
        """测试健身问答意图识别"""
        # 设置模拟响应
        enhanced_recognizer.model_service.get_response = AsyncMock(return_value="E")
        
        # 测试健身问答意图
        result = await enhanced_recognizer.recognize_intent("肌肉生长的原理是什么")
        
        # 验证结果
        assert result.intent == "fitness_qa"
        assert result.confidence >= 0.8
    
    @pytest.mark.asyncio
    async def test_context_aware_intent_recognition(self, enhanced_recognizer):
        """测试上下文感知的意图识别"""
        # 设置模拟响应
        enhanced_recognizer.function_call_recognizer.recognize_intent_with_function = AsyncMock(return_value=(
            IntentData(
                intent="daily_workout_plan",
                confidence=0.95,
                parameters={"plan_type": "daily", "body_part": "胸部", "scenario": "gym"}
            ),
            '[{"name": "generate_training_plan", "arguments": {"plan_type": "daily", "body_part": "胸部", "scenario": "gym"}}]'
        ))
        
        # 创建上下文
        context = {
            "collecting_training_params": True,
            "training_params": {"body_part": "胸部"},
            "asking_param": "scenario"
        }
        
        # 测试上下文感知的意图识别
        result = await enhanced_recognizer.recognize_intent("在健身房", context)
        
        # 验证结果
        assert result.intent == "daily_workout_plan"
        assert result.confidence >= 0.9
        assert "body_part" in result.parameters
        assert "scenario" in result.parameters
        assert result.parameters["scenario"] == "gym"
    
    @pytest.mark.asyncio
    async def test_integration_with_conversation_service(self, enhanced_recognizer, db_session):
        """测试与对话服务的集成"""
        # 导入对话服务
        from app.services.conversation.orchestrator import ConversationService
        
        # 创建对话服务实例
        conversation_service = ConversationService(db_session, enhanced_recognizer.llm_service)
        
        # 替换对话服务中的意图识别器
        conversation_service.intent_recognizer = enhanced_recognizer
        
        # 设置模拟响应
        enhanced_recognizer.model_service.get_response = AsyncMock(return_value="A")
        
        # 模拟处理消息
        user_id = 1  # 假设用户ID
        message = "我想做一个胸部训练计划"
        
        # 处理消息
        response_generator = conversation_service.process_message(
            user_id=user_id,
            message=message,
            session_id=None  # 新会话
        )
        
        # 收集响应
        responses = []
        async for response in response_generator:
            responses.append(response)
        
        # 验证响应
        assert any(r.get("type") == "meta" for r in responses)
        meta_response = next(r for r in responses if r.get("type") == "meta")
        
        # 验证元数据中的意图识别结果
        meta_info = meta_response.get("data", {})
        assert "intent" in meta_info
        assert meta_info["intent"] == "daily_workout_plan"
