import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch

from app.services.ai_assistant.conversation.orchestrator import ConversationOrchestrator
from app.services.ai_assistant.llm.proxy import LLMProxy
from app.services.ai_assistant.knowledge.retriever import KnowledgeRetriever
from app.services.ai_assistant.intent.handlers.fitness_advice import FitnessAdviceHandler
from app.services.ai_assistant.common.cache import MemoryCacheService
from app.services.ai_assistant.common.response_adapter import ResponseAdapter

# 测试用户场景
TEST_USERS = {
    "beginner": {
        "id": "user_001",
        "name": "王小明",
        "age": 25,
        "gender": "男",
        "fitness_level": "初级",
        "fitness_goals": ["减脂", "塑形"],
        "health_conditions": ["膝盖轻微不适"],
        "training_frequency": "每周3-4次",
        "conversation_flow": [
            {"user": "我想开始健身，但不知道从哪里开始", "expected_topics": ["初学者", "基础动作", "安全"]},
            {"user": "我每周能练几次比较好？", "expected_topics": ["训练频率", "恢复", "计划"]},
            {"user": "有哪些基础动作推荐？", "expected_topics": ["复合动作", "技巧", "姿势"]},
            {"user": "我膝盖有点不舒服，需要注意什么？", "expected_topics": ["安全", "膝盖保护", "替代动作"]}
        ]
    },
    "intermediate": {
        "id": "user_002",
        "name": "李晓华",
        "age": 30,
        "gender": "女",
        "fitness_level": "中级",
        "fitness_goals": ["增肌", "提升力量"],
        "health_conditions": [],
        "training_frequency": "每周5次",
        "diet_preferences": ["偏素食"],
        "conversation_flow": [
            {"user": "我想增加上肢力量，有什么训练建议？", "expected_topics": ["上肢肌群", "渐进超负荷", "组间休息"]},
            {"user": "我是素食者，如何确保蛋白质摄入足够？", "expected_topics": ["植物蛋白", "蛋白质补充", "营养均衡"]},
            {"user": "如何突破现有的力量平台期？", "expected_topics": ["训练变化", "周期化", "强度技巧"]},
            {"user": "建议的训练分化方案是什么？", "expected_topics": ["分化训练", "肌群恢复", "训练计划"]}
        ]
    },
    "special_needs": {
        "id": "user_003",
        "name": "刘阿姨",
        "age": 45,
        "gender": "女",
        "fitness_level": "初级",
        "fitness_goals": ["康复", "增强核心力量"],
        "health_conditions": ["腰椎间盘突出", "高血压"],
        "training_frequency": "每周2-3次",
        "conversation_flow": [
            {"user": "我有腰椎间盘突出，可以做什么安全的运动？", "expected_topics": ["安全", "核心", "低冲击"]},
            {"user": "哪些动作应该避免？", "expected_topics": ["禁忌动作", "脊柱负荷", "保护"]},
            {"user": "如何正确加强核心肌群？", "expected_topics": ["核心训练", "稳定性", "姿势控制"]},
            {"user": "我的血压有点高，运动时需要注意什么？", "expected_topics": ["心血管健康", "强度控制", "监测"]}
        ]
    }
}

@pytest.fixture
def mock_llm_proxy():
    """提供模拟的LLM代理"""
    mock = AsyncMock(spec=LLMProxy)
    
    # 模拟对不同用户类型的回复
    async def mock_chat(prompt, *args, **kwargs):
        if "初级" in prompt and "膝盖" in prompt:
            return "作为初学者，建议避免高冲击动作。对于膝盖不适，可以尝试游泳、椭圆机等低冲击运动，并且深蹲时确保正确姿势，膝盖不要超过脚尖。"
        elif "中级" in prompt and "增肌" in prompt:
            return "对于中级训练者，建议采用5×5或渐进式负重训练。上肢力量可以通过俯卧撑变式、引体向上和各种推举动作增强。每组间休息60-90秒，每周训练同一肌群2次。"
        elif "腰椎" in prompt:
            return "针对腰椎间盘突出，应避免负重弯腰、硬拉和高冲击运动。建议尝试温和的麦肯基疗法、游泳和适度步行。核心训练可以从平板支撑开始，逐渐增加难度。"
        else:
            return "这是一个通用健身建议回复，根据您的情况，建议合理安排训练计划，注意营养补充和充分休息。"
    
    mock.chat.side_effect = mock_chat
    mock.cache_service = MemoryCacheService()
    
    return mock

@pytest.fixture
def mock_knowledge_retriever():
    """提供模拟的知识检索器"""
    mock = AsyncMock(spec=KnowledgeRetriever)
    mock.retrieve.return_value = [
        {"content": "这是模拟的健身知识内容", "metadata": {"source": "健身指南"}}
    ]
    return mock

@pytest.fixture
def orchestrator(mock_llm_proxy, mock_knowledge_retriever):
    """创建对话协调器"""
    orchestrator = ConversationOrchestrator(
        llm_proxy=mock_llm_proxy,
        knowledge_retriever=mock_knowledge_retriever
    )
    return orchestrator

@pytest.mark.asyncio
async def test_beginner_user_guidance(orchestrator):
    """测试初学者用户引导场景"""
    user = TEST_USERS["beginner"]
    user_id = user["id"]
    
    # 初始化用户上下文
    initial_context = {"user_profile": user}
    current_state = await orchestrator.state_manager.get_current_state(user_id)
    current_state.context.update(initial_context)
    await orchestrator.state_manager.update_state(user_id, current_state)
    
    # 执行对话流
    for step in user["conversation_flow"]:
        raw_response = await orchestrator.process_message(user_id, step["user"])
        # 使用响应适配器处理响应
        response = orchestrator.get_text_response(raw_response)
        
        # 验证回复中包含预期主题
        for topic in step["expected_topics"]:
            assert topic.lower() in response.lower(), f"回复中应包含主题：{topic}"
        
        # 验证回复根据用户水平调整专业度
        assert "初学者" in response or "基础" in response or "开始" in response
        
        # 对于特定健康状况的询问，验证安全建议
        if "膝盖" in step["user"]:
            assert "膝盖" in response
            assert "安全" in response or "保护" in response or "避免" in response

@pytest.mark.asyncio
async def test_advanced_user_professional_advice(orchestrator):
    """测试进阶用户专业建议场景"""
    user = TEST_USERS["intermediate"]
    user_id = user["id"]
    
    # 初始化用户上下文
    initial_context = {"user_profile": user}
    current_state = await orchestrator.state_manager.get_current_state(user_id)
    current_state.context.update(initial_context)
    await orchestrator.state_manager.update_state(user_id, current_state)
    
    # 执行对话流
    for step in user["conversation_flow"]:
        raw_response = await orchestrator.process_message(user_id, step["user"])
        # 使用响应适配器处理响应
        response = orchestrator.get_text_response(raw_response)
        
        # 验证回复中包含预期主题
        for topic in step["expected_topics"]:
            assert topic.lower() in response.lower(), f"回复中应包含主题：{topic}"
        
        # 验证回复中包含更专业的术语
        professional_terms = ["负重", "组数", "休息时间", "肌群", "周期化", "分化"]
        term_count = sum(1 for term in professional_terms if term.lower() in response.lower())
        assert term_count >= 2, "回复中应包含至少2个专业术语"
        
        # 验证饮食建议考虑了用户偏好
        if "素食" in step["user"]:
            assert "植物蛋白" in response or "豆类" in response or "坚果" in response

@pytest.mark.asyncio
async def test_special_needs_safe_recommendations(orchestrator):
    """测试特殊需求用户安全建议场景"""
    user = TEST_USERS["special_needs"]
    user_id = user["id"]
    
    # 初始化用户上下文
    initial_context = {"user_profile": user}
    current_state = await orchestrator.state_manager.get_current_state(user_id)
    current_state.context.update(initial_context)
    await orchestrator.state_manager.update_state(user_id, current_state)
    
    # 执行对话流
    for step in user["conversation_flow"]:
        raw_response = await orchestrator.process_message(user_id, step["user"])
        # 使用响应适配器处理响应
        response = orchestrator.get_text_response(raw_response)
        
        # 验证回复中包含预期主题
        for topic in step["expected_topics"]:
            assert topic.lower() in response.lower(), f"回复中应包含主题：{topic}"
        
        # 验证安全考虑
        safety_terms = ["安全", "避免", "注意", "保护", "风险", "禁忌"]
        has_safety_focus = any(term in response.lower() for term in safety_terms)
        assert has_safety_focus, "对特殊需求用户的回复应强调安全性"
        
        # 对于特定健康状况的询问，验证针对性建议
        if "腰椎" in step["user"]:
            assert "腰椎" in response
            assert "避免" in response or "替代" in response
        
        if "血压" in step["user"]:
            assert "血压" in response or "心率" in response
            assert "强度" in response or "监测" in response

@pytest.mark.asyncio
async def test_conversation_memory_between_sessions(orchestrator):
    """测试会话间记忆保持"""
    user = TEST_USERS["beginner"]
    user_id = user["id"]
    
    # 首次对话
    first_message = "我是新手，想开始健身"
    await orchestrator.process_message(user_id, first_message)
    
    # 添加用户信息到上下文
    current_state = await orchestrator.state_manager.get_current_state(user_id)
    current_state.context["user_profile"] = user
    current_state.context["previous_topics"] = ["初学者健身"]
    await orchestrator.state_manager.update_state(user_id, current_state)
    
    # 模拟会话间隔
    await asyncio.sleep(0.5)
    
    # 后续对话应该记住用户信息
    second_message = "上次我们聊到哪了？"
    raw_response = await orchestrator.process_message(user_id, second_message)
    # 使用响应适配器处理响应
    response = orchestrator.get_text_response(raw_response)
    
    # 验证回复中引用了之前的上下文
    assert "初学者" in response or "新手" in response
    assert "上次" in response or "之前" in response or "继续" in response

@pytest.mark.asyncio
async def test_adaptive_response_complexity(orchestrator):
    """测试回复复杂度自适应调整"""
    # 测试不同水平用户的回复复杂度
    complexity_samples = {}
    
    for user_type, user in TEST_USERS.items():
        user_id = user["id"]
        
        # 初始化用户上下文
        initial_context = {"user_profile": user}
        current_state = await orchestrator.state_manager.get_current_state(user_id)
        current_state.context.update(initial_context)
        await orchestrator.state_manager.update_state(user_id, current_state)
        
        # 获取回复
        test_message = "请给我一些健身建议"
        raw_response = await orchestrator.process_message(user_id, test_message)
        # 使用响应适配器处理响应
        response = orchestrator.get_text_response(raw_response)
        
        # 分析回复复杂度
        sentence_count = len([s for s in response.split('.') if s.strip()])
        word_count = len(response.split())
        complexity_samples[user_type] = {
            "response": response,
            "sentence_count": sentence_count,
            "word_count": word_count
        }
    
    # 验证特殊需求用户的回复更注重安全性
    assert "安全" in complexity_samples["special_needs"]["response"]
    
    # 验证中级用户的回复比初级用户更专业/复杂
    beginner_words = complexity_samples["beginner"]["word_count"]
    intermediate_words = complexity_samples["intermediate"]["word_count"]
    
    # 通常中级用户的回复会包含更多专业内容，但也可能相近
    assert intermediate_words >= beginner_words * 0.8