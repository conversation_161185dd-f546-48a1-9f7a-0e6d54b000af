"""
对话状态系统集成测试

测试对话状态系统的各个组件，包括状态转换和上下文管理。
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch

from app.services.ai_assistant.conversation.states.base import ConversationState
from app.services.ai_assistant.conversation.states.idle import IdleState
from app.services.ai_assistant.conversation.states.fitness_advice import FitnessAdviceState
from app.services.ai_assistant.conversation.states.manager import ConversationStateManager
from app.services.ai_assistant.conversation.orchestrator import ConversationOrchestrator
from app.services.ai_assistant.llm.proxy import LLMProxy
from app.services.ai_assistant.knowledge.retriever import KnowledgeRetriever

# 测试用户数据
TEST_USERS = {
    "beginner": {
        "user_id": "user_123",
        "name": "王小明",
        "age": 25,
        "gender": "男",
        "fitness_level": "初级",
        "fitness_goals": ["减脂", "塑形"],
        "health_conditions": ["膝盖轻微不适"],
        "training_frequency": "每周3-4次"
    },
    "intermediate": {
        "user_id": "user_456",
        "name": "李晓华",
        "age": 30,
        "gender": "女",
        "fitness_level": "中级",
        "fitness_goals": ["增肌", "提升力量"],
        "health_conditions": [],
        "training_frequency": "每周5次",
        "diet_preferences": ["偏素食"]
    }
}

@pytest.fixture
def mock_llm_proxy():
    """提供模拟的LLM代理"""
    mock = AsyncMock(spec=LLMProxy)
    mock.chat.return_value = "这是LLM的模拟回复"
    mock.generate_text.return_value = "这是生成的文本"
    return mock

@pytest.fixture
def mock_knowledge_retriever():
    """提供模拟的知识检索器"""
    mock = AsyncMock(spec=KnowledgeRetriever)
    mock.retrieve.return_value = [
        {"content": "这是检索到的健身知识", "metadata": {"source": "健身指南", "relevance": 0.85}}
    ]
    return mock

@pytest.fixture
def state_manager():
    """创建状态管理器实例"""
    manager = ConversationStateManager()
    return manager

@pytest.fixture
def orchestrator(mock_llm_proxy, mock_knowledge_retriever):
    """创建对话协调器实例"""
    orchestrator = ConversationOrchestrator(
        llm_proxy=mock_llm_proxy,
        knowledge_retriever=mock_knowledge_retriever
    )
    return orchestrator

@pytest.mark.asyncio
async def test_initial_state_is_idle(state_manager):
    """测试初始状态为空闲状态"""
    user_id = "test_user"
    state = await state_manager.get_current_state(user_id)
    
    assert isinstance(state, IdleState)
    assert state.user_id == user_id

@pytest.mark.asyncio
async def test_state_transition_from_idle_to_fitness(state_manager):
    """测试从空闲状态转换到健身建议状态"""
    user_id = "test_user"
    
    # 获取初始状态（应为空闲状态）
    initial_state = await state_manager.get_current_state(user_id)
    assert isinstance(initial_state, IdleState)
    
    # 转换到健身建议状态
    context = {"user_query": "我想了解一些增肌训练的建议"}
    new_state = await state_manager.transition_to(user_id, "fitness_advice", context)
    
    # 验证新状态
    assert isinstance(new_state, FitnessAdviceState)
    assert new_state.user_id == user_id
    assert "user_query" in new_state.context
    
    # 确认状态已更新
    current_state = await state_manager.get_current_state(user_id)
    assert isinstance(current_state, FitnessAdviceState)

@pytest.mark.asyncio
async def test_context_preservation_between_states(state_manager):
    """测试状态转换过程中上下文保存和恢复"""
    user_id = "test_user"
    
    # 设置初始上下文
    initial_context = {
        "user_profile": {
            "fitness_level": "中级",
            "fitness_goals": ["增肌", "提升力量"]
        },
        "conversation_history": ["之前的对话1", "之前的对话2"]
    }
    
    # 转换到健身建议状态并添加上下文
    fitness_state = await state_manager.transition_to(
        user_id, "fitness_advice", initial_context
    )
    
    # 添加更多上下文
    fitness_state.context["current_topic"] = "增肌训练"
    fitness_state.context["last_recommendation"] = "建议进行复合动作训练"
    
    # 保存状态更新
    await state_manager.update_state(user_id, fitness_state)
    
    # 转换回空闲状态
    idle_state = await state_manager.transition_to(user_id, "idle")
    
    # 验证上下文被保留
    assert "user_profile" in idle_state.context
    assert idle_state.context["user_profile"]["fitness_level"] == "中级"
    assert "conversation_history" in idle_state.context
    assert "current_topic" in idle_state.context
    assert idle_state.context["last_recommendation"] == "建议进行复合动作训练"

@pytest.mark.asyncio
async def test_long_term_user_profile_memory(state_manager):
    """测试用户信息长期记忆功能"""
    user_id = TEST_USERS["beginner"]["user_id"]
    
    # 初始化用户资料
    user_profile = TEST_USERS["beginner"]
    
    # 创建初始状态
    initial_state = await state_manager.get_current_state(user_id)
    initial_state.context["user_profile"] = user_profile
    await state_manager.update_state(user_id, initial_state)
    
    # 模拟多次状态转换
    for _ in range(3):
        fitness_state = await state_manager.transition_to(user_id, "fitness_advice")
        await asyncio.sleep(0.1)  # 模拟一些处理时间
        idle_state = await state_manager.transition_to(user_id, "idle")
        await asyncio.sleep(0.1)  # 模拟一些处理时间
    
    # 获取最终状态
    final_state = await state_manager.get_current_state(user_id)
    
    # 验证用户资料被保留
    assert "user_profile" in final_state.context
    assert final_state.context["user_profile"]["name"] == "王小明"
    assert final_state.context["user_profile"]["fitness_level"] == "初级"
    assert "减脂" in final_state.context["user_profile"]["fitness_goals"]

@pytest.mark.asyncio
async def test_orchestrator_uses_state_manager(orchestrator):
    """测试对话协调器正确使用状态管理器"""
    conversation_id = "test_conversation"
    user_message = "我想知道如何进行深蹲"
    
    # 处理用户消息
    response = await orchestrator.process_message(
        message=user_message,
        conversation_id=conversation_id
    )
    
    # 验证响应
    assert response is not None
    assert isinstance(response, dict)  # 返回值是字典而非字符串
    assert "response_content" in response
    
    # 验证状态已更新
    state = await orchestrator.state_manager.get_current_state(conversation_id)
    assert state is not None
    assert "messages" in state.context

@pytest.mark.asyncio
async def test_state_persistence_and_recovery(state_manager):
    """测试状态持久化和恢复"""
    user_id = "test_user"
    
    # 创建状态并设置上下文
    initial_state = await state_manager.get_current_state(user_id)
    initial_state.context["user_profile"] = TEST_USERS["intermediate"]
    initial_state.context["session_data"] = {"start_time": "2023-08-15T14:30:00"}
    await state_manager.update_state(user_id, initial_state)
    
    # 模拟保存状态到持久化存储
    with patch.object(state_manager, "_save_state_to_storage") as mock_save:
        await state_manager.persist_state(user_id)
        mock_save.assert_called_once()
    
    # 清除内存中的状态
    state_manager._states = {}
    
    # 模拟从持久化存储恢复状态
    with patch.object(state_manager, "_load_state_from_storage") as mock_load:
        mock_load.return_value = {
            "state_type": "idle",
            "context": {
                "user_profile": TEST_USERS["intermediate"],
                "session_data": {"start_time": "2023-08-15T14:30:00"}
            }
        }
        
        # 恢复状态
        recovered_state = await state_manager.get_current_state(user_id)
        
        # 验证状态正确恢复
        assert isinstance(recovered_state, IdleState)
        assert recovered_state.context["user_profile"]["name"] == "李晓华"
        assert recovered_state.context["session_data"]["start_time"] == "2023-08-15T14:30:00"