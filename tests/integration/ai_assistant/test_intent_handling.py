"""
意图处理系统集成测试

测试意图处理器工厂和各种处理器的集成功能
"""

import pytest
from typing import Dict, Any, List

from app.services.ai_assistant.intent.handlers.factory import IntentHandlerFactory
from app.services.ai_assistant.intent.handlers.general_chat import GeneralChatHandler
from app.services.ai_assistant.llm.proxy import DefaultLLMProxy
from app.services.ai_assistant.knowledge.retriever import DefaultKnowledgeRetriever


@pytest.fixture
def llm_proxy():
    """创建一个测试用的LLM代理"""
    return DefaultLLMProxy()


@pytest.fixture
def knowledge_retriever():
    """创建一个测试用的知识检索器"""
    return DefaultKnowledgeRetriever()


@pytest.fixture
def handler_factory(llm_proxy, knowledge_retriever):
    """创建一个配置好的处理器工厂"""
    factory = IntentHandlerFactory()
    factory.register("general_chat", GeneralChatHandler(llm_proxy, knowledge_retriever))
    return factory


@pytest.mark.asyncio
async def test_general_chat_greeting(handler_factory, llm_proxy, knowledge_retriever):
    """测试问候意图处理"""
    # 准备测试数据
    intent = "greeting"
    user_message = {"text": "你好", "timestamp": "2023-09-01T12:00:00"}
    user_context = {"user_id": "test_user", "session_id": "test_session"}
    
    # 获取处理器并处理
    handler = handler_factory.create("general_chat", llm_proxy=llm_proxy, knowledge_retriever=knowledge_retriever)
    response = await handler.handle(intent, user_message, user_context)
    
    # 验证结果
    assert response is not None
    assert "content" in response
    assert len(response["content"]) > 0
    assert "response_type" in response
    assert response["response_type"] == "greeting"


@pytest.mark.asyncio
async def test_general_chat_farewell(handler_factory, llm_proxy, knowledge_retriever):
    """测试告别意图处理"""
    # 准备测试数据
    intent = "farewell"
    user_message = {"text": "再见", "timestamp": "2023-09-01T12:00:00"}
    user_context = {"user_id": "test_user", "session_id": "test_session"}
    
    # 获取处理器并处理
    handler = handler_factory.create("general_chat", llm_proxy=llm_proxy, knowledge_retriever=knowledge_retriever)
    response = await handler.handle(intent, user_message, user_context)
    
    # 验证结果
    assert response is not None
    assert "content" in response
    assert len(response["content"]) > 0
    assert "response_type" in response
    assert response["response_type"] == "farewell"


@pytest.mark.asyncio
async def test_general_chat_help(handler_factory, llm_proxy, knowledge_retriever):
    """测试帮助意图处理"""
    # 准备测试数据
    intent = "help"
    user_message = {"text": "你能帮我什么", "timestamp": "2023-09-01T12:00:00"}
    user_context = {"user_id": "test_user", "session_id": "test_session"}
    
    # 获取处理器并处理
    handler = handler_factory.create("general_chat", llm_proxy=llm_proxy, knowledge_retriever=knowledge_retriever)
    response = await handler.handle(intent, user_message, user_context)
    
    # 验证结果
    assert response is not None
    assert "content" in response
    assert len(response["content"]) > 0
    assert "response_type" in response
    assert response["response_type"] == "help"
    assert "suggested_next_intents" in response
    assert len(response["suggested_next_intents"]) > 0


@pytest.mark.asyncio
async def test_general_chat_smalltalk(handler_factory, llm_proxy, knowledge_retriever):
    """测试闲聊意图处理"""
    # 准备测试数据
    intent = "smalltalk"
    user_message = {"text": "今天天气怎么样", "timestamp": "2023-09-01T12:00:00"}
    user_context = {"user_id": "test_user", "session_id": "test_session"}
    
    # 获取处理器并处理
    handler = handler_factory.create("general_chat", llm_proxy=llm_proxy, knowledge_retriever=knowledge_retriever)
    response = await handler.handle(intent, user_message, user_context)
    
    # 验证结果
    assert response is not None
    assert "content" in response
    assert len(response["content"]) > 0
    assert "response_type" in response
    assert response["response_type"] == "smalltalk"


@pytest.mark.asyncio
async def test_general_chat_personalization(handler_factory, llm_proxy, knowledge_retriever):
    """测试个性化响应"""
    # 准备测试数据
    intent = "greeting"
    user_message = {"text": "你好", "timestamp": "2023-09-01T12:00:00"}
    user_context = {
        "user_id": "test_user", 
        "session_id": "test_session",
        "name": "张三",
        "fitness_level": "intermediate",
        "fitness_goal": "增肌",
        "last_workout_date": "昨天"
    }
    
    # 获取处理器并处理
    handler = handler_factory.create("general_chat", llm_proxy=llm_proxy, knowledge_retriever=knowledge_retriever)
    response = await handler.handle(intent, user_message, user_context)
    
    # 验证结果
    assert response is not None
    assert "content" in response
    assert "张三" in response["content"]
    assert "增肌" in response["content"]
    assert "昨天" in response["content"] 