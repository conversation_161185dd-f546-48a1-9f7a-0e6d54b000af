"""
健身AI助手系统端到端对话流程测试

本模块包含对健身AI助手系统的端到端测试，用于验证整个系统在实际用户对话场景中的表现。
测试内容包括基本对话功能、状态跟踪、上下文管理、响应格式、缓存机制以及特定用户场景和意图处理。

测试设计原则：
1. 模拟真实用户对话场景
2. 验证系统响应包含预期信息（关键词匹配）而非精确内容匹配
3. 优先验证功能完整性而非详细内容
4. 并尽可能减少对外部服务的依赖

注意：这些测试应该与单元测试和其他集成测试一起运行，以确保系统的全面覆盖。
"""

import pytest
import asyncio
import time
from unittest.mock import AsyncMock, MagicMock, patch

from app.services.ai_assistant.conversation.orchestrator import ConversationOrchestrator
from app.services.ai_assistant.llm.proxy import LLMProxy
from app.services.ai_assistant.knowledge.retriever import KnowledgeRetriever
from app.services.ai_assistant.intent.handlers.fitness_advice import FitnessAdviceHandler
from app.services.ai_assistant.common.cache import MemoryCacheService

# 基本对话流程测试
@pytest.mark.asyncio
async def test_basic_conversation_flow():
    """
    测试基本对话流程
    
    验证内容：
    1. 能够正确初始化对话编排器
    2. 能够发送消息并获得回复
    3. 回复内容为非空字符串
    
    这是最基本的系统可用性测试，确保整个对话系统能够正常工作
    """
    # 创建对话协调器
    orchestrator = ConversationOrchestrator()
    
    # 测试简单对话
    response = await orchestrator.process_message("你好", "test_user_123")
    
    # 验证回复
    assert response is not None
    assert "response_content" in response
    assert len(response["response_content"]) > 0
    assert "conversation_id" in response
    assert response["conversation_id"] == "test_user_123"

# 对话状态测试
@pytest.mark.asyncio
async def test_conversation_state_tracking():
    """
    测试对话状态跟踪功能
    
    验证内容：
    1. 初始状态正确设置为空闲状态
    2. 发送消息后状态会根据内容变化
    3. 状态变化后上下文信息得以保留
    
    这个测试确保对话状态管理功能工作正常，系统能够跟踪用户意图并进行状态转换
    """
    # 创建对话协调器
    orchestrator = ConversationOrchestrator()
    conversation_id = "test_state_tracking_123"
    
    # 获取初始状态
    initial_state = orchestrator.get_conversation_state(conversation_id)
    
    # 初始状态应该是空闲状态
    assert initial_state == "idle"
    
    # 发送消息后状态可能会变化
    await orchestrator.process_message("我需要健身建议", conversation_id)
    
    # 获取更新后的状态
    updated_state = orchestrator.get_conversation_state(conversation_id)
    
    # 验证状态已更新（具体状态取决于您的系统实现）
    assert updated_state is not None

# 对话上下文测试
@pytest.mark.asyncio
async def test_conversation_context():
    """
    测试对话上下文存储功能
    
    验证内容：
    1. 能够在对话过程中存储上下文信息
    2. 能够在后续消息中访问之前存储的上下文
    3. 上下文中包含对话历史记录
    
    这个测试确保系统能够在对话过程中保持对话上下文，是实现连续对话的基础
    """
    # 创建对话协调器
    orchestrator = ConversationOrchestrator()
    conversation_id = "test_context_123"
    
    # 发送消息
    message = "你好，我想了解健身"
    response = await orchestrator.process_message(message, conversation_id)
    
    # 检查回复是否添加到对话状态中
    current_state = await orchestrator.state_manager.get_current_state(conversation_id)
    
    # 检查状态上下文是否存在
    assert current_state.context is not None
    
    # 确认对话ID正确记录
    assert current_state.context.get("id") == conversation_id

# 响应字段测试
@pytest.mark.asyncio
async def test_response_fields():
    """
    测试响应字段格式
    
    验证内容：
    1. 响应包含所有必要字段（content、source、intent等）
    2. 各字段的类型符合预期
    
    这个测试确保系统响应的格式符合预期，下游组件能够正确解析和使用
    """
    # 创建对话协调器
    orchestrator = ConversationOrchestrator()
    
    # 测试健身建议
    fitness_response = await orchestrator.process_message("我想了解健身", "test_fields_123")
    assert fitness_response is not None
    
    # 检查响应中的必要字段
    required_fields = ["response_content", "conversation_id", "message_id", "timestamp"]
    for field in required_fields:
        assert field in fitness_response, f"响应应包含{field}字段"
    
    # 检查字段类型正确
    assert isinstance(fitness_response["response_content"], str)
    assert isinstance(fitness_response["conversation_id"], str)
    assert isinstance(fitness_response["message_id"], str)
    assert isinstance(fitness_response["timestamp"], int)

# 缓存机制测试
@pytest.mark.asyncio
async def test_caching_mechanism():
    """
    测试缓存机制
    
    验证内容：
    1. 相同请求的后续响应速度显著快于首次请求
    2. 缓存命中后响应内容与首次请求相同
    
    这个测试验证系统能够有效缓存对话请求和响应，提高系统性能
    """
    # 创建对话协调器
    orchestrator = ConversationOrchestrator()
    conversation_id = "test_cache_123"
    message = "健身前要怎么热身"
    
    # 第一次请求，缓存未命中
    start_time1 = time.time()
    response1 = await orchestrator.process_message(message, conversation_id)
    processing_time1 = time.time() - start_time1
    
    # 稍等片刻以确保响应被缓存
    await asyncio.sleep(0.5)
    
    # 第二次请求，应该命中缓存
    start_time2 = time.time()
    response2 = await orchestrator.process_message(message, conversation_id)
    processing_time2 = time.time() - start_time2
    
    # 验证两次回复内容相同
    assert response1["response_content"] == response2["response_content"]
    
    # 如果使用了缓存，第二次处理应该会快得多
    # 注意：这个断言可能在某些环境下不稳定，因为处理时间受多种因素影响
    # 如果经常失败，可以考虑移除或放宽条件
    if "processing_time_ms" in response1 and "processing_time_ms" in response2:
        assert response2["processing_time_ms"] <= response1["processing_time_ms"] * 1.5, "缓存响应应该更快"

# 意图处理 - 健身建议
@pytest.mark.asyncio
async def test_fitness_advice_handling():
    """
    测试健身建议意图处理
    
    验证内容：
    1. 系统能够识别健身建议类请求
    2. 返回的响应包含相关健身术语和建议
    3. 回复内容与健身相关
    
    这个测试确保系统能够正确处理健身建议类请求，提供相关且专业的回复
    """
    # 创建对话协调器
    orchestrator = ConversationOrchestrator()
    conversation_id = "test_fitness_advice_123"
    
    # 发送健身建议请求
    response = await orchestrator.process_message("我想获取一些健身建议", conversation_id)
    
    # 验证回复内容相关性
    assert response is not None
    assert "response_content" in response
    
    # 验证回复中包含健身相关词汇
    fitness_terms = ["健身", "锻炼", "运动", "训练"]
    assert any(term in response["response_content"] for term in fitness_terms), "回复内容应该包含健身相关术语"

# 意图处理 - 训练计划
@pytest.mark.asyncio
async def test_training_plan_handling():
    """
    测试训练计划意图处理
    
    验证内容：
    1. 系统能够识别训练计划类请求
    2. 返回的响应包含训练计划相关术语
    3. 回复内容具有结构性和计划性
    
    这个测试确保系统能够正确处理训练计划类请求，提供相关且系统的训练方案
    """
    # 创建对话协调器
    orchestrator = ConversationOrchestrator()
    conversation_id = "test_training_plan_123"
    
    # 发送训练计划请求
    response = await orchestrator.process_message("帮我制定一个训练计划", conversation_id)
    
    # 验证回复内容相关性
    assert response is not None
    assert "response_content" in response
    
    # 验证回复中包含训练计划相关词汇
    plan_terms = ["训练计划", "训练", "计划", "锻炼", "组数", "次数"]
    assert any(term in response["response_content"] for term in plan_terms), "回复内容应该包含训练计划相关术语"

# 特殊用户需求场景 - 初学者
@pytest.mark.asyncio
async def test_beginner_user_scenario():
    """
    测试初学者用户场景
    
    验证内容：
    1. 系统能够识别并适应初学者用户的需求
    2. 返回的响应包含针对初学者的简单建议和术语
    3. 避免使用过于专业或复杂的词汇
    
    这个测试确保系统能够根据用户的健身水平进行适当的回复调整
    """
    # 创建对话协调器
    orchestrator = ConversationOrchestrator()
    conversation_id = "test_beginner_123"
    
    # 初始化用户上下文
    current_state = await orchestrator.state_manager.get_current_state(conversation_id)
    current_state.context["user_profile"] = {
        "name": "王小明",
        "age": 25,
        "gender": "男",
        "fitness_level": "初级",
        "fitness_goals": ["减脂", "塑形"],
        "health_conditions": []
    }
    await orchestrator.state_manager.update_state(conversation_id, current_state)
    
    # 发送适合初学者的请求
    response = await orchestrator.process_message("作为初学者，我应该怎么开始健身？", conversation_id)
    
    # 验证回复内容相关性
    assert response is not None
    assert "response_content" in response
    
    # 验证回复中包含初学者相关建议
    beginner_terms = ["基础", "简单", "入门", "开始", "基本"]
    assert any(term in response["response_content"] for term in beginner_terms), "回复内容应该包含适合初学者的建议"

# 特殊用户需求场景 - 健康状况
@pytest.mark.asyncio
async def test_health_condition_consideration():
    """
    测试特殊健康状况考虑
    
    验证内容：
    1. 系统能够考虑用户的特殊健康状况
    2. 返回的响应包含安全提示和注意事项
    3. 建议中强调安全性和适应性
    
    这个测试确保系统能够为有特殊健康状况的用户提供安全合适的建议
    """
    # 创建对话协调器
    orchestrator = ConversationOrchestrator()
    conversation_id = "test_health_condition_123"
    
    # 初始化用户上下文，包含健康状况
    current_state = await orchestrator.state_manager.get_current_state(conversation_id)
    current_state.context["user_profile"] = {
        "name": "刘阿姨",
        "age": 45,
        "gender": "女",
        "fitness_level": "初级",
        "fitness_goals": ["康复", "增强核心力量"],
        "health_conditions": ["腰椎间盘突出", "高血压"]
    }
    await orchestrator.state_manager.update_state(conversation_id, current_state)
    
    # 发送考虑健康状况的请求
    response = await orchestrator.process_message("我有腰椎间盘突出，请推荐适合我的训练", conversation_id)
    
    # 验证回复内容相关性
    assert response is not None
    assert "response_content" in response
    
    # 验证回复中包含健康考虑相关词汇
    health_terms = ["安全", "注意", "避免", "保护", "风险", "腰椎", "建议"]
    assert any(term in response["response_content"] for term in health_terms), "回复内容应该包含健康状况相关考虑"

# 会话连续性测试
@pytest.mark.asyncio
async def test_conversation_continuity():
    """
    测试对话连续性
    
    验证内容：
    1. 系统能够在多轮对话中保持上下文连贯
    2. 后续回复能够参考前面对话的内容
    3. 能够理解并回应上下文相关的问题
    
    这个测试确保系统能够进行连贯的多轮对话，而不是简单的单轮问答
    """
    # 创建对话协调器
    orchestrator = ConversationOrchestrator()
    conversation_id = "test_continuity_123"
    
    # 第一轮对话
    first_response = await orchestrator.process_message("健身需要注意什么？", conversation_id)
    
    # 第二轮对话，引用前一轮内容
    second_response = await orchestrator.process_message("可以具体说说饮食方面吗？", conversation_id)
    
    # 验证回复内容
    assert first_response is not None and second_response is not None
    assert "response_content" in first_response and "response_content" in second_response
    
    # 验证第二轮回复包含饮食相关内容
    nutrition_terms = ["蛋白质", "碳水", "营养", "食物", "饮食", "热量"]
    assert any(term in second_response["response_content"] for term in nutrition_terms), "第二轮回复应包含饮食相关内容"
