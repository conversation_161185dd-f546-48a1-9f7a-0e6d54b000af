"""
缓存机制集成测试

测试AI助手各组件的缓存功能是否正常工作。
"""

import pytest
import time
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch

from app.services.ai_assistant.common.cache import (
    CacheService,
    MemoryCacheService,
    LRUCacheService
)
from app.services.ai_assistant.llm.proxy import LLMProxy
from app.services.ai_assistant.knowledge.retriever import KnowledgeRetriever
from app.services.ai_assistant.intent.handlers.fitness_advice import FitnessAdviceHandler
from app.services.ai_assistant.conversation.orchestrator import ConversationOrchestrator

# 测试查询和响应
TEST_QUERIES = {
    "q1": "如何开始健身计划？",
    "q2": "增肌需要吃什么食物？",
    "q3": "深蹲的正确姿势是什么？",
    "q4": "HIIT训练有哪些好处？"
}

TEST_RESPONSES = {
    "q1": "开始健身计划需要从基础动作学起，建议先掌握深蹲、俯卧撑等动作，每周进行3-4次训练...",
    "q2": "增肌需要摄入足够的蛋白质，如鸡胸肉、鱼、蛋、奶制品等，同时需要适量碳水化合物提供能量...",
    "q3": "正确的深蹲姿势包括：1. 双脚与肩同宽 2. 挺胸收腹 3. 臀部向后推 4. 膝盖不超过脚尖...",
    "q4": "HIIT训练的好处包括提高心肺功能、增强代谢率、节省时间、燃烧脂肪等..."
}

@pytest.fixture
def memory_cache():
    """创建内存缓存服务"""
    return MemoryCacheService()

@pytest.fixture
def lru_cache():
    """创建LRU缓存服务"""
    return LRUCacheService(max_size=10)

@pytest.fixture
def mock_llm_proxy(memory_cache):
    """提供带缓存的模拟LLM代理"""
    mock = AsyncMock(spec=LLMProxy)
    
    # 模拟chat方法，记录调用次数
    call_count = 0
    
    async def mock_chat(prompt, *args, **kwargs):
        nonlocal call_count
        call_count += 1
        
        # 根据提示内容返回不同的响应
        for key, query in TEST_QUERIES.items():
            if query in prompt:
                return TEST_RESPONSES[key]
        return f"LLM回答 #{call_count}"
    
    mock.chat.side_effect = mock_chat
    mock.call_count = lambda: call_count
    mock.cache_service = memory_cache
    
    return mock

@pytest.fixture
def mock_knowledge_retriever(memory_cache):
    """提供带缓存的模拟知识检索器"""
    mock = AsyncMock(spec=KnowledgeRetriever)
    
    # 模拟retrieve方法，记录调用次数
    call_count = 0
    
    async def mock_retrieve(query, *args, **kwargs):
        nonlocal call_count
        call_count += 1
        
        # 根据查询返回不同的知识结果
        if "深蹲" in query:
            return [{"content": "深蹲是一种复合性下肢训练动作", "metadata": {"source": "健身指南"}}]
        elif "增肌" in query:
            return [{"content": "增肌需要合理的训练计划和饮食", "metadata": {"source": "营养学指南"}}]
        else:
            return []
    
    mock.retrieve.side_effect = mock_retrieve
    mock.call_count = lambda: call_count
    mock.cache_service = memory_cache
    
    return mock

@pytest.fixture
def fitness_handler(mock_llm_proxy, mock_knowledge_retriever):
    """创建健身建议处理器"""
    handler = FitnessAdviceHandler(
        llm_proxy=mock_llm_proxy,
        knowledge_retriever=mock_knowledge_retriever
    )
    return handler

@pytest.fixture
def orchestrator(mock_llm_proxy, mock_knowledge_retriever):
    """创建对话协调器"""
    orchestrator = ConversationOrchestrator(
        llm_proxy=mock_llm_proxy,
        knowledge_retriever=mock_knowledge_retriever
    )
    return orchestrator

@pytest.mark.asyncio
async def test_memory_cache_basic_operations(memory_cache):
    """测试内存缓存的基本操作"""
    # 设置缓存项
    await memory_cache.set("key1", "value1")
    await memory_cache.set("key2", {"data": "value2"})
    
    # 获取缓存项
    value1 = await memory_cache.get("key1")
    value2 = await memory_cache.get("key2")
    
    assert value1 == "value1"
    assert value2 == {"data": "value2"}
    
    # 获取不存在的键
    value3 = await memory_cache.get("key3")
    assert value3 is None
    
    # 删除缓存项
    await memory_cache.delete("key1")
    value1_after = await memory_cache.get("key1")
    assert value1_after is None
    
    # 清除所有缓存
    await memory_cache.clear()
    value2_after = await memory_cache.get("key2")
    assert value2_after is None

@pytest.mark.asyncio
async def test_lru_cache_eviction(lru_cache):
    """测试LRU缓存的淘汰策略"""
    # 设置缓存容量为10
    assert lru_cache.max_size == 10
    
    # 添加11个项目，应该淘汰最早的
    for i in range(11):
        await lru_cache.set(f"key{i}", f"value{i}")
    
    # 第一个项目应该被淘汰
    value0 = await lru_cache.get("key0")
    assert value0 is None
    
    # 其他项目应该存在
    for i in range(1, 11):
        value = await lru_cache.get(f"key{i}")
        assert value == f"value{i}"
    
    # 访问key1，使其成为最近使用的
    await lru_cache.get("key1")
    
    # 再添加一个新项目，应该淘汰key2
    await lru_cache.set("key11", "value11")
    
    # key1应该还在，key2应该被淘汰
    assert await lru_cache.get("key1") == "value1"
    assert await lru_cache.get("key2") is None

@pytest.mark.asyncio
async def test_cache_expiration():
    """测试缓存过期策略"""
    cache = MemoryCacheService()
    
    # 设置带过期时间的缓存项
    await cache.set("short_lived", "will expire soon", expire_seconds=1)
    await cache.set("long_lived", "will stay longer", expire_seconds=10)
    
    # 立即获取应该都存在
    assert await cache.get("short_lived") == "will expire soon"
    assert await cache.get("long_lived") == "will stay longer"
    
    # 等待短期缓存过期
    await asyncio.sleep(1.1)
    
    # 短期缓存应该过期，长期缓存仍然存在
    assert await cache.get("short_lived") is None
    assert await cache.get("long_lived") == "will stay longer"

@pytest.mark.asyncio
async def test_llm_response_caching(mock_llm_proxy):
    """测试LLM响应缓存"""
    llm = mock_llm_proxy
    cache = llm.cache_service
    
    # 首次查询，应该调用LLM
    response1 = await llm.chat(f"用户问题: {TEST_QUERIES['q1']}")
    assert response1 == TEST_RESPONSES['q1']
    assert llm.call_count() == 1
    
    # 再次相同查询，应该使用缓存
    response2 = await llm.chat(f"用户问题: {TEST_QUERIES['q1']}")
    assert response2 == TEST_RESPONSES['q1']
    assert llm.call_count() == 1  # 调用次数不变
    
    # 不同查询，应该调用LLM
    response3 = await llm.chat(f"用户问题: {TEST_QUERIES['q2']}")
    assert response3 == TEST_RESPONSES['q2']
    assert llm.call_count() == 2

@pytest.mark.asyncio
async def test_knowledge_retrieval_caching(mock_knowledge_retriever):
    """测试知识检索结果缓存"""
    retriever = mock_knowledge_retriever
    
    # 首次查询，应该调用检索
    results1 = await retriever.retrieve("深蹲技巧")
    assert len(results1) > 0
    assert "深蹲" in results1[0]["content"]
    assert retriever.call_count() == 1
    
    # 再次相同查询，应该使用缓存
    results2 = await retriever.retrieve("深蹲技巧")
    assert len(results2) > 0
    assert results2 == results1
    assert retriever.call_count() == 1  # 调用次数不变
    
    # 不同查询，应该调用检索
    results3 = await retriever.retrieve("增肌方法")
    assert len(results3) > 0
    assert "增肌" in results3[0]["content"]
    assert retriever.call_count() == 2

@pytest.mark.asyncio
async def test_handler_uses_cache(fitness_handler, mock_llm_proxy, mock_knowledge_retriever):
    """测试健身建议处理器使用缓存"""
    user_id = "test_user"
    
    # 首次查询，应该调用LLM和知识检索
    response1 = await fitness_handler.handle(user_id, TEST_QUERIES['q3'])
    assert TEST_RESPONSES['q3'] in response1
    assert mock_llm_proxy.call_count() == 1
    assert mock_knowledge_retriever.call_count() == 1
    
    # 再次相同查询，应该使用缓存
    response2 = await fitness_handler.handle(user_id, TEST_QUERIES['q3'])
    assert response2 == response1
    assert mock_llm_proxy.call_count() == 1  # 调用次数不变
    assert mock_knowledge_retriever.call_count() == 1  # 调用次数不变
    
    # 不同查询，应该调用LLM和知识检索
    response3 = await fitness_handler.handle(user_id, TEST_QUERIES['q4'])
    assert TEST_RESPONSES['q4'] in response3
    assert mock_llm_proxy.call_count() == 2
    assert mock_knowledge_retriever.call_count() == 2

@pytest.mark.asyncio
async def test_orchestrator_end_to_end_caching(orchestrator, mock_llm_proxy):
    """测试对话协调器端到端缓存"""
    user_id = "test_user"
    
    # 首次消息处理
    response1 = await orchestrator.process_message(user_id, TEST_QUERIES['q1'])
    initial_call_count = mock_llm_proxy.call_count()
    
    # 相同用户发送完全相同的消息
    response2 = await orchestrator.process_message(user_id, TEST_QUERIES['q1'])
    
    # 验证使用了缓存
    assert mock_llm_proxy.call_count() == initial_call_count
    
    # 验证响应相同
    assert response1 == response2

@pytest.mark.asyncio
async def test_cache_performance():
    """测试缓存性能提升"""
    # 创建一个慢速的模拟函数
    async def slow_function(arg):
        await asyncio.sleep(0.5)  # 模拟耗时操作
        return f"Result for {arg}"
    
    # 创建缓存装饰器
    cache = MemoryCacheService()
    
    # 缓存版本的慢速函数
    async def cached_slow_function(arg):
        # 尝试从缓存获取
        cache_key = f"slow_func:{arg}"
        cached_result = await cache.get(cache_key)
        if cached_result is not None:
            return cached_result
        
        # 缓存未命中，调用原函数
        result = await slow_function(arg)
        
        # 存入缓存
        await cache.set(cache_key, result, expire_seconds=60)
        return result
    
    # 测试无缓存执行时间
    start_time = time.time()
    result1 = await slow_function("test")
    result2 = await slow_function("test")  # 再次调用相同参数
    uncached_time = time.time() - start_time
    
    # 测试有缓存执行时间
    start_time = time.time()
    result3 = await cached_slow_function("test")
    result4 = await cached_slow_function("test")  # 使用缓存
    cached_time = time.time() - start_time
    
    # 验证结果正确
    assert result1 == result2 == result3 == result4
    
    # 验证缓存提供了性能提升
    assert cached_time < uncached_time
    assert cached_time < 0.6  # 应该小于一次函数调用的时间