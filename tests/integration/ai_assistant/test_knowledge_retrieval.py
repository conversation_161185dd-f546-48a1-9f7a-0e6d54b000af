"""
知识检索集成测试

该模块测试知识检索系统的功能，包括知识检索器和向量存储的正确工作。
"""

import os
import asyncio
import pytest
import tempfile
import numpy as np
from typing import List, Dict, Any
from unittest.mock import AsyncMock, MagicMock, patch

from app.services.ai_assistant.knowledge import (
    KnowledgeRetriever,
    DefaultKnowledgeRetriever,
    VectorKnowledgeRetriever,
    VectorStore
)
from app.services.ai_assistant.llm.proxy import MockLLMProxy
from app.services.ai_assistant.intent.handlers.fitness_advice import FitnessAdviceHandler
from app.services.ai_assistant.llm.proxy import LLMProxy

# 测试数据
TEST_DOCUMENTS = [
    {
        "content": "深蹲是一种复合性下肢训练动作，主要锻炼股四头肌、臀大肌和腿后肌群。标准动作要求保持背部挺直，髋关节向后推，膝盖不超过脚尖。",
        "metadata": {"source": "健身基础指南", "category": "动作技巧", "difficulty": "初级"}
    },
    {
        "content": "进行增肌训练时，应保持足够的蛋白质摄入，一般建议每公斤体重1.6-2.2克蛋白质。同时确保充足的碳水化合物提供能量支持训练。",
        "metadata": {"source": "健身营养指南", "category": "营养建议", "difficulty": "中级"}
    },
    {
        "content": "对于初学者，建议每周进行3-4次力量训练，每次训练控制在45-60分钟，确保有足够的恢复时间，避免过度训练导致受伤。",
        "metadata": {"source": "健身计划指南", "category": "训练频率", "difficulty": "初级"}
    },
    {
        "content": "高强度间歇训练(HIIT)是一种高效的有氧训练方式，通过短时间的高强度运动和低强度恢复交替进行，可以在较短时间内达到良好的燃脂效果。",
        "metadata": {"source": "有氧训练指南", "category": "训练方法", "difficulty": "中级"}
    },
    {
        "content": "对于有腰椎问题的人群，应避免负重深蹲和硬拉等动作，可以选择更安全的替代动作如保加利亚分腿蹲或腿举机，减少脊柱压力。",
        "metadata": {"source": "特殊人群训练指南", "category": "安全建议", "difficulty": "特殊"}
    }
]

# 模拟嵌入向量
TEST_EMBEDDINGS = [
    np.random.rand(128) for _ in range(len(TEST_DOCUMENTS))
]

@pytest.fixture
def test_vector_store():
    """创建测试用向量存储"""
    store = VectorStore(dimension=128)
    
    # 添加测试文档
    docs = [doc["content"] for doc in TEST_DOCUMENTS]
    metadata = [doc["metadata"] for doc in TEST_DOCUMENTS]
    
    # 使用固定的测试嵌入向量
    store.add(docs=docs, embeddings=TEST_EMBEDDINGS, metadatas=metadata)
    
    return store

@pytest.fixture
def mock_llm_proxy():
    """提供模拟的LLM代理"""
    mock = AsyncMock(spec=LLMProxy)
    mock.embed_query.side_effect = lambda text: np.random.rand(128)
    mock.chat.return_value = "这是基于知识的回答"
    return mock

@pytest.fixture
def vector_retriever(test_vector_store, mock_llm_proxy):
    """创建向量知识检索器"""
    retriever = VectorKnowledgeRetriever(
        vector_store=test_vector_store,
        llm_proxy=mock_llm_proxy
    )
    return retriever

@pytest.fixture
def fitness_advice_handler(vector_retriever, mock_llm_proxy):
    """创建健身建议处理器"""
    handler = FitnessAdviceHandler(
        llm_proxy=mock_llm_proxy,
        knowledge_retriever=vector_retriever
    )
    return handler

class TestKnowledgeRetrieval:
    """知识检索集成测试"""
    
    @pytest.fixture
    def mock_llm_proxy(self):
        """模拟LLM代理"""
        proxy = MockLLMProxy()
        
        # 定义模拟的embedding生成函数
        async def mock_get_embeddings(texts: List[str]) -> List[List[float]]:
            # 为每个文本生成一个伪随机的嵌入向量
            result = []
            for text in texts:
                # 使用文本的哈希值作为随机种子，确保相同文本有相似的向量
                seed = hash(text) % 10000
                np.random.seed(seed)
                # 生成一个128维的向量，范围在[-1, 1]之间
                vector = np.random.uniform(-1, 1, 128).tolist()
                result.append(vector)
            return result
        
        # 替换代理的embedding方法
        proxy.get_embeddings = mock_get_embeddings
        return proxy
    
    @pytest.fixture
    def default_retriever(self):
        """默认知识检索器"""
        return DefaultKnowledgeRetriever()
    
    @pytest.fixture
    def vector_retriever(self, mock_llm_proxy):
        """向量知识检索器"""
        # 创建一个向量存储
        vector_store = VectorStore(
            embedding_dim=128,  # 使用128维的嵌入向量
            llm_proxy=mock_llm_proxy
        )
        
        # 创建检索器
        retriever = VectorKnowledgeRetriever(
            vector_store=vector_store,
            llm_proxy=mock_llm_proxy
        )
        
        return retriever

    @pytest.mark.asyncio
    async def test_default_retriever_retrieve(self, default_retriever):
        """测试默认知识检索器的检索功能"""
        # 获取检索结果
        results = await default_retriever.retrieve("健身锻炼的最佳时间是什么时候？")
        
        # 验证结果
        assert isinstance(results, list)
        assert len(results) > 0
        
        # 检查结果结构
        for item in results:
            assert "id" in item
            assert "content" in item
            assert "metadata" in item
            assert "score" in item
            assert isinstance(item["score"], float)
    
    @pytest.mark.asyncio
    async def test_default_retriever_add_knowledge(self, default_retriever):
        """测试默认知识检索器的添加知识功能"""
        # 添加知识
        texts = [
            "高强度间歇训练(HIIT)可以在短时间内提高有氧能力。",
            "睡眠对肌肉恢复和生长至关重要，每晚应保证7-8小时的睡眠。"
        ]
        
        metadatas = [
            {"category": "training", "type": "advice"},
            {"category": "recovery", "type": "fact"}
        ]
        
        # 添加知识
        doc_ids = await default_retriever.add_knowledge(texts, metadatas)
        
        # 验证返回的ID
        assert isinstance(doc_ids, list)
        assert len(doc_ids) == 2
        
        # 验证知识已添加
        results = await default_retriever.retrieve("睡眠")
        sleep_found = False
        for item in results:
            if "睡眠" in item["content"]:
                sleep_found = True
                break
        
        assert sleep_found, "添加的知识未被正确检索"
    
    @pytest.mark.asyncio
    async def test_vector_retriever_add_and_retrieve(self, vector_retriever):
        """测试向量知识检索器的添加和检索功能"""
        # 添加知识
        texts = [
            "健身初学者应该从基础动作开始，掌握正确的姿势。",
            "增肌需要保持热量盈余，每天摄入比消耗多300-500卡路里。",
            "减脂期间应该保持足够的蛋白质摄入，以避免肌肉流失。",
            "全身训练适合每周训练3-4次的健身者。",
            "分化训练适合中高级训练者，可以更深入地刺激特定肌群。"
        ]
        
        metadatas = [
            {"category": "beginner", "type": "advice"},
            {"category": "nutrition", "type": "bulking"},
            {"category": "nutrition", "type": "cutting"},
            {"category": "training", "type": "fullbody"},
            {"category": "training", "type": "split"}
        ]
        
        # 添加知识
        doc_ids = await vector_retriever.add_knowledge(texts, metadatas)
        
        # 验证返回的ID
        assert isinstance(doc_ids, list)
        assert len(doc_ids) == 5
        
        # 测试检索 - 通用查询
        results = await vector_retriever.retrieve("健身训练方法")
        assert len(results) > 0
        
        # 测试检索 - 具体查询
        results = await vector_retriever.retrieve("增肌饮食")
        bulking_found = False
        for item in results:
            if "增肌" in item["content"]:
                bulking_found = True
                # 验证分数
                assert 0 <= item["score"] <= 1
                break
        
        assert bulking_found, "未能检索到增肌相关内容"
        
        # 测试元数据过滤
        results = await vector_retriever.retrieve(
            query="训练方法",
            filter_metadata={"category": "training"}
        )
        
        training_count = 0
        for item in results:
            if item["metadata"]["category"] == "training":
                training_count += 1
        
        assert training_count > 0, "元数据过滤失败"
    
    @pytest.mark.asyncio
    async def test_vector_store_save_and_load(self, vector_retriever, mock_llm_proxy):
        """测试向量存储的保存和加载功能"""
        # 添加一些测试数据
        texts = [
            "深蹲是一个全身性的复合动作，主要锻炼下肢和核心。",
            "卧推是训练胸肌的基础动作，也锻炼肱三头肌和肩部。",
            "引体向上是锻炼背阔肌和二头肌的有效动作。"
        ]
        
        metadatas = [
            {"category": "exercise", "muscle": "legs"},
            {"category": "exercise", "muscle": "chest"},
            {"category": "exercise", "muscle": "back"}
        ]
        
        # 添加知识
        await vector_retriever.add_knowledge(texts, metadatas)
        
        # 创建临时文件保存索引
        with tempfile.NamedTemporaryFile(delete=False) as temp:
            temp_path = temp.name
            
            # 保存索引
            success = vector_retriever.save(temp_path)
            assert success, "索引保存失败"
            
            # 创建新的检索器并加载索引
            new_retriever = VectorKnowledgeRetriever(
                llm_proxy=mock_llm_proxy,
                embedding_dim=128,
                index_path=temp_path
            )
            
            # 测试新检索器
            results = await new_retriever.retrieve("胸肌训练")
            
            # 验证结果
            chest_found = False
            for item in results:
                if "胸肌" in item["content"]:
                    chest_found = True
                    break
            
            assert chest_found, "加载的索引未能正确检索知识"
            
            # 清理临时文件
            try:
                os.unlink(temp_path)
            except:
                pass
    
    @pytest.mark.asyncio
    async def test_retriever_in_context(self, vector_retriever):
        """测试在上下文中使用知识检索器"""
        # 添加知识
        texts = [
            "有氧运动应在力量训练后进行，以避免影响力量表现。",
            "训练后30分钟内摄入蛋白质和碳水化合物，有助于恢复和肌肉生长。",
            "过度训练的症状包括持续疲劳、性能下降和睡眠障碍。"
        ]
        
        metadatas = [
            {"category": "training", "type": "cardio"},
            {"category": "nutrition", "type": "recovery"},
            {"category": "recovery", "type": "warning"}
        ]
        
        # 添加知识
        await vector_retriever.add_knowledge(texts, metadatas)
        
        # 模拟对话上下文
        context = {
            "user_message": "我应该什么时候做有氧运动？",
            "intent": "workout_recommendation"
        }
        
        # 检索相关知识
        results = await vector_retriever.retrieve(context["user_message"])
        
        # 验证知识是否可以丰富上下文
        assert len(results) > 0
        
        # 验证是否找到了有氧运动的相关信息
        cardio_info_found = False
        for item in results:
            if "有氧运动" in item["content"]:
                cardio_info_found = True
                # 将知识添加到上下文
                if "knowledge" not in context:
                    context["knowledge"] = []
                context["knowledge"].append(item)
        
        assert cardio_info_found, "未能在上下文中添加相关知识"
        assert "knowledge" in context, "知识未添加到上下文"

@pytest.mark.asyncio
async def test_vector_search_accuracy(vector_retriever):
    """测试向量搜索准确性"""
    # 测试关键词搜索
    query = "深蹲正确姿势"
    results = await vector_retriever.retrieve(query)
    
    # 验证结果
    assert len(results) > 0
    
    # 模拟精确匹配(由于使用随机向量，我们只验证返回格式)
    assert "content" in results[0]
    assert "metadata" in results[0]
    assert "source" in results[0]["metadata"]

@pytest.mark.asyncio
async def test_metadata_filtering(vector_retriever):
    """测试元数据过滤功能"""
    # 按难度级别过滤
    query = "训练建议"
    filter_metadata = {"difficulty": "初级"}
    
    results = await vector_retriever.retrieve(
        query, 
        filter_metadata=filter_metadata,
        top_k=3
    )
    
    # 验证结果
    assert len(results) > 0
    for result in results:
        assert result["metadata"]["difficulty"] == "初级"
    
    # 按类别过滤
    filter_metadata = {"category": "营养建议"}
    results = await vector_retriever.retrieve(
        "蛋白质摄入", 
        filter_metadata=filter_metadata
    )
    
    # 验证结果
    assert len(results) > 0
    for result in results:
        assert result["metadata"]["category"] == "营养建议"

@pytest.mark.asyncio
async def test_knowledge_integration_in_responses(fitness_advice_handler):
    """测试知识整合到回答中"""
    user_id = "test_user"
    user_query = "初学者应该怎么安排训练频率？"
    
    # 模拟知识检索
    knowledge_results = [TEST_DOCUMENTS[2]]
    
    # 替换实际的检索调用
    with patch.object(fitness_advice_handler.knowledge_retriever, 'retrieve') as mock_retrieve:
        mock_retrieve.return_value = knowledge_results
        
        # 获取回答
        response = await fitness_advice_handler.handle(user_id, user_query)
        
        # 验证检索调用
        mock_retrieve.assert_called_once_with(user_query, top_k=3)
        
        # 验证LLM调用包含了知识内容
        llm_calls = fitness_advice_handler.llm_proxy.chat.call_args_list
        assert len(llm_calls) > 0
        
        # 验证传给LLM的提示中包含了知识内容
        prompt = llm_calls[0][1].get('prompt', '')
        assert "对于初学者，建议每周进行3-4次力量训练" in prompt

@pytest.mark.asyncio
async def test_fallback_strategy_when_no_knowledge(fitness_advice_handler):
    """测试无相关知识时的回退策略"""
    user_id = "test_user"
    user_query = "这是一个与健身无关的问题"
    
    # 模拟空知识检索结果
    with patch.object(fitness_advice_handler.knowledge_retriever, 'retrieve') as mock_retrieve:
        mock_retrieve.return_value = []
        
        # 获取回答
        response = await fitness_advice_handler.handle(user_id, user_query)
        
        # 验证检索调用
        mock_retrieve.assert_called_once()
        
        # 验证LLM调用时使用了回退策略
        llm_calls = fitness_advice_handler.llm_proxy.chat.call_args_list
        assert len(llm_calls) > 0
        
        # 验证提示中包含了回退指示
        prompt = llm_calls[0][1].get('prompt', '')
        assert "没有找到相关知识" in prompt or "基于你的专业知识" in prompt

@pytest.mark.asyncio
async def test_vector_store_save_and_load():
    """测试向量存储的保存和加载功能"""
    # 创建测试目录
    test_dir = "test_vector_index"
    os.makedirs(test_dir, exist_ok=True)
    
    try:
        # 创建并填充向量存储
        store = VectorStore(dimension=128)
        docs = [doc["content"] for doc in TEST_DOCUMENTS]
        metadata = [doc["metadata"] for doc in TEST_DOCUMENTS]
        store.add(docs=docs, embeddings=TEST_EMBEDDINGS, metadatas=metadata)
        
        # 保存索引
        index_path = os.path.join(test_dir, "test_index")
        store.save(index_path)
        
        # 验证索引文件已创建
        assert os.path.exists(index_path)
        
        # 创建新的向量存储并加载索引
        new_store = VectorStore(dimension=128)
        new_store.load(index_path)
        
        # 使用相同的查询向量进行搜索比较
        query_vector = np.random.rand(128)
        original_results = store.similarity_search_by_vector(
            query_vector, top_k=3
        )
        loaded_results = new_store.similarity_search_by_vector(
            query_vector, top_k=3
        )
        
        # 验证加载后的索引能够正常工作
        assert len(loaded_results) == len(original_results)
        
    finally:
        # 清理测试文件
        import shutil
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)

@pytest.mark.asyncio
async def test_default_knowledge_retriever():
    """测试默认知识检索器的回退行为"""
    retriever = DefaultKnowledgeRetriever()
    
    # 测试默认检索器始终返回空结果
    results = await retriever.retrieve("任何查询")
    assert isinstance(results, list)
    assert len(results) == 0