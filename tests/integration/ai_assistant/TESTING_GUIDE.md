# 健身AI助手系统测试指南

## 概述

本文档提供健身AI助手系统的测试指南，包括运行测试的方法、测试架构说明、常见问题排查以及如何扩展测试。

## 测试组织结构

测试根据关注点分为多个文件：

- `test_end_to_end_flow.py` - 端到端对话流程测试
- `test_bailian_integration.py` - 百炼LLM集成测试
- `test_conversation_states.py` - 对话状态管理测试
- `test_intent_handling.py` - 意图处理测试
- `test_caching.py` - 缓存机制测试
- `test_knowledge_retrieval.py` - 知识检索测试
- `test_user_scenarios.py` - 用户场景测试

## 运行测试

### 运行所有测试

```bash
python -m pytest tests/integration/ai_assistant/ -v
```

### 运行特定测试文件

```bash
python -m pytest tests/integration/ai_assistant/test_end_to_end_flow.py -v
```

### 运行特定测试

```bash
python -m pytest tests/integration/ai_assistant/test_end_to_end_flow.py::test_conversation_continuity -v
```

### 运行并生成覆盖率报告

```bash
python -m pytest tests/integration/ai_assistant/ --cov=app.services.ai_assistant --cov-report=html
```

## 测试设计原则

1. **隔离与模拟**：测试应尽可能使用模拟对象替代外部依赖
2. **关键词验证**：对于LLM响应，使用关键词验证而非精确匹配
3. **功能完整性**：优先验证功能完整性而非具体实现细节
4. **用户视角**：从用户视角出发，测试实际用户场景
5. **状态验证**：确保正确跟踪和管理对话状态

## 编写新测试

### 端到端测试模板

```python
@pytest.mark.asyncio
async def test_your_test_name():
    """
    测试简要描述
    
    验证内容：
    1. 第一个验证点
    2. 第二个验证点
    3. 第三个验证点
    """
    # 创建对话协调器
    orchestrator = ConversationOrchestrator()
    
    # 生成用户ID
    user_id = f"test_user_{int(time.time())}"
    
    # 用户消息
    message = "你的测试消息内容"
    
    # 处理消息
    response = await orchestrator.process_message(user_id, message)
    
    # 验证响应
    assert isinstance(response, dict)
    assert "content" in response
    assert isinstance(response["content"], str)
    
    # 验证响应内容（关键词验证）
    response_text = response["content"]
    for keyword in ["关键词1", "关键词2", "关键词3"]:
        assert keyword in response_text, f"回复中应包含关键词：{keyword}"
```

### 测试内容扩展方向

1. **多轮对话测试**：测试系统在多轮对话中的表现
2. **边缘情况测试**：测试系统对非典型输入的处理
3. **性能测试**：测试系统在高负载下的表现
4. **特殊用户测试**：测试系统对特殊用户群体的适应性
5. **长期记忆测试**：测试系统的长期记忆能力

## 测试数据管理

对于需要测试数据的测试，建议：

1. 使用固定的测试数据集
2. 将测试数据与测试代码分离
3. 使用工厂方法创建测试数据
4. 在测试完成后清理测试数据

## 故障排查

### 常见错误及解决方法

1. **测试初始化失败**：检查依赖项安装和环境变量
2. **缓存测试失败**：检查缓存键的计算方式，确保键的一致性
3. **LLM响应不一致**：使用模拟对象替代真实LLM服务
4. **状态管理测试失败**：检查状态存储和恢复逻辑

### CI/CD环境中的测试

1. 使用环境变量区分测试环境
2. 配置测试特定的配置文件
3. 使用模拟服务替代外部依赖

## 测试维护

为保持测试套件的可维护性：

1. 定期检查并更新测试，确保与当前系统行为一致
2. 移除过时的测试或标记为已弃用
3. 在系统功能变更时同步更新测试
4. 保持测试代码的可读性和简洁性

## 持续集成

配置CI流水线以自动运行测试：

```yaml
# .github/workflows/test.yml 示例
name: Run Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: '3.9'
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    - name: Run tests
      run: |
        python -m pytest tests/integration/ai_assistant/ -v
```

## 总结

健全的测试对于确保AI助手系统的质量和可靠性至关重要。遵循本指南中的原则和实践，可以帮助开发团队构建和维护高质量的测试套件，从而支持系统的持续发展和改进。 