"""
测试响应格式适配器

测试 ResponseAdapter 类处理不同格式响应的能力。
"""

import pytest
from app.services.ai_assistant.common.response_adapter import ResponseAdapter


class TestResponseAdapter:
    """测试响应格式适配器"""
    
    def test_to_text_with_string(self):
        """测试字符串响应转换"""
        response = "这是一个字符串响应"
        result = ResponseAdapter.to_text(response)
        assert result == response
        assert isinstance(result, str)
    
    def test_to_text_with_content_dict(self):
        """测试带content字段的字典响应转换"""
        response = {"content": "这是内容", "other_field": "其他字段"}
        result = ResponseAdapter.to_text(response)
        assert result == "这是内容"
        assert isinstance(result, str)
    
    def test_to_text_with_response_content_dict(self):
        """测试带response_content字段的字典响应转换"""
        response = {"response_content": "这是响应内容", "other_field": "其他字段"}
        result = ResponseAdapter.to_text(response)
        assert result == "这是响应内容"
        assert isinstance(result, str)
    
    def test_to_text_with_text_dict(self):
        """测试带text字段的字典响应转换"""
        response = {"text": "这是文本", "other_field": "其他字段"}
        result = ResponseAdapter.to_text(response)
        assert result == "这是文本"
        assert isinstance(result, str)
    
    def test_to_text_with_message_dict(self):
        """测试带message字段的字典响应转换"""
        response = {"message": "这是消息", "other_field": "其他字段"}
        result = ResponseAdapter.to_text(response)
        assert result == "这是消息"
        assert isinstance(result, str)
    
    def test_to_text_with_unknown_dict(self):
        """测试不包含已知字段的字典响应转换"""
        response = {"unknown_field": "未知字段", "other_field": "其他字段"}
        result = ResponseAdapter.to_text(response)
        assert isinstance(result, str)
        assert "未知字段" in result
    
    def test_get_text_response(self):
        """测试get_text_response方法"""
        response = {"content": "这是内容"}
        result = ResponseAdapter.get_text_response(response)
        assert result == "这是内容"
        assert isinstance(result, str)
    
    def test_extract_content(self):
        """测试extract_content方法"""
        response = {"content": "这是内容", "other_field": "其他字段"}
        result = ResponseAdapter.extract_content(response)
        assert result == "这是内容"
        
        response = {"response_content": "这是响应内容"}
        result = ResponseAdapter.extract_content(response)
        assert result == "这是响应内容"
        
        response = {"text": "这是文本"}
        result = ResponseAdapter.extract_content(response)
        assert result == "这是文本"
        
        response = {"message": "这是消息"}
        result = ResponseAdapter.extract_content(response)
        assert result == "这是消息"
        
        response = {"unknown_field": "未知字段"}
        result = ResponseAdapter.extract_content(response)
        assert result is None 