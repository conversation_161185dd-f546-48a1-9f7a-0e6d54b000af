"""
测试辅助工具模块

提供测试过程中需要的各种辅助函数、<PERSON><PERSON>对象、测试数据生成等功能
"""

import json
import uuid
import time
import random
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict

from app import models, schemas
from app.models.message import MessageRole


# 预定义的用户档案模板
USER_PROFILE_TEMPLATES = {
    "beginner_male": {
        "age": 25,
        "gender": 1,  # 男性
        "height": 175,
        "weight": 70,
        "fitness_goal": 3,  # 增肌
        "experience_level": 1,  # 初学者
        "activity_level": 2,  # 轻度活动
        "health_conditions": [],
        "allergies": []
    },
    "intermediate_female": {
        "age": 30,
        "gender": 2,  # 女性
        "height": 165,
        "weight": 60,
        "fitness_goal": 1,  # 减肥
        "experience_level": 2,  # 中级
        "activity_level": 3,  # 中度活动
        "health_conditions": [],
        "allergies": []
    },
    "advanced_athlete": {
        "age": 28,
        "gender": 1,  # 男性
        "height": 180,
        "weight": 85,
        "fitness_goal": 3,  # 增肌
        "experience_level": 3,  # 高级
        "activity_level": 5,  # 极高活动
        "health_conditions": [],
        "allergies": []
    },
    "weight_loss_focused": {
        "age": 35,
        "gender": 2,  # 女性
        "height": 160,
        "weight": 75,
        "fitness_goal": 1,  # 减肥
        "experience_level": 2,  # 中级
        "activity_level": 2,  # 轻度活动
        "health_conditions": [],
        "allergies": []
    }
}


@dataclass
class TestMessage:
    """测试消息数据结构"""
    content: str
    role: MessageRole
    expected_intent: Optional[str] = None
    expected_keywords: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class TestConversation:
    """测试对话数据结构"""
    session_id: str
    user_profile: Dict[str, Any]
    messages: List[TestMessage]
    expected_outcomes: Dict[str, Any]


def create_test_user_profile(template_name: str, **overrides) -> Dict[str, Any]:
    """
    创建测试用户档案

    Args:
        template_name: 模板名称
        **overrides: 覆盖的字段值

    Returns:
        用户档案字典
    """
    if template_name not in USER_PROFILE_TEMPLATES:
        raise ValueError(f"未知的用户档案模板: {template_name}")

    profile = USER_PROFILE_TEMPLATES[template_name].copy()
    profile.update(overrides)

    # 添加测试标识
    profile["user_id"] = f"test_user_{template_name}_{int(time.time())}"
    profile["is_test_user"] = True

    return profile


def create_test_user(db_session, user_data: Dict[str, Any]) -> models.User:
    """
    创建测试用户

    Args:
        db_session: 数据库会话
        user_data: 用户数据

    Returns:
        创建的用户对象
    """
    # 确保必需字段存在
    required_fields = {
        "openid": f"test_openid_{uuid.uuid4().hex[:8]}",
        "nickname": "测试用户",
        "email": f"test_{uuid.uuid4().hex[:8]}@example.com"
    }

    for field, default_value in required_fields.items():
        if field not in user_data:
            user_data[field] = default_value

    # 检查用户是否已存在（基于email）
    existing_user = db_session.query(models.User).filter(
        models.User.email == user_data.get("email")
    ).first()

    if existing_user:
        # 更新现有用户的信息
        for key, value in user_data.items():
            if hasattr(existing_user, key):
                setattr(existing_user, key, value)
        db_session.commit()
        db_session.refresh(existing_user)
        return existing_user

    # 创建新用户
    user_create = schemas.UserCreate(**user_data)
    user = models.User(**user_create.model_dump())

    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)

    return user


def create_test_conversation(
    db_session,
    user_id: int,
    session_id: Optional[str] = None
) -> models.Conversation:
    """
    创建测试对话

    Args:
        db_session: 数据库会话
        user_id: 用户ID
        session_id: 会话ID，如果为None则自动生成

    Returns:
        创建的对话对象
    """
    if session_id is None:
        session_id = f"test_session_{uuid.uuid4().hex[:8]}"

    conversation_data = {
        "user_id": user_id,
        "session_id": session_id,
        "is_active": True,
        "meta_info": {"test_conversation": True}
    }

    conversation = models.Conversation(**conversation_data)
    db_session.add(conversation)
    db_session.commit()
    db_session.refresh(conversation)

    return conversation


def create_test_message(
    db_session,
    conversation_id: int,
    user_id: int,
    content: str,
    role: MessageRole = MessageRole.USER,
    meta_info: Optional[Dict[str, Any]] = None
) -> models.Message:
    """
    创建测试消息

    Args:
        db_session: 数据库会话
        conversation_id: 对话ID
        user_id: 用户ID
        content: 消息内容
        role: 消息角色
        meta_info: 元数据

    Returns:
        创建的消息对象
    """
    message_data = {
        "conversation_id": conversation_id,
        "user_id": user_id,
        "content": content,
        "role": role,
        "meta_info": meta_info or {}
    }

    message = models.Message(**message_data)
    db_session.add(message)
    db_session.commit()
    db_session.refresh(message)

    return message


def generate_random_user_profile() -> Dict[str, Any]:
    """
    生成随机用户档案

    Returns:
        随机生成的用户档案
    """
    return {
        "age": random.randint(18, 65),
        "gender": random.choice([1, 2]),
        "height": random.randint(150, 200),
        "weight": random.randint(45, 120),
        "fitness_goal": random.choice([1, 2, 3]),
        "experience_level": random.choice([1, 2, 3]),
        "activity_level": random.choice([1, 2, 3, 4, 5]),
        "user_id": f"random_user_{uuid.uuid4().hex[:8]}",
        "is_test_user": True
    }


def load_test_scenarios() -> Dict[str, List[Dict[str, Any]]]:
    """
    加载测试场景

    Returns:
        测试场景字典
    """
    return {
        "fitness_consultation": [
            {
                "message": "你好，我想开始健身",
                "expected_intent": "fitness_advice",
                "expected_keywords": ["健身", "开始"]
            },
            {
                "message": "我的目标是减肥10公斤",
                "expected_intent": "fitness_advice",
                "expected_keywords": ["减肥", "目标"]
            },
            {
                "message": "请帮我制定训练计划",
                "expected_intent": "training_plan",
                "expected_keywords": ["训练计划", "制定"]
            },
            {
                "message": "深蹲的正确姿势是什么？",
                "expected_intent": "exercise_action",
                "expected_keywords": ["深蹲", "姿势"]
            }
        ],
        "nutrition_guidance": [
            {
                "message": "我想了解健身饮食",
                "expected_intent": "diet_advice",
                "expected_keywords": ["饮食", "健身"]
            },
            {
                "message": "增肌期间应该吃什么？",
                "expected_intent": "diet_advice",
                "expected_keywords": ["增肌", "吃什么"]
            },
            {
                "message": "蛋白质摄入量建议",
                "expected_intent": "diet_advice",
                "expected_keywords": ["蛋白质", "摄入量"]
            }
        ],
        "training_guidance": [
            {
                "message": "胸部训练动作推荐",
                "expected_intent": "exercise_action",
                "expected_keywords": ["胸部", "训练", "动作"]
            },
            {
                "message": "如何提高卧推重量？",
                "expected_intent": "exercise_action",
                "expected_keywords": ["卧推", "重量", "提高"]
            },
            {
                "message": "训练频率建议",
                "expected_intent": "fitness_advice",
                "expected_keywords": ["训练", "频率"]
            }
        ]
    }


def create_mock_ai_response(
    intent: str = "general_chat",
    confidence: float = 0.8,
    response_content: str = "这是一个模拟的AI响应"
) -> Dict[str, Any]:
    """
    创建模拟AI响应

    Args:
        intent: 意图类型
        confidence: 置信度
        response_content: 响应内容

    Returns:
        模拟的AI响应
    """
    return {
        "response_content": response_content,
        "intent": intent,
        "confidence": confidence,
        "timestamp": int(time.time()),
        "current_state": "idle",
        "conversation_id": f"mock_conversation_{uuid.uuid4().hex[:8]}",
        "processing_time_ms": random.randint(100, 2000)
    }


def validate_ai_response(response: Dict[str, Any]) -> List[str]:
    """
    验证AI响应格式

    Args:
        response: AI响应

    Returns:
        验证错误列表，空列表表示验证通过
    """
    errors = []

    # 检查必需字段
    required_fields = ["response_content", "intent", "confidence", "timestamp"]
    for field in required_fields:
        if field not in response:
            errors.append(f"缺少必需字段: {field}")

    # 检查字段类型
    if "response_content" in response and not isinstance(response["response_content"], str):
        errors.append("response_content必须是字符串类型")

    if "confidence" in response:
        confidence = response["confidence"]
        if not isinstance(confidence, (int, float)) or not (0 <= confidence <= 1):
            errors.append("confidence必须是0-1之间的数值")

    if "timestamp" in response and not isinstance(response["timestamp"], (int, float)):
        errors.append("timestamp必须是数值类型")

    return errors


def measure_response_time(func):
    """
    响应时间测量装饰器

    Args:
        func: 要测量的函数

    Returns:
        装饰后的函数
    """
    async def wrapper(*args, **kwargs):
        start_time = time.time()
        result = await func(*args, **kwargs)
        end_time = time.time()

        response_time = end_time - start_time

        # 如果结果是字典，添加响应时间信息
        if isinstance(result, dict):
            result["response_time"] = response_time
            result["response_time_ms"] = response_time * 1000

        return result

    return wrapper


def generate_test_report(test_results: List[Dict[str, Any]]) -> str:
    """
    生成测试报告

    Args:
        test_results: 测试结果列表

    Returns:
        格式化的测试报告
    """
    if not test_results:
        return "无测试结果"

    total_tests = len(test_results)
    successful_tests = sum(1 for result in test_results if result.get("success", False))
    success_rate = successful_tests / total_tests * 100

    response_times = [r.get("response_time", 0) for r in test_results if "response_time" in r]
    avg_response_time = sum(response_times) / len(response_times) if response_times else 0

    intent_distribution = {}
    for result in test_results:
        intent = result.get("intent", "unknown")
        intent_distribution[intent] = intent_distribution.get(intent, 0) + 1

    report = f"""
📊 测试报告
{'='*50}
🎯 总体统计:
   - 总测试数: {total_tests}
   - 成功数: {successful_tests}
   - 失败数: {total_tests - successful_tests}
   - 成功率: {success_rate:.1f}%

⏱️ 性能统计:
   - 平均响应时间: {avg_response_time:.2f}秒
   - 最快响应: {min(response_times):.2f}秒 (如果有数据)
   - 最慢响应: {max(response_times):.2f}秒 (如果有数据)

🎯 意图分布:
"""

    for intent, count in intent_distribution.items():
        percentage = count / total_tests * 100
        report += f"   - {intent}: {count} ({percentage:.1f}%)\n"

    report += f"\n📅 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

    return report


def cleanup_test_data(db_session, test_user_ids: List[int]):
    """
    清理测试数据

    Args:
        db_session: 数据库会话
        test_user_ids: 测试用户ID列表
    """
    try:
        # 删除测试用户的消息
        db_session.query(models.Message).filter(
            models.Message.user_id.in_(test_user_ids)
        ).delete(synchronize_session=False)

        # 删除测试用户的对话
        db_session.query(models.Conversation).filter(
            models.Conversation.user_id.in_(test_user_ids)
        ).delete(synchronize_session=False)

        # 删除测试用户
        db_session.query(models.User).filter(
            models.User.id.in_(test_user_ids)
        ).delete(synchronize_session=False)

        db_session.commit()

    except Exception as e:
        db_session.rollback()
        raise e


# 常用的测试断言函数
def assert_valid_response(response: Dict[str, Any]):
    """断言响应有效"""
    errors = validate_ai_response(response)
    assert not errors, f"响应验证失败: {errors}"


def assert_response_time(response_time: float, max_time: float = 5.0):
    """断言响应时间"""
    assert response_time <= max_time, f"响应时间 {response_time:.2f}s 超过最大允许时间 {max_time}s"


def assert_intent_match(actual_intent: str, expected_intent: str, strict: bool = False):
    """断言意图匹配"""
    if strict:
        assert actual_intent == expected_intent, f"意图不匹配: 期望 {expected_intent}, 实际 {actual_intent}"
    else:
        # 宽松匹配，允许一定的误差
        if actual_intent != expected_intent:
            print(f"⚠️ 意图不完全匹配: 期望 {expected_intent}, 实际 {actual_intent}")


def assert_contains_keywords(text: str, keywords: List[str], min_matches: int = 1):
    """断言包含关键词"""
    text_lower = text.lower()
    matches = sum(1 for keyword in keywords if keyword.lower() in text_lower)
    assert matches >= min_matches, f"文本中至少应包含 {min_matches} 个关键词，实际匹配 {matches} 个"
