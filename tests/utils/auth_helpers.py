"""
测试认证辅助工具

提供测试环境中的JWT token生成和认证相关功能
"""

from datetime import datetime, timedelta
from typing import Dict, Optional
from jose import jwt
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../"))

from app.core.config import settings


def generate_test_jwt(user_id: int, expires_delta: Optional[timedelta] = None) -> str:
    """
    生成测试用JWT token
    
    Args:
        user_id: 用户ID
        expires_delta: 过期时间间隔，默认为15分钟
        
    Returns:
        str: JWT token字符串
    """
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    
    to_encode = {"exp": expire, "sub": str(user_id)}
    encoded_jwt = jwt.encode(
        to_encode, 
        settings.SECRET_KEY, 
        algorithm=settings.ALGORITHM
    )
    return encoded_jwt


def create_test_auth_headers(user_id: int) -> Dict[str, str]:
    """
    创建测试认证头
    
    Args:
        user_id: 用户ID
        
    Returns:
        Dict[str, str]: 包含Authorization头的字典
    """
    token = generate_test_jwt(user_id)
    return {"Authorization": f"Bearer {token}"}


def create_long_lived_test_token(user_id: int, days: int = 30) -> str:
    """
    创建长期有效的测试token
    
    Args:
        user_id: 用户ID
        days: 有效天数，默认30天
        
    Returns:
        str: JWT token字符串
    """
    expires_delta = timedelta(days=days)
    return generate_test_jwt(user_id, expires_delta)


def verify_test_token(token: str) -> Optional[int]:
    """
    验证测试token并返回用户ID
    
    Args:
        token: JWT token字符串
        
    Returns:
        Optional[int]: 用户ID，如果token无效则返回None
    """
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        user_id = payload.get("sub")
        if user_id:
            return int(user_id)
    except Exception:
        pass
    return None


class TestAuthManager:
    """测试认证管理器"""
    
    def __init__(self, default_user_id: int = 15):
        self.default_user_id = default_user_id
        self._cached_token = None
        self._token_expires = None
    
    def get_auth_headers(self, user_id: Optional[int] = None) -> Dict[str, str]:
        """获取认证头"""
        uid = user_id or self.default_user_id
        
        # 检查缓存的token是否仍然有效
        if (self._cached_token and 
            self._token_expires and 
            datetime.utcnow() < self._token_expires - timedelta(minutes=1)):
            return {"Authorization": f"Bearer {self._cached_token}"}
        
        # 生成新token
        expires_delta = timedelta(hours=1)  # 1小时有效期
        self._cached_token = generate_test_jwt(uid, expires_delta)
        self._token_expires = datetime.utcnow() + expires_delta
        
        return {"Authorization": f"Bearer {self._cached_token}"}
    
    def get_token(self, user_id: Optional[int] = None) -> str:
        """获取token字符串"""
        headers = self.get_auth_headers(user_id)
        return headers["Authorization"].replace("Bearer ", "")
    
    def invalidate_cache(self):
        """清除缓存的token"""
        self._cached_token = None
        self._token_expires = None


# 创建默认的测试认证管理器实例
default_auth_manager = TestAuthManager()


def get_test_auth_headers(user_id: Optional[int] = None) -> Dict[str, str]:
    """
    便捷函数：获取测试认证头
    
    Args:
        user_id: 用户ID，默认使用15
        
    Returns:
        Dict[str, str]: 认证头字典
    """
    return default_auth_manager.get_auth_headers(user_id)


def get_test_token(user_id: Optional[int] = None) -> str:
    """
    便捷函数：获取测试token
    
    Args:
        user_id: 用户ID，默认使用15
        
    Returns:
        str: JWT token字符串
    """
    return default_auth_manager.get_token(user_id)


if __name__ == "__main__":
    # 测试功能
    print("🧪 测试认证辅助工具")
    
    # 生成测试token
    user_id = 15
    token = generate_test_jwt(user_id)
    print(f"✅ 生成测试token: {token[:50]}...")
    
    # 验证token
    verified_user_id = verify_test_token(token)
    print(f"✅ 验证token成功，用户ID: {verified_user_id}")
    
    # 生成认证头
    headers = create_test_auth_headers(user_id)
    print(f"✅ 认证头: {headers}")
    
    # 测试认证管理器
    auth_manager = TestAuthManager()
    headers2 = auth_manager.get_auth_headers()
    print(f"✅ 管理器认证头: {headers2}")
    
    print("🎉 所有测试通过！")
