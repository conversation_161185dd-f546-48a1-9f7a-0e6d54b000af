---
description: 
globs: 
alwaysApply: false
---
# 健身AI助手 LangGraph 实现指南

本规则提供健身AI助手基于LangGraph框架的实现指南，参考 [AI-Agent-LangGraph-Implementation.md](mdc:docs/agent/AI-Agent-LangGraph-Implementation.md) 中的详细设计。

## 架构概览

健身AI助手采用基于图状工作流的架构，主要包含：
- 专家节点路由系统
- 状态管理与持久化
- 流式响应机制
- 多级缓存优化

## 核心组件

### 状态定义
状态管理通过 `ConversationState` 类实现，包含会话ID、用户ID、消息历史、意图和参数等信息。

### 专家节点
系统包含多个专家节点：
- 路由节点：使用 `LLM_INTENT_RECOGNITION_MODEL` 识别用户意图
- 参数收集器：收集并标准化训练计划所需参数
- 训练计划专家：生成个性化训练计划
- 健身咨询专家：提供专业健身建议
- 用户信息收集器：收集用户健身相关信息
- 通用聊天专家：处理非专业领域的闲聊

### 图状工作流
使用 `StateGraph` 构建工作流，定义节点间的转换条件和逻辑流程。

## 接口规范

系统支持两种接口：
1. REST API：`/api/v1/ai-chat/message`
2. WebSocket流式响应：`/api/v1/ai-chat/stream/{session_id}`

## 开发指南

1. 专家节点开发：
   - 在 `app/services/langgraph/nodes/` 下实现新的专家节点
   - 遵循异步编程模式，使用 `async/await`
   - 确保返回格式为 `{"next": "node_name"}`

2. 状态管理：
   - 避免在状态中存储过大的数据
   - 使用压缩机制控制消息历史大小
   - 确保状态对象可序列化

3. 缓存策略：
   - 频繁访问的状态优先缓存
   - 使用合理的过期时间
   - 实现缓存预热和异步更新

请参考 [AI-Agent-LangGraph-Implementation.md](mdc:docs/agent/AI-Agent-LangGraph-Implementation.md) 获取完整实现细节和后续开发计划。
