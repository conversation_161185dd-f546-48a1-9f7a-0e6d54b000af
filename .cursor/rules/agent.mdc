---
description: 
globs: 
alwaysApply: true
---
# .cursorrule - Configuration for AI Features (Fitness AI Assistant)

# Project Overview
project:
  description: >
    Develop a real-time conversational AI assistant for fitness and diet advice,
    integrated into an existing FastAPI backend. Uses Lang<PERSON>hain for LLM orchestration,
    RAG with a local knowledge base, PostgreSQL for user/exercise data, and Redis for caching/memory.
    Follows the modular implementation plan outlined in `docs/agent.md`.
  language: Python
  framework: FastAPI
  database: PostgreSQL (via SQLAlchemy)
  cache: Redis
  ai_framework: LangChain
  key_libraries: [fastapi, uvicorn, sqlalchemy, alembic, pydantic, langchain, langchain-community, faiss-cpu, tiktoken, tongyi-qwen, zhipuai]

# Key Files and Directories
files:
  important:
    - docs/agent.md # PRIMARY implementation plan - refer to this constantly!
    - README.md # Project overview and existing structure
    - app/main.py # FastAPI app entry point
    - app/core/config.py # Settings management
    - app/db/base_class.py # SQLAlchemy base
    - app/models/user.py # Existing User model
    - app/models/exercise.py # Existing Exercise model
    - app/api/v1/api.py # Main API router
    - requirements.txt # Dependencies (needs updating)
    - .env # Environment variables (API keys, DB URL)

  new_components_locations: # Where to place NEW code based on agent.md
    - app/services/llm_proxy_service.py
    - app/services/knowledge_service.py
    - app/services/conversation_service.py
    - app/services/sql_tool_service.py
    - app/services/training_plan_service.py
    - app/models/conversation.py
    - app/models/message.py
    - app/models/qa_pair.py
    - app/models/training_plan.py
    - app/models/workout.py
    - app/models/workout_exercise.py
    - app/crud/crud_conversation.py
    - app/crud/crud_message.py
    - app/crud/crud_qa_pair.py
    - app/crud/crud_training_plan.py # (+workout, workout_exercise)
    - app/schemas/training_plan.py # Pydantic schemas for plan generation
    - app/api/endpoints/chat.py
    - app/api/endpoints/training_plan.py
    - /data/vectorstore/ # For FAISS index file

# Implementation Strategy (Based on docs/agent.md)
implementation_strategy:
  overview: >
    Follow the 5-phase modular implementation plan detailed in `docs/agent.md`.
    Prioritize core functionality (DB tools, personalization, plan generation, streaming)
    before RAG enhancement and full memory/optimization.
  phases:
    - Phase 1: Core Framework & LLM Setup (Basic LLM call, new models, basic logging)
    - Phase 2: Database Tools & Personalization (SQL Tools, Agent/Chain with tools, Intent Recog, Active Query)
    - Phase 3: Training Plan Generation & Streaming (Structured Output, Plan Service, WebSocket)
    - Phase 4: RAG Knowledge Base Integration (FAISS, Embeddings, Update ConversationService)
    - Phase 5: Memory, Testing, Optimization & Monitoring (LangChain Memory, Testing, Caching, Prometheus)
  approach: >
    - Develop features module by module (Service, CRUD, Model, API).
    - Use LangChain components (Chains, Agents, Tools, Parsers, Memory) as described in the plan.
    - Emphasize asynchronous programming (`async`/`await`) for I/O operations (DB, LLM, API calls).
    - Refer to `docs/agent.md` for detailed task breakdown within each phase.

# Coding Rules and Conventions
rules:
  - Place primary business logic within services in `app/services/`.
  - Define SQLAlchemy models in `app/models/`, inheriting from `app.db.base_class.Base`.
  - Implement database interactions via CRUD functions in `app/crud/`. Pass `db: Session` dependency.
  - Define API endpoints using FastAPI `APIRouter` in `app/api/endpoints/`. Use dependency injection (`Depends`).
  - Use Pydantic models defined in `app/schemas/` for API request/response validation and structured LLM output.
  - Load configuration (API keys, DB URLs, model names) from `app/core/config.py` (which reads from `.env`). Do not hardcode secrets.
  - Use `async def` for functions involving database I/O, external API calls (LLM), or WebSocket handling.
  - Initialize LangChain components (LLMs, Embeddings, Tools) correctly, passing necessary configuration (e.g., API keys, model names from config).
  - When generating code for specific services or models, refer to the class/method names and structure outlined in `docs/agent.md`.
  - Use standard Python logging (`import logging; logger = logging.getLogger(__name__)`).
  - Ensure new database models have corresponding Alembic migration scripts generated.

# Style Guide
style_guide:
  language_version: Python 3.11
  format: Adhere to PEP 8. Use linters like Flake8/Black if configured.
  typing: Use type hints extensively for function signatures and variables.
  docstrings: Write clear docstrings for all public classes, methods, and complex functions explaining their purpose, args, and returns.
  models: Follow SQLAlchemy model structure.
  schemas: Follow Pydantic model structure.

# Documentation Rules
documentation:
  source_of_truth: `docs/agent.md` contains the detailed implementation plan and phase breakdown.
  readme: `README.md` provides a high-level overview of the existing project.
  in_code: Use docstrings for explanations. Add comments only for non-obvious logic.
  api: FastAPI automatically generates OpenAPI documentation (`/docs`, `/redoc`). Ensure Pydantic schemas and endpoint docstrings are descriptive.

# Testing Approach (Based on agent.md Phase 5)
tests:
  strategy: >
    Testing will be implemented primarily in Phase 5, but consider writing tests earlier for critical components.
  unit_tests: Target core logic within services (`app/services/`) and utility functions. Mock external dependencies (LLM APIs, DB).
  integration_tests: Test API endpoints (`app/api/endpoints/`) ensuring correct interaction between services, CRUD, and database. Use a test database.
  framework: Pytest is recommended (already in `requirements.txt`).
