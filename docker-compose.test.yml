version: '3.8'

services:
  test-db:
    image: postgres:13
    environment:
      POSTGRES_DB: fitness_test_db
      POSTGRES_USER: test_user
      POSTGRES_PASSWORD: test_password
    ports:
      - "5433:5432"
    volumes:
      - test_db_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U test_user -d fitness_test_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  test-redis:
    image: redis:6-alpine
    ports:
      - "6380:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  test-app:
    build: .
    environment:
      - DATABASE_URL=*************************************************/fitness_test_db
      - REDIS_URL=redis://test-redis:6379/0
      - TESTING=true
      - PYTHONPATH=/app
    depends_on:
      test-db:
        condition: service_healthy
      test-redis:
        condition: service_healthy
    volumes:
      - .:/app
    working_dir: /app
    command: tail -f /dev/null  # Keep container running for testing

volumes:
  test_db_data:
