from sqlalchemy.orm import Session
import asyncio
from datetime import datetime

from app import models, schemas
from app.db.session import SessionLocal
from app.services.community_service import CommunityService
from app.schemas.community import PostCreate

async def create_simple_post():
    """为ID为1的用户创建简单帖子"""
    print("正在创建简单帖子...")
    db = SessionLocal()
    
    try:
        # 检查用户是否存在
        user = db.query(models.User).filter(models.User.id == 1).first()
        if not user:
            print("错误: ID为1的用户不存在")
            return
            
        # 创建社区服务实例
        community_service = CommunityService(db)
        
        # 创建帖子
        post_data = PostCreate(
            title=f"测试训练记录 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            content="这是一个通过API测试创建的训练记录",
            visibility="Everyone",
            tags=["测试", "API", "训练记录"]
        )
        
        post = await community_service.create_post_with_workout(1, post_data)
        print(f"成功创建示例帖子! Post ID: {post.id}")
        print(f"标题: {post.title}")
        print(f"内容: {post.content}")
        print(f"标签: {post.tags if hasattr(post, 'tags') else '无标签'}")
        
    except Exception as e:
        print(f"创建帖子时出错: {str(e)}")
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(create_simple_post()) 