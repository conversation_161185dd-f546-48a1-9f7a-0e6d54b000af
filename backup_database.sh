#!/bin/bash

# 创建备份目录
BACKUP_DIR="/home/<USER>/backend/database_backups"
mkdir -p $BACKUP_DIR

# 设置备份文件名（使用时间戳）
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/fitness_db_backup_$TIMESTAMP.sql"

# 执行数据库备份
echo "开始备份数据库到 $BACKUP_FILE..."
docker exec backend-db-1 pg_dump -U postgres -d fitness_db > $BACKUP_FILE

# 检查备份是否成功
if [ $? -eq 0 ]; then
    echo "数据库备份成功完成！"
    echo "备份文件: $BACKUP_FILE"
    echo "文件大小: $(du -h $BACKUP_FILE | cut -f1)"
else
    echo "数据库备份失败！"
    exit 1
fi

# 保留最近30个备份，删除旧备份
echo "清理旧备份文件..."
ls -tp $BACKUP_DIR/fitness_db_backup_*.sql | grep -v '/$' | tail -n +31 | xargs -I {} rm -- {}
echo "备份清理完成，保留最近30个备份"

# 列出当前所有备份
echo "当前备份列表:"
ls -lh $BACKUP_DIR/fitness_db_backup_*.sql | sort -r 