# 数据目录
data/
data/images.db
data/.DS_Store
data/._.DS_Store
data/**/.DS_Store
data/**/._.DS_Store
data.zip

# Python
__pycache__/
*/__pycache__/
*/**/__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
*.pyc
*/*.pyc
*/**/*.pyc

# 虚拟环境
venv/
venv/**/*
.venv/
.venv/**/*
ENV/
env/
.env/

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store
**/.DS_Store
**/._.DS_Store

# 日志文件
*.log
logs/
logs.txt
uvicorn.log
database_backups/backup.log

# 本地配置文件
# .env
.env.local
.env.*.local
src/fitness-coach/.env

# 数据库文件
*.db
*.sqlite3

# 临时文件
*.tmp
*.temp
.coverage
htmlcov/

# Alembic版本文件中的pycache
alembic/versions/__pycache__/

# src目录下的缓存文件
src/**/__pycache__/
src/**/*.pyc
src/fitness-coach/uvicorn.log
src/fitness-coach/fitness_coach.egg-info 
