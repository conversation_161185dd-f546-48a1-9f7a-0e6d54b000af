{% extends "admin/layout.html" %}

{% block title %}用户管理 - 健身教练后台管理{% endblock %}

{% block page_title %}用户管理{% endblock %}

{% block header_buttons %}
<form class="d-flex" action="/admin/users" method="get">
    <input type="text" name="q" class="form-control me-2" placeholder="搜索用户..." value="{{ query or '' }}">
    <button type="submit" class="btn btn-sm btn-outline-secondary">搜索</button>
</form>
{% endblock %}

{% block content %}
<div class="card mb-4">
    <div class="card-body">
        <div class="table-container">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>昵称</th>
                        <th>头像</th>
                        <th>手机号</th>
                        <th>性别</th>
                        <th>年龄</th>
                        <th>体重</th>
                        <th>身高</th>
                        <th>BMI</th>
                        <th>注册时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr>
                        <td>{{ user.id }}</td>
                        <td>{{ user.nickname or '未设置' }}</td>
                        <td>
                            {% if user.avatar_url %}
                            <img src="{{ user.avatar_url }}" alt="Avatar" class="img-thumbnail" width="40" height="40">
                            {% else %}
                            无头像
                            {% endif %}
                        </td>
                        <td>{{ user.phone or '未设置' }}</td>
                        <td>{{ user.gender or '未设置' }}</td>
                        <td>{{ user.age or '未设置' }}</td>
                        <td>{{ user.weight or '未设置' }}</td>
                        <td>{{ user.height or '未设置' }}</td>
                        <td>{{ user.bmi or '未计算' }}</td>
                        <td>{{ user.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                        <td>
                            <a href="/admin/users/{{ user.id }}" class="btn btn-sm btn-primary">详情</a>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="11" class="text-center">暂无用户数据</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 分页控件 -->
{% if total_pages > 1 %}
<nav>
    <ul class="pagination justify-content-center">
        <li class="page-item {% if page == 1 %}disabled{% endif %}">
            <a class="page-link" href="?page={{ page - 1 }}{% if query %}&q={{ query }}{% endif %}" tabindex="-1">上一页</a>
        </li>
        
        {% for i in range(1, total_pages + 1) %}
        <li class="page-item {% if page == i %}active{% endif %}">
            <a class="page-link" href="?page={{ i }}{% if query %}&q={{ query }}{% endif %}">{{ i }}</a>
        </li>
        {% endfor %}
        
        <li class="page-item {% if page == total_pages %}disabled{% endif %}">
            <a class="page-link" href="?page={{ page + 1 }}{% if query %}&q={{ query }}{% endif %}">下一页</a>
        </li>
    </ul>
</nav>
{% endif %}
{% endblock %} 