{% extends "admin/layout.html" %}

{% block title %}用户详情 - 健身教练后台管理{% endblock %}

{% block page_title %}用户详情{% endblock %}

{% block header_buttons %}
<a href="/admin/users" class="btn btn-sm btn-outline-secondary">
    <span data-feather="arrow-left"></span>
    返回列表
</a>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                基本信息
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    {% if user.avatar_url %}
                    <img src="{{ user.avatar_url }}" alt="头像" class="img-thumbnail rounded-circle" style="width: 120px; height: 120px; object-fit: cover;">
                    {% else %}
                    <div class="bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center mx-auto" style="width: 120px; height: 120px;">
                        <span class="h1">{{ user.nickname[0] if user.nickname else '?' }}</span>
                    </div>
                    {% endif %}
                    <h4 class="mt-2">{{ user.nickname or '未设置昵称' }}</h4>
                    <p class="text-muted">ID: {{ user.id }}</p>
                </div>

                <table class="table">
                    <tbody>
                        <tr>
                            <th>手机号</th>
                            <td>{{ user.phone or '未设置' }}</td>
                        </tr>
                        <tr>
                            <th>性别</th>
                            <td>
                                {% if user.gender == 'male' %}
                                男
                                {% elif user.gender == 'female' %}
                                女
                                {% elif user.gender == 'other' %}
                                其他
                                {% else %}
                                未设置
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>年龄</th>
                            <td>{{ user.age or '未设置' }}</td>
                        </tr>
                        <tr>
                            <th>地区</th>
                            <td>{{ (user.country + ' ' + user.province + ' ' + user.city) if (user.country and user.province and user.city) else '未设置' }}</td>
                        </tr>
                        <tr>
                            <th>注册时间</th>
                            <td>{{ user.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                        </tr>
                        <tr>
                            <th>更新时间</th>
                            <td>{{ user.updated_at.strftime('%Y-%m-%d %H:%M:%S') if user.updated_at else '未更新' }}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="col-md-8 mb-4">
        <div class="card mb-4">
            <div class="card-header">
                健身数据
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h5 class="card-title">身高</h5>
                                <p class="card-text display-6">{{ user.height or '--' }} <small class="text-muted">cm</small></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h5 class="card-title">体重</h5>
                                <p class="card-text display-6">{{ user.weight or '--' }} <small class="text-muted">kg</small></p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h5 class="card-title">BMI</h5>
                                <p class="card-text display-6">{{ user.bmi or '--' }}</p>
                                <p class="card-text">
                                    {% if user.bmi %}
                                        {% if user.bmi < 18.5 %}
                                        <span class="badge bg-warning">偏瘦</span>
                                        {% elif user.bmi < 24 %}
                                        <span class="badge bg-success">正常</span>
                                        {% elif user.bmi < 28 %}
                                        <span class="badge bg-warning">超重</span>
                                        {% else %}
                                        <span class="badge bg-danger">肥胖</span>
                                        {% endif %}
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h5 class="card-title">日均消耗热量</h5>
                                <p class="card-text display-6">{{ user.tedd or '--' }} <small class="text-muted">kcal</small></p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-6 mb-3">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h5 class="card-title">活动水平</h5>
                                <p class="card-text">
                                    {% if user.activity_level == 1 %}
                                    <span class="badge bg-secondary">久坐不动</span>
                                    {% elif user.activity_level == 2 %}
                                    <span class="badge bg-info">轻度活动</span>
                                    {% elif user.activity_level == 3 %}
                                    <span class="badge bg-primary">中度活动</span>
                                    {% elif user.activity_level == 4 %}
                                    <span class="badge bg-warning">高度活动</span>
                                    {% elif user.activity_level == 5 %}
                                    <span class="badge bg-danger">极度活动</span>
                                    {% else %}
                                    未设置
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h5 class="card-title">健身目标</h5>
                                <p class="card-text">
                                    {% if user.fitness_goal == 'weight_loss' %}
                                    <span class="badge bg-info">减脂</span>
                                    {% elif user.fitness_goal == 'muscle_gain' %}
                                    <span class="badge bg-success">增肌</span>
                                    {% elif user.fitness_goal == 'maintenance' %}
                                    <span class="badge bg-primary">保持</span>
                                    {% elif user.fitness_goal == 'endurance' %}
                                    <span class="badge bg-warning">耐力</span>
                                    {% else %}
                                    未设置
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                用户设置
            </div>
            <div class="card-body">
                {% if user_setting %}
                <table class="table">
                    <tbody>
                        <tr>
                            <th>通知开关</th>
                            <td>
                                {% if user_setting.notification_enabled %}
                                <span class="badge bg-success">开启</span>
                                {% else %}
                                <span class="badge bg-secondary">关闭</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>设置创建时间</th>
                            <td>{{ user_setting.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                        </tr>
                        <tr>
                            <th>设置更新时间</th>
                            <td>{{ user_setting.updated_at.strftime('%Y-%m-%d %H:%M:%S') if user_setting.updated_at else '未更新' }}</td>
                        </tr>
                    </tbody>
                </table>
                {% else %}
                <p class="text-center text-muted">该用户尚未设置任何配置项</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %} 