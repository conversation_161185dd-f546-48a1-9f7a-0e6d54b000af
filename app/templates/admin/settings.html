{% extends "admin/layout.html" %}

{% block title %}用户设置 - 健身教练后台管理{% endblock %}

{% block page_title %}用户设置{% endblock %}

{% block header_buttons %}
<form class="d-flex" action="/admin/settings" method="get">
    <input type="text" name="q" class="form-control me-2" placeholder="搜索用户ID..." value="{{ query or '' }}">
    <button type="submit" class="btn btn-sm btn-outline-secondary">搜索</button>
</form>
{% endblock %}

{% block content %}
<div class="card mb-4">
    <div class="card-body">
        <div class="table-container">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>用户ID</th>
                        <th>用户昵称</th>
                        <th>通知开关</th>
                        <th>创建时间</th>
                        <th>更新时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for setting in settings %}
                    <tr>
                        <td>{{ setting.id }}</td>
                        <td>{{ setting.user_id }}</td>
                        <td>{{ setting.user.nickname if setting.user else '未知' }}</td>
                        <td>
                            {% if setting.notification_enabled %}
                            <span class="badge bg-success">开启</span>
                            {% else %}
                            <span class="badge bg-secondary">关闭</span>
                            {% endif %}
                        </td>
                        <td>{{ setting.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                        <td>{{ setting.updated_at.strftime('%Y-%m-%d %H:%M:%S') if setting.updated_at else '未更新' }}</td>
                        <td>
                            <a href="/admin/users/{{ setting.user_id }}" class="btn btn-sm btn-primary">查看用户</a>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="7" class="text-center">暂无设置数据</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 分页控件 -->
{% if total_pages > 1 %}
<nav>
    <ul class="pagination justify-content-center">
        <li class="page-item {% if page == 1 %}disabled{% endif %}">
            <a class="page-link" href="?page={{ page - 1 }}{% if query %}&q={{ query }}{% endif %}" tabindex="-1">上一页</a>
        </li>
        
        {% for i in range(1, total_pages + 1) %}
        <li class="page-item {% if page == i %}active{% endif %}">
            <a class="page-link" href="?page={{ i }}{% if query %}&q={{ query }}{% endif %}">{{ i }}</a>
        </li>
        {% endfor %}
        
        <li class="page-item {% if page == total_pages %}disabled{% endif %}">
            <a class="page-link" href="?page={{ page + 1 }}{% if query %}&q={{ query }}{% endif %}">下一页</a>
        </li>
    </ul>
</nav>
{% endif %}
{% endblock %} 