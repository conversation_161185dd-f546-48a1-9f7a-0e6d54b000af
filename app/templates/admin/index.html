{% extends "admin/layout.html" %}

{% block title %}数据概览 - 健身教练后台管理{% endblock %}

{% block page_title %}数据概览{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-4 mb-4">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <h5 class="card-title">用户总数</h5>
                <p class="card-text display-4">{{ user_count }}</p>
                <p class="card-text">截至目前的总注册用户数量</p>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-4">
        <div class="card text-white bg-success">
            <div class="card-body">
                <h5 class="card-title">今日新增</h5>
                <p class="card-text display-4">{{ new_today }}</p>
                <p class="card-text">今日新增用户数量</p>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-4">
        <div class="card text-white bg-info">
            <div class="card-body">
                <h5 class="card-title">分享次数</h5>
                <p class="card-text display-4">{{ share_count }}</p>
                <p class="card-text">累计分享追踪次数</p>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                最近注册的用户
            </div>
            <div class="card-body">
                <div class="table-container">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>昵称</th>
                                <th>手机号</th>
                                <th>性别</th>
                                <th>注册时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in recent_users %}
                            <tr>
                                <td>{{ user.id }}</td>
                                <td>{{ user.nickname or '未设置' }}</td>
                                <td>{{ user.phone or '未设置' }}</td>
                                <td>{{ user.gender or '未设置' }}</td>
                                <td>{{ user.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                <td><a href="/admin/users/{{ user.id }}" class="btn btn-sm btn-primary">查看</a></td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="6" class="text-center">暂无用户数据</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 