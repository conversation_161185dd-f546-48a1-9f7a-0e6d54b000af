{% extends "admin/layout.html" %}

{% block title %}分享追踪 - 健身教练后台管理{% endblock %}

{% block page_title %}分享追踪{% endblock %}

{% block header_buttons %}
<form class="d-flex" action="/admin/share_tracks" method="get">
    <input type="text" name="q" class="form-control me-2" placeholder="搜索用户ID..." value="{{ query or '' }}">
    <button type="submit" class="btn btn-sm btn-outline-secondary">搜索</button>
</form>
{% endblock %}

{% block content %}
<div class="card mb-4">
    <div class="card-body">
        <div class="table-container">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>分享用户</th>
                        <th>扫描用户</th>
                        <th>分享类型</th>
                        <th>页面</th>
                        <th>分享时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for track in tracks %}
                    <tr>
                        <td>{{ track.id }}</td>
                        <td>{{ track.shared_by }}</td>
                        <td>{{ track.scanned_by or '未扫描' }}</td>
                        <td>
                            {% if track.share_type == 'menu' %}
                            <span class="badge bg-primary">菜单分享</span>
                            {% elif track.share_type == 'button' %}
                            <span class="badge bg-success">按钮分享</span>
                            {% elif track.share_type == 'qrcode' %}
                            <span class="badge bg-info">二维码分享</span>
                            {% else %}
                            <span class="badge bg-secondary">{{ track.share_type }}</span>
                            {% endif %}
                        </td>
                        <td>{{ track.page }}</td>
                        <td>{{ track.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                        <td>
                            <a href="/admin/users/{{ track.shared_by }}" class="btn btn-sm btn-primary">查看分享用户</a>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="7" class="text-center">暂无分享追踪数据</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 分页控件 -->
{% if total_pages > 1 %}
<nav>
    <ul class="pagination justify-content-center">
        <li class="page-item {% if page == 1 %}disabled{% endif %}">
            <a class="page-link" href="?page={{ page - 1 }}{% if query %}&q={{ query }}{% endif %}" tabindex="-1">上一页</a>
        </li>
        
        {% for i in range(1, total_pages + 1) %}
        <li class="page-item {% if page == i %}active{% endif %}">
            <a class="page-link" href="?page={{ i }}{% if query %}&q={{ query }}{% endif %}">{{ i }}</a>
        </li>
        {% endfor %}
        
        <li class="page-item {% if page == total_pages %}disabled{% endif %}">
            <a class="page-link" href="?page={{ page + 1 }}{% if query %}&q={{ query }}{% endif %}">下一页</a>
        </li>
    </ul>
</nav>
{% endif %}
{% endblock %} 