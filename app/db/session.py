from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
import logging
from datetime import datetime
import json

# 导入Base
from app.models.base import Base

logger = logging.getLogger("fitness-coach-api")

# 删除重复的Base声明

class MockModel:
    """模拟的数据库模型基类"""
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)

    def to_dict(self):
        """将模型转换为字典"""
        return {k: v for k, v in self.__dict__.items() if not k.startswith('_')}

    def __repr__(self):
        """可读的模型表示"""
        attrs = ', '.join(f"{k}={v}" for k, v in self.__dict__.items() if not k.startswith('_'))
        return f"{self.__class__.__name__}({attrs})"

# 定义模拟会话类供开发和测试使用
class MockQuery:
    def __init__(self, model_class=None, results=None):
        self.model_class = model_class

        # 如果结果是普通的字典列表，将它们转换为模型对象
        if results and isinstance(results[0], dict):
            self.results = []
            for item in results:
                if model_class:
                    self.results.append(model_class(**item))
                else:
                    self.results.append(MockModel(**item))
        else:
            self.results = results or []

    def filter(self, *args, **kwargs):
        return self

    def filter_by(self, **kwargs):
        return self

    def first(self):
        if self.results and len(self.results) > 0:
            return self.results[0]
        return None

    def all(self):
        return self.results

    def offset(self, n):
        return self

    def limit(self, n):
        return self

    def order_by(self, *args):
        return self

    def scalar(self):
        """返回查询的第一个元素，或者如果是计数查询，返回10"""
        try:
            # 特殊处理: 对于计数查询，返回10
            if str(self.model_class).lower().find('count') >= 0:
                return 10

            # 对于模型类的scalar调用
            if self.results and len(self.results) > 0:
                if hasattr(self.results[0], 'scalar'):
                    return self.results[0].scalar()
                return 10  # 默认值
            return 0
        except Exception as e:
            logger.error(f"MockQuery.scalar 错误: {str(e)}")
            return 10  # 出错时返回默认值

    def between(self, start_date, end_date):
        return self

    def count(self):
        return 10

    def join(self, *args, **kwargs):
        return self

    def outerjoin(self, *args, **kwargs):
        return self

    def group_by(self, *args, **kwargs):
        return self

    def having(self, *args, **kwargs):
        return self

    def subquery(self, *args, **kwargs):
        return self

    def exists(self, *args, **kwargs):
        return True

    def update(self, values, **kwargs):
        return 1

    def delete(self, **kwargs):
        return 1

    def ilike(self, other):
        return True

    def cast(self, type_):
        return self

class MockSession:
    def __init__(self):
        self.committed = False
        self.closed = False

        # 获取当前时间作为创建时间
        now = datetime.now()

        # 存储测试用户数据
        self.test_data = {
            "users": [
                {
                    "id": 1,
                    "openid": "test_openid",
                    "nickname": "测试用户",
                    "avatar_url": "https://example.com/avatar.jpg",
                    "phone": "13800138000",
                    "gender": "male",
                    "country": "China",
                    "province": "Zhejiang",
                    "city": "Hangzhou",
                    "age": 30,
                    "weight": 70.0,
                    "height": 175.0,
                    "activity_level": 3,
                    "bmi": 22.9,
                    "tedd": 2500,
                    "created_at": now,
                    "updated_at": now,
                    "completed": True,
                    "email": "<EMAIL>"
                }
            ],
            "user_settings": [
                {
                    "id": 1,
                    "user_id": 1,
                    "notification_enabled": True,
                    "created_at": now,
                    "updated_at": now
                }
            ],
            "share_tracks": [
                {
                    "id": 1,
                    "shared_by": 1,
                    "scanned_by": None,
                    "page": "index",
                    "share_type": "qrcode",
                    "created_at": now,
                    "scanned_at": None
                }
            ]
        }

    def commit(self):
        self.committed = True

    def rollback(self):
        pass

    def close(self):
        self.closed = True

    def add(self, obj):
        pass

    def delete(self, obj):
        pass

    def refresh(self, obj):
        pass

    def query(self, *args, **kwargs):
        try:
            # 处理函数查询，比如 func.count
            if len(args) == 1 and str(args[0]).startswith('count'):
                return MockQuery(args[0], [{"count": 10}])

            # 简单处理常见的查询情况
            if len(args) > 0 and hasattr(args[0], "__tablename__"):
                table_name = args[0].__tablename__
                if table_name == "users" and "users" in self.test_data:
                    return MockQuery(args[0], self.test_data["users"])
                elif table_name == "user_settings" and "user_settings" in self.test_data:
                    return MockQuery(args[0], self.test_data["user_settings"])
                elif table_name == "share_tracks" and "share_tracks" in self.test_data:
                    return MockQuery(args[0], self.test_data["share_tracks"])
        except Exception as e:
            logger.error(f"MockSession.query 错误: {str(e)}")

        # 默认返回空查询
        return MockQuery()

# 全局变量，标记是否使用模拟会话
USE_MOCK_SESSION = False  # 使用真实数据库会话
engine = None
SessionLocal = None

# 在应用启动时初始化数据库连接
def init_db():
    global engine, SessionLocal, USE_MOCK_SESSION

    try:
        # 使用真实数据库
        USE_MOCK_SESSION = False
        # 直接使用配置的数据库URL
        db_url = settings.DATABASE_URL

        # 使用URL创建引擎，并优化连接池配置
        engine = create_engine(
            db_url,
            pool_size=50,  # 设置连接池大小
            max_overflow=50,  # 最大溢出连接数
            pool_timeout=20,  # 连接超时时间
            pool_recycle=1200,  # 连接重用时间(30分钟)
            pool_pre_ping=True,  # 连接前检查
            echo=False  # 禁用SQL语句日志记录，无论是否为开发环境
        )
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        logger.info("数据库连接成功初始化，连接池大小: 50，最大溢出: 50")
    except Exception as e:
        logger.error(f"数据库连接失败: {str(e)}")
        # 如果连接失败，使用模拟会话
        USE_MOCK_SESSION = True
        # 创建一个函数，返回模拟会话
        SessionLocal = lambda: MockSession()
        logger.warning("使用模拟数据库会话")

# 初始化数据库连接
init_db()

# get_db 现在是一个简单的生成器，根据全局状态返回会话
def get_db():
    """获取数据库会话"""
    db = None
    try:
        # 根据全局状态决定返回真实会话还是模拟会话
        if USE_MOCK_SESSION:
            db = MockSession()
        else:
            db = SessionLocal()
        yield db
    finally:
        if db:
            try:
                db.close()
            except Exception as e:
                logger.error(f"关闭数据库会话出错: {str(e)}")

# 初始化数据库
def init_db():
    Base.metadata.create_all(bind=engine)