from app.db.base_class import Base
from app.models.user import User  # noqa

# 导入团队相关模型
from app.models.team import (  # noqa
    Team,
    TeamMembership,
    ClientRelation,
    TrainingPlanTemplate,
    ClientTrainingPlan,
    TrainingSession,
    SessionExerciseRecord,
)

# 导入游戏化系统模型
from app.models.gamification import (  # noqa
    UserLevel,
    UserAttribute,
    UserTitle,
    Card,
    UserCard,
    CardSynthesisRecipe,
    CardSynthesisIngredient,
    Currency,
    CurrencyTransaction,
    ShopItem,
    UserPurchase,
    Achievement,
    UserAchievement,
    Milestone,
    UserMilestone,
    Task,
    UserTask,
    DailyCheckIn
)