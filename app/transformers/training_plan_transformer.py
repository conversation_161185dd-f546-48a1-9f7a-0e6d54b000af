"""
训练计划数据转换器
"""
import logging
import re
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime

from app import crud, models
from app.schemas.training_plan import TrainingPlanSchema, DailyWorkoutSchema
from app.utils.training_plan_utils import (
    get_body_part_name,
    get_fitness_goal_name,
    format_equipment_list,
    format_body_parts_list,
    safe_get
)
from app.utils.exercise_utils import (
    determine_training_scenario,
    format_candidate_exercises,
    remove_duplicate_exercises
)

logger = logging.getLogger(__name__)

def parse_rest_seconds(rest_value: Any) -> int:
    """
    解析休息时间值，将带单位的字符串转换为整数秒数

    Args:
        rest_value: 休息时间值，可能是整数、字符串或其他类型

    Returns:
        整数形式的休息秒数
    """
    # 默认休息时间（秒）
    default_seconds = 60

    # 如果是整数，直接返回
    if isinstance(rest_value, int):
        return rest_value

    # 如果不是字符串，尝试转换为字符串
    if not isinstance(rest_value, str):
        try:
            # 尝试直接转换为整数
            return int(rest_value)
        except (ValueError, TypeError):
            logger.warning(f"无法将休息时间 '{rest_value}' 转换为整数，使用默认值 {default_seconds}")
            return default_seconds

    # 处理带单位的字符串，如 "90秒"、"1.5分钟"
    try:
        # 移除所有空白字符
        rest_str = rest_value.strip()

        # 匹配数字部分
        number_match = re.match(r'^(\d+\.?\d*)', rest_str)
        if not number_match:
            logger.warning(f"休息时间字符串 '{rest_str}' 不包含有效数字，使用默认值 {default_seconds}")
            return default_seconds

        # 提取数字部分
        number_str = number_match.group(1)
        number = float(number_str)

        # 检查单位
        if "分" in rest_str or "min" in rest_str.lower():
            # 如果是分钟，转换为秒
            return int(number * 60)
        else:
            # 默认假设是秒
            return int(number)
    except Exception as e:
        logger.warning(f"解析休息时间 '{rest_value}' 时出错: {str(e)}，使用默认值 {default_seconds}")
        return default_seconds

class TrainingPlanTransformer:
    """训练计划数据转换器"""

    @staticmethod
    def build_plan_context(
        user: models.User,
        training_history: List[Any],
        duration_weeks: int,
        days_per_week: int,
        fitness_goal: Optional[int],
        available_equipment: Optional[List[int]],
        focus_body_parts: Optional[List[int]],
        time_per_workout: Optional[int],
        additional_notes: Optional[str]
    ) -> Dict[str, Any]:
        """
        构建训练计划生成上下文

        Args:
            user: 用户对象
            training_history: 训练历史
            duration_weeks: 计划持续周数
            days_per_week: 每周训练天数
            fitness_goal: 健身目标
            available_equipment: 可用器材ID列表
            focus_body_parts: 重点训练部位ID列表
            time_per_workout: 每次训练时长（分钟）
            additional_notes: 额外备注

        Returns:
            上下文字典
        """
        # 构建用户健身水平信息
        fitness_level_info = f"""
用户信息:
- 性别: {'男' if user.gender == 1 else '女' if user.gender == 2 else '未知'}
- 年龄: {user.age or '未知'}
- 身高: {user.height or '未知'} cm
- 体重: {user.weight or '未知'} kg
- 健身经验: {user.experience_level or '初学者'}
- 健身目标: {get_fitness_goal_name(fitness_goal) if fitness_goal else (get_fitness_goal_name(user.fitness_goal) if user.fitness_goal else '未指定')}
        """

        # 构建训练历史信息
        if training_history:
            recent_exercises = []
            for record in training_history[:5]:  # 只显示最近5条记录
                exercise_name = record.get("exercise_name", "未知动作")
                workout_date = record.get("workout_date", "未知日期")
                recent_exercises.append(f"- {workout_date}: {exercise_name}")

            training_history_info = f"""
最近训练历史:
{''.join(recent_exercises)}
            """
        else:
            training_history_info = "最近训练历史: 无"

        # 获取器材和身体部位名称
        equipment_names = format_equipment_list(available_equipment or [])
        body_part_names = format_body_parts_list(focus_body_parts or [])

        # 构建训练需求
        training_requirements = f"""
训练计划需求:
- 持续时间: {duration_weeks} 周
- 频率: 每周 {days_per_week} 天
- 每次训练时长: {time_per_workout or '不限'} 分钟
- 可用器材: {equipment_names}
- 重点训练部位: {body_part_names}
- 额外要求: {additional_notes or '无'}
        """

        return {
            "user_info": fitness_level_info,
            "training_history": training_history_info,
            "requirements": training_requirements,
            "user_id": user.id,
            "duration_weeks": duration_weeks,
            "days_per_week": days_per_week,
            "available_equipment": available_equipment or [],
            "focus_body_parts": focus_body_parts or []
        }

    @staticmethod
    def build_daily_workout_context(
        user: models.User,
        training_history: List[Any],
        available_time: int,
        target_body_parts: Optional[List[int]],
        available_equipment: Optional[List[int]],
        recovery_level: Optional[int],
        additional_notes: Optional[str]
    ) -> Dict[str, Any]:
        """
        构建单日训练计划生成上下文

        Args:
            user: 用户对象
            training_history: 训练历史
            available_time: 可用时间（分钟）
            target_body_parts: 目标训练部位ID列表
            available_equipment: 可用器材ID列表
            recovery_level: 恢复程度（1-10）
            additional_notes: 额外备注

        Returns:
            上下文字典
        """
        # 构建用户健身水平信息
        fitness_level_info = f"""
用户信息:
- 性别: {'男' if user.gender == 1 else '女' if user.gender == 2 else '未知'}
- 年龄: {user.age or '未知'}
- 身高: {user.height or '未知'} cm
- 体重: {user.weight or '未知'} kg
- 健身经验: {user.experience_level or '初学者'}
- 健身目标: {get_fitness_goal_name(user.fitness_goal) if user.fitness_goal else '未指定'}
        """

        # 构建训练历史信息
        if training_history:
            # 分析最近训练的身体部位
            body_part_counts = {}
            for record in training_history:
                body_part = record.get("body_part_name")
                if body_part:
                    body_part_counts[body_part] = body_part_counts.get(body_part, 0) + 1

            # 找出最常训练的部位
            frequent_parts = sorted(body_part_counts.items(), key=lambda x: x[1], reverse=True)
            frequent_parts_str = ", ".join([f"{part}({count}次)" for part, count in frequent_parts[:3]])

            # 构建训练历史文本
            recent_exercises = []
            for record in training_history[:5]:  # 只显示最近5条记录
                exercise_name = record.get("exercise_name", "未知动作")
                workout_date = record.get("workout_date", "未知日期")
                body_part = record.get("body_part_name", "未知部位")
                recent_exercises.append(f"- {workout_date}: {exercise_name} ({body_part})")

            training_history_info = f"""
最近训练历史:
{''.join(recent_exercises)}

最常训练的部位: {frequent_parts_str}
            """
        else:
            training_history_info = "最近训练历史: 无"

        # 获取器材和身体部位名称
        equipment_names = format_equipment_list(available_equipment or [])
        body_part_names = format_body_parts_list(target_body_parts or [])

        # 分析身体部位训练频率
        body_part_analysis = ""
        if training_history and target_body_parts:
            target_part_names = [get_body_part_name(bp_id) for bp_id in target_body_parts if get_body_part_name(bp_id)]
            part_last_trained = {}

            for part_name in target_part_names:
                last_date = None
                for record in training_history:
                    if record.get("body_part_name") == part_name:
                        last_date = record.get("workout_date")
                        break
                part_last_trained[part_name] = last_date or "未训练过"

            if part_last_trained:
                analysis_items = [f"- {part}: 上次训练时间 {date}" for part, date in part_last_trained.items()]
                body_part_analysis = f"""
目标部位训练分析:
{''.join(analysis_items)}
                """

        # 构建训练需求
        training_requirements = f"""
今日训练需求:
- 可用时间: {available_time} 分钟
- 目标训练部位: {body_part_names}
- 可用器材: {equipment_names}
- 恢复程度: {recovery_level}/10 (1表示疲劳，10表示完全恢复)
- 额外要求: {additional_notes or '无'}
{body_part_analysis}
        """

        return {
            "user_info": fitness_level_info,
            "training_history": training_history_info,
            "requirements": training_requirements,
            "user_id": user.id,
            "available_time": available_time,
            "target_body_parts": target_body_parts or [],
            "available_equipment": available_equipment or [],
            "recovery_level": recovery_level
        }

    @staticmethod
    def transform_training_plan_to_db(user_id: int, plan_data: TrainingPlanSchema, db) -> Dict[str, Any]:
        """
        将训练计划数据转换为数据库模型并保存

        Args:
            user_id: 用户ID
            plan_data: 训练计划数据
            db: 数据库会话

        Returns:
            保存后的训练计划
        """
        try:
            # 创建训练计划
            plan_in = {
                "user_id": user_id,
                "plan_name": plan_data.plan_name,
                "description": plan_data.description,
                "duration_weeks": plan_data.duration_weeks,
                "is_active": True,
                "status": "active"
            }

            db_plan = crud.crud_training_plan.create(db, obj_in=plan_in)
            logger.info(f"创建训练计划: id={db_plan.id}, name={db_plan.plan_name}")

            # 创建训练日
            for workout in plan_data.workouts:
                workout_in = {
                    "training_plan_id": db_plan.id,
                    "name": workout.workout_name,
                    "day_number": workout.day_number,
                    "day_of_week": workout.day_of_week,
                    "description": workout.description,
                    "estimated_duration": workout.estimated_duration
                }

                db_workout = crud.crud_workout.create(db, obj_in=workout_in)
                logger.info(f"创建训练日: id={db_workout.id}, name={db_workout.name}, day={db_workout.day_number}")

                # 创建训练动作
                for i, exercise in enumerate(workout.exercises, 1):
                    exercise_in = {
                        "workout_id": db_workout.id,
                        "exercise_id": exercise.exercise_id,
                        "sets": exercise.sets,
                        "reps": exercise.reps,
                        "rest_seconds": exercise.rest_seconds,
                        "order": i,
                        "notes": exercise.notes
                    }

                    db_exercise = crud.crud_workout_exercise.create(db, obj_in=exercise_in)
                    logger.info(f"创建训练动作: workout_id={db_workout.id}, exercise_id={exercise.exercise_id}")

            # 获取完整的训练计划（包含所有关联数据）
            return crud.crud_training_plan.get_with_workouts(db, id=db_plan.id)

        except Exception as e:
            logger.error(f"保存训练计划时出错: {str(e)}")
            # 回滚事务
            db.rollback()
            raise ValueError(f"保存训练计划失败: {str(e)}")

    @staticmethod
    def transform_daily_workout_to_db(
        user_id: int,
        workout_data: DailyWorkoutSchema,
        db,
        fitness_goal: Optional[int] = None,
        experience_level: Optional[int] = None,
        target_body_parts: Optional[List[int]] = None,
        training_scenario: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        将单日训练计划数据转换为数据库模型并保存

        Args:
            user_id: 用户ID
            workout_data: 单日训练计划数据
            db: 数据库会话
            fitness_goal: 健身目标
            experience_level: 经验水平
            target_body_parts: 目标训练部位ID列表
            training_scenario: 训练场景

        Returns:
            保存后的训练计划，包含完整动作信息
        """
        # 记录函数调用开始
        logger.info(f"进入transform_daily_workout_to_db函数，参数: user_id={user_id}, target_body_parts={target_body_parts}")

        # 记录workout_data的基本信息
        workout_name = safe_get(workout_data, "workout_name")
        description = safe_get(workout_data, "description")
        estimated_duration = safe_get(workout_data, "estimated_duration")

        logger.info(f"训练计划基本信息: 名称={workout_name}, 时长={estimated_duration}分钟")

        # 记录训练动作信息
        exercises_count = len(workout_data.exercises) if hasattr(workout_data, "exercises") else 0
        logger.info(f"训练动作数量: {exercises_count}")

        # 记录目标部位信息
        if target_body_parts:
            body_part_names = [get_body_part_name(bp_id) for bp_id in target_body_parts]
            logger.info(f"目标训练部位: IDs={target_body_parts}, 名称={body_part_names}")
        else:
            logger.warning("未指定目标训练部位")
        try:
            # 1. 创建训练计划和训练日基础结构
            today = datetime.now().strftime("%Y-%m-%d")
            plan_in = {
                "user_id": user_id,
                "plan_name": f"单日训练: {today}",
                "description": f"系统为 {today} 生成的单日训练",
                "duration_weeks": 1,
                "is_active": True,
                "is_template": False,
                "status": "active",
                "fitness_goal": fitness_goal,
                "experience_level": experience_level
            }

            db_plan = crud.crud_training_plan.create(db, obj_in=plan_in)

            # 使用 workout_data 中的 target_body_parts 如果传入的为 None
            if target_body_parts is None and hasattr(workout_data, "target_body_parts"):
                target_body_parts = workout_data.target_body_parts

            workout_in = {
                "training_plan_id": db_plan.id,
                "name": safe_get(workout_data, "workout_name"),
                "day_number": 1,
                "description": safe_get(workout_data, "description"),
                "estimated_duration": safe_get(workout_data, "estimated_duration"),
                "target_body_parts": target_body_parts,
                "training_scenario": training_scenario
            }

            db_workout = crud.crud_workout.create(db, obj_in=workout_in)

            # 2. 添加训练动作
            logger.info(f"处理 {len(workout_data.exercises)} 个训练动作")
            for i, exercise in enumerate(workout_data.exercises, 1):
                # 处理不同类型的 exercise 对象
                logger.debug(f"处理第 {i} 个训练动作: {exercise}")
                if isinstance(exercise, dict):
                    # 如果是字典，直接使用字典访问方式
                    # 获取休息时间，支持多种字段名
                    rest_value = exercise.get("rest_seconds",
                                exercise.get("rest_time_seconds",
                                exercise.get("rest_time", 60)))  # 支持多种字段名

                    # 解析休息时间，处理可能带有单位的字符串
                    rest_seconds = parse_rest_seconds(rest_value)
                    logger.debug(f"获取到休息时间: {rest_seconds}秒 (原始值: {rest_value}, 解析后: {rest_seconds})")

                    exercise_in = {
                        "workout_id": db_workout.id,
                        "exercise_id": exercise.get("exercise_id"),
                        "sets": exercise.get("sets"),
                        "reps": exercise.get("reps"),
                        "rest_seconds": rest_seconds,
                        "order": i,
                        "notes": exercise.get("notes", "")
                    }
                else:
                    # 如果是对象，使用属性访问方式
                    # 获取休息时间，支持多种字段名
                    rest_value = getattr(exercise, "rest_seconds",
                               getattr(exercise, "rest_time_seconds",
                               getattr(exercise, "rest_time", 60)))

                    # 解析休息时间，处理可能带有单位的字符串
                    rest_seconds = parse_rest_seconds(rest_value)
                    logger.debug(f"获取到休息时间: {rest_seconds}秒 (原始值: {rest_value}, 解析后: {rest_seconds})")

                    exercise_in = {
                        "workout_id": db_workout.id,
                        "exercise_id": exercise.exercise_id,
                        "sets": exercise.sets,
                        "reps": exercise.reps,
                        "rest_seconds": rest_seconds,
                        "order": i,
                        "notes": getattr(exercise, "notes", "")
                    }

                db_exercise = crud.crud_workout_exercise.create(db, obj_in=exercise_in)
                # 安全地获取 exercise_id 用于日志记录
                exercise_id = exercise.get("exercise_id") if isinstance(exercise, dict) else getattr(exercise, "exercise_id", None)
                logger.info(f"创建训练动作: workout_id={db_workout.id}, exercise_id={exercise_id}")

                # 3. 为每个训练动作创建对应的组记录
                sets_count = exercise_in["sets"]
                reps_str = exercise_in["reps"]

                # 尝试解析 reps 字符串，获取具体的重复次数
                try:
                    # 首先确保 reps_str 是字符串类型
                    if isinstance(reps_str, int):
                        logger.info(f"reps 是整数类型 ({reps_str})，转换为字符串")
                        reps_str = str(reps_str)  # 将整数转换为字符串
                    elif not isinstance(reps_str, str):
                        logger.warning(f"reps 不是字符串或整数类型 ({type(reps_str).__name__})，转换为字符串")
                        reps_str = str(reps_str)  # 将其他类型转换为字符串

                    if "-" in reps_str:
                        # 如果是范围，如 "8-12"，取中间值
                        reps_range = reps_str.split("-")
                        reps_num = (int(reps_range[0]) + int(reps_range[1])) // 2
                    else:
                        # 如果是单个数字，直接转换
                        reps_num = int(reps_str)
                except (ValueError, IndexError, TypeError) as e:
                    # 如果解析失败，设置默认值
                    logger.warning(f"解析 reps 值 '{reps_str}' 失败: {str(e)}，使用默认值 10")
                    reps_num = 10

                # 为每组创建 SetRecord
                for set_num in range(1, sets_count + 1):
                    set_record_in = {
                        "workout_exercise_id": db_exercise.id,
                        "set_number": set_num,
                        "set_type": "normal",
                        "reps": reps_num,
                        "completed": False
                    }
                    crud.crud_set_record.create(db, obj_in=set_record_in)
                    logger.info(f"创建组记录: workout_exercise_id={db_exercise.id}, set_number={set_num}")

            # 3. 直接构建返回数据，而不是使用 get_with_workouts
            # 获取训练计划
            plan = crud.crud_training_plan.get(db, id=db_plan.id)

            # 获取训练日
            workout = crud.crud_workout.get(db, id=db_workout.id)

            # 获取训练动作
            exercises = crud.crud_workout_exercise.get_by_workout(db, workout_id=workout.id)

            # 获取所有训练动作的ID列表
            exercise_ids = [ex.exercise_id for ex in exercises]

            # 批量查询所有训练动作的详细信息
            from app.models.exercise import Exercise
            exercise_details = {}
            if exercise_ids:
                details = db.query(Exercise).filter(Exercise.id.in_(exercise_ids)).all()
                exercise_details = {d.id: d for d in details}

            # 构建训练动作列表
            workout_exercises = []
            for exercise in exercises:
                # 获取训练动作的详细信息
                detail = exercise_details.get(exercise.exercise_id)

                exercise_dict = {
                    "id": exercise.id,
                    "exercise_id": exercise.exercise_id,
                    "name": detail.name if detail else f"动作ID: {exercise.exercise_id}",
                    "image_name": detail.image_name if detail else None,
                    "sets": exercise.sets,
                    "reps": exercise.reps,
                    "rest_seconds": exercise.rest_seconds,
                    "order": exercise.order,
                    "notes": exercise.notes,
                    "exercise_type": exercise.exercise_type,
                    "superset_group": exercise.superset_group,
                    "weight": exercise.weight
                }
                workout_exercises.append(exercise_dict)

            # 构建训练日数据
            workout_dict = {
                "id": workout.id,
                "name": workout.name,
                "description": workout.description,
                "estimated_duration": workout.estimated_duration,
                "workout_exercises": workout_exercises,
                "exercises": workout_exercises  # 添加 'exercises' 键以兼容现有代码
            }

            # 构建训练计划数据
            plan_data = {
                "id": plan.id,
                "name": plan.plan_name,
                "description": plan.description,
                "workouts": [workout_dict],
                # 添加顶层的workout_exercises字段，以兼容intent_handler.py中的代码
                "workout_exercises": workout_exercises,
                # 添加顶层的estimated_duration字段，以兼容intent_handler.py中的代码
                "estimated_duration": workout.estimated_duration,
                # 添加workout_name字段，用于显示在消息中
                "workout_name": workout.name
            }

            logger.info(f"返回训练计划数据: id={plan.id}, name={plan.plan_name}, 包含 {len(workout_exercises)} 个训练动作")
            return plan_data

        except Exception as e:
            import traceback
            error_trace = traceback.format_exc()
            logger.error(f"保存单日训练计划时出错: {str(e)}")
            logger.error(f"错误堆栈: {error_trace}")
            # 回滚事务
            db.rollback()
            raise ValueError(f"保存单日训练计划失败: {str(e)}")

    @staticmethod
    def enrich_workout_response(workout_id: int, db) -> Dict[str, Any]:
        """
        丰富训练响应数据

        Args:
            workout_id: 训练ID
            db: 数据库会话

        Returns:
            丰富后的训练数据
        """
        # 获取训练信息
        workout = crud.crud_workout.get(db, id=workout_id)
        if not workout:
            logger.error(f"训练不存在: workout_id={workout_id}")
            raise ValueError(f"训练不存在: workout_id={workout_id}")

        # 获取训练动作
        exercises = crud.crud_workout_exercise.get_by_workout(db, workout_id=workout_id)
        logger.info(f"获取到 {len(exercises)} 个训练动作")

        # 构建增强的响应
        enhanced_exercises = []
        for ex in exercises:
            # 获取动作详情
            exercise = crud.crud_exercise.get(db, id=ex.exercise_id)
            if exercise:
                enhanced_ex = {
                    "id": ex.id,
                    "exercise_id": ex.exercise_id,
                    "name": exercise.name,
                    "sets": ex.sets,
                    "reps": ex.reps,
                    "rest_seconds": ex.rest_seconds,
                    "order": ex.order,
                    "notes": ex.notes,
                    "image_name": exercise.image_name,
                    "gif_url": exercise.gif_url,
                    "description": exercise.description,
                    "level": exercise.level,
                    "body_part_id": exercise.body_part_id
                }
                enhanced_exercises.append(enhanced_ex)

        # 构建响应
        response = {
            "id": workout.id,
            "name": workout.name,
            "description": workout.description,
            "estimated_duration": workout.estimated_duration,
            "exercises": enhanced_exercises
        }

        return response

    @staticmethod
    async def get_candidate_exercises_for_plan(
        sql_tool_service,
        user: models.User,
        focus_body_parts: List[int],
        available_equipment: Optional[List[int]] = None
    ) -> List[Dict[str, Any]]:
        """
        获取训练计划的候选动作

        Args:
            sql_tool_service: SQL工具服务
            user: 用户对象
            focus_body_parts: 重点训练部位ID列表
            available_equipment: 可用器材ID列表

        Returns:
            候选动作列表
        """
        from app.services.conversation.exercise_helper import get_candidate_exercises, personalized_filtering
        from app.services.conversation.profile_helper import _get_user_profile, _get_recommended_difficulty
        from app.services.conversation.parameter_extractor import DEFAULT_TRAINING_PARAMS

        # 如果没有指定重点部位，返回空列表
        if not focus_body_parts:
            logger.warning("未指定重点训练部位，无法获取候选动作")
            return []

        # 构建用户资料
        user_profile = _get_user_profile(None, user)  # 第一个参数在函数内部不使用
        difficulty_range = _get_recommended_difficulty(None, user_profile)

        # 为每个重点部位获取候选动作
        all_candidates = []
        for body_part_id in focus_body_parts:
            # 根据ID获取身体部位名称
            body_part_name = get_body_part_name(body_part_id)
            if body_part_name:
                # 获取候选动作
                logger.info(f"为部位 {body_part_name} 获取候选动作")
                candidates = await get_candidate_exercises(
                    sql_tool_service,
                    body_part=body_part_name,
                    scenario=None,  # 不限场景
                    equipment_ids=available_equipment,
                    difficulty_range=difficulty_range,
                    limit=10  # 每个部位10个候选
                )
                all_candidates.extend(candidates)

        # 去除重复项
        unique_candidates = remove_duplicate_exercises(all_candidates)

        # 个性化筛选
        training_params = {}
        if user.fitness_goal:
            goal_name = get_fitness_goal_name(user.fitness_goal)
            training_params = DEFAULT_TRAINING_PARAMS.get(goal_name, {})

        # 筛选出最合适的动作
        filtered_exercises = await personalized_filtering(
            sql_tool_service,
            unique_candidates,
            user_profile,
            training_params,
            limit=min(20, len(unique_candidates))  # 最多20个候选，确保有足够的选择
        )

        return filtered_exercises

    @staticmethod
    async def get_candidate_exercises_for_daily_workout(
        sql_tool_service,
        user: models.User,
        target_body_parts: List[int],
        available_equipment: Optional[List[int]] = None
    ) -> List[Dict[str, Any]]:
        """
        获取单日训练计划的候选动作

        Args:
            sql_tool_service: SQL工具服务
            user: 用户对象
            target_body_parts: 目标训练部位ID列表
            available_equipment: 可用器材ID列表

        Returns:
            候选动作列表
        """
        from app.services.conversation.exercise_helper import get_candidate_exercises, personalized_filtering
        from app.services.conversation.profile_helper import _get_user_profile, _get_recommended_difficulty
        from app.services.conversation.parameter_extractor import DEFAULT_TRAINING_PARAMS

        # 如果没有指定目标部位，返回空列表
        if not target_body_parts:
            logger.warning("未指定目标训练部位，无法获取候选动作")
            return []

        # 构建用户资料
        user_profile = _get_user_profile(None, user)  # 第一个参数在函数内部不使用
        difficulty_range = _get_recommended_difficulty(None, user_profile)

        # 确定训练场景
        scenario = determine_training_scenario(available_equipment)

        # 为每个目标部位获取候选动作
        all_candidates = []
        for body_part_id in target_body_parts:
            # 根据ID获取身体部位名称
            body_part_name = get_body_part_name(body_part_id)
            if body_part_name:
                # 获取候选动作
                logger.info(f"为部位 {body_part_name} 获取候选动作，场景={scenario}")
                candidates = await get_candidate_exercises(
                    sql_tool_service,
                    body_part=body_part_name,
                    scenario=scenario,
                    equipment_ids=available_equipment,
                    difficulty_range=difficulty_range,
                    limit=10  # 每个部位10个候选
                )
                all_candidates.extend(candidates)

        # 去除重复项
        unique_candidates = remove_duplicate_exercises(all_candidates)

        # 个性化筛选
        training_params = {}
        if user.fitness_goal:
            goal_name = get_fitness_goal_name(user.fitness_goal)
            training_params = DEFAULT_TRAINING_PARAMS.get(goal_name, {})

        # 筛选出最合适的动作
        filtered_exercises = await personalized_filtering(
            sql_tool_service,
            unique_candidates,
            user_profile,
            training_params,
            limit=min(15, len(unique_candidates))  # 最多15个候选，确保有足够的选择
        )

        return filtered_exercises
