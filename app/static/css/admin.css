/* 管理后台自定义样式 */

/* 表格样式优化 */
.table-container {
    overflow-x: auto;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 500;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* 卡片样式 */
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: #f8f9fa;
    font-weight: 500;
}

/* 仪表盘卡片 */
.card.bg-primary, .card.bg-success, .card.bg-info {
    transition: transform 0.3s;
}

.card.bg-primary:hover, .card.bg-success:hover, .card.bg-info:hover {
    transform: translateY(-5px);
}

/* 徽章样式 */
.badge {
    font-weight: 500;
    padding: 0.35em 0.65em;
}

/* 按钮样式 */
.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

/* 响应式优化 */
@media (max-width: 767.98px) {
    .sidebar {
        top: 5rem;
    }
}

/* 数据详情页样式 */
.profile-image {
    width: 120px;
    height: 120px;
    object-fit: cover;
    border-radius: 50%;
}

.user-placeholder {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    color: white;
    background-color: #6c757d;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
} 