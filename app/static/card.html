<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>运动动作卡片</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'Arial', sans-serif;
    }
    
    body {
      background-color: #f5f5f5;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      padding: 20px;
    }
    
    .card-container {
      width: 100%;
      max-width: 600px;
      height: 800px; /* 3:4比例 */
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      background: #fff;
      overflow: hidden;
      position: relative;
      display: flex;
      flex-direction: column;
    }
    
    .exercise-card {
      padding: 24px;
      overflow-y: auto;
      flex: 1;
    }
    
    .exercise-card h2 {
      font-size: 24px;
      color: #333;
      margin-bottom: 15px;
      border-bottom: 2px solid #f0f0f0;
      padding-bottom: 10px;
      text-align: center;
    }
    
    .exercise-card video {
      width: 100%;
      height: auto;
      border-radius: 8px;
      margin-bottom: 15px;
      background-color: #f0f0f0;
    }
    
    .muscles-section {
      display: flex;
      margin-bottom: 15px;
      background-color: #f9f9f9;
      border-radius: 8px;
      padding: 12px;
    }
    
    .target-muscles, .synergist-muscles {
      flex: 1;
    }
    
    .muscles-section h3 {
      font-size: 16px;
      color: #555;
      margin-bottom: 5px;
    }
    
    .muscles-section p {
      font-size: 14px;
      color: #333;
    }
    
    .muscle-images {
      display: flex;
      margin-bottom: 15px;
      gap: 10px;
    }
    
    .front-view, .back-view {
      flex: 1;
      border-radius: 8px;
      overflow: hidden;
    }
    
    .muscle-images h3 {
      font-size: 16px;
      color: #555;
      margin-bottom: 8px;
      text-align: center;
    }
    
    .image-container {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
      justify-content: center;
      background-color: #f9f9f9;
      padding: 8px;
      border-radius: 8px;
    }
    
    .image-container img {
      width: 48%;
      height: auto;
      object-fit: contain;
      border-radius: 4px;
    }
    
    .instructions, .tips {
      margin-bottom: 15px;
    }
    
    .instructions h3, .tips h3 {
      font-size: 18px;
      color: #333;
      margin-bottom: 8px;
      border-left: 4px solid #4a90e2;
      padding-left: 8px;
    }
    
    .instructions ol, .tips ul {
      padding-left: 25px;
    }
    
    .instructions li, .tips li {
      margin-bottom: 8px;
      font-size: 14px;
      line-height: 1.5;
      color: #444;
    }
    
    .button-container {
      padding: 16px 24px;
      background: #f9f9f9;
      border-top: 1px solid #eee;
    }
    
    button {
      width: 100%;
      padding: 12px;
      background: #4a90e2;
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 16px;
      cursor: pointer;
      transition: background 0.3s;
    }
    
    button:hover {
      background: #3a80d2;
    }
    
    .canvas-container {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.8);
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }
    
    canvas {
      max-width: 90%;
      max-height: 90%;
      border: 2px solid white;
    }
    
    .canvas-controls {
      position: absolute;
      bottom: 20px;
      display: flex;
      gap: 10px;
    }
    
    .canvas-controls button {
      width: auto;
      padding: 8px 16px;
    }
    
    .generating-overlay {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.7);
      justify-content: center;
      align-items: center;
      z-index: 1001;
      flex-direction: column;
      color: white;
    }
    
    .spinner {
      border: 5px solid rgba(255,255,255,0.3);
      border-radius: 50%;
      border-top: 5px solid white;
      width: 50px;
      height: 50px;
      animation: spin 1s linear infinite;
      margin-bottom: 15px;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="card-container">
    <div class="exercise-card">
      <h2>杠铃平板卧推</h2>
      <video controls>
        <source src="http://**************:8000/api/v1/exercise/video/Bench-Press2286a9ea51d9.mp4" type="video/mp4">
        您的浏览器不支持视频标签
      </video>
      
      <div class="muscles-section">
        <div class="target-muscles">
          <h3>主要肌肉</h3>
          <p>胸大肌</p>
        </div>
        <div class="synergist-muscles">
          <h3>次要肌肉</h3>
          <p>三角肌前束、胸大肌上束、肱三头肌</p>
        </div>
      </div>
      
      <div class="muscle-images">
        <div class="front-view">
          <h3>前视图</h3>
          <div class="image-container">
            <img src="http://**************:8000/muscles/body_front/main/front_chest.png" alt="前视图-胸大肌">
            <img src="http://**************:8000/muscles/body_front/minor/front_deltoid_anterior.png" alt="前视图-三角肌前束">
            <img src="http://**************:8000/muscles/body_front/minor/front_triceps.png" alt="前视图-肱三头肌">
          </div>
        </div>
        <div class="back-view">
          <h3>后视图</h3>
          <div class="image-container">
            <img src="http://**************:8000/muscles/body_back/main/back_chest.png" alt="后视图-胸大肌">
            <img src="http://**************:8000/muscles/body_back/minor/back_deltoid_anterior.png" alt="后视图-三角肌前束">
            <img src="http://**************:8000/muscles/body_back/minor/back_chest.png" alt="后视图-胸大肌上束">
            <img src="http://**************:8000/muscles/body_back/minor/back_triceps.png" alt="后视图-肱三头肌">
          </div>
        </div>
      </div>
      
      <div class="instructions">
        <h3>动作介绍</h3>
        <ol>
          <li>双手握住杠铃，手距略宽于肩宽，掌心朝向脚部，从杠铃架上抬起杠铃，双臂完全伸直，将杠铃置于胸部正上方。</li>
          <li>缓慢将杠铃下降至胸部，同时保持肘部呈90度角。</li>
          <li>当杠铃接触胸部时，将其推回起始位置，同时保持背部平贴在长凳上。</li>
          <li>根据目标的重复次数重复此动作，始终保持对杠铃的控制，并确保动作规范。</li>
        </ol>
      </div>
      
      <div class="tips">
        <h3>动作要点</h3>
        <ul>
          <li>避免过度弓背：一个常见的错误是抬举时过度弓起下背部，这可能导致下背部受伤。下背部应保持自然弧度，但不要过于夸张。臀部、肩部和头部应始终与长凳保持接触。</li>
          <li>控制动作：避免快速抬起杠铃的冲动。缓慢、稳定的动作更有效，并能降低受伤风险。将杠铃缓慢下降至胸部中部，稍作停顿后推回起始位置，但不要完全锁死肘关节。</li>
          <li>不要单独训练：确保有训练伙伴在旁边保护，尤其是在使用较重的重量时。</li>
        </ul>
      </div>
    </div>
    
    <div class="button-container">
      <button id="generateBtn">生成动态图</button>
    </div>
  </div>
  
  <div class="canvas-container" id="canvasContainer">
    <canvas id="previewCanvas"></canvas>
    <div class="canvas-controls">
      <button id="closePreviewBtn">关闭</button>
      <button id="downloadBtn">下载</button>
    </div>
  </div>
  
  <div class="generating-overlay" id="generatingOverlay">
    <div class="spinner"></div>
    <p>正在生成动态图，请稍候...</p>
  </div>

  <script>
    // 示例数据
    const exerciseData = {
      name: "杠铃平板卧推",
      videoUrl: "http://**************:8000/api/v1/exercise/video/Bench-Press2286a9ea51d9.mp4",
      targetMuscles: "胸大肌",
      synergistMuscles: "三角肌前束、胸大肌上束、肱三头肌",
      frontImages: [
        "http://**************:8000/muscles/body_front/main/front_chest.png",
        "http://**************:8000/muscles/body_front/minor/front_deltoid_anterior.png",
        "http://**************:8000/muscles/body_front/minor/front_triceps.png"
      ],
      backImages: [
        "http://**************:8000/muscles/body_back/main/back_chest.png",
        "http://**************:8000/muscles/body_back/minor/back_deltoid_anterior.png",
        "http://**************:8000/muscles/body_back/minor/back_chest.png",
        "http://**************:8000/muscles/body_back/minor/back_triceps.png"
      ],
      instructions: [
        "双手握住杠铃，手距略宽于肩宽，掌心朝向脚部，从杠铃架上抬起杠铃，双臂完全伸直，将杠铃置于胸部正上方。",
        "缓慢将杠铃下降至胸部，同时保持肘部呈90度角。",
        "当杠铃接触胸部时，将其推回起始位置，同时保持背部平贴在长凳上。",
        "根据目标的重复次数重复此动作，始终保持对杠铃的控制，并确保动作规范。"
      ],
      tips: [
        "避免过度弓背：一个常见的错误是抬举时过度弓起下背部，这可能导致下背部受伤。下背部应保持自然弧度，但不要过于夸张。臀部、肩部和头部应始终与长凳保持接触。",
        "控制动作：避免快速抬起杠铃的冲动。缓慢、稳定的动作更有效，并能降低受伤风险。将杠铃缓慢下降至胸部中部，稍作停顿后推回起始位置，但不要完全锁死肘关节。",
        "不要单独训练：确保有训练伙伴在旁边保护，尤其是在使用较重的重量时。"
      ]
    };

    // DOM元素
    const generateBtn = document.getElementById('generateBtn');
    const canvasContainer = document.getElementById('canvasContainer');
    const previewCanvas = document.getElementById('previewCanvas');
    const closePreviewBtn = document.getElementById('closePreviewBtn');
    const downloadBtn = document.getElementById('downloadBtn');
    const generatingOverlay = document.getElementById('generatingOverlay');
    const video = document.querySelector('video');
    
    // 生成的GIF URL
    let generatedGifUrl = null;
    
    // 生成动态图
    generateBtn.addEventListener('click', async () => {
      generatingOverlay.style.display = 'flex';
      
      try {
        // 设置canvas尺寸
        const canvas = document.createElement('canvas');
        canvas.width = 600;
        canvas.height = 800; // 3:4比例
        const ctx = canvas.getContext('2d');
        
        // 确保视频已加载并准备好
        if (video.readyState < 2) {
          await new Promise(resolve => {
            video.onloadeddata = resolve;
          });
        }
        
        // 模拟创建动态图的过程
        // 注意：这里简化了实际实现，只是为了演示
        // 在实际应用中，需要使用frame-by-frame录制并使用FFmpeg.js等库转换
        
        // 绘制卡片内容
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        // 绘制标题
        ctx.font = 'bold 24px Arial';
        ctx.fillStyle = '#333333';
        ctx.textAlign = 'center';
        ctx.fillText(exerciseData.name, canvas.width/2, 40);
        
        // 绘制视频截图（静态）
        video.pause();
        ctx.drawImage(video, 30, 60, 540, 300);
        
        // 绘制肌肉组信息
        ctx.font = 'bold 18px Arial';
        ctx.textAlign = 'left';
        ctx.fillText('主要肌肉：' + exerciseData.targetMuscles, 30, 390);
        ctx.fillText('次要肌肉：' + exerciseData.synergistMuscles, 30, 420);
        
        // 准备加载图片
        const loadImage = (src) => {
          return new Promise((resolve) => {
            const img = new Image();
            img.onload = () => resolve(img);
            img.src = src;
          });
        };
        
        // 加载并绘制示意图
        const frontImg = await loadImage(exerciseData.frontImages[0]);
        ctx.drawImage(frontImg, 30, 450, 260, 150);
        
        const backImg = await loadImage(exerciseData.backImages[0]);
        ctx.drawImage(backImg, 310, 450, 260, 150);
        
        // 绘制动作介绍
        ctx.font = '16px Arial';
        ctx.fillText('动作介绍：', 30, 630);
        const introText = exerciseData.instructions[0].substring(0, 60) + "...";
        ctx.fillText(introText, 30, 655);
        
        // 绘制动作要点
        ctx.fillText('动作要点：', 30, 690);
        const tipText = exerciseData.tips[0].substring(0, 60) + "...";
        ctx.fillText(tipText, 30, 715);
        
        // 在底部添加水印
        ctx.font = '14px Arial';
        ctx.fillStyle = '#888888';
        ctx.textAlign = 'center';
        ctx.fillText('健身助手 © 2023', canvas.width/2, 780);
        
        // 显示预览
        previewCanvas.width = canvas.width;
        previewCanvas.height = canvas.height;
        previewCanvas.getContext('2d').drawImage(canvas, 0, 0);
        
        // 模拟生成GIF（在实际应用中，这里需要使用proper GIF encoder）
        // 此处我们使用canvas.toDataURL作为示例
        generatedGifUrl = canvas.toDataURL('image/png');
        
        // 显示预览
        canvasContainer.style.display = 'flex';
      } catch (error) {
        console.error('生成动态图时出错:', error);
        alert('生成动态图时出错，请重试');
      } finally {
        generatingOverlay.style.display = 'none';
      }
    });
    
    // 关闭预览
    closePreviewBtn.addEventListener('click', () => {
      canvasContainer.style.display = 'none';
    });
    
    // 下载动态图
    downloadBtn.addEventListener('click', () => {
      if (generatedGifUrl) {
        const a = document.createElement('a');
        a.href = generatedGifUrl;
        a.download = `${exerciseData.name}.png`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      }
    });
  </script>
</body>
</html>