import os
import shutil
import uuid
from fastapi import UploadFile
from PIL import Image
from io import BytesIO
from app.core.config import settings

# 头像存储路径
AVATAR_DIR = "static/avatars"
os.makedirs(AVATAR_DIR, exist_ok=True)

# 头像尺寸
AVATAR_SIZE = (200, 200)


async def save_avatar(file: UploadFile, user_id: int) -> str:
    """保存上传的头像文件"""
    # 生成唯一文件名
    file_ext = file.filename.split('.')[-1]
    filename = f"{user_id}_{uuid.uuid4().hex}.{file_ext}"
    file_path = os.path.join(AVATAR_DIR, filename)
    
    # 保存文件
    with open(file_path, "wb") as buffer:
        shutil.copyfileobj(file.file, buffer)
    
    return filename


def process_avatar(filename: str) -> str:
    """处理头像(裁剪、压缩等)"""
    file_path = os.path.join(AVATAR_DIR, filename)
    
    try:
        # 打开图片
        with Image.open(file_path) as img:
            # 转换为RGB模式
            if img.mode != 'RGB':
                img = img.convert('RGB')
            
            # 裁剪为正方形
            width, height = img.size
            size = min(width, height)
            left = (width - size) // 2
            top = (height - size) // 2
            right = left + size
            bottom = top + size
            img = img.crop((left, top, right, bottom))
            
            # 缩放到目标尺寸
            img = img.resize(AVATAR_SIZE, Image.LANCZOS)
            
            # 保存为WebP格式
            webp_filename = f"{filename.split('.')[0]}.webp"
            webp_path = os.path.join(AVATAR_DIR, webp_filename)
            img.save(webp_path, 'WEBP', quality=85)
    except Exception as e:
        # 如果处理失败，返回原始文件名
        return f"{settings.API_URL}/static/avatars/{filename}"
    
    # 返回头像URL
    avatar_url = f"{settings.API_URL}/static/avatars/{webp_filename}"
    return avatar_url 