"""
错误处理工具类，统一管理错误处理和日志记录
"""
import logging
import traceback
import json
from typing import Dict, Any, Optional, Union, List, Callable
import time
import asyncio
from functools import wraps

logger = logging.getLogger(__name__)

class ErrorHandler:
    """错误处理工具类"""
    
    @staticmethod
    def format_error_message(error: Exception, user_friendly: bool = True) -> str:
        """格式化错误消息，根据需要隐藏敏感信息"""
        error_str = str(error)
        
        # 隐藏可能的API密钥和敏感信息
        if "api_key" in error_str.lower() or "password" in error_str.lower() or "secret" in error_str.lower():
            return "服务调用时发生内部错误，请稍后再试" if user_friendly else "API调用错误，敏感信息已隐藏"
        
        # Redis连接错误友好化
        if "redis" in error_str.lower() and ("connection" in error_str.lower() or "timeout" in error_str.lower()):
            return "缓存服务暂时不可用，不影响主要功能" if user_friendly else f"Redis连接错误: {error_str}"
        
        # LLM相关错误友好化
        if "llm" in error_str.lower() or "model" in error_str.lower() or "token" in error_str.lower():
            return "AI服务暂时遇到问题，请稍后再试" if user_friendly else f"LLM错误: {error_str}"
        
        # 数据库错误友好化
        if "database" in error_str.lower() or "sql" in error_str.lower() or "postgres" in error_str.lower():
            return "数据服务暂时不可用，请稍后再试" if user_friendly else f"数据库错误: {error_str}"
        
        # 超时错误友好化
        if "timeout" in error_str.lower() or "timed out" in error_str.lower():
            return "服务响应超时，请稍后再试或简化您的请求" if user_friendly else f"超时错误: {error_str}"
        
        # 网络错误友好化
        if "network" in error_str.lower() or "connection" in error_str.lower():
            return "网络连接问题，请检查您的网络或稍后再试" if user_friendly else f"网络错误: {error_str}"
        
        # 默认错误消息
        return "处理您的请求时遇到问题，请稍后再试" if user_friendly else error_str
    
    @staticmethod
    def log_error(error: Exception, context: Optional[str] = None, level: str = "error", include_traceback: bool = True) -> None:
        """记录错误日志，可选择是否包含堆栈跟踪"""
        log_method = getattr(logger, level.lower())
        
        # 构建错误消息
        error_message = f"{context + ': ' if context else ''}{str(error)}"
        
        # 记录错误消息
        log_method(error_message)
        
        # 如果需要，记录堆栈跟踪
        if include_traceback:
            logger.debug(f"错误堆栈: {traceback.format_exc()}")
    
    @staticmethod
    def categorize_error(error: Exception) -> Dict[str, Any]:
        """分类错误，返回错误类型和可能的解决方案"""
        error_str = str(error).lower()
        error_type = type(error).__name__
        
        # 初始化结果
        result = {
            "error_type": error_type,
            "severity": "medium",  # 默认严重程度
            "requires_retry": False,  # 是否需要重试
            "requires_fallback": False,  # 是否需要降级处理
            "should_alert": False,  # 是否需要发送告警
            "solution": "检查日志获取详细信息"  # 默认解决方案
        }
        
        # 分类错误类型
        if isinstance(error, asyncio.TimeoutError) or "timeout" in error_str:
            result.update({
                "category": "timeout",
                "severity": "medium",
                "requires_retry": True,
                "solution": "增加超时时间或优化请求"
            })
        elif "redis" in error_str:
            result.update({
                "category": "cache",
                "severity": "low",
                "requires_fallback": True,
                "solution": "检查Redis连接或使用内存缓存降级"
            })
        elif "database" in error_str or "sql" in error_str:
            result.update({
                "category": "database",
                "severity": "high",
                "should_alert": True,
                "solution": "检查数据库连接和SQL语句"
            })
        elif "llm" in error_str or "model" in error_str or "api" in error_str:
            result.update({
                "category": "llm",
                "severity": "medium",
                "requires_fallback": True,
                "solution": "尝试使用备用模型或检查API密钥"
            })
        elif "memory" in error_str or "out of memory" in error_str:
            result.update({
                "category": "resource",
                "severity": "high",
                "should_alert": True,
                "solution": "优化内存使用或增加资源配置"
            })
        elif "permission" in error_str or "access" in error_str:
            result.update({
                "category": "permission",
                "severity": "medium",
                "solution": "检查权限设置"
            })
        elif "network" in error_str or "connection" in error_str:
            result.update({
                "category": "network",
                "severity": "medium",
                "requires_retry": True,
                "solution": "检查网络连接或等待网络恢复"
            })
        elif "cancelled" in error_str or isinstance(error, asyncio.CancelledError):
            result.update({
                "category": "cancelled",
                "severity": "low",
                "solution": "任务被取消，无需处理"
            })
        else:
            result.update({
                "category": "unknown",
                "severity": "medium",
                "solution": "查看详细错误日志进行分析"
            })
        
        return result
    
    @staticmethod
    def with_error_handling(fallback_value=None, log_level="error", include_traceback=True):
        """错误处理装饰器，可用于同步和异步函数"""
        def decorator(func):
            if asyncio.iscoroutinefunction(func):
                @wraps(func)
                async def async_wrapper(*args, **kwargs):
                    try:
                        return await func(*args, **kwargs)
                    except Exception as e:
                        # 记录错误
                        ErrorHandler.log_error(
                            error=e,
                            context=f"执行{func.__name__}时出错",
                            level=log_level,
                            include_traceback=include_traceback
                        )
                        
                        # 返回回退值
                        return fallback_value
                
                return async_wrapper
            else:
                @wraps(func)
                def sync_wrapper(*args, **kwargs):
                    try:
                        return func(*args, **kwargs)
                    except Exception as e:
                        # 记录错误
                        ErrorHandler.log_error(
                            error=e,
                            context=f"执行{func.__name__}时出错",
                            level=log_level,
                            include_traceback=include_traceback
                        )
                        
                        # 返回回退值
                        return fallback_value
                
                return sync_wrapper
        
        return decorator
    
    @staticmethod
    def monitor_task_execution(task_name: str):
        """监控任务执行的装饰器，记录执行时间和错误"""
        def decorator(func):
            if asyncio.iscoroutinefunction(func):
                @wraps(func)
                async def async_wrapper(*args, **kwargs):
                    start_time = time.time()
                    try:
                        result = await func(*args, **kwargs)
                        execution_time = time.time() - start_time
                        logger.info(f"任务 '{task_name}' 成功完成，耗时: {execution_time:.2f}秒")
                        return result
                    except Exception as e:
                        execution_time = time.time() - start_time
                        logger.error(f"任务 '{task_name}' 执行失败，耗时: {execution_time:.2f}秒, 错误: {str(e)}")
                        raise
                
                return async_wrapper
            else:
                @wraps(func)
                def sync_wrapper(*args, **kwargs):
                    start_time = time.time()
                    try:
                        result = func(*args, **kwargs)
                        execution_time = time.time() - start_time
                        logger.info(f"任务 '{task_name}' 成功完成，耗时: {execution_time:.2f}秒")
                        return result
                    except Exception as e:
                        execution_time = time.time() - start_time
                        logger.error(f"任务 '{task_name}' 执行失败，耗时: {execution_time:.2f}秒, 错误: {str(e)}")
                        raise
                
                return sync_wrapper
        
        return decorator