from typing import List, Dict, Optional
from app.models.exercise import Exercise, ExerciseDetail, Muscle, BodyPart, Equipment

def format_exercise_response(
    exercise: Exercise,
    detail: Optional[ExerciseDetail] = None,
    muscles: Optional[List[Muscle]] = None,
    body_parts: Optional[List[BodyPart]] = None,
    equipment: Optional[List[Equipment]] = None
) -> Dict:
    """格式化健身动作响应数据"""
    response = {
        "id": exercise.id,
        "name": exercise.name,
        "body_parts": [],
        "equipment": [],
        "image_name": exercise.image_name,
        "gif_url": exercise.gif_url,
        "description": exercise.description,
        "level": exercise.level,
        "created_at": exercise.created_at,
        "updated_at": exercise.updated_at
    }
    
    # 添加身体部位信息
    if body_parts:
        response["body_parts"] = [
            {"id": part.id, "name": part.name}
            for part in body_parts
            if part.id in exercise.body_part_id
        ]
    
    # 添加器材信息
    if equipment:
        response["equipment"] = [
            {"id": eq.id, "name": eq.name}
            for eq in equipment
            if eq.id in exercise.equipment_id
        ]
    
    # 添加详情信息
    if detail:
        response["detail"] = {
            "target_muscles": [],
            "synergist_muscles": [],
            "instructions": detail.ex_instructions,
            "tips": detail.exercise_tips,
            "video_file": detail.video_file
        }
        
        # 添加目标肌肉信息
        if muscles:
            response["detail"]["target_muscles"] = [
                {"id": m.id, "name": m.name, "en_name": m.en_name}
                for m in muscles
                if m.id in detail.target_muscles_id
            ]
            
            # 添加协同肌肉信息
            if detail.synergist_muscles_id:
                response["detail"]["synergist_muscles"] = [
                    {"id": m.id, "name": m.name, "en_name": m.en_name}
                    for m in muscles
                    if m.id in detail.synergist_muscles_id
                ]
    
    return response

def validate_exercise_data(
    exercise_data: Dict,
    body_parts: List[BodyPart],
    equipment: List[Equipment],
    muscles: List[Muscle]
) -> List[str]:
    """验证健身动作数据的有效性"""
    errors = []
    
    # 验证身体部位ID
    for body_part_id in exercise_data.get("body_part_id", []):
        if not any(part.id == body_part_id for part in body_parts):
            errors.append(f"无效的身体部位ID: {body_part_id}")
    
    # 验证器材ID
    for equipment_id in exercise_data.get("equipment_id", []):
        if not any(eq.id == equipment_id for eq in equipment):
            errors.append(f"无效的器材ID: {equipment_id}")
    
    # 验证详情数据
    if "detail" in exercise_data:
        detail = exercise_data["detail"]
        
        # 验证目标肌肉ID
        for muscle_id in detail.get("target_muscles_id", []):
            if not any(m.id == muscle_id for m in muscles):
                errors.append(f"无效的目标肌肉ID: {muscle_id}")
        
        # 验证协同肌肉ID
        for muscle_id in detail.get("synergist_muscles_id", []):
            if not any(m.id == muscle_id for m in muscles):
                errors.append(f"无效的协同肌肉ID: {muscle_id}")
    
    return errors

def generate_exercise_search_query(
    query: str,
    body_part_id: Optional[int] = None,
    equipment_id: Optional[int] = None,
    level: Optional[int] = None
) -> str:
    """生成健身动作搜索查询"""
    conditions = []
    
    if query:
        conditions.append(f"name ILIKE '%{query}%'")
    
    if body_part_id:
        conditions.append(f"{body_part_id} = ANY(body_part_id)")
    
    if equipment_id:
        conditions.append(f"{equipment_id} = ANY(equipment_id)")
    
    if level is not None:
        conditions.append(f"level = {level}")
    
    if conditions:
        return " AND ".join(conditions)
    return "TRUE" 