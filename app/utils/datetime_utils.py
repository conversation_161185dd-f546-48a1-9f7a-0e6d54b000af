"""
日期时间工具函数 - 提供统一的日期时间处理方法
"""
from datetime import datetime, timezone
import logging

logger = logging.getLogger(__name__)

def get_utc_now():
    """获取带UTC时区的当前时间
    
    Returns:
        带UTC时区的当前时间
    """
    return datetime.now(timezone.utc)

def ensure_timezone(dt):
    """确保datetime对象带有时区信息
    
    如果输入的datetime对象没有时区信息，则添加UTC时区
    
    Args:
        dt: 日期时间对象
        
    Returns:
        带时区的日期时间对象
    """
    if dt is None:
        return None
        
    try:
        if dt.tzinfo is None:
            return dt.replace(tzinfo=timezone.utc)
        return dt
    except Exception as e:
        logger.error(f"确保时区信息时出错: {str(e)}")
        return dt

def calculate_time_diff_seconds(dt1, dt2):
    """计算两个日期时间对象之间的时间差（秒）
    
    确保两个日期时间对象都有时区信息，然后计算时间差
    
    Args:
        dt1: 第一个日期时间对象
        dt2: 第二个日期时间对象
        
    Returns:
        时间差（秒）
    """
    if dt1 is None or dt2 is None:
        return None
        
    try:
        # 确保两个日期时间对象都有时区信息
        dt1 = ensure_timezone(dt1)
        dt2 = ensure_timezone(dt2)
        
        # 计算时间差
        time_diff = (dt1 - dt2).total_seconds()
        return time_diff
    except Exception as e:
        logger.error(f"计算时间差时出错: {str(e)}")
        return None

def format_datetime(dt, format_str="%Y-%m-%d %H:%M:%S"):
    """格式化日期时间对象为字符串
    
    Args:
        dt: 日期时间对象
        format_str: 格式化字符串
        
    Returns:
        格式化后的字符串
    """
    if dt is None:
        return ""
        
    try:
        return dt.strftime(format_str)
    except Exception as e:
        logger.error(f"格式化日期时间时出错: {str(e)}")
        return ""
