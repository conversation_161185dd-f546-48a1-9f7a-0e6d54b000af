"""
训练计划相关工具函数
"""
import json
import logging
import re
from typing import Dict, List, Any, Optional, Union

from app.services.sql_tool_service import BODY_PART_CATEGORIES, EQUIPMENT_CATEGORIES

logger = logging.getLogger(__name__)

def extract_json_from_response(response: str) -> str:
    """
    从LLM响应中提取JSON部分
    
    Args:
        response: LLM返回的原始响应
        
    Returns:
        提取的JSON字符串
    """
    # 尝试查找JSON块
    json_pattern = r'```(?:json)?\s*([\s\S]*?)\s*```'
    matches = re.findall(json_pattern, response)
    
    if matches:
        # 使用找到的第一个JSON块
        logger.info("从响应中提取到JSON块")
        return matches[0].strip()
    
    # 如果没有找到JSON块，尝试直接解析整个响应
    try:
        # 检查是否整个响应就是一个JSON
        json.loads(response)
        logger.info("整个响应是有效的JSON")
        return response
    except json.JSONDecodeError:
        # 如果不是有效的JSON，尝试查找可能的JSON部分
        # 查找第一个 { 和最后一个 }
        start = response.find('{')
        end = response.rfind('}')
        
        if start != -1 and end != -1 and start < end:
            potential_json = response[start:end+1]
            logger.info(f"提取潜在JSON部分: {start}到{end}")
            return potential_json
    
    # 如果所有尝试都失败，返回原始响应
    logger.warning("无法从响应中提取JSON，返回原始响应")
    return response

def get_body_part_name(body_part_id: int) -> Optional[str]:
    """
    根据ID获取身体部位名称
    
    Args:
        body_part_id: 身体部位ID
        
    Returns:
        身体部位名称，如果未找到则返回None
    """
    for bp in BODY_PART_CATEGORIES:
        if bp["id"] == body_part_id:
            return bp["name"]
    return None

def get_fitness_goal_name(goal_id: int) -> Optional[str]:
    """
    根据ID获取健身目标名称
    
    Args:
        goal_id: 健身目标ID
        
    Returns:
        健身目标名称，如果未找到则返回None
    """
    goal_map = {
        1: "增肌",
        2: "减脂",
        3: "塑形",
        4: "增强体能",
        5: "提高力量"
    }
    return goal_map.get(goal_id)

def get_equipment_name(equipment_id: int) -> Optional[str]:
    """
    根据ID获取器材名称
    
    Args:
        equipment_id: 器材ID
        
    Returns:
        器材名称，如果未找到则返回None
    """
    for eq in EQUIPMENT_CATEGORIES:
        if eq["id"] == equipment_id:
            return eq["name"]
    return None

def format_equipment_list(equipment_ids: List[int]) -> str:
    """
    将器材ID列表格式化为名称字符串
    
    Args:
        equipment_ids: 器材ID列表
        
    Returns:
        格式化后的器材名称字符串
    """
    if not equipment_ids:
        return "不限"
    
    equipment_names = []
    for eq_id in equipment_ids:
        name = get_equipment_name(eq_id)
        if name:
            equipment_names.append(name)
    
    return ", ".join(equipment_names) if equipment_names else "不限"

def format_body_parts_list(body_part_ids: List[int]) -> str:
    """
    将身体部位ID列表格式化为名称字符串
    
    Args:
        body_part_ids: 身体部位ID列表
        
    Returns:
        格式化后的身体部位名称字符串
    """
    if not body_part_ids:
        return "全身"
    
    body_part_names = []
    for bp_id in body_part_ids:
        name = get_body_part_name(bp_id)
        if name:
            body_part_names.append(name)
    
    return ", ".join(body_part_names) if body_part_names else "全身"

def normalize_body_part_id(body_part_id: Any) -> Union[List[int], int, None]:
    """
    规范化身体部位ID格式
    
    Args:
        body_part_id: 原始身体部位ID（可能是字符串、列表或整数）
        
    Returns:
        规范化后的身体部位ID
    """
    if body_part_id is None:
        return None
        
    try:
        if isinstance(body_part_id, list):
            return [int(id) for id in body_part_id]
        elif isinstance(body_part_id, (int, float)):
            return int(body_part_id)
        elif isinstance(body_part_id, str):
            if body_part_id.startswith('[') and body_part_id.endswith(']'):
                # 可能是字符串形式的列表
                import ast
                return [int(id) for id in ast.literal_eval(body_part_id)]
            else:
                # 尝试转为整数
                return int(body_part_id)
    except Exception as e:
        logger.error(f"规范化身体部位ID '{body_part_id}' 失败: {str(e)}")
        
    return None

def safe_get(obj: Any, attr: str, default: Any = None) -> Any:
    """
    安全地获取属性，支持对象属性和字典访问
    
    Args:
        obj: 要获取属性的对象或字典
        attr: 属性名
        default: 默认值
        
    Returns:
        属性值或默认值
    """
    if isinstance(obj, dict):
        return obj.get(attr, default)
    return getattr(obj, attr, default)
