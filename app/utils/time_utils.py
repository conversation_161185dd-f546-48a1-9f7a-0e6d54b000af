"""
时间处理工具函数

提供时间处理相关的工具函数，如获取当前UTC时间、计算时间差等。
"""

import logging
from datetime import datetime, timezone
from typing import Optional, Any

logger = logging.getLogger(__name__)

def get_utc_now() -> datetime:
    """获取当前UTC时间
    
    Returns:
        带时区信息的当前UTC时间
    """
    return datetime.now(timezone.utc)

def ensure_timezone(dt: Any) -> Optional[datetime]:
    """确保时间有时区信息
    
    Args:
        dt: 时间对象，可能是datetime、字符串或其他类型
        
    Returns:
        带时区信息的datetime对象，如果输入无效则返回None
    """
    if dt is None:
        return None
        
    try:
        # 如果是字符串，尝试解析
        if isinstance(dt, str):
            try:
                dt = datetime.fromisoformat(dt)
            except ValueError:
                try:
                    # 尝试其他常见格式
                    dt = datetime.strptime(dt, "%Y-%m-%d %H:%M:%S")
                except ValueError:
                    logger.error(f"无法解析时间字符串: {dt}")
                    return None
        
        # 确保是datetime对象
        if not isinstance(dt, datetime):
            logger.error(f"无效的时间对象类型: {type(dt)}")
            return None
            
        # 添加时区信息（如果没有）
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=timezone.utc)
            
        return dt
    except Exception as e:
        logger.error(f"确保时区信息时出错: {str(e)}")
        return None

def calculate_time_diff_seconds(dt1: Any, dt2: Any) -> Optional[float]:
    """计算两个时间的差值（秒）
    
    Args:
        dt1: 第一个时间
        dt2: 第二个时间
        
    Returns:
        时间差（秒），如果输入无效则返回None
    """
    # 确保时间有时区信息
    dt1 = ensure_timezone(dt1)
    dt2 = ensure_timezone(dt2)
    
    if dt1 is None or dt2 is None:
        return None
        
    try:
        # 计算时间差
        diff = dt1 - dt2
        return abs(diff.total_seconds())
    except Exception as e:
        logger.error(f"计算时间差时出错: {str(e)}")
        return None

def format_datetime(dt: Any, format_str: str = "%Y-%m-%d %H:%M:%S") -> Optional[str]:
    """格式化时间
    
    Args:
        dt: 时间对象
        format_str: 格式化字符串
        
    Returns:
        格式化后的时间字符串，如果输入无效则返回None
    """
    # 确保时间有时区信息
    dt = ensure_timezone(dt)
    
    if dt is None:
        return None
        
    try:
        return dt.strftime(format_str)
    except Exception as e:
        logger.error(f"格式化时间时出错: {str(e)}")
        return None
