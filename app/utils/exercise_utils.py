"""
健身动作相关工具函数
"""
import logging
from typing import Dict, List, Any, Optional, Tuple

logger = logging.getLogger(__name__)

def determine_training_scenario(available_equipment: Optional[List[int]]) -> Optional[str]:
    """
    根据可用器材确定训练场景
    
    Args:
        available_equipment: 可用器材ID列表
        
    Returns:
        训练场景: "home"(家庭), "gym"(健身房), None(不限)
    """
    if not available_equipment:
        return None
        
    # 如果只有自重器材(ID=2)，或者是自重+哑铃(ID=1,2)，视为居家场景
    if set(available_equipment).issubset({4, 2}):
        logger.info(f"基于可用器材 {available_equipment} 判断为'居家'场景")
        return "home"
    # 如果包含更多器材，视为健身房场景
    elif len(available_equipment) > 2 or any(eq_id > 2 for eq_id in available_equipment):
        logger.info(f"基于可用器材 {available_equipment} 判断为'健身房'场景")
        return "gym"
        
    return None

def format_candidate_exercises(candidates: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    标准化候选动作字段
    
    Args:
        candidates: 原始候选动作列表
        
    Returns:
        标准化后的候选动作列表
    """
    standardized_candidates = []
    for ex in candidates:
        # 确保所有返回的动作都有标准化的字段名称
        standardized_ex = {
            "id": ex.get("exercise_id") if "exercise_id" in ex else ex.get("id"),
            "name": ex.get("exercise_name") if "exercise_name" in ex else ex.get("name"),
            "description": ex.get("description", ""),
            "body_part_id": ex.get("body_part_id"),
            "body_parts": [ex.get("body_part_name")] if ex.get("body_part_name") else [],
            "equipment": [ex.get("equipment_name")] if ex.get("equipment_name") else [],
            "equipment_id": ex.get("equipment_id"),
            "muscles": [ex.get("muscle_name")] if ex.get("muscle_name") else [],
            "level": ex.get("difficulty", 2)  # 使用difficulty作为level
        }
        standardized_candidates.append(standardized_ex)
        
    return standardized_candidates

def remove_duplicate_exercises(candidates: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    移除重复的候选动作
    
    Args:
        candidates: 候选动作列表
        
    Returns:
        去重后的候选动作列表
    """
    seen_ids = set()
    unique_candidates = []
    
    for ex in candidates:
        ex_id = ex.get("id")
        if ex_id and ex_id not in seen_ids:
            seen_ids.add(ex_id)
            unique_candidates.append(ex)
            
    return unique_candidates
