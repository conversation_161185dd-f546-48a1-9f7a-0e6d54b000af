import hashlib
from app.core.config import settings
import logging

logger = logging.getLogger("fitness-coach-api")

def generate_user_secure_path(user_id: int) -> str:
    """
    为用户生成一个安全、一致的存储路径
    每个用户ID总是生成相同的路径，但无法反向推导出用户ID
    
    Args:
        user_id: 用户ID
        
    Returns:
        str: 安全的用户目录路径名
    """
    try:
        if user_id is None:
            logger.warning("生成安全路径时用户ID为空")
            return "anonymous_user"
            
        # 使用用户ID和应用密钥创建哈希值
        hash_obj = hashlib.sha256(f"{user_id}_{settings.SECRET_KEY}".encode())
        secure_hash = hash_obj.hexdigest()[:24]  # 使用前24个字符作为目录名
        
        # 加上前缀以增强可读性
        result = f"user_{secure_hash}"
        logger.debug(f"为用户 {user_id} 生成安全路径: {result}")
        return result
    except Exception as e:
        logger.error(f"生成用户安全路径失败: {str(e)}")
        # 出错时返回一个通用路径
        return f"user_fallback_{user_id}" 