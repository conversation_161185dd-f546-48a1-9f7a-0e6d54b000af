import os
import uuid
import requests
import hashlib
from app.core.config import settings
import logging
import shutil
from datetime import datetime
from app.utils.secure_path import generate_user_secure_path

# 定义基础目录
BASE_DIR = "/data/users"

logger = logging.getLogger("fitness-coach-api")


def get_access_token():
    """获取微信小程序 access token"""
    url = "https://api.weixin.qq.com/cgi-bin/token"
    params = {
        "grant_type": "client_credential",
        "appid": settings.WECHAT_MINI_APPID,
        "secret": settings.WECHAT_MINI_SECRET
    }
    
    try:
        response = requests.get(url, params=params)
        data = response.json()
        
        if "access_token" not in data:
            error_msg = f"获取 access token 失败: {data.get('errmsg', '未知错误')}"
            logger.error(error_msg)
            raise Exception(error_msg)
        
        return data["access_token"]
    except Exception as e:
        logger.error(f"获取 access token 异常: {str(e)}")
        raise


def generate_qrcode(page: str, scene: str, width: int, user_id: int) -> dict:
    """生成小程序码并保存到文件系统"""
    try:
        # 使用统一的安全路径生成函数
        secure_path = generate_user_secure_path(user_id)
        user_dir = f"{BASE_DIR}/{secure_path}"
        qrcode_dir = f"{user_dir}/qrcode"
        
        # 创建必要的目录结构
        for dir_path in [BASE_DIR, user_dir, qrcode_dir]:
            try:
                os.makedirs(dir_path, exist_ok=True)
                logger.info(f"确保目录存在: {dir_path}")
            except Exception as e:
                logger.error(f"创建目录失败: {dir_path}, 错误: {str(e)}")
                raise Exception(f"服务器存储错误: 无法创建目录 {dir_path}")
            
        # 检查目录写入权限
        if not os.access(qrcode_dir, os.W_OK):
            logger.error(f"目录没有写入权限: {qrcode_dir}")
            raise Exception(f"服务器存储错误: 目录 {qrcode_dir} 无写入权限")

        # 生成带时间戳的文件名 - 文件名仍保持唯一性
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        random_suffix = uuid.uuid4().hex[:8]  # 添加随机后缀确保文件名唯一
        filename = f"qrcode_{timestamp}_{random_suffix}.png"
        file_path = os.path.join(qrcode_dir, filename)
        
        # 检查微信配置
        if not settings.WECHAT_MINI_APPID or not settings.WECHAT_MINI_SECRET:
            logger.error("微信小程序配置缺失: APPID或SECRET未设置")
            raise Exception("微信小程序配置错误")
            
        # 处理scene参数 - 确保符合微信API要求
        # 最大32个可见字符，只支持数字，大小写英文以及部分特殊字符：!#$&'()*+,/:;=?@-.\_~
        if len(scene) > 32:
            logger.warning(f"scene参数过长 ({len(scene)} > 32)，将被截断")
            scene = scene[:32]
        
        # 获取 access token
        access_token = get_access_token()
        
        # 调用微信接口生成小程序码
        url = f"https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token={access_token}"
        data = {
            "scene": scene,
            "page": page,
            "width": width,
            "check_path": False,  # 不校验页面是否存在
            "env_version": "release"  # 默认使用正式版
        }
        
        logger.info(f"调用微信小程序码API: page={page}, scene={scene}, width={width}")
        response = requests.post(url, json=data)
        
        # 检查是否成功
        if response.headers.get("Content-Type", "").startswith("application/json"):
            # 返回的是JSON，说明有错误
            error = response.json()
            error_code = error.get("errcode", "unknown")
            error_msg = f"生成小程序码失败: 错误码={error_code}, 信息={error.get('errmsg', '未知错误')}"
            logger.error(error_msg)
            raise Exception(error_msg)
        
        # 保存小程序码
        with open(file_path, "wb") as f:
            f.write(response.content)
        
        logger.info(f"小程序码已保存到: {file_path}")
        
        # 设置文件权限
        try:
            os.chmod(file_path, 0o644)  # rw-r--r--
            logger.info(f"设置文件权限: {file_path}")
        except Exception as e:
            logger.error(f"设置文件权限失败: {str(e)}")
            
        # 返回URL和文件路径
        qrcode_url = f"/api/v1/qrcode/image/{secure_path}/{filename}"
        
        logger.info(f"小程序码URL: {qrcode_url}")
        return {
            "url": qrcode_url,
            "path": file_path
        }
    except Exception as e:
        logger.error(f"生成小程序码异常: {str(e)}")
        raise 