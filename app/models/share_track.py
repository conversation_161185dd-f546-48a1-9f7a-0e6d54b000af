from sqlalchemy import Column, Integer, String, DateTime, Boolean, ForeignKey, func
from app.db.base_class import Base


class ShareTrack(Base):
    __tablename__ = "share_tracks"

    id = Column(Integer, primary_key=True, index=True)
    shared_by = Column(Integer, ForeignKey("users.id", ondelete="SET NULL"), nullable=True)
    scanned_by = Column(Integer, ForeignKey("users.id", ondelete="SET NULL"), nullable=True)
    share_type = Column(String, nullable=False)  # 'menu', 'button', 'qrcode'
    page = Column(String, nullable=False)
    scene = Column(String, nullable=True)  # 新增: 场景参数，用于精确匹配QR码
    qrcode_url = Column(String, nullable=True)  # 小程序码访问URL
    is_active = Column(Boolean, default=True)   # 新增: 二维码是否有效
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())  # 新增: 更新时间 