from sqlalchemy import Column, Integer, String, Float, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, DateTime
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.db.base_class import Base

class SetRecord(Base):
    """训练组记录模型，记录用户在训练中完成的每一组动作的详细信息"""
    __tablename__ = "set_records"

    id = Column(Integer, primary_key=True, index=True)
    workout_exercise_id = Column(Integer, ForeignKey("workout_exercises.id"), nullable=False)
    set_number = Column(Integer, nullable=False)  # 组号
    set_type = Column(String(20), nullable=False, default="normal")  # 组类型：warmup, normal, drop, superset, rest
    weight = Column(Float, nullable=True)  # 重量（kg）
    reps = Column(Integer, nullable=True)  # 重复次数
    completed = Column(Boolean, default=False)  # 是否完成
    notes = Column(String(255), nullable=True)  # 备注
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    workout_exercise = relationship("WorkoutExercise", back_populates="set_records")

    def __repr__(self):
        return f"<SetRecord {self.id}: Set {self.set_number} of WorkoutExercise {self.workout_exercise_id}>"
