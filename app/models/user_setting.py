from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, func, String
from sqlalchemy.orm import relationship
from app.db.base_class import Base
import enum


class CharacterType(enum.Enum):
    """AI教练角色类型枚举"""
    MOTIVATIONAL = "motivational"  # 激励型
    STRICT = "strict"  # 严格型


class UserSetting(Base):
    __tablename__ = "user_settings"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, unique=True)
    notification_enabled = Column(Boolean, default=True)

    # AI教练角色偏好，默认为激励型
    ai_character_type = Column(String, default=CharacterType.MOTIVATIONAL.value)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    user = relationship("User", back_populates="settings")