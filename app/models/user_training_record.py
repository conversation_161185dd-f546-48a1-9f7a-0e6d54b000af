from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, JSON, ARRAY, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base_class import Base


class UserTrainingRecord(Base):
    """用户训练记录模型"""
    __tablename__ = "user_training_records"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # 基础信息
    training_age = Column(Integer, default=0)  # 训练年龄（月）
    preferred_training_days = Column(Integer, default=3)  # 每周首选训练天数
    
    # 体能和力量指标
    squat_max = Column(Float, nullable=True)  # 深蹲最大重量
    bench_press_max = Column(Float, nullable=True)  # 卧推最大重量
    deadlift_max = Column(Float, nullable=True)  # 硬拉最大重量
    
    # 训练偏好
    preferred_exercise_types = Column(JSON, nullable=True)  # 首选运动类型
    avoided_exercise_types = Column(JSON, nullable=True)  # 避免的运动类型
    
    # 目标数据
    fitness_goals = Column(JSON, nullable=True)  # 健身目标
    target_muscle_groups = Column(JSON, nullable=True)  # 目标肌肉群
    
    # 时间记录
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 其他健康指标
    injuries = Column(JSON, nullable=True)  # 伤病记录
    medical_conditions = Column(JSON, nullable=True)  # 医疗状况
    
    # 其他训练记录相关字段
    date = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    body_parts = Column(ARRAY(String), nullable=True)  # 训练的身体部位
    duration_minutes = Column(Integer, nullable=True)  # 训练时长(分钟)
    exercises_data = Column(JSON, nullable=True)  # 训练的动作详情JSON格式
    notes = Column(Text, nullable=True)  # 训练笔记
    
    # 关系
    user = relationship("User", back_populates="training_records") 