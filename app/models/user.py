from sqlalchemy import Column, Integer, String, DateTime, Boolean, Enum, Float, SmallInteger, ARRAY, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.db.base_class import Base
import enum

class Gender(enum.IntEnum):
    """性别枚举"""
    UNKNOWN = 0  # 未知
    MALE = 1  # 男
    FEMALE = 2  # 女

class ExperienceLevel(enum.IntEnum):
    """健身经验级别"""
    BEGINNER = 1  # 初学者
    INTERMEDIATE = 2  # 中级
    ADVANCED = 3  # 高级

class FitnessGoal(enum.IntEnum):
    """健身目标"""
    LOSE_WEIGHT = 1  # 减肥
    MAINTAIN = 2  # 保持
    GAIN_MUSCLE = 3  # 增肌

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=True)
    phone = Column(String, unique=True, index=True, nullable=True)
    hashed_password = Column(String, nullable=True)

    # 微信相关字段
    openid = Column(String, unique=True, index=True, nullable=False)
    unionid = Column(String, unique=True, index=True, nullable=True)
    session_key = Column(String, nullable=True)
    nickname = Column(String)
    avatar_url = Column(String)

    # 用户地理信息
    country = Column(String)
    province = Column(String)
    city = Column(String)

    # 个人信息
    gender = Column(Integer)  # 0: 未知, 1: 男性, 2: 女性
    birthday = Column(DateTime, nullable=True)
    age = Column(Integer, nullable=True)
    height = Column(Float, nullable=True)  # cm
    weight = Column(Float, nullable=True)  # kg
    activity_level = Column(Integer, default=3)
    body_type = Column(String, nullable=True)
    experience_level = Column(Integer, nullable=True)  # 1-初学者, 2-中级, 3-高级
    fitness_goal = Column(Integer, nullable=True)  # 1-减肥, 2-保持, 3-增肌
    language = Column(String, nullable=True)

    # 健康信息
    health_conditions = Column(ARRAY(String), nullable=True)  # 健康状况记录
    allergies = Column(ARRAY(String), nullable=True)  # 过敏源记录

    # 计算的健康指标
    bmi = Column(Float, nullable=True)
    tedd = Column(Integer, nullable=True)  # 每日总能量消耗(kcal)

    # 状态标记
    completed = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    is_superuser = Column(Boolean, default=False)

    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    last_login = Column(DateTime(timezone=True))

    # 关系
    settings = relationship("UserSetting", back_populates="user", uselist=False, cascade="all, delete-orphan")
    meal_records = relationship("MealRecord", back_populates="user")
    favorite_exercises = relationship("UserFavoriteExercise", back_populates="user", cascade="all, delete-orphan")
    favorite_foods = relationship("UserFavoriteFood", back_populates="user", cascade="all, delete-orphan")
    training_plans = relationship("TrainingPlan", back_populates="user", cascade="all, delete-orphan")
    training_plan_records = relationship("UserTrainingPlanRecord", back_populates="user", cascade="all, delete-orphan")
    workout_templates = relationship("WorkoutTemplate", back_populates="user", cascade="all, delete-orphan")

    training_records = relationship("UserTrainingRecord", back_populates="user", cascade="all, delete-orphan")
    daily_workouts = relationship("DailyWorkout", back_populates="user", cascade="all, delete-orphan")

    # LangChain相关关系
    conversations = relationship("Conversation", back_populates="user", cascade="all, delete-orphan")
    qa_pairs = relationship("QAPair", back_populates="user")
    messages = relationship("Message", back_populates="user", cascade="all, delete-orphan")
    feedbacks = relationship("Feedback", back_populates="user", cascade="all, delete-orphan")
    user_stats = relationship("UserStats", back_populates="user", uselist=False, cascade="all, delete-orphan")
    food_recognitions = relationship("FoodRecognition", back_populates="user")

    # 社区相关关系
    posts = relationship("Post", back_populates="user", cascade="all, delete-orphan")
    comments = relationship("Comment", back_populates="user", cascade="all, delete-orphan")
    post_likes = relationship("PostLike", back_populates="user", cascade="all, delete-orphan")
    comment_likes = relationship("CommentLike", back_populates="user", cascade="all, delete-orphan")
    notifications = relationship("Notification", foreign_keys="Notification.user_id", back_populates="user", cascade="all, delete-orphan")
    images = relationship("Image", back_populates="user", cascade="all, delete-orphan")
    # 用户关系
    followers = relationship("UserRelation", foreign_keys="UserRelation.following_id", back_populates="following")
    following = relationship("UserRelation", foreign_keys="UserRelation.follower_id", back_populates="follower")

    # 团队相关关系
    owned_teams = relationship("app.models.team.team.Team", back_populates="owner", cascade="all, delete-orphan")
    team_memberships = relationship("app.models.team.membership.TeamMembership", back_populates="user", cascade="all, delete-orphan")
    created_templates = relationship("app.models.team.template.TrainingPlanTemplate", foreign_keys="app.models.team.template.TrainingPlanTemplate.creator_id", back_populates="creator")
    coached_plans = relationship("app.models.team.training.ClientTrainingPlan", foreign_keys="app.models.team.training.ClientTrainingPlan.coach_id", back_populates="coach")

    # 教练-客户关系
    coached_clients = relationship("app.models.team.relation.ClientRelation", foreign_keys="app.models.team.relation.ClientRelation.coach_id", back_populates="coach")
    coaches = relationship("app.models.team.relation.ClientRelation", foreign_keys="app.models.team.relation.ClientRelation.client_id", back_populates="client")

    # 游戏化系统关系
    level = relationship("UserLevel", back_populates="user", uselist=False, cascade="all, delete-orphan")
    attributes = relationship("UserAttribute", back_populates="user", uselist=False, cascade="all, delete-orphan")
    titles = relationship("UserTitle", back_populates="user", cascade="all, delete-orphan")
    cards = relationship("UserCard", back_populates="user", cascade="all, delete-orphan")
    currency = relationship("Currency", back_populates="user", uselist=False, cascade="all, delete-orphan")
    achievements = relationship("UserAchievement", back_populates="user", cascade="all, delete-orphan")
    milestones = relationship("UserMilestone", back_populates="user", cascade="all, delete-orphan")
    tasks = relationship("UserTask", back_populates="user", cascade="all, delete-orphan")
    purchases = relationship("UserPurchase", back_populates="user", cascade="all, delete-orphan")
    checkins = relationship("DailyCheckIn", back_populates="user", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<User {self.id}: {self.nickname}>"