from sqlalchemy import Column, Integer, String, Boolean, Float, ForeignKey, DateTime, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base_class import Base


class Food(Base):
    """食品基本信息表"""
    __tablename__ = "foods"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(128), nullable=False, index=True)
    code = Column(String(128), unique=True, nullable=False, index=True)
    category = Column(String(64), index=True)
    food_type = Column(String(32), index=True)
    goods_id = Column(Integer, nullable=True)
    thumb_image_url = Column(String)
    large_image_url = Column(String)
    is_liquid = Column(Boolean, default=False)
    hot = Column(Integer, default=0)  # 热度字段，默认为0
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    can_revise = Column(Boolean, default=False)

    # 关联关系
    nutritional_profile = relationship("NutritionalProfile", back_populates="food", uselist=False, cascade="all, delete-orphan")
    nutrients = relationship("FoodNutrientValue", back_populates="food", cascade="all, delete-orphan")
    units = relationship("FoodUnit", back_populates="food", cascade="all, delete-orphan")


class NutritionalProfile(Base):
    """食品营养概况表"""
    __tablename__ = "nutritional_profiles"

    id = Column(Integer, primary_key=True, index=True)
    food_id = Column(Integer, ForeignKey("foods.id", ondelete="CASCADE"), unique=True)
    health_light = Column(Integer, nullable=True)
    lights = Column(JSON, nullable=True)
    warnings = Column(JSON, nullable=True)
    warning_scenes = Column(JSON, nullable=True)
    calory = Column(Float, nullable=True)
    protein_fraction = Column(Float, nullable=True)
    fat_fraction = Column(Float, nullable=True)
    carb_fraction = Column(Float, nullable=True)
    food_rank = Column(Integer, nullable=True)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关联关系
    food = relationship("Food", back_populates="nutritional_profile")


class FoodNutrientValue(Base):
    """食品营养素明细表"""
    __tablename__ = "food_nutrient_values"

    id = Column(Integer, primary_key=True, index=True)
    food_id = Column(Integer, ForeignKey("foods.id", ondelete="CASCADE"), index=True)
    name_en = Column(String(64), nullable=False)
    name_cn = Column(String(64), nullable=False)
    value = Column(Float, nullable=True)
    unit = Column(String(16), nullable=True)
    unit_name = Column(String(16), nullable=True)
    precision = Column(Integer, nullable=True)
    nrv = Column(Float, nullable=True)
    category = Column(String(16), nullable=False, index=True)

    # 关联关系
    food = relationship("Food", back_populates="nutrients")


class FoodUnit(Base):
    """食品计量单位表"""
    __tablename__ = "food_units"

    id = Column(Integer, primary_key=True, index=True)
    food_id = Column(Integer, ForeignKey("foods.id", ondelete="CASCADE"), index=True)
    unit_name = Column(String(32), nullable=False)
    weight = Column(Float, nullable=False)
    eat_weight = Column(Float, nullable=False)
    is_default = Column(Boolean, default=False)

    # 关联关系
    food = relationship("Food", back_populates="units") 