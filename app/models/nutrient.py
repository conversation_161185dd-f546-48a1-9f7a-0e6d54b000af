from sqlalchemy import Column, Integer, String, Numeric, Text
from app.db.base_class import Base

class VitaminRNI(Base):
    __tablename__ = "vitamin_rni"
    
    id = Column(Integer, primary_key=True, index=True)
    age_start_years = Column(Numeric(5, 2), nullable=False)
    sex = Column(String(6), nullable=False)
    rec_type = Column(String(3), nullable=False)
    
    vitamin_a = Column(Numeric)
    vitamin_a_unit = Column(String(10), nullable=False)
    
    vitamin_d = Column(Numeric)
    vitamin_d_unit = Column(String(10), nullable=False)
    
    vitamin_e = Column(Numeric)
    vitamin_e_unit = Column(String(10), nullable=False)
    
    thiamine = Column(Numeric)  # 对应 vitamin_b1
    thiamine_unit = Column(String(10), nullable=False)
    
    lactoflavin = Column(Numeric)  # 对应 vitamin_b2
    lactoflavin_unit = Column(String(10), nullable=False)
    
    vitamin_b6 = Column(Numeric)
    vitamin_b6_unit = Column(String(10), nullable=False)
    
    vitamin_b12 = Column(Numeric)
    vitamin_b12_unit = Column(String(10), nullable=False)
    
    vitamin_c = Column(Numeric)
    vitamin_c_unit = Column(String(10), nullable=False)
    
    niacin = Column(Numeric)
    niacin_unit = Column(String(10), nullable=False)
    
    folacin = Column(Numeric)  # 对应 叶酸
    folacin_unit = Column(String(10), nullable=False)
    
    pantothenic = Column(Numeric)  # 对应 泛酸
    pantothenic_unit = Column(String(10), nullable=False)
    
    biotin = Column(Numeric)  # 对应 生物素
    biotin_unit = Column(String(10), nullable=False)
    
    choline = Column(Numeric)  # 对应 胆碱
    choline_unit = Column(String(10), nullable=False)

class VitaminPregnancyInc(Base):
    __tablename__ = "vitamin_pregnancy_inc"
    
    id = Column(Integer, primary_key=True, index=True)
    stage = Column(String(16), nullable=False)
    vitamin_d_inc = Column(Numeric)
    thiamine_inc = Column(Numeric)
    lactoflavin_inc = Column(Numeric)
    notes = Column(Text)
    unit = Column(String(10), nullable=False)
    vitamin_a_inc = Column(Numeric)
    vitamin_e_inc = Column(Numeric)
    vitamin_b6_inc = Column(Numeric)
    vitamin_b12_inc = Column(Numeric)
    vitamin_c_inc = Column(Numeric)
    niacin_inc = Column(Numeric)
    folacin_inc = Column(Numeric)
    pantothenic_inc = Column(Numeric)
    biotin_inc = Column(Numeric)
    choline_inc = Column(Numeric)

class MineralRNI(Base):
    __tablename__ = "mineral_rni"
    
    id = Column(Integer, primary_key=True, index=True)
    age_start_years = Column(Numeric(5, 2), nullable=False)
    sex = Column(String(6), nullable=False)
    rec_type = Column(String(3), nullable=False)
    
    calcium = Column(Numeric)
    calcium_unit = Column(String(10), nullable=False)
    phosphor = Column(Numeric)
    phosphor_unit = Column(String(10), nullable=False)
    kalium = Column(Numeric)
    kalium_unit = Column(String(10), nullable=False)
    natrium = Column(Numeric)
    natrium_unit = Column(String(10), nullable=False)
    magnesium = Column(Numeric)
    magnesium_unit = Column(String(10), nullable=False)
    chlorine = Column(Numeric)
    chlorine_unit = Column(String(10), nullable=False)
    iron = Column(Numeric)
    iron_unit = Column(String(10), nullable=False)
    iodine = Column(Numeric)
    iodine_unit = Column(String(10), nullable=False)
    zinc = Column(Numeric)
    zinc_unit = Column(String(10), nullable=False)
    selenium = Column(Numeric)
    selenium_unit = Column(String(10), nullable=False)
    copper = Column(Numeric)
    copper_unit = Column(String(10), nullable=False)
    fluorine = Column(Numeric)
    fluorine_unit = Column(String(10), nullable=False)
    chromium = Column(Numeric)
    chromium_unit = Column(String(10), nullable=False)
    cobalt = Column(Numeric)
    cobalt_unit = Column(String(10), nullable=False)
    # 注意: manganese(锰) 在CSV中存在但表中不存在

# 新增表: 水分参考摄入量
class WaterRNI(Base):
    __tablename__ = "water_rni"
    
    id = Column(Integer, primary_key=True, index=True)
    sex = Column(String(6), nullable=False)
    age_start_years = Column(Numeric(5, 2), nullable=False)
    drinking_ml = Column(Integer)  # 推荐饮水量 (mL/d)
    total_water_ml = Column(Integer)  # 总水摄入量 (mL/d)

# 新增表: 孕期/哺乳期水分增量
class WaterPregnancyInc(Base):
    __tablename__ = "water_pregnancy_inc"
    
    id = Column(Integer, primary_key=True, index=True)
    stage = Column(String(16), nullable=False)
    sex = Column(String(6), nullable=False)
    drinking_inc_ml = Column(Integer)  # 饮水量增量 (mL/d)
    total_water_inc_ml = Column(Integer)  # 总水摄入量增量 (mL/d)

# 新增表: 其他膳食成分SPL和UL值
class OtherDietarySplUl(Base):
    __tablename__ = "other_dietary_spl_ul"
    
    id = Column(Integer, primary_key=True, index=True)
    name_cn = Column(String(32), nullable=False)
    spl = Column(Numeric)  # 推荐最大摄入量 SPL
    spl_unit = Column(String(10))
    ul = Column(Numeric)  # 可耐受最高摄入量 UL
    ul_unit = Column(String(10))

# 新增表: 宏量营养素参考值
class NutritionReferenceMain(Base):
    __tablename__ = "nutrition_reference_main"
    
    id = Column(Integer, primary_key=True, index=True)
    sex = Column(String(6), nullable=False)
    age_start_years = Column(Numeric(5, 2), nullable=False)
    
    # 脂肪及脂肪酸
    fat = Column(Numeric)  # 总脂肪 AMDR 最大值
    saturated_fat = Column(Numeric)  # 饱和脂肪 AMDR 最大值
    pufa = Column(Numeric)  # n‑6 多不饱和脂肪酸 AMDR 最大值
    n3fa = Column(Numeric)  # n‑3 多不饱和脂肪酸 AMDR 最大值
    la = Column(Numeric)  # 亚油酸 AI 最大值
    ala = Column(Numeric)  # α‑亚麻酸 AI 最大值
    epa_dha = Column(Numeric)  # EPA+DHA 最大值 (g/d)
    
    # 碳水化合物
    carbohydrate_ear = Column(Numeric)  # 总碳水 EAR 或 AI (g/d)
    carbohydrate = Column(Numeric)  # 总碳水 AMDR 最大值 (%E→小数)
    fiber_dietary = Column(Numeric)  # 膳食纤维 AI 最大值 (g/d)
    fructose = Column(Numeric)  # 添加糖 AMDR 最大值 (%E→小数)
    
    # 蛋白质
    protein_ear = Column(Numeric)  # EAR (g/d)
    protein_rni = Column(Numeric)  # RNI (g/d)
    protein = Column(Numeric)  # AMDR 最大值 (%E→小数)

# 新增表: 孕期/哺乳期宏量营养素增量
class NutritionPregnancyInc(Base):
    __tablename__ = "nutrition_pregnancy_inc"
    
    id = Column(Integer, primary_key=True, index=True)
    stage = Column(String(16), nullable=False)
    
    # 脂肪及脂肪酸增量
    fat = Column(Numeric)
    saturated_fat = Column(Numeric)
    pufa = Column(Numeric)
    n3fa = Column(Numeric)
    la = Column(Numeric)
    ala = Column(Numeric)
    epa_dha = Column(Numeric)
    
    # 碳水化合物增量
    carbohydrate = Column(Numeric)
    fiber_dietary = Column(Numeric)
    fructose = Column(Numeric)
    
    # 蛋白质增量
    protein_ear = Column(Numeric)
    protein_rni = Column(Numeric)
    protein = Column(Numeric)
