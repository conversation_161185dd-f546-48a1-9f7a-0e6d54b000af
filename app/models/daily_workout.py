from sqlalchemy import Column, Integer, String, DateTime, Text, SmallInteger, ForeignKey, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.db.base_class import Base
import enum
from datetime import datetime

class DailyWorkoutStatus(enum.IntEnum):
    ACTIVE = 1
    DELETED = 2

class DailyWorkout(Base):
    """单日训练模型，不关联训练计划，用于社区分享"""
    __tablename__ = "daily_workouts"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)  # 用户必须存在
    # 关联原始Workout（可选）
    workout_id = Column(Integer, ForeignKey("workouts.id"), nullable=True)
    
    # 基本信息
    name = Column(String(100), nullable=False)  # 例如 "上肢训练"
    title = Column(String(100), nullable=True)  # 兼容community版本的title字段
    description = Column(Text, nullable=True)
    content = Column(String(1000), nullable=True)  # 兼容community版本的content字段
    
    # 训练总结数据
    training_duration = Column(SmallInteger, nullable=True)  # 训练时长（分钟）
    training_volume = Column(String(50), nullable=True)  # 训练总重量
    training_sets = Column(Integer, nullable=True)  # 训练总组数
    total_capacity = Column(SmallInteger, nullable=True)  # 总容量（例如总重量或总组数）- 兼容旧字段
    training_date = Column(DateTime, default=datetime.utcnow)
    visibility = Column(String(20), default="Everyone", nullable=False)
    
    # 状态与时间戳
    status = Column(Enum(DailyWorkoutStatus), default=DailyWorkoutStatus.ACTIVE)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    workout = relationship("Workout", back_populates="daily_workout")
    posts = relationship("Post", back_populates="related_workout", cascade="all, delete-orphan")
    user = relationship("User", back_populates="daily_workouts")
    workout_exercises = relationship("WorkoutExercise", back_populates="daily_workout", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<DailyWorkout {self.id}: {self.name or self.title}>"
