from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Enum
from sqlalchemy.orm import relationship
from app.db.base_class import Base
import enum
from datetime import datetime
from typing import TYPE_CHECKING

# 避免循环导入
if TYPE_CHECKING:
    from app.models.community.report import Report, CommentReport

class CommentStatus(enum.IntEnum):
    ACTIVE = 1
    DELETED = 2
    REPORTED = 3

class Comment(Base):
    __tablename__ = "comments"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    post_id = Column(Integer, ForeignKey("posts.id"), nullable=False)
    parent_id = Column(Integer, ForeignKey("comments.id"), nullable=True)
    content = Column(String(1000), nullable=False)
    like_count = Column(Integer, default=0)
    status = Column(Enum(CommentStatus), default=CommentStatus.ACTIVE)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="comments")
    post = relationship("Post", back_populates="comments")
    parent = relationship("Comment", remote_side=[id], backref="replies")
    likes = relationship("CommentLike", back_populates="comment")
    reports = relationship("Report", foreign_keys="Report.comment_id", back_populates="comment")