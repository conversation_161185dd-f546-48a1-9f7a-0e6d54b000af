from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Enum
from sqlalchemy.orm import relationship
from app.db.base_class import Base
import enum
from datetime import datetime

class ReportStatus(enum.IntEnum):
    PENDING = 1
    RESOLVED = 2
    REJECTED = 3

class Report(Base):
    """举报模型，可用于举报帖子或评论"""
    __tablename__ = "reports"

    id = Column(Integer, primary_key=True, index=True)
    reporter_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    post_id = Column(Integer, ForeignKey("posts.id"), nullable=True)
    comment_id = Column(Integer, ForeignKey("comments.id"), nullable=True)
    reason = Column(String(500), nullable=False)
    status = Column(Enum(ReportStatus), default=ReportStatus.PENDING)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关系
    reporter = relationship("User", foreign_keys=[reporter_id])
    post = relationship("Post", back_populates="reports")
    comment = relationship("Comment", back_populates="reports")

# PostReport和CommentReport是Report的别名，用于兼容性
PostReport = Report
CommentReport = Report 