from sqlalchemy import <PERSON>um<PERSON>, Integer, Foreign<PERSON>ey, DateTime
from sqlalchemy.orm import relationship
from app.db.base_class import Base
from datetime import datetime

class PostLike(Base):
    """帖子点赞模型"""
    __tablename__ = "post_likes"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    post_id = Column(Integer, ForeignKey("posts.id"), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)

    # 关系
    user = relationship("User", back_populates="post_likes")
    post = relationship("Post", back_populates="likes")

class CommentLike(Base):
    """评论点赞模型"""
    __tablename__ = "comment_likes"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    comment_id = Column(Integer, Foreign<PERSON>ey("comments.id"), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)

    # 关系
    user = relationship("User", back_populates="comment_likes")
    comment = relationship("Comment", back_populates="likes") 