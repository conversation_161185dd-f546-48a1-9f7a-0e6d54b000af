from sqlalchemy import Column, Integer, String, DateTime, Foreign<PERSON>ey, Enum, Boolean
from sqlalchemy.orm import relationship
from app.db.base_class import Base
import enum
from datetime import datetime

class NotificationType(enum.IntEnum):
    LIKE = 1
    COMMENT = 2
    FOLLOW = 3
    SYSTEM = 4

class Notification(Base):
    __tablename__ = "notifications"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    type = Column(Enum(NotificationType), nullable=False)
    content = Column(String(500), nullable=False)
    is_read = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="notifications") 