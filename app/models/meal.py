from sqlalchemy import Column, Integer, String, Boolean, Float, Foreign<PERSON>ey, DateTime, Date, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime

from app.db.base_class import Base


class MealRecord(Base):
    """餐食记录表"""
    __tablename__ = "meal_records"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    date = Column(Date, nullable=False, index=True)
    meal_type = Column(String(20), nullable=False)
    image_url = Column(String, nullable=True)
    file_id = Column(String(100), nullable=True)
    thumb_image_url = Column(String, nullable=True)
    total_calory = Column(Float, default=0)
    total_protein = Column(Float, default=0)
    total_fat = Column(Float, default=0)
    total_carbohydrate = Column(Float, default=0)
    is_ai_recognized = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关联关系
    user = relationship("User", back_populates="meal_records")
    food_items = relationship("FoodItem", back_populates="meal_record", cascade="all, delete-orphan")
    health_recommendations = relationship("HealthRecommendation", back_populates="meal_record", cascade="all, delete-orphan")
    food_recognition = relationship("FoodRecognition", back_populates="meal_record", uselist=False)


class FoodItem(Base):
    """食物项表"""
    __tablename__ = "food_items"

    id = Column(Integer, primary_key=True, index=True)
    meal_record_id = Column(Integer, ForeignKey("meal_records.id", ondelete="CASCADE"), index=True)
    food_id = Column(Integer, ForeignKey("foods.id"), nullable=True)
    name = Column(String(100), nullable=False)
    quantity = Column(Float, default=1.0)
    unit_name = Column(String(20), default="份")
    weight = Column(Float, nullable=False)
    category = Column(String(50), nullable=True)
    cuisine_type = Column(String(50), nullable=True)
    cuisine_type_detail = Column(String(100), nullable=True)
    image_url = Column(String, nullable=True)
    
    # 营养相关字段
    health_light = Column(Integer, nullable=True)
    lights = Column(JSON, nullable=True)
    warnings = Column(JSON, nullable=True)
    warning_scenes = Column(JSON, nullable=True)
    calory = Column(Float, nullable=True)
    protein = Column(Float, nullable=True)
    fat = Column(Float, nullable=True)
    carbohydrate = Column(Float, nullable=True)
    protein_fraction = Column(Float, nullable=True)
    fat_fraction = Column(Float, nullable=True)
    carb_fraction = Column(Float, nullable=True)
    
    is_custom = Column(Boolean, default=False)
    is_takeout = Column(Boolean, default=False)  # 是否为外卖食品
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关联关系
    meal_record = relationship("MealRecord", back_populates="food_items")
    food = relationship("Food", backref="food_items")
    nutrient_intakes = relationship("FoodItemNutrientIntake", back_populates="food_item", cascade="all, delete-orphan")


class FoodItemNutrientIntake(Base):
    """食物营养素摄入详情表"""
    __tablename__ = "food_item_nutrient_intakes"

    id = Column(Integer, primary_key=True, index=True)
    food_item_id = Column(Integer, ForeignKey("food_items.id", ondelete="CASCADE"), index=True)
    name_en = Column(String(64), nullable=False)
    name_cn = Column(String(64), nullable=False)
    value = Column(Float, nullable=True)
    unit = Column(String(16), nullable=True)
    unit_name = Column(String(16), nullable=True)
    nrv_percentage = Column(Float, nullable=True)
    category = Column(String(16), nullable=False, index=True)

    # 关联关系
    food_item = relationship("FoodItem", back_populates="nutrient_intakes")


class HealthRecommendation(Base):
    """健康建议表"""
    __tablename__ = "health_recommendations"

    id = Column(Integer, primary_key=True, index=True)
    meal_record_id = Column(Integer, ForeignKey("meal_records.id", ondelete="CASCADE"), index=True)
    recommendation_text = Column(String, nullable=False)
    recommendation_type = Column(String(32), nullable=True)
    priority = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # 关联关系
    meal_record = relationship("MealRecord", back_populates="health_recommendations")


class FoodRecognition(Base):
    """食物识别临时记录表"""
    __tablename__ = "food_recognitions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    meal_date = Column(Date, nullable=False)
    meal_type = Column(String(20), nullable=False)
    image_url = Column(String(255), nullable=True)
    thumb_image_url = Column(String(255), nullable=True)  # 添加缩略图URL字段
    secure_path = Column(String(100), nullable=True)
    status = Column(String(20), default="processing")  # processing, completed, confirmed, rejected, error
    recognition_result = Column(JSON, nullable=True)
    matched_foods = Column(JSON, nullable=True)
    nutrition_totals = Column(JSON, nullable=True)
    meal_record_id = Column(Integer, ForeignKey("meal_records.id"), nullable=True)
    user_modified = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    meal_record = relationship("MealRecord", back_populates="food_recognition")
    user = relationship("User", back_populates="food_recognitions") 