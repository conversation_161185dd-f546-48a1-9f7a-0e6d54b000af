from sqlalchemy import Column, Integer, String, Foreign<PERSON>ey, DateTime, Boolean, Text, func
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.base_class import Base

class UserFavoriteExercise(Base):
    """用户收藏的健身动作"""
    __tablename__ = "user_favorite_exercises"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    exercise_id = Column(Integer, ForeignKey("exercises.id", ondelete="CASCADE"), nullable=False)
    notes = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    is_active = Column(Boolean, default=True)
    
    # 关联关系
    user = relationship("User", back_populates="favorite_exercises")
    exercise = relationship("Exercise", backref="favorited_by")
    
    def __repr__(self):
        return f"<UserFavoriteExercise user_id={self.user_id} exercise_id={self.exercise_id}>"

class UserFavoriteFood(Base):
    """用户收藏的食物"""
    __tablename__ = "user_favorite_foods"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    food_id = Column(Integer, ForeignKey("foods.id", ondelete="CASCADE"), nullable=False)
    notes = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    is_active = Column(Boolean, default=True)
    
    # 关联关系
    user = relationship("User", back_populates="favorite_foods")
    food = relationship("Food", backref="favorited_by")
    
    def __repr__(self):
        return f"<UserFavoriteFood user_id={self.user_id} food_id={self.food_id}>" 