from sqlalchemy import Column, Integer, String, DateTime, <PERSON><PERSON><PERSON>, SmallInteger, Text, ForeignKey, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.db.base_class import Base

# 导入 ClientTrainingPlan 类
from app.models.team.training import ClientTrainingPlan

class TrainingPlan(Base):
    __tablename__ = "training_plans"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    plan_name = Column(String(100), nullable=False, default="个性化训练计划")
    description = Column(Text, nullable=True)
    fitness_goal = Column(SmallInteger, nullable=True)  # 参照 User 模型的FitnessGoal枚举
    experience_level = Column(SmallInteger, nullable=True)  # 参照 User 模型的ExperienceLevel枚举
    duration_weeks = Column(SmallInteger, nullable=True, default=4)

    # 添加开始日期和结束日期
    start_date = Column(DateTime(timezone=True), nullable=True, index=True)
    end_date = Column(DateTime(timezone=True), nullable=True)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    is_active = Column(Boolean, default=True)
    is_template = Column(Boolean, default=False, nullable=False)
    privacy_setting = Column(SmallInteger, default=1, nullable=False)  # 0: Public, 1: Private
    status = Column(String(20), default="active")  # active, completed, paused

    # 关系
    user = relationship("User", back_populates="training_plans")
    workouts = relationship("Workout", back_populates="training_plan", cascade="all, delete-orphan")
    # 使用字符串引用避免循环导入
    client_plans = relationship("app.models.team.training.ClientTrainingPlan", back_populates="training_plan", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<TrainingPlan {self.id}: {self.plan_name}>"