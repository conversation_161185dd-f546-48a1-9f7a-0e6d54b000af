from sqlalchemy import Column, String, Integer, ARRAY, Text, ForeignKey, SmallInteger, Boolean, Index, func, DateTime, Table
from sqlalchemy.orm import relationship
from app.db.base_class import Base
import datetime

# 定义中间表
exercise_body_parts = Table(
    'exercise_body_parts',
    Base.metadata,
    Column('exercise_id', Integer, ForeignKey('exercises.id'), primary_key=True),
    Column('body_part_id', Integer, ForeignKey('body_parts.id'), primary_key=True)
)

exercise_equipment = Table(
    'exercise_equipment',
    Base.metadata,
    Column('exercise_id', Integer, ForeignKey('exercises.id'), primary_key=True),
    Column('equipment_id', Integer, ForeignKey('equipment.id'), primary_key=True)
)

class Exercise(Base):
    """健身动作基本信息"""
    __tablename__ = "exercises"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    en_name = Column(String(100), index=True)
    body_part_id = Column(ARRAY(Integer))
    equipment_id = Column(ARRAY(Integer))
    image_name = Column(String(255))
    gif_url = Column(String(255))
    description = Column(Text)
    level = Column(SmallInteger)
    sort_priority = Column(Integer, default=0)
    user_id = Column(Integer)
    exercise_type = Column(String(50))
    hit_time = Column(Integer, default=0)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow,
                        onupdate=datetime.datetime.utcnow)

    # 关联关系
    details = relationship("ExerciseDetail", back_populates="exercise", uselist=False)
    body_parts = relationship("BodyPart", secondary=exercise_body_parts, back_populates="exercises")
    equipment = relationship("Equipment", secondary=exercise_equipment, back_populates="exercises")
    training_plan_records = relationship("UserTrainingPlanRecord", back_populates="exercise")
    workout_exercises = relationship("WorkoutExercise", back_populates="exercise")

    __table_args__ = (
        # 复合索引，用于提高多条件组合查询性能
        Index('idx_exercise_name_en_name', 'name', 'en_name'),
        Index('idx_exercise_level_hit_time', 'level', 'hit_time'),

        # GIN索引，专门为数组字段优化
        Index('idx_body_part_id_gin', body_part_id, postgresql_using='gin'),
        Index('idx_equipment_id_gin', equipment_id, postgresql_using='gin'),

        # 函数索引，用于提高不区分大小写的关键词搜索性能
        Index('idx_exercise_name_lower', func.lower(name)),
        Index('idx_exercise_en_name_lower', func.lower(en_name)),

        # 部分索引，用于优化常见查询场景
        Index('idx_popular_exercises', hit_time.desc(), postgresql_where=(hit_time > 0)),

        # 复合索引，用于提高排序性能
        Index('idx_exercise_sort_priority', sort_priority.desc(), id.asc()),
    )

class ExerciseDetail(Base):
    """健身动作详细信息"""
    __tablename__ = "exercise_details"

    id = Column(Integer, primary_key=True, index=True)
    exercise_id = Column(Integer, ForeignKey("exercises.id"), nullable=False, index=True)
    target_muscles_id = Column(ARRAY(Integer))
    synergist_muscles_id = Column(ARRAY(Integer))
    ex_instructions = Column(ARRAY(Text))
    exercise_tips = Column(ARRAY(Text))
    video_file = Column(String(255))
    is_public = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow,
                        onupdate=datetime.datetime.utcnow)

    # 关联关系
    exercise = relationship("Exercise", back_populates="details")

    __table_args__ = (
        # GIN索引，专门为数组字段优化，加速查询包含特定肌肉的动作
        Index('idx_target_muscles_gin', target_muscles_id, postgresql_using='gin'),
        Index('idx_synergist_muscles_gin', synergist_muscles_id, postgresql_using='gin'),

        # 添加索引提高JOIN查询性能，并标记为公开的动作优先
        Index('idx_exercise_detail_public', exercise_id, is_public),
    )

class Muscle(Base):
    """肌肉信息"""
    __tablename__ = "muscles"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), nullable=False)
    en_name = Column(String(50), nullable=False, unique=True)

    __table_args__ = (
        # 为支持多语言搜索添加索引
        Index('idx_muscle_name', name),
        Index('idx_muscle_name_lower', func.lower(name)),
    )

class BodyPart(Base):
    """身体部位"""
    __tablename__ = "body_parts"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), nullable=False, unique=True)
    
    # 添加与 Exercise 的关系
    exercises = relationship("Exercise", secondary=exercise_body_parts, back_populates="body_parts")
    

    __table_args__ = (
        # 优化身体部位查询
        Index('idx_body_part_name_lower', func.lower(name)),
    )

class Equipment(Base):
    """器材"""
    __tablename__ = "equipment"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), nullable=False, unique=True)
    
    # 添加与 Exercise 的关系
    exercises = relationship("Exercise", secondary=exercise_equipment, back_populates="equipment")
    

    __table_args__ = (
        # 优化器材查询
        Index('idx_equipment_name_lower', func.lower(name)),
    )