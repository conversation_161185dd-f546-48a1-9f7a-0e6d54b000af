from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Boolean, Text, Enum
from sqlalchemy.orm import relationship
from datetime import datetime
import enum

from app.db.base_class import Base


class TaskType(enum.Enum):
    """任务类型枚举"""
    DAILY = "daily"  # 日常任务
    WEEKLY = "weekly"  # 周常任务
    CHALLENGE = "challenge"  # 挑战任务


class TaskCategory(enum.Enum):
    """任务类别枚举"""
    EXERCISE = "exercise"  # 运动类
    DIET = "diet"  # 饮食类
    SOCIAL = "social"  # 社交类
    COMBINED = "combined"  # 综合类


class Task(Base):
    """任务模型，定义系统中的各类任务"""
    __tablename__ = "tasks"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    description = Column(Text)
    task_type = Column(Enum(TaskType), nullable=False)
    category = Column(Enum(TaskCategory), nullable=False)
    
    # 任务要求
    requirement_type = Column(String, nullable=False)  # workout_minutes(训练时间), food_record_count(饮食记录数), etc.
    requirement_value = Column(Integer, nullable=False)  # 任务要求的数值
    
    # 奖励
    currency_reward = Column(Integer, default=0)  # 像素杠铃奖励
    experience_reward_type = Column(String, nullable=True)  # exercise(运动经验), diet(饮食经验), both(两者都有)
    experience_reward_value = Column(Integer, default=0)  # 经验值奖励
    card_reward_id = Column(Integer, ForeignKey("cards.id"), nullable=True)  # 卡片奖励
    
    # 等级要求
    min_level_required = Column(Integer, default=1)  # 最低等级要求
    level_type = Column(String, nullable=True)  # exercise(运动等级), diet(饮食等级)
    
    # 其他属性
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    card_reward = relationship("Card")
    user_tasks = relationship("UserTask", back_populates="task")


class UserTask(Base):
    """用户任务模型，记录用户的任务进度和完成情况"""
    __tablename__ = "user_tasks"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    task_id = Column(Integer, ForeignKey("tasks.id"), index=True)
    progress = Column(Integer, default=0)  # 当前进度
    completed = Column(Boolean, default=False)  # 是否完成
    
    # 时间相关
    created_at = Column(DateTime, default=datetime.utcnow)  # 任务获取时间
    expires_at = Column(DateTime, nullable=False)  # 任务过期时间
    completed_at = Column(DateTime, nullable=True)  # 完成时间
    reward_claimed = Column(Boolean, default=False)  # 是否已领取奖励
    
    # 关系
    user = relationship("User", back_populates="tasks")
    task = relationship("Task", back_populates="user_tasks")


class DailyCheckIn(Base):
    """每日签到模型，记录用户的签到情况"""
    __tablename__ = "daily_checkins"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    checkin_date = Column(DateTime, default=datetime.utcnow)
    streak_count = Column(Integer, default=1)  # 连续签到天数
    
    # 奖励
    currency_reward = Column(Integer, default=1)  # 像素杠铃奖励
    experience_reward_exercise = Column(Integer, default=0)  # 运动经验奖励
    experience_reward_diet = Column(Integer, default=0)  # 饮食经验奖励
    
    # 关系
    user = relationship("User", back_populates="checkins") 