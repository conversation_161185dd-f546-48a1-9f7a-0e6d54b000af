from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Boolean, Text, Float
from sqlalchemy.orm import relationship
from datetime import datetime

from app.db.base_class import Base


class Currency(Base):
    """用户虚拟货币模型，存储用户的像素杠铃数量"""
    __tablename__ = "currencies"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), unique=True, index=True)
    amount = Column(Integer, default=0)  # 当前余额
    lifetime_earned = Column(Integer, default=0)  # 历史总收入
    daily_earned_today = Column(Integer, default=0)  # 今日已获取（用于每日上限控制）
    last_reset_date = Column(DateTime, default=datetime.utcnow)  # 上次重置每日计数的日期
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="currency")
    transactions = relationship("CurrencyTransaction", back_populates="currency")


class CurrencyTransaction(Base):
    """虚拟货币交易记录，记录用户货币的获取和消费"""
    __tablename__ = "currency_transactions"
    
    id = Column(Integer, primary_key=True, index=True)
    currency_id = Column(Integer, ForeignKey("currencies.id"), index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    amount = Column(Integer)  # 正数为获得，负数为消费
    balance_after = Column(Integer)  # 交易后余额
    description = Column(String)
    transaction_type = Column(String)  # task_reward, achievement, level_up, purchase, exchange, etc.
    related_entity_type = Column(String, nullable=True)  # task, achievement, shop_item, etc.
    related_entity_id = Column(Integer, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 关系
    user = relationship("User")
    currency = relationship("Currency", back_populates="transactions")


class ShopItem(Base):
    """商店物品模型，定义可购买的虚拟或实物商品"""
    __tablename__ = "shop_items"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    description = Column(Text)
    image_url = Column(String)
    category = Column(String, nullable=False)  # virtual(虚拟物品), physical(实物商品)
    subcategory = Column(String)  # appearance(外观), equipment(装备), nutrition(营养品), experience(体验), etc.
    price = Column(Integer, nullable=False)  # 价格（像素杠铃）
    stock = Column(Integer, nullable=True)  # 库存数量，null表示无限库存
    is_limited = Column(Boolean, default=False)  # 是否限定商品
    is_active = Column(Boolean, default=True)  # 是否在售
    
    # 折扣相关
    discount_level_type = Column(String, nullable=True)  # exercise(运动等级折扣), diet(饮食等级折扣)
    discount_level_required = Column(Integer, nullable=True)  # 折扣所需等级
    discount_percentage = Column(Integer, nullable=True)  # 折扣百分比，如20表示8折
    
    # 时间相关
    created_at = Column(DateTime, default=datetime.utcnow)
    available_from = Column(DateTime, nullable=True)  # 开始销售时间
    available_until = Column(DateTime, nullable=True)  # 结束销售时间
    
    # 关系
    purchases = relationship("UserPurchase", back_populates="item")
    # 关联卡片（如果购买的是卡片）
    card_id = Column(Integer, ForeignKey("cards.id"), nullable=True)
    card = relationship("Card")


class UserPurchase(Base):
    """用户购买记录，记录用户的商品购买历史"""
    __tablename__ = "user_purchases"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    item_id = Column(Integer, ForeignKey("shop_items.id"), index=True)
    quantity = Column(Integer, default=1)  # 购买数量
    price_paid = Column(Integer)  # 支付价格
    total_paid = Column(Integer)  # 总支付金额
    discount_applied = Column(Float, default=0)  # 应用的折扣
    transaction_id = Column(Integer, ForeignKey("currency_transactions.id"), nullable=True)
    
    # 物流相关（实物商品）
    status = Column(String, default="completed")  # completed(已完成), pending(待处理), shipped(已发货), etc.
    shipping_address = Column(Text, nullable=True)
    tracking_number = Column(String, nullable=True)
    
    # 时间相关
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="purchases")
    item = relationship("ShopItem", back_populates="purchases")
    transaction = relationship("CurrencyTransaction") 