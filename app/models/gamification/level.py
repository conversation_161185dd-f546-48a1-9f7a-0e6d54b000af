from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from datetime import datetime

from app.db.base_class import Base

class UserLevel(Base):
    """用户等级模型，存储用户在运动和饮食两个路线上的等级和经验值"""
    __tablename__ = "user_levels"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), unique=True, index=True)
    exercise_level = Column(Integer, default=1)
    exercise_experience = Column(Integer, default=0)
    diet_level = Column(Integer, default=1)
    diet_experience = Column(Integer, default=0)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="level")
    titles = relationship("UserTitle", back_populates="user_level")


class UserAttribute(Base):
    """用户属性模型，存储用户的运动和饮食相关属性值"""
    __tablename__ = "user_attributes"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), unique=True, index=True)
    # 运动属性
    strength = Column(Integer, default=10)  # 力量
    endurance = Column(Integer, default=10)  # 耐力
    flexibility = Column(Integer, default=10)  # 灵活性
    # 饮食属性
    nutrition_knowledge = Column(Integer, default=10)  # 营养知识
    cooking_skill = Column(Integer, default=10)  # 烹饪技巧
    diet_planning = Column(Integer, default=10)  # 饮食规划
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    user = relationship("User", back_populates="attributes")


class UserTitle(Base):
    """用户称号模型，存储用户获得的所有称号"""
    __tablename__ = "user_titles"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), index=True)
    level_id = Column(Integer, ForeignKey("user_levels.id"), index=True)
    title_name = Column(String, nullable=False)
    title_type = Column(String, nullable=False)  # exercise(运动), diet(饮食), combined(综合)
    obtained_at = Column(DateTime, default=datetime.utcnow)
    is_active = Column(Boolean, default=False)  # 当前是否显示该称号
    
    # 关系
    user = relationship("User", back_populates="titles")
    user_level = relationship("UserLevel", back_populates="titles") 