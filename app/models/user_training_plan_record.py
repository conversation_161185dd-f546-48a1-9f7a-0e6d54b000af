from sqlalchemy import Column, Integer, String, Date, JSON, Text, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base_class import Base

class UserTrainingPlanRecord(Base):
    """用户训练计划记录表"""
    __tablename__ = "user_training_plan_records"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    exercise_id = Column(Integer, ForeignKey("exercises.id"), nullable=False, index=True)
    name = Column(String(255))  # 添加 name 字段
    date = Column(Date, nullable=False, index=True)
    sets = Column(JSON)  # 存储每组的具体信息（重量、次数等）
    total_sets = Column(Integer)
    muscle_group = Column(String(50), index=True)
    notes = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关联关系
    user = relationship("User", back_populates="training_plan_records")
    exercise = relationship("Exercise", back_populates="training_plan_records")

    def to_dict(self):
        """转换为字典格式"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "exercise_id": self.exercise_id,
            "name": self.name,  # 添加 name 字段到返回数据中
            "date": self.date.isoformat() if self.date else None,
            "sets": self.sets,
            "total_sets": self.total_sets,
            "muscle_group": self.muscle_group,
            "notes": self.notes,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        } 