from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, func
from sqlalchemy.orm import relationship

from app.db.base_class import Base


class ClientTransferHistory(Base):
    """客户转移历史记录模型"""
    
    __tablename__ = "client_transfer_history"
    
    id = Column(Integer, primary_key=True, index=True)
    client_relation_id = Column(Integer, ForeignKey("client_relations.id", ondelete="CASCADE"), index=True)
    from_coach_id = Column(Integer, ForeignKey("users.id", ondelete="SET NULL"), nullable=True)
    to_coach_id = Column(Integer, ForeignKey("users.id", ondelete="SET NULL"), nullable=True)
    reason = Column(String, nullable=True)
    transferred_at = Column(DateTime, server_default=func.now())
    
    # 关系
    client_relation = relationship("ClientRelation", back_populates="transfer_history")
    from_coach = relationship("User", foreign_keys=[from_coach_id])
    to_coach = relationship("User", foreign_keys=[to_coach_id]) 