from sqlalchemy import Column, Integer, DateTime, ForeignKey, Float, Text, JSON, String, UniqueConstraint
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.db.base_class import Base

# 避免循环导入
import app.models.training_plan

class ClientTrainingPlan(Base):
    __tablename__ = "client_training_plans"

    id = Column(Integer, primary_key=True, index=True)
    client_relation_id = Column(Integer, ForeignKey("client_relations.id"))
    training_plan_id = Column(Integer, ForeignKey("training_plans.id"))
    coach_id = Column(Integer, ForeignKey("users.id"))
    status = Column(String(20))
    start_date = Column(DateTime, nullable=False)
    end_date = Column(DateTime, nullable=False)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())

    # 预约相关
    scheduled_time = Column(JSON)  # 存储每周固定训练时间
    next_session = Column(DateTime)  # 下次训练时间

    # 进度追踪
    completion_rate = Column(Float, default=0)
    last_workout_date = Column(DateTime)

    # 备注
    notes = Column(Text)

    # 关系
    client_relation = relationship("ClientRelation", back_populates="training_plans")
    training_plan = relationship("app.models.training_plan.TrainingPlan", back_populates="client_plans")
    coach = relationship("User", back_populates="coached_plans")
    sessions = relationship("TrainingSession", back_populates="client_plan", cascade="all, delete-orphan")

    __table_args__ = (
        UniqueConstraint(
            'client_relation_id',
            'training_plan_id',
            'start_date',
            name='uq_client_plan_period'
        ),
    )

    def __repr__(self):
        return f"<ClientTrainingPlan {self.id} for Client Relation {self.client_relation_id}>"

class TrainingSession(Base):
    __tablename__ = "training_sessions"

    id = Column(Integer, primary_key=True, index=True)
    client_plan_id = Column(Integer, ForeignKey("client_training_plans.id"))
    scheduled_start = Column(DateTime, nullable=False)
    actual_start = Column(DateTime)
    actual_end = Column(DateTime)
    status = Column(String(20))
    completion_rate = Column(Float, default=0)
    feedback = Column(Text)
    mood_rating = Column(Integer)  # 1-5
    difficulty_rating = Column(Integer)  # 1-5
    notes = Column(Text)

    # 关系
    exercise_records = relationship("SessionExerciseRecord", back_populates="session", cascade="all, delete-orphan")
    client_plan = relationship("ClientTrainingPlan", back_populates="sessions")

    def __repr__(self):
        return f"<TrainingSession {self.id} for Plan {self.client_plan_id}>"

class SessionExerciseRecord(Base):
    __tablename__ = "session_exercise_records"

    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(Integer, ForeignKey("training_sessions.id"))
    exercise_id = Column(Integer, ForeignKey("exercises.id"))
    order = Column(Integer)
    sets_planned = Column(Integer)
    sets_completed = Column(Integer, default=0)

    # 详细记录（JSON格式存储每组详情）
    set_records = Column(JSON, default=list)
    # 示例: [
    #   {"set": 1, "weight": 50, "reps": 12, "completed": true},
    #   {"set": 2, "weight": 52.5, "reps": 10, "completed": true}
    # ]

    notes = Column(Text)
    status = Column(String(20))

    # 关系
    session = relationship("TrainingSession", back_populates="exercise_records")
    exercise = relationship("Exercise")

    def __repr__(self):
        return f"<SessionExerciseRecord {self.id} for Session {self.session_id}>"
