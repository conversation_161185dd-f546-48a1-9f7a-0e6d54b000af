from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Text, JSON, Boolean
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.db.base_class import Base

class TrainingPlanTemplate(Base):
    __tablename__ = "training_plan_templates"

    id = Column(Integer, primary_key=True, index=True)
    team_id = Column(Integer, ForeignKey("teams.id"))
    creator_id = Column(Integer, ForeignKey("users.id"))
    name = Column(String(100), nullable=False)
    description = Column(Text)
    duration_weeks = Column(Integer, nullable=False)
    fitness_goal = Column(Integer)
    experience_level = Column(Integer)
    equipment_required = Column(JSON, default=list)
    template_data = Column(JSON, nullable=False)  # 完整的训练计划模板数据
    is_public = Column(Boolean, default=False)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())

    # 关系
    team = relationship("Team", back_populates="training_plan_templates")
    creator = relationship("User", back_populates="created_templates")
    exercises = relationship("TemplateExercise", back_populates="template", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<TrainingPlanTemplate {self.id}: {self.name}>"

class TemplateExercise(Base):
    __tablename__ = "template_exercises"

    id = Column(Integer, primary_key=True, index=True)
    template_id = Column(Integer, ForeignKey("training_plan_templates.id"))
    exercise_id = Column(Integer, ForeignKey("exercises.id"))
    day_number = Column(Integer)  # 第几天
    order = Column(Integer)  # 顺序
    sets = Column(Integer)
    reps = Column(Integer)
    rest_seconds = Column(Integer)
    notes = Column(Text)

    # 关系
    template = relationship("TrainingPlanTemplate", back_populates="exercises")
    exercise = relationship("Exercise")

    def __repr__(self):
        return f"<TemplateExercise {self.id} for Template {self.template_id}>"
