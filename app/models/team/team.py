from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Text, Boolean, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.db.base_class import Base
from app.models.team.enums import TeamStatus

class Team(Base):
    __tablename__ = "teams"

    id = Column(Integer, primary_key=True, index=True)
    owner_id = Column(Integer, ForeignKey("users.id"))
    name = Column(String(100), nullable=False)
    description = Column(Text)
    logo_url = Column(String)
    status = Column(Integer, default=TeamStatus.ACTIVE)
    settings = Column(JSON, default=dict)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())

    # 关系
    owner = relationship("User", back_populates="owned_teams")
    members = relationship("TeamMembership", back_populates="team", cascade="all, delete-orphan")
    training_plan_templates = relationship("TrainingPlanTemplate", back_populates="team", cascade="all, delete-orphan")
    stats = relationship("TeamStats", back_populates="team", uselist=False, cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Team {self.id}: {self.name}>"
