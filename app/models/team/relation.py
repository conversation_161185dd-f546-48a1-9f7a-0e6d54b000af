from sqlalchemy import Column, Integer, String, DateTime, Foreign<PERSON><PERSON>, Text, Boolean
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.db.base_class import Base

class ClientRelation(Base):
    __tablename__ = "client_relations"

    id = Column(Integer, primary_key=True, index=True)
    coach_id = Column(Integer, ForeignKey("users.id"))
    client_id = Column(Integer, ForeignKey("users.id"))
    status = Column(String(20), nullable=False)  # pending, active, paused, terminated
    start_date = Column(DateTime)
    end_date = Column(DateTime)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())

    # 合同相关
    contract_type = Column(String(50))  # monthly, quarterly, yearly, etc.
    contract_details = Column(Text)
    payment_status = Column(String(20))
    is_auto_renew = Column(<PERSON><PERSON>an, default=False)

    # 关系
    coach = relationship("User", foreign_keys=[coach_id], back_populates="coached_clients")
    client = relationship("User", foreign_keys=[client_id], back_populates="coaches")
    training_plans = relationship("ClientTrainingPlan", back_populates="client_relation", cascade="all, delete-orphan")
    transfer_history = relationship("ClientTransferHistory", back_populates="client_relation", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<ClientRelation {self.id}: Coach {self.coach_id} - Client {self.client_id}>" 