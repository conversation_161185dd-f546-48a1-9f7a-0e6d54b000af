"""
响应格式适配器

该模块提供响应格式适配功能，用于在不同格式的响应之间进行转换。
"""

import logging
from typing import Dict, Any, Union, Optional

logger = logging.getLogger(__name__)

class ResponseAdapter:
    """
    响应格式适配器
    
    用于在不同格式的响应之间进行转换，例如将字典响应转换为文本响应。
    """
    
    @staticmethod
    def to_text(response: Union[str, Dict[str, Any]]) -> str:
        """
        将响应转换为文本格式
        
        Args:
            response: 原始响应，可以是字符串或字典
            
        Returns:
            文本格式的响应
        """
        # 如果已经是字符串，直接返回
        if isinstance(response, str):
            return response
        
        # 如果是字典，提取内容
        if isinstance(response, dict):
            # 优先使用content字段
            if "content" in response:
                return response["content"]
            
            # 尝试使用response_content字段
            if "response_content" in response:
                return response["response_content"]
            
            # 尝试使用text字段
            if "text" in response:
                return response["text"]
            
            # 尝试使用message字段
            if "message" in response:
                return response["message"]
            
            # 如果没有找到内容字段，返回整个字典的字符串表示
            logger.warning(f"无法从字典响应中提取文本内容: {response}")
            return str(response)
        
        # 其他类型，转换为字符串
        return str(response)
    
    @staticmethod
    def get_text_response(response: Union[str, Dict[str, Any]]) -> str:
        """
        获取文本响应
        
        Args:
            response: 原始响应
            
        Returns:
            文本格式的响应
        """
        return ResponseAdapter.to_text(response)
    
    @staticmethod
    def extract_content(response: Dict[str, Any]) -> Optional[str]:
        """
        从字典响应中提取内容
        
        Args:
            response: 字典格式的响应
            
        Returns:
            提取的内容，如果没有找到则返回None
        """
        if "content" in response:
            return response["content"]
        
        if "response_content" in response:
            return response["response_content"]
        
        if "text" in response:
            return response["text"]
        
        if "message" in response:
            return response["message"]
        
        return None 