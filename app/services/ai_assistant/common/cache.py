"""
缓存服务模块

该模块提供缓存功能，用于优化AI助手的性能，减少重复计算和API调用。
"""

import time
import logging
import hashlib
import json
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Union, List, Tuple, Callable, TypeVar

logger = logging.getLogger(__name__)

T = TypeVar('T')

class CacheService(ABC):
    """
    缓存服务接口
    
    定义了缓存的基本操作，包括获取、设置和删除缓存项。
    """
    
    @abstractmethod
    async def get(self, key: str) -> Optional[Any]:
        """
        获取缓存项
        
        Args:
            key: 缓存键
            
        Returns:
            缓存的值，如果不存在则返回None
        """
        pass
    
    @abstractmethod
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """
        设置缓存项
        
        Args:
            key: 缓存键
            value: 要缓存的值
            ttl: 生存时间（秒），如果为None则永不过期
            
        Returns:
            是否成功设置
        """
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> bool:
        """
        删除缓存项
        
        Args:
            key: 缓存键
            
        Returns:
            是否成功删除
        """
        pass
    
    @abstractmethod
    async def clear(self) -> bool:
        """
        清空缓存
        
        Returns:
            是否成功清空
        """
        pass
    
    async def cached(
        self, 
        key_prefix: str,
        func: Callable[..., T],
        ttl: Optional[int] = None,
        *args, 
        **kwargs
    ) -> T:
        """
        装饰器方法，用于缓存函数调用结果
        
        Args:
            key_prefix: 缓存键前缀
            func: 要缓存的函数
            ttl: 生存时间（秒）
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            函数调用结果
        """
        # 生成缓存键
        cache_key = self._generate_cache_key(key_prefix, *args, **kwargs)
        
        # 尝试从缓存获取
        cached_result = await self.get(cache_key)
        if cached_result is not None:
            logger.debug(f"缓存命中: {cache_key}")
            return cached_result
        
        # 缓存未命中，调用函数
        logger.debug(f"缓存未命中: {cache_key}")
        result = await func(*args, **kwargs)
        
        # 缓存结果
        await self.set(cache_key, result, ttl)
        
        return result
    
    def _generate_cache_key(self, prefix: str, *args, **kwargs) -> str:
        """
        生成缓存键
        
        Args:
            prefix: 缓存键前缀
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            缓存键
        """
        # 序列化参数
        serialized_args = json.dumps(args, sort_keys=True)
        serialized_kwargs = json.dumps(kwargs, sort_keys=True)
        
        # 计算哈希值
        key_hash = hashlib.md5(f"{serialized_args}:{serialized_kwargs}".encode()).hexdigest()
        
        return f"{prefix}:{key_hash}"


class MemoryCacheService(CacheService):
    """
    内存缓存服务
    
    使用内存字典实现的简单缓存服务，适用于单进程应用。
    缓存项可以设置过期时间，过期后自动失效。
    """
    
    def __init__(self):
        """初始化内存缓存服务"""
        # 缓存存储: {key: (value, expiry_time)}
        # expiry_time为None表示永不过期
        self._cache: Dict[str, Tuple[Any, Optional[float]]] = {}
    
    async def get(self, key: str) -> Optional[Any]:
        """
        获取缓存项
        
        Args:
            key: 缓存键
            
        Returns:
            缓存的值，如果不存在或已过期则返回None
        """
        if key not in self._cache:
            return None
        
        value, expiry_time = self._cache[key]
        
        # 检查是否过期
        if expiry_time is not None and time.time() > expiry_time:
            # 过期了，删除并返回None
            del self._cache[key]
            return None
        
        return value
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None, expire_seconds: Optional[int] = None) -> bool:
        """
        设置缓存项
        
        Args:
            key: 缓存键
            value: 要缓存的值
            ttl: 生存时间（秒），如果为None则永不过期
            expire_seconds: 生存时间（秒）的别名，为兼容性保留
            
        Returns:
            是否成功设置
        """
        # 使用expire_seconds作为ttl的别名，优先使用ttl
        effective_ttl = ttl if ttl is not None else expire_seconds
        
        # 计算过期时间
        expiry_time = None
        if effective_ttl is not None:
            expiry_time = time.time() + effective_ttl
        
        # 存储值和过期时间
        self._cache[key] = (value, expiry_time)
        
        return True
    
    async def delete(self, key: str) -> bool:
        """
        删除缓存项
        
        Args:
            key: 缓存键
            
        Returns:
            是否成功删除
        """
        if key in self._cache:
            del self._cache[key]
            return True
        return False
    
    async def clear(self) -> bool:
        """
        清空缓存
        
        Returns:
            是否成功清空
        """
        self._cache.clear()
        return True
    
    async def cleanup(self) -> int:
        """
        清理过期的缓存项
        
        Returns:
            清理的项数
        """
        current_time = time.time()
        expired_keys = [
            key for key, (_, expiry_time) in self._cache.items()
            if expiry_time is not None and current_time > expiry_time
        ]
        
        for key in expired_keys:
            del self._cache[key]
        
        return len(expired_keys)


class LRUCacheService(CacheService):
    """
    LRU (Least Recently Used) 缓存服务
    
    使用LRU算法实现的缓存服务，当缓存达到容量上限时，
    会优先删除最近最少使用的项。
    """
    
    def __init__(self, capacity: int = 1000, max_size: Optional[int] = None):
        """
        初始化LRU缓存服务
        
        Args:
            capacity: 缓存容量上限
            max_size: 缓存容量上限的别名，为兼容性保留
        """
        # 使用max_size作为capacity的别名，优先使用max_size
        self._capacity = max_size if max_size is not None else capacity
        # 缓存存储: {key: (value, expiry_time, access_time)}
        self._cache: Dict[str, Tuple[Any, Optional[float], float]] = {}
    
    @property
    def max_size(self) -> int:
        """获取缓存容量上限"""
        return self._capacity
    
    @max_size.setter
    def max_size(self, value: int) -> None:
        """设置缓存容量上限"""
        self._capacity = value
    
    async def get(self, key: str) -> Optional[Any]:
        """
        获取缓存项
        
        Args:
            key: 缓存键
            
        Returns:
            缓存的值，如果不存在或已过期则返回None
        """
        if key not in self._cache:
            return None
        
        value, expiry_time, _ = self._cache[key]
        
        # 检查是否过期
        if expiry_time is not None and time.time() > expiry_time:
            # 过期了，删除并返回None
            del self._cache[key]
            return None
        
        # 更新访问时间
        self._cache[key] = (value, expiry_time, time.time())
        
        return value
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """
        设置缓存项
        
        Args:
            key: 缓存键
            value: 要缓存的值
            ttl: 生存时间（秒），如果为None则永不过期
            
        Returns:
            是否成功设置
        """
        # 如果缓存已满且要添加的键不在缓存中，需要删除最近最少使用的项
        if len(self._cache) >= self._capacity and key not in self._cache:
            self._evict_lru()
        
        # 计算过期时间
        expiry_time = None
        if ttl is not None:
            expiry_time = time.time() + ttl
        
        # 存储值、过期时间和访问时间
        self._cache[key] = (value, expiry_time, time.time())
        
        return True
    
    async def delete(self, key: str) -> bool:
        """
        删除缓存项
        
        Args:
            key: 缓存键
            
        Returns:
            是否成功删除
        """
        if key in self._cache:
            del self._cache[key]
            return True
        return False
    
    async def clear(self) -> bool:
        """
        清空缓存
        
        Returns:
            是否成功清空
        """
        self._cache.clear()
        return True
    
    def _evict_lru(self) -> None:
        """删除最近最少使用的缓存项"""
        if not self._cache:
            return
        
        # 找出访问时间最早的键
        lru_key = min(self._cache.items(), key=lambda x: x[1][2])[0]
        del self._cache[lru_key]


# 默认缓存服务实例
default_cache_service = MemoryCacheService() 