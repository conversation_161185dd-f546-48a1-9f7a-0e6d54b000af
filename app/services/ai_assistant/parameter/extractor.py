"""
参数提取器模块

这个模块提供了用于从用户输入中提取参数的接口和基础实现。
它定义了一个通用的参数提取器接口，以及基于不同策略的具体实现。
"""
from abc import ABC, abstractmethod
import logging
import json
import re
from typing import Dict, List, Any, Optional, Union, TypeVar, Generic, Type

from app.services.ai_assistant.llm.service import llm_service

logger = logging.getLogger(__name__)

T = TypeVar('T')

class BaseParameterExtractor(ABC, Generic[T]):
    """
    参数提取器基类
    
    定义了从用户输入中提取参数的通用接口。
    """
    
    @abstractmethod
    def extract(self, user_input: str, **kwargs) -> T:
        """
        从用户输入中提取参数
        
        Args:
            user_input: 用户输入文本
            **kwargs: 其他参数
            
        Returns:
            提取的参数对象
        """
        pass
    
    @abstractmethod
    async def aextract(self, user_input: str, **kwargs) -> T:
        """
        异步从用户输入中提取参数
        
        Args:
            user_input: 用户输入文本
            **kwargs: 其他参数
            
        Returns:
            提取的参数对象
        """
        pass


class LLMParameterExtractor(BaseParameterExtractor[Dict[str, Any]]):
    """
    基于LLM的参数提取器
    
    使用大语言模型从用户输入中提取参数。
    """
    
    def __init__(self, 
                 parameter_schema: Dict[str, Any], 
                 template: Optional[str] = None,
                 model: str = "intent-recognition-app",
                 provider: Optional[str] = None):
        """
        初始化基于LLM的参数提取器
        
        Args:
            parameter_schema: 参数模式，包含参数名称、类型和描述
            template: 提示模板，如果为None则使用默认模板
            model: 使用的模型
            provider: LLM提供商
        """
        self.parameter_schema = parameter_schema
        self.model = model
        self.provider = provider
        
        # 如果未提供模板，使用默认模板
        if template is None:
            self.template = self._create_default_template()
        else:
            self.template = template
    
    def _create_default_template(self) -> str:
        """
        创建默认提示模板
        
        根据参数模式创建默认的提示模板。
        
        Returns:
            提示模板
        """
        params_description = []
        for name, schema in self.parameter_schema.items():
            param_type = schema.get("type", "string")
            description = schema.get("description", "")
            required = schema.get("required", False)
            
            # 构建参数描述
            param_desc = f"- {name} ({param_type}): {description}"
            if required:
                param_desc += " [必需]"
            
            params_description.append(param_desc)
        
        # 组合成完整模板
        return f"""从以下用户输入中提取参数。如果某个参数未明确提及，请返回null。

参数说明：
{chr(10).join(params_description)}

用户输入："{'{user_input}'}

以JSON格式返回提取的参数：
"""
    
    def extract(self, user_input: str, **kwargs) -> Dict[str, Any]:
        """
        从用户输入中提取参数（同步方法）
        
        Args:
            user_input: 用户输入文本
            **kwargs: 其他参数，可以覆盖初始化时的设置
            
        Returns:
            提取的参数字典
        """
        # 填充模板
        prompt = self.template.format(user_input=user_input)
        
        # 获取模型和提供商，允许在调用时覆盖
        model = kwargs.pop("model", self.model)
        provider = kwargs.pop("provider", self.provider)
        
        # 调用LLM服务
        result = llm_service.generate_text(prompt, provider=provider, model=model, **kwargs)
        
        # 解析JSON结果
        try:
            # 尝试从可能嵌入在文本中的JSON内容提取JSON对象
            json_match = re.search(r'```json\s*(.+?)\s*```|({.+})', result, re.DOTALL)
            if json_match:
                json_str = json_match.group(1) or json_match.group(2)
                return json.loads(json_str)
            
            # 如果没有明确的JSON标记，尝试直接解析
            return json.loads(result)
        
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse LLM response as JSON: {str(e)}, response: {result}")
            return {}
    
    async def aextract(self, user_input: str, **kwargs) -> Dict[str, Any]:
        """
        异步从用户输入中提取参数
        
        Args:
            user_input: 用户输入文本
            **kwargs: 其他参数，可以覆盖初始化时的设置
            
        Returns:
            提取的参数字典
        """
        # 填充模板
        prompt = self.template.format(user_input=user_input)
        
        # 获取模型和提供商，允许在调用时覆盖
        model = kwargs.pop("model", self.model)
        provider = kwargs.pop("provider", self.provider)
        
        # 调用LLM服务
        result = await llm_service.agenerate_text(prompt, provider=provider, model=model, **kwargs)
        
        # 解析JSON结果
        try:
            # 尝试从可能嵌入在文本中的JSON内容提取JSON对象
            json_match = re.search(r'```json\s*(.+?)\s*```|({.+})', result, re.DOTALL)
            if json_match:
                json_str = json_match.group(1) or json_match.group(2)
                return json.loads(json_str)
            
            # 如果没有明确的JSON标记，尝试直接解析
            return json.loads(result)
        
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse LLM response as JSON: {str(e)}, response: {result}")
            return {}


class RegexParameterExtractor(BaseParameterExtractor[Dict[str, Any]]):
    """
    基于正则表达式的参数提取器
    
    使用预定义的正则表达式模式从用户输入中提取参数。
    """
    
    def __init__(self, patterns: Dict[str, str]):
        """
        初始化基于正则表达式的参数提取器
        
        Args:
            patterns: 参数名称到正则表达式模式的映射
        """
        self.patterns = {}
        
        # 编译正则表达式
        for name, pattern in patterns.items():
            self.patterns[name] = re.compile(pattern)
    
    def extract(self, user_input: str, **kwargs) -> Dict[str, Any]:
        """
        从用户输入中提取参数
        
        Args:
            user_input: 用户输入文本
            **kwargs: 其他参数
            
        Returns:
            提取的参数字典
        """
        result = {}
        
        for name, pattern in self.patterns.items():
            match = pattern.search(user_input)
            if match:
                # 如果模式有命名组，则使用命名组
                if match.groupdict():
                    for group_name, value in match.groupdict().items():
                        result[group_name] = value
                # 否则使用第一个捕获组
                else:
                    result[name] = match.group(1) if match.groups() else match.group(0)
        
        return result
    
    async def aextract(self, user_input: str, **kwargs) -> Dict[str, Any]:
        """
        异步从用户输入中提取参数
        
        Args:
            user_input: 用户输入文本
            **kwargs: 其他参数
            
        Returns:
            提取的参数字典
        """
        # 正则表达式提取是CPU密集型操作，不需要特殊的异步实现
        return self.extract(user_input, **kwargs)


class RuleBasedParameterExtractor(BaseParameterExtractor[Dict[str, Any]]):
    """
    基于规则的参数提取器
    
    使用预定义的关键词和规则从用户输入中提取参数。
    """
    
    def __init__(self, rules: Dict[str, List[str]]):
        """
        初始化基于规则的参数提取器
        
        Args:
            rules: 参数名称到关键词列表的映射
        """
        self.rules = rules
    
    def extract(self, user_input: str, **kwargs) -> Dict[str, Any]:
        """
        从用户输入中提取参数
        
        Args:
            user_input: 用户输入文本
            **kwargs: 其他参数
            
        Returns:
            提取的参数字典
        """
        result = {}
        
        # 转换为小写以进行不区分大小写的匹配
        user_input_lower = user_input.lower()
        
        for name, keywords in self.rules.items():
            for keyword in keywords:
                if keyword.lower() in user_input_lower:
                    result[name] = keyword
                    break
        
        return result
    
    async def aextract(self, user_input: str, **kwargs) -> Dict[str, Any]:
        """
        异步从用户输入中提取参数
        
        Args:
            user_input: 用户输入文本
            **kwargs: 其他参数
            
        Returns:
            提取的参数字典
        """
        # 基于规则的提取是CPU密集型操作，不需要特殊的异步实现
        return self.extract(user_input, **kwargs)


class CompositeParameterExtractor(BaseParameterExtractor[Dict[str, Any]]):
    """
    组合参数提取器
    
    组合多个参数提取器，按优先级顺序应用。
    """
    
    def __init__(self, extractors: List[BaseParameterExtractor[Dict[str, Any]]]):
        """
        初始化组合参数提取器
        
        Args:
            extractors: 参数提取器列表，按优先级顺序
        """
        self.extractors = extractors
    
    def extract(self, user_input: str, **kwargs) -> Dict[str, Any]:
        """
        从用户输入中提取参数
        
        Args:
            user_input: 用户输入文本
            **kwargs: 其他参数
            
        Returns:
            提取的参数字典
        """
        result = {}
        
        # 按优先级顺序应用提取器
        for extractor in self.extractors:
            # 提取参数
            params = extractor.extract(user_input, **kwargs)
            
            # 合并结果，保留已有值
            for name, value in params.items():
                if name not in result and value is not None:
                    result[name] = value
        
        return result
    
    async def aextract(self, user_input: str, **kwargs) -> Dict[str, Any]:
        """
        异步从用户输入中提取参数
        
        Args:
            user_input: 用户输入文本
            **kwargs: 其他参数
            
        Returns:
            提取的参数字典
        """
        result = {}
        
        # 按优先级顺序应用提取器
        for extractor in self.extractors:
            # 提取参数
            params = await extractor.aextract(user_input, **kwargs)
            
            # 合并结果，保留已有值
            for name, value in params.items():
                if name not in result and value is not None:
                    result[name] = value
        
        return result 