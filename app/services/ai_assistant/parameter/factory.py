"""
参数提取器工厂模块

这个模块提供了一个工厂类，用于创建和管理不同类型的参数提取器。
它简化了参数提取器的创建和使用。
"""
import logging
from typing import Dict, List, Any, Optional, Type, Union

from app.services.ai_assistant.parameter.extractor import (
    BaseParameterExtractor,
    LLMParameterExtractor,
    RegexParameterExtractor,
    RuleBasedParameterExtractor,
    CompositeParameterExtractor
)

logger = logging.getLogger(__name__)

class ParameterExtractorFactory:
    """
    参数提取器工厂类
    
    用于创建和管理不同类型的参数提取器。
    """
    
    _extractors: Dict[str, BaseParameterExtractor] = {}
    
    @classmethod
    def register(cls, name: str, extractor: BaseParameterExtractor) -> None:
        """
        注册参数提取器
        
        Args:
            name: 提取器名称
            extractor: 参数提取器实例
        """
        cls._extractors[name] = extractor
        logger.info(f"Registered parameter extractor: {name}")
    
    @classmethod
    def get(cls, name: str) -> BaseParameterExtractor:
        """
        获取参数提取器
        
        Args:
            name: 提取器名称
            
        Returns:
            参数提取器实例
            
        Raises:
            ValueError: 如果提取器不存在
        """
        if name not in cls._extractors:
            raise ValueError(f"Parameter extractor '{name}' not registered")
        
        return cls._extractors[name]
    
    @classmethod
    def create_llm_extractor(cls, 
                            name: str, 
                            parameter_schema: Dict[str, Any], 
                            template: Optional[str] = None,
                            model: str = "intent-recognition-app",
                            provider: Optional[str] = None) -> LLMParameterExtractor:
        """
        创建基于LLM的参数提取器
        
        Args:
            name: 提取器名称
            parameter_schema: 参数模式
            template: 提示模板
            model: 使用的模型
            provider: LLM提供商
            
        Returns:
            基于LLM的参数提取器
        """
        extractor = LLMParameterExtractor(
            parameter_schema=parameter_schema,
            template=template,
            model=model,
            provider=provider
        )
        
        cls.register(name, extractor)
        return extractor
    
    @classmethod
    def create_regex_extractor(cls, 
                              name: str, 
                              patterns: Dict[str, str]) -> RegexParameterExtractor:
        """
        创建基于正则表达式的参数提取器
        
        Args:
            name: 提取器名称
            patterns: 参数名称到正则表达式模式的映射
            
        Returns:
            基于正则表达式的参数提取器
        """
        extractor = RegexParameterExtractor(patterns=patterns)
        
        cls.register(name, extractor)
        return extractor
    
    @classmethod
    def create_rule_based_extractor(cls, 
                                   name: str, 
                                   rules: Dict[str, List[str]]) -> RuleBasedParameterExtractor:
        """
        创建基于规则的参数提取器
        
        Args:
            name: 提取器名称
            rules: 参数名称到关键词列表的映射
            
        Returns:
            基于规则的参数提取器
        """
        extractor = RuleBasedParameterExtractor(rules=rules)
        
        cls.register(name, extractor)
        return extractor
    
    @classmethod
    def create_composite_extractor(cls, 
                                  name: str, 
                                  extractor_names: List[str]) -> CompositeParameterExtractor:
        """
        创建组合参数提取器
        
        Args:
            name: 提取器名称
            extractor_names: 要组合的提取器名称列表
            
        Returns:
            组合参数提取器
        """
        extractors = []
        
        for extractor_name in extractor_names:
            try:
                extractor = cls.get(extractor_name)
                extractors.append(extractor)
            except ValueError as e:
                logger.warning(f"Skipping unavailable extractor '{extractor_name}': {str(e)}")
        
        if not extractors:
            raise ValueError("No valid extractors specified")
        
        extractor = CompositeParameterExtractor(extractors=extractors)
        
        cls.register(name, extractor)
        return extractor
    
    @classmethod
    def create_fitness_parameter_extractors(cls) -> None:
        """
        创建健身相关的参数提取器
        
        这个方法创建了一组预定义的提取器，用于提取健身相关的参数。
        """
        # 创建健身建议参数提取器
        fitness_advice_schema = {
            "fitness_level": {
                "type": "string",
                "description": "用户的健身水平，如初学者、中级或高级",
                "required": True
            },
            "fitness_goal": {
                "type": "string",
                "description": "用户的健身目标，如增肌、减脂或提高耐力",
                "required": True
            },
            "limitations": {
                "type": "string",
                "description": "用户的限制条件，如受伤、设备限制或时间限制"
            }
        }
        
        cls.create_llm_extractor(
            name="fitness_advice",
            parameter_schema=fitness_advice_schema
        )
        
        # 创建训练动作生成参数提取器
        exercise_generation_schema = {
            "target_muscle": {
                "type": "string",
                "description": "目标肌肉群，如胸肌、背肌或腿部",
                "required": True
            },
            "fitness_level": {
                "type": "string",
                "description": "用户的健身水平，如初学者、中级或高级"
            },
            "environment": {
                "type": "string",
                "description": "健身环境，如健身房、家庭或户外"
            },
            "available_equipment": {
                "type": "string",
                "description": "可用器材，如哑铃、杠铃或健身器械"
            }
        }
        
        cls.create_llm_extractor(
            name="exercise_generation",
            parameter_schema=exercise_generation_schema
        )
        
        # 创建肌肉群规则提取器
        muscle_rules = {
            "target_muscle": [
                "胸肌", "胸部", "chest", "pecs", "pectoralis",
                "背肌", "背部", "back", "lats", "latissimus",
                "腿部", "legs", "腿", "下肢", "大腿", "小腿",
                "肩膀", "肩部", "shoulders", "三角肌", "deltoids",
                "手臂", "arms", "二头肌", "biceps", "三头肌", "triceps",
                "腹肌", "腹部", "abs", "核心", "core",
                "臀部", "glutes", "臀肌", "gluteus"
            ]
        }
        
        cls.create_rule_based_extractor(
            name="muscle_recognizer",
            rules=muscle_rules
        )
        
        # 创建健身水平规则提取器
        fitness_level_rules = {
            "fitness_level": [
                "初学者", "beginner", "新手", "入门",
                "中级", "intermediate", "有经验",
                "高级", "advanced", "专业", "进阶"
            ]
        }
        
        cls.create_rule_based_extractor(
            name="fitness_level_recognizer",
            rules=fitness_level_rules
        )
        
        # 创建组合提取器
        cls.create_composite_extractor(
            name="exercise_composite",
            extractor_names=["exercise_generation", "muscle_recognizer", "fitness_level_recognizer"]
        )


# 创建工厂实例并初始化预定义的提取器
parameter_extractor_factory = ParameterExtractorFactory()
parameter_extractor_factory.create_fitness_parameter_extractors() 