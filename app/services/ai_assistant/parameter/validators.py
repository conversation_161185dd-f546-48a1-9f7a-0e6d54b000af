"""
参数验证器模块

这个模块提供了用于验证参数的工具函数和类。
它确保提取的参数符合预期的类型和约束条件。
"""
import logging
from typing import Dict, List, Any, Optional, Union, Callable, TypeVar

logger = logging.getLogger(__name__)

T = TypeVar('T')

class ValidationError(Exception):
    """参数验证错误"""
    
    def __init__(self, message: str, field: Optional[str] = None):
        self.field = field
        self.message = message
        super().__init__(message)


class FieldValidator:
    """
    字段验证器
    
    用于验证单个字段的值是否符合要求。
    """
    
    def __init__(self, 
                 field_name: str, 
                 required: bool = False, 
                 field_type: Optional[type] = None,
                 min_value: Optional[Union[int, float]] = None,
                 max_value: Optional[Union[int, float]] = None,
                 min_length: Optional[int] = None,
                 max_length: Optional[int] = None,
                 pattern: Optional[str] = None,
                 choices: Optional[List[Any]] = None,
                 custom_validator: Optional[Callable[[Any], bool]] = None,
                 error_message: Optional[str] = None):
        """
        初始化字段验证器
        
        Args:
            field_name: 字段名称
            required: 是否必需
            field_type: 期望的字段类型
            min_value: 最小值（对于数值类型）
            max_value: 最大值（对于数值类型）
            min_length: 最小长度（对于字符串、列表等）
            max_length: 最大长度（对于字符串、列表等）
            pattern: 正则表达式模式（对于字符串）
            choices: 可选值列表
            custom_validator: 自定义验证函数
            error_message: 自定义错误消息
        """
        self.field_name = field_name
        self.required = required
        self.field_type = field_type
        self.min_value = min_value
        self.max_value = max_value
        self.min_length = min_length
        self.max_length = max_length
        self.pattern = pattern
        self.choices = choices
        self.custom_validator = custom_validator
        self.error_message = error_message
    
    def validate(self, value: Any) -> Optional[str]:
        """
        验证字段值
        
        Args:
            value: 要验证的值
            
        Returns:
            如果验证失败，返回错误消息；否则返回None
        """
        # 检查是否为None
        if value is None:
            if self.required:
                return f"字段 '{self.field_name}' 是必需的"
            return None
        
        # 检查类型
        if self.field_type is not None and not isinstance(value, self.field_type):
            return f"字段 '{self.field_name}' 的类型应为 {self.field_type.__name__}"
        
        # 检查数值范围
        if isinstance(value, (int, float)):
            if self.min_value is not None and value < self.min_value:
                return f"字段 '{self.field_name}' 的值不能小于 {self.min_value}"
            if self.max_value is not None and value > self.max_value:
                return f"字段 '{self.field_name}' 的值不能大于 {self.max_value}"
        
        # 检查长度
        if hasattr(value, '__len__'):
            if self.min_length is not None and len(value) < self.min_length:
                return f"字段 '{self.field_name}' 的长度不能小于 {self.min_length}"
            if self.max_length is not None and len(value) > self.max_length:
                return f"字段 '{self.field_name}' 的长度不能大于 {self.max_length}"
        
        # 检查模式
        if self.pattern is not None and isinstance(value, str):
            import re
            if not re.match(self.pattern, value):
                return f"字段 '{self.field_name}' 的值不符合模式 '{self.pattern}'"
        
        # 检查可选值
        if self.choices is not None and value not in self.choices:
            return f"字段 '{self.field_name}' 的值必须是以下之一: {', '.join(map(str, self.choices))}"
        
        # 自定义验证
        if self.custom_validator is not None:
            try:
                if not self.custom_validator(value):
                    return self.error_message or f"字段 '{self.field_name}' 验证失败"
            except Exception as e:
                return f"字段 '{self.field_name}' 验证时出错: {str(e)}"
        
        return None


class SchemaValidator:
    """
    模式验证器
    
    用于验证参数字典是否符合预定义的模式。
    """
    
    def __init__(self, schema: Dict[str, Dict[str, Any]]):
        """
        初始化模式验证器
        
        Args:
            schema: 参数模式，包含字段名称和验证规则
        """
        self.schema = schema
        self.validators = {}
        
        # 为每个字段创建验证器
        for field_name, field_schema in schema.items():
            self.validators[field_name] = FieldValidator(
                field_name=field_name,
                required=field_schema.get('required', False),
                field_type=self._get_type(field_schema.get('type')),
                min_value=field_schema.get('min_value'),
                max_value=field_schema.get('max_value'),
                min_length=field_schema.get('min_length'),
                max_length=field_schema.get('max_length'),
                pattern=field_schema.get('pattern'),
                choices=field_schema.get('choices'),
                custom_validator=field_schema.get('validator'),
                error_message=field_schema.get('error_message')
            )
    
    def _get_type(self, type_name: Optional[str]) -> Optional[type]:
        """
        根据类型名称获取类型对象
        
        Args:
            type_name: 类型名称
            
        Returns:
            类型对象
        """
        if type_name is None:
            return None
        
        type_map = {
            'string': str,
            'str': str,
            'integer': int,
            'int': int,
            'float': float,
            'number': (int, float),
            'boolean': bool,
            'bool': bool,
            'list': list,
            'array': list,
            'object': dict,
            'dict': dict
        }
        
        return type_map.get(type_name.lower())
    
    def validate(self, params: Dict[str, Any]) -> Dict[str, str]:
        """
        验证参数字典
        
        Args:
            params: 要验证的参数字典
            
        Returns:
            验证错误的字典，键为字段名称，值为错误消息
        """
        errors = {}
        
        # 验证已提供的字段
        for field_name, value in params.items():
            if field_name in self.validators:
                error = self.validators[field_name].validate(value)
                if error:
                    errors[field_name] = error
        
        # 检查缺少的必需字段
        for field_name, validator in self.validators.items():
            if validator.required and field_name not in params:
                errors[field_name] = f"缺少必需字段 '{field_name}'"
        
        return errors
    
    def validate_and_raise(self, params: Dict[str, Any]) -> None:
        """
        验证参数字典，如果验证失败则抛出异常
        
        Args:
            params: 要验证的参数字典
            
        Raises:
            ValidationError: 如果验证失败
        """
        errors = self.validate(params)
        if errors:
            # 获取第一个错误
            field, message = next(iter(errors.items()))
            raise ValidationError(message, field)


def validate_params(params: Dict[str, Any], schema: Dict[str, Dict[str, Any]]) -> Dict[str, str]:
    """
    验证参数字典
    
    Args:
        params: 要验证的参数字典
        schema: 参数模式
        
    Returns:
        验证错误的字典，键为字段名称，值为错误消息
    """
    validator = SchemaValidator(schema)
    return validator.validate(params)


def validate_and_transform(params: Dict[str, Any], schema: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
    """
    验证并转换参数字典
    
    Args:
        params: 要验证的参数字典
        schema: 参数模式
        
    Returns:
        验证并转换后的参数字典
        
    Raises:
        ValidationError: 如果验证失败
    """
    # 创建验证器
    validator = SchemaValidator(schema)
    
    # 验证参数
    errors = validator.validate(params)
    if errors:
        # 获取第一个错误
        field, message = next(iter(errors.items()))
        raise ValidationError(message, field)
    
    # 转换参数
    result = {}
    for field_name, field_schema in schema.items():
        if field_name in params:
            # 获取值
            value = params[field_name]
            
            # 如果值为None且字段非必需，跳过
            if value is None and not field_schema.get('required', False):
                continue
            
            # 转换类型
            field_type = field_schema.get('type')
            if field_type is not None and value is not None:
                if field_type == 'int' or field_type == 'integer':
                    value = int(value)
                elif field_type == 'float':
                    value = float(value)
                elif field_type == 'boolean' or field_type == 'bool':
                    if isinstance(value, str):
                        value = value.lower() in ('true', 'yes', 'y', '1', 't')
            
            # 存储值
            result[field_name] = value
        elif field_schema.get('default') is not None:
            # 使用默认值
            result[field_name] = field_schema['default']
    
    return result


# 健身领域验证器模式

def create_fitness_goal_schema() -> Dict[str, Dict[str, Any]]:
    """
    创建健身目标验证模式
    
    Returns:
        健身目标验证模式
    """
    return {
        '健身目标': {
            'type': 'string',
            'required': True,
            'choices': ['减脂', '增肌', '塑形', '增强体能', '提高力量', '康复', '保持健康'],
            'error_message': '请选择有效的健身目标：减脂、增肌、塑形、增强体能、提高力量、康复或保持健康'
        },
        '健身水平': {
            'type': 'string',
            'required': True,
            'choices': ['初学者', '初级', '中级', '高级', '专业'],
            'error_message': '请选择有效的健身水平：初学者、初级、中级、高级或专业'
        },
        '年龄': {
            'type': 'integer',
            'min_value': 12,
            'max_value': 100,
            'error_message': '请提供有效的年龄（12-100岁）'
        },
        '性别': {
            'type': 'string',
            'choices': ['男', '女', '其他'],
            'error_message': '请选择有效的性别：男、女或其他'
        },
        '身高': {
            'type': 'float',
            'min_value': 100,
            'max_value': 250,
            'error_message': '请提供有效的身高（100-250厘米）'
        },
        '体重': {
            'type': 'float',
            'min_value': 30,
            'max_value': 300,
            'error_message': '请提供有效的体重（30-300公斤）'
        },
        '健身频率': {
            'type': 'string',
            'choices': ['每天', '每周1-2次', '每周3-4次', '每周5-6次', '每月几次', '不定期'],
            'error_message': '请选择有效的健身频率：每天、每周1-2次、每周3-4次、每周5-6次、每月几次或不定期'
        },
        '健身时长': {
            'type': 'integer',
            'min_value': 15,
            'max_value': 180,
            'error_message': '请提供有效的健身时长（15-180分钟）'
        },
        '特殊需求': {
            'type': 'string',
            'max_length': 500,
            'error_message': '特殊需求字段过长（最多500个字符）'
        }
    }


def create_exercise_schema() -> Dict[str, Dict[str, Any]]:
    """
    创建运动动作验证模式
    
    Returns:
        运动动作验证模式
    """
    return {
        '动作名称': {
            'type': 'string',
            'required': True,
            'min_length': 2,
            'max_length': 50,
            'error_message': '请提供有效的动作名称（2-50个字符）'
        },
        '部位': {
            'type': 'string',
            'required': True,
            'choices': ['胸部', '背部', '肩部', '手臂', '腿部', '臀部', '核心', '全身'],
            'error_message': '请选择有效的锻炼部位：胸部、背部、肩部、手臂、腿部、臀部、核心或全身'
        },
        '重量': {
            'type': 'float',
            'min_value': 0,
            'max_value': 500,
            'error_message': '请提供有效的重量（0-500公斤）'
        },
        '组数': {
            'type': 'integer',
            'min_value': 1,
            'max_value': 20,
            'error_message': '请提供有效的组数（1-20组）'
        },
        '次数': {
            'type': 'integer',
            'min_value': 1,
            'max_value': 100,
            'error_message': '请提供有效的次数（1-100次）'
        },
        '时间': {
            'type': 'integer',
            'min_value': 5,
            'max_value': 300,
            'error_message': '请提供有效的时间（5-300秒）'
        },
        '难度': {
            'type': 'string',
            'choices': ['初级', '中级', '高级'],
            'error_message': '请选择有效的难度：初级、中级或高级'
        }
    }


def create_training_plan_schema() -> Dict[str, Dict[str, Any]]:
    """
    创建训练计划验证模式
    
    Returns:
        训练计划验证模式
    """
    return {
        '计划名称': {
            'type': 'string',
            'required': True,
            'min_length': 2,
            'max_length': 50,
            'error_message': '请提供有效的计划名称（2-50个字符）'
        },
        '目标': {
            'type': 'string',
            'required': True,
            'choices': ['减脂', '增肌', '塑形', '增强体能', '提高力量', '康复', '保持健康'],
            'error_message': '请选择有效的训练目标：减脂、增肌、塑形、增强体能、提高力量、康复或保持健康'
        },
        '难度': {
            'type': 'string',
            'required': True,
            'choices': ['初级', '中级', '高级'],
            'error_message': '请选择有效的难度：初级、中级或高级'
        },
        '周期': {
            'type': 'integer',
            'min_value': 1,
            'max_value': 12,
            'error_message': '请提供有效的周期（1-12周）'
        },
        '每周天数': {
            'type': 'integer',
            'min_value': 1,
            'max_value': 7,
            'error_message': '请提供有效的每周训练天数（1-7天）'
        },
        '每次时长': {
            'type': 'integer',
            'min_value': 15,
            'max_value': 180,
            'error_message': '请提供有效的每次训练时长（15-180分钟）'
        },
        '训练部位': {
            'type': 'list',
            'choices': ['胸部', '背部', '肩部', '手臂', '腿部', '臀部', '核心', '全身'],
            'error_message': '请选择有效的训练部位：胸部、背部、肩部、手臂、腿部、臀部、核心或全身'
        }
    }


def create_diet_schema() -> Dict[str, Dict[str, Any]]:
    """
    创建饮食建议验证模式
    
    Returns:
        饮食建议验证模式
    """
    return {
        '饮食目标': {
            'type': 'string',
            'required': True,
            'choices': ['减脂', '增肌', '维持体重', '提高能量', '改善健康'],
            'error_message': '请选择有效的饮食目标：减脂、增肌、维持体重、提高能量或改善健康'
        },
        '目前体重': {
            'type': 'float',
            'min_value': 30,
            'max_value': 300,
            'error_message': '请提供有效的体重（30-300公斤）'
        },
        '目标体重': {
            'type': 'float',
            'min_value': 30,
            'max_value': 300,
            'error_message': '请提供有效的目标体重（30-300公斤）'
        },
        '活动水平': {
            'type': 'string',
            'choices': ['久坐不动', '轻度活动', '中度活动', '高度活动', '非常活跃'],
            'error_message': '请选择有效的活动水平：久坐不动、轻度活动、中度活动、高度活动或非常活跃'
        },
        '饮食习惯': {
            'type': 'string',
            'choices': ['无特殊要求', '素食', '纯素', '低碳水', '高蛋白', '生酮饮食', '间歇性断食'],
            'error_message': '请选择有效的饮食习惯：无特殊要求、素食、纯素、低碳水、高蛋白、生酮饮食或间歇性断食'
        },
        '食物过敏': {
            'type': 'list',
            'error_message': '请提供有效的食物过敏信息'
        },
        '每日热量': {
            'type': 'integer',
            'min_value': 1000,
            'max_value': 5000,
            'error_message': '请提供有效的每日热量目标（1000-5000卡路里）'
        }
    }


# 创建常用验证模式
fitness_goal_schema = create_fitness_goal_schema()
exercise_schema = create_exercise_schema()
training_plan_schema = create_training_plan_schema()
diet_schema = create_diet_schema() 