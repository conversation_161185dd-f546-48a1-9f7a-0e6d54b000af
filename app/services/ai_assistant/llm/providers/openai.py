"""
OpenAI大模型的LLM代理实现

这个模块提供了与OpenAI大模型交互的具体实现，支持同步/异步调用和流式输出。
"""
from typing import Dict, List, Any, Optional, Union, AsyncGenerator
import logging
import json
import time
import asyncio
from app.services.ai_assistant.llm.proxy import BaseLLMProxy
from app.core.config import settings

# 尝试导入所需的依赖
try:
    import openai
    from openai import AsyncOpenAI, OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    from langchain.chat_models import ChatOpenAI
    from langchain.schema import HumanMessage, SystemMessage, AIMessage
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False

logger = logging.getLogger(__name__)

class OpenAILLMProxy(BaseLLMProxy):
    """OpenAI大模型的代理实现"""
    
    def __init__(self):
        """初始化OpenAI大模型代理"""
        self.api_key = getattr(settings, "OPENAI_API_KEY", None)
        self.api_base = getattr(settings, "OPENAI_API_BASE", "https://api.openai.com/v1")
        
        self.model_mapping = {
            # 默认模型映射
            "default": getattr(settings, "OPENAI_MODEL", "gpt-3.5-turbo"),
            # 根据不同任务的模型映射
            "agent-app": getattr(settings, "OPENAI_AGENT_MODEL", "gpt-4"),
            "intent-recognition-app": getattr(settings, "OPENAI_INTENT_MODEL", "gpt-3.5-turbo"),
            "fitness_advice": getattr(settings, "OPENAI_FITNESS_MODEL", "gpt-4"),
            "exercise-generation-app": getattr(settings, "OPENAI_EXERCISE_MODEL", "gpt-4"),
        }
        
        # 检查API密钥是否可用
        if not self.api_key and OPENAI_AVAILABLE:
            logger.warning("OPENAI_API_KEY not set, some features may not work")
        
        # 设置默认参数
        self.default_params = {
            "temperature": 0.7,
            "max_tokens": 1500,
        }
        
        # 初始化客户端
        if OPENAI_AVAILABLE and self.api_key:
            self.client = OpenAI(api_key=self.api_key, base_url=self.api_base)
            self.async_client = AsyncOpenAI(api_key=self.api_key, base_url=self.api_base)
    
    def _get_model_name(self, model: Optional[str] = None) -> str:
        """
        获取模型名称
        
        Args:
            model: 模型标识符，可以是模型名称或预定义的别名
            
        Returns:
            实际的模型名称
        """
        if model is None:
            return self.model_mapping["default"]
        
        if model in self.model_mapping:
            return self.model_mapping[model]
        
        return model
    
    def generate_text(self, prompt: str, model: Optional[str] = None, **kwargs) -> str:
        """
        生成文本（同步方法）
        
        Args:
            prompt: 提示文本
            model: 模型名称或别名
            **kwargs: 其他参数，如temperature、max_tokens等
            
        Returns:
            生成的文本
        """
        if not OPENAI_AVAILABLE:
            raise ImportError("openai is not installed")
        
        if not self.api_key:
            raise ValueError("OpenAI API key is not set")
        
        model_name = self._get_model_name(model)
        
        # 合并默认参数和自定义参数
        params = {**self.default_params, **kwargs}
        
        try:
            start_time = time.time()
            messages = [{"role": "user", "content": prompt}]
            
            response = self.client.chat.completions.create(
                model=model_name,
                messages=messages,
                temperature=params.get("temperature"),
                max_tokens=params.get("max_tokens"),
            )
            
            elapsed_time = time.time() - start_time
            
            logger.debug(f"Generated text with model {model_name} in {elapsed_time:.2f}s")
            return response.choices[0].message.content
        
        except Exception as e:
            logger.error(f"Exception in generate_text: {str(e)}")
            return f"Error: {str(e)}"
    
    async def agenerate_text(self, prompt: str, model: Optional[str] = None, **kwargs) -> str:
        """
        生成文本（异步方法）
        
        Args:
            prompt: 提示文本
            model: 模型名称或别名
            **kwargs: 其他参数，如temperature、max_tokens等
            
        Returns:
            生成的文本
        """
        if not OPENAI_AVAILABLE:
            raise ImportError("openai is not installed")
        
        if not self.api_key:
            raise ValueError("OpenAI API key is not set")
        
        model_name = self._get_model_name(model)
        
        # 合并默认参数和自定义参数
        params = {**self.default_params, **kwargs}
        
        try:
            start_time = time.time()
            messages = [{"role": "user", "content": prompt}]
            
            response = await self.async_client.chat.completions.create(
                model=model_name,
                messages=messages,
                temperature=params.get("temperature"),
                max_tokens=params.get("max_tokens"),
            )
            
            elapsed_time = time.time() - start_time
            
            logger.debug(f"Generated text with model {model_name} in {elapsed_time:.2f}s")
            return response.choices[0].message.content
        
        except Exception as e:
            logger.error(f"Exception in agenerate_text: {str(e)}")
            return f"Error: {str(e)}"
    
    def get_chat_response(self, messages: List[Dict[str, str]], model: Optional[str] = None, **kwargs) -> str:
        """
        获取聊天响应（同步方法）
        
        Args:
            messages: 消息列表，格式为[{"role": "user", "content": "你好"}, ...]
            model: 模型名称或别名
            **kwargs: 其他参数，如temperature、max_tokens等
            
        Returns:
            生成的响应文本
        """
        if not OPENAI_AVAILABLE:
            raise ImportError("openai is not installed")
        
        if not self.api_key:
            raise ValueError("OpenAI API key is not set")
        
        model_name = self._get_model_name(model)
        
        # 合并默认参数和自定义参数
        params = {**self.default_params, **kwargs}
        
        try:
            start_time = time.time()
            
            response = self.client.chat.completions.create(
                model=model_name,
                messages=messages,
                temperature=params.get("temperature"),
                max_tokens=params.get("max_tokens"),
            )
            
            elapsed_time = time.time() - start_time
            
            logger.debug(f"Generated chat response with model {model_name} in {elapsed_time:.2f}s")
            return response.choices[0].message.content
        
        except Exception as e:
            logger.error(f"Exception in get_chat_response: {str(e)}")
            return f"Error: {str(e)}"
    
    async def aget_chat_response(self, messages: List[Dict[str, str]], model: Optional[str] = None, **kwargs) -> str:
        """
        获取聊天响应（异步方法）
        
        Args:
            messages: 消息列表，格式为[{"role": "user", "content": "你好"}, ...]
            model: 模型名称或别名
            **kwargs: 其他参数，如temperature、max_tokens等
            
        Returns:
            生成的响应文本
        """
        if not OPENAI_AVAILABLE:
            raise ImportError("openai is not installed")
        
        if not self.api_key:
            raise ValueError("OpenAI API key is not set")
        
        model_name = self._get_model_name(model)
        
        # 合并默认参数和自定义参数
        params = {**self.default_params, **kwargs}
        
        try:
            start_time = time.time()
            
            response = await self.async_client.chat.completions.create(
                model=model_name,
                messages=messages,
                temperature=params.get("temperature"),
                max_tokens=params.get("max_tokens"),
            )
            
            elapsed_time = time.time() - start_time
            
            logger.debug(f"Generated chat response with model {model_name} in {elapsed_time:.2f}s")
            return response.choices[0].message.content
        
        except Exception as e:
            logger.error(f"Exception in aget_chat_response: {str(e)}")
            return f"Error: {str(e)}"
    
    async def stream_chat(self, messages: List[Dict[str, str]], model: Optional[str] = None, **kwargs) -> AsyncGenerator[str, None]:
        """
        流式获取聊天响应（异步生成器）
        
        Args:
            messages: 消息列表，格式为[{"role": "user", "content": "你好"}, ...]
            model: 模型名称或别名
            **kwargs: 其他参数，如temperature、max_tokens等
            
        Yields:
            生成的响应文本片段
        """
        if not OPENAI_AVAILABLE:
            raise ImportError("openai is not installed")
        
        if not self.api_key:
            raise ValueError("OpenAI API key is not set")
        
        model_name = self._get_model_name(model)
        
        # 合并默认参数和自定义参数
        params = {**self.default_params, **kwargs}
        
        try:
            response = await self.async_client.chat.completions.create(
                model=model_name,
                messages=messages,
                temperature=params.get("temperature"),
                max_tokens=params.get("max_tokens"),
                stream=True,
            )
            
            async for chunk in response:
                if chunk.choices and chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
        
        except Exception as e:
            logger.error(f"Exception in stream_chat: {str(e)}")
            yield f"Error: {str(e)}"
    
    def get_llm(self, model: Optional[str] = None, **kwargs) -> Any:
        """
        获取LLM实例，用于与LangChain集成
        
        Args:
            model: 模型名称或别名
            **kwargs: 其他参数，如temperature、max_tokens等
            
        Returns:
            LLM实例
        """
        if not LANGCHAIN_AVAILABLE:
            raise ImportError("langchain is not installed")
        
        model_name = self._get_model_name(model)
        
        # 合并默认参数和自定义参数
        params = {**self.default_params, **kwargs}
        
        # 创建ChatOpenAI实例
        return ChatOpenAI(
            model_name=model_name,
            temperature=params.get("temperature", 0.7),
            max_tokens=params.get("max_tokens", 1500),
            openai_api_key=self.api_key,
            openai_api_base=self.api_base,
        ) 