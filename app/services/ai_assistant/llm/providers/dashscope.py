"""
DashScope大模型的LLM代理实现

这个模块提供了与DashScope大模型交互的具体实现，支持同步/异步调用和流式输出。
"""
from typing import Dict, List, Any, Optional, Union, AsyncGenerator
import logging
import json
import time
import asyncio
from app.services.ai_assistant.llm.proxy import BaseLLMProxy
from app.core.config import settings

# 尝试导入所需的依赖
try:
    import dashscope
    DASHSCOPE_AVAILABLE = True
except ImportError:
    DASHSCOPE_AVAILABLE = False

try:
    from langchain.chat_models import ChatOpenAI
    from langchain.schema import HumanMessage, SystemMessage, AIMessage
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False

logger = logging.getLogger(__name__)

class DashscopeLLMProxy(BaseLLMProxy):
    """DashScope大模型的代理实现"""
    
    def __init__(self):
        """初始化DashScope大模型代理"""
        self.api_key = getattr(settings, "DASHSCOPE_API_KEY", None)
        self.model_mapping = {
            # 默认模型映射
            "default": getattr(settings, "DASHSCOPE_MODEL", "qwen-plus"),
            # 根据不同任务的模型映射
            "agent-app": getattr(settings, "DASHSCOPE_AGENT_MODEL", "qwen-plus"),
            "intent-recognition-app": getattr(settings, "DASHSCOPE_INTENT_MODEL", "qwen-turbo"),
            "fitness_advice": getattr(settings, "DASHSCOPE_FITNESS_MODEL", "qwen-plus"),
            "exercise-generation-app": getattr(settings, "DASHSCOPE_EXERCISE_MODEL", "qwen-plus"),
        }
        # 检查API密钥是否可用
        if not self.api_key and DASHSCOPE_AVAILABLE:
            logger.warning("DASHSCOPE_API_KEY not set, some features may not work")
        
        # 设置默认参数
        self.default_params = {
            "temperature": 0.7,
            "max_tokens": 1500,
        }
    
    def _get_model_name(self, model: Optional[str] = None) -> str:
        """
        获取模型名称
        
        Args:
            model: 模型标识符，可以是模型名称或预定义的别名
            
        Returns:
            实际的模型名称
        """
        if model is None:
            return self.model_mapping["default"]
        
        if model in self.model_mapping:
            return self.model_mapping[model]
        
        return model
    
    def generate_text(self, prompt: str, model: Optional[str] = None, **kwargs) -> str:
        """
        生成文本（同步方法）
        
        Args:
            prompt: 提示文本
            model: 模型名称或别名
            **kwargs: 其他参数，如temperature、max_tokens等
            
        Returns:
            生成的文本
        """
        if not DASHSCOPE_AVAILABLE:
            raise ImportError("dashscope is not installed")
        
        model_name = self._get_model_name(model)
        
        # 合并默认参数和自定义参数
        params = {**self.default_params, **kwargs}
        
        try:
            start_time = time.time()
            response = dashscope.Generation.call(
                model=model_name,
                prompt=prompt,
                api_key=self.api_key,
                temperature=params.get("temperature"),
                max_tokens=params.get("max_tokens"),
            )
            elapsed_time = time.time() - start_time
            
            if response.status_code == 200:
                logger.debug(f"Generated text with model {model_name} in {elapsed_time:.2f}s")
                return response.output.text
            else:
                logger.error(f"Error generating text: {response.code} - {response.message}")
                return f"Error: {response.message}"
        
        except Exception as e:
            logger.error(f"Exception in generate_text: {str(e)}")
            return f"Error: {str(e)}"
    
    async def agenerate_text(self, prompt: str, model: Optional[str] = None, **kwargs) -> str:
        """
        生成文本（异步方法）
        
        Args:
            prompt: 提示文本
            model: 模型名称或别名
            **kwargs: 其他参数，如temperature、max_tokens等
            
        Returns:
            生成的文本
        """
        # 使用线程池执行同步方法
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None, self.generate_text, prompt, model, **kwargs
        )
    
    def get_chat_response(self, messages: List[Dict[str, str]], model: Optional[str] = None, **kwargs) -> str:
        """
        获取聊天响应（同步方法）
        
        Args:
            messages: 消息列表，格式为[{"role": "user", "content": "你好"}, ...]
            model: 模型名称或别名
            **kwargs: 其他参数，如temperature、max_tokens等
            
        Returns:
            生成的响应文本
        """
        if not DASHSCOPE_AVAILABLE:
            raise ImportError("dashscope is not installed")
        
        model_name = self._get_model_name(model)
        
        # 合并默认参数和自定义参数
        params = {**self.default_params, **kwargs}
        
        try:
            start_time = time.time()
            response = dashscope.Generation.call(
                model=model_name,
                messages=messages,
                api_key=self.api_key,
                temperature=params.get("temperature"),
                max_tokens=params.get("max_tokens"),
            )
            elapsed_time = time.time() - start_time
            
            if response.status_code == 200:
                logger.debug(f"Generated chat response with model {model_name} in {elapsed_time:.2f}s")
                return response.output.text
            else:
                logger.error(f"Error generating chat response: {response.code} - {response.message}")
                return f"Error: {response.message}"
        
        except Exception as e:
            logger.error(f"Exception in get_chat_response: {str(e)}")
            return f"Error: {str(e)}"
    
    async def aget_chat_response(self, messages: List[Dict[str, str]], model: Optional[str] = None, **kwargs) -> str:
        """
        获取聊天响应（异步方法）
        
        Args:
            messages: 消息列表，格式为[{"role": "user", "content": "你好"}, ...]
            model: 模型名称或别名
            **kwargs: 其他参数，如temperature、max_tokens等
            
        Returns:
            生成的响应文本
        """
        # 使用线程池执行同步方法
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None, self.get_chat_response, messages, model, **kwargs
        )
    
    async def stream_chat(self, messages: List[Dict[str, str]], model: Optional[str] = None, **kwargs) -> AsyncGenerator[str, None]:
        """
        流式获取聊天响应（异步生成器）
        
        Args:
            messages: 消息列表，格式为[{"role": "user", "content": "你好"}, ...]
            model: 模型名称或别名
            **kwargs: 其他参数，如temperature、max_tokens等
            
        Yields:
            生成的响应文本片段
        """
        if not DASHSCOPE_AVAILABLE:
            raise ImportError("dashscope is not installed")
        
        model_name = self._get_model_name(model)
        
        # 合并默认参数和自定义参数
        params = {**self.default_params, **kwargs}
        
        try:
            response = await dashscope.Generation.async_call(
                model=model_name,
                messages=messages,
                api_key=self.api_key,
                temperature=params.get("temperature"),
                max_tokens=params.get("max_tokens"),
                stream=True,
            )
            
            async for chunk in response:
                if chunk.status_code == 200:
                    # 确保输出的是文本，有些模型可能返回不同格式
                    if hasattr(chunk.output, 'text') and chunk.output.text:
                        yield chunk.output.text
                else:
                    logger.error(f"Error in stream_chat: {chunk.code} - {chunk.message}")
                    yield f"Error: {chunk.message}"
        
        except Exception as e:
            logger.error(f"Exception in stream_chat: {str(e)}")
            yield f"Error: {str(e)}"
    
    def get_llm(self, model: Optional[str] = None, **kwargs) -> Any:
        """
        获取LLM实例，用于与LangChain集成
        
        Args:
            model: 模型名称或别名
            **kwargs: 其他参数，如temperature、max_tokens等
            
        Returns:
            LLM实例
        """
        if not LANGCHAIN_AVAILABLE:
            raise ImportError("langchain is not installed")
        
        model_name = self._get_model_name(model)
        
        # 合并默认参数和自定义参数
        params = {**self.default_params, **kwargs}
        
        # 创建ChatOpenAI实例，使用DashScope API兼容模式
        return ChatOpenAI(
            model_name=model_name,
            temperature=params.get("temperature", 0.7),
            max_tokens=params.get("max_tokens", 1500),
            openai_api_key=self.api_key,
            openai_api_base="https://api.dashscope.com/v1",
        ) 