"""
百炼应用LLM代理模块

该模块提供了针对阿里云百炼应用的LLM代理实现。
"""

import logging
import json
import asyncio
from typing import Dict, Any, List, Optional, Union

import aiohttp
from app.core.chat_config import PROVIDERS, BAILIAN_APPS
from app.services.ai_assistant.llm.proxy import LLMProxy
from app.services.ai_assistant.common.cache import CacheService, default_cache_service

logger = logging.getLogger(__name__)

class BailianLLMProxy(LLMProxy):
    """
    百炼应用LLM代理
    
    专门用于调用阿里云百炼应用的LLM代理实现。
    支持健身教练、营养建议等专业领域的百炼应用。
    """
    
    def __init__(
        self, 
        app_type: str = "fitness-coach-app",
        cache_service: Optional[CacheService] = None
    ):
        """
        初始化百炼应用LLM代理
        
        Args:
            app_type: 应用类型，可选值: fitness-coach-app, nutrition-coach-app等
            cache_service: 缓存服务，如果不提供则使用默认缓存服务
        """
        self.app_type = app_type
        self.app_config = BAILIAN_APPS.get(app_type)
        if not self.app_config:
            raise ValueError(f"未找到应用配置: {app_type}")
        
        self.provider_config = PROVIDERS.get("bailian")
        if not self.provider_config:
            raise ValueError("未找到百炼提供商配置")
        
        self.api_key = self.provider_config["api_key"]
        self.api_base = self.provider_config["api_base"]
        self.app_id = self.app_config["app_id"]
        self.cache_service = cache_service or default_cache_service
        
        logger.info(f"百炼应用LLM代理初始化成功: {app_type}, 应用ID: {self.app_id}")
        
    async def _call_bailian_api(
        self, 
        request_data: Dict[str, Any], 
        stream: bool = False
    ) -> Dict[str, Any]:
        """
        调用百炼API
        
        Args:
            request_data: 请求数据
            stream: 是否使用流式输出
            
        Returns:
            API响应数据
        """
        try:
            # 构建请求头
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }
            
            # 添加流式输出请求头（如果需要）
            if stream and "stream_header" in self.provider_config:
                headers.update(self.provider_config["stream_header"])
            
            logger.info(f"调用百炼应用API: {self.app_type}, 请求大小: {len(str(request_data))}")
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.api_base}applications/completions",
                    headers=headers,
                    json=request_data,
                    timeout=30  # 设置30秒超时
                ) as response:
                    # 检查响应状态
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"百炼API错误 [{response.status}]: {error_text}")
                        raise ValueError(f"调用百炼应用时出错: HTTP {response.status}")
                    
                    # 解析响应
                    response_data = await response.json()
                    
                    # 检查响应数据
                    if "result" not in response_data:
                        logger.error(f"百炼API返回异常数据: {response_data}")
                        raise ValueError(f"百炼应用返回异常数据: {json.dumps(response_data)[:100]}...")
                    
                    return response_data
                    
        except aiohttp.ClientError as e:
            logger.error(f"百炼API网络错误: {str(e)}")
            raise ConnectionError(f"调用百炼应用时网络错误: {str(e)}")
        except asyncio.TimeoutError:
            logger.error("百炼API请求超时")
            raise TimeoutError("调用百炼应用时请求超时，请稍后再试")
        except Exception as e:
            logger.error(f"调用百炼应用时出错: {str(e)}")
            raise
        
    async def chat(
        self, 
        system_prompt: str, 
        user_message: str, 
        chat_history: Optional[List[Dict[str, str]]] = None,
        temperature: float = 0.7,
        max_tokens: int = 1024,
        model: Optional[str] = None,
        use_cache: bool = True,
        **kwargs
    ) -> str:
        """
        调用百炼应用进行对话
        
        Args:
            system_prompt: 系统提示
            user_message: 用户消息
            chat_history: 聊天历史
            temperature: 温度参数，控制随机性
            max_tokens: 最大生成token数
            model: 模型名称（对百炼应用无效）
            use_cache: 是否使用缓存
            **kwargs: 其他参数
            
        Returns:
            助手回复文本
        """
        # 缓存处理
        if use_cache and self.cache_service:
            # 构建缓存键
            cache_key = f"bailian:{self.app_type}:chat:{system_prompt}:{user_message}"
            if chat_history:
                # 添加最近3轮对话历史到缓存键
                recent_history = chat_history[-3:] if len(chat_history) > 3 else chat_history
                history_str = json.dumps(recent_history, sort_keys=True)
                cache_key += f":{history_str}"
            
            cached_response = await self.cache_service.get(cache_key)
            if cached_response is not None:
                logger.debug(f"百炼应用缓存命中: {user_message[:30]}...")
                return cached_response
        
        # 准备请求数据
        history = chat_history or []
        messages = []
        
        # 添加系统提示
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        
        # 添加历史消息
        for msg in history:
            role = msg.get("role", "user")
            content = msg.get("content", "")
            if role and content:
                messages.append({"role": role, "content": content})
        
        # 添加当前用户消息
        messages.append({"role": "user", "content": user_message})
        
        # 构建请求体
        request_data = {
            "app_id": self.app_id,
            "parameters": {
                "temperature": temperature,
                "max_tokens": max_tokens
            },
            "messages": messages
        }
        
        try:
            # 调用百炼API
            stream = kwargs.get("stream", False)
            response_data = await self._call_bailian_api(request_data, stream)
            
            # 提取回复文本
            ai_response = response_data.get("result", {}).get("output", {}).get("text", "")
            
            if not ai_response:
                logger.warning(f"百炼API返回空回复: {response_data}")
                ai_response = "抱歉，我无法生成有效回复。请稍后再试。"
            
            # 缓存响应
            if use_cache and self.cache_service and ai_response:
                await self.cache_service.set(cache_key, ai_response, ttl=3600)
            
            logger.debug(f"百炼应用回复: {ai_response[:50]}...")
            return ai_response
                    
        except Exception as e:
            logger.error(f"调用百炼应用时出错: {str(e)}")
            return f"调用百炼应用时出错: {str(e)}"
    
    async def generate_text(
        self,
        prompt: str,
        temperature: float = 0.7,
        max_tokens: int = 1024,
        model: Optional[str] = None,
        use_cache: bool = True,
        **kwargs
    ) -> str:
        """
        生成文本
        
        Args:
            prompt: 提示文本
            temperature: 温度参数，控制随机性
            max_tokens: 最大生成token数
            model: 模型名称（对百炼应用无效）
            use_cache: 是否使用缓存
            **kwargs: 其他参数
            
        Returns:
            生成的文本
        """
        return await self.chat(
            system_prompt="",
            user_message=prompt,
            temperature=temperature,
            max_tokens=max_tokens,
            use_cache=use_cache,
            **kwargs
        )
    
    async def get_embeddings(
        self,
        texts: Union[str, List[str]],
        model: Optional[str] = None,
        use_cache: bool = True,
        **kwargs
    ) -> List[List[float]]:
        """
        获取文本的嵌入向量
        
        注意：百炼应用不直接支持生成嵌入向量，此方法返回模拟向量。
        
        Args:
            texts: 文本或文本列表
            model: 模型名称（对百炼应用无效）
            use_cache: 是否使用缓存
            **kwargs: 其他参数
            
        Returns:
            嵌入向量列表
        """
        import random
        
        # 确保texts是列表
        if isinstance(texts, str):
            texts = [texts]
        
        logger.warning("百炼应用不支持生成嵌入向量，返回模拟向量")
        
        # 为每个文本生成稳定的伪随机向量（基于文本内容）
        results = []
        for text in texts:
            # 使用文本的哈希值作为随机种子，确保相同文本生成相同向量
            random.seed(hash(text))
            vector = [random.uniform(-1, 1) for _ in range(1536)]
            results.append(vector)
        
        return results 