"""
LLM提供商工厂模块

这个模块提供了用于创建和管理不同LLM提供商实例的工厂类。
根据配置或请求参数动态选择合适的LLM提供商。
"""
import logging
from typing import Dict, Type, Optional, Any
from app.services.ai_assistant.llm.proxy import LLMProxy
from app.core.config import settings

logger = logging.getLogger(__name__)

class LLMProxyFactory:
    """LLM提供商工厂类，用于创建和管理不同的LLM提供商实例"""
    
    _providers: Dict[str, Type[LLMProxy]] = {}
    _instances: Dict[str, LLMProxy] = {}
    _default_provider: str = "qwen"  # 将默认提供商改为qwen
    
    @classmethod
    def register_provider(cls, name: str, provider_class: Type[LLMProxy]) -> None:
        """
        注册LLM提供商
        
        Args:
            name: 提供商名称
            provider_class: 提供商类
        """
        cls._providers[name] = provider_class
        logger.info(f"Registered LLM provider: {name}")
    
    @classmethod
    def set_default_provider(cls, name: str) -> None:
        """
        设置默认LLM提供商
        
        Args:
            name: 提供商名称
        """
        if name in cls._providers:
            cls._default_provider = name
            logger.info(f"Set default LLM provider to: {name}")
        else:
            logger.warning(f"Cannot set default provider to {name}, provider not registered")
    
    @classmethod
    def get_provider(cls, name: Optional[str] = None) -> LLMProxy:
        """
        获取LLM提供商实例
        
        Args:
            name: 提供商名称，如果为None则使用默认提供商
            
        Returns:
            LLM提供商实例
            
        Raises:
            ValueError: 如果指定的提供商不存在
        """
        # 如果未指定名称，使用默认提供商
        if name is None:
            name = cls._default_provider
        
        # 如果提供商不存在，尝试加载
        if name not in cls._instances:
            if name not in cls._providers:
                available_providers = list(cls._providers.keys())
                raise ValueError(f"LLM provider '{name}' not registered. Available providers: {available_providers}")
            
            try:
                cls._instances[name] = cls._providers[name]()
                logger.debug(f"Created LLM provider instance: {name}")
            except Exception as e:
                logger.error(f"Failed to create LLM provider '{name}': {str(e)}")
                raise
        
        return cls._instances[name]
    
    @classmethod
    def load_providers(cls) -> None:
        """
        加载所有可用的LLM提供商
        
        根据配置加载不同的LLM提供商，并设置默认提供商。
        """
        # 尝试导入可用的提供商
        # 首先尝试加载Qwen提供商
        try:
            from app.services.ai_assistant.llm.providers.qwen_proxy import QwenLLMProxy
            cls.register_provider("qwen", QwenLLMProxy)
        except ImportError:
            logger.warning("Qwen provider not available")
            
        # 尝试导入其他提供商
        try:
            from app.services.ai_assistant.llm.providers.openai import OpenAILLMProxy
            cls.register_provider("openai", OpenAILLMProxy)
        except ImportError:
            logger.warning("OpenAI provider not available")
        
        try:
            from app.services.ai_assistant.llm.providers.dashscope import DashscopeLLMProxy
            cls.register_provider("dashscope", DashscopeLLMProxy)
        except ImportError:
            logger.warning("DashScope provider not available")
        
        try:
            from app.services.ai_assistant.llm.providers.tongyi import TongyiLLMProxy
            cls.register_provider("tongyi", TongyiLLMProxy)
        except ImportError:
            logger.warning("Tongyi provider not available")
        
        # 从配置中获取默认提供商，优先使用qwen
        default_provider = getattr(settings, "LLM_PROVIDER", "qwen")
        if default_provider in cls._providers:
            cls.set_default_provider(default_provider)
        else:
            logger.warning(f"Default LLM provider '{default_provider}' not available, using qwen or first available provider")
            if "qwen" in cls._providers:
                cls.set_default_provider("qwen")
            elif cls._providers:
                first_provider = next(iter(cls._providers.keys()))
                cls.set_default_provider(first_provider)
            else:
                logger.error("No LLM providers available")
    
    @classmethod
    def list_available_providers(cls) -> Dict[str, str]:
        """
        列出所有可用的LLM提供商
        
        Returns:
            提供商名称和描述的字典
        """
        result = {}
        for name, provider_class in cls._providers.items():
            result[name] = provider_class.__doc__ or "No description available"
        return result 