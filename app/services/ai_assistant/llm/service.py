"""
LLM服务模块

这个模块提供了一个统一的LLM服务接口，作为应用程序使用LLM功能的主要入口。
它封装了LLM提供商的选择和调用，提供了更高级的功能如上下文管理、提示模板等。
"""
import logging
from typing import Dict, List, Any, Optional, Union, AsyncGenerator
from app.services.ai_assistant.llm.factory import LLMProxyFactory
from app.services.ai_assistant.llm.proxy import LLMProxy
from app.core.config import settings

logger = logging.getLogger(__name__)

class LLMService:
    """
    LLM服务类，提供对大语言模型的统一访问接口
    
    该服务作为应用程序使用LLM功能的主要入口，封装了对不同提供商的选择和调用。
    它提供了更高级的功能如上下文管理、提示模板等。
    """
    
    def __init__(self):
        """初始化LLM服务"""
        # 加载所有可用的提供商
        LLMProxyFactory.load_providers()
        
        # 设置常用提示模板
        self.templates = {
            "fitness_advice": """作为一名专业健身教练，根据用户的健身水平、目标和限制条件，提供科学合理的健身建议。
用户信息：
- 健身水平: {fitness_level}
- 健身目标: {fitness_goal}
- 限制条件: {limitations}

请提供专业的健身建议：""",
            
            "exercise_generation": """请为用户设计一组针对{target_muscle}的训练动作。
用户信息：
- 健身水平: {fitness_level}
- 健身环境: {environment}
- 可用器材: {available_equipment}

要求：
1. 提供3-5个针对{target_muscle}的训练动作
2. 每个动作包含名称、组数、次数和详细说明
3. 动作应适合用户的健身水平和可用器材
4. 提供正确的动作姿势和注意事项
""",
            
            "intent_recognition": """分析以下用户输入，并确定用户的主要意图。
用户输入: "{user_input}"

可能的意图类别:
- 寻求健身建议
- 询问特定动作
- 制定训练计划
- 饮食咨询
- 健康状况咨询
- 闲聊

请仅返回一个最匹配的意图类别，不要添加其他解释。
"""
        }
    
    def _get_provider(self, provider: Optional[str] = None) -> LLMProxy:
        """
        获取LLM提供商实例
        
        Args:
            provider: 提供商名称，如果为None则使用默认提供商
            
        Returns:
            LLM提供商实例
        """
        try:
            return LLMProxyFactory.get_provider(provider)
        except ValueError as e:
            logger.error(f"Failed to get LLM provider: {str(e)}")
            # 回退到默认提供商
            return LLMProxyFactory.get_provider(None)
    
    def fill_template(self, template_name: str, **kwargs) -> str:
        """
        填充提示模板
        
        Args:
            template_name: 模板名称
            **kwargs: 模板参数
            
        Returns:
            填充后的提示文本
            
        Raises:
            ValueError: 如果模板不存在
        """
        if template_name not in self.templates:
            raise ValueError(f"Template '{template_name}' not found")
        
        try:
            return self.templates[template_name].format(**kwargs)
        except KeyError as e:
            logger.error(f"Missing template parameter: {str(e)}")
            raise ValueError(f"Missing template parameter: {str(e)}")
    
    def add_template(self, name: str, template: str) -> None:
        """
        添加提示模板
        
        Args:
            name: 模板名称
            template: 模板文本
        """
        self.templates[name] = template
        logger.debug(f"Added template: {name}")
    
    def generate_text(self, prompt: str, provider: Optional[str] = None, model: Optional[str] = None, **kwargs) -> str:
        """
        生成文本
        
        Args:
            prompt: 提示文本
            provider: 提供商名称
            model: 模型名称
            **kwargs: 其他参数
            
        Returns:
            生成的文本
        """
        llm_provider = self._get_provider(provider)
        return llm_provider.generate_text(prompt, model, **kwargs)
    
    async def agenerate_text(self, prompt: str, provider: Optional[str] = None, model: Optional[str] = None, **kwargs) -> str:
        """
        异步生成文本
        
        Args:
            prompt: 提示文本
            provider: 提供商名称
            model: 模型名称
            **kwargs: 其他参数
            
        Returns:
            生成的文本
        """
        llm_provider = self._get_provider(provider)
        return await llm_provider.agenerate_text(prompt, model, **kwargs)
    
    def get_chat_response(self, messages: List[Dict[str, str]], provider: Optional[str] = None, model: Optional[str] = None, **kwargs) -> str:
        """
        获取聊天响应
        
        Args:
            messages: 消息列表
            provider: 提供商名称
            model: 模型名称
            **kwargs: 其他参数
            
        Returns:
            生成的响应文本
        """
        llm_provider = self._get_provider(provider)
        return llm_provider.get_chat_response(messages, model, **kwargs)
    
    async def aget_chat_response(self, messages: List[Dict[str, str]], provider: Optional[str] = None, model: Optional[str] = None, **kwargs) -> str:
        """
        异步获取聊天响应
        
        Args:
            messages: 消息列表
            provider: 提供商名称
            model: 模型名称
            **kwargs: 其他参数
            
        Returns:
            生成的响应文本
        """
        llm_provider = self._get_provider(provider)
        return await llm_provider.aget_chat_response(messages, model, **kwargs)
    
    async def stream_chat(self, messages: List[Dict[str, str]], provider: Optional[str] = None, model: Optional[str] = None, **kwargs) -> AsyncGenerator[str, None]:
        """
        流式获取聊天响应
        
        Args:
            messages: 消息列表
            provider: 提供商名称
            model: 模型名称
            **kwargs: 其他参数
            
        Yields:
            生成的响应文本片段
        """
        llm_provider = self._get_provider(provider)
        async for chunk in llm_provider.stream_chat(messages, model, **kwargs):
            yield chunk
    
    def get_fitness_advice(self, fitness_level: str, fitness_goal: str, limitations: str, **kwargs) -> str:
        """
        获取健身建议
        
        Args:
            fitness_level: 健身水平
            fitness_goal: 健身目标
            limitations: 限制条件
            **kwargs: 其他参数
            
        Returns:
            健身建议
        """
        prompt = self.fill_template("fitness_advice", 
                                    fitness_level=fitness_level,
                                    fitness_goal=fitness_goal,
                                    limitations=limitations)
        
        # 使用特定模型和提供商
        provider = kwargs.pop("provider", None)
        model = kwargs.pop("model", "fitness_advice")
        
        return self.generate_text(prompt, provider, model, **kwargs)
    
    async def aget_fitness_advice(self, fitness_level: str, fitness_goal: str, limitations: str, **kwargs) -> str:
        """
        异步获取健身建议
        
        Args:
            fitness_level: 健身水平
            fitness_goal: 健身目标
            limitations: 限制条件
            **kwargs: 其他参数
            
        Returns:
            健身建议
        """
        prompt = self.fill_template("fitness_advice", 
                                    fitness_level=fitness_level,
                                    fitness_goal=fitness_goal,
                                    limitations=limitations)
        
        # 使用特定模型和提供商
        provider = kwargs.pop("provider", None)
        model = kwargs.pop("model", "fitness_advice")
        
        return await self.agenerate_text(prompt, provider, model, **kwargs)
    
    def generate_exercises(self, target_muscle: str, fitness_level: str, environment: str, available_equipment: str, **kwargs) -> str:
        """
        生成训练动作
        
        Args:
            target_muscle: 目标肌肉
            fitness_level: 健身水平
            environment: 健身环境
            available_equipment: 可用器材
            **kwargs: 其他参数
            
        Returns:
            训练动作
        """
        prompt = self.fill_template("exercise_generation", 
                                    target_muscle=target_muscle,
                                    fitness_level=fitness_level,
                                    environment=environment,
                                    available_equipment=available_equipment)
        
        # 使用特定模型和提供商
        provider = kwargs.pop("provider", None)
        model = kwargs.pop("model", "exercise-generation-app")
        
        return self.generate_text(prompt, provider, model, **kwargs)
    
    async def agenerate_exercises(self, target_muscle: str, fitness_level: str, environment: str, available_equipment: str, **kwargs) -> str:
        """
        异步生成训练动作
        
        Args:
            target_muscle: 目标肌肉
            fitness_level: 健身水平
            environment: 健身环境
            available_equipment: 可用器材
            **kwargs: 其他参数
            
        Returns:
            训练动作
        """
        prompt = self.fill_template("exercise_generation", 
                                    target_muscle=target_muscle,
                                    fitness_level=fitness_level,
                                    environment=environment,
                                    available_equipment=available_equipment)
        
        # 使用特定模型和提供商
        provider = kwargs.pop("provider", None)
        model = kwargs.pop("model", "exercise-generation-app")
        
        return await self.agenerate_text(prompt, provider, model, **kwargs)
    
    def recognize_intent(self, user_input: str, **kwargs) -> str:
        """
        识别用户意图
        
        Args:
            user_input: 用户输入
            **kwargs: 其他参数
            
        Returns:
            识别的意图类别
        """
        prompt = self.fill_template("intent_recognition", user_input=user_input)
        
        # 使用特定模型和提供商
        provider = kwargs.pop("provider", None)
        model = kwargs.pop("model", "intent-recognition-app")
        
        return self.generate_text(prompt, provider, model, **kwargs)
    
    async def arecognize_intent(self, user_input: str, **kwargs) -> str:
        """
        异步识别用户意图
        
        Args:
            user_input: 用户输入
            **kwargs: 其他参数
            
        Returns:
            识别的意图类别
        """
        prompt = self.fill_template("intent_recognition", user_input=user_input)
        
        # 使用特定模型和提供商
        provider = kwargs.pop("provider", None)
        model = kwargs.pop("model", "intent-recognition-app")
        
        return await self.agenerate_text(prompt, provider, model, **kwargs)

# 创建单例实例
llm_service = LLMService() 