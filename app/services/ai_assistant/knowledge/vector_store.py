"""
向量数据库模块

该模块提供了基于FAISS的向量存储和检索功能，用于高效地检索相似文本。
"""

import os
import logging
import pickle
from typing import Dict, Any, List, Optional, Tuple, Union
import numpy as np

try:
    import faiss
except ImportError:
    logging.warning("FAISS库未安装，将使用简单向量检索实现")
    faiss = None

from app.services.ai_assistant.llm.proxy import LLMProxy, DefaultLLMProxy

logger = logging.getLogger(__name__)

class VectorStore:
    """
    向量数据库
    
    基于FAISS实现的向量存储和检索功能，用于高效地检索相似文本。
    如果FAISS不可用，将使用简单的向量检索实现。
    """
    
    def __init__(
        self,
        embedding_dim: int = 1536,  # OpenAI默认embedding维度
        index_type: str = "flat",
        llm_proxy: Optional[LLMProxy] = None,
        index_path: Optional[str] = None
    ):
        """
        初始化向量数据库
        
        Args:
            embedding_dim: 向量维度
            index_type: 索引类型，可选值为"flat"或"ivf_flat"
            llm_proxy: 语言模型代理，用于生成文本嵌入
            index_path: 索引文件路径，如果提供则从文件加载索引
        """
        self.embedding_dim = embedding_dim
        self.index_type = index_type
        self.llm_proxy = llm_proxy or DefaultLLMProxy()
        
        # 存储文档内容和元数据
        self.documents: List[Dict[str, Any]] = []
        # 文档ID到索引的映射
        self.id_to_index: Dict[str, int] = {}
        
        # 创建或加载索引
        if index_path and os.path.exists(index_path):
            self._load_index(index_path)
        else:
            self._create_index()
    
    def _create_index(self) -> None:
        """创建FAISS索引"""
        if faiss:
            if self.index_type == "flat":
                # 创建精确但较慢的平面索引
                self.index = faiss.IndexFlatL2(self.embedding_dim)
            elif self.index_type == "ivf_flat":
                # 创建近似但较快的IVF索引
                quantizer = faiss.IndexFlatL2(self.embedding_dim)
                self.index = faiss.IndexIVFFlat(quantizer, self.embedding_dim, 100)
                # 需要训练数据才能使用IVF索引
                self.is_trained = False
            else:
                # 默认使用平面索引
                self.index = faiss.IndexFlatL2(self.embedding_dim)
        else:
            # FAISS不可用时，使用简单实现
            self.index = SimpleVectorIndex(self.embedding_dim)
    
    def _load_index(self, index_path: str) -> None:
        """
        从文件加载索引
        
        Args:
            index_path: 索引文件路径
        """
        try:
            with open(index_path, 'rb') as f:
                data = pickle.load(f)
                
                if faiss:
                    self.index = faiss.deserialize_index(data['index_data'])
                else:
                    self.index = data['index']
                
                self.documents = data.get('documents', [])
                self.id_to_index = data.get('id_to_index', {})
                
                logger.info(f"已从{index_path}加载索引，包含{len(self.documents)}个文档")
        except Exception as e:
            logger.error(f"加载索引失败: {str(e)}")
            self._create_index()
    
    def save_index(self, index_path: str) -> bool:
        """
        保存索引到文件
        
        Args:
            index_path: 索引文件路径
            
        Returns:
            是否成功保存
        """
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(index_path), exist_ok=True)
            
            with open(index_path, 'wb') as f:
                data = {
                    'documents': self.documents,
                    'id_to_index': self.id_to_index
                }
                
                if faiss:
                    data['index_data'] = faiss.serialize_index(self.index)
                else:
                    data['index'] = self.index
                
                pickle.dump(data, f)
                
            logger.info(f"已将索引保存到{index_path}")
            return True
        except Exception as e:
            logger.error(f"保存索引失败: {str(e)}")
            return False
    
    async def add_texts(
        self,
        texts: List[str],
        metadatas: Optional[List[Dict[str, Any]]] = None,
        ids: Optional[List[str]] = None
    ) -> List[str]:
        """
        添加文本到向量数据库
        
        Args:
            texts: 文本列表
            metadatas: 元数据列表，与texts一一对应
            ids: ID列表，与texts一一对应，如果不提供则自动生成
            
        Returns:
            添加的文本ID列表
        """
        if not texts:
            return []
        
        # 获取嵌入向量
        embeddings = await self.llm_proxy.get_embeddings(texts)
        
        # 准备元数据
        if metadatas is None:
            metadatas = [{} for _ in texts]
        
        # 准备ID
        if ids is None:
            ids = [f"doc_{len(self.documents) + i}" for i in range(len(texts))]
        
        # 添加到索引
        self._add_embeddings(embeddings, texts, metadatas, ids)
        
        return ids
    
    def _add_embeddings(
        self,
        embeddings: List[List[float]],
        texts: List[str],
        metadatas: List[Dict[str, Any]],
        ids: List[str]
    ) -> None:
        """
        添加嵌入向量到索引
        
        Args:
            embeddings: 嵌入向量列表
            texts: 文本列表
            metadatas: 元数据列表
            ids: ID列表
        """
        if faiss:
            # 转换为numpy数组
            vectors = np.array(embeddings).astype('float32')
            
            # 如果是IVF索引且未训练，先训练
            if self.index_type == "ivf_flat" and not getattr(self, "is_trained", True):
                if vectors.shape[0] >= 100:  # 需要足够的数据来训练
                    self.index.train(vectors)
                    self.is_trained = True
                else:
                    # 数据不足，先用临时平面索引
                    logger.warning("IVF索引训练数据不足，将使用平面索引")
                    self.index = faiss.IndexFlatL2(self.embedding_dim)
            
            # 添加向量到索引
            start_idx = len(self.documents)
            self.index.add(vectors)
        else:
            # 简单实现
            start_idx = len(self.documents)
            for embedding in embeddings:
                self.index.add(embedding)
        
        # 存储文档内容和元数据
        for i, (text, metadata, doc_id) in enumerate(zip(texts, metadatas, ids)):
            idx = start_idx + i
            self.id_to_index[doc_id] = idx
            self.documents.append({
                "id": doc_id,
                "content": text,
                "metadata": metadata
            })
    
    async def similarity_search(
        self,
        query: str,
        k: int = 4,
        filter_metadata: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        相似度搜索
        
        Args:
            query: 查询文本
            k: 返回结果数量
            filter_metadata: 元数据过滤条件
            
        Returns:
            相似文档列表，每个文档包含内容、相似度分数和元数据
        """
        # 获取查询文本的嵌入向量
        query_embedding = await self.llm_proxy.get_embeddings(query)
        
        # 搜索相似向量
        indices, distances = self._search_embeddings(query_embedding[0], k * 2)  # 获取更多结果用于过滤
        
        # 准备结果
        results = []
        for idx, distance in zip(indices, distances):
            if idx >= len(self.documents) or idx < 0:
                continue
            
            doc = self.documents[idx]
            
            # 应用元数据过滤
            if filter_metadata and not self._match_metadata(doc["metadata"], filter_metadata):
                continue
            
            # 计算相似度分数 (1 - 归一化距离)
            score = 1.0 - min(distance / 100.0, 1.0)
            
            results.append({
                "id": doc["id"],
                "content": doc["content"],
                "score": score,
                "metadata": doc["metadata"]
            })
            
            if len(results) >= k:
                break
        
        return results
    
    def _search_embeddings(
        self,
        embedding: List[float],
        k: int
    ) -> Tuple[List[int], List[float]]:
        """
        搜索相似嵌入向量
        
        Args:
            embedding: 查询嵌入向量
            k: 返回结果数量
            
        Returns:
            (索引列表, 距离列表)
        """
        if faiss:
            # 转换为numpy数组
            vector = np.array([embedding]).astype('float32')
            
            # 搜索最近的k个向量
            distances, indices = self.index.search(vector, min(k, len(self.documents)))
            return indices[0].tolist(), distances[0].tolist()
        else:
            # 简单实现
            return self.index.search(embedding, k)
    
    def _match_metadata(self, metadata: Dict[str, Any], filter_metadata: Dict[str, Any]) -> bool:
        """
        检查元数据是否匹配过滤条件
        
        Args:
            metadata: 文档元数据
            filter_metadata: 元数据过滤条件
            
        Returns:
            是否匹配
        """
        for key, value in filter_metadata.items():
            if key not in metadata:
                return False
            
            if isinstance(value, list):
                # 列表值，检查是否有交集
                if not isinstance(metadata[key], list):
                    metadata_value = [metadata[key]]
                else:
                    metadata_value = metadata[key]
                
                if not any(v in metadata_value for v in value):
                    return False
            else:
                # 单一值，检查是否相等
                if metadata[key] != value:
                    return False
        
        return True
    
    def get_document_by_id(self, doc_id: str) -> Optional[Dict[str, Any]]:
        """
        根据ID获取文档
        
        Args:
            doc_id: 文档ID
            
        Returns:
            文档信息，如果找不到则返回None
        """
        idx = self.id_to_index.get(doc_id)
        if idx is not None and 0 <= idx < len(self.documents):
            return self.documents[idx]
        return None


class SimpleVectorIndex:
    """
    简单向量索引
    
    当FAISS不可用时，使用简单的向量索引实现，基于欧几里得距离计算。
    这个实现效率较低，仅用于测试和开发环境。
    """
    
    def __init__(self, dim: int):
        """
        初始化简单向量索引
        
        Args:
            dim: 向量维度
        """
        self.dim = dim
        self.vectors: List[List[float]] = []
    
    def add(self, vector: List[float]) -> None:
        """
        添加向量
        
        Args:
            vector: 向量
        """
        self.vectors.append(vector)
    
    def search(self, query: List[float], k: int) -> Tuple[List[int], List[float]]:
        """
        搜索最近的k个向量
        
        Args:
            query: 查询向量
            k: 返回结果数量
            
        Returns:
            (索引列表, 距离列表)
        """
        distances = []
        for i, vector in enumerate(self.vectors):
            # 计算欧几里得距离
            dist = sum((q - v) ** 2 for q, v in zip(query, vector)) ** 0.5
            distances.append((i, dist))
        
        # 按距离排序
        distances.sort(key=lambda x: x[1])
        
        # 返回前k个结果
        k = min(k, len(distances))
        indices = [d[0] for d in distances[:k]]
        dists = [d[1] for d in distances[:k]]
        
        return indices, dists 