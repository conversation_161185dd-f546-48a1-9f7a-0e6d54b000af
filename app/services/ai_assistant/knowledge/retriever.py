"""
知识检索器模块

该模块提供了知识检索功能，用于从知识库中检索相关内容。
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union

from app.services.ai_assistant.knowledge.vector_store import VectorStore
from app.services.ai_assistant.llm.proxy import LLMProxy, DefaultLLMProxy
from app.services.ai_assistant.common.cache import CacheService, default_cache_service

logger = logging.getLogger(__name__)

class KnowledgeRetriever(ABC):
    """
    知识检索器接口
    
    定义了知识检索的基本接口，用于从知识库中检索相关内容。
    """
    
    @abstractmethod
    async def retrieve(
        self,
        query: str,
        limit: int = 5,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        检索相关内容
        
        Args:
            query: 查询文本
            limit: 返回结果数量
            **kwargs: 其他参数
            
        Returns:
            相关内容列表，每个内容包含文本和元数据
        """
        pass
    
    @abstractmethod
    async def add_knowledge(
        self,
        texts: List[str],
        metadatas: Optional[List[Dict[str, Any]]] = None,
        **kwargs
    ) -> List[str]:
        """
        添加知识到知识库
        
        Args:
            texts: 文本列表
            metadatas: 元数据列表，与texts一一对应
            **kwargs: 其他参数
            
        Returns:
            添加的文本ID列表
        """
        pass


class DefaultKnowledgeRetriever(KnowledgeRetriever):
    """
    默认知识检索器
    
    用于测试和开发环境的简单知识检索器实现。
    不实际存储或检索知识，仅返回固定响应。
    """
    
    def __init__(self, cache_service: Optional[CacheService] = None):
        """
        初始化默认知识检索器
        
        Args:
            cache_service: 缓存服务，如果不提供则使用默认缓存服务
        """
        self.cache_service = cache_service or default_cache_service
        self.knowledge_base = [
            {
                "id": "kb_default_1",
                "content": "健身初学者应该从基础动作开始，如深蹲、俯卧撑和平板支撑。",
                "metadata": {"category": "beginner", "type": "advice"}
            },
            {
                "id": "kb_default_2",
                "content": "每周至少应该进行150分钟的中等强度有氧运动或75分钟的高强度有氧运动。",
                "metadata": {"category": "general", "type": "guideline"}
            },
            {
                "id": "kb_default_3",
                "content": "蛋白质是肌肉恢复和生长的重要营养素，每公斤体重应摄入1.6-2.2克蛋白质。",
                "metadata": {"category": "nutrition", "type": "fact"}
            }
        ]
    
    async def retrieve(
        self,
        query: str,
        limit: int = 5,
        use_cache: bool = True,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        返回预设的知识条目
        
        Args:
            query: 查询文本
            limit: 返回结果数量
            use_cache: 是否使用缓存
            **kwargs: 其他参数
            
        Returns:
            相关内容列表
        """
        logger.info(f"DefaultKnowledgeRetriever接收到查询: {query}")
        
        # 如果使用缓存，尝试从缓存获取结果
        if use_cache and self.cache_service:
            cache_key = f"default_retriever:retrieve:{query}:{limit}"
            cached_result = await self.cache_service.get(cache_key)
            if cached_result is not None:
                logger.debug(f"查询缓存命中: {query}")
                return cached_result
        
        # 简单匹配逻辑，仅用于测试
        results = []
        for item in self.knowledge_base:
            # 检查查询词是否在内容中
            if any(word in item["content"].lower() for word in query.lower().split()):
                results.append({
                    "id": item["id"],
                    "content": item["content"],
                    "metadata": item["metadata"],
                    "score": 0.8  # 固定相似度分数
                })
        
        # 如果没有匹配项，返回默认项
        if not results:
            results = self.knowledge_base[:min(limit, len(self.knowledge_base))]
            for item in results:
                item["score"] = 0.5  # 较低的相似度分数
        
        final_results = results[:limit]
        
        # 如果使用缓存，将结果存入缓存
        if use_cache and self.cache_service:
            # 缓存10分钟
            await self.cache_service.set(cache_key, final_results, ttl=600)
        
        return final_results
    
    async def add_knowledge(
        self,
        texts: List[str],
        metadatas: Optional[List[Dict[str, Any]]] = None,
        **kwargs
    ) -> List[str]:
        """
        模拟添加知识
        
        Args:
            texts: 文本列表
            metadatas: 元数据列表
            **kwargs: 其他参数
            
        Returns:
            添加的文本ID列表
        """
        logger.info(f"DefaultKnowledgeRetriever模拟添加{len(texts)}条知识")
        
        if metadatas is None:
            metadatas = [{} for _ in texts]
        
        ids = []
        for i, (text, metadata) in enumerate(zip(texts, metadatas)):
            doc_id = f"kb_added_{len(self.knowledge_base) + i}"
            self.knowledge_base.append({
                "id": doc_id,
                "content": text,
                "metadata": metadata
            })
            ids.append(doc_id)
        
        # 清除缓存，因为知识库已更新
        if self.cache_service:
            await self.cache_service.clear()
        
        return ids


class VectorKnowledgeRetriever(KnowledgeRetriever):
    """
    向量知识检索器
    
    基于向量数据库的知识检索器实现，使用语义搜索检索相关内容。
    """
    
    def __init__(
        self,
        vector_store: Optional[VectorStore] = None,
        llm_proxy: Optional[LLMProxy] = None,
        embedding_dim: int = 1536,
        index_path: Optional[str] = None,
        cache_service: Optional[CacheService] = None,
        cache_ttl: int = 600  # 默认缓存10分钟
    ):
        """
        初始化向量知识检索器
        
        Args:
            vector_store: 向量数据库，如果不提供则创建新的
            llm_proxy: 语言模型代理，用于生成文本嵌入
            embedding_dim: 嵌入向量维度
            index_path: 索引文件路径，如果提供则从文件加载索引
            cache_service: 缓存服务，如果不提供则使用默认缓存服务
            cache_ttl: 缓存过期时间（秒）
        """
        self.llm_proxy = llm_proxy or DefaultLLMProxy()
        self.cache_service = cache_service or default_cache_service
        self.cache_ttl = cache_ttl
        
        if vector_store:
            self.vector_store = vector_store
        else:
            self.vector_store = VectorStore(
                embedding_dim=embedding_dim,
                llm_proxy=self.llm_proxy,
                index_path=index_path
            )
    
    async def retrieve(
        self,
        query: str,
        limit: int = 5,
        filter_metadata: Optional[Dict[str, Any]] = None,
        use_cache: bool = True,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        检索相关内容
        
        Args:
            query: 查询文本
            limit: 返回结果数量
            filter_metadata: 元数据过滤条件
            use_cache: 是否使用缓存
            **kwargs: 其他参数
            
        Returns:
            相关内容列表
        """
        logger.info(f"VectorKnowledgeRetriever接收到查询: {query}")
        
        try:
            # 如果使用缓存，尝试从缓存获取结果
            if use_cache and self.cache_service:
                # 构建缓存键，包括查询、限制和过滤条件
                filter_str = ""
                if filter_metadata:
                    filter_str = "_" + "_".join(f"{k}:{v}" for k, v in sorted(filter_metadata.items()))
                cache_key = f"vector_retriever:retrieve:{query}:{limit}{filter_str}"
                
                cached_result = await self.cache_service.get(cache_key)
                if cached_result is not None:
                    logger.debug(f"查询缓存命中: {query}")
                    return cached_result
            
            # 使用向量数据库进行相似度搜索
            results = await self.vector_store.similarity_search(
                query=query,
                k=limit,
                filter_metadata=filter_metadata
            )
            
            if not results:
                logger.warning(f"查询'{query}'没有找到相关内容")
            else:
                logger.info(f"查询'{query}'找到{len(results)}条相关内容")
            
            # 如果使用缓存，将结果存入缓存
            if use_cache and self.cache_service and results:
                await self.cache_service.set(cache_key, results, ttl=self.cache_ttl)
            
            return results
        except Exception as e:
            logger.error(f"知识检索失败: {str(e)}")
            # 发生错误时返回空列表
            return []
    
    async def add_knowledge(
        self,
        texts: List[str],
        metadatas: Optional[List[Dict[str, Any]]] = None,
        ids: Optional[List[str]] = None,
        clear_cache: bool = True,
        **kwargs
    ) -> List[str]:
        """
        添加知识到向量数据库
        
        Args:
            texts: 文本列表
            metadatas: 元数据列表
            ids: ID列表
            clear_cache: 是否清除缓存
            **kwargs: 其他参数
            
        Returns:
            添加的文本ID列表
        """
        logger.info(f"VectorKnowledgeRetriever添加{len(texts)}条知识")
        
        try:
            # 添加文本到向量数据库
            doc_ids = await self.vector_store.add_texts(
                texts=texts,
                metadatas=metadatas,
                ids=ids
            )
            
            # 如果提供了保存路径，保存索引
            save_path = kwargs.get("save_path")
            if save_path:
                success = self.vector_store.save_index(save_path)
                if success:
                    logger.info(f"已将知识库保存到{save_path}")
                else:
                    logger.warning(f"保存知识库到{save_path}失败")
            
            # 清除缓存，因为知识库已更新
            if clear_cache and self.cache_service:
                await self.cache_service.clear()
                logger.debug("已清除知识检索缓存")
            
            return doc_ids
        except Exception as e:
            logger.error(f"添加知识失败: {str(e)}")
            # 发生错误时返回空列表
            return []
    
    def save(self, index_path: str) -> bool:
        """
        保存知识库到文件
        
        Args:
            index_path: 索引文件路径
            
        Returns:
            是否成功保存
        """
        return self.vector_store.save_index(index_path)
    
    async def clear_cache(self) -> bool:
        """
        清除知识检索缓存
        
        Returns:
            是否成功清除
        """
        if self.cache_service:
            return await self.cache_service.clear()
        return False

# 创建默认知识检索器实例
default_retriever = DefaultKnowledgeRetriever() 