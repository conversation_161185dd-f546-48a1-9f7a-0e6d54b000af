"""
意图识别器工厂模块

该模块提供了创建不同类型意图识别器的工厂。
"""

import logging
from typing import Dict, List, Any, Optional, Type

from app.services.ai_assistant.intent.recognition.recognizer import (
    BaseIntentRecognizer,
    LLMIntentRecognizer,
    RuleBasedIntentRecognizer,
    CompositeIntentRecognizer
)

logger = logging.getLogger(__name__)

class IntentRecognizerFactory:
    """
    意图识别器工厂类
    
    提供创建各种意图识别器实例的方法。
    """
    
    def __init__(self):
        """初始化意图识别器工厂"""
        # 定义常用意图类型
        self.common_intent_types = [
            "greeting",          # 问候
            "farewell",          # 告别
            "thanks",            # 感谢
            "help",              # 帮助
            "smalltalk",         # 闲聊
            "fitness_advice",    # 健身建议
            "training_plan",     # 训练计划
            "exercise_action",   # 运动动作
            "diet_advice",       # 饮食建议
            "workout_tracking",  # 训练记录
            "general_chat",      # 一般聊天
            "unknown"            # 未知意图
        ]
        
        # 定义规则匹配的意图规则
        self.intent_rules = {
            "greeting": [
                "你好", "早上好", "下午好", "晚上好", "嗨", "hi", "hello", 
                "您好", "你是谁", "请问", "在吗", "在么", "在不在"
            ],
            "farewell": [
                "再见", "拜拜", "下次见", "回头见", "明天见", "谢谢你的帮助",
                "goodbye", "bye", "see you", "see you later", "talk to you later",
                "下线了", "我走了"
            ],
            "thanks": [
                "谢谢", "感谢", "多谢", "非常感谢", "谢谢你的帮助", "谢了",
                "thank you", "thanks", "appreciate it", "much appreciated"
            ],
            "help": [
                "帮助", "help", "怎么用", "使用方法", "指南", "guide", "tutorial",
                "教程", "说明", "使用说明", "能做什么", "你会什么", "功能介绍"
            ]
        }
    
    def create_llm_recognizer(self, 
                             intent_types: Optional[List[str]] = None,
                             template: Optional[str] = None,
                             model: str = "intent-recognition-app",
                             provider: Optional[str] = None) -> BaseIntentRecognizer:
        """
        创建基于LLM的意图识别器
        
        Args:
            intent_types: 支持的意图类型列表，如果为None则使用默认列表
            template: 提示模板，如果为None则使用默认模板
            model: 使用的模型
            provider: LLM提供商
            
        Returns:
            LLM意图识别器实例
        """
        if intent_types is None:
            intent_types = self.common_intent_types
        
        return LLMIntentRecognizer(
            intent_types=intent_types,
            template=template,
            model=model,
            provider=provider
        )
    
    def create_rule_based_recognizer(self, 
                                    intent_rules: Optional[Dict[str, List[str]]] = None) -> BaseIntentRecognizer:
        """
        创建基于规则的意图识别器
        
        Args:
            intent_rules: 意图规则字典，如果为None则使用默认规则
            
        Returns:
            规则意图识别器实例
        """
        if intent_rules is None:
            intent_rules = self.intent_rules
        
        return RuleBasedIntentRecognizer(intent_rules)
    
    def create_composite_recognizer(self, 
                                   primary: Optional[BaseIntentRecognizer] = None,
                                   fallback: Optional[BaseIntentRecognizer] = None) -> BaseIntentRecognizer:
        """
        创建组合意图识别器
        
        组合使用规则识别器和LLM识别器，优先使用规则识别器，
        如果规则识别器无法识别（返回unknown意图或置信度低），则使用LLM识别器。
        
        Args:
            primary: 主识别器，如果为None则创建规则识别器
            fallback: 备用识别器，如果为None则创建LLM识别器
            
        Returns:
            组合意图识别器实例
        """
        if primary is None:
            primary = self.create_rule_based_recognizer()
        
        if fallback is None:
            fallback = self.create_llm_recognizer()
        
        return CompositeIntentRecognizer([primary, fallback])
