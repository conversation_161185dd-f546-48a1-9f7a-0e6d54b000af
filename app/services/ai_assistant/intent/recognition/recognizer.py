"""
意图识别器模块

这个模块提供了用于识别用户意图的接口和基础实现。
它定义了一个通用的意图识别器接口，以及基于不同策略的具体实现。
"""
from abc import ABC, abstractmethod
import logging
import json
import re
from typing import Dict, List, Any, Optional, Union, Set

from app.services.ai_assistant.llm.service import llm_service

logger = logging.getLogger(__name__)

class IntentRecognitionResult:
    """
    意图识别结果类
    
    用于存储意图识别的结果，包括意图类型、置信度和提取的参数。
    """
    
    def __init__(self, 
                 intent_type: str, 
                 confidence: float = 1.0, 
                 parameters: Optional[Dict[str, Any]] = None):
        """
        初始化意图识别结果
        
        Args:
            intent_type: 意图类型
            confidence: 置信度，范围从0到1
            parameters: 提取的参数
        """
        self.intent_type = intent_type
        self.confidence = confidence
        self.parameters = parameters or {}
    
    def __str__(self) -> str:
        """
        返回意图识别结果的字符串表示
        
        Returns:
            意图识别结果的字符串表示
        """
        return f"Intent: {self.intent_type} (confidence: {self.confidence:.2f}), parameters: {self.parameters}"
    
    def to_dict(self) -> Dict[str, Any]:
        """
        将意图识别结果转换为字典
        
        Returns:
            意图识别结果的字典表示
        """
        return {
            "intent_type": self.intent_type,
            "confidence": self.confidence,
            "parameters": self.parameters
        }


class BaseIntentRecognizer(ABC):
    """
    意图识别器基类
    
    定义了识别用户意图的通用接口。
    """
    
    @abstractmethod
    def recognize(self, user_input: str) -> IntentRecognitionResult:
        """
        识别用户意图
        
        Args:
            user_input: 用户输入文本
            
        Returns:
            意图识别结果
        """
        pass
    
    @abstractmethod
    async def arecognize(self, user_input: str) -> IntentRecognitionResult:
        """
        异步识别用户意图
        
        Args:
            user_input: 用户输入文本
            
        Returns:
            意图识别结果
        """
        pass


class LLMIntentRecognizer(BaseIntentRecognizer):
    """
    基于LLM的意图识别器
    
    使用大语言模型识别用户意图。
    """
    
    def __init__(self, 
                 intent_types: List[str], 
                 template: Optional[str] = None,
                 model: str = "intent-recognition-app",
                 provider: Optional[str] = None):
        """
        初始化基于LLM的意图识别器
        
        Args:
            intent_types: 支持的意图类型列表
            template: 提示模板，如果为None则使用默认模板
            model: 使用的模型
            provider: LLM提供商
        """
        self.intent_types = intent_types
        self.model = model
        self.provider = provider
        
        # 如果未提供模板，使用默认模板
        if template is None:
            self.template = self._create_default_template()
        else:
            self.template = template
    
    def _create_default_template(self) -> str:
        """
        创建默认提示模板
        
        根据支持的意图类型创建默认的提示模板。
        
        Returns:
            提示模板
        """
        intent_types_str = "\n".join([f"- {intent_type}" for intent_type in self.intent_types])
        
        return f"""分析以下用户输入，并确定用户的主要意图。

可能的意图类别:
{intent_types_str}

用户输入: "{{user_input}}"

以JSON格式返回意图和置信度，如：
{{"intent_type": "意图类型", "confidence": 0.95}}
"""
    
    def recognize(self, user_input: str) -> IntentRecognitionResult:
        """
        识别用户意图
        
        Args:
            user_input: 用户输入文本
            
        Returns:
            意图识别结果
        """
        # 填充模板
        prompt = self.template.format(user_input=user_input)
        
        # 调用LLM服务
        result = llm_service.generate_text(
            prompt, 
            provider=self.provider, 
            model=self.model
        )
        
        # 解析结果
        try:
            # 尝试从可能嵌入在文本中的JSON内容提取JSON对象
            json_match = re.search(r'```json\s*(.+?)\s*```|({.+})', result, re.DOTALL)
            if json_match:
                json_str = json_match.group(1) or json_match.group(2)
                data = json.loads(json_str)
            else:
                # 如果没有明确的JSON标记，尝试直接解析
                data = json.loads(result)
            
            # 提取意图类型和置信度
            intent_type = data.get("intent_type", "unknown")
            confidence = data.get("confidence", 1.0)
            parameters = data.get("parameters", {})
            
            # 如果意图类型不在支持的列表中，设置为未知
            if intent_type not in self.intent_types:
                logger.warning(f"Unsupported intent type: {intent_type}")
                intent_type = "unknown"
            
            return IntentRecognitionResult(intent_type, confidence, parameters)
        
        except (json.JSONDecodeError, KeyError) as e:
            logger.error(f"Failed to parse LLM response: {str(e)}, response: {result}")
            
            # 尝试直接从文本中提取意图类型
            for intent_type in self.intent_types:
                if intent_type.lower() in result.lower():
                    return IntentRecognitionResult(intent_type, 0.6)
            
            return IntentRecognitionResult("unknown", 0.0)
    
    async def arecognize(self, user_input: str) -> IntentRecognitionResult:
        """
        异步识别用户意图
        
        Args:
            user_input: 用户输入文本
            
        Returns:
            意图识别结果
        """
        # 填充模板
        prompt = self.template.format(user_input=user_input)
        
        # 调用LLM服务
        result = await llm_service.agenerate_text(
            prompt, 
            provider=self.provider, 
            model=self.model
        )
        
        # 解析结果
        try:
            # 尝试从可能嵌入在文本中的JSON内容提取JSON对象
            json_match = re.search(r'```json\s*(.+?)\s*```|({.+})', result, re.DOTALL)
            if json_match:
                json_str = json_match.group(1) or json_match.group(2)
                data = json.loads(json_str)
            else:
                # 如果没有明确的JSON标记，尝试直接解析
                data = json.loads(result)
            
            # 提取意图类型和置信度
            intent_type = data.get("intent_type", "unknown")
            confidence = data.get("confidence", 1.0)
            parameters = data.get("parameters", {})
            
            # 如果意图类型不在支持的列表中，设置为未知
            if intent_type not in self.intent_types:
                logger.warning(f"Unsupported intent type: {intent_type}")
                intent_type = "unknown"
            
            return IntentRecognitionResult(intent_type, confidence, parameters)
        
        except (json.JSONDecodeError, KeyError) as e:
            logger.error(f"Failed to parse LLM response: {str(e)}, response: {result}")
            
            # 尝试直接从文本中提取意图类型
            for intent_type in self.intent_types:
                if intent_type.lower() in result.lower():
                    return IntentRecognitionResult(intent_type, 0.6)
            
            return IntentRecognitionResult("unknown", 0.0)


class RuleBasedIntentRecognizer(BaseIntentRecognizer):
    """
    基于规则的意图识别器
    
    使用预定义的关键词和规则识别用户意图。
    """
    
    def __init__(self, intent_rules: Dict[str, List[str]]):
        """
        初始化基于规则的意图识别器
        
        Args:
            intent_rules: 意图类型到关键词列表的映射
        """
        self.intent_rules = intent_rules
        self.all_keywords: Set[str] = set()
        
        # 收集所有关键词
        for keywords in intent_rules.values():
            self.all_keywords.update(keywords)
    
    def recognize(self, user_input: str) -> IntentRecognitionResult:
        """
        识别用户意图
        
        Args:
            user_input: 用户输入文本
            
        Returns:
            意图识别结果
        """
        # 将用户输入转换为小写
        user_input_lower = user_input.lower()
        
        # 计算每个意图的匹配度
        matches = {}
        
        for intent_type, keywords in self.intent_rules.items():
            count = 0
            matched_keywords = []
            
            for keyword in keywords:
                if keyword.lower() in user_input_lower:
                    count += 1
                    matched_keywords.append(keyword)
            
            if count > 0:
                # 计算置信度
                confidence = count / len(keywords)
                matches[intent_type] = (confidence, count, matched_keywords)
        
        # 如果没有匹配，返回未知意图
        if not matches:
            return IntentRecognitionResult("unknown", 0.0)
        
        # 找到最匹配的意图
        best_intent = max(matches.items(), key=lambda x: (x[1][1], x[1][0]))
        intent_type, (confidence, count, matched_keywords) = best_intent
        
        # 创建参数
        parameters = {"matched_keywords": matched_keywords}
        
        return IntentRecognitionResult(intent_type, confidence, parameters)
    
    async def arecognize(self, user_input: str) -> IntentRecognitionResult:
        """
        异步识别用户意图
        
        Args:
            user_input: 用户输入文本
            
        Returns:
            意图识别结果
        """
        # 基于规则的识别是CPU密集型操作，不需要特殊的异步实现
        return self.recognize(user_input)


class CompositeIntentRecognizer(BaseIntentRecognizer):
    """
    组合意图识别器
    
    组合多个意图识别器，选择置信度最高的结果。
    """
    
    def __init__(self, recognizers: List[BaseIntentRecognizer]):
        """
        初始化组合意图识别器
        
        Args:
            recognizers: 意图识别器列表
        """
        self.recognizers = recognizers
    
    def recognize(self, user_input: str) -> IntentRecognitionResult:
        """
        识别用户意图
        
        Args:
            user_input: 用户输入文本
            
        Returns:
            意图识别结果
        """
        results = []
        
        # 获取所有识别器的结果
        for recognizer in self.recognizers:
            result = recognizer.recognize(user_input)
            results.append(result)
        
        # 选择置信度最高的结果
        best_result = max(results, key=lambda x: x.confidence)
        
        # 如果最佳结果的置信度太低，返回未知意图
        if best_result.confidence < 0.3:
            return IntentRecognitionResult("unknown", 0.0)
        
        return best_result
    
    async def arecognize(self, user_input: str) -> IntentRecognitionResult:
        """
        异步识别用户意图
        
        Args:
            user_input: 用户输入文本
            
        Returns:
            意图识别结果
        """
        results = []
        
        # 获取所有识别器的结果
        for recognizer in self.recognizers:
            result = await recognizer.arecognize(user_input)
            results.append(result)
        
        # 选择置信度最高的结果
        best_result = max(results, key=lambda x: x.confidence)
        
        # 如果最佳结果的置信度太低，返回未知意图
        if best_result.confidence < 0.3:
            return IntentRecognitionResult("unknown", 0.0)
        
        return best_result 