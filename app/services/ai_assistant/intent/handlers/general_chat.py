"""
通用聊天处理器模块

该模块提供处理与通用聊天相关的用户意图的功能，包括问候、闲聊、感谢和帮助等。
作为默认处理器，它处理不属于特定健身类别的对话。
"""

import logging
from typing import Dict, Any, Optional
import datetime

from app.services.ai_assistant.intent.handlers.base import BaseIntentHandler
from app.services.ai_assistant.llm.proxy import LLMProxy
from app.services.ai_assistant.knowledge.retriever import KnowledgeRetriever

logger = logging.getLogger(__name__)

class GeneralChatHandler(BaseIntentHandler):
    """处理与通用聊天相关的用户意图的处理器"""
    
    def __init__(
        self, 
        llm_proxy: LLMProxy,
        knowledge_retriever: Optional[KnowledgeRetriever] = None
    ):
        """
        初始化通用聊天处理器
        
        Args:
            llm_proxy: 语言模型代理，用于生成聊天响应
            knowledge_retriever: 知识库检索器，用于检索相关知识
        """
        super().__init__(llm_proxy, knowledge_retriever)
        logger.info("通用聊天处理器已初始化")
    
    async def handle(self, intent: str, user_message: Dict[str, Any], user_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理通用聊天相关的意图
        
        Args:
            intent: 具体的聊天意图类型
            user_message: 用户消息内容
            user_context: 用户上下文信息
            
        Returns:
            包含响应内容的字典
        """
        logger.info(f"处理通用聊天意图: {intent}")
        
        if intent == "greeting":
            return await self._handle_greeting(user_message, user_context)
        elif intent == "farewell":
            return await self._handle_farewell(user_message, user_context)
        elif intent == "thanks":
            return await self._handle_thanks(user_message, user_context)
        elif intent == "help":
            return await self._handle_help(user_message, user_context)
        elif intent == "smalltalk":
            return await self._handle_smalltalk(user_message, user_context)
        else:
            return await self._handle_default(user_message, user_context)
    
    async def _handle_greeting(self, user_message: Dict[str, Any], user_context: Dict[str, Any]) -> Dict[str, Any]:
        """处理问候意图"""
        logger.info("处理问候意图")
        
        # 获取当前时间，根据时间段生成不同的问候语
        current_hour = datetime.datetime.now().hour
        
        if 5 <= current_hour < 12:
            time_greeting = "早上好"
        elif 12 <= current_hour < 18:
            time_greeting = "下午好"
        else:
            time_greeting = "晚上好"
        
        # 获取用户名称（如果有）
        user_name = user_context.get("name", "")
        name_greeting = f"，{user_name}" if user_name else ""
        
        # 构建基本问候语
        greeting = f"{time_greeting}{name_greeting}！我是您的健身教练助手。"
        
        # 根据用户的健身情况添加个性化内容
        if user_context.get("last_workout_date"):
            last_workout = user_context.get("last_workout_date")
            greeting += f" 您上次锻炼是在{last_workout}。"
        
        if user_context.get("fitness_goal"):
            goal = user_context.get("fitness_goal")
            greeting += f" 我会继续帮助您实现{goal}的目标。"
        
        greeting += " 今天有什么可以帮您的吗？"
        
        return {
            "response_type": "greeting",
            "content": greeting,
            "suggested_next_intents": ["help", "fitness_advice", "training_plan"]
        }
    
    async def _handle_farewell(self, user_message: Dict[str, Any], user_context: Dict[str, Any]) -> Dict[str, Any]:
        """处理告别意图"""
        logger.info("处理告别意图")
        
        # 构建基本告别语
        farewell = "再见！希望很快能再次帮到您。"
        
        # 根据用户的训练计划添加提醒
        if user_context.get("next_workout_date"):
            next_workout = user_context.get("next_workout_date")
            farewell += f" 别忘了您的下一次训练安排在{next_workout}。"
        
        farewell += " 保持健康，加油！"
        
        return {
            "response_type": "farewell",
            "content": farewell
        }
    
    async def _handle_thanks(self, user_message: Dict[str, Any], user_context: Dict[str, Any]) -> Dict[str, Any]:
        """处理感谢意图"""
        logger.info("处理感谢意图")
        
        response = "不客气！很高兴能帮到您。如果您有任何其他健身相关的问题，随时可以问我。"
        
        return {
            "response_type": "thanks",
            "content": response,
            "suggested_next_intents": ["help", "fitness_advice", "training_plan"]
        }
    
    async def _handle_help(self, user_message: Dict[str, Any], user_context: Dict[str, Any]) -> Dict[str, Any]:
        """处理帮助意图"""
        logger.info("处理帮助意图")
        
        help_text = """
        我是您的AI健身教练助手，可以帮助您：
        
        1. 提供健身建议和指导
        2. 解答关于特定运动动作的问题
        3. 创建和调整个性化训练计划
        4. 提供饮食和营养方面的建议
        5. 跟踪您的健身进度
        
        您可以直接询问我任何健身相关的问题，例如：
        - "如何正确做深蹲？"
        - "给我推荐一个增肌训练计划"
        - "我想减脂，应该怎么吃？"
        - "哪些运动适合初学者？"
        
        请告诉我您的具体需求，我会尽力提供帮助！
        """
        
        return {
            "response_type": "help",
            "content": help_text,
            "suggested_next_intents": ["fitness_advice", "exercise_action", "training_plan", "diet_advice"]
        }
    
    async def _handle_smalltalk(self, user_message: Dict[str, Any], user_context: Dict[str, Any]) -> Dict[str, Any]:
        """处理闲聊意图"""
        logger.info("处理闲聊意图")
        
        query = user_message.get("text", "")
        
        # 使用LLM生成闲聊回复
        prompt = f"""
        用户在与健身教练助手进行闲聊，用户说:
        {query}
        
        请以健身教练助手的身份回复，保持友好、专业，并在适当的时候引导话题回到健身相关内容。
        回复应该简洁、自然，像真人一样。
        """
        
        smalltalk_response = await self.llm_proxy.generate_text(prompt, temperature=0.7)
        
        return {
            "response_type": "smalltalk",
            "content": smalltalk_response,
            "suggested_next_intents": ["help", "fitness_advice"]
        }
    
    async def _handle_default(self, user_message: Dict[str, Any], user_context: Dict[str, Any]) -> Dict[str, Any]:
        """处理未识别的通用聊天意图"""
        logger.info("处理默认通用聊天查询")
        
        query = user_message.get("text", "")
        
        # 尝试从知识库检索相关信息
        relevant_knowledge = []
        if self.knowledge_retriever:
            try:
                relevant_knowledge = await self.knowledge_retriever.retrieve(query)
            except Exception as e:
                logger.error(f"知识检索失败: {str(e)}")
        
        # 构建提示
        knowledge_text = ""
        if relevant_knowledge:
            knowledge_text = "参考以下相关知识:\n"
            for i, item in enumerate(relevant_knowledge, 1):
                knowledge_text += f"{i}. {item.get('content', '')}\n"
        
        prompt = f"""
        用户查询: {query}
        
        {knowledge_text}
        
        请以健身教练助手的身份回复。如果是健身相关问题，提供专业、准确的建议。
        如果不是健身相关问题，礼貌地引导话题回到健身领域。
        回复应该友好、有帮助，但保持简洁。
        """
        
        # 使用LLM生成回答
        response = await self.llm_proxy.generate_text(prompt, temperature=0.5)
        
        return {
            "response_type": "general_response",
            "content": response,
            "suggested_next_intents": ["help", "fitness_advice", "training_plan"]
        } 