"""
意图处理器包

该包包含处理各种用户意图的处理器类。
每个处理器负责处理特定类型的用户意图并生成相应的响应。
"""

from app.services.ai_assistant.intent.handlers.base import BaseIntentHandler
from app.services.ai_assistant.intent.handlers.factory import IntentHandlerFactory

# 导入所有处理器，以便可以使用工厂注册
try:
    from app.services.ai_assistant.intent.handlers.general_chat import GeneralChatHandler
except ImportError:
    pass

try:
    from app.services.ai_assistant.intent.handlers.fitness_advice import FitnessAdviceHandler
except ImportError:
    pass

try:
    from app.services.ai_assistant.intent.handlers.exercise_action import ExerciseActionHandler
except ImportError:
    pass

try:
    from app.services.ai_assistant.intent.handlers.training_plan import TrainingPlanHandler
except ImportError:
    pass

try:
    from app.services.ai_assistant.intent.handlers.diet_advice import DietAdviceHandler
except ImportError:
    pass

__all__ = [
    'BaseIntentHandler',
    'IntentHandlerFactory',
    'GeneralChatHandler',
    'FitnessAdviceHandler',
    'ExerciseActionHandler',
    'TrainingPlanHandler',
    'DietAdviceHandler'
]
