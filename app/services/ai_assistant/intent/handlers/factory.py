"""
意图处理器工厂模块

该模块提供创建和管理不同类型意图处理器的工厂类，实现了依赖注入和注册机制。
"""

import logging
from typing import Dict, Type, Optional, Any

from app.services.ai_assistant.intent.handlers.base import BaseIntentHandler
from app.services.ai_assistant.intent.handlers.general_chat import GeneralChatHandler
from app.services.ai_assistant.intent.handlers.fitness_advice import FitnessAdviceHandler
from app.services.ai_assistant.llm.proxy import LLMProxy
from app.services.ai_assistant.knowledge.retriever import KnowledgeRetriever
from app.services.ai_assistant.common.cache import CacheService

logger = logging.getLogger(__name__)

class IntentHandlerFactory:
    """意图处理器工厂，负责创建和管理不同类型的意图处理器"""
    
    _handlers: Dict[str, Type[BaseIntentHandler]] = {}
    
    @classmethod
    def register(cls, intent_type: str, handler_class: Type[BaseIntentHandler]) -> None:
        """
        注册一个意图处理器类
        
        Args:
            intent_type: 意图类型
            handler_class: 对应的处理器类或实例
        """
        cls._handlers[intent_type] = handler_class
        # 判断是类还是实例
        if isinstance(handler_class, type):
            logger.info(f"已注册意图处理器: {intent_type} -> {handler_class.__name__}")
        else:
            logger.info(f"已注册意图处理器: {intent_type} -> {handler_class.__class__.__name__}")
    
    @classmethod
    def create(cls, 
              intent_type: str, 
              llm_proxy: LLMProxy,
              knowledge_retriever: Optional[KnowledgeRetriever] = None,
              **kwargs) -> BaseIntentHandler:
        """
        创建指定类型的意图处理器实例
        
        Args:
            intent_type: 意图类型
            llm_proxy: 语言模型代理
            knowledge_retriever: 知识检索器
            **kwargs: 传递给处理器构造函数的额外参数
            
        Returns:
            对应类型的意图处理器实例
            
        Raises:
            ValueError: 如果指定的意图类型未注册
        """
        if intent_type not in cls._handlers:
            # 如果未找到指定类型，尝试获取默认处理器
            if "default" in cls._handlers:
                logger.warning(f"未找到意图类型 '{intent_type}' 的处理器，使用默认处理器")
                handler_class = cls._handlers["default"]
            else:
                raise ValueError(f"未注册的意图类型: {intent_type}")
        else:
            handler_class = cls._handlers[intent_type]
        
        try:
            # 检查是否已经是实例
            if not isinstance(handler_class, type):
                logger.debug(f"意图处理器 {intent_type} 已是实例，直接返回")
                return handler_class
                
            # 创建处理器实例并返回
            handler = handler_class(
                llm_proxy=llm_proxy,
                knowledge_retriever=knowledge_retriever,
                **kwargs
            )
            logger.debug(f"已创建意图处理器: {intent_type} -> {handler.__class__.__name__}")
            return handler
        except Exception as e:
            logger.error(f"创建意图处理器失败: {intent_type}, 错误: {str(e)}")
            raise
    
    @classmethod
    def get_registered_types(cls) -> list:
        """
        获取所有已注册的意图类型
        
        Returns:
            已注册的意图类型列表
        """
        return list(cls._handlers.keys())

    @classmethod
    def create_fitness_advice_handler(
        cls,
        llm_proxy: Optional[LLMProxy] = None,
        knowledge_retriever: Optional[KnowledgeRetriever] = None,
        cache_service: Optional[CacheService] = None,
        use_bailian: bool = True
    ) -> FitnessAdviceHandler:
        """
        创建健身建议处理器
        
        Args:
            llm_proxy: LLM代理，如果不提供且use_bailian为True则创建百炼代理
            knowledge_retriever: 知识检索器
            cache_service: 缓存服务
            use_bailian: 是否使用百炼应用
            
        Returns:
            健身建议处理器实例
        """
        # 如果使用百炼且未提供代理，则创建百炼代理
        if use_bailian and llm_proxy is None:
            from app.services.ai_assistant.llm.providers.bailian_proxy import BailianLLMProxy
            llm_proxy = BailianLLMProxy(app_type="fitness-coach-app", cache_service=cache_service)
        
        return FitnessAdviceHandler(
            llm_proxy=llm_proxy,
            knowledge_retriever=knowledge_retriever,
            cache_service=cache_service,
            use_bailian=use_bailian
        )

# 在导入模块时自动注册所有处理器
def register_handlers():
    """注册所有可用的意图处理器"""
    try:
        from app.services.ai_assistant.intent.handlers.general_chat import GeneralChatHandler
        IntentHandlerFactory.register("general_chat", GeneralChatHandler)
        # 同时将GeneralChatHandler注册为默认处理器
        IntentHandlerFactory.register("default", GeneralChatHandler)
    except ImportError:
        logger.warning("未能加载GeneralChatHandler")
    
    try:
        from app.services.ai_assistant.intent.handlers.fitness_advice import FitnessAdviceHandler
        IntentHandlerFactory.register("fitness_advice", FitnessAdviceHandler)
    except ImportError:
        logger.warning("未能加载FitnessAdviceHandler")
    
    try:
        from app.services.ai_assistant.intent.handlers.exercise_action import ExerciseActionHandler
        IntentHandlerFactory.register("exercise_action", ExerciseActionHandler)
    except ImportError:
        logger.warning("未能加载ExerciseActionHandler")
    
    try:
        from app.services.ai_assistant.intent.handlers.training_plan import TrainingPlanHandler
        IntentHandlerFactory.register("training_plan", TrainingPlanHandler)
    except ImportError:
        logger.warning("未能加载TrainingPlanHandler")
    
    try:
        from app.services.ai_assistant.intent.handlers.diet_advice import DietAdviceHandler
        IntentHandlerFactory.register("diet_advice", DietAdviceHandler)
    except ImportError:
        logger.warning("未能加载DietAdviceHandler")

# 执行注册
register_handlers() 