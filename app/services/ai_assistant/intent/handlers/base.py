"""
意图处理器基类

该模块定义了所有意图处理器的基类，提供标准接口和通用功能。
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Union

from app.services.ai_assistant.llm.proxy import LLMProxy
from app.services.ai_assistant.knowledge.retriever import KnowledgeRetriever
from app.services.ai_assistant.common.response_adapter import ResponseAdapter

logger = logging.getLogger(__name__)


class BaseIntentHandler(ABC):
    """
    意图处理器基类
    
    所有具体意图处理器必须继承该类并实现handle方法。
    """
    
    def __init__(self, 
                llm_proxy: Optional[LLMProxy] = None,
                knowledge_retriever: Optional[KnowledgeRetriever] = None):
        """
        初始化处理器
        
        Args:
            llm_proxy: 大语言模型代理
            knowledge_retriever: 知识检索器
        """
        self.llm_proxy = llm_proxy
        self.knowledge_retriever = knowledge_retriever
        
    @abstractmethod
    async def handle(self, 
                    intent: str, 
                    user_message: Dict[str, Any], 
                    user_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理用户意图的抽象方法
        
        Args:
            intent: 意图类型
            user_message: 用户消息内容
            user_context: 用户上下文信息
            
        Returns:
            包含响应内容的字典
        """
        pass
    
    def _get_response_template(self) -> Dict[str, Any]:
        """
        获取响应模板
        
        Returns:
            响应模板字典
        """
        return {
            "text": "",
            "intent": "",
            "confidence": 0.0,
            "parameters": {},
            "metadata": {},
            "suggestions": []
        }
    
    async def _enhance_response(self, 
                         response: Dict[str, Any], 
                         user_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        增强响应
        
        添加根据用户上下文定制的内容，如建议和个性化信息。
        
        Args:
            response: 原始响应
            user_context: 用户上下文
            
        Returns:
            增强后的响应
        """
        # 复制响应以避免修改原始响应
        enhanced = response.copy()
        
        # 根据用户偏好和上下文添加建议
        if user_context.get("fitness_level"):
            fitness_level = user_context["fitness_level"]
            if fitness_level == "beginner":
                enhanced["suggestions"].append("需要了解基础健身知识吗？")
            elif fitness_level == "intermediate":
                enhanced["suggestions"].append("想尝试新的训练计划吗？")
            elif fitness_level == "advanced":
                enhanced["suggestions"].append("需要优化您的高级训练方案吗？")
        
        # 添加用户历史中可能感兴趣的内容
        if user_context.get("recent_interests"):
            for interest in user_context["recent_interests"][:2]:  # 最多添加两个
                enhanced["suggestions"].append(f"想了解更多关于{interest}的信息吗？")
        
        return enhanced
    
    async def _format_response_text(self, text: str, user_context: Dict[str, Any]) -> str:
        """
        格式化响应文本
        
        根据用户上下文个性化响应文本。
        
        Args:
            text: 原始响应文本
            user_context: 用户上下文
            
        Returns:
            格式化后的响应文本
        """
        # 添加用户名称
        if user_context.get("name"):
            if "[用户名]" in text:
                text = text.replace("[用户名]", user_context["name"])
        
        # 替换其他占位符
        if "[健身水平]" in text and user_context.get("fitness_level"):
            level_map = {
                "beginner": "初学者",
                "intermediate": "中级健身者",
                "advanced": "高级健身者"
            }
            text = text.replace("[健身水平]", level_map.get(user_context["fitness_level"], "健身爱好者"))
        
        return text

    async def can_handle(self, intent: str) -> bool:
        """
        检查该处理器是否可以处理指定的意图
        
        Args:
            intent: 意图类型
            
        Returns:
            如果可以处理该意图，返回True；否则返回False
        """
        # 默认实现：子类可以覆盖此方法以提供自定义的检查逻辑
        return True 

    def get_text_response(self, response: Union[str, Dict[str, Any]]) -> str:
        """
        获取文本格式的响应
        
        将任何类型的响应转换为纯文本格式，方便客户端处理和显示。
        
        Args:
            response: 原始响应，可以是字符串或字典
            
        Returns:
            文本格式的响应
        """
        return ResponseAdapter.to_text(response) 