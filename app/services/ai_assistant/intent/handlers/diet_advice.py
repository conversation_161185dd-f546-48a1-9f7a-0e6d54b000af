"""
饮食建议处理器模块

该模块提供处理与饮食相关的用户意图的功能，包括饮食计划建议、食物营养信息查询、
营养问题解答和卡路里计算等。
"""

import logging
from typing import Dict, Any, Optional, List

from app.services.ai_assistant.intent.handlers.base import BaseIntentHandler
from app.services.ai_assistant.llm.proxy import LLMProxy
from app.services.ai_assistant.knowledge.retriever import KnowledgeRetriever

logger = logging.getLogger(__name__)

class DietAdviceHandler(BaseIntentHandler):
    """处理与饮食建议相关的用户意图的处理器"""

    def __init__(
        self, 
        llm_proxy: LLMProxy,
        knowledge_retriever: Optional[KnowledgeRetriever] = None
    ):
        """
        初始化饮食建议处理器
        
        Args:
            llm_proxy: 语言模型代理，用于生成饮食建议
            knowledge_retriever: 知识库检索器，用于检索相关饮食知识
        """
        self.llm_proxy = llm_proxy
        self.knowledge_retriever = knowledge_retriever
        logger.info("饮食建议处理器已初始化")
    
    def handle(self, intent: str, user_message: Dict[str, Any], user_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理饮食相关的意图
        
        Args:
            intent: 具体的饮食意图类型
            user_message: 用户消息内容
            user_context: 用户上下文信息
            
        Returns:
            包含响应内容的字典
        """
        logger.info(f"处理饮食建议意图: {intent}")
        
        if intent == "diet_plan":
            return self._handle_diet_plan(user_message, user_context)
        elif intent == "food_nutrition":
            return self._handle_food_nutrition(user_message, user_context)
        elif intent == "nutrition_question":
            return self._handle_nutrition_question(user_message, user_context)
        elif intent == "calorie_calculation":
            return self._handle_calorie_calculation(user_message, user_context)
        else:
            return self._handle_default(user_message, user_context)
    
    def _handle_diet_plan(self, user_message: Dict[str, Any], user_context: Dict[str, Any]) -> Dict[str, Any]:
        """处理饮食计划请求"""
        logger.info("生成个性化饮食计划")
        
        # 提取用户的饮食目标和偏好
        goal = user_message.get("goal", "")
        preferences = user_message.get("preferences", [])
        restrictions = user_message.get("restrictions", [])
        
        # 获取用户的基本信息
        weight = user_context.get("weight", 0)
        height = user_context.get("height", 0)
        age = user_context.get("age", 0)
        gender = user_context.get("gender", "")
        activity_level = user_context.get("activity_level", "moderate")
        
        # 构建提示
        prompt = f"""
        生成一个个性化饮食计划，基于以下信息:
        - 目标: {goal}
        - 饮食偏好: {', '.join(preferences) if preferences else '无特殊偏好'}
        - 饮食限制: {', '.join(restrictions) if restrictions else '无特殊限制'}
        - 体重: {weight}kg
        - 身高: {height}cm
        - 年龄: {age}岁
        - 性别: {gender}
        - 活动水平: {activity_level}
        
        提供一个包含三餐和两次零食的每日饮食计划，包括:
        1. 总体建议和注意事项
        2. 每餐的食物选择和大致份量
        3. 对于特定饮食限制的替代选项
        4. 推荐的水分摄入量
        """
        
        # 使用LLM生成饮食计划
        diet_plan = self.llm_proxy.generate(prompt, temperature=0.7)
        
        return {
            "response_type": "diet_plan",
            "content": diet_plan,
            "suggested_next_intents": ["food_nutrition", "calorie_calculation"]
        }
    
    def _handle_food_nutrition(self, user_message: Dict[str, Any], user_context: Dict[str, Any]) -> Dict[str, Any]:
        """处理食物营养信息查询"""
        logger.info("查询食物营养信息")
        
        # 提取用户查询的食物
        food_items = user_message.get("food_items", [])
        
        if not food_items:
            return {
                "response_type": "clarification",
                "content": "请告诉我您想了解哪些食物的营养信息？"
            }
        
        # 构建提示
        prompt = f"""
        提供以下食物的详细营养信息:
        {', '.join(food_items)}
        
        对于每种食物，包括:
        1. 每100克的卡路里含量
        2. 宏量营养素分布(蛋白质、碳水化合物、脂肪)
        3. 主要维生素和矿物质含量
        4. 这些食物对健康的益处
        5. 推荐的健康食用方式和份量
        """
        
        # 使用LLM生成食物营养信息
        nutrition_info = self.llm_proxy.generate(prompt, temperature=0.3)
        
        return {
            "response_type": "food_nutrition",
            "content": nutrition_info,
            "suggested_next_intents": ["nutrition_question", "diet_plan"]
        }
    
    def _handle_nutrition_question(self, user_message: Dict[str, Any], user_context: Dict[str, Any]) -> Dict[str, Any]:
        """处理营养相关问题"""
        logger.info("回答营养相关问题")
        
        question = user_message.get("question", "")
        
        if not question:
            return {
                "response_type": "clarification",
                "content": "请详细描述您的营养相关问题，以便我能提供准确的回答。"
            }
        
        # 尝试从知识库检索相关信息
        relevant_knowledge = None
        if self.knowledge_retriever:
            relevant_knowledge = self.knowledge_retriever.retrieve(question, "nutrition")
        
        # 构建提示
        prompt = f"""
        回答以下营养相关问题:
        {question}
        
        {f'参考以下知识: {relevant_knowledge}' if relevant_knowledge else ''}
        
        提供科学、准确、实用的回答，并在适当的情况下引用可靠的营养学研究或指南。
        """
        
        # 使用LLM生成回答
        answer = self.llm_proxy.generate(prompt, temperature=0.4)
        
        return {
            "response_type": "nutrition_question",
            "content": answer,
            "suggested_next_intents": ["food_nutrition", "diet_plan"]
        }
    
    def _handle_calorie_calculation(self, user_message: Dict[str, Any], user_context: Dict[str, Any]) -> Dict[str, Any]:
        """处理卡路里计算请求"""
        logger.info("计算卡路里需求")
        
        # 获取用户的基本信息
        weight = user_context.get("weight", user_message.get("weight", 0))
        height = user_context.get("height", user_message.get("height", 0))
        age = user_context.get("age", user_message.get("age", 0))
        gender = user_context.get("gender", user_message.get("gender", ""))
        activity_level = user_context.get("activity_level", user_message.get("activity_level", "moderate"))
        goal = user_message.get("goal", "maintain")
        
        if not all([weight, height, age, gender]):
            return {
                "response_type": "clarification",
                "content": "为了计算您的卡路里需求，我需要知道您的体重(kg)、身高(cm)、年龄和性别。您也可以提供您的活动水平(久坐不动/轻度活动/中度活动/高度活动)和目标(减重/维持/增重)。"
            }
        
        # 构建提示
        prompt = f"""
        计算基于以下信息的每日卡路里需求:
        - 体重: {weight}kg
        - 身高: {height}cm
        - 年龄: {age}岁
        - 性别: {gender}
        - 活动水平: {activity_level}
        - 目标: {goal}
        
        提供以下信息:
        1. 基础代谢率(BMR)
        2. 总能量消耗(TDEE)
        3. 基于目标的推荐卡路里摄入量
        4. 推荐的宏量营养素分布
        5. 根据目标的饮食建议
        """
        
        # 使用LLM生成卡路里计算和建议
        calorie_info = self.llm_proxy.generate(prompt, temperature=0.3)
        
        return {
            "response_type": "calorie_calculation",
            "content": calorie_info,
            "suggested_next_intents": ["diet_plan", "nutrition_question"]
        }
    
    def _handle_default(self, user_message: Dict[str, Any], user_context: Dict[str, Any]) -> Dict[str, Any]:
        """处理未识别的饮食相关意图"""
        logger.info("处理默认饮食相关查询")
        
        query = user_message.get("text", "")
        
        # 尝试从知识库检索相关信息
        relevant_knowledge = None
        if self.knowledge_retriever:
            relevant_knowledge = self.knowledge_retriever.retrieve(query, "nutrition")
        
        # 构建提示
        prompt = f"""
        用户查询: {query}
        
        {f'参考以下相关知识: {relevant_knowledge}' if relevant_knowledge else ''}
        
        这是一个与饮食或营养相关的查询。请提供有帮助、准确和科学的回答。
        在回答中考虑健康饮食原则和个性化的因素。
        """
        
        # 使用LLM生成回答
        response = self.llm_proxy.generate(prompt, temperature=0.5)
        
        return {
            "response_type": "general_nutrition_advice",
            "content": response,
            "suggested_next_intents": ["diet_plan", "food_nutrition", "nutrition_question"]
        } 