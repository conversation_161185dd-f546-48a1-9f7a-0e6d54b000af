"""
对话状态管理器模块

该模块提供了管理对话状态的功能，包括状态转换、持久化和恢复。
"""

import logging
import json
import time
from typing import Dict, Any, Optional, Type, List, Tuple
from datetime import datetime

from app.services.ai_assistant.conversation.states.base import ConversationState, ConversationStateFactory
from app.services.ai_assistant.conversation.states.idle import IdleState
from app.services.ai_assistant.common.cache import CacheService, default_cache_service

logger = logging.getLogger(__name__)

class ConversationStateManager:
    """
    对话状态管理器
    
    管理对话状态的转换、持久化和恢复，并提供长期记忆支持。
    """
    
    def __init__(self, cache_service: Optional[CacheService] = None):
        """
        初始化状态管理器
        
        Args:
            cache_service: 缓存服务，用于存储长期记忆
        """
        self.state_factory = ConversationStateFactory()
        self.cache_service = cache_service or default_cache_service
        
        # 注册所有状态
        self._register_states()
        
        # 会话状态存储
        self.conversations: Dict[str, Dict[str, Any]] = {}
        self.active_states: Dict[str, ConversationState] = {}
        
        # 长期记忆配置
        self.memory_ttl = 30 * 24 * 60 * 60  # 30天
        self.max_memory_items = 10  # 每个用户保存的最大记忆项数
    
    def _register_states(self):
        """注册所有状态"""
        from app.services.ai_assistant.conversation.states.idle import IdleState
        self.state_factory.register(IdleState)
        
        # 注册其他状态
        try:
            from app.services.ai_assistant.conversation.states.fitness_advice import FitnessAdviceState
            self.state_factory.register(FitnessAdviceState)
        except ImportError:
            logger.warning("无法加载健身建议状态")
        
        try:
            from app.services.ai_assistant.conversation.states.training_plan import TrainingPlanState
            self.state_factory.register(TrainingPlanState)
        except ImportError:
            logger.warning("无法加载训练计划状态")
        
        try:
            from app.services.ai_assistant.conversation.states.exercise_action import ExerciseActionState
            self.state_factory.register(ExerciseActionState)
        except ImportError:
            logger.warning("无法加载运动动作状态")
        
        try:
            from app.services.ai_assistant.conversation.states.diet_advice import DietAdviceState
            self.state_factory.register(DietAdviceState)
        except ImportError:
            logger.warning("无法加载饮食建议状态")
    
    def get_or_create_conversation(self, conversation_id: str, user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        获取或创建对话上下文
        
        Args:
            conversation_id: 对话ID
            user_id: 用户ID
            
        Returns:
            对话上下文
        """
        if conversation_id not in self.conversations:
            # 如果未提供用户ID，使用对话ID作为用户ID
            if user_id is None:
                user_id = conversation_id
                
            # 创建新的对话上下文
            current_time = datetime.now().isoformat()
            self.conversations[conversation_id] = {
                "id": conversation_id,
                "user_id": user_id,
                "messages": [],
                "current_state": "idle",
                "created_at": current_time,
                "updated_at": current_time,
                "user_profile": {},
                "fitness_preferences": {},
                "conversation_summary": "",
                "need_load_memory": True
            }
            
            # 如果有用户ID，标记需要加载长期记忆
            # 注意：我们不能直接在这里调用异步方法，所以我们只是标记需要加载
            if user_id:
                self.conversations[conversation_id]["need_load_memory"] = True
        elif user_id and "user_id" not in self.conversations[conversation_id]:
            # 如果对话存在但没有设置用户ID，设置用户ID
            self.conversations[conversation_id]["user_id"] = user_id
        
        return self.conversations[conversation_id]
    
    async def _load_long_term_memory(self, conversation_id: str, user_id: str) -> None:
        """
        加载用户的长期记忆
        
        Args:
            conversation_id: 对话ID
            user_id: 用户ID
        """
        try:
            # 加载用户资料
            profile_key = f"user_profile:{user_id}"
            user_profile = await self.cache_service.get(profile_key)
            
            if user_profile:
                self.conversations[conversation_id]["user_profile"] = user_profile
                logger.debug(f"已加载用户资料: {user_id}")
            
            # 加载健身偏好
            prefs_key = f"fitness_preferences:{user_id}"
            fitness_preferences = await self.cache_service.get(prefs_key)
            
            if fitness_preferences:
                self.conversations[conversation_id]["fitness_preferences"] = fitness_preferences
                logger.debug(f"已加载健身偏好: {user_id}")
            
            # 加载对话摘要
            summary_key = f"conversation_summary:{user_id}"
            conversation_summary = await self.cache_service.get(summary_key)
            
            if conversation_summary:
                self.conversations[conversation_id]["conversation_summary"] = conversation_summary
                logger.debug(f"已加载对话摘要: {user_id}")
                
        except Exception as e:
            logger.error(f"加载长期记忆失败: {str(e)}")
    
    async def _save_long_term_memory(self, conversation_id: str) -> None:
        """
        保存用户的长期记忆
        
        Args:
            conversation_id: 对话ID
        """
        if conversation_id not in self.conversations:
            return
        
        context = self.conversations[conversation_id]
        user_id = context.get("user_id")
        
        if not user_id:
            return
        
        try:
            # 保存用户资料
            if "user_profile" in context and context["user_profile"]:
                profile_key = f"user_profile:{user_id}"
                await self.cache_service.set(profile_key, context["user_profile"], ttl=self.memory_ttl)
            
            # 保存健身偏好
            if "fitness_preferences" in context and context["fitness_preferences"]:
                prefs_key = f"fitness_preferences:{user_id}"
                await self.cache_service.set(prefs_key, context["fitness_preferences"], ttl=self.memory_ttl)
            
            # 更新并保存对话摘要
            summary = await self._generate_conversation_summary(conversation_id)
            if summary:
                summary_key = f"conversation_summary:{user_id}"
                await self.cache_service.set(summary_key, summary, ttl=self.memory_ttl)
                
            logger.debug(f"已保存用户长期记忆: {user_id}")
                
        except Exception as e:
            logger.error(f"保存长期记忆失败: {str(e)}")
    
    async def _generate_conversation_summary(self, conversation_id: str) -> str:
        """
        生成对话摘要
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            对话摘要
        """
        if conversation_id not in self.conversations:
            return ""
        
        context = self.conversations[conversation_id]
        messages = context.get("messages", [])
        
        if len(messages) < 4:  # 至少需要2轮对话才生成摘要
            return context.get("conversation_summary", "")
        
        # 只取最近的10条消息
        recent_messages = messages[-10:]
        
        # 构建摘要文本
        summary_parts = []
        
        for i in range(0, len(recent_messages), 2):
            if i+1 < len(recent_messages):
                user_msg = recent_messages[i].get("content", "")
                assistant_msg = recent_messages[i+1].get("content", "")
                
                # 截断过长的消息
                if len(user_msg) > 100:
                    user_msg = user_msg[:97] + "..."
                if len(assistant_msg) > 150:
                    assistant_msg = assistant_msg[:147] + "..."
                
                summary_parts.append(f"用户: {user_msg}")
                summary_parts.append(f"助手: {assistant_msg}")
        
        # 构建完整摘要
        current_summary = "\n".join(summary_parts)
        
        # 如果有旧摘要，保留部分信息
        old_summary = context.get("conversation_summary", "")
        if old_summary and len(old_summary) > 300:
            # 保留旧摘要的前300个字符作为历史上下文
            combined_summary = f"历史对话:\n{old_summary[:300]}...\n\n最近对话:\n{current_summary}"
        else:
            combined_summary = current_summary
        
        # 更新上下文中的摘要
        context["conversation_summary"] = combined_summary
        
        return combined_summary
    
    def update_user_profile(self, conversation_id: str, profile_data: Dict[str, Any]) -> None:
        """
        更新用户资料
        
        Args:
            conversation_id: 对话ID
            profile_data: 用户资料数据
        """
        if conversation_id not in self.conversations:
            logger.warning(f"尝试更新不存在的对话用户资料: {conversation_id}")
            return
        
        context = self.conversations[conversation_id]
        
        # 获取当前用户资料
        current_profile = context.get("user_profile", {})
        
        # 更新资料
        current_profile.update(profile_data)
        context["user_profile"] = current_profile
        
        # 异步保存长期记忆
        import asyncio
        asyncio.create_task(self._save_long_term_memory(conversation_id))
    
    def update_fitness_preferences(self, conversation_id: str, preferences: Dict[str, Any]) -> None:
        """
        更新健身偏好
        
        Args:
            conversation_id: 对话ID
            preferences: 健身偏好数据
        """
        if conversation_id not in self.conversations:
            logger.warning(f"尝试更新不存在的对话健身偏好: {conversation_id}")
            return
        
        context = self.conversations[conversation_id]
        
        # 获取当前健身偏好
        current_prefs = context.get("fitness_preferences", {})
        
        # 更新偏好
        current_prefs.update(preferences)
        context["fitness_preferences"] = current_prefs
        
        # 异步保存长期记忆
        import asyncio
        asyncio.create_task(self._save_long_term_memory(conversation_id))
    
    def get_active_state(self, conversation_id: str) -> ConversationState:
        """
        获取当前活动的状态
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            当前状态
        """
        # 如果已有活动状态，直接返回
        if conversation_id in self.active_states:
            return self.active_states[conversation_id]
        
        # 获取或创建对话上下文
        context = self.get_or_create_conversation(conversation_id)
        
        # 创建默认状态
        current_state_name = context.get("current_state", "idle")
        state = self.state_factory.create(current_state_name, context)
        
        # 保存并返回状态
        self.active_states[conversation_id] = state
        return state
    
    async def process_message(self, conversation_id: str, message: str, user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        处理用户消息
        
        获取当前状态并处理消息，如果需要，执行状态转换。
        
        Args:
            conversation_id: 对话ID
            message: 用户消息
            user_id: 用户ID
            
        Returns:
            处理结果
        """
        # 获取当前状态
        current_state = self.get_active_state(conversation_id)
        
        # 处理消息
        result = await current_state.process_message(message)
        
        # 更新对话上下文中的消息历史
        context = self.get_or_create_conversation(conversation_id, user_id)
        
        # 添加时间戳
        current_time = int(time.time())
        context["updated_at"] = datetime.now().isoformat()
        
        # 添加消息到历史
        context["messages"].append({
            "role": "user",
            "content": message,
            "timestamp": current_time
        })
        
        context["messages"].append({
            "role": "assistant",
            "content": result.get("response", ""),
            "timestamp": current_time + 1
        })
        
        # 限制消息历史长度
        if len(context["messages"]) > 50:
            context["messages"] = context["messages"][-50:]
        
        # 检查是否需要状态转换
        if current_state.should_transition():
            # 执行状态转换
            next_state_class = current_state.get_next_state()
            
            # 退出当前状态
            current_state.exit()
            
            # 创建并进入新状态
            new_state = next_state_class(context)
            new_state.enter()
            
            # 更新活动状态
            self.active_states[conversation_id] = new_state
            
            # 更新上下文中的当前状态
            context["current_state"] = new_state.name
            
            logger.info(f"状态转换: {current_state.name} -> {new_state.name}")
        
        # 尝试从用户消息中提取用户资料和偏好
        self._extract_user_info_from_message(conversation_id, message, result.get("response", ""))
        
        # 异步保存长期记忆
        import asyncio
        asyncio.create_task(self._save_long_term_memory(conversation_id))
        
        # 返回处理结果
        return result
    
    def _extract_user_info_from_message(self, conversation_id: str, message: str, response: str) -> None:
        """
        从用户消息和回复中提取用户信息
        
        Args:
            conversation_id: 对话ID
            message: 用户消息
            response: 助手回复
        """
        context = self.conversations.get(conversation_id)
        if not context:
            return
        
        # 用户资料提取
        profile_updates = {}
        
        # 简单的规则匹配，实际系统可能使用更复杂的NLU
        if "我是" in message and len(message) < 50:
            profile_updates["name"] = message.split("我是")[1].split("，")[0].strip()
        
        if "岁" in message and "我" in message and len(message) < 50:
            age_text = message.split("岁")[0].split("我")[-1].strip()
            if age_text.isdigit():
                profile_updates["age"] = int(age_text)
        
        # 健身偏好提取
        fitness_updates = {}
        
        if "增肌" in message or "增强肌肉" in message:
            fitness_updates["goal"] = "增肌"
        elif "减脂" in message or "减肥" in message or "瘦身" in message:
            fitness_updates["goal"] = "减脂"
        elif "耐力" in message or "心肺" in message:
            fitness_updates["goal"] = "提高耐力"
            
        if "初学者" in message or "新手" in message:
            fitness_updates["level"] = "初级"
        elif "有经验" in message or "练过" in message:
            fitness_updates["level"] = "中级"
        elif "高级" in message or "专业" in message:
            fitness_updates["level"] = "高级"
        
        # 更新信息
        if profile_updates:
            self.update_user_profile(conversation_id, profile_updates)
        
        if fitness_updates:
            self.update_fitness_preferences(conversation_id, fitness_updates)
    
    async def save_conversation(self, conversation_id: str) -> bool:
        """
        保存对话状态
        
        将对话上下文保存到持久化存储。
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            是否成功保存
        """
        if conversation_id not in self.conversations:
            logger.warning(f"尝试保存不存在的对话: {conversation_id}")
            return False
        
        context = self.conversations[conversation_id]
        
        try:
            # 保存长期记忆
            await self._save_long_term_memory(conversation_id)
            
            # 保存到缓存（临时存储）
            cache_key = f"conversation:{conversation_id}"
            await self.cache_service.set(cache_key, context, ttl=86400)  # 1天
            
            logger.info(f"保存对话: {conversation_id}")
            return True
        except Exception as e:
            logger.error(f"保存对话失败: {str(e)}")
            return False
    
    async def load_conversation(self, conversation_id: str) -> bool:
        """
        加载对话状态
        
        从持久化存储加载对话上下文。
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            是否成功加载
        """
        try:
            # 如果已经加载，返回成功
            if conversation_id in self.conversations:
                return True
            
            # 从缓存加载
            cache_key = f"conversation:{conversation_id}"
            cached_context = await self.cache_service.get(cache_key)
            
            if cached_context:
                self.conversations[conversation_id] = cached_context
                logger.info(f"从缓存加载对话: {conversation_id}")
                return True
            
            # 创建默认上下文
            self.get_or_create_conversation(conversation_id)
            return True
        except Exception as e:
            logger.error(f"加载对话失败: {str(e)}")
            return False
    
    def get_conversation_summary(self, conversation_id: str) -> Dict[str, Any]:
        """
        获取对话摘要
        
        生成对话的摘要信息，包括当前状态、消息数量等。
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            对话摘要
        """
        if conversation_id not in self.conversations:
            return {
                "id": conversation_id,
                "exists": False
            }
        
        context = self.conversations[conversation_id]
        
        # 提取用户资料和健身偏好的摘要
        user_profile = context.get("user_profile", {})
        fitness_preferences = context.get("fitness_preferences", {})
        
        profile_summary = {}
        if "name" in user_profile:
            profile_summary["name"] = user_profile["name"]
        if "age" in user_profile:
            profile_summary["age"] = user_profile["age"]
        if "gender" in user_profile:
            profile_summary["gender"] = user_profile["gender"]
            
        fitness_summary = {}
        if "goal" in fitness_preferences:
            fitness_summary["goal"] = fitness_preferences["goal"]
        if "level" in fitness_preferences:
            fitness_summary["level"] = fitness_preferences["level"]
        
        return {
            "id": conversation_id,
            "exists": True,
            "user_id": context.get("user_id"),
            "current_state": context.get("current_state", "idle"),
            "message_count": len(context.get("messages", [])),
            "created_at": context.get("created_at", ""),
            "updated_at": context.get("updated_at", ""),
            "user_profile": profile_summary,
            "fitness_preferences": fitness_summary,
            "has_summary": bool(context.get("conversation_summary"))
        }
    
    async def get_conversation_history(self, conversation_id: str, max_messages: int = 10) -> List[Dict[str, Any]]:
        """
        获取对话历史
        
        Args:
            conversation_id: 对话ID
            max_messages: 最大返回的消息数量
            
        Returns:
            对话历史消息列表
        """
        if conversation_id not in self.conversations:
            return []
        
        messages = self.conversations[conversation_id].get("messages", [])
        return messages[-max_messages:] if messages else []
    
    async def get_user_profile(self, conversation_id: str) -> Dict[str, Any]:
        """
        获取用户资料
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            用户资料
        """
        if conversation_id not in self.conversations:
            return {}
        
        return self.conversations[conversation_id].get("user_profile", {})
    
    async def get_fitness_preferences(self, conversation_id: str) -> Dict[str, Any]:
        """
        获取健身偏好
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            健身偏好
        """
        if conversation_id not in self.conversations:
            return {}
        
        return self.conversations[conversation_id].get("fitness_preferences", {})
    
    async def reset_conversation(self, conversation_id: str) -> None:
        """
        重置对话
        
        清除对话历史，但保留用户资料和偏好。
        
        Args:
            conversation_id: 对话ID
        """
        if conversation_id not in self.conversations:
            return
        
        # 获取当前上下文
        context = self.conversations[conversation_id]
        
        # 保存用户资料和偏好
        user_id = context.get("user_id")
        user_profile = context.get("user_profile", {})
        fitness_preferences = context.get("fitness_preferences", {})
        
        # 重置上下文
        current_time = datetime.now().isoformat()
        self.conversations[conversation_id] = {
            "id": conversation_id,
            "user_id": user_id,
            "messages": [],
            "current_state": "idle",
            "created_at": current_time,
            "updated_at": current_time,
            "user_profile": user_profile,
            "fitness_preferences": fitness_preferences,
            "conversation_summary": "",
            "need_load_memory": True
        }
        
        # 重置活动状态
        if conversation_id in self.active_states:
            del self.active_states[conversation_id]
        
        logger.info(f"重置对话: {conversation_id}")
    
    async def get_current_state(self, conversation_id: str) -> ConversationState:
        """
        获取当前状态
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            当前状态
        """
        # 检查是否需要加载长期记忆
        context = self.get_or_create_conversation(conversation_id)
        if context.get("need_load_memory"):
            user_id = context.get("user_id")
            if user_id:
                await self._load_long_term_memory(conversation_id, user_id)
                context["need_load_memory"] = False
        
        return self.get_active_state(conversation_id)
    
    async def transition_state(
        self, 
        conversation_id: str, 
        intent: str, 
        message: str, 
        response: str
    ) -> ConversationState:
        """
        转换状态
        
        Args:
            conversation_id: 对话ID
            intent: 意图
            message: 用户消息
            response: 助手回复
            
        Returns:
            新状态
        """
        # 获取当前状态
        current_state = self.get_active_state(conversation_id)
        
        # 检查是否需要状态转换
        state_changed = False
        
        # 如果当前状态已指示需要转换
        if current_state.should_transition():
            next_state_class = current_state.get_next_state()
            state_changed = True
        # 否则，根据意图找到合适的状态
        elif not current_state.can_handle(intent):
            # 寻找可以处理该意图的状态
            next_state_class = self.state_factory.find_state_for_intent(intent)
            if next_state_class:
                state_changed = True
        
        # 如果需要转换状态
        if state_changed:
            context = self.get_or_create_conversation(conversation_id)
            
            # 退出当前状态
            current_state.exit()
            
            # 创建并进入新状态
            new_state = next_state_class(context)
            new_state.enter()
            
            # 更新活动状态
            self.active_states[conversation_id] = new_state
            
            # 更新上下文中的当前状态
            context["current_state"] = new_state.name
            
            logger.info(f"状态转换: {current_state.name} -> {new_state.name}")
            
            return new_state
        
        return current_state
    
    async def transition_to(
        self, 
        conversation_id: str, 
        state_name: str, 
        context_updates: Optional[Dict[str, Any]] = None
    ) -> ConversationState:
        """
        直接转换到指定状态
        
        Args:
            conversation_id: 对话ID
            state_name: 目标状态名称
            context_updates: 需要更新的上下文内容
            
        Returns:
            新状态
        """
        # 获取当前上下文
        context = self.get_or_create_conversation(conversation_id)
        
        # 更新上下文
        if context_updates:
            context.update(context_updates)
        
        # 获取当前状态
        current_state = self.get_active_state(conversation_id)
        
        # 退出当前状态
        current_state.exit()
        
        # 创建新状态
        from app.services.ai_assistant.conversation.states.fitness_advice import FitnessAdviceState
        from app.services.ai_assistant.conversation.states.idle import IdleState
        
        if state_name == "fitness_advice":
            new_state_class = FitnessAdviceState
        elif state_name == "idle":
            new_state_class = IdleState
        else:
            # 默认使用空闲状态
            new_state_class = IdleState
            logger.warning(f"未找到状态 '{state_name}'，使用默认状态")
        
        # 创建并进入新状态
        new_state = new_state_class(context)
        new_state.enter()
        
        # 更新活动状态
        self.active_states[conversation_id] = new_state
        
        # 更新上下文中的当前状态
        context["current_state"] = new_state.name
        
        logger.info(f"状态转换: {current_state.name} -> {new_state.name}")
        
        return new_state

    async def update_state(self, conversation_id: str, state: ConversationState) -> None:
        """
        更新对话状态
        
        Args:
            conversation_id: 对话ID
            state: 新状态
        """
        # 更新活动状态
        self.active_states[conversation_id] = state
        
        # 更新上下文
        if conversation_id in self.conversations:
            self.conversations[conversation_id].update(state.context)
            self.conversations[conversation_id]["current_state"] = state.name
        
        logger.debug(f"已更新对话 {conversation_id} 的状态: {state.name}")
        
        # 尝试保存长期记忆
        if "user_id" in self.conversations[conversation_id]:
            await self._save_long_term_memory(conversation_id)

    async def _save_state_to_storage(self, conversation_id: str, data: Dict[str, Any]) -> bool:
        """
        保存状态到持久化存储
        
        Args:
            conversation_id: 对话ID
            data: 要保存的数据
            
        Returns:
            是否成功保存
        """
        # 目前简单实现，使用缓存服务作为持久化存储
        if self.cache_service:
            key = f"state:{conversation_id}"
            await self.cache_service.set(key, data, ttl=self.memory_ttl)
            logger.debug(f"已保存状态到存储: {conversation_id}")
            return True
        
        return False
    
    async def _load_state_from_storage(self, conversation_id: str) -> Optional[Dict[str, Any]]:
        """
        从持久化存储加载状态
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            加载的状态数据，如果不存在则返回None
        """
        # 目前简单实现，使用缓存服务作为持久化存储
        if self.cache_service:
            key = f"state:{conversation_id}"
            data = await self.cache_service.get(key)
            if data:
                logger.debug(f"已从存储加载状态: {conversation_id}")
                return data
        
        logger.debug(f"未找到存储的状态: {conversation_id}")
        return None
    
    async def persist_state(self, conversation_id: str) -> bool:
        """
        持久化对话状态
        
        Args:
            conversation_id: 对话ID
            
        Returns:
            是否成功持久化
        """
        if conversation_id not in self.conversations:
            return False
        
        # 获取当前状态类型
        current_state = self.get_active_state(conversation_id)
        state_type = current_state.__class__.__name__ if current_state else "idle"
        
        # 准备要保存的数据
        data = {
            "state_type": state_type,
            "context": self.conversations[conversation_id]
        }
        
        # 保存到存储
        return await self._save_state_to_storage(conversation_id, data)


# 创建全局实例
conversation_state_manager = ConversationStateManager() 