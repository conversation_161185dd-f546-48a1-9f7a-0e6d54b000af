"""
对话状态基类模块

该模块定义了对话状态的基础接口和基类，提供状态转换和上下文管理的标准方法。
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Type, ClassVar

logger = logging.getLogger(__name__)

class ConversationState(ABC):
    """
    对话状态基类
    
    所有具体对话状态必须继承该类并实现其抽象方法。
    """
    
    # 状态名称，子类需要重写
    name: ClassVar[str] = "base"
    
    def __init__(self, context: Dict[str, Any]):
        """
        初始化状态
        
        Args:
            context: 对话上下文
        """
        self.context = context
        
        # 从上下文中提取用户ID，如果存在
        self.user_id = context.get("id", "")
        if not self.user_id and "user_id" in context:
            self.user_id = context["user_id"]
        
    @abstractmethod
    async def process_message(self, message: str) -> Dict[str, Any]:
        """
        处理用户消息
        
        Args:
            message: 用户输入的消息
            
        Returns:
            处理结果，包含响应和新的状态信息
        """
        pass
    
    async def handle_message(self, message: str, intent: Optional[str] = None, user_info: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        处理用户消息的接口适配器
        
        此方法用于兼容 orchestrator 的调用方式，调用具体状态的 process_message 方法
        
        Args:
            message: 用户输入的消息
            intent: 识别出的意图
            user_info: 用户信息
            
        Returns:
            处理结果，包含响应和新的状态信息
        """
        # 更新上下文中的用户信息
        if user_info:
            self.update_context({"user_info": user_info})
        
        # 如果提供了意图，记录到上下文
        if intent:
            self.update_context({"current_intent": intent})
        
        # 调用具体状态的处理方法
        return await self.process_message(message)
    
    @abstractmethod
    def should_transition(self) -> bool:
        """
        检查是否应该转换到新状态
        
        Returns:
            如果应该转换，返回True；否则返回False
        """
        pass
    
    @abstractmethod
    def get_next_state(self) -> Type['ConversationState']:
        """
        获取下一个状态
        
        Returns:
            下一个状态的类
        """
        pass
    
    def enter(self) -> None:
        """
        进入状态时执行的操作
        """
        logger.info(f"进入状态: {self.name}")
        self.context["current_state"] = self.name
    
    def exit(self) -> None:
        """
        退出状态时执行的操作
        """
        logger.info(f"退出状态: {self.name}")
        
    def update_context(self, updates: Dict[str, Any]) -> None:
        """
        更新对话上下文
        
        Args:
            updates: 需要更新的键值对
        """
        self.context.update(updates)
        
    @classmethod
    def can_handle(cls, intent: str) -> bool:
        """
        检查该状态是否可以处理指定的意图
        
        Args:
            intent: 意图类型
            
        Returns:
            如果可以处理该意图，返回True；否则返回False
        """
        # 默认实现：子类可以覆盖此方法以提供自定义的检查逻辑
        return False

    async def handle_message_stream(self, message: str, intent: Optional[str] = None, user_info: Optional[Dict[str, Any]] = None):
        """
        流式处理用户消息的接口适配器
        
        此方法用于兼容 orchestrator 的流式调用方式，默认实现是调用 handle_message 并包装为流式响应
        
        Args:
            message: 用户输入的消息
            intent: 识别出的意图
            user_info: 用户信息
            
        Yields:
            流式响应内容，可能是字符串或字典
        """
        # 调用非流式处理方法
        result = await self.handle_message(message, intent, user_info)
        
        # 将结果包装为流式响应
        if isinstance(result, dict):
            response_content = result.get("response_content", result.get("response", ""))
            if response_content:
                # 发送完整消息
                yield {
                    "type": "message",
                    "content": response_content,
                    "role": "assistant",
                    "meta_info": {
                        "intent": result.get("intent_type", intent),
                        "confidence": result.get("confidence", 1.0),
                        "transitioning": result.get("transitioning", False),
                        "next_state": result.get("next_state")
                    }
                }
        elif isinstance(result, str):
            # 发送文本消息
            yield {
                "type": "message",
                "content": result,
                "role": "assistant",
                "meta_info": {
                    "intent": intent,
                    "confidence": 1.0
                }
            }
        else:
            # 发送默认响应
            yield {
                "type": "message",
                "content": "抱歉，我无法处理您的请求。",
                "role": "assistant",
                "meta_info": {
                    "intent": "error",
                    "confidence": 0.0
                }
            }


class ConversationStateFactory:
    """
    对话状态工厂
    
    用于创建和管理对话状态实例。
    """
    
    def __init__(self):
        """初始化状态工厂"""
        self._states: Dict[str, Type[ConversationState]] = {}
        
    def register(self, state_class: Type[ConversationState]) -> None:
        """
        注册状态类
        
        Args:
            state_class: 状态类
        """
        self._states[state_class.name] = state_class
        logger.info(f"注册状态: {state_class.name}")
        
    def create(self, state_name: str, context: Dict[str, Any]) -> ConversationState:
        """
        创建状态实例
        
        Args:
            state_name: 状态名称
            context: 对话上下文
            
        Returns:
            状态实例
        """
        if state_name not in self._states:
            logger.warning(f"未找到状态: {state_name}，使用默认状态")
            # 使用IdleState作为默认状态
            from app.services.ai_assistant.conversation.states.idle import IdleState
            return IdleState(context)
        
        return self._states[state_name](context)
    
    def create_for_intent(self, intent: str, context: Dict[str, Any]) -> Optional[ConversationState]:
        """
        根据意图创建适当的状态
        
        Args:
            intent: 意图类型
            context: 对话上下文
            
        Returns:
            能处理该意图的状态实例，如果没有找到则返回None
        """
        for state_class in self._states.values():
            if state_class.can_handle(intent):
                return state_class(context)
        
        return None
    
    def get_default_state(self, context: Dict[str, Any]) -> ConversationState:
        """
        获取默认状态
        
        Args:
            context: 对话上下文
            
        Returns:
            默认状态实例
        """
        # 使用IdleState作为默认状态
        from app.services.ai_assistant.conversation.states.idle import IdleState
        return IdleState(context)
    
    def find_state_for_intent(self, intent: str) -> Optional[Type[ConversationState]]:
        """
        根据意图查找能处理该意图的状态类
        
        Args:
            intent: 意图类型
            
        Returns:
            能处理该意图的状态类，如果没有找到则返回None
        """
        for state_class in self._states.values():
            if state_class.can_handle(intent):
                return state_class
        
        return None 