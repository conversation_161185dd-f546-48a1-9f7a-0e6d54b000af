"""
对话状态包

该包包含所有对话状态的定义和管理类。
"""

# 导入基础组件
from app.services.ai_assistant.conversation.states.base import ConversationState, ConversationStateFactory
from app.services.ai_assistant.conversation.states.manager import ConversationStateManager, conversation_state_manager

# 导入具体状态
from app.services.ai_assistant.conversation.states.idle import IdleState

# 有条件地导入其他状态，避免导入错误
try:
    from app.services.ai_assistant.conversation.states.fitness_advice import FitnessAdviceState
except ImportError:
    pass

# 导出所有组件
__all__ = [
    'ConversationState',
    'ConversationStateFactory',
    'ConversationStateManager',
    'conversation_state_manager',
    'IdleState',
]
