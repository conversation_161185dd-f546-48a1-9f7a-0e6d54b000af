"""
意图转换管理器

负责处理意图变更、强制切换等逻辑，确保意图之间的平滑转换。
"""

import logging
from typing import Dict, Any, Optional, Tuple, List

from app.services.meta_info_manager import MetaInfoManager

logger = logging.getLogger(__name__)

# 定义意图切换关键词
INTENT_SWITCH_KEYWORDS = {
    "停止": "general_chat",
    "取消": "general_chat",
    "换个话题": "general_chat",
    "不想继续": "general_chat",
    "聊点别的": "general_chat",
    "换一个": "general_chat",
    "重新开始": "general_chat"
}

# 定义高置信度阈值
HIGH_CONFIDENCE_THRESHOLD = 0.85


class IntentTransitionManager:
    """意图转换管理器，负责处理意图变更、强制切换等逻辑"""

    @staticmethod
    def check_forced_intent_switch(message: str) -> Optional[Tuple[str, float]]:
        """检查用户消息是否包含强制意图切换关键词

        Args:
            message: 用户消息

        Returns:
            如果包含强制意图切换关键词，返回(目标意图, 置信度)，否则返回None
        """
        for keyword, target_intent in INTENT_SWITCH_KEYWORDS.items():
            if keyword in message:
                logger.info(f"检测到意图切换关键词: '{keyword}'，强制切换到意图: {target_intent}")
                return (target_intent, 0.95)  # 高置信度

        return None

    @staticmethod
    def handle_forced_intent_switch(meta_info: Dict[str, Any], target_intent: str, confidence: float) -> Dict[str, Any]:
        """处理强制意图切换

        Args:
            meta_info: 原始元数据
            target_intent: 目标意图
            confidence: 置信度

        Returns:
            更新后的元数据
        """
        # 保留基本信息
        preserved_keys = ["user_id", "db_message_id", "session_id", "conversation_id"]
        preserved_data = {k: v for k, v in meta_info.items() if k in preserved_keys}

        # 设置新意图
        preserved_data["intent"] = target_intent
        preserved_data["confidence"] = confidence

        # 记录原始意图（如果有）
        original_intent = meta_info.get("intent")
        if original_intent:
            logger.info(f"强制意图切换: {original_intent} -> {target_intent}，重置所有参数")
        else:
            logger.info(f"强制设置意图: {target_intent}，重置所有参数")

        return preserved_data

    @staticmethod
    def should_preserve_intent(meta_info: Dict[str, Any], new_intent: str, new_confidence: float) -> bool:
        """判断是否应该保留原始意图

        Args:
            meta_info: 当前元数据
            new_intent: 新识别的意图
            new_confidence: 新意图的置信度

        Returns:
            是否应该保留原始意图
        """
        # 检查是否处于参数收集状态
        is_collecting_params = meta_info.get("collecting_training_params", False)
        is_waiting_for_info = meta_info.get("waiting_for_info") is not None

        # 如果处于参数收集状态且新意图置信度不够高，保留原始意图
        if (is_collecting_params or is_waiting_for_info) and new_confidence < HIGH_CONFIDENCE_THRESHOLD:
            original_intent = meta_info.get("intent")
            if original_intent:
                logger.info(f"正在收集参数，保持原始意图: {original_intent}，忽略低置信度新意图: {new_intent} ({new_confidence})")
                return True

        return False

    @staticmethod
    def prepare_meta_info_for_new_conversation(
        message: str,
        last_meta_info: Optional[Dict[str, Any]] = None,
        last_message_time = None,
        current_time = None,
        db_message_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """为新会话准备元数据，处理强制意图切换、参数收集状态和会话超时

        Args:
            message: 用户消息
            last_meta_info: 上一条消息的元数据，可选
            last_message_time: 上一条消息的时间，可选
            current_time: 当前时间，可选
            db_message_id: 当前消息的数据库ID，可选

        Returns:
            准备好的元数据
        """
        # 初始化元数据
        initial_meta_info = {}

        # 检查强制意图切换
        forced_switch = IntentTransitionManager.check_forced_intent_switch(message)

        if forced_switch:
            # 处理强制意图切换
            target_intent, confidence = forced_switch
            initial_meta_info = IntentTransitionManager.handle_forced_intent_switch({}, target_intent, confidence)
            logger.info(f"检测到强制意图切换关键词，设置意图: {target_intent}")
        elif last_meta_info:
            # 检查会话时间间隔
            if last_message_time and current_time:
                # 使用MetaInfoManager检查会话超时
                last_meta_info = MetaInfoManager.check_session_timeout(
                    last_message_time, current_time, last_meta_info
                )
                logger.info(f"会话时间检查后的元数据: {last_meta_info}")

            # 检查是否处于参数收集状态
            is_collecting_params = last_meta_info.get("collecting_training_params", False)
            is_waiting_for_info = last_meta_info.get("waiting_for_info") is not None

            if is_collecting_params or is_waiting_for_info:
                # 如果处于参数收集状态，保留相关元数据
                original_intent = last_meta_info.get("intent")
                initial_meta_info = MetaInfoManager.merge_meta_info({}, last_meta_info, original_intent)
                logger.info(f"处于参数收集状态，合并元数据: {initial_meta_info}")
            else:
                # 不在参数收集状态，但可能有其他需要保留的元数据
                # 保留基本信息
                preserved_keys = ["user_id", "session_id", "conversation_id"]
                for key in preserved_keys:
                    if key in last_meta_info:
                        initial_meta_info[key] = last_meta_info[key]

        # 更新 db_message_id 和 clientMessageId
        if db_message_id:
            initial_meta_info["db_message_id"] = db_message_id
            initial_meta_info["clientMessageId"] = db_message_id
            logger.info(f"更新消息ID: db_message_id={db_message_id}")

        return initial_meta_info

    @staticmethod
    def handle_intent_transition(current_meta_info: Dict[str, Any], new_intent_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理意图转换，确保平滑过渡

        Args:
            current_meta_info: 当前元数据
            new_intent_data: 新意图数据，包含intent和confidence

        Returns:
            更新后的元数据
        """
        # 获取当前意图和新意图
        current_intent = current_meta_info.get("intent")
        new_intent = new_intent_data.get("intent")
        new_confidence = new_intent_data.get("confidence", 0)

        # 如果没有意图变更，直接返回
        if not new_intent or current_intent == new_intent:
            return current_meta_info

        # 检查是否应该保留原始意图
        if IntentTransitionManager.should_preserve_intent(current_meta_info, new_intent, new_confidence):
            return current_meta_info

        # 处理意图转换
        update_meta_info = {"intent": new_intent, "confidence": new_confidence}
        result = MetaInfoManager.merge_meta_info(current_meta_info, update_meta_info)

        return result
