from typing import Dict, List, Optional, Any, Callable, TypeVar, Union
from typing_extensions import TypedDict
# 避免命名冲突，不直接导入AnyMessage
# from langchain_core.messages import AnyMessage
from dataclasses import dataclass, field
import logging
import json
import time
from datetime import datetime

logger = logging.getLogger(__name__)

# 创建自定义消息类
@dataclass
class CustomAnyMessage:
    role: str
    content: str
    type: str = "human"  # 默认类型
    additional_kwargs: Dict[str, Any] = field(default_factory=dict)

# 使用自定义消息类
AnyMessage = CustomAnyMessage

T = TypeVar('T')

# 自定义消息管理器，替代 langgraph.graph.add_messages
def add_messages(left: List[AnyMessage], right: Optional[List[AnyMessage]]) -> List[AnyMessage]:
    """添加消息到历史记录，并进行智能压缩"""
    if right is None:
        return left

    # 合并消息
    combined = left + right

    # 如果消息数量超过阈值，进行压缩
    MAX_MESSAGES = 20  # 最大保留消息数
    if len(combined) > MAX_MESSAGES:
        # 保留最新的消息
        preserved_messages = combined[-MAX_MESSAGES:]
        logger.debug(f"消息历史已压缩，从{len(combined)}条减少到{len(preserved_messages)}条")
        return preserved_messages

    return combined

def update_dialog_state(left: List[str], right: Optional[str]) -> List[str]:
    """更新对话状态栈 - 推入或弹出专家模式"""
    if right is None:
        return left
    if right == "pop":
        return left[:-1] if left else []
    return left + [right]

def update_dict(left: Dict[str, Any], right: Dict[str, Any]) -> Dict[str, Any]:
    """合并字典更新，保留旧值并添加/更新新值"""
    result = left.copy()
    if right:
        result.update(right)
    return result

def update_training_params(left: Dict[str, Any], right: Dict[str, Any]) -> Dict[str, Any]:
    """更新训练参数，进行类型转换和验证"""
    result = left.copy()
    if not right:
        return result

    # 处理特殊字段
    for key, value in right.items():
        # 标准化身体部位
        if key == "body_part" and isinstance(value, str):
            # 将中文部位名称映射到标准名称
            body_part_mapping = {
                "胸": "chest", "胸部": "chest", "胸肌": "chest",
                "背": "back", "背部": "back",
                "腿": "legs", "腿部": "legs",
                "肩": "shoulders", "肩部": "shoulders", "肩膀": "shoulders",
                "手臂": "arms", "臂": "arms",
                "核心": "core", "腹部": "core", "腹肌": "core"
            }
            if value in body_part_mapping:
                result[key] = body_part_mapping[value]
            else:
                result[key] = value
        # 标准化训练场景
        elif key == "training_scene" and isinstance(value, str):
            scene_mapping = {
                "健身房": "gym", "房": "gym",
                "家": "home", "家里": "home", "家庭": "home"
            }
            if value.lower() in scene_mapping:
                result[key] = scene_mapping[value]
            else:
                result[key] = value
        else:
            result[key] = value

    return result

# 用于状态管理的装饰器，替代 Annotated
def state_updater(updater_func: Callable[[T, Optional[T]], T]):
    """状态更新装饰器，用于替代 Annotated"""
    def decorator(cls):
        cls._updaters = getattr(cls, '_updaters', {})
        for key, hint in cls.__annotations__.items():
            cls._updaters[key] = updater_func
        return cls
    return decorator

# 消息历史压缩函数
def compress_message_history(messages: List[AnyMessage], max_tokens: int = 4000) -> List[AnyMessage]:
    """压缩消息历史，保持在token限制内"""
    if not messages:
        return []

    # 估算当前消息的总token数（简单估算：每个单词约1.3个token）
    def estimate_tokens(msg: AnyMessage) -> int:
        content = msg.content if hasattr(msg, 'content') else ""
        # 简单估算：每个字符约0.5个token（中文），每个单词约1.3个token（英文）
        return len(content) * 0.5

    total_tokens = sum(estimate_tokens(msg) for msg in messages)

    # 如果未超过限制，直接返回
    if total_tokens <= max_tokens:
        return messages

    # 保留最新的几条消息
    preserved_count = min(10, len(messages))
    preserved_messages = messages[-preserved_count:]
    preserved_tokens = sum(estimate_tokens(msg) for msg in preserved_messages)

    # 如果仅保留的消息已经超过限制，进一步减少
    if preserved_tokens > max_tokens:
        # 只保留最新的用户消息和AI回复
        for i in range(len(preserved_messages)-1, -1, -1):
            if len(preserved_messages) <= 4:  # 至少保留2轮对话
                break
            if preserved_tokens <= max_tokens:
                break
            msg = preserved_messages[i]
            preserved_tokens -= estimate_tokens(msg)
            preserved_messages.pop(i)

    # 创建一个系统消息，表示历史已压缩
    try:
        system_msg = AnyMessage(
            role="system",
            content="[注意：为了保持对话效率，较早的对话历史已被压缩]",
            type="system"
        )
        return [system_msg] + preserved_messages
    except TypeError:
        # 如果AnyMessage不支持type参数，使用简化版本
        system_msg = AnyMessage(
            role="system",
            content="[注意：为了保持对话效率，较早的对话历史已被压缩]"
        )
        return [system_msg] + preserved_messages

class FitnessAssistantState(TypedDict):
    # 对话消息历史
    messages: List[AnyMessage]  # 使用自定义的 add_messages 更新

    # 用户信息 - 包含用户的个人资料
    user_info: Dict[str, Any]  # 使用 update_dict 更新

    # 训练参数 - 收集的训练相关参数
    training_params: Dict[str, Any]  # 使用 update_training_params 更新

    # 元数据 - 存储会话状态和上下文信息
    metadata: Dict[str, Any]  # 使用 update_dict 更新

    # 对话流程状态 - 用于跟踪当前专家工作流
    dialog_state: List[str]  # 使用 update_dialog_state 更新

# 为每个键定义更新函数
UPDATERS = {
    "messages": add_messages,
    "user_info": update_dict,
    "training_params": update_training_params,
    "metadata": update_dict,
    "dialog_state": update_dialog_state
}

def update_state(state: FitnessAssistantState, updates: Dict[str, Any]) -> FitnessAssistantState:
    """更新状态，应用适当的更新函数到每个键"""
    result = state.copy()
    for key, value in updates.items():
        if key in UPDATERS:
            updater = UPDATERS[key]
            result[key] = updater(state.get(key, []), value)
    return result

@dataclass
class ConversationState:
    """健身AI助手对话状态定义"""

    # 消息历史
    messages: List[AnyMessage] = field(default_factory=list)

    # 用户信息
    user_info: Dict[str, Any] = field(default_factory=dict)

    # 训练参数
    training_params: Dict[str, Any] = field(default_factory=dict)

    # 元数据（包含会话状态、意图等信息）
    meta_info: Dict[str, Any] = field(default_factory=dict)

    # 内部流程状态
    flow_state: Dict[str, Any] = field(default_factory=dict)

    # 会话ID - 用于数据库关联
    session_id: str = field(default_factory=lambda: f"session_{int(time.time())}")

    # 最后更新时间
    last_updated: float = field(default_factory=time.time)

    def compress(self, max_tokens: int = 4000) -> 'ConversationState':
        """压缩状态，减少内存占用"""
        # 创建新状态对象
        compressed = ConversationState(
            messages=compress_message_history(self.messages, max_tokens),
            user_info=self.user_info.copy(),
            training_params=self.training_params.copy(),
            meta_info=self.meta_info.copy(),
            flow_state=self.flow_state.copy(),
            session_id=self.session_id,
            last_updated=time.time()
        )
        return compressed

    def to_dict(self) -> Dict[str, Any]:
        """将状态转换为可序列化的字典"""
        # 处理消息列表
        messages_dict = []
        for msg in self.messages:
            if hasattr(msg, 'to_dict'):
                messages_dict.append(msg.to_dict())
            else:
                messages_dict.append({
                    "role": msg.role,
                    "content": msg.content,
                    "type": getattr(msg, "type", "human"),
                    "additional_kwargs": getattr(msg, "additional_kwargs", {})
                })

        return {
            "messages": messages_dict,
            "user_info": self.user_info,
            "training_params": self.training_params,
            "meta_info": self.meta_info,
            "flow_state": self.flow_state,
            "session_id": self.session_id,
            "last_updated": self.last_updated
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ConversationState':
        """从字典创建状态对象"""
        # 处理消息列表
        messages = []
        for msg_dict in data.get("messages", []):
            try:
                messages.append(AnyMessage(
                    role=msg_dict["role"],
                    content=msg_dict["content"],
                    type=msg_dict.get("type", "human"),
                    additional_kwargs=msg_dict.get("additional_kwargs", {})
                ))
            except TypeError:
                # 如果AnyMessage不支持某些参数，使用简化版本
                messages.append(AnyMessage(
                    role=msg_dict["role"],
                    content=msg_dict["content"]
                ))

        return cls(
            messages=messages,
            user_info=data.get("user_info", {}),
            training_params=data.get("training_params", {}),
            meta_info=data.get("meta_info", {}),
            flow_state=data.get("flow_state", {}),
            session_id=data.get("session_id", f"session_{int(time.time())}"),
            last_updated=data.get("last_updated", time.time())
        )

    def get_selective_state(self) -> Dict[str, Any]:
        """获取选择性状态，只包含需要持久化的关键信息"""
        return {
            "intent": self.flow_state.get("intent"),
            "active_flow": self.flow_state.get("active_flow"),
            "training_params": self.training_params,
            "last_message_id": self.meta_info.get("last_message_id"),
            "session_id": self.session_id,
            "last_updated": self.last_updated
        }