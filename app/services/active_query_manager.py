from typing import List, Dict, Any, Optional, Tuple
from pydantic import BaseModel
import logging
import re

logger = logging.getLogger(__name__)


class UserInfoStatus(BaseModel):
    """用户信息状态模型"""
    missing_fields: List[str]
    complete: bool
    completion_percentage: float


class ActiveQueryManager:
    """主动查询管理器，用于检查和获取所需用户信息"""
    
    def __init__(self, llm_service):
        """初始化主动查询管理器
        
        Args:
            llm_service: LLM服务，用于生成自然询问消息
        """
        self.llm_service = llm_service
        
        # 不同意图所需的用户信息字段
        self.required_fields_mapping = {
            "daily_workout_plan": [
                "height", "weight", "fitness_goal", "experience_level", 
                "health_conditions", "activity_level"
            ],
            "weekly_training_plan": [
                "height", "weight", "fitness_goal", "experience_level", 
                "health_conditions", "activity_level"
            ],
            "diet_advice": [
                "height", "weight", "age", "gender", "fitness_goal", 
                "activity_level", "allergies"
            ],
            "body_fat_calculation": ["height", "weight", "gender", "age"],
            "calorie_calculation": ["height", "weight", "age", "gender", "activity_level"]
        }
        
        # 用户信息字段的中文名称映射
        self.field_name_map = {
            "height": "身高",
            "weight": "体重",
            "age": "年龄",
            "gender": "性别",
            "fitness_goal": "健身目标",
            "experience_level": "健身经验水平",
            "activity_level": "日常活动水平",
            "health_conditions": "健康状况",
            "allergies": "过敏源"
        }
        
        # 用户信息字段的枚举值映射
        self.field_enum_map = {
            "fitness_goal": {
                1: "减脂",
                2: "保持体型",
                3: "增肌"
            },
            "experience_level": {
                1: "初学者",
                2: "中级",
                3: "高级"
            },
            "gender": {
                0: "未知",
                1: "男性",
                2: "女性"
            },
            "activity_level": {
                1: "久坐少动",
                2: "轻度活动",
                3: "中度活动",
                4: "高度活动"
            }
        }
        
        # 参数模式字典 - 用于识别特定参数
        self.param_patterns = {
            "body_part": [
                (r"(训练|练习|锻炼)[：:]?\s*([^，。？！,\.!?]+)(部位|肌肉)", 2),
                (r"(部位|肌肉)[：:]?\s*([^，。？！,\.!?]+)", 2),
                (r"想练(?:习|好)?([^，。？！,\.!?]+)(?:部位|肌肉)?", 1),
            ],
            "training_scene": [
                (r"(场景|地点|环境)[：:]?\s*([^，。？！,\.!?]+)", 2),
                (r"在([^，。？！,\.!?]*(?:家里|家庭|健身房|户外|公园)[^，。？！,\.!?]*)", 1),
            ],
            "plan_type": [
                (r"(计划类型|计划周期)[：:]?\s*([^，。？！,\.!?]+)", 2),
                (r"([^，。？！,\.!?]*(?:单日|一天|周计划|一周|每周)[^，。？！,\.!?]*)[计划|训练]", 1),
            ],
            "equipment": [
                (r"(器材|设备)[：:]?\s*([^，。？！,\.!?]+)", 2),
                (r"使用(?:的)?([^，。？！,\.!?]*(?:哑铃|杠铃|器械|拉力带|壶铃)[^，。？！,\.!?]*)", 1),
                (r"有([^，。？！,\.!?]*(?:哑铃|杠铃|器械|拉力带|壶铃)[^，。？！,\.!?]*)", 1),
            ],
            "goal": [
                (r"(目标|目的)[：:]?\s*([^，。？！,\.!?]+)", 2),
                (r"(想要|希望)([^，。？！,\.!?]*(?:增肌|减脂|塑形|增强|提高)[^，。？！,\.!?]*)", 2),
                (r"为了([^，。？！,\.!?]*(?:增肌|减脂|塑形|增强|提高)[^，。？！,\.!?]*)", 1),
            ],
        }
        
        # 部位映射字典 - 用于标准化部位名称
        self.body_part_mapping = {
            "胸": "胸部", "胸肌": "胸部", "胸大肌": "胸部", "上胸": "胸部", "下胸": "胸部",
            "背": "背部", "背肌": "背部", "背阔肌": "背部", "上背": "背部", "下背": "背部",
            "腿": "腿部", "腿部肌肉": "腿部", "大腿": "腿部", "小腿": "腿部", "股四头肌": "腿部",
            "肩": "肩部", "肩膀": "肩部", "三角肌": "肩部", "前束": "肩部", "侧束": "肩部", "后束": "肩部",
            "手臂": "臂部", "臂": "臂部", "二头肌": "臂部", "三头肌": "臂部", "肱二头肌": "臂部", "肱三头肌": "臂部",
            "核心": "核心", "腹": "核心", "腹肌": "核心", "腹直肌": "核心", "腹外斜肌": "核心",
            "手腕": "前臂", "前臂": "前臂", "小臂": "前臂", "握力": "前臂"
        }
    
    def check_user_info_status(
        self, 
        intent: str, 
        user_data: Dict[str, Any], 
        skipped_fields: Optional[List[str]] = None
    ) -> UserInfoStatus:
        """检查用户信息状态，返回缺失字段和完成度
        
        Args:
            intent: 意图类型
            user_data: 用户数据字典
            skipped_fields: 已跳过的字段列表
            
        Returns:
            用户信息状态对象
        """
        # 获取该意图所需的必要字段
        required_fields = self.required_fields_mapping.get(intent, [])
        if not required_fields:
            # 如果没有特定要求，则视为完整
            return UserInfoStatus(
                missing_fields=[],
                complete=True,
                completion_percentage=100.0
            )
        
        # 初始化跳过字段列表
        if skipped_fields is None:
            skipped_fields = []
        
        # 检查缺失的字段
        missing_fields = []
        for field in required_fields:
            # 如果字段已被跳过，则不再视为缺失
            if field in skipped_fields:
                continue
                
            # 检查字段是否存在且不为空(None、空字符串、空列表等)
            if field not in user_data or user_data[field] is None:
                missing_fields.append(field)
            elif isinstance(user_data[field], str) and not user_data[field].strip():
                missing_fields.append(field)
            elif isinstance(user_data[field], (list, dict)) and not user_data[field]:
                missing_fields.append(field)
        
        # 计算完成百分比
        total_fields = len(required_fields)
        completed_fields = total_fields - len(missing_fields) - len(skipped_fields)
        completion_percentage = (completed_fields / total_fields) * 100 if total_fields > 0 else 100.0
        
        return UserInfoStatus(
            missing_fields=missing_fields,
            complete=len(missing_fields) == 0,
            completion_percentage=completion_percentage
        )
    
    async def generate_query_message(
        self, 
        missing_fields: List[str], 
        is_retry: bool = False
    ) -> str:
        """生成询问缺失信息的消息
        
        Args:
            missing_fields: 缺失的字段列表
            is_retry: 是否是重试提问（用户回复模糊时）
            
        Returns:
            询问消息
        """
        if not missing_fields:
            return ""
            
        # 获取第一个缺失字段
        field = missing_fields[0]
        field_name = self.field_name_map.get(field, field)
        
        # 获取字段的描述信息
        field_description = self.get_field_description(field)
        
        if is_retry:
            # 重试提问，提供更明确的选项
            if field == "gender":
                return f"抱歉，我没能理解您的输入。请明确告诉我您的性别是男性还是女性？"
            elif field == "fitness_goal":
                return f"抱歉，我没能理解您的输入。请选择您的健身目标：1=减脂, 2=保持体型, 3=增肌"
            elif field == "experience_level":
                return f"抱歉，我没能理解您的输入。请选择您的健身经验水平：1=初学者, 2=中级, 3=高级"
            elif field == "activity_level":
                return f"抱歉，我没能理解您的输入。请选择您的日常活动水平：1=久坐少动, 2=轻度活动, 3=中度活动, 4=高度活动"
            else:
                return f"抱歉，我没能理解您的输入。{field_description}"
        else:
            # 首次提问
            return f"为了给您提供更个性化的服务，请告诉我您的{field_name}。{field_description}"
    
    def validate_and_parse(self, field: str, value: str) -> Tuple[bool, Any]:
        """验证和解析用户输入的值

        Args:
            field: 字段名称
            value: 用户输入的值

        Returns:
            Tuple[bool, Any]: (是否有效, 解析后的值)
        """
        if not value or not value.strip():
            logger.warning(f"字段 '{field}' 的输入为空")
            return False, None

        value = value.strip()
        
        try:
            # 针对不同字段类型进行验证和解析
            if field == "height":
                # 处理身高输入，单位为厘米
                # 允许的格式: "175", "175cm", "1.75m"
                # 移除所有非数字字符，除了小数点
                cleaned_value = ''.join(c for c in value if c.isdigit() or c == '.')
                if not cleaned_value:
                    return False, None
                
                number = float(cleaned_value)
                # 如果用户输入的是米，转换为厘米
                if '.' in cleaned_value and number < 3:
                    number = number * 100
                
                # 验证合理范围 (80cm ~ 250cm)
                if 80 <= number <= 250:
                    return True, int(round(number))
                else:
                    logger.warning(f"身高值超出合理范围: {number}cm")
                    return False, None
                
            elif field == "weight":
                # 处理体重输入，单位为千克
                # 允许的格式: "70", "70kg", "70.5"
                cleaned_value = ''.join(c for c in value if c.isdigit() or c == '.')
                if not cleaned_value:
                    return False, None
                
                number = float(cleaned_value)
                # 验证合理范围 (30kg ~ 200kg)
                if 30 <= number <= 200:
                    return True, number
                else:
                    logger.warning(f"体重值超出合理范围: {number}kg")
                    return False, None
                
            elif field == "age":
                # 处理年龄输入
                # 允许的格式: "25", "25岁"
                cleaned_value = ''.join(c for c in value if c.isdigit())
                if not cleaned_value:
                    return False, None
                
                number = int(cleaned_value)
                # 验证合理范围 (12 ~ 100岁)
                if 12 <= number <= 100:
                    return True, number
                else:
                    logger.warning(f"年龄值超出合理范围: {number}岁")
                    return False, None
                
            elif field == "gender":
                # 处理性别输入
                lower_value = value.lower()
                if any(term in lower_value for term in ["男", "male", "man"]):
                    return True, "male"
                elif any(term in lower_value for term in ["女", "female", "woman"]):
                    return True, "female"
                else:
                    logger.warning(f"无法识别的性别值: {value}")
                    return False, None
                
            elif field == "fitness_goal":
                # 处理健身目标
                # 首先尝试处理数字输入 (1, 2, 3)
                if value.isdigit():
                    number = int(value)
                    if number in self.field_enum_map.get("fitness_goal", {}):
                        mapped_value = self.field_enum_map["fitness_goal"][number]
                        logger.info(f"将数字 {number} 映射到健身目标: {mapped_value}")
                        return True, mapped_value
                
                goal_map = {
                    "减肥": "weight_loss",
                    "减脂": "fat_loss",
                    "增肌": "muscle_gain",
                    "塑形": "toning",
                    "增强体能": "endurance",
                    "维持健康": "general_fitness",
                    "增强力量": "strength",
                    "康复": "rehabilitation"
                }
                
                for key, mapped_value in goal_map.items():
                    if key in value:
                        return True, mapped_value
                
                logger.warning(f"无法识别的健身目标: {value}")
                return False, None
                
            elif field == "experience_level":
                # 处理健身经验水平
                # 首先尝试处理数字输入 (1, 2, 3)
                if value.isdigit():
                    number = int(value)
                    if number in self.field_enum_map.get("experience_level", {}):
                        mapped_value = self.field_enum_map["experience_level"][number]
                        logger.info(f"将数字 {number} 映射到经验水平: {mapped_value}")
                        return True, mapped_value
                
                level_map = {
                    "新手": "beginner",
                    "初级": "beginner",
                    "入门": "beginner",
                    "中级": "intermediate",
                    "高级": "advanced",
                    "专业": "expert"
                }
                
                for key, mapped_value in level_map.items():
                    if key in value:
                        return True, mapped_value
                
                logger.warning(f"无法识别的经验水平: {value}")
                return False, None
                
            elif field == "activity_level":
                # 处理日常活动水平
                # 首先尝试处理数字输入 (1, 2, 3, 4)
                if value.isdigit():
                    number = int(value)
                    if number in self.field_enum_map.get("activity_level", {}):
                        mapped_value = self.field_enum_map["activity_level"][number]
                        logger.info(f"将数字 {number} 映射到活动水平: {mapped_value}")
                        return True, mapped_value
                
                activity_map = {
                    "久坐": "sedentary",
                    "轻度": "lightly_active",
                    "适中": "moderately_active",
                    "很活跃": "very_active",
                    "极度活跃": "extremely_active"
                }
                
                for key, mapped_value in activity_map.items():
                    if key in value:
                        return True, mapped_value
                
                logger.warning(f"无法识别的活动水平: {value}")
                return False, None
                
            elif field == "health_conditions":
                # 处理健康状况，多个条件用逗号分隔
                conditions = [c.strip() for c in value.split(",")]
                return True, ", ".join(conditions)
                
            elif field == "allergies":
                # 处理过敏原，多个过敏原用逗号分隔
                allergies = [a.strip() for a in value.split(",")]
                return True, ", ".join(allergies)
                
            else:
                # 对于未特别处理的字段，直接返回输入值
                logger.warning(f"未知字段类型 '{field}'，返回原始值")
                return True, value
                
        except Exception as e:
            logger.error(f"解析字段 '{field}' 的值 '{value}' 时出错: {str(e)}")
            return False, None
    
    def get_missing_fields(self, user_data: Dict[str, Any], skipped_fields: List[str] = None) -> List[str]:
        """获取用户缺少的重要字段

        Args:
            user_data: 用户数据字典
            skipped_fields: 已经明确跳过的字段列表

        Returns:
            List[str]: 缺失字段列表，按优先级排序
        """
        if skipped_fields is None:
            skipped_fields = []
            
        # 定义重要字段及其优先级顺序（越重要的越靠前）
        essential_fields = [
            "height",
            "weight",
            "age",
            "gender",
            "fitness_goal",
            "experience_level",
            "activity_level"
        ]
        
        # 可选字段（健康状况和过敏信息是可选的）
        optional_fields = [
            "health_conditions", 
            "allergies"
        ]
        
        # 找出缺失的必要字段（值为None、0、空字符串或者不存在的字段）
        missing_fields = []
        for field in essential_fields:
            # 如果字段已被明确跳过，则不再询问
            if field in skipped_fields:
                continue
                
            # 检查字段是否存在且有效
            field_value = user_data.get(field)
            if field_value is None or field_value == "" or field_value == 0:
                missing_fields.append(field)
                
        # 返回缺失的重要字段列表
        return missing_fields
    
    def get_field_description(self, field: str) -> str:
        """获取字段的描述信息，用于提示用户如何填写
        
        Args:
            field: 字段名
            
        Returns:
            字段描述
        """
        descriptions = {
            "height": "您的身高(厘米)，例如：175",
            "weight": "您的体重(公斤)，例如：70",
            "age": "您的年龄，例如：30",
            "gender": "您的性别，例如：男/女",
            "fitness_goal": "您的健身目标(1=减脂, 2=保持体型, 3=增肌)",
            "experience_level": "您的健身经验水平(1=初学者, 2=中级, 3=高级)",
            "activity_level": "您的日常活动水平(1=久坐少动, 2=轻度活动, 3=中度活动, 4=高度活动)",
            "health_conditions": "您的健康状况，例如：高血压、糖尿病等，如无请填写'无'",
            "allergies": "您的过敏源，例如：花生、海鲜等，如无请填写'无'"
        }
        return descriptions.get(field, f"请填写您的{self.field_name_map.get(field, field)}")
    
    def format_user_info(self, user_data: Dict[str, Any]) -> str:
        """格式化用户信息，用于展示给用户
        
        Args:
            user_data: 用户数据字典
            
        Returns:
            格式化后的用户信息字符串
        """
        formatted_info = []
        for field, value in user_data.items():
            if field in self.field_name_map and value is not None:
                field_name = self.field_name_map[field]
                
                # 特殊处理枚举值
                if field in self.field_enum_map and value in self.field_enum_map[field]:
                    value = self.field_enum_map[field][value]
                
                # 特殊处理单位
                if field == "height" and value:
                    value = f"{value} cm"
                elif field == "weight" and value:
                    value = f"{value} kg"
                
                formatted_info.append(f"{field_name}: {value}")
        
        return "\n".join(formatted_info)
    
    def get_name_by_id(self, field: str, id_value: int) -> Optional[str]:
        """根据字段名和ID值获取对应的文本名称
        
        Args:
            field: 字段名称，如 'gender', 'fitness_goal' 等
            id_value: 枚举ID值
            
        Returns:
            对应的文本名称，未找到则返回None
        """
        if field not in self.field_enum_map:
            logger.warning(f"字段 '{field}' 不在枚举映射中")
            return None
            
        field_mapping = self.field_enum_map[field]
        if id_value not in field_mapping:
            logger.warning(f"ID值 '{id_value}' 在字段 '{field}' 的枚举映射中不存在")
            return None
            
        return field_mapping[id_value]
    
    def get_id_by_name(self, field: str, name: str) -> Optional[int]:
        """根据字段名和文本名称获取对应的ID值
        
        Args:
            field: 字段名称，如 'gender', 'fitness_goal' 等
            name: 文本名称
            
        Returns:
            对应的ID值，未找到则返回None
        """
        if field not in self.field_enum_map:
            logger.warning(f"字段 '{field}' 不在枚举映射中")
            return None
            
        field_mapping = self.field_enum_map[field]
        for id_val, text in field_mapping.items():
            if text == name:
                return id_val
                
        logger.warning(f"名称 '{name}' 在字段 '{field}' 的枚举映射中不存在")
        return None
    
    def extract_training_params(self, message: str) -> Dict[str, Any]:
        """
        从用户消息中提取训练参数
        """
        params = {}
        
        # 遍历每种参数类型
        for param_name, patterns in self.param_patterns.items():
            for pattern, group_idx in patterns:
                matches = re.search(pattern, message, re.IGNORECASE | re.DOTALL)
                if matches:
                    value = matches.group(group_idx).strip()
                    
                    # 应用映射（如果存在）
                    if param_name == "body_part" and value in self.body_part_mapping:
                        value = self.body_part_mapping[value]
                        
                    # 标准化值
                    if param_name == "training_scene":
                        if any(keyword in value for keyword in ["家", "宿舍", "卧室"]):
                            value = "家庭"
                        elif any(keyword in value for keyword in ["健身房", "器械", "俱乐部"]):
                            value = "健身房"
                        elif any(keyword in value for keyword in ["户外", "公园", "广场"]):
                            value = "户外"
                    
                    params[param_name] = value
                    break  # 找到一个匹配就跳出内循环
        
        logger.info(f"从消息中提取参数: {params}")
        return params
    
    def generate_follow_up_question(self, missing_param: str) -> str:
        """
        根据缺失的参数生成后续问题
        """
        questions = {
            "body_part": "您想训练哪个部位的肌肉？例如：胸部、背部、腿部",
            "training_scene": "您打算在哪里训练？是在健身房还是家中？",
            "plan_type": "您需要单日计划还是周期计划？",
            "equipment": "您有哪些训练器材可以使用？",
            "goal": "您的训练目标是什么？例如：增肌、减脂、提高力量"
        }
        
        return questions.get(missing_param, f"请告诉我您的{missing_param}是什么？") 