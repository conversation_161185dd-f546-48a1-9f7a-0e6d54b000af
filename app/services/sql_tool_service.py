from langchain.tools import Tool, BaseTool
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session, joinedload
from typing import Optional, List, Dict, Any, Union, Callable, Tuple, Type
from sqlalchemy import or_, func, cast, String, text
from app import crud, models
from app.db.session import get_db
from app.models.exercise import Exercise, ExerciseDetail
from app.models.training_plan import TrainingPlan
from app.models.workout import Workout
from app.models.workout_exercise import WorkoutExercise
from app.schemas.exercise import ExerciseSchema
from app.schemas.training_plan import TrainingPlanSchema
from app.services.base_service import BaseService
from app.core.param_definitions import (
    BODY_PART_CATEGORIES, MUSCLE_CATEGORIES, EQUIPMENT_CATEGORIES,
    BODY_PART_ALIASES, normalize_input
)
import logging
import json

logger = logging.getLogger(__name__)

class SQLToolService(BaseService):
    """SQL工具服务，提供数据库查询和操作的工具"""

    def _get_best_match_id(self, search_term: str, categories: List[Dict[str, Any]], aliases: Dict[str, str] = None) -> Tuple[Optional[int], Optional[str]]:
        """获取最匹配的ID和名称

        Args:
            search_term: 搜索词
            categories: 类别列表
            aliases: 别名字典，如果提供则先进行别名匹配

        Returns:
            匹配的ID和名称的元组，未找到则返回(None, None)
        """
        if not search_term:
            return None, None

        # 标准化输入
        normalized_term = normalize_input(search_term)

        # 别名映射
        if aliases and normalized_term in aliases:
            normalized_term = aliases[normalized_term]
            logger.info(f"别名映射: '{search_term}' -> '{normalized_term}'")

        # 精确匹配
        for category in categories:
            category_name = category.get("name", "")
            if normalize_input(category_name) == normalized_term:
                return category.get("id"), category_name

        # 模糊匹配
        best_match = None
        best_match_score = 0

        for category in categories:
            category_name = normalize_input(category.get("name", ""))

            # 如果搜索词包含在类别名称中，或类别名称包含在搜索词中
            if normalized_term in category_name or category_name in normalized_term:
                # 计算匹配得分：长度越接近，得分越高
                match_score = min(len(normalized_term), len(category_name)) / max(len(normalized_term), len(category_name))
                if match_score > best_match_score:
                    best_match = category
                    best_match_score = match_score

        if best_match:
            return best_match.get("id"), best_match.get("name")

        return None, None

    def __init__(self, db: Session = None):
        """初始化SQL工具服务

        Args:
            db: 数据库会话，如果未提供则自动创建
        """
        self.db = db or next(get_db())

        # 使用参数定义中心的身体部位别名映射表
        self.body_part_aliases = BODY_PART_ALIASES

        # 初始化器材别名映射表
        self.equipment_aliases = {
            "徒手": "自重",  # 徒手映射到自重
            "哑铃": "哑铃",  # 确保哑铃能正确映射到自身
            "杠铃": "杠铃",  # 确保杠铃能正确映射到自身
            "弹力带": "阻力带",  # 弹力带映射到阻力带
            "家庭": "自重",  # 家庭场景默认为自重训练
            "小哑铃": "哑铃",
            "杠": "杠铃",
            "杠铃架": "史密斯机",
            "smith": "史密斯机",
            "史密斯": "史密斯机",
            "仰卧板": "仰卧椅",
            "卧推架": "卧推凳",
            "推胸器": "坐姿推胸机",
            "拉力器": "拉力绳",
            "拉力带": "弹力带",
            "无": "自重"
        }

        # 记录初始化完成
        logger.info("SQL工具服务已初始化，设置了身体部位和器材别名映射表")

    def get_id_by_name(self, name: str, categories: List[Dict[str, Any]]) -> Optional[int]:
        """根据名称在类别列表中查找ID

        Args:
            name: 名称
            categories: 类别列表，每个类别包含id和name字段

        Returns:
            找到的ID，未找到则返回None
        """
        # 标准化名称
        normalized_name = normalize_input(name)

        for category in categories:
            if normalize_input(category.get("name", "")) == normalized_name:
                return category.get("id")
        return None

    def get_name_by_id(self, id: int, categories: List[Dict[str, Any]]) -> Optional[str]:
        """根据ID在类别列表中查找名称

        Args:
            id: ID
            categories: 类别列表，每个类别包含id和name字段

        Returns:
            找到的名称，未找到则返回None
        """
        for category in categories:
            if category.get("id") == id:
                return category.get("name")
        return None

    def search_exercises_tool(self) -> Tool:
        """搜索训练动作工具"""

        class SearchExercisesInput(BaseModel):
            equipment: Optional[Union[int, str]] = Field(None, description="器材ID或名称")
            body_part: Optional[Union[int, str]] = Field(None, description="身体部位ID或名称")
            muscle: Optional[Union[int, str]] = Field(None, description="目标肌肉ID或名称")
            difficulty: Optional[int] = Field(None, description="难度等级（1-5）")
            name: Optional[str] = Field(None, description="动作名称（支持模糊搜索）")
            scenario: Optional[str] = Field(None, description="训练场景(居家/健身房等)")
            limit: int = Field(10, description="返回结果数量限制")

        def _run(
            body_part: Optional[Union[int, str]] = None,
            equipment: Optional[Union[int, str]] = None,
            muscle: Optional[Union[int, str]] = None,
            difficulty: Optional[int] = None,
            name: Optional[str] = None,
            scenario: Optional[str] = None,
            limit: int = 10
        ) -> List[Dict[str, Any]]:
            """搜索训练动作

            Args:
                equipment: 器材ID或名称
                body_part: 身体部位ID或名称
                muscle: 目标肌肉ID或名称
                difficulty: 难度等级（1-5）
                name: 动作名称（支持模糊搜索）
                scenario: 训练场景(居家/健身房等)
                limit: 返回结果数量限制

            Returns:
                符合条件的训练动作列表
            """
            logger.info(f"搜索训练动作：equipment={equipment}, body_part={body_part}, muscle={muscle}, difficulty={difficulty}, name={name}, scenario={scenario}, limit={limit}")

            # 处理body_part转换 - 增强匹配逻辑
            body_part_id = None
            body_part_name = None
            if body_part is not None:
                if isinstance(body_part, str):
                    # 使用辅助方法获取最匹配的身体部位ID和名称
                    body_part_id, body_part_name = self._get_best_match_id(body_part, BODY_PART_CATEGORIES, self.body_part_aliases)
                    if body_part_id:
                        logger.info(f"识别身体部位: '{body_part}' -> {body_part_name} (ID: {body_part_id})")
                    else:
                        logger.warning(f"无法识别身体部位: '{body_part}'")
                else:
                    body_part_id = body_part
                    # 查找对应名称
                    for category in BODY_PART_CATEGORIES:
                        if category.get("id") == body_part_id:
                            body_part_name = category.get("name")
                            break

            # 根据场景推断器材ID
            target_equipment_id = None
            if scenario == "居家":
                # 居家场景下默认"自重"器材
                target_equipment_id = self.get_id_by_name("自重", EQUIPMENT_CATEGORIES)
                logger.info(f"居家场景推断器材: 自重 (ID: {target_equipment_id})")
            elif scenario == "健身房":
                # 健身房场景无需特别过滤器材，但可以排除自重
                pass

            # 处理equipment转换
            equipment_id = None
            if equipment is not None:
                if isinstance(equipment, str):
                    # 使用辅助方法获取最匹配的器材ID和名称
                    equipment_id, equipment_name = self._get_best_match_id(equipment, EQUIPMENT_CATEGORIES, self.equipment_aliases)
                    if equipment_id:
                        logger.info(f"识别器材: '{equipment}' -> {equipment_name} (ID: {equipment_id})")
                    else:
                        logger.warning(f"无法识别器材: '{equipment}'")
                else:
                    equipment_id = equipment

                # 明确指定的器材优先于场景推断的器材
                if equipment_id is not None:
                    target_equipment_id = equipment_id

            # 处理muscle转换
            target_muscle_id = None
            if muscle is not None:
                if isinstance(muscle, str):
                    # 使用辅助方法获取最匹配的肌肉ID和名称
                    target_muscle_id, muscle_name = self._get_best_match_id(muscle, MUSCLE_CATEGORIES, self.muscle_aliases)
                    if target_muscle_id:
                        logger.info(f"识别肌肉: '{muscle}' -> {muscle_name} (ID: {target_muscle_id})")
                    else:
                        logger.warning(f"无法识别肌肉: '{muscle}'")
                else:
                    target_muscle_id = muscle

            # 构建查询 - 预加载details关系
            query = self.db.query(Exercise).options(joinedload(Exercise.details))

            # 应用过滤条件 - 修复数组字段的查询
            if target_equipment_id is not None:
                try:
                    # 使用PostgreSQL的ANY操作符和数组交集操作符
                    if isinstance(target_equipment_id, list):
                        # 对于列表，使用字符串匹配
                        conditions = [func.array_to_string(Exercise.equipment_id, ',').like(f"%{eq_id}%") for eq_id in target_equipment_id]
                        query = query.filter(or_(*conditions))
                        logger.info(f"应用器材列表过滤: IDs={target_equipment_id}")
                    else:
                        # 对于单个ID，使用字符串匹配
                        query = query.filter(func.array_to_string(Exercise.equipment_id, ',').like(f"%{target_equipment_id}%"))
                        logger.info(f"应用单个器材过滤: ID={target_equipment_id}")
                except Exception as e:
                    logger.warning(f"设备ID高级过滤错误: {str(e)}，尝试基本过滤")
                    # 降级为字符串匹配
                    if isinstance(target_equipment_id, list):
                        conditions = [func.array_to_string(Exercise.equipment_id, ',').like(f"%{eq_id}%") for eq_id in target_equipment_id]
                        query = query.filter(or_(*conditions))
                    else:
                        query = query.filter(func.array_to_string(Exercise.equipment_id, ',').like(f"%{target_equipment_id}%"))

            if body_part_id is not None:
                try:
                    # 使用PostgreSQL的ANY操作符和数组交集操作符
                    if isinstance(body_part_id, list):
                        # 对于列表，使用字符串匹配
                        conditions = [func.array_to_string(Exercise.body_part_id, ',').like(f"%{bp_id}%") for bp_id in body_part_id]
                        query = query.filter(or_(*conditions))
                        logger.info(f"应用身体部位列表过滤: IDs={body_part_id}, 名称={body_part_name}")
                    else:
                        # 对于单个ID，使用字符串匹配
                        query = query.filter(func.array_to_string(Exercise.body_part_id, ',').like(f"%{body_part_id}%"))
                        logger.info(f"应用单个身体部位过滤: ID={body_part_id}, 名称={body_part_name}")
                except Exception as e:
                    logger.warning(f"身体部位ID高级过滤错误: {str(e)}，尝试基本过滤")
                    # 降级为字符串匹配
                    if isinstance(body_part_id, list):
                        conditions = [func.array_to_string(Exercise.body_part_id, ',').like(f"%{bp_id}%") for bp_id in body_part_id]
                        query = query.filter(or_(*conditions))
                    else:
                        query = query.filter(func.array_to_string(Exercise.body_part_id, ',').like(f"%{body_part_id}%"))

            # 过滤目标肌肉 (需要JOIN ExerciseDetail)
            if target_muscle_id is not None:
                try:
                    if isinstance(target_muscle_id, list):
                        # 对于列表，使用字符串匹配
                        conditions = [func.array_to_string(ExerciseDetail.target_muscles_id, ',').like(f"%{m_id}%") for m_id in target_muscle_id]
                        query = query.join(Exercise.details).filter(or_(*conditions))
                        logger.info(f"应用肌肉列表过滤: IDs={target_muscle_id}")
                    else:
                        # 对于单个ID，使用字符串匹配
                        query = query.join(Exercise.details).filter(
                            func.array_to_string(ExerciseDetail.target_muscles_id, ',').like(f"%{target_muscle_id}%")
                        )
                        logger.info(f"应用单个肌肉过滤: ID={target_muscle_id}")
                except Exception as e:
                    logger.warning(f"肌肉过滤错误: {str(e)}，尝试简单过滤")
                    # 降级为字符串匹配
                    if isinstance(target_muscle_id, list):
                        conditions = [func.array_to_string(ExerciseDetail.target_muscles_id, ',').like(f"%{m_id}%") for m_id in target_muscle_id]
                        query = query.join(Exercise.details).filter(or_(*conditions))
                    else:
                        query = query.join(Exercise.details).filter(func.array_to_string(ExerciseDetail.target_muscles_id, ',').like(f"%{target_muscle_id}%"))

            if difficulty: # 使用模型中的 level 字段
                query = query.filter(Exercise.level <= difficulty)
                logger.info(f"应用难度过滤: level<={difficulty}")

            if name:
                query = query.filter(Exercise.name.ilike(f"%{name}%"))
                logger.info(f"应用名称过滤: name={name}")

            # 调整排序逻辑 - 避免总是返回相同顺序
            if body_part_id is not None:
                # 如果有指定身体部位，优先返回匹配该部位的动作
                if isinstance(body_part_id, list):
                    # 对于列表，按照优先级、难度和ID排序
                    query = query.order_by(
                        Exercise.sort_priority.desc(),
                        Exercise.level.asc(),
                        Exercise.id.asc()
                    )
                else:
                    # 对于单个ID，可以使用array_position排序
                    try:
                        query = query.order_by(
                            func.array_position(Exercise.body_part_id, body_part_id).asc().nullslast(),
                            Exercise.sort_priority.desc(),
                            Exercise.level.asc(),
                            Exercise.id.asc()
                        )
                    except Exception as e:
                        logger.warning(f"排序错误: {str(e)}，使用基本排序")
                        query = query.order_by(
                            Exercise.sort_priority.desc(),
                            Exercise.level.asc(),
                            Exercise.id.asc()
                        )
            else:
                # 默认排序
                query = query.order_by(
                    Exercise.sort_priority.desc(),
                    Exercise.level.asc(),
                    Exercise.id.asc()
                )

            # 执行查询
            try:
                exercises = query.limit(limit).all()
                logger.info(f"查询到 {len(exercises)} 条结果")
            except Exception as e:
                logger.error(f"执行查询出错: {str(e)}")
                exercises = []

            # 转换结果，处理可能是列表的ID字段并修正字段名
            result = []
            for exercise in exercises:
                # 处理器材名称
                equipment_names = []
                equipment_ids = exercise.equipment_id if isinstance(exercise.equipment_id, list) else [exercise.equipment_id]
                for eq_id in equipment_ids:
                    if eq_id is not None:  # 避免None值
                        name_val = self.get_name_by_id(eq_id, EQUIPMENT_CATEGORIES)
                        if name_val:
                            equipment_names.append(name_val)

                # 处理身体部位名称
                body_part_names = []
                body_part_ids = exercise.body_part_id if isinstance(exercise.body_part_id, list) else [exercise.body_part_id]
                for bp_id in body_part_ids:
                    if bp_id is not None:  # 避免None值
                        name_val = self.get_name_by_id(bp_id, BODY_PART_CATEGORIES)
                        if name_val:
                            body_part_names.append(name_val)

                # 处理目标肌肉名称和ID (从details获取)
                muscle_names = []
                target_muscles_id_list = []
                if exercise.details and exercise.details.target_muscles_id:
                     target_muscles_id_list = exercise.details.target_muscles_id if isinstance(exercise.details.target_muscles_id, list) else [exercise.details.target_muscles_id]
                     for m_id in target_muscles_id_list:
                         if m_id is not None:  # 避免None值
                             name_val = self.get_name_by_id(m_id, MUSCLE_CATEGORIES)
                             if name_val:
                                 muscle_names.append(name_val)

                result.append({
                    "id": exercise.id,
                    "name": exercise.name,
                    "description": exercise.description,
                    "equipment": equipment_names,           # 器材名称列表
                    "equipment_id": exercise.equipment_id,  # 原始器材ID(可能列表)
                    "body_parts": body_part_names,          # 身体部位名称列表
                    "body_part_id": exercise.body_part_id,  # 原始部位ID(可能列表)
                    "muscles": muscle_names,                # 目标肌肉名称列表 (从details获取)
                    "target_muscles_id": target_muscles_id_list, # 目标肌肉ID列表 (从details获取)
                    "level": exercise.level,                # 使用level字段 (模型对应)
                    "difficulty": exercise.level,           # 也可提供difficulty作为别名
                    "image_url": exercise.image_name,       # 使用image_name字段 (模型对应)
                    "gif_url": exercise.gif_url,            # 添加gif_url (模型对应)
                    "video_url": exercise.details.video_file if exercise.details else None # 从details获取 (模型对应)
                })

            return result

        return Tool(
            name="search_exercises",
            description="搜索训练动作，可以根据器材、身体部位、肌肉群、难度和名称等条件筛选",
            func=_run,
            args_schema=SearchExercisesInput
        )

    def get_exercise_by_id(self, exercise_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取训练动作

        Args:
            exercise_id: 训练动作ID

        Returns:
            找到的训练动作详情，未找到则返回None
        """
        # 使用joinedload加载details关系
        exercise = self.db.query(Exercise).options(joinedload(Exercise.details)).filter(Exercise.id == exercise_id).first()

        if not exercise:
            return None

        # 获取肌肉名称
        muscle_names = []
        target_muscles_id = []

        # 处理详情信息
        if exercise.details:
            target_muscles_id = exercise.details.target_muscles_id or []

            # 处理目标肌肉
            if isinstance(target_muscles_id, list):
                for m_id in target_muscles_id:
                    name = self.get_name_by_id(m_id, MUSCLE_CATEGORIES)
                    if name:
                        muscle_names.append(name)
            else:
                # 兼容单个ID的情况
                name = self.get_name_by_id(target_muscles_id, MUSCLE_CATEGORIES)
                if name:
                    muscle_names.append(name)

        # 处理器材名称
        equipment_names = []
        if isinstance(exercise.equipment_id, list):
            for eq_id in exercise.equipment_id:
                name = self.get_name_by_id(eq_id, EQUIPMENT_CATEGORIES)
                if name:
                    equipment_names.append(name)
        else:
            name = self.get_name_by_id(exercise.equipment_id, EQUIPMENT_CATEGORIES)
            if name:
                equipment_names.append(name)

        # 处理身体部位名称
        body_part_names = []
        if isinstance(exercise.body_part_id, list):
            for bp_id in exercise.body_part_id:
                name = self.get_name_by_id(bp_id, BODY_PART_CATEGORIES)
                if name:
                    body_part_names.append(name)
        else:
            name = self.get_name_by_id(exercise.body_part_id, BODY_PART_CATEGORIES)
            if name:
                body_part_names.append(name)

        return {
            "id": exercise.id,
            "name": exercise.name,
            "description": exercise.description,
            "equipment": equipment_names,
            "equipment_id": exercise.equipment_id,
            "body_parts": body_part_names,
            "body_part_id": exercise.body_part_id,
            "muscles": muscle_names,
            "target_muscles_id": target_muscles_id,
            "difficulty": exercise.level,  # 使用level字段替代difficulty
            "image_url": exercise.image_name,  # 使用image_name字段替代image_url
            "gif_url": exercise.gif_url,  # 添加gif_url字段
            "video_url": exercise.details.video_file if exercise.details else None  # 从details获取video_file
        }

    def get_user_profile_tool(self) -> Tool:
        """获取用户个人资料工具"""

        class UserProfileInput(BaseModel):
            user_id: int = Field(..., description="用户ID")

        def _run(user_id: int) -> Dict[str, Any]:
            """获取用户个人资料"""
            user = crud.crud_user.get(self.db, id=user_id)
            if not user:
                return {"error": "用户不存在"}

            # 返回用户个人资料（排除敏感信息）
            return {
                "id": user.id,
                "nickname": user.nickname,
                "gender": user.gender,
                "age": user.age,
                "height": user.height,
                "weight": user.weight,
                "bmi": user.bmi,
                "experience_level": user.experience_level,
                "fitness_goal": user.fitness_goal,
                "activity_level": user.activity_level,
                "health_conditions": user.health_conditions,
                "allergies": user.allergies,
            }

        return Tool(
            name="get_user_profile",
            description="获取用户的个人资料信息，包括身高、体重、健身目标、经验水平等基本信息",
            func=_run,
            args_schema=UserProfileInput
        )

    def get_user_training_history_tool(self) -> Tool:
        """获取用户训练历史工具"""

        class TrainingHistoryInput(BaseModel):
            user_id: int = Field(..., description="用户ID")
            days: Optional[int] = Field(7, description="获取过去几天的训练记录")

        def _run(user_id: int, days: int = 7) -> List[Dict]:
            """获取用户训练历史"""
            try:
                records = crud.crud_user_training_record.get_user_training_history(
                    self.db, user_id=user_id, days=days
                )

                # 格式化返回结果
                results = []
                for record in records:
                    results.append({
                        "id": record.id,
                        "date": record.date.isoformat(),
                        "body_parts": record.body_parts,
                        "duration_minutes": record.duration_minutes,
                        "exercises_data": record.exercises_data,
                        "notes": record.notes
                    })
                return results
            except Exception as e:
                logger.error(f"获取用户训练历史错误: {str(e)}")
                return []

        return Tool(
            name="get_user_training_history",
            description="获取用户过去一段时间的训练记录，包括训练日期、训练部位、时长等信息",
            func=_run,
            args_schema=TrainingHistoryInput
        )

    def get_user_body_parts_last_trained_tool(self) -> Tool:
        """获取用户最近训练过的身体部位工具"""

        class BodyPartsLastTrainedInput(BaseModel):
            user_id: int = Field(..., description="用户ID")
            days: Optional[int] = Field(14, description="查询过去几天的训练记录")

        def _run(user_id: int, days: int = 14) -> Dict[str, str]:
            """获取用户最近训练过的身体部位及日期"""
            try:
                body_parts_dates = crud.crud_user_training_record.get_user_recent_trained_body_parts(
                    self.db, user_id=user_id, days=days
                )

                # 将datetime转换为字符串
                return {
                    body_part: date.isoformat()
                    for body_part, date in body_parts_dates.items()
                }
            except Exception as e:
                logger.error(f"获取用户训练过的身体部位错误: {str(e)}")
                return {}

        return Tool(
            name="get_user_body_parts_last_trained",
            description="获取用户最近训练过的身体部位及其最近训练日期，帮助制定合理的训练计划",
            func=_run,
            args_schema=BodyPartsLastTrainedInput
        )

    def get_tools(self) -> List[Tool]:
        """获取所有SQL工具"""
        return [
            self.get_user_profile_tool(),
            self.search_exercises_tool(),
            self.get_user_training_history_tool(),
            self.get_user_body_parts_last_trained_tool()
        ]

    def execute_query(self, query: str, params: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """执行SQL查询并返回结果
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            查询结果列表
        """
        try:
            # 安全检查 - 仅允许SELECT查询
            if not query.strip().lower().startswith("select"):
                logger.warning(f"非法查询请求: {query}")
                return [{"error": "仅支持SELECT查询"}]
            
            # 执行查询
            result = self.db.execute(text(query), params or {})
            
            # 提取结果
            columns = result.keys()
            rows = []
            for row in result:
                rows.append({col: value for col, value in zip(columns, row)})
            
            return rows
        except Exception as e:
            logger.error(f"SQL查询执行错误: {str(e)}")
            return [{"error": f"查询执行错误: {str(e)}"}]
    
    def get_user_info(self, user_id: int) -> Dict[str, Any]:
        """获取用户信息
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户信息字典
        """
        try:
            user = self.db.query(User).filter(User.id == user_id).first()
            if not user:
                logger.warning(f"未找到用户: {user_id}")
                return {}
            
            # 构建用户信息字典
            user_info = {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "gender": getattr(user, "gender", None),
                "age": getattr(user, "age", None),
                "height": getattr(user, "height", None),
                "weight": getattr(user, "weight", None),
                "fitness_level": getattr(user, "fitness_level", None),
                "fitness_goal": getattr(user, "fitness_goal", None),
                "activity_level": getattr(user, "activity_level", None),
                "health_condition": getattr(user, "health_condition", None)
            }
            
            # 过滤掉None值
            return {k: v for k, v in user_info.items() if v is not None}
        except Exception as e:
            logger.error(f"获取用户信息错误: {str(e)}")
            return {}
    
    def search_exercises(self, 
                          body_part: Optional[str] = None,
                          equipment: Optional[str] = None, 
                          difficulty: Optional[str] = None,
                          limit: int = 10) -> List[Dict[str, Any]]:
        """搜索健身动作
        
        Args:
            body_part: 身体部位
            equipment: 器材
            difficulty: 难度
            limit: 结果数量限制
            
        Returns:
            动作列表
        """
        try:
            query = self.db.query(Exercise)
            
            # 应用过滤条件
            if body_part:
                query = query.filter(Exercise.target_muscles.ilike(f"%{body_part}%"))
            if equipment:
                query = query.filter(Exercise.equipment.ilike(f"%{equipment}%"))
            if difficulty:
                query = query.filter(Exercise.difficulty == difficulty)
            
            # 限制结果数量
            exercises = query.limit(limit).all()
            
            # 转换为字典列表
            result = []
            for ex in exercises:
                exercise_dict = {
                    "id": ex.id,
                    "name": ex.name,
                    "description": ex.description,
                    "target_muscles": ex.target_muscles,
                    "equipment": ex.equipment,
                    "difficulty": ex.difficulty,
                    "instructions": ex.instructions
                }
                result.append(exercise_dict)
            
            return result
        except Exception as e:
            logger.error(f"搜索健身动作错误: {str(e)}")
            return []
    
    def get_user_workouts(self, user_id: int, limit: int = 5) -> List[Dict[str, Any]]:
        """获取用户的训练记录
        
        Args:
            user_id: 用户ID
            limit: 结果数量限制
            
        Returns:
            训练记录列表
        """
        try:
            # 查询用户的训练记录
            workouts = self.db.query(Workout).filter(
                Workout.user_id == user_id
            ).order_by(Workout.date.desc()).limit(limit).all()
            
            # 转换为字典列表
            result = []
            for workout in workouts:
                # 获取训练中的动作
                workout_exercises = self.db.query(WorkoutExercise).filter(
                    WorkoutExercise.workout_id == workout.id
                ).all()
                
                exercises = []
                for we in workout_exercises:
                    exercise = self.db.query(Exercise).filter(
                        Exercise.id == we.exercise_id
                    ).first()
                    
                    if exercise:
                        exercises.append({
                            "id": exercise.id,
                            "name": exercise.name,
                            "sets": we.sets,
                            "reps": we.reps,
                            "weight": we.weight,
                            "duration": we.duration
                        })
                
                workout_dict = {
                    "id": workout.id,
                    "date": workout.date.isoformat() if workout.date else None,
                    "name": workout.name,
                    "duration": workout.duration,
                    "calories": workout.calories,
                    "exercises": exercises
                }
                result.append(workout_dict)
            
            return result
        except Exception as e:
            logger.error(f"获取用户训练记录错误: {str(e)}")
            return []