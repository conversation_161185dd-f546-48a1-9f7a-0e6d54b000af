from typing import List, Dict, Any, Optional
from langchain.schema import HumanMessage, AIMessage, SystemMessage
import logging
from app.services.base_service import BaseService

logger = logging.getLogger(__name__)

class MemoryService(BaseService):
    """
    对话记忆服务，负责管理会话的历史消息和状态
    """
    
    def __init__(self, session_id: str, db = None):
        """
        初始化记忆服务
        
        Args:
            session_id: 会话ID
            db: 数据库会话，可选
        """
        super().__init__(db)
        self.session_id = session_id
        self.messages = []  # 内存中的消息历史
        logger.info(f"初始化对话记忆服务: session_id={session_id}")
        
    def add_user_message(self, content: str) -> None:
        """
        添加用户消息到历史记录
        
        Args:
            content: 消息内容
        """
        logger.debug(f"添加用户消息: {content[:50]}...")
        self.messages.append(HumanMessage(content=content))
        
    def add_ai_message(self, content: str) -> None:
        """
        添加AI消息到历史记录
        
        Args:
            content: 消息内容
        """
        logger.debug(f"添加AI消息: {content[:50]}...")
        self.messages.append(AIMessage(content=content))
        
    def add_system_message(self, content: str) -> None:
        """
        添加系统消息到历史记录
        
        Args:
            content: 消息内容
        """
        logger.debug(f"添加系统消息: {content[:50]}...")
        self.messages.append(SystemMessage(content=content))
        
    def get_chat_history(self) -> List:
        """
        获取聊天历史记录
        
        Returns:
            历史消息列表
        """
        return self.messages
        
    def clear(self) -> None:
        """
        清空历史记录
        """
        logger.info(f"清空会话记忆: session_id={self.session_id}")
        self.messages = []
        
    def get_last_n_messages(self, n: int) -> List:
        """
        获取最近n条消息
        
        Args:
            n: 消息数量
            
        Returns:
            最近n条消息列表
        """
        return self.messages[-n:] if n > 0 else []
        
    def save_to_db(self) -> None:
        """
        将会话历史保存到数据库
        """
        # 这里可以实现将内存中的消息保存到数据库的逻辑
        # 目前使用内存存储，未来可以扩展为数据库存储
        pass
        
    def load_from_db(self) -> None:
        """
        从数据库加载会话历史
        """
        # 这里可以实现从数据库加载消息历史的逻辑
        # 目前使用内存存储，未来可以扩展为数据库存储
        pass 