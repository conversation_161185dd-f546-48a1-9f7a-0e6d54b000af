import os
import logging
import uuid
import shutil
import subprocess
import tempfile
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
from enum import Enum
from pathlib import Path
import time

from fastapi import HTTPException

logger = logging.getLogger(__name__)

class VideoQuality(str, Enum):
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

class VideoOutputFormat(str, Enum):
    LIVEPHOTO = "livephoto"
    MP4 = "mp4"
    GIF = "gif"

class VideoProcessingService:
    """视频处理服务"""

    # 基础存储路径
    BASE_STORAGE_PATH = "/data/videos"
    # 临时文件存储路径
    TEMP_STORAGE_PATH = "/data/videos/temp"
    # 处理后文件存储路径
    PROCESSED_STORAGE_PATH = "/data/videos/processed"
    # 文件访问基础URL
    BASE_URL = "/api/v1/download"

    # 质量设置 - 分辨率和比特率
    QUALITY_SETTINGS = {
        VideoQuality.HIGH: {
            "resolution": "1920x1080",
            "video_bitrate": "5000k",
            "audio_bitrate": "192k",
            "gif_fps": "15"
        },
        VideoQuality.MEDIUM: {
            "resolution": "1280x720",
            "video_bitrate": "2500k",
            "audio_bitrate": "128k",
            "gif_fps": "12"
        },
        VideoQuality.LOW: {
            "resolution": "854x480",
            "video_bitrate": "1000k",
            "audio_bitrate": "96k",
            "gif_fps": "10"
        }
    }

    @classmethod
    def initialize(cls):
        """初始化存储目录"""
        os.makedirs(cls.BASE_STORAGE_PATH, exist_ok=True)
        os.makedirs(cls.TEMP_STORAGE_PATH, exist_ok=True)
        os.makedirs(cls.PROCESSED_STORAGE_PATH, exist_ok=True)
        logger.info(f"视频处理服务初始化完成，存储路径: {cls.BASE_STORAGE_PATH}")

    @staticmethod
    def _check_ffmpeg():
        """检查FFmpeg是否可用"""
        try:
            result = subprocess.run(
                ["ffmpeg", "-version"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=True
            )
            logger.info(f"FFmpeg可用: {result.stdout.splitlines()[0]}")
            return True
        except (subprocess.SubprocessError, FileNotFoundError) as e:
            logger.error(f"FFmpeg不可用: {str(e)}")
            return False

    @staticmethod
    def _generate_unique_filename(original_filename: str, output_format: VideoOutputFormat) -> str:
        """生成唯一的文件名"""
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        random_str = uuid.uuid4().hex[:8]

        # 获取原始文件的扩展名（不使用）
        # original_ext = os.path.splitext(original_filename)[1].lower()

        # 根据输出格式确定扩展名
        if output_format == VideoOutputFormat.LIVEPHOTO:
            # LivePhoto包含两个文件，这里返回基础名称
            return f"{timestamp}_{random_str}"
        elif output_format == VideoOutputFormat.MP4:
            return f"{timestamp}_{random_str}.mp4"
        elif output_format == VideoOutputFormat.GIF:
            return f"{timestamp}_{random_str}.gif"
        # 默认为MP4 - 这行代码实际上不会执行，因为枚举类型已经覆盖了所有情况
        return f"{timestamp}_{random_str}.mp4"

    @classmethod
    async def save_uploaded_video(cls, file_content: bytes, original_filename: str) -> str:
        """保存上传的视频文件到临时目录"""
        # 确保临时目录存在
        os.makedirs(cls.TEMP_STORAGE_PATH, exist_ok=True)

        # 生成临时文件名
        temp_filename = f"upload_{uuid.uuid4().hex}{os.path.splitext(original_filename)[1]}"
        temp_file_path = os.path.join(cls.TEMP_STORAGE_PATH, temp_filename)

        # 保存文件
        with open(temp_file_path, "wb") as f:
            f.write(file_content)

        logger.info(f"上传的视频已保存到临时文件: {temp_file_path}")
        return temp_file_path

    @classmethod
    async def process_video(
        cls,
        video_path: str,
        output_format: VideoOutputFormat,
        quality: VideoQuality,
        title: Optional[str] = None,
        description: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        处理视频文件

        参数:
            video_path: 视频文件路径
            output_format: 输出格式
            quality: 输出质量
            title: 视频标题（可选）
            description: 视频描述（可选）

        返回:
            Dict包含处理结果信息
        """
        start_time = time.time()

        # 检查FFmpeg是否可用
        if not cls._check_ffmpeg():
            raise HTTPException(status_code=500, detail="服务器配置错误: FFmpeg不可用")

        # 检查文件是否存在
        if not os.path.exists(video_path):
            raise HTTPException(status_code=404, detail="视频文件不存在")

        # 获取原始文件名
        original_filename = os.path.basename(video_path)

        # 生成唯一的输出文件名
        output_filename = cls._generate_unique_filename(original_filename, output_format)

        # 根据输出格式处理视频
        try:
            if output_format == VideoOutputFormat.MP4:
                result = await cls._convert_to_mp4(video_path, output_filename, quality)
            elif output_format == VideoOutputFormat.GIF:
                result = await cls._convert_to_gif(video_path, output_filename, quality)
            elif output_format == VideoOutputFormat.LIVEPHOTO:
                result = await cls._convert_to_livephoto(video_path, output_filename, quality)
            else:
                raise HTTPException(status_code=400, detail=f"不支持的输出格式: {output_format}")

            # 添加元数据
            result.update({
                "title": title or "Processed Video",
                "description": description or "",
                "original_filename": original_filename,
                "process_time": f"{time.time() - start_time:.2f}秒",
                "quality": quality,
                "output_format": output_format
            })

            logger.info(f"视频处理完成: {output_filename}, 耗时: {result['process_time']}")
            return result

        except Exception as e:
            logger.error(f"视频处理失败: {str(e)}")
            # 清理临时文件
            if os.path.exists(video_path):
                try:
                    os.remove(video_path)
                except Exception:
                    pass
            raise HTTPException(status_code=500, detail=f"视频处理失败: {str(e)}")

    @classmethod
    async def _convert_to_mp4(
        cls,
        input_path: str,
        output_filename: str,
        quality: VideoQuality
    ) -> Dict[str, Any]:
        """
        将视频转换为MP4格式

        参数:
            input_path: 输入视频路径
            output_filename: 输出文件名
            quality: 输出质量

        返回:
            Dict包含处理结果信息
        """
        # 确保输出目录存在
        os.makedirs(cls.PROCESSED_STORAGE_PATH, exist_ok=True)

        # 获取质量设置
        quality_settings = cls.QUALITY_SETTINGS[quality]
        resolution = quality_settings["resolution"]
        video_bitrate = quality_settings["video_bitrate"]
        audio_bitrate = quality_settings["audio_bitrate"]

        # 构建输出路径
        output_path = os.path.join(cls.PROCESSED_STORAGE_PATH, output_filename)

        # 构建FFmpeg命令
        cmd = [
            "ffmpeg",
            "-i", input_path,
            "-c:v", "libx264",
            "-preset", "medium",
            "-crf", "23" if quality == VideoQuality.HIGH else ("26" if quality == VideoQuality.MEDIUM else "28"),
            "-b:v", video_bitrate,
            "-c:a", "aac",
            "-b:a", audio_bitrate,
            "-vf", f"scale={resolution}:force_original_aspect_ratio=decrease,pad={resolution}:(ow-iw)/2:(oh-ih)/2",
            "-movflags", "+faststart",  # 优化网络播放
            "-y",  # 覆盖输出文件
            output_path
        ]

        try:
            # 执行FFmpeg命令
            process = subprocess.run(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=True
            )

            # 获取文件大小
            file_size = os.path.getsize(output_path)

            # 构建文件URL
            file_url = f"{cls.BASE_URL}/{output_filename}"

            return {
                "status": "success",
                "fileUrl": file_url,
                "message": "视频已成功转换为MP4格式",
                "file_path": output_path,
                "file_size": file_size,
                "file_size_formatted": cls._format_file_size(file_size)
            }

        except subprocess.CalledProcessError as e:
            logger.error(f"MP4转换失败: {e.stderr}")
            raise Exception(f"MP4转换失败: {e.stderr}")

    @classmethod
    async def _convert_to_gif(
        cls,
        input_path: str,
        output_filename: str,
        quality: VideoQuality
    ) -> Dict[str, Any]:
        """
        将视频转换为GIF格式

        参数:
            input_path: 输入视频路径
            output_filename: 输出文件名
            quality: 输出质量

        返回:
            Dict包含处理结果信息
        """
        # 确保输出目录存在
        os.makedirs(cls.PROCESSED_STORAGE_PATH, exist_ok=True)

        # 获取质量设置
        quality_settings = cls.QUALITY_SETTINGS[quality]
        resolution = quality_settings["resolution"]
        fps = quality_settings["gif_fps"]

        # 构建输出路径
        output_path = os.path.join(cls.PROCESSED_STORAGE_PATH, output_filename)

        # 创建临时调色板文件
        palette_path = os.path.join(cls.TEMP_STORAGE_PATH, f"palette_{uuid.uuid4().hex}.png")

        try:
            # 步骤1: 生成调色板以获得更好的GIF质量
            palette_cmd = [
                "ffmpeg",
                "-i", input_path,
                "-vf", f"fps={fps},scale={resolution}:force_original_aspect_ratio=decrease:flags=lanczos,palettegen",
                "-y",
                palette_path
            ]

            subprocess.run(
                palette_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=True
            )

            # 步骤2: 使用调色板生成GIF
            gif_cmd = [
                "ffmpeg",
                "-i", input_path,
                "-i", palette_path,
                "-lavfi", f"fps={fps},scale={resolution}:force_original_aspect_ratio=decrease:flags=lanczos [x]; [x][1:v] paletteuse",
                "-y",
                output_path
            ]

            subprocess.run(
                gif_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=True
            )

            # 清理调色板文件
            if os.path.exists(palette_path):
                os.remove(palette_path)

            # 获取文件大小
            file_size = os.path.getsize(output_path)

            # 构建文件URL
            file_url = f"{cls.BASE_URL}/{output_filename}"

            return {
                "status": "success",
                "fileUrl": file_url,
                "message": "视频已成功转换为GIF格式",
                "file_path": output_path,
                "file_size": file_size,
                "file_size_formatted": cls._format_file_size(file_size)
            }

        except subprocess.CalledProcessError as e:
            # 清理临时文件
            if os.path.exists(palette_path):
                os.remove(palette_path)

            logger.error(f"GIF转换失败: {e.stderr}")
            raise Exception(f"GIF转换失败: {e.stderr}")

        except Exception as e:
            # 清理临时文件
            if os.path.exists(palette_path):
                os.remove(palette_path)

            logger.error(f"GIF转换过程中发生错误: {str(e)}")
            raise

    @classmethod
    async def _convert_to_livephoto(
        cls,
        input_path: str,
        output_filename: str,
        quality: VideoQuality
    ) -> Dict[str, Any]:
        """
        将视频转换为LivePhoto格式（HEIF图像+MOV视频）

        参数:
            input_path: 输入视频路径
            output_filename: 输出文件名（不含扩展名）
            quality: 输出质量

        返回:
            Dict包含处理结果信息
        """
        # 确保输出目录存在
        os.makedirs(cls.PROCESSED_STORAGE_PATH, exist_ok=True)

        # 获取质量设置
        quality_settings = cls.QUALITY_SETTINGS[quality]
        resolution = quality_settings["resolution"]
        video_bitrate = quality_settings["video_bitrate"]
        audio_bitrate = quality_settings["audio_bitrate"]

        # 构建输出路径
        heif_filename = f"{output_filename}.heic"
        mov_filename = f"{output_filename}.mov"
        heif_path = os.path.join(cls.PROCESSED_STORAGE_PATH, heif_filename)
        mov_path = os.path.join(cls.PROCESSED_STORAGE_PATH, mov_filename)

        try:
            # 步骤1: 从视频中提取第一帧作为HEIF图像
            heif_cmd = [
                "ffmpeg",
                "-i", input_path,
                "-vf", f"scale={resolution}:force_original_aspect_ratio=decrease,pad={resolution}:(ow-iw)/2:(oh-ih)/2",
                "-frames:v", "1",
                "-c:v", "libx265",
                "-crf", "23" if quality == VideoQuality.HIGH else ("26" if quality == VideoQuality.MEDIUM else "28"),
                "-y",
                heif_path
            ]

            subprocess.run(
                heif_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=True
            )

            # 步骤2: 转换视频为MOV格式
            mov_cmd = [
                "ffmpeg",
                "-i", input_path,
                "-c:v", "libx264",
                "-preset", "medium",
                "-crf", "23" if quality == VideoQuality.HIGH else ("26" if quality == VideoQuality.MEDIUM else "28"),
                "-b:v", video_bitrate,
                "-c:a", "aac",
                "-b:a", audio_bitrate,
                "-vf", f"scale={resolution}:force_original_aspect_ratio=decrease,pad={resolution}:(ow-iw)/2:(oh-ih)/2",
                "-movflags", "+faststart",
                "-y",
                mov_path
            ]

            subprocess.run(
                mov_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=True
            )

            # 获取文件大小
            heif_size = os.path.getsize(heif_path)
            mov_size = os.path.getsize(mov_path)
            total_size = heif_size + mov_size

            # 构建文件URL
            heif_url = f"{cls.BASE_URL}/{heif_filename}"
            mov_url = f"{cls.BASE_URL}/{mov_filename}"

            return {
                "status": "success",
                "fileUrl": {
                    "heif": heif_url,
                    "mov": mov_url
                },
                "message": "视频已成功转换为LivePhoto格式",
                "file_paths": {
                    "heif": heif_path,
                    "mov": mov_path
                },
                "file_sizes": {
                    "heif": heif_size,
                    "mov": mov_size,
                    "total": total_size
                },
                "file_size_formatted": cls._format_file_size(total_size)
            }

        except subprocess.CalledProcessError as e:
            # 清理临时文件
            if os.path.exists(heif_path):
                os.remove(heif_path)
            if os.path.exists(mov_path):
                os.remove(mov_path)

            logger.error(f"LivePhoto转换失败: {e.stderr}")
            raise Exception(f"LivePhoto转换失败: {e.stderr}")

    @staticmethod
    def _format_file_size(size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.2f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.2f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.2f} GB"

    @classmethod
    async def cleanup_temp_files(cls, older_than_days: int = 7) -> Dict[str, Any]:
        """
        清理临时文件

        参数:
            older_than_days: 删除多少天前的文件

        返回:
            Dict包含清理结果信息
        """
        try:
            # 计算截止日期
            cutoff_date = datetime.now() - timedelta(days=older_than_days)

            # 统计数据
            deleted_count = 0
            failed_count = 0
            total_size = 0

            # 遍历临时目录
            for root, dirs, files in os.walk(cls.TEMP_STORAGE_PATH):
                for file in files:
                    file_path = os.path.join(root, file)

                    # 获取文件修改时间
                    try:
                        mtime = datetime.fromtimestamp(os.path.getmtime(file_path))

                        # 如果文件早于截止日期，则删除
                        if mtime < cutoff_date:
                            file_size = os.path.getsize(file_path)
                            try:
                                os.remove(file_path)
                                deleted_count += 1
                                total_size += file_size
                                logger.info(f"已删除临时文件: {file_path}")
                            except Exception as e:
                                failed_count += 1
                                logger.error(f"删除临时文件失败: {file_path}, 错误: {str(e)}")
                    except Exception as e:
                        logger.error(f"获取文件信息失败: {file_path}, 错误: {str(e)}")

            return {
                "status": "success",
                "message": f"已清理 {deleted_count} 个临时文件，总大小 {cls._format_file_size(total_size)}",
                "details": {
                    "deleted_count": deleted_count,
                    "failed_count": failed_count,
                    "total_size": total_size,
                    "total_size_formatted": cls._format_file_size(total_size),
                    "cutoff_date": cutoff_date.isoformat()
                }
            }

        except Exception as e:
            logger.error(f"清理临时文件失败: {str(e)}")
            raise Exception(f"清理临时文件失败: {str(e)}")

    @classmethod
    async def _convert_to_gif(
        cls,
        input_path: str,
        output_filename: str,
        quality: VideoQuality
    ) -> Dict[str, Any]:
        """
        将视频转换为GIF格式

        参数:
            input_path: 输入视频路径
            output_filename: 输出文件名
            quality: 输出质量

        返回:
            Dict包含处理结果信息
        """
        # 确保输出目录存在
        os.makedirs(cls.PROCESSED_STORAGE_PATH, exist_ok=True)

        # 获取质量设置
        quality_settings = cls.QUALITY_SETTINGS[quality]
        resolution = quality_settings["resolution"]
        fps = quality_settings["gif_fps"]

        # 构建输出路径
        output_path = os.path.join(cls.PROCESSED_STORAGE_PATH, output_filename)

        # 创建临时调色板文件
        palette_path = os.path.join(cls.TEMP_STORAGE_PATH, f"palette_{uuid.uuid4().hex}.png")

        try:
            # 步骤1: 生成调色板以获得更好的GIF质量
            palette_cmd = [
                "ffmpeg",
                "-i", input_path,
                "-vf", f"fps={fps},scale={resolution}:force_original_aspect_ratio=decrease:flags=lanczos,palettegen",
                "-y",
                palette_path
            ]

            subprocess.run(
                palette_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=True
            )

            # 步骤2: 使用调色板生成GIF
            gif_cmd = [
                "ffmpeg",
                "-i", input_path,
                "-i", palette_path,
                "-lavfi", f"fps={fps},scale={resolution}:force_original_aspect_ratio=decrease:flags=lanczos [x]; [x][1:v] paletteuse",
                "-y",
                output_path
            ]

            subprocess.run(
                gif_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=True
            )

            # 清理调色板文件
            if os.path.exists(palette_path):
                os.remove(palette_path)

            # 获取文件大小
            file_size = os.path.getsize(output_path)

            # 构建文件URL
            file_url = f"{cls.BASE_URL}/{output_filename}"

            return {
                "status": "success",
                "fileUrl": file_url,
                "message": "视频已成功转换为GIF格式",
                "file_path": output_path,
                "file_size": file_size,
                "file_size_formatted": cls._format_file_size(file_size)
            }

        except subprocess.CalledProcessError as e:
            # 清理临时文件
            if os.path.exists(palette_path):
                os.remove(palette_path)

            logger.error(f"GIF转换失败: {e.stderr}")
            raise Exception(f"GIF转换失败: {e.stderr}")

        except Exception as e:
            # 清理临时文件
            if os.path.exists(palette_path):
                os.remove(palette_path)

            logger.error(f"GIF转换过程中发生错误: {str(e)}")
            raise
