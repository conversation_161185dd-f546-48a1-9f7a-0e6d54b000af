from sqlalchemy.orm import Session
from sqlalchemy.future import select
from sqlalchemy import func, and_, or_
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, timedelta

from app.models.team import (
    Team, TeamMembership, TeamStats, TeamRole, TeamStatus, 
    MembershipStatus, ClientRelation, ClientStatus
)
from app.models.user import User
from app.schemas.team import <PERSON><PERSON><PERSON>, TeamUpdate, TeamResponse, TeamDetail
from app.crud.team import (
    create_team as crud_create_team,
    get_team_by_id, 
    update_team as crud_update_team,
    get_user_teams, 
    get_team_with_stats
)

class TeamServiceException(Exception):
    """团队服务异常基类"""
    pass

class TeamNotFoundException(TeamServiceException):
    """团队不存在异常"""
    pass

class InsufficientPermissionException(TeamServiceException):
    """权限不足异常"""
    pass

class TeamService:
    """团队服务，处理团队相关业务逻辑"""
    
    def __init__(self, db: Session):
        """初始化
        
        Args:
            db (Session): 数据库会话
        """
        self.db = db
    
    async def create_team(self, owner: User, team_data: TeamCreate) -> Team:
        """创建新团队
        
        Args:
            owner (User): 团队所有者
            team_data (TeamCreate): 团队创建数据
            
        Returns:
            Team: 创建的团队
        """
        return await crud_create_team(self.db, owner, team_data)
    
    async def get_team(self, team_id: int) -> Optional[Team]:
        """获取团队
        
        Args:
            team_id (int): 团队ID
            
        Returns:
            Optional[Team]: 团队对象，如果不存在则返回None
        """
        team = await get_team_by_id(self.db, team_id)
        if not team:
            raise TeamNotFoundException(f"Team with ID {team_id} not found")
        return team
    
    async def update_team(self, team_id: int, team_data: TeamUpdate, current_user: User) -> Team:
        """更新团队信息
        
        Args:
            team_id (int): 团队ID
            team_data (TeamUpdate): 团队更新数据
            current_user (User): 当前用户
            
        Returns:
            Team: 更新后的团队
        """
        # 检查权限
        await self._check_team_permission(team_id, current_user.id, [TeamRole.OWNER, TeamRole.ADMIN])
        
        # 更新团队
        team = await crud_update_team(self.db, team_id, team_data)
        if not team:
            raise TeamNotFoundException(f"Team with ID {team_id} not found")
        
        return team
    
    async def get_user_teams(self, user_id: int) -> List[Dict[str, Any]]:
        """获取用户的团队列表
        
        Args:
            user_id (int): 用户ID
            
        Returns:
            List[Dict[str, Any]]: 团队列表
        """
        return await get_user_teams(self.db, user_id)
    
    async def get_team_detail(self, team_id: int, user_id: int) -> Dict[str, Any]:
        """获取团队详情
        
        Args:
            team_id (int): 团队ID
            user_id (int): 用户ID
            
        Returns:
            Dict[str, Any]: 团队详情
        """
        team_detail = await get_team_with_stats(self.db, team_id, user_id)
        if not team_detail:
            raise TeamNotFoundException(f"Team with ID {team_id} not found or user has no access")
        
        return team_detail
    
    async def delete_team(self, team_id: int, current_user: User) -> bool:
        """删除团队
        
        Args:
            team_id (int): 团队ID
            current_user (User): 当前用户
            
        Returns:
            bool: 是否成功删除
        """
        # 检查权限
        await self._check_team_permission(team_id, current_user.id, [TeamRole.OWNER])
        
        # 获取团队
        team = await self.get_team(team_id)
        
        # 标记为删除状态而不是真正删除
        team.status = TeamStatus.SUSPENDED
        self.db.add(team)
        await self.db.commit()
        
        return True
    
    async def _check_team_permission(self, team_id: int, user_id: int, required_roles: List[TeamRole]) -> bool:
        """检查用户在团队中的权限
        
        Args:
            team_id (int): 团队ID
            user_id (int): 用户ID
            required_roles (List[TeamRole]): 所需角色列表
            
        Returns:
            bool: 是否有权限
            
        Raises:
            InsufficientPermissionException: 权限不足
        """
        # 查询用户在团队中的成员关系
        query = select(TeamMembership).filter(
            TeamMembership.team_id == team_id,
            TeamMembership.user_id == user_id,
            TeamMembership.status == MembershipStatus.ACTIVE
        )
        result = await self.db.execute(query)
        membership = result.scalars().first()
        
        if not membership or membership.role not in required_roles:
            raise InsufficientPermissionException("Insufficient permission for this operation")
        
        return True
    
    async def check_team_access(self, team_id: int, user_id: int) -> bool:
        """检查用户是否有权访问团队
        
        Args:
            team_id (int): 团队ID
            user_id (int): 用户ID
            
        Returns:
            bool: 是否有权访问
        """
        query = select(TeamMembership).filter(
            TeamMembership.team_id == team_id,
            TeamMembership.user_id == user_id,
            TeamMembership.status == MembershipStatus.ACTIVE
        )
        result = await self.db.execute(query)
        membership = result.scalars().first()
        
        return membership is not None
    
    async def update_team_stats(self, team_id: int) -> TeamStats:
        """更新团队统计数据
        
        Args:
            team_id (int): 团队ID
            
        Returns:
            TeamStats: 更新后的统计数据
        """
        # 获取团队统计对象
        query = select(TeamStats).filter(TeamStats.team_id == team_id)
        result = await self.db.execute(query)
        stats = result.scalars().first()
        
        if not stats:
            stats = TeamStats(team_id=team_id)
            self.db.add(stats)
        
        # 计算成员数量
        member_query = select(func.count()).select_from(TeamMembership).filter(
            TeamMembership.team_id == team_id,
            TeamMembership.status == MembershipStatus.ACTIVE
        )
        member_result = await self.db.execute(member_query)
        stats.total_members = member_result.scalar() or 0
        
        # 计算客户数量
        client_query = select(func.count()).select_from(ClientRelation).filter(
            ClientRelation.team_id == team_id,
            ClientRelation.status == ClientStatus.ACTIVE
        )
        client_result = await self.db.execute(client_query)
        stats.total_clients = client_result.scalar() or 0
        
        # 计算30天内活跃客户数量
        thirty_days_ago = datetime.now() - timedelta(days=30)
        active_client_query = select(func.count(ClientRelation.id.distinct())).select_from(ClientRelation).join(
            "training_plans"
        ).filter(
            ClientRelation.team_id == team_id,
            ClientRelation.status == ClientStatus.ACTIVE,
            ClientRelation.training_plans.any(updated_at >= thirty_days_ago)
        )
        active_client_result = await self.db.execute(active_client_query)
        stats.active_clients_30d = active_client_result.scalar() or 0
        
        # 更新时间
        stats.updated_at = func.now()
        
        self.db.add(stats)
        await self.db.commit()
        await self.db.refresh(stats)
        
        return stats
    
    async def log_team_activity(self, team_id: int, user_id: int, action: str, details: Dict[str, Any]) -> None:
        """记录团队活动
        
        Args:
            team_id (int): 团队ID
            user_id (int): 用户ID
            action (str): 活动类型
            details (Dict[str, Any]): 活动详情
        """
        # 这里可以实现团队活动日志记录
        # 可以创建一个TeamActivityLog模型来存储
        pass
