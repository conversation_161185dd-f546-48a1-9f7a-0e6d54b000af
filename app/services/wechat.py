import httpx
import json
import base64
from Crypto.Cipher import AES
from fastapi import HTTPException, Request
from app.core.config import settings
import logging
from typing import Tuple, Optional, Dict, Any
from datetime import datetime, timedelta
import requests
from sqlalchemy.orm import Session
from jose import jwt
from app.models.user import User
from app.schemas.wechat_login import UserInfo
import aiohttp
import uuid
import time
import asyncio
import traceback
import hashlib
import hmac

logger = logging.getLogger("fitness-coach-api")

class WechatAPIError(Exception):
    """微信API调用异常"""
    def __init__(self, message: str):
        self.message = message
        super().__init__(self.message)

class WechatService:
    """微信服务类，处理微信小程序登录相关功能"""

    def __init__(self):
        self.appid = settings.WECHAT_MINI_APPID
        self.secret = settings.WECHAT_MINI_SECRET

    @staticmethod
    def validate_ip(ip: str) -> bool:
        """验证IP是否在白名单中"""
        # 如果未启用IP白名单检查，则直接返回True
        if not settings.CHECK_IP_WHITELIST:
            return True
            
        # 处理IPv6格式的IPv4地址 (::ffff:127.0.0.1)
        if ip.startswith('::ffff:'):
            ip = ip.split('::ffff:')[1]
            
        # 检查IP是否在白名单中
        if ip in settings.IP_WHITELIST:
            return True
            
        # 记录不在白名单的IP
        logger.warning(f"IP不在白名单中: {ip}")
        return False

    @classmethod
    async def code2session(cls, code: str, request: Optional[Request] = None) -> Dict[str, Any]:
        """使用授权码获取用户openid"""
        # 记录详细的API调用信息
        logger.info(f"开始微信code2session API调用: code={code[:5]}***")
        print(f"【调试信息】开始微信code2session: {code[:5]}***")
        
        # 在开发环境中，如果使用特定的测试码，返回模拟数据
        if settings.IS_DEV and code in ["the_test_code", "test_code"]:
            print(f"【调试信息】开发环境使用测试code，返回模拟数据")
            # 使用固定的真实openid
            return {
                "session_key": "test_session_key",
                "openid": "oCU0j7eNq5n9_aK6BXJwxQ5MaQtQ",  # 使用固定的真实openid
                "unionid": None  # unionid为空
            }
            
        # 构建请求URL
        url = f"https://api.weixin.qq.com/sns/jscode2session"
        params = {
            "appid": settings.WECHAT_MINI_APPID,
            "secret": settings.WECHAT_MINI_SECRET,
            "js_code": code,
            "grant_type": "authorization_code"
        }
        
        # 记录请求详情(屏蔽敏感信息)
        log_params = params.copy()
        log_params["secret"] = "***MASKED***"
        log_params["js_code"] = log_params["js_code"][:5] + "***" if log_params["js_code"] and len(log_params["js_code"]) > 5 else log_params["js_code"]
        logger.debug(f"微信API请求: {url} 参数: {log_params}")
        print(f"【调试信息】微信API请求参数: {json.dumps(log_params)}")
        
        try:
            # 设置超时参数，避免请求长时间挂起
            timeout = aiohttp.ClientTimeout(
                total=settings.API_REQUEST_TIMEOUT,  
                connect=settings.CONNECTION_TIMEOUT
            )
            
            # 获取请求ID用于跟踪
            request_id = str(uuid.uuid4())[:8] if not request else str(id(request))
            logger.info(f"微信API请求开始: ID={request_id}")
            
            start_time = time.time()
            async with aiohttp.ClientSession(timeout=timeout) as session:
                try:
                    print(f"【调试信息】发送请求到微信API...")
                    async with session.get(url, params=params) as response:
                        # 记录API响应状态
                        status = response.status
                        elapsed = time.time() - start_time
                        logger.info(f"微信API响应: 状态码={status}, 耗时={elapsed:.3f}秒")
                        print(f"【调试信息】微信API响应: 状态码={status}, 耗时={elapsed:.3f}秒")
                        
                        # 获取响应内容 - 修改为先读取文本，再尝试解析JSON
                        try:
                            # 先读取响应文本
                            text_content = await response.text()
                            print(f"【调试信息】微信API原始响应: {text_content}")
                            logger.debug(f"微信API原始响应: {text_content}")
                            
                            # 处理可能的空响应
                            if not text_content or text_content.strip() == "":
                                error_msg = "微信API返回空响应"
                                logger.error(error_msg)
                                print(f"【调试错误】{error_msg}")
                                raise WechatAPIError(error_msg)
                            
                            # 尝试将文本解析为JSON
                            try:
                                # 尝试标准JSON解析
                                result = json.loads(text_content)
                            except json.JSONDecodeError as je:
                                # JSON解析失败，尝试特殊处理
                                logger.warning(f"标准JSON解析失败，尝试特殊处理: {str(je)}")
                                print(f"【调试警告】标准JSON解析失败，尝试特殊处理: {str(je)}")
                                
                                # 检查是否包含键值对格式
                                if ":" in text_content and "{" in text_content and "}" in text_content:
                                    try:
                                        # 尝试修复格式问题
                                        fixed_content = text_content.replace("'", '"')
                                        result = json.loads(fixed_content)
                                        logger.info("修复JSON格式成功")
                                    except Exception:
                                        # 如果仍无法解析，则尝试模拟响应
                                        error_msg = f"无法解析微信API响应: {text_content}"
                                        logger.error(error_msg)
                                        print(f"【调试错误】{error_msg}")
                                        raise WechatAPIError(error_msg)
                                else:
                                    # 检查是否类似于正常响应但格式有问题
                                    if "openid" in text_content:
                                        # 尝试从文本中提取openid和session_key
                                        try:
                                            import re
                                            openid_match = re.search(r'["\']?openid["\']?\s*[:=]\s*["\']?([^"\',}]+)["\']?', text_content)
                                            session_key_match = re.search(r'["\']?session_key["\']?\s*[:=]\s*["\']?([^"\',}]+)["\']?', text_content)
                                            
                                            if openid_match and session_key_match:
                                                openid = openid_match.group(1)
                                                session_key = session_key_match.group(1)
                                                
                                                result = {
                                                    "openid": openid,
                                                    "session_key": session_key
                                                }
                                                
                                                # 尝试提取unionid（如果存在）
                                                unionid_match = re.search(r'["\']?unionid["\']?\s*[:=]\s*["\']?([^"\',}]+)["\']?', text_content)
                                                if unionid_match:
                                                    result["unionid"] = unionid_match.group(1)
                                                    
                                                logger.info("通过正则表达式成功提取了微信API数据")
                                                print("【调试信息】通过正则表达式成功提取了微信API数据")
                                            else:
                                                error_msg = f"无法从文本中提取openid和session_key: {text_content}"
                                                logger.error(error_msg)
                                                print(f"【调试错误】{error_msg}")
                                                raise WechatAPIError(error_msg)
                                        except Exception as ex:
                                            error_msg = f"尝试提取响应数据失败: {str(ex)}, 原始文本: {text_content}"
                                            logger.error(error_msg)
                                            print(f"【调试错误】{error_msg}")
                                            raise WechatAPIError(error_msg)
                                    else:
                                        error_msg = f"微信API返回非JSON格式且无法解析: {text_content}"
                                        logger.error(error_msg)
                                        print(f"【调试错误】{error_msg}")
                                        raise WechatAPIError(error_msg)
                        except Exception as e:
                            error_msg = f"读取微信API响应失败: {str(e)}"
                            logger.error(error_msg)
                            print(f"【调试错误】{error_msg}")
                            raise WechatAPIError(error_msg)
                        
                        # 记录响应详情(部分隐藏敏感信息)
                        log_result = result.copy() if isinstance(result, dict) else {"raw_result": str(result)[:100]}
                        if isinstance(log_result, dict):
                            if "session_key" in log_result:
                                log_result["session_key"] = log_result["session_key"][:5] + "***" if log_result["session_key"] else None
                            if "openid" in log_result and log_result["openid"]:
                                log_result["openid"] = log_result["openid"][:5] + "***"
                        logger.debug(f"微信API响应数据: {log_result}")
                        print(f"【调试信息】微信API响应数据: {json.dumps(log_result, ensure_ascii=False)}")
                        
                        # 检查结果
                        if not isinstance(result, dict):
                            error_msg = f"微信API返回非字典结果: {result}"
                            logger.error(error_msg)
                            print(f"【调试错误】{error_msg}")
                            raise WechatAPIError(error_msg)
                        
                        # 检查错误码
                        if "errcode" in result and result["errcode"] != 0:
                            error_msg = f"微信API错误: code={result.get('errcode')}, msg={result.get('errmsg')}"
                            logger.error(error_msg)
                            print(f"【调试错误】{error_msg}")
                            raise WechatAPIError(error_msg)
                        
                        # 检查必要的字段
                        if "openid" not in result or "session_key" not in result:
                            error_msg = f"微信API响应缺少必要字段: {log_result}"
                            logger.error(error_msg)
                            print(f"【调试错误】{error_msg}")
                            raise WechatAPIError(error_msg)
                        
                        logger.info(f"微信code2session成功: openid={result['openid'][:5]}***")
                        print(f"【调试信息】微信code2session成功: openid={result['openid'][:5]}***")
                        return result
                except aiohttp.ClientResponseError as e:
                    error_msg = f"微信API响应错误: {str(e)}"
                    logger.error(error_msg)
                    print(f"【调试错误】{error_msg}")
                    raise WechatAPIError(error_msg)
                except aiohttp.ClientConnectionError as e:
                    error_msg = f"微信API连接错误: {str(e)}"
                    logger.error(error_msg)
                    print(f"【调试错误】{error_msg}")
                    raise WechatAPIError(error_msg)
                except aiohttp.ClientError as e:
                    error_msg = f"微信API通用错误: {str(e)}"
                    logger.error(error_msg)
                    print(f"【调试错误】{error_msg}")
                    raise WechatAPIError(error_msg)
                except asyncio.TimeoutError:
                    elapsed = time.time() - start_time
                    error_msg = f"微信API超时: 耗时={elapsed:.3f}秒, 超过了设置的{settings.API_REQUEST_TIMEOUT}秒限制"
                    logger.error(error_msg)
                    print(f"【调试错误】{error_msg}")
                    raise WechatAPIError(error_msg)
        except Exception as e:
            # 记录异常并重新抛出
            error_msg = f"微信API调用异常: {str(e)}"
            logger.error(error_msg)
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            print(f"【调试错误】{error_msg}")
            print(f"【调试错误】异常堆栈: {traceback.format_exc()}")
            raise WechatAPIError(error_msg)

    @staticmethod
    def decrypt_phone_number(session_key: str, encrypted_data: str, iv: str):
        """解密微信获取的手机号信息
        
        Args:
            session_key: 微信返回的session_key
            encrypted_data: 包括敏感数据在内的完整用户信息的加密数据
            iv: 加密算法的初始向量
            
        Returns:
            dict: 解密后的用户信息，包含phoneNumber字段
        """
        try:
            # 在开发环境中使用测试session_key直接返回模拟数据
            if settings.IS_DEV and session_key == "test_session_key":
                logger.info("开发环境：返回模拟手机号数据")
                print("【解密手机号】开发环境，返回模拟手机号数据")
                return {
                    "phoneNumber": "13800138000",
                    "purePhoneNumber": "13800138000",
                    "countryCode": "86"
                }
            
            print("【解密手机号】开始解密真实数据")
            # Base64解码
            session_key_bytes = base64.b64decode(session_key)
            encrypted_data_bytes = base64.b64decode(encrypted_data)
            iv_bytes = base64.b64decode(iv)
            
            print(f"【解密手机号】session_key解码后长度: {len(session_key_bytes)}")
            print(f"【解密手机号】encrypted_data解码后长度: {len(encrypted_data_bytes)}")
            print(f"【解密手机号】iv解码后长度: {len(iv_bytes)}")
            
            # 使用AES-128-CBC解密
            cipher = AES.new(session_key_bytes, AES.MODE_CBC, iv_bytes)
            decrypted = cipher.decrypt(encrypted_data_bytes)
            
            # 获取明文，去除补位字符
            decrypted = WechatService._unpad(decrypted)
            
            # 将解密后的数据转换为JSON
            data = json.loads(decrypted)
            
            # 检查appid是否一致
            if 'watermark' in data and 'appid' in data['watermark']:
                watermark_appid = data['watermark']['appid']
                print(f"【解密手机号】watermark中的appid: {watermark_appid}")
                if watermark_appid != settings.WECHAT_MINI_APPID:
                    print(f"【解密手机号】appid不匹配: 期望={settings.WECHAT_MINI_APPID}, 实际={watermark_appid}")
                    raise ValueError('Invalid watermark appid')
            else:
                print("【解密手机号】数据中缺少watermark或appid字段")
            
            print(f"【解密手机号】解密成功: {json.dumps(data, ensure_ascii=False)}")
            return data
        except Exception as e:
            print(f"【解密手机号】解密失败: {str(e)}")
            # 如果解密失败，尝试返回部分解析的数据
            logger.error(f"解密失败: {str(e)}")
            try:
                # 尝试解码错误的数据，获取尽可能多的信息
                print("【解密手机号】尝试解析错误数据")
                sample_data = {
                    "phoneNumber": encrypted_data[:10] + "..." if encrypted_data else "无数据",
                    "error": str(e),
                    "purePhoneNumber": "解密失败",
                    "countryCode": "解密失败"
                }
                return sample_data
            except:
                return {
                    "phoneNumber": "解密失败",
                    "purePhoneNumber": "解密失败",
                    "countryCode": "解密失败",
                    "error": str(e)
                }
    
    @staticmethod
    def _unpad(s):
        """去除PKCS#7填充"""
        return s[:-ord(s[len(s)-1:])] 

    def get_session_key(self, code: str) -> Tuple[Optional[str], Optional[str]]:
        """获取微信session_key和openid"""
        url = f"https://api.weixin.qq.com/sns/jscode2session"
        params = {
            "appid": self.appid,
            "secret": self.secret,
            "js_code": code,
            "grant_type": "authorization_code"
        }
        
        try:
            response = requests.get(url, params=params)
            data = response.json()
            
            if "errcode" in data:
                return None, None
                
            return data.get("session_key"), data.get("openid")
        except Exception as e:
            print(f"Failed to get session key: {e}")
            return None, None

    def decrypt_data(self, session_key: str, encrypted_data: str, iv: str) -> Optional[dict]:
        """解密微信加密数据"""
        try:
            session_key = base64.b64decode(session_key)
            encrypted_data = base64.b64decode(encrypted_data)
            iv = base64.b64decode(iv)
            
            cipher = AES.new(session_key, AES.MODE_CBC, iv)
            decrypted = cipher.decrypt(encrypted_data)
            
            # 去除补位符
            pad = decrypted[-1]
            if isinstance(pad, int):
                pad = bytes([pad])
            decrypted = decrypted[:-pad[0]]
            
            # 解析JSON
            decrypted = json.loads(decrypted)
            return decrypted
        except Exception as e:
            print(f"Failed to decrypt data: {e}")
            return None

    def create_access_token(self, user_id: int) -> str:
        """创建JWT token"""
        expire = datetime.utcnow().replace(tzinfo=None) + timedelta(days=settings.ACCESS_TOKEN_EXPIRE_DAYS)
        to_encode = {
            "exp": expire,
            "sub": str(user_id)
        }
        encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
        return encoded_jwt

    def process_login(
        self, 
        db: Session, 
        code: str, 
        encrypted_data: Optional[str] = None,
        iv: Optional[str] = None
    ) -> Tuple[str, bool, UserInfo]:
        """处理微信登录流程"""
        # 获取session_key和openid
        session_key, openid = self.get_session_key(code)
        if not session_key or not openid:
            raise ValueError("Failed to get session key or openid")

        # 查找现有用户
        user = db.query(User).filter(User.openid == openid).first()
        is_new_user = False

        # 解密用户信息
        user_info = None
        if encrypted_data and iv:
            user_info = self.decrypt_data(session_key, encrypted_data, iv)

        if not user:
            # 创建新用户
            user = User(
                openid=openid,
                phone=user_info.get("phoneNumber") if user_info else None,
                nickname=user_info.get("nickName") if user_info else None,
                avatar_url=user_info.get("avatarUrl") if user_info else None,
                gender=user_info.get("gender") if user_info else None,
                country=user_info.get("country") if user_info else None,
                province=user_info.get("province") if user_info else None,
                city=user_info.get("city") if user_info else None
            )
            db.add(user)
            db.commit()
            db.refresh(user)
            is_new_user = True
        elif user_info:
            # 更新现有用户信息
            user.phone = user_info.get("phoneNumber", user.phone)
            user.nickname = user_info.get("nickName", user.nickname)
            user.avatar_url = user_info.get("avatarUrl", user.avatar_url)
            user.gender = user_info.get("gender", user.gender)
            user.country = user_info.get("country", user.country)
            user.province = user_info.get("province", user.province)
            user.city = user_info.get("city", user.city)
            db.commit()
            db.refresh(user)

        # 创建token
        token = self.create_access_token(user.id)
        
        # 构建用户信息响应
        # 首先检查用户的created_at是否有时区信息，确保安全处理
        created_at = None
        if hasattr(user, 'created_at') and user.created_at:
            created_at = user.created_at
            # 移除时区信息以确保一致性
            if created_at.tzinfo is not None:
                created_at = created_at.replace(tzinfo=None)
        
        user_info_response = UserInfo(
            id=user.id,
            nickname=user.nickname,
            avatar_url=user.avatar_url,
            phone=user.phone,
            gender=user.gender,
            country=user.country,
            province=user.province,
            city=user.city,
            created_at=created_at
        )

        return token, is_new_user, user_info_response 