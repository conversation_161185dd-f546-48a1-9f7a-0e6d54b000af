"""
待处理请求管理器 - 负责管理待处理请求的保存和恢复
"""
from typing import Dict, Any, Optional, AsyncGenerator
import logging

from app.services.conversation.intent_handler import IntentHandler
from app.services.intent_recognizer import IntentData

logger = logging.getLogger(__name__)

class PendingRequestManager:
    """待处理请求管理器，负责管理待处理请求的保存和恢复"""

    def __init__(self, intent_handler: IntentHandler):
        """初始化待处理请求管理器

        Args:
            intent_handler: 意图处理器，用于执行恢复的请求
        """
        self.intent_handler = intent_handler

    def save_pending_request(self,
                            message: str,
                            intent_data: IntentData,
                            meta_info: Dict[str, Any]) -> Dict[str, Any]:
        """保存待处理请求

        Args:
            message: 原始消息
            intent_data: 意图数据
            meta_info: 元数据

        Returns:
            更新后的元数据
        """
        # 深拷贝元数据，避免修改原始数据
        response_meta_info = meta_info.copy()

        # 将意图数据转换为可序列化的字典
        intent_dict = {
            "intent": intent_data.intent,
            "confidence": intent_data.confidence,
            "parameters": intent_data.parameters
        }

        # 保存待处理请求
        response_meta_info["pending_request"] = {
            "message": message,
            "intent_data": intent_dict
        }

        logger.info(f"保存待处理请求: intent={intent_data.intent}, message={message[:50]}{'...' if len(message) > 50 else ''}")

        return response_meta_info

    def has_pending_request(self, meta_info: Dict[str, Any]) -> bool:
        """检查是否有待处理请求

        Args:
            meta_info: 元数据

        Returns:
            是否有待处理请求
        """
        return "pending_request" in meta_info and meta_info["pending_request"] is not None

    def get_pending_request(self, meta_info: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """获取待处理请求

        Args:
            meta_info: 元数据

        Returns:
            待处理请求，如果没有则返回None
        """
        return meta_info.get("pending_request")

    def clear_pending_request(self, meta_info: Dict[str, Any]) -> Dict[str, Any]:
        """清除待处理请求

        Args:
            meta_info: 元数据

        Returns:
            更新后的元数据
        """
        # 深拷贝元数据，避免修改原始数据
        response_meta_info = meta_info.copy()

        # 清除待处理请求
        if "pending_request" in response_meta_info:
            response_meta_info.pop("pending_request")

        return response_meta_info

    async def restore_pending_request(self,
                                    meta_info: Dict[str, Any],
                                    user_data: Dict[str, Any],
                                    history: list) -> AsyncGenerator[Dict[str, Any], None]:
        """恢复待处理请求

        Args:
            meta_info: 元数据
            user_data: 用户数据
            history: 对话历史

        Returns:
            异步生成器，生成响应内容
        """
        # 获取待处理请求
        pending_request = self.get_pending_request(meta_info)

        if not pending_request:
            logger.warning("尝试恢复待处理请求，但没有找到待处理请求")
            yield {"type": "message", "content": "抱歉，无法恢复之前的请求。请重新告诉我您需要什么帮助。", "role": "assistant"}
            return

        # 获取原始消息和意图数据
        original_message = pending_request.get("message")
        intent_data_dict = pending_request.get("intent_data")

        if not original_message or not intent_data_dict:
            logger.warning("待处理请求数据不完整")
            yield {"type": "message", "content": "抱歉，无法恢复之前的请求。请重新告诉我您需要什么帮助。", "role": "assistant"}
            return

        # 创建IntentData对象
        intent_data = IntentData(
            intent=intent_data_dict.get("intent", "general_chat"),
            confidence=intent_data_dict.get("confidence", 0.0),
            parameters=intent_data_dict.get("parameters", {})
        )

        # 清除待处理请求
        response_meta_info = self.clear_pending_request(meta_info)

        # 确认恢复
        yield {"type": "message", "content": "谢谢您提供的信息，现在我可以回答您之前的问题了。", "role": "assistant"}

        # 执行原始意图
        logger.info(f"恢复执行原始意图: intent={intent_data.intent}")
        async for response in self.intent_handler.handle_intent(
            intent_data=intent_data,
            meta_info=response_meta_info,
            user_data=user_data,
            history=history
        ):
            yield response
