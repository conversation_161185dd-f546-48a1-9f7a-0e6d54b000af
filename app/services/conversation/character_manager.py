"""
角色管理模块
负责处理AI教练的角色扮演和个性化回复
"""
import logging
from typing import Dict, Any, Optional, List, Union
from app.models.user_setting import CharacterType
from app.core.config import settings

logger = logging.getLogger(__name__)

class CharacterManager:
    """角色管理器，负责处理AI教练的角色扮演和个性化回复"""
    
    def __init__(self, llm_proxy_service):
        """初始化角色管理器
        
        Args:
            llm_proxy_service: LLM代理服务，用于调用语言模型
        """
        self.llm_proxy = llm_proxy_service
        self.character_model = settings.LLM_CHARACTER_MODEL
        
        # 角色提示词库
        self.characters = {
            CharacterType.MOTIVATIONAL.value: self._get_motivational_prompt(),
            CharacterType.STRICT.value: self._get_strict_prompt()
        }
        
        # 默认角色
        self.default_character = CharacterType.MOTIVATIONAL.value
        
        logger.info(f"角色管理器初始化完成，默认角色: {self.default_character}")
    
    def _get_motivational_prompt(self) -> str:
        """获取激励型角色的提示词
        
        Returns:
            激励型角色的提示词
        """
        return """你是一位充满激情和鼓励性的健身教练。
        
        语言风格:
        - 热情洋溢，充满正能量
        - 经常使用鼓励性词汇，如"你做得到！"、"坚持就是胜利！"
        - 强调进步而非完美
        - 使用生动的比喻和形象化的语言
        - 适度使用感叹号表达热情
        
        性格特点:
        - 乐观积极，始终相信用户能够成功
        - 善于发现用户的进步和优点
        - 理解健身过程中的困难，但总是引导用户看到希望
        - 将挑战描述为成长的机会
        - 强调健身是一段旅程，而非目的地
        
        沟通方式:
        - 使用"我们"而非"你"，建立共同感
        - 分享成功故事和积极例子
        - 承认困难但立即提供解决方案
        - 将失败重新定义为学习机会
        - 设定小目标，庆祝小成就
        """
    
    def _get_strict_prompt(self) -> str:
        """获取严格型角色的提示词
        
        Returns:
            严格型角色的提示词
        """
        return """你是一位严格、直接且注重纪律的健身教练。
        
        语言风格:
        - 简洁明了，不拖泥带水
        - 直接指出问题和解决方案
        - 使用命令式语句，如"做到这一点"、"必须坚持"
        - 少用修饰词，重点突出关键信息
        - 偶尔使用反问句强调重点
        
        性格特点:
        - 高标准，不接受借口
        - 注重结果和效率
        - 认为纪律和坚持是成功的关键
        - 相信通过挑战才能获得真正的进步
        - 重视科学和数据，而非感受
        
        沟通方式:
        - 直接指出问题所在
        - 提供明确的、可操作的指导
        - 不过分关注情感安慰
        - 强调责任感和自律
        - 设定明确的期望和后果
        """
    
    async def process_response(self, response_text: str, character_type: Optional[str] = None) -> str:
        """处理回复，使其符合所选角色的风格
        
        Args:
            response_text: 原始回复文本
            character_type: 角色类型，如果为None则使用默认角色
            
        Returns:
            处理后的回复文本
        """
        # 使用指定角色或默认角色
        character = character_type or self.default_character
        
        # 确保角色类型有效
        if character not in self.characters:
            logger.warning(f"未知的角色类型: {character}，使用默认角色: {self.default_character}")
            character = self.default_character
        
        # 获取角色提示词
        character_prompt = self.characters[character]
        
        # 构建提示
        prompt = f"""请以下面描述的角色身份重新表达以下健身教练的回复，保持原始信息不变，但调整语言风格和表达方式：
        
        角色描述：
        {character_prompt}
        
        原始回复：
        {response_text}
        
        请用该角色的语言风格重新表达："""
        
        try:
            # 调用LLM处理回复
            character_response = await self.llm_proxy.aget_chat_response(
                messages=[
                    {"role": "system", "content": "你是一个能够根据指定角色风格重写文本的AI助手。"},
                    {"role": "user", "content": prompt}
                ],
                model=self.character_model
            )
            
            logger.info(f"角色化处理完成，角色类型: {character}")
            return character_response
        except Exception as e:
            logger.error(f"角色化处理回复时出错: {str(e)}")
            # 出错时返回原始回复
            return response_text
    
    def get_character_type(self, user_settings: Optional[Any]) -> str:
        """从用户设置中获取角色类型
        
        Args:
            user_settings: 用户设置对象
            
        Returns:
            角色类型
        """
        if user_settings and hasattr(user_settings, 'ai_character_type') and user_settings.ai_character_type:
            return user_settings.ai_character_type
        
        return self.default_character
