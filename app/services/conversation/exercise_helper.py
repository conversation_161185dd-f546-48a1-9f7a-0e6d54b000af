# app/services/conversation/exercise_helper.py
from __future__ import annotations
import logging
import json
from typing import TYPE_CHECKING, Dict, Any, List, Optional, Tuple, Set, Union
import re
import random
from app.services.sql_tool_service import BODY_PART_CATEGORIES, MUSCLE_CATEGORIES, EQUIPMENT_CATEGORIES

# Import newly created classes
from .parameter_extractor import ParameterExtractor
from .training_plan_manager import TrainingPlanManager

if TYPE_CHECKING:
    from .orchestrator import ConversationService
    from app.services.intent_recognizer import IntentData
    from app.models.user import User
    from app.services.exercise_service import ExerciseService
    # Import profile_helper if needed by rule_based_filtering
    from .profile_helper import _get_recommended_difficulty
    from .exercise_recommender import ExerciseRecommender

logger = logging.getLogger(__name__)

# Keep core exercise functions here or move to ExerciseRecommender?
# For now, keep them here and ExerciseRecommender imports them.

async def get_candidate_exercises(
    service: 'ConversationService',
    body_part: Union[str, int, List[int]],
    scenario: Optional[str] = None,
    equipment_ids: Optional[List[int]] = None,
    difficulty_range: Optional[Dict[str, int]] = None,
    limit: int = 100
) -> List[Dict[str, Any]]:
    """获取候选训练动作

    Args:
        service: 对话服务实例，提供工具访问
        body_part: 身体部位，可以是名称(str)、ID(int)或ID列表(List[int])
        scenario: 训练场景（如"居家"、"健身房"）
        equipment_ids: 可用器材ID列表
        difficulty_range: 难度范围字典，包含preferred、min、max键
        limit: 返回结果的最大数量

    Returns:
        候选训练动作列表
    """
    # 增加对参数类型的详细日志记录，便于调试
    body_part_type = type(body_part).__name__
    equipment_type = type(equipment_ids).__name__ if equipment_ids is not None else "None"
    logger.info(f"获取候选训练动作: body_part={body_part} (类型:{body_part_type}), "
                f"scenario={scenario}, equipment_ids={equipment_ids} (类型:{equipment_type}), "
                f"difficulty_range={difficulty_range}, limit={limit}")

    # 获取搜索工具
    tool = service.search_exercises_tool()

    # 提取首选难度
    difficulty = difficulty_range.get("preferred") if difficulty_range else None

    # 处理body_part参数 - 如果是字符串，尝试转换为ID
    if isinstance(body_part, str):
        # 尝试从BODY_PART_CATEGORIES中查找对应的ID
        body_part_id = None
        for category in BODY_PART_CATEGORIES:
            if category["name"].lower() == body_part.lower():
                body_part_id = category["id"]
                logger.info(f"将身体部位名称 '{body_part}' 转换为ID: {body_part_id}")
                body_part = body_part_id
                break

    # 处理scenario参数 - 如果提供了场景，尝试转换为对应的器材ID
    if scenario and not equipment_ids:
        if scenario.lower() in ["家庭", "居家", "home"]:
            # 查找"自重"器材ID
            for category in EQUIPMENT_CATEGORIES:
                if category["name"] == "自重":
                    equipment_ids = [category["id"]]
                    logger.info(f"根据场景 '{scenario}' 设置默认器材ID: {equipment_ids}")
                    break

    # 根据body_part类型处理不同情况
    all_exercises = []

    # 如果body_part是列表，分别获取每个部位的动作并合并
    if isinstance(body_part, list):
        logger.info(f"body_part是ID列表，将分别查询每个部位并合并结果")

        # 每个部位分配的查询限制
        part_limit = max(limit // len(body_part), 5)  # 确保每个部位至少有5个结果

        # 记录已见过的动作ID，避免重复
        seen_ids = set()

        for bp in body_part:
            logger.info(f"查询身体部位ID: {bp}")
            try:
                # 为单个部位ID执行查询，同时传入equipment_ids参数
                part_exercises = tool.func(
                    body_part=bp,
                    equipment=equipment_ids,
                    scenario=scenario,
                    difficulty=difficulty,
                    limit=part_limit
                )

                # 过滤重复动作
                new_exercises = []
                for ex in part_exercises:
                    if ex["id"] not in seen_ids:
                        seen_ids.add(ex["id"])
                        new_exercises.append(ex)

                logger.info(f"部位ID {bp} 找到 {len(new_exercises)} 个新动作")
                all_exercises.extend(new_exercises)
            except Exception as e:
                logger.error(f"查询部位ID {bp} 出错: {str(e)}")

        # 确保不超过总限制
        if len(all_exercises) > limit:
            logger.info(f"合并结果总数 ({len(all_exercises)}) 超过限制 ({limit})，将截断")
            all_exercises = all_exercises[:limit]
    else:
        # 对于字符串或单个ID，直接调用搜索工具
        try:
            all_exercises = tool.func(
                body_part=body_part,
                equipment=equipment_ids,
                scenario=scenario,
                difficulty=difficulty,
                limit=limit
            )
            logger.info(f"为身体部位 '{body_part}' 找到 {len(all_exercises)} 个动作")
        except Exception as e:
            logger.error(f"查询训练动作失败: {str(e)}")
            all_exercises = []

    # 如果没有找到结果，尝试放宽条件
    if not all_exercises:
        logger.info("未找到结果，尝试放宽条件")
        try:
            # 尝试不指定身体部位，只按难度和场景查询
            all_exercises = tool.func(
                body_part=None,
                equipment=equipment_ids,
                scenario=scenario,
                difficulty=difficulty,
                limit=limit
            )
            logger.info(f"放宽条件后找到 {len(all_exercises)} 个动作")
        except Exception as e:
            logger.error(f"放宽条件查询失败: {str(e)}")

    # 确保所有动作都有必要的字段
    for ex in all_exercises:
        # 确保有肌肉字段
        if "muscles" not in ex:
            ex["muscles"] = []

        # 确保有组数和次数字段
        if "sets" not in ex:
            ex["sets"] = 3
        if "reps" not in ex:
            ex["reps"] = "8-12"
        if "rest_seconds" not in ex:
            ex["rest_seconds"] = 60

    # 记录结果数量和示例
    if all_exercises:
        sample_ids = [ex['id'] for ex in all_exercises[:3]]
        logger.info(f"获取到 {len(all_exercises)} 个候选训练动作，示例ID: {sample_ids}")
    else:
        logger.warning(f"未找到任何候选训练动作，请检查查询参数")

    return all_exercises

async def personalized_filtering(
    service: 'ConversationService',
    candidate_exercises: List[Dict[str, Any]],
    user_profile: Dict[str, Any],
    training_params: Dict[str, Any],
    message: str = "",
    limit: int = 5
) -> List[Dict[str, Any]]:
    """使用LLM进行个性化二次筛选"""
    if not candidate_exercises: return []
    if len(candidate_exercises) <= limit:
        logger.info(f"候选数量 ({len(candidate_exercises)}) <= 限制 ({limit})，跳过LLM筛选")
        # Apply default params directly if skipping LLM
        for ex in candidate_exercises:
            ex.update({k: v for k, v in training_params.items() if k in ["sets", "reps", "rest_seconds"]})
            if "notes" not in ex: ex["notes"] = ""
        return candidate_exercises

    from .profile_helper import _get_user_profile_text # Keep this local import
    user_info_text = _get_user_profile_text(service, user_profile)
    exercise_info = "".join([f"{i+1}. ID: {ex['id']}, 名称: {ex['name']}\n   部位: {', '.join(ex['body_parts'])}\n   肌肉: {', '.join(ex['muscles'])}\n   器材: {', '.join(ex['equipment'])}\n   难度: {ex['level']}\n" for i, ex in enumerate(candidate_exercises[:20])]) # Limit prompt length
    user_preference = f"用户原始消息: {message}" if message else ""

    prompt = f"""
作为专业健身教练，请从以下候选训练动作中为用户选择{limit}个最适合的动作。
{user_info_text}

{user_preference}

候选训练动作:
{exercise_info}

请从上述动作中选择{limit}个最适合该用户的动作，考虑用户的健身目标、经验水平和健康状况。请为每个动作提供推荐的组数(sets)、次数(reps)和休息时间(rest_seconds)。

请只返回JSON格式的结果，不要有任何其他文字，格式如下:
[
  {{
    "id": 动作ID,
    "sets": 推荐组数,
    "reps": "推荐次数(如10-12)",
    "rest_seconds": 推荐休息秒数,
    "notes": "给用户的特别建议"
  }},
  // ... 更多动作 ...
]
"""

    try:
        response = await service.llm_proxy.aget_chat_response([
            {"role": "system", "content": "你是一位专业健身教练AI助手，擅长根据用户情况提供个性化训练建议。"},
            {"role": "user", "content": prompt}
        ])
        recommendations = extract_json_from_text(response)

        if not recommendations:
            logger.warning("无法从LLM响应中提取JSON，使用规则筛选")
            return _rule_based_filtering(service, candidate_exercises, user_profile, training_params, limit)

        selected_exercise_ids = {rec["id"] for rec in recommendations}
        selected_exercises = []
        recommendation_map = {rec["id"]: rec for rec in recommendations}

        for exercise in candidate_exercises:
            if exercise["id"] in selected_exercise_ids:
                rec = recommendation_map.get(exercise["id"])
                if rec:
                    exercise_with_params = exercise.copy()
                    exercise_with_params.update({
                        "sets": rec.get("sets", training_params.get("sets", 3)),
                        "reps": rec.get("reps", training_params.get("reps", "8-12")),
                        "rest_seconds": rec.get("rest_seconds", training_params.get("rest_seconds", 60)),
                        "notes": rec.get("notes", "")
                    })
                    selected_exercises.append(exercise_with_params)

        # Ensure the number of returned exercises matches the limit if possible
        if len(selected_exercises) > limit:
             logger.warning(f"LLM推荐了 {len(selected_exercises)} 个动作, 超过限制 {limit}, 将截断.")
             selected_exercises = selected_exercises[:limit]
        elif len(selected_exercises) < limit:
             logger.warning(f"LLM推荐了 {len(selected_exercises)} 个动作, 少于限制 {limit}, 可能需要规则补充.")
             # Optionally, call rule-based filtering to fill the gap?

        return selected_exercises

    except Exception as e:
        logger.error(f"个性化筛选错误: {str(e)}")
        return _rule_based_filtering(service, candidate_exercises, user_profile, training_params, limit)

# --- Utility Functions ---

def extract_json_from_text(text: str) -> Any:
    """从文本中提取JSON数据"""
    try:
        return json.loads(text)
    except json.JSONDecodeError:
        json_patterns = [r'(\[[\s\S]*?\])', r'({[\s\S]*?})']
        for pattern in json_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                try: return json.loads(match)
                except: continue
        logger.warning(f"无法从文本中提取JSON: {text[:200]}...")
        return None

def _rule_based_filtering(
    service: 'ConversationService',
    candidate_exercises: List[Dict[str, Any]],
    user_profile: Dict[str, Any],
    training_params: Dict[str, Any],
    limit: int = 5
) -> List[Dict[str, Any]]:
    """基于规则的动作筛选（LLM失败时的备选方案）"""
    try:
        # Need profile_helper for difficulty
        from .profile_helper import _get_recommended_difficulty
        difficulty = _get_recommended_difficulty(service, user_profile)["preferred"]
    except Exception as e:
        logger.error(f"获取推荐难度失败: {e}, 使用默认难度 2")
        difficulty = 2 # Default to intermediate if profile helper fails

    filtered = [e for e in candidate_exercises if e.get("level") == difficulty]

    if len(filtered) < limit:
        filtered = candidate_exercises # Broaden pool if specific difficulty yields too few

    # Sort by closeness to user's experience level
    user_exp = user_profile.get("experience_level", 2) # Default to intermediate
    sorted_exercises = sorted(filtered, key=lambda x: abs(x.get("level", 2) - user_exp))

    result = sorted_exercises[:limit]

    # Add training parameters
    default_sets = training_params.get("sets", 3)
    default_reps = training_params.get("reps", "8-12")
    default_rest = training_params.get("rest_seconds", 60)
    for exercise in result:
        exercise.update({
            "sets": exercise.get("sets", default_sets),
            "reps": exercise.get("reps", default_reps),
            "rest_seconds": exercise.get("rest_seconds", default_rest),
            "notes": exercise.get("notes", "")
        })

    logger.info(f"规则筛选完成，选出 {len(result)} 个动作")
    return result

# --- Backward Compatibility Wrappers ---
# (Keep these until all call sites are updated)

async def extract_training_parameters(service: 'ConversationService', message: str) -> Dict[str, Any]:
    # Note: This only extracts basic params (body_part, scenario, plan_type)
    # The full extraction is now ParameterExtractor.extract_all_parameters or extract_training_params
    logger.warning("Called deprecated wrapper: extract_training_parameters. Consider using ParameterExtractor methods.")
    return await ParameterExtractor.extract_training_parameters(service, message)

async def _extract_body_part(service: 'ConversationService', message: str, intent_data: Any = None) -> Optional[str]:
    logger.warning("Called deprecated wrapper: _extract_body_part. Consider using ParameterExtractor._extract_body_part.")
    return await ParameterExtractor._extract_body_part(service, message, intent_data)

def _extract_scenario(service: 'ConversationService', message: str) -> Optional[str]:
    # Original signature included service, new one doesn't. Adapt or update caller.
    logger.warning("Called deprecated wrapper: _extract_scenario. Consider using ParameterExtractor._extract_scenario.")
    return ParameterExtractor._extract_scenario(message)

def extract_plan_type(message: str) -> Optional[str]:
    logger.warning("Called deprecated wrapper: extract_plan_type. Consider using ParameterExtractor.extract_plan_type.")
    return ParameterExtractor.extract_plan_type(message)

async def recommend_exercise_combination(service, body_part, scenario, user_profile, training_params, exercise_count=5):
    # 延迟导入，避免循环依赖
    from .exercise_recommender import ExerciseRecommender

    logger.warning("Called deprecated wrapper: recommend_exercise_combination. Consider using ExerciseRecommender class.")
    return await ExerciseRecommender.recommend_exercise_combination(
        service, body_part, scenario, user_profile, training_params, exercise_count
    )

def check_required_training_params(params: Dict[str, Any]) -> List[str]:
    logger.warning("Called deprecated wrapper: check_required_training_params.")
    # This will eventually move to a parameter validation helper
    required_fields = ["body_parts", "experience_level", "goal", "gender", "age"]
    missing = [field for field in required_fields if not params.get(field)]
    return missing

def format_training_plan_response(plan_data: Dict[str, Any], params: Dict[str, Any]) -> str:
    # 延迟导入，避免循环依赖
    from .training_plan_manager import TrainingPlanManager

    logger.warning("Called deprecated wrapper: format_training_plan_response.")
    return TrainingPlanManager.format_training_plan_response(plan_data, params)

def get_training_prompt_message(params: Dict[str, Any]) -> str:
    # 延迟导入，避免循环依赖
    from .training_plan_manager import TrainingPlanManager

    logger.warning("Called deprecated wrapper: get_training_prompt_message.")
    return TrainingPlanManager.get_training_prompt_message(params)

def get_daily_workout_prompt(training_params: Dict[str, Any]) -> str:
    # 延迟导入，避免循环依赖
    from .training_plan_manager import TrainingPlanManager

    logger.warning("Called deprecated wrapper: get_daily_workout_prompt.")
    return TrainingPlanManager.get_daily_workout_prompt(training_params)

def get_weekly_plan_prompt(training_params: Dict[str, Any]) -> str:
    # 延迟导入，避免循环依赖
    from .training_plan_manager import TrainingPlanManager

    logger.warning("Called deprecated wrapper: get_weekly_plan_prompt.")
    return TrainingPlanManager.get_weekly_plan_prompt(training_params)

def extract_training_params(user_input: str, user_profile: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    # 延迟导入，避免循环依赖
    from .parameter_extractor import ParameterExtractor

    logger.warning("Called deprecated wrapper: extract_training_params.")
    return ParameterExtractor.extract_training_params(user_input, user_profile)