from typing import Dict, Any
import logging
from app.services.conversation.states import (
    ConversationState,
    NormalConversationState,
    UserProfileCollectionState,
    TrainingParamCollectionState
)
from app.services.conversation.interruption_confirmation_state import InterruptionConfirmationState

logger = logging.getLogger(__name__)

class ConversationStateManager:
    """会话状态管理器，负责根据会话元数据确定当前会话状态"""

    def __init__(self):
        """初始化状态管理器"""
        self.states = {
            "normal": NormalConversationState(),
            "user_profile_collection": UserProfileCollectionState(),
            "training_param_collection": TrainingParamCollectionState(),
            "interruption_confirmation": InterruptionConfirmationState()
        }

    def get_state(self, meta_info: Dict[str, Any]) -> ConversationState:
        """
        根据会话元数据确定当前会话状态

        Args:
            meta_info: 会话元数据

        Returns:
            对应的ConversationState实例
        """
        # 记录当前元数据状态，用于调试
        logger.debug(f"确定会话状态 - 元数据: confirming_continuation={meta_info.get('confirming_continuation')}, "
                    f"waiting_for_info={meta_info.get('waiting_for_info') is not None}, "
                    f"collecting_training_params={meta_info.get('collecting_training_params')}")

        # 优先处理中断确认状态
        if meta_info.get("confirming_continuation"):
            logger.info("当前状态: 中断确认状态")
            return self.states["interruption_confirmation"]

        # 其次处理用户信息收集状态
        if meta_info.get("waiting_for_info"):
            logger.info("当前状态: 用户信息收集状态")
            return self.states["user_profile_collection"]

        # 再次处理训练参数收集状态
        if meta_info.get("collecting_training_params"):
            logger.info("当前状态: 训练参数收集状态")
            return self.states["training_param_collection"]

        # 默认为正常对话状态
        logger.info("当前状态: 正常对话状态")
        return self.states["normal"]