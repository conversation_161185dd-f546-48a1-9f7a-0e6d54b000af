# app/services/conversation/training_plan_manager.py
from __future__ import annotations
import logging
import json
from typing import TYPE_CHECKING, Dict, Any, List, Optional, Tuple, Set
import re
import random

# Import necessary components from other modules
# (Add imports as needed, e.g., for ConversationService)

if TYPE_CHECKING:
    from .orchestrator import ConversationService
    from app.models.user import User

logger = logging.getLogger(__name__)

# --- Training Plan Manager Class ---

class TrainingPlanManager:
    """训练计划管理器，负责计划参数检查和格式化"""
    
    @staticmethod
    def get_training_prompt_message(params: Dict[str, Any]) -> str:
        """生成训练计划询问文本"""
        body_part = params.get("body_part", "")
        scenario = params.get("scenario", "gym")
        scenario_text = "居家" if scenario == "home" else "健身房"
        
        if not body_part: return "您想训练哪个身体部位？(例如：胸部、背部、腿部、肩部等)"
        elif not scenario: return f"您想在什么环境下训练{body_part}？(居家或健身房)"
        else: return f"您是需要{scenario_text}环境下的{body_part}单日训练计划，还是周期训练计划？"
    
    @staticmethod
    def check_required_training_params(params: Dict[str, Any]) -> List[str]:
        """检查训练计划所需参数是否完整"""
        missing = []
        if not params.get("body_part"): missing.append("body_part")
        if not params.get("scenario"): missing.append("scenario")
        if not params.get("plan_type"): missing.append("plan_type")
        return missing
    
    @staticmethod
    def format_training_plan_response(plan_data: Dict[str, Any], params: Dict[str, Any]) -> str:
        """格式化训练计划响应文本"""
        body_part = params.get("body_part", "")
        scenario = params.get("scenario", "gym")
        scenario_text = "居家" if scenario == "home" else "健身房"
        plan_type = params.get("plan_type", "daily")
        
        result = f"为您生成的{scenario_text}环境下{body_part}"
        
        if plan_type == "daily":
            result += "训练计划：\n\n"
            result += f"训练名称：{plan_data.get('name', '日常训练')}\n"
            result += f"预计时长：{plan_data.get('estimated_duration', 60)}分钟\n"
            result += f"训练说明：{plan_data.get('description', '')}\n\n"
            result += "训练动作：\n"
            exercises = plan_data.get("exercises", [])
            for i, ex in enumerate(exercises, 1):
                result += f"{i}. {ex.get('name')}\n"
                result += f"   组数：{ex.get('sets', 3)}组\n"
                result += f"   次数：{ex.get('reps', '10-12')}\n"
                result += f"   休息：{ex.get('rest_seconds', 60)}秒\n"
                if ex.get('notes'): result += f"   说明：{ex.get('notes')}\n"
                result += "\n"
        else:
            result += "周期训练计划：\n\n"
            result += f"计划名称：{plan_data.get('plan_name', '周期训练')}\n"
            result += f"周期：{plan_data.get('duration_weeks', 1)}周\n"
            result += f"说明：{plan_data.get('description', '')}\n\n"
            workouts = plan_data.get("workouts", [])
            for workout in workouts:
                result += f"第{workout.get('day_number', 1)}天 - {workout.get('name', '训练日')}\n"
                result += f"时长：{workout.get('estimated_duration', 60)}分钟\n"
                exercises = workout.get("exercises", [])
                for i, ex in enumerate(exercises, 1):
                    result += f"{i}. {ex.get('name')}\n"
                    result += f"   组数：{ex.get('sets', 3)}组\n"
                    result += f"   次数：{ex.get('reps', '10-12')}\n"
                    result += f"   休息：{ex.get('rest_seconds', 60)}秒\n"
                    if ex.get('notes'): result += f"   说明：{ex.get('notes')}\n"
                result += "\n"
        
        return result
    
    @staticmethod
    def format_exercise_recommendations(exercises: List[Dict[str, Any]], training_params: Dict[str, Any]) -> str:
        """格式化训练动作推荐为可读文本"""
        if not exercises: return "抱歉，没有找到符合条件的训练动作。请尝试调整您的要求或训练目标。"
        
        result_parts = []
        body_parts = training_params.get("body_parts", [])
        body_parts_text = "全身" if not body_parts else "、".join(body_parts)
        scenario = training_params.get("scenario", "")
        title = f"为您推荐的{scenario}{body_parts_text}训练动作"
        result_parts.extend([f"# {title}", ""])
        
        training_goal = training_params.get("training_goal")
        if training_goal: result_parts.append(f"训练目标: {training_goal}")
        
        result_parts.extend(["", "## 推荐动作", ""])
        sets = training_params.get("sets", 3)
        reps = training_params.get("reps", "8-12")
        rest = training_params.get("rest_seconds", 60)
        
        for i, ex in enumerate(exercises, 1):
            result_parts.append(f"{i}. **{ex.get('name', '未知动作')}**")
            if "body_parts" in ex and ex["body_parts"]:
                result_parts.append(f"   - 训练部位: {', '.join(ex['body_parts'])}")
            result_parts.append(f"   - 推荐: {sets}组 × {reps}次 (休息{rest}秒)")
            if "description" in ex and ex["description"]:
                result_parts.append(f"   - 描述: {ex['description']}")
            if "tips" in ex and ex["tips"]:
                result_parts.append(f"   - 要点: {ex['tips']}")
            result_parts.append("")
            
        result_parts.extend(["## 训练建议", ""])
        if training_goal == "增肌":
            result_parts.extend([
                "- 选择合适的重量，最后1-2次重复应该感到吃力",
                "- 控制动作节奏，尤其是离心阶段(放下重量)",
                "- 确保足够的蛋白质摄入，促进肌肉恢复和生长"
            ])
        elif training_goal == "减脂":
            result_parts.extend([
                "- 减少组间休息时间，保持心率",
                "- 考虑将这些动作组合成循环训练",
                "- 配合有氧训练和饮食控制以获得更好的减脂效果"
            ])
        elif training_goal == "力量":
            result_parts.extend([
                "- 确保正确的动作形式，避免使用过重的重量",
                "- 每次训练后确保充分休息和恢复",
                "- 逐渐增加重量以促进力量提升"
            ])
        else:
            result_parts.extend([
                "- 注意动作质量和正确的姿势",
                "- 根据个人感觉适当调整重量和组数",
                "- 结合有氧训练获得全面的健身效果"
            ])
            
        return "\n".join(result_parts)
    
    @staticmethod
    def _format_exercise_list(service: 'ConversationService', exercises: List[Dict[str, Any]], body_part: str) -> str:
        """格式化训练动作列表输出为JSON字符串"""
        formatted_data = []
        for ex in exercises:
            formatted_data.append({
                "id": ex.get("id"),
                "name": ex.get("name"),
                "description": ex.get("description"),
                "equipment": ex.get("equipment"),
                "body_parts": ex.get("body_parts"),
                "muscles": ex.get("muscles"),
                "level": ex.get("level"),
                "image_url": ex.get("image_url"),
                "gif_url": ex.get("gif_url"),
                "video_url": ex.get("video_url"),
                "sets": ex.get("sets", 3),
                "reps": ex.get("reps", "10-12"),
                "rest_seconds": ex.get("rest_seconds", 60),
                "notes": ex.get("notes", "")
            })
        
        return json.dumps({
            "type": "exercise_list",
            "message": f"为你找到了{len(exercises)}个{body_part}训练动作：",
            "data": formatted_data
        }, ensure_ascii=False)
    
    @staticmethod
    def get_daily_workout_prompt(training_params: Dict[str, Any]) -> str:
        """生成每日训练计划的AI提示"""
        body_parts = training_params.get("body_parts", [])
        body_parts_text = "全身" if not body_parts else "、".join(body_parts)
        scenario = training_params.get("scenario", "家庭")
        training_goal = training_params.get("training_goal", "综合健身")
        experience_level = training_params.get("experience_level", 1)
        experience_map = {1: "初学者", 2: "中级健身者", 3: "高级健身者"}
        experience_text = experience_map.get(experience_level, "初学者")
        
        prompt = f"""设计一个针对{experience_text}的{body_parts_text}训练计划，训练环境为{scenario}，训练目标是{training_goal}。

请返回包含以下内容的结构化JSON格式：
{{
  "workout_name": "训练计划名称",
  "duration": 训练时长(分钟),
  "target_body_parts": ["目标部位1", "目标部位2"],
  "scenario": "{scenario}",
  "exercises": [
    {{
      "name": "动作名称",
      "sets": 组数,
      "reps": "次数范围",
      "rest_seconds": 休息秒数,
      "notes": "动作说明",
      "tips": ["技术要点1", "技术要点2"]
    }},
    // 更多动作...
  ],
  "training_tips": ["训练建议1", "训练建议2"]
}}

请确保动作适合{experience_text}水平，并且在{scenario}环境可以执行。"""
        return prompt
    
    @staticmethod
    def get_weekly_plan_prompt(training_params: Dict[str, Any]) -> str:
        """生成每周训练计划的AI提示"""
        training_goal = training_params.get("training_goal", "综合健身")
        experience_level = training_params.get("experience_level", 1)
        scenario = training_params.get("scenario", "家庭")
        experience_map = {1: "初学者", 2: "中级健身者", 3: "高级健身者"}
        experience_text = experience_map.get(experience_level, "初学者")
        days_per_week = 3 if experience_level == 1 else (4 if experience_level == 2 else 5)
        
        prompt = f"""设计一个为期一周的训练计划，适合{experience_text}，训练目标为{training_goal}，训练环境为{scenario}。

请返回包含以下内容的结构化JSON格式：
{{
  "plan_name": "训练计划名称",
  "duration_weeks": 1,
  "training_days": {days_per_week},
  "goal": "{training_goal}",
  "split_type": "训练分化方式",
  "days": [
    {{
      "day_number": 1,
      "workout_name": "训练日名称",
      "target_body_parts": ["目标部位1", "目标部位2"],
      "duration": 训练时长(分钟),
      "exercises": [
        {{
          "name": "动作名称",
          "sets": 组数,
          "reps": "次数范围",
          "rest_seconds": 休息秒数,
          "notes": "动作说明"
        }},
        // 更多动作...
      ],
      "training_tips": ["训练提示1", "训练提示2"]
    }},
    {{
      "day_number": 2,
      "is_rest_day": true,
      "recovery_tips": ["恢复建议1", "恢复建议2"]
    }},
    // 更多训练日...
  ],
  "general_tips": ["总体建议1", "总体建议2"]
}}

请设计{days_per_week}个训练日和适当的休息日，确保动作适合{experience_text}水平，并且在{scenario}环境可执行。如果是"家庭"环境，请选择无器械或简易器械的动作。"""
        return prompt
    
    @staticmethod
    def get_exercise_recommendation_prompt(training_params: Dict[str, Any]) -> str:
        """生成训练动作推荐的AI提示"""
        body_parts = training_params.get("body_parts", [])
        body_parts_text = "全身" if not body_parts else "、".join(body_parts)
        scenario = training_params.get("scenario", "家庭")
        training_goal = training_params.get("training_goal", "综合健身")
        experience_level = training_params.get("experience_level", 1)
        exercise_count = training_params.get("exercise_count", 5)
        experience_map = {1: "初学者", 2: "中级健身者", 3: "高级健身者"}
        experience_text = experience_map.get(experience_level, "初学者")
        
        prompt = f"""请为一位{experience_text}推荐{exercise_count}个适合在{scenario}环境下进行的{body_parts_text}训练动作。
训练目标是{training_goal}。

每个动作请提供以下信息：
1. 动作名称
2. 动作描述
3. 目标肌肉群
4. 建议的组数和次数
5. 技术要点和注意事项

请以结构化JSON格式返回，包含exercises数组，每个元素包含name、description、body_parts、sets、reps、rest_seconds和tips字段。"""
        return prompt 