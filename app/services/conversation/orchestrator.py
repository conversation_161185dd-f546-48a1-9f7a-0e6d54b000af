# app/services/conversation/orchestrator.py
from langchain.agents import Agent<PERSON><PERSON>cutor, create_openai_functions_agent
from langchain.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain.schema import SystemMessage, HumanMessage, AIMessage
from langchain.memory import ConversationBufferMemory
from typing import List, Dict, Any, Optional, Tuple, Union, AsyncGenerator
from sqlalchemy.orm import Session
from app.services.llm_proxy_service import LLMProxyService
from app.services.sql_tool_service import SQLToolService
from app.services.intent_recognizer import IntentRecognizer
from app.services.intent_recognition import EnhancedIntentRecognizer
from app.services.active_query_manager import ActiveQueryManager
from app import models, schemas, crud
import logging
import json
from uuid import uuid4
import time
from langchain.agents.output_parsers.openai_functions import OpenAIFunctionsAgentOutputParser
from langchain_community.chat_models import ChatOpenAI
from app.services.memory_service import MemoryService
from app.services.tool_registrar import ToolRegistrar
from app.services.llm_log_service import LLMLogService
from app.core.config import settings
from app.services.training_plan_service import TrainingPlanService
from app.schemas.exercise import ExerciseSchema
import datetime as dt
import asyncio

# 导入新的管理模块
from app.services.conversation.user_profile_manager import UserProfileManager
from app.services.conversation.training_param_manager import TrainingParamManager
from app.services.conversation.parameter_extractor import ParameterExtractor
from app.services.conversation.training_plan_manager import TrainingPlanManager
from app.services.conversation.character_manager import CharacterManager

# Import helper modules
from .utils import _save_ai_response

# 导入新模块
from app.services.conversation.state_manager import ConversationStateManager
from app.services.conversation.utils import ConversationUtils
from app.services.conversation.intent_handler import IntentHandler

logger = logging.getLogger(__name__)


class ConversationService:
    """会话服务，处理用户消息，协调各组件实现智能对话"""

    def __init__(self,
                db: Session,
                llm_proxy_service: Optional[LLMProxyService] = None,
                session_id: str = None,
                user_id: str = None,
                agent_model: str = None,
                conversation_model: str = None,
                # 添加向后兼容的参数
                llm_proxy: Optional[LLMProxyService] = None):
        """初始化对话服务

        Args:
            db: 数据库会话
            llm_proxy_service: LLM代理服务，如果未提供则创建新实例
            session_id: 对话会话ID
            user_id: 用户ID
            agent_model: 指定Agent使用的模型，默认使用系统配置
            conversation_model: 指定对话使用的模型，默认使用系统配置
            llm_proxy: 向后兼容参数，与llm_proxy_service相同
        """
        # 保存数据库会话
        self.db = db

        # 生成会话ID
        self.session_id = session_id or str(uuid4())
        self.user_id = user_id

        # 添加响应处理状态跟踪
        self.response_processed = False

        # 初始化服务 - 参数兼容处理
        self.llm_proxy = llm_proxy_service or llm_proxy or LLMProxyService()
        self.memory = MemoryService(self.session_id)

        # 工具服务
        self.sql_tool = SQLToolService(self.db)

        # 初始化LLM日志服务
        self.llm_log = LLMLogService()

        # 初始化意图识别器
        self.intent_recognizer = EnhancedIntentRecognizer(self.llm_proxy)

        # 初始化用户信息管理器（替代原有的ActiveQueryManager）
        self.user_profile_manager = UserProfileManager(self.llm_proxy)

        # 保留原ActiveQueryManager以兼容现有代码
        self.active_query_manager = ActiveQueryManager(self.llm_proxy)

        # 初始化训练参数管理器
        self.training_param_manager = TrainingParamManager(self)

        # 初始化角色管理器
        self.character_manager = CharacterManager(self.llm_proxy)

        # 初始化Agent
        self.agent_model = agent_model or getattr(settings, 'AGENT_MODEL', settings.LLM_MODEL)
        self.conversation_model = conversation_model or settings.LLM_MODEL

        # 注册工具
        self.tool_registrar = ToolRegistrar(self.sql_tool)
        self.all_tools = self.tool_registrar.get_all_tools()

        # 初始化Agent
        self._initialize_agent()

        # 系统消息 - 更新系统提示，使回复更简洁自然
        self.system_message = """你是一个专业的健身教练和营养师。你的目标是帮助用户实现他们的健身目标、改善体型和健康水平。

你能提供以下服务：
- 针对不同健身水平的训练计划建议
- 针对不同训练目标的训练动作推荐
- 饮食和营养建议
- 健身常识和科学健身方法
- 健身误区纠正
- 健康生活方式建议

回复要求：
- 保持简洁、直接地回答用户的问题
- 直接提供用户所需的信息，避免不必要的介绍性或结束性短语
- 采用自然、支持性的对话语气
- 避免使用"希望这对你有帮助"、"如有其他问题请告诉我"等套话
- 如果用户问特定部位的训练动作，直接推荐具体动作而不是笼统介绍

专业要求：
- 保持专业、积极和有支持性的态度
- 提供基于科学证据的健身和营养建议
- 根据用户的具体情况提供个性化建议
- 强调安全和循序渐进的重要性
- 清晰解释专业术语
- 在建议超出你专业知识范围时承认局限性

禁止事项：
- 提供医疗诊断或治疗建议
- 推荐可能危险的极端饮食或训练方法
- 保证特定结果
- 分享未经科学验证的健身或营养信息
"""

        # 添加状态管理器
        self.state_manager = ConversationStateManager()

        # 添加意图处理器
        self.intent_handler = IntentHandler(
            db=self.db,
            llm_proxy=self,  # 传递self，让IntentHandler从中提取llm_proxy属性
            agent_executor=self.agent_executor,
            sql_tool=self.sql_tool,
            conversation_model=self.conversation_model
        )

        # 添加待处理请求管理器
        from app.services.conversation.pending_request_manager import PendingRequestManager
        self.pending_request_manager = PendingRequestManager(self.intent_handler)

    def _initialize_agent(self):
        """初始化代理"""
        # 获取使用Agent特定设置的LLM
        llm = self.llm_proxy.get_llm(model=self.agent_model)

        # 定义更强大的系统提示，增强Agent能力
        system_prompt = """你是健身AI教练，一个专业的健身和营养顾问。你的目标是帮助用户实现健身目标、改善体型和健康水平。

你具有以下能力:
1. 理解用户需求 - 你能准确理解用户的健身需求和目标
2. 个性化建议 - 根据用户的身体状况、健身经验和目标提供个性化建议
3. 工具使用 - 你可以使用各种工具来帮助用户，包括查询训练动作、制定训练计划、分析饮食等
4. 任务规划 - 你能将复杂的健身目标分解为可执行的步骤
5. 上下文记忆 - 你能记住对话上下文，避免重复询问已知信息

你可以使用以下工具:
- 查询训练动作数据库
- 为用户创建个性化训练计划
- 提供营养和健身建议
- 分析用户的健身数据
- 跟踪用户的健身进度

在回答用户问题时:
1. 首先分析用户的需求和上下文
2. 确定需要使用的工具或知识
3. 执行必要的查询或计算
4. 提供清晰、专业、个性化的回答
5. 避免重复已知信息，保持对话简洁

基于用户的需求，选择合适的工具提供帮助。如果用户询问的问题不在你的专业范围内，坦诚告知并建议他们咨询相关专业人士。"""

        prompt = ChatPromptTemplate.from_messages([
            ("system", system_prompt),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", "{input}"),
            MessagesPlaceholder(variable_name="agent_scratchpad"),
        ])

        # 创建更强大的Agent
        agent = create_openai_functions_agent(llm, self.all_tools, prompt)
        self.agent_executor = AgentExecutor(
            agent=agent,
            tools=self.all_tools,
            verbose=False,
            handle_parsing_errors=True,  # 增强错误处理能力
            max_iterations=5,  # 限制最大迭代次数，避免无限循环
            return_intermediate_steps=True  # 返回中间步骤，便于调试
        )

        # 使用静态变量存储Agent实例，避免重复初始化
        if not hasattr(ConversationService, '_agent_instance'):
            ConversationService._agent_instance = self.agent_executor
            logger.info("首次初始化Agent完成，使用agent-app应用进行智能对话")
        else:
            # 如果已经存在Agent实例，直接使用
            self.agent_executor = ConversationService._agent_instance
            logger.info("使用已存在的Agent实例，避免重复初始化")

    async def handle_user_info_update(
        self,
        user_id: int,
        field: str,
        value: Any,
        conversation_id: int
    ) -> Tuple[str, Dict[str, Any]]:
        """处理用户信息更新请求"""
        logger.info(f"处理用户信息更新: user_id={user_id}, field={field}, value={value}")

        # 使用UserProfileManager处理更新
        return await self.user_profile_manager.handle_user_info_update(
            self.db, user_id, field, value
        )

    async def _handle_user_profile_collection(
        self,
        user_id: int,
        message: str,
        conversation_id: int,
        user: 'models.User',
        conversation: 'models.Conversation',
        response_meta_info: Dict[str, Any],
        skipped_fields: List[str]
    ) -> AsyncGenerator[Union[str, Dict[str, Any]], None]:
        """处理用户信息收集状态

        处理等待用户输入个人信息的状态，验证用户输入并进行数据库更新

        Args:
            user_id: 用户ID
            message: 用户消息
            conversation_id: 会话ID
            user: 用户模型对象
            conversation: 会话模型对象
            response_meta_info: 响应元数据
            skipped_fields: 已跳过的字段列表

        Yields:
            字符串消息或元数据更新字典
        """
        waiting_for_info = response_meta_info.get("waiting_for_info")
        pending_request_data = response_meta_info.get("pending_request")

        if not waiting_for_info or not isinstance(waiting_for_info, dict):
            return

        current_field = waiting_for_info.get("field")
        current_attempt = waiting_for_info.get("attempt", 0)

        if not current_field:
            logger.warning("等待信息，但current_field为空")
            response_meta_info["waiting_for_info"] = None
            yield {"meta_info_update": {"waiting_for_info": None}}
            return

        logger.info(f"处理字段 '{current_field}' 的回复，尝试次数: {current_attempt}")
        is_valid, parsed_value = await self.user_profile_manager.validate_and_parse(current_field, message)

        if is_valid:
            # 验证成功，更新用户数据
            logger.info(f"用户字段 '{current_field}' 验证成功: {parsed_value}")
            update_value = parsed_value

            # 处理枚举值映射
            if current_field in self.user_profile_manager.FIELD_ENUM_MAP:
                field_mapping = self.user_profile_manager.FIELD_ENUM_MAP[current_field]
                for code, text in field_mapping.items():
                    if text == parsed_value:
                        update_value = code
                        logger.info(f"字段 '{current_field}' 的值从文本 '{parsed_value}' 映射到整数代码: {update_value}")
                        break
                else:
                    logger.warning(f"无法将 '{current_field}' 的值 '{parsed_value}' 映射到整数代码")

            # 更新数据库
            user_update = {current_field: update_value}
            try:
                crud.crud_user.update(self.db, db_obj=user, obj_in=user_update)
                logger.debug(f"更新用户字段 '{current_field}' 成功")

                # 更新本地用户数据
                user_data = user.__dict__.copy()
                user_data[current_field] = update_value

                # 计算BMI（如果有必要）
                if current_field in ["height", "weight"] and user.height and user.weight:
                    try:
                        height_m = user.height / 100
                        bmi = user.weight / (height_m * height_m)
                        crud.crud_user.update(self.db, db_obj=user, obj_in={"bmi": round(bmi, 2)})
                        user_data["bmi"] = round(bmi, 2)
                        logger.debug(f"更新用户BMI成功: {round(bmi, 2)}")
                    except Exception as e:
                        logger.error(f"计算BMI出错: {str(e)}")

                # 重置等待状态
                response_meta_info["waiting_for_info"] = None
                yield {"meta_info_update": {"waiting_for_info": None}}

                # 检查剩余字段
                remaining_fields = self.user_profile_manager.get_missing_fields(user_data, skipped_fields)

                if response_meta_info.get("conversation_state") == "guided" and remaining_fields:
                    # 继续询问下一个字段
                    next_field = remaining_fields[0]
                    response_meta_info["waiting_for_info"] = {"field": next_field, "attempt": 0}
                    yield {"meta_info_update": {"waiting_for_info": response_meta_info["waiting_for_info"]}}
                    query_message = await self.user_profile_manager.generate_query_message([next_field])
                    yield query_message
                else:
                    # 信息收集完成或退出引导模式
                    # 检查是否有待处理请求
                    if pending_request_data:
                        logger.info("信息收集完成，恢复处理之前的请求")
                        # 弹出待处理请求
                        pending_request = response_meta_info.pop('pending_request', None)
                        response_meta_info["conversation_state"] = "normal"
                        yield {"meta_info_update": {"conversation_state": "normal", "pending_request": None}}

                        if pending_request:
                            original_message = pending_request.get("original_message")
                            original_intent_dict = pending_request.get("original_intent")

                            from app.services.intent_recognizer import IntentData
                            original_intent = IntentData(**original_intent_dict) if original_intent_dict else None

                            if original_message and original_intent:
                                # 更新user为最新数据
                                user = crud.crud_user.get(self.db, id=user_id)
                                # 调用意图执行逻辑
                                from app.core.chat_config import HISTORY_MESSAGE_LIMIT_EXTENDED
                                raw_messages = crud.crud_message.get_conversation_history_raw(
                                    self.db, conversation_id=conversation_id, limit=HISTORY_MESSAGE_LIMIT_EXTENDED
                                )
                                async for chunk in _handle_intent_execution(
                                    self,
                                    user_id=user_id,
                                    message=original_message,
                                    intent_data=original_intent,
                                    user=user,
                                    conversation=conversation,
                                    response_meta_info=response_meta_info,
                                    messages=raw_messages
                                ):
                                    yield chunk

                                # 更新会话元数据
                                crud.crud_conversation.update(self.db, db_obj=conversation, obj_in={"meta_info": response_meta_info})
                                self.db.commit()
                                return
                        else:
                            logger.warning("找到待处理请求标记，但数据不完整，无法恢复")

                    # 没有待处理请求或恢复失败
                    logger.info("信息收集完成，没有待处理请求或恢复失败")
                    response_meta_info["conversation_state"] = "normal"
                    yield {"meta_info_update": {"conversation_state": "normal"}}
                    user_info_text = self.user_profile_manager.format_user_info(user_data)
                    completion_message = f"感谢您提供的信息！以下是您的个人资料：\n\n{user_info_text}\n\n您的信息已更新，我可以为您提供更个性化的健身建议了。有什么我可以帮您的吗？"
                    yield completion_message

            except Exception as e:
                logger.error(f"更新用户数据出错: {str(e)}")
                yield f"更新信息时出错: {str(e)}"
                response_meta_info["waiting_for_info"] = None
                yield {"meta_info_update": {"waiting_for_info": None}}

        else:
            # 输入无效，处理重试逻辑
            current_attempt += 1
            if current_attempt >= 2:
                # 超过重试次数，跳过此字段
                logger.info(f"跳过字段 '{current_field}' (尝试 {current_attempt} 次后失败)")
                if current_field not in skipped_fields:
                    skipped_fields.append(current_field)
                response_meta_info["skipped_fields"] = skipped_fields
                yield f"由于无法识别您的输入，我将暂时跳过这个问题。"
                response_meta_info["waiting_for_info"] = None
                yield {"meta_info_update": {"waiting_for_info": None, "skipped_fields": skipped_fields}}

                # 检查是否有待处理请求
                if pending_request_data:
                    logger.info("跳过字段后，恢复处理之前的请求")
                    pending_request = response_meta_info.pop('pending_request', None)
                    response_meta_info["conversation_state"] = "normal"
                    yield {"meta_info_update": {"conversation_state": "normal", "pending_request": None}}

                    if pending_request:
                        original_message = pending_request.get("original_message")
                        original_intent_dict = pending_request.get("original_intent")
                        from app.services.intent_recognizer import IntentData
                        original_intent = IntentData(**original_intent_dict) if original_intent_dict else None

                        if original_message and original_intent:
                            # 更新user为最新数据
                            user = crud.crud_user.get(self.db, id=user_id)
                            # 调用意图执行逻辑
                            from app.core.chat_config import HISTORY_MESSAGE_LIMIT_EXTENDED
                            raw_messages = crud.crud_message.get_conversation_history_raw(
                                self.db, conversation_id=conversation_id, limit=HISTORY_MESSAGE_LIMIT_EXTENDED
                            )

                            # 处理历史消息格式
                            from app.services.conversation.utils import ConversationUtils
                            history = ConversationUtils.process_history(raw_messages)

                            # 准备用户数据
                            user_data = {
                                "id": user.id,
                                "nickname": user.nickname,
                                "gender": user.gender,
                                "age": user.age,
                                "height": user.height,
                                "weight": user.weight,
                                "fitness_goal": user.fitness_goal,
                                "experience_level": user.experience_level
                            }

                            # 使用意图处理器处理意图
                            async for chunk in self.intent_handler.handle_intent(
                                original_intent,
                                response_meta_info,
                                user_data,
                                history
                            ):
                                yield chunk

                            # 更新会话元数据
                            crud.crud_conversation.update(self.db, db_obj=conversation, obj_in={"meta_info": response_meta_info})
                            self.db.commit()
                            return
                    else:
                        logger.warning("找到待处理请求标记，但数据不完整")

                # 如果没有待处理请求，检查是否还有其他字段要问
                if response_meta_info.get("conversation_state") == "guided":
                    # 获取最新用户数据
                    user_data = user.__dict__.copy()
                    remaining_fields = self.user_profile_manager.get_missing_fields(user_data, skipped_fields)

                    if remaining_fields:
                        # 继续询问下一个字段
                        next_field = remaining_fields[0]
                        response_meta_info["waiting_for_info"] = {"field": next_field, "attempt": 0}
                        yield {"meta_info_update": {"waiting_for_info": response_meta_info["waiting_for_info"]}}
                        query_message = await self.user_profile_manager.generate_query_message([next_field])
                        yield query_message
                    else:
                        # 所有字段处理完毕
                        response_meta_info["conversation_state"] = "normal"
                        yield {"meta_info_update": {"conversation_state": "normal"}}
                        yield "感谢您提供的信息。虽然有些信息我无法识别，但我会尽力为您服务。有什么我可以帮您的吗？"

            else:
                # 重试询问
                response_meta_info["waiting_for_info"] = {"field": current_field, "attempt": current_attempt}
                yield {"meta_info_update": {"waiting_for_info": response_meta_info["waiting_for_info"]}}
                query_message = await self.user_profile_manager.generate_query_message([current_field], is_retry=True)
                yield query_message

        # 更新会话元数据
        crud.crud_conversation.update(self.db, db_obj=conversation, obj_in={"meta_info": response_meta_info})

    async def _handle_training_param_collection(
        self,
        user_id: int,
        message: str,
        conversation_id: int,
        user: 'models.User',
        conversation: 'models.Conversation',
        response_meta_info: Dict[str, Any]
    ) -> AsyncGenerator[Union[str, Dict[str, Any]], None]:
        """处理训练参数收集状态

        处理等待用户输入训练参数的状态，包括body_part, scenario, plan_type等

        Args:
            user_id: 用户ID
            message: 用户消息
            conversation_id: 会话ID
            user: 用户模型对象
            conversation: 会话模型对象
            response_meta_info: 响应元数据

        Yields:
            字符串消息或元数据更新字典
        """
        # 检查当前状态
        collecting_training_params = response_meta_info.get("collecting_training_params", False)

        # 兼容旧逻辑：如果存在awaiting_scenario状态，将其转换为新逻辑
        if response_meta_info.get("awaiting_scenario", False):
            collecting_training_params = True
            response_meta_info["collecting_training_params"] = True
            response_meta_info["asking_param"] = "scenario"
            # 确保training_params存在
            if "training_params" not in response_meta_info:
                response_meta_info["training_params"] = {}
            # 如果有已识别的身体部位，添加到训练参数中
            if "identified_body_part" in response_meta_info:
                response_meta_info["training_params"]["body_part"] = response_meta_info["identified_body_part"]
            # 清除旧状态
            response_meta_info["awaiting_scenario"] = False

        # 处理参数收集状态
        if collecting_training_params:
            # 获取已收集的训练参数
            training_params = response_meta_info.get("training_params", {})

            # 记录原始参数，用于日志
            original_params = training_params.copy()
            logger.info(f"处理训练参数收集，原始参数: {original_params}")

            # 获取当前正在询问的参数
            asking_param = response_meta_info.get("asking_param")

            # 使用ParameterExtractor进行强化提取
            try:
                # 首先尝试使用参数提取器进行强化提取
                param_extractor = ParameterExtractor(self.llm_proxy)

                # 根据当前询问的参数进行强化提取
                if asking_param:
                    logger.info(f"使用强化提取步骤提取参数: {asking_param}")
                    extracted_value = await param_extractor.extract_parameter(
                        param_name=asking_param,
                        user_message=message,
                        context=f"正在收集训练参数: {asking_param}"
                    )

                    if extracted_value:
                        # 如果成功提取到参数值，更新训练参数
                        if asking_param == "body_part":
                            # 对于body_part，保持列表格式
                            training_params[asking_param] = [extracted_value]
                        else:
                            training_params[asking_param] = extracted_value

                        logger.info(f"强化提取成功: {asking_param}={extracted_value}")
                    else:
                        logger.info(f"强化提取未能提取到值，尝试使用常规提取方法")
                        # 如果强化提取失败，回退到常规提取方法
                        extracted_params = await self.training_param_manager.extract_training_parameters(message, training_params)
                        training_params.update(extracted_params)
                else:
                    # 如果没有指定要提取的参数，使用常规提取方法
                    logger.info("未指定要提取的参数，使用常规提取方法")
                    extracted_params = await self.training_param_manager.extract_training_parameters(message, training_params)
                    training_params.update(extracted_params)
            except Exception as e:
                logger.error(f"强化提取参数时出错: {str(e)}，回退到常规提取方法")
                # 如果强化提取出错，回退到常规提取方法
                extracted_params = await self.training_param_manager.extract_training_parameters(message, training_params)
                training_params.update(extracted_params)

            # 记录更新后的参数
            logger.info(f"最终提取的训练参数: {training_params}")

            # 根据当前询问的参数类型处理用户回答
            if asking_param == "body_part":
                # 处理身体部位回答
                body_parts = training_params.get("body_part", [])

                if body_parts and isinstance(body_parts, list) and len(body_parts) > 0:
                    # 立即检查下一个缺失参数并更新asking_param
                    missing_params = self.training_param_manager.check_required_training_params(training_params)
                    next_param = missing_params[0] if missing_params else None
                    if next_param:
                        response_meta_info["asking_param"] = next_param
                        response_meta_info["training_params"] = training_params
                        yield {"meta_info_update": {"asking_param": next_param, "training_params": training_params}}

                    yield f"了解，您想训练的是{body_parts[0]}。"

                    # 如果有下一个参数，立即询问
                    if next_param:
                        prompt_message = self.training_param_manager.get_training_prompt_message(training_params)
                        yield prompt_message

                    # 更新会话元数据并返回，避免继续执行后面的代码
                    crud.crud_conversation.update(self.db, db_obj=conversation, obj_in={"meta_info": response_meta_info})
                    return
                elif "body_part" in extracted_params and isinstance(extracted_params["body_part"], str):
                    # 处理单个字符串情况
                    body_part = extracted_params["body_part"]
                    training_params["body_part"] = body_part
                    # 同时更新muscle字段，如果存在
                    if "muscle" in extracted_params and extracted_params["muscle"]:
                        training_params["muscle"] = extracted_params["muscle"]

                    # 立即检查下一个缺失参数并更新asking_param
                    missing_params = self.training_param_manager.check_required_training_params(training_params)
                    next_param = missing_params[0] if missing_params else None
                    if next_param:
                        response_meta_info["asking_param"] = next_param
                        response_meta_info["training_params"] = training_params
                        yield {"meta_info_update": {"asking_param": next_param, "training_params": training_params}}

                    yield f"了解，您想训练的是{body_part}。"

                    # 如果有下一个参数，立即询问
                    if next_param:
                        prompt_message = self.training_param_manager.get_training_prompt_message(training_params)
                        yield prompt_message

                    # 更新会话元数据并返回，避免继续执行后面的代码
                    crud.crud_conversation.update(self.db, db_obj=conversation, obj_in={"meta_info": response_meta_info})
                    return
                else:
                    # 尝试识别是否有不标准格式数据
                    try:
                        # 如果提取的参数是字典格式
                        if isinstance(extracted_params, dict) and len(extracted_params) > 0:
                            first_key = list(extracted_params.keys())[0]
                            if "胸" in first_key:
                                training_params["body_part"] = "胸部"
                                # 同时更新muscle字段，如果存在
                                if "muscle" in extracted_params and extracted_params["muscle"]:
                                    training_params["muscle"] = extracted_params["muscle"]

                                # 立即检查下一个缺失参数并更新asking_param
                                missing_params = self.training_param_manager.check_required_training_params(training_params)
                                next_param = missing_params[0] if missing_params else None
                                if next_param:
                                    response_meta_info["asking_param"] = next_param
                                    response_meta_info["training_params"] = training_params
                                    yield {"meta_info_update": {"asking_param": next_param, "training_params": training_params}}

                                yield f"了解，您想训练的是胸部。"

                                # 如果有下一个参数，立即询问
                                if next_param:
                                    prompt_message = self.training_param_manager.get_training_prompt_message(training_params)
                                    yield prompt_message

                                crud.crud_conversation.update(self.db, db_obj=conversation, obj_in={"meta_info": response_meta_info})
                                return
                    except Exception as e:
                        logger.error(f"处理不标准body_part格式时出错: {str(e)}")

                    # 尝试直接提取单个身体部位
                    body_part = await ParameterExtractor._extract_body_part(self, message, None)
                    if body_part:
                        training_params["body_part"] = [body_part]  # 保持body_part为列表格式
                        # 同时更新muscle字段，如果存在
                        if "muscle" in extracted_params and extracted_params["muscle"]:
                            training_params["muscle"] = extracted_params["muscle"]

                        # 立即检查下一个缺失参数并更新asking_param
                        missing_params = self.training_param_manager.check_required_training_params(training_params)
                        next_param = missing_params[0] if missing_params else None
                        if next_param:
                            response_meta_info["asking_param"] = next_param
                            response_meta_info["training_params"] = training_params
                            yield {"meta_info_update": {"asking_param": next_param, "training_params": training_params}}

                        yield f"了解，您想训练的是{body_part}。"

                        # 如果有下一个参数，立即询问
                        if next_param:
                            prompt_message = self.training_param_manager.get_training_prompt_message(training_params)
                            yield prompt_message

                        # 更新会话元数据并返回，避免继续执行后面的代码
                        crud.crud_conversation.update(self.db, db_obj=conversation, obj_in={"meta_info": response_meta_info})
                        return
                    else:
                        # 无法识别时提供选项
                        yield "抱歉，我无法确定您想训练的身体部位。请选择以下常见部位之一：胸部、背部、肩部、手臂、腿部、腰腹部"
                        # 不更新参数，下次循环继续询问
                        crud.crud_conversation.update(self.db, db_obj=conversation, obj_in={"meta_info": response_meta_info})
                        return

            elif asking_param == "scenario":
                # 处理训练场景回答
                # 场景信息已在前面提取并更新到training_params中
                scenario = training_params.get("scenario")

                if scenario:
                    # 保持兼容性：同时更新旧字段
                    response_meta_info["scenario"] = scenario
                    scenario_name = "居家" if scenario == "home" else "健身房"

                    # 立即检查下一个缺失参数并更新asking_param
                    missing_params = self.training_param_manager.check_required_training_params(training_params)
                    next_param = missing_params[0] if missing_params else None
                    if next_param:
                        response_meta_info["asking_param"] = next_param
                        response_meta_info["training_params"] = training_params
                        yield {"meta_info_update": {"asking_param": next_param, "training_params": training_params}}

                    yield f"好的，您选择了{scenario_name}训练环境。"

                    # 如果有下一个参数，立即询问
                    if next_param:
                        prompt_message = self.training_param_manager.get_training_prompt_message(training_params)
                        yield prompt_message

                    # 更新会话元数据并返回，避免继续执行后面的代码
                    crud.crud_conversation.update(self.db, db_obj=conversation, obj_in={"meta_info": response_meta_info})
                    return
                else:
                    # 默认设置为健身房
                    training_params["scenario"] = "gym"
                    response_meta_info["scenario"] = "gym"

                    # 合并其他可能的参数
                    for key in ["body_part", "muscle", "equipment", "plan_type"]:
                        if key in extracted_params and extracted_params[key] and (key not in training_params or not training_params[key]):
                            training_params[key] = extracted_params[key]

                    # 立即检查下一个缺失参数并更新asking_param
                    missing_params = self.training_param_manager.check_required_training_params(training_params)
                    next_param = missing_params[0] if missing_params else None
                    if next_param:
                        response_meta_info["asking_param"] = next_param
                        response_meta_info["training_params"] = training_params
                        yield {"meta_info_update": {"asking_param": next_param, "training_params": training_params}}

                    yield "未能明确识别训练场景，我将默认为您提供健身房环境下的训练。"

                    # 如果有下一个参数，立即询问
                    if next_param:
                        prompt_message = self.training_param_manager.get_training_prompt_message(training_params)
                        yield prompt_message

                    # 更新会话元数据并返回，避免继续执行后面的代码
                    crud.crud_conversation.update(self.db, db_obj=conversation, obj_in={"meta_info": response_meta_info})
                    return

            elif asking_param == "plan_type":
                # 处理计划类型回答
                # 计划类型信息已在前面提取并更新到training_params中
                plan_type = training_params.get("plan_type")

                if plan_type == "daily":
                    # 检查是否所有必要参数都已收集
                    missing_params = self.training_param_manager.check_required_training_params(training_params)
                    if not missing_params:
                        # 所有参数已收集完成
                        response_meta_info["collecting_training_params"] = False
                        response_meta_info["training_params"] = training_params
                        yield {"meta_info_update": {"collecting_training_params": False, "training_params": training_params}}

                    yield "好的，我将为您生成单日训练计划。"

                    # 如果所有参数已收集完成，转到处理训练计划意图
                    if not missing_params:
                        # 所有参数已收集完成，开始生成训练计划
                        logger.info(f"训练参数收集完成: {training_params}")

                        # 创建训练计划意图
                        from app.services.intent_recognizer import IntentData
                        intent_data = IntentData(
                            intent="daily_workout_plan",
                            confidence=1.0,
                            parameters={"body_part": training_params.get("body_part")}
                        )

                        # 复制参数到元数据
                        response_meta_info["intent"] = intent_data.intent
                        response_meta_info["confidence"] = intent_data.confidence
                        response_meta_info["parameters"] = intent_data.parameters

                        # 处理训练计划意图
                        from app.core.chat_config import HISTORY_MESSAGE_LIMIT_EXTENDED
                        raw_messages = crud.crud_message.get_conversation_history_raw(
                            self.db, conversation_id=conversation_id, limit=HISTORY_MESSAGE_LIMIT_EXTENDED
                        )
                        # 处理历史消息格式
                        from app.services.conversation.utils import ConversationUtils
                        history = ConversationUtils.process_history(raw_messages)

                        # 准备用户数据
                        user_data = {
                            "id": user.id,
                            "nickname": user.nickname,
                            "gender": user.gender,
                            "age": user.age,
                            "height": user.height,
                            "weight": user.weight,
                            "fitness_goal": user.fitness_goal,
                            "experience_level": user.experience_level
                        }

                        # 使用意图处理器处理意图
                        async for chunk in self.intent_handler.handle_intent(
                            intent_data,
                            response_meta_info,
                            user_data,
                            history
                        ):
                            yield chunk

                    # 更新会话元数据并返回，避免继续执行后面的代码
                    crud.crud_conversation.update(self.db, db_obj=conversation, obj_in={"meta_info": response_meta_info})
                    return
                elif plan_type == "weekly":
                    # 检查是否所有必要参数都已收集
                    missing_params = self.training_param_manager.check_required_training_params(training_params)
                    if not missing_params:
                        # 所有参数已收集完成
                        response_meta_info["collecting_training_params"] = False
                        response_meta_info["training_params"] = training_params
                        yield {"meta_info_update": {"collecting_training_params": False, "training_params": training_params}}

                    yield "好的，我将为您生成周期训练计划。"

                    # 如果所有参数已收集完成，转到处理训练计划意图
                    if not missing_params:
                        # 所有参数已收集完成，开始生成训练计划
                        logger.info(f"训练参数收集完成: {training_params}")

                        # 创建训练计划意图
                        from app.services.intent_recognizer import IntentData
                        intent_data = IntentData(
                            intent="weekly_training_plan",
                            confidence=1.0,
                            parameters={"body_part": training_params.get("body_part")}
                        )

                        # 复制参数到元数据
                        response_meta_info["intent"] = intent_data.intent
                        response_meta_info["confidence"] = intent_data.confidence
                        response_meta_info["parameters"] = intent_data.parameters

                        # 处理训练计划意图
                        from app.core.chat_config import HISTORY_MESSAGE_LIMIT_EXTENDED
                        raw_messages = crud.crud_message.get_conversation_history_raw(
                            self.db, conversation_id=conversation_id, limit=HISTORY_MESSAGE_LIMIT_EXTENDED
                        )
                        # 处理历史消息格式
                        from app.services.conversation.utils import ConversationUtils
                        history = ConversationUtils.process_history(raw_messages)

                        # 准备用户数据
                        user_data = {
                            "id": user.id,
                            "nickname": user.nickname,
                            "gender": user.gender,
                            "age": user.age,
                            "height": user.height,
                            "weight": user.weight,
                            "fitness_goal": user.fitness_goal,
                            "experience_level": user.experience_level
                        }

                        # 使用意图处理器处理意图
                        async for chunk in self.intent_handler.handle_intent(
                            intent_data,
                            response_meta_info,
                            user_data,
                            history
                        ):
                            yield chunk

                    # 更新会话元数据并返回，避免继续执行后面的代码
                    crud.crud_conversation.update(self.db, db_obj=conversation, obj_in={"meta_info": response_meta_info})
                    return
                else:
                    # 默认为单日计划
                    training_params["plan_type"] = "daily"

                    # 检查是否所有必要参数都已收集
                    missing_params = self.training_param_manager.check_required_training_params(training_params)
                    if not missing_params:
                        # 所有参数已收集完成
                        response_meta_info["collecting_training_params"] = False
                        response_meta_info["training_params"] = training_params
                        yield {"meta_info_update": {"collecting_training_params": False, "training_params": training_params}}

                    yield "未能明确识别计划类型，我将默认为您生成单日训练计划。"

                    # 如果所有参数已收集完成，转到处理训练计划意图
                    if not missing_params:
                        # 所有参数已收集完成，开始生成训练计划
                        logger.info(f"训练参数收集完成: {training_params}")

                        # 创建训练计划意图
                        from app.services.intent_recognizer import IntentData
                        intent_data = IntentData(
                            intent="daily_workout_plan",
                            confidence=1.0,
                            parameters={"body_part": training_params.get("body_part")}
                        )

                        # 复制参数到元数据
                        response_meta_info["intent"] = intent_data.intent
                        response_meta_info["confidence"] = intent_data.confidence
                        response_meta_info["parameters"] = intent_data.parameters

                        # 处理训练计划意图
                        from app.core.chat_config import HISTORY_MESSAGE_LIMIT_EXTENDED
                        raw_messages = crud.crud_message.get_conversation_history_raw(
                            self.db, conversation_id=conversation_id, limit=HISTORY_MESSAGE_LIMIT_EXTENDED
                        )
                        # 处理历史消息格式
                        from app.services.conversation.utils import ConversationUtils
                        history = ConversationUtils.process_history(raw_messages)

                        # 准备用户数据
                        user_data = {
                            "id": user.id,
                            "nickname": user.nickname,
                            "gender": user.gender,
                            "age": user.age,
                            "height": user.height,
                            "weight": user.weight,
                            "fitness_goal": user.fitness_goal,
                            "experience_level": user.experience_level
                        }

                        # 使用意图处理器处理意图
                        async for chunk in self.intent_handler.handle_intent(
                            intent_data,
                            response_meta_info,
                            user_data,
                            history
                        ):
                            yield chunk

                    # 更新会话元数据并返回，避免继续执行后面的代码
                    crud.crud_conversation.update(self.db, db_obj=conversation, obj_in={"meta_info": response_meta_info})
                    return

    async def _check_message_relevance(self, message: str, last_assistant_message: 'models.Message') -> bool:
        """
        检查用户新消息与之前流程的相关性

        Args:
            message: 用户新消息
            last_assistant_message: 上一条AI助手消息

        Returns:
            bool: 如果相关返回True，否则返回False
        """
        try:
            # 获取上一条消息的内容
            last_content = last_assistant_message.content

            # 构建提示
            prompt = f"""
            我需要判断用户的新消息是否与之前的对话流程相关。

            上一条AI助手消息: "{last_content}"

            用户新消息: "{message}"

            请分析用户的新消息是否与上一条AI助手消息中提到的流程或问题相关。
            如果用户的新消息是对上一条AI助手消息的直接回应，或者与上一条消息中提到的主题相关，则判断为"相关"。
            如果用户的新消息引入了全新的话题，与上一条消息无关，则判断为"不相关"。

            只需回答"相关"或"不相关"。
            """

            # 使用aget_chat_response代替agenerate_text
            response = await self.llm_proxy.aget_chat_response(
                messages=[
                    {"role": "system", "content": "你是一个对话分析助手，负责判断用户消息与上下文的相关性。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1
            )

            # 解析响应
            is_related = "相关" in response.lower()
            logger.info(f"消息相关性判断结果: {is_related}, 原始响应: {response}")

            return is_related
        except Exception as e:
            logger.error(f"检查消息相关性时出错: {str(e)}")
            # 出错时默认为相关，避免打断用户体验
            return True

    async def _analyze_continuation_response(self, message: str) -> bool:
        """
        分析用户对是否继续之前流程的回复

        Args:
            message: 用户回复消息

        Returns:
            bool: 如果用户选择继续之前的流程返回True，否则返回False
        """
        try:
            # 构建提示
            prompt = f"""
            我需要判断用户是否想继续之前的对话流程。

            用户回复: "{message}"

            如果用户表示想继续之前的流程（例如："继续"、"是的"、"好的"、"继续之前的"等），判断为"继续"。
            如果用户表示想处理新问题（例如："回答新问题"、"不用了"、"回答刚才的问题"等），判断为"新问题"。

            只需回答"继续"或"新问题"。
            """

            # 使用aget_chat_response代替agenerate_text
            response = await self.llm_proxy.aget_chat_response(
                messages=[
                    {"role": "system", "content": "你是一个对话分析助手，负责判断用户是否想继续之前的对话流程。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1
            )

            # 解析响应
            continue_flow = "继续" in response.lower()
            logger.info(f"用户回复分析结果: {'继续流程' if continue_flow else '处理新问题'}, 原始响应: {response}")

            return continue_flow
        except Exception as e:
            logger.error(f"分析用户回复时出错: {str(e)}")
            # 出错时默认处理新问题，避免用户被卡在旧流程
            return False

    async def process_message_stream(self, message, meta_info=None, quick_intent=None, user_id=None, conversation_id=None):
        """处理用户消息并以流式方式返回响应

        Args:
            message: 用户消息内容
            meta_info: 元数据信息，可选
            quick_intent: 快速意图，可选
            user_id: 用户ID，可选，如果提供则覆盖初始化时设置的user_id
            conversation_id: 会话ID，可选，如果提供则直接使用而不查询session_id
        """
        # 使用传入的user_id（如果有）或使用初始化时设置的user_id
        effective_user_id = user_id if user_id is not None else self.user_id
        logger.info(f"收到消息: {message[:50]}{'...' if len(message) > 50 else ''}, session_id={self.session_id}")

        # 保存quick_intent以便状态访问
        self.quick_intent = quick_intent

        # 确保中断处理器已初始化
        if not hasattr(self, 'interruption_handler'):
            from app.services.conversation.interruption_handler import InterruptionHandler
            self.interruption_handler = InterruptionHandler(self.llm_proxy)

        # 获取用户
        user = crud.crud_user.get(self.db, id=effective_user_id)
        if not user:
            logger.error(f"用户不存在: user_id={effective_user_id}")
            yield {"type": "message", "content": "用户不存在，请尝试重新登录", "role": "assistant"}
            return

        # 获取或创建会话
        if conversation_id:
            # 如果提供了conversation_id，直接获取会话
            conversation = crud.crud_conversation.get(self.db, id=conversation_id)
            if not conversation:
                logger.error(f"会话不存在: conversation_id={conversation_id}")
                yield {"type": "message", "content": "会话不存在，请尝试创建新会话", "role": "assistant"}
                return
        else:
            # 否则使用session_id获取或创建会话
            conversation = crud.crud_conversation.get_by_session_id(self.db, session_id=self.session_id)
            if not conversation:
                conversation_data = {
                    "user_id": effective_user_id,
                    "session_id": self.session_id,
                    "metadata": meta_info or {}
                }
                conversation = crud.crud_conversation.create(self.db, obj_in=conversation_data)
                logger.info(f"创建新会话: session_id={self.session_id}, conversation_id={conversation.id}")

        self.conversation_id = conversation.id

        # 加载历史消息
        raw_messages = crud.crud_message.get_conversation_messages(
            self.db, conversation_id=conversation.id, limit=20
        )

        # 获取最后一条AI消息，用于中断检测
        last_assistant_message = None
        messages = crud.crud_message.get_conversation_messages(
            self.db, conversation_id=conversation.id, limit=5
        )
        for msg in reversed(messages):
            if msg.role == "assistant":
                last_assistant_message = msg
                break

        # 检查元数据中是否已有消息ID，避免重复保存用户消息
        if meta_info and 'db_message_id' in meta_info:
            logger.debug(f"用户消息已在API端点保存，跳过重复保存: db_message_id={meta_info['db_message_id']}")
        else:
            # 保存用户消息
            user_message_data = {
                "conversation_id": conversation.id,
                "user_id": effective_user_id,
                "content": message,
                "role": "user",
                "meta_info": meta_info
            }
            user_message = crud.crud_message.create(self.db, obj_in=user_message_data)

            # 更新元数据以包含数据库ID
            if meta_info is None:
                meta_info = {}
            meta_info['db_message_id'] = user_message.id

        # 初始化元数据
        response_meta_info = meta_info.copy() if meta_info else {}

        # 检查是否需要进行中断检测
        if not response_meta_info.get("confirming_continuation") and last_assistant_message:
            # 检查是否处于特殊状态
            is_in_special_state = (
                response_meta_info.get("waiting_for_info") or
                response_meta_info.get("collecting_training_params") or
                response_meta_info.get("active_flow")
            )

            if is_in_special_state:
                # 进行中断检测
                is_interrupted = await self.interruption_handler.check_interruption(
                    message, last_assistant_message, response_meta_info
                )

                if is_interrupted:
                    # 处理中断
                    async for response in self.interruption_handler.handle_interruption(
                        message, response_meta_info
                    ):
                        yield response

                    # 更新会话元数据
                    crud.crud_conversation.update(self.db, db_obj=conversation, obj_in={"meta_info": response_meta_info})
                    self.db.commit()
                    return



        # 确定当前状态
        current_state = self.state_manager.get_state(response_meta_info)

        # 使用状态模式处理消息
        final_response_text = ""
        message_responses = []  # 收集所有消息类型的响应

        # 重置响应处理状态
        self.response_processed = False

        # 确保传递user_id和conversation_id参数
        effective_user_id = user_id if user_id is not None else self.user_id
        effective_conversation_id = conversation.id

        # 记录状态和参数，用于调试
        logger.info(f"使用状态 {current_state.__class__.__name__} 处理消息，user_id={effective_user_id}, conversation_id={effective_conversation_id}")

        async for response in current_state.handle_message(
            self,
            message,
            response_meta_info,
            user_id=effective_user_id,
            conversation_id=effective_conversation_id
        ):
            # 收集最终完整的响应文本(用于保存到数据库)
            if response.get("type") == "token":
                final_response_text += response.get("content", "")
                # 标记已处理响应
                self.response_processed = True
            elif response.get("type") == "message":
                message_content = response.get("content", "")
                message_responses.append(message_content)
                final_response_text = message_content  # 保留最后一个消息作为最终响应
                # 标记已处理响应
                self.response_processed = True

            # 将响应传递给调用者
            yield response

        # 如果有多个消息响应，使用最后一个作为最终响应
        if message_responses:
            final_response_text = message_responses[-1]
            logger.debug(f"收集到 {len(message_responses)} 条消息响应，使用最后一条作为最终响应")

        # 消息处理完成后的收尾工作
        async for finalize_response in self._finalize_conversation(conversation, response_meta_info, final_response_text):
            # 将收尾工作的响应传递给调用者
            yield finalize_response

    async def _finalize_conversation(self, conversation, meta_info, final_response_text=""):
        """完成会话处理，更新状态并保存响应

        Args:
            conversation: 会话对象
            meta_info: 元数据
            final_response_text: 最终响应文本
        """
        # 更新会话元数据 - 使用正确的字段名 meta_info
        crud.crud_conversation.update(self.db, db_obj=conversation, obj_in={"meta_info": meta_info})

        # 如果有最终响应文本，保存到数据库
        if final_response_text:
            # 创建AI响应消息
            ai_message_data = {
                "conversation_id": conversation.id,
                "user_id": conversation.user_id,  # 使用会话关联的用户ID
                "content": final_response_text,
                "role": "assistant",
                "meta_info": meta_info  # 使用正确的字段名 meta_info
            }
            ai_message = crud.crud_message.create(self.db, obj_in=ai_message_data)
            logger.debug(f"在_finalize_conversation中保存AI消息: id={ai_message.id}, content={final_response_text[:50]}...")

            # 添加一个标记，表示AI消息已在此处保存
            yield {"type": "ai_message_saved", "ai_message_saved": True}
        else:
            # 即使没有响应文本，也要确保元数据被保存
            logger.debug("没有生成响应文本，但仍然更新了会话元数据")

        # 提交数据库更改
        self.db.commit()

    async def _handle_intent_execution(self, intent_data, meta_info, user_id=None):
        """委托意图处理给IntentHandler

        Args:
            intent_data: 意图数据
            meta_info: 元数据
            user_id: 用户ID，可选
        """
        # 获取用户数据
        effective_user_id = user_id if user_id is not None else self.user_id

        # 确保用户ID不为None
        if effective_user_id is None:
            logger.error("用户ID为None，无法获取用户数据")
            # 使用空字典作为默认值，但添加一个标记表示这是默认数据
            user_data = {"is_default": True}
            # 尝试从会话ID获取用户ID
            if hasattr(self, 'conversation_id') and self.conversation_id:
                try:
                    conversation = crud.crud_conversation.get(self.db, id=self.conversation_id)
                    if conversation and conversation.user_id:
                        effective_user_id = conversation.user_id
                        logger.info(f"从会话ID获取到用户ID: {effective_user_id}")
                        # 重新尝试获取用户
                        user = crud.crud_user.get(self.db, id=effective_user_id)
                        if user:
                            # 更新self.user_id以便后续使用
                            self.user_id = effective_user_id
                        else:
                            logger.warning(f"尝试从会话获取用户ID后仍未找到用户: user_id={effective_user_id}")
                            # 使用yield而不是return
                            yield {"type": "meta", "data": {"user_data": user_data}}
                            return  # 提前结束生成器
                except Exception as e:
                    logger.error(f"尝试从会话获取用户ID时出错: {str(e)}")
                    # 使用yield而不是return
                    yield {"type": "meta", "data": {"user_data": user_data}}
                    return  # 提前结束生成器
            else:
                logger.warning("无法从会话ID获取用户ID，conversation_id不存在")
                # 使用yield而不是return
                yield {"type": "meta", "data": {"user_data": user_data}}
                return  # 提前结束生成器

        # 获取用户对象
        user = crud.crud_user.get(self.db, id=effective_user_id)

        # 确保用户数据包含所有必要的字段
        if user:
            # 转换为字典并确保包含所有必要字段
            user_data = {
                "id": user.id,
                "nickname": user.nickname,
                "gender": user.gender,
                "age": user.age,
                "height": user.height,
                "weight": user.weight,
                "fitness_goal": user.fitness_goal,
                "experience_level": user.experience_level
            }
            logger.info(f"获取到用户数据: id={user_data['id']}, 性别={user_data['gender']}, 年龄={user_data['age']}")
        else:
            user_data = {"is_default": True}
            logger.warning(f"未找到用户数据，user_id={effective_user_id}")

        # 获取历史消息
        raw_messages = crud.crud_message.get_conversation_messages(
            self.db, conversation_id=self.conversation_id, limit=10
        )

        # 处理历史消息格式
        from app.services.conversation.utils import ConversationUtils
        history = ConversationUtils.process_history(raw_messages)

        # 确保intent_handler已初始化
        if not hasattr(self, 'intent_handler') or self.intent_handler is None:
            logger.warning("intent_handler未初始化，创建新实例")
            from app.services.conversation.intent_handler import IntentHandler
            self.intent_handler = IntentHandler(
                db=self.db,
                llm_proxy=self,  # 传递self，让IntentHandler从中提取llm_proxy属性
                agent_executor=self.agent_executor,
                sql_tool=self.sql_tool,
                conversation_model=self.conversation_model
            )

        # 初始化模型服务（如果尚未初始化）
        if not hasattr(self, 'model_service'):
            from app.services.model_service import ModelService
            self.model_service = ModelService(self.llm_proxy)

        # 根据意图选择合适的模型
        model_type = self.model_service.get_model_for_intent(intent_data.intent)
        logger.info(f"为意图 {intent_data.intent} 选择模型类型: {model_type}")

        # 更新元数据中的模型信息
        response_meta_info = meta_info.copy()
        response_meta_info["model_type"] = model_type

        # 委托给意图处理器
        logger.info(f"委托意图处理: intent={intent_data.intent}, 参数={intent_data.parameters}")
        async for response in self.intent_handler.handle_intent(
            intent_data, response_meta_info, user_data, history
        ):
            yield response