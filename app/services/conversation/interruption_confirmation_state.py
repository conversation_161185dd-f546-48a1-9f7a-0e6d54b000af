"""
中断确认状态 - 处理对话中断后的确认流程
"""
from typing import Dict, Any, AsyncGenerator
import logging
from app.services.conversation.states import ConversationState
from app.services.conversation.interruption_handler import InterruptionHandler


logger = logging.getLogger(__name__)

class InterruptionConfirmationState(ConversationState):
    """中断确认状态：处理用户对中断确认的回复"""

    async def handle_message(self, service, message: str, meta_info: Dict[str, Any], user_id: int = None, conversation_id: int = None) -> AsyncGenerator[Dict[str, Any], None]:
        """处理中断确认状态的消息

        Args:
            service: 对话服务实例
            message: 用户消息
            meta_info: 元数据
            user_id: 用户ID
            conversation_id: 会话ID
        """
        # 深度复制元数据，避免修改原始数据
        response_meta_info = meta_info.copy()

        # 确保中断处理器已初始化
        if not hasattr(service, 'interruption_handler'):
            service.interruption_handler = InterruptionHandler(service.llm_proxy)

        # 分析用户回复，判断是继续原流程还是处理新问题
        continue_previous = await service.interruption_handler.analyze_continuation_response(message)

        if continue_previous:
            # 用户选择继续原流程
            logger.info("用户选择继续原流程")

            # 清除确认状态
            response_meta_info.pop("confirming_continuation", None)

            # 确认继续
            yield {"type": "message", "content": "好的，我们继续之前的对话。", "role": "assistant"}

            # 如果有待处理的新消息，清除它
            if "pending_new_message" in response_meta_info:
                response_meta_info.pop("pending_new_message", None)

            # 发送元数据更新事件
            yield {"type": "meta_info_update", "meta_info_update": response_meta_info}

            # 如果是在用户信息收集状态中断的
            if "waiting_for_info" in response_meta_info and response_meta_info["waiting_for_info"]:
                field = response_meta_info["waiting_for_info"].get("field")
                if field:
                    # 重新询问之前的问题
                    question = service.user_profile_manager.get_field_question(field)
                    yield {"type": "message", "content": question, "role": "assistant"}

            # 如果是在训练参数收集状态中断的
            elif "collecting_training_params" in response_meta_info and response_meta_info["collecting_training_params"]:
                asking_param = response_meta_info.get("asking_param")
                if asking_param:
                    # 重新询问之前的问题
                    question = service.training_param_manager.get_param_question(asking_param)
                    yield {"type": "message", "content": question, "role": "assistant"}
        else:
            # 用户选择处理新问题
            logger.info("用户选择处理新问题")

            # 清除确认状态
            response_meta_info.pop("confirming_continuation", None)

            # 清除特殊状态
            response_meta_info.pop("waiting_for_info", None)
            response_meta_info.pop("collecting_training_params", None)
            response_meta_info.pop("asking_param", None)

            # 确认处理新问题
            yield {"type": "message", "content": "好的，让我们处理您的新问题。", "role": "assistant"}

            # 获取待处理的新消息
            new_message = response_meta_info.pop("pending_new_message", message)

            # 发送元数据更新事件
            yield {"type": "meta_info_update", "meta_info_update": response_meta_info}

            # 处理新消息 - 使用正常对话状态处理
            from app.services.conversation.states import NormalConversationState
            normal_state = NormalConversationState()
            async for response in normal_state.handle_message(service, new_message, response_meta_info, user_id, conversation_id):
                yield response
