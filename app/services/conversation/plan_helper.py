from __future__ import annotations
import logging
from typing import TYPE_CHECKING, Dict, Any, List, Optional, Tuple, Union
import json
import random
from datetime import datetime

if TYPE_CHECKING:
    from .orchestrator import ConversationService

logger = logging.getLogger(__name__)

def _create_workout_plan(
    service: 'ConversationService',
    selected_exercises: List[Dict[str, Any]],
    training_params: Dict[str, Any]
) -> Dict[str, Any]:
    """构建训练计划
    
    Args:
        service: ConversationService实例
        selected_exercises: 训练动作列表
        training_params: 训练参数字典
        
    Returns:
        训练计划字典
    """
    # 收集所有身体部位
    all_body_parts = set()
    for exercise in selected_exercises:
        if exercise.get("body_parts"):
            all_body_parts.update(exercise["body_parts"])
    
    # 生成计划名称
    body_parts_str = "、".join(all_body_parts) if all_body_parts else "综合"
    workout_name = f"{body_parts_str}训练"
    
    # 计算预估时长（每组训练+休息时间）
    total_seconds = 0
    for exercise in selected_exercises:
        sets = exercise.get("sets", 3)
        rest_seconds = exercise.get("rest_seconds", 60)
        # 估计每组训练时间（根据次数范围不同而不同）
        reps_str = str(exercise.get("reps", "10-12"))
        if "-" in reps_str:
            # 范围性重复次数，如 "8-12"
            avg_seconds_per_set = 60  # 平均60秒一组
        else:
            # 固定重复次数
            avg_seconds_per_set = 40  # 平均40秒一组
        
        # 总时间 = 组数 * (每组时间 + 休息时间)
        total_seconds += sets * (avg_seconds_per_set + rest_seconds)
    
    # 转换为分钟并取整
    total_minutes = (total_seconds + 59) // 60  # 向上取整到分钟
    
    # 构建训练计划
    workout = {
        "name": workout_name,
        "description": f"专注于{body_parts_str}的训练计划，适合{service.active_query_manager.field_enum_map.get('experience_level', {}).get(training_params.get('experience_level'), '所有')}水平的训练者",
        "estimated_duration": total_minutes,
        "exercises": selected_exercises
    }
    
    return workout

def _format_workout_plan(service: 'ConversationService', workout: Dict[str, Any]) -> str:
    """格式化训练计划输出
    
    Args:
        service: ConversationService实例
        workout: 训练计划字典
        
    Returns:
        格式化后的JSON字符串
    """
    return json.dumps({
        "type": "workout_plan",
        "message": f"为你生成了{workout['name']}训练计划：",
        "data": workout
    }, ensure_ascii=False)

def format_daily_workout(workout_data: Dict[str, Any]) -> str:
    """格式化每日训练计划为可读文本
    
    Args:
        workout_data: 训练计划数据
        
    Returns:
        格式化的文本
    """
    try:
        text_parts = []
        
        # 添加标题
        workout_name = workout_data.get("workout_name", "每日训练计划")
        text_parts.append(f"# {workout_name}")
        text_parts.append("")
        
        # 添加基本信息
        duration = workout_data.get("duration", 60)
        text_parts.append(f"训练时长: {duration}分钟")
        
        # 添加目标部位
        if "target_body_parts" in workout_data and workout_data["target_body_parts"]:
            target_parts = "、".join(workout_data["target_body_parts"])
            text_parts.append(f"目标部位: {target_parts}")
            
        # 添加训练环境
        if "scenario" in workout_data:
            text_parts.append(f"训练环境: {workout_data['scenario']}")
            
        text_parts.append("")
        
        # 添加训练动作
        text_parts.append("## 训练动作")
        text_parts.append("")
        
        exercises = workout_data.get("exercises", [])
        for i, exercise in enumerate(exercises, 1):
            name = exercise.get("name", "未知动作")
            sets = exercise.get("sets", 3)
            reps = exercise.get("reps", "8-12")
            rest = exercise.get("rest_seconds", 60)
            
            text_parts.append(f"{i}. **{name}** - {sets}组 × {reps}次 (休息{rest}秒)")
            
            # 添加动作说明
            if "notes" in exercise and exercise["notes"]:
                text_parts.append(f"   - 说明: {exercise['notes']}")
                
            # 添加动作要点
            if "tips" in exercise and exercise["tips"]:
                if isinstance(exercise["tips"], list):
                    for tip in exercise["tips"]:
                        text_parts.append(f"   - {tip}")
                else:
                    text_parts.append(f"   - {exercise['tips']}")
                    
            text_parts.append("")
            
        # 添加训练提示
        if "training_tips" in workout_data and workout_data["training_tips"]:
            text_parts.append("## 训练提示")
            text_parts.append("")
            
            for tip in workout_data["training_tips"]:
                text_parts.append(f"- {tip}")
                
            text_parts.append("")
            
        return "\n".join(text_parts)
    except Exception as e:
        logger.error(f"格式化每日训练计划时出错: {str(e)}")
        return f"训练计划: {workout_data.get('workout_name', '每日训练计划')}"

def format_weekly_plan(plan_data: Dict[str, Any]) -> str:
    """格式化周训练计划为可读文本
    
    Args:
        plan_data: 训练计划数据
        
    Returns:
        格式化的文本
    """
    try:
        text_parts = []
        
        # 添加标题
        plan_name = plan_data.get("plan_name", "每周训练计划")
        text_parts.append(f"# {plan_name}")
        text_parts.append("")
        
        # 添加基本信息
        if "goal" in plan_data:
            text_parts.append(f"训练目标: {plan_data['goal']}")
            
        if "training_days" in plan_data:
            text_parts.append(f"每周训练天数: {plan_data['training_days']}天")
            
        if "duration_weeks" in plan_data:
            duration = plan_data.get("duration_weeks", 1)
            text_parts.append(f"计划周期: {duration}周")
            
        if "split_type" in plan_data:
            text_parts.append(f"分化方式: {plan_data['split_type']}")
            
        text_parts.append("")
        
        # 添加训练安排
        text_parts.append("## 每周安排")
        text_parts.append("")
        
        days = plan_data.get("days", [])
        for day_data in days:
            day_number = day_data.get("day_number", "未知")
            
            if day_data.get("is_rest_day", False):
                # 休息日
                day_name = day_data.get("day_name", f"第{day_number}天 - 休息日")
                text_parts.append(f"### {day_name}")
                text_parts.append("")
                
                # 添加恢复建议
                if "recovery_tips" in day_data and day_data["recovery_tips"]:
                    text_parts.append("恢复建议:")
                    for tip in day_data["recovery_tips"]:
                        text_parts.append(f"- {tip}")
                    text_parts.append("")
            else:
                # 训练日
                workout_name = day_data.get("workout_name", f"第{day_number}天训练")
                text_parts.append(f"### 第{day_number}天: {workout_name}")
                text_parts.append("")
                
                # 添加目标部位
                if "target_body_parts" in day_data and day_data["target_body_parts"]:
                    target_parts = "、".join(day_data["target_body_parts"])
                    text_parts.append(f"目标部位: {target_parts}")
                    
                # 添加训练时长
                duration = day_data.get("duration", 60)
                text_parts.append(f"训练时长: {duration}分钟")
                text_parts.append("")
                
                # 添加训练动作
                text_parts.append("训练动作:")
                exercises = day_data.get("exercises", [])
                for i, exercise in enumerate(exercises, 1):
                    name = exercise.get("name", "未知动作")
                    sets = exercise.get("sets", 3)
                    reps = exercise.get("reps", "8-12")
                    rest = exercise.get("rest_seconds", 60)
                    
                    text_parts.append(f"{i}. **{name}** - {sets}组 × {reps}次 (休息{rest}秒)")
                    
                    # 添加动作说明
                    if "notes" in exercise and exercise["notes"]:
                        text_parts.append(f"   - 说明: {exercise['notes']}")
                        
                text_parts.append("")
                
                # 添加训练提示
                if "training_tips" in day_data and day_data["training_tips"]:
                    text_parts.append("训练提示:")
                    for tip in day_data["training_tips"]:
                        text_parts.append(f"- {tip}")
                    text_parts.append("")
                    
        # 添加总体建议
        if "general_tips" in plan_data and plan_data["general_tips"]:
            text_parts.append("## 总体建议")
            text_parts.append("")
            
            for tip in plan_data["general_tips"]:
                text_parts.append(f"- {tip}")
                
            text_parts.append("")
            
        return "\n".join(text_parts)
    except Exception as e:
        logger.error(f"格式化周训练计划时出错: {str(e)}")
        return f"训练计划: {plan_data.get('plan_name', '每周训练计划')}"

def validate_training_plan(plan_data: Dict[str, Any], is_daily: bool = False) -> bool:
    """验证训练计划数据的完整性
    
    Args:
        plan_data: 训练计划数据
        is_daily: 是否为每日计划
        
    Returns:
        是否验证通过
    """
    try:
        if not plan_data:
            return False
            
        if is_daily:
            # 验证每日计划
            required_fields = ["workout_name", "duration", "exercises"]
            for field in required_fields:
                if field not in plan_data:
                    logger.warning(f"每日训练计划缺少字段: {field}")
                    return False
                    
            # 验证训练动作
            if not plan_data.get("exercises") or not isinstance(plan_data["exercises"], list):
                logger.warning("每日训练计划缺少有效的训练动作列表")
                return False
                
            if len(plan_data["exercises"]) == 0:
                logger.warning("每日训练计划没有包含任何训练动作")
                return False
        else:
            # 验证周训练计划
            required_fields = ["plan_name", "duration_weeks", "days"]
            for field in required_fields:
                if field not in plan_data:
                    logger.warning(f"周训练计划缺少字段: {field}")
                    return False
            
            # 验证训练日
            if not plan_data.get("days") or not isinstance(plan_data["days"], list):
                logger.warning("周训练计划缺少有效的训练日列表")
                return False
                
            if len(plan_data["days"]) == 0:
                logger.warning("周训练计划没有包含任何训练日")
                return False
                
            # 验证每个训练日
            for day in plan_data["days"]:
                if "is_rest_day" in day and day["is_rest_day"]:
                    # 休息日无需进一步验证
                    continue
                    
                # 验证训练日数据
                if "exercises" not in day or not isinstance(day["exercises"], list) or len(day["exercises"]) == 0:
                    logger.warning(f"训练日 {day.get('day_number', '未知')} 缺少有效的训练动作")
                    return False
                    
        return True
    except Exception as e:
        logger.error(f"验证训练计划时出错: {str(e)}")
        return False

def enrich_training_plan(plan_data: Dict[str, Any], training_params: Dict[str, Any], is_daily: bool = False) -> Dict[str, Any]:
    """丰富训练计划数据
    
    Args:
        plan_data: 原始训练计划数据
        training_params: 训练参数
        is_daily: 是否为每日计划
        
    Returns:
        丰富后的训练计划数据
    """
    try:
        enriched_plan = plan_data.copy()
        
        # 添加通用数据
        enriched_plan["success"] = True
        
        if is_daily:
            # 丰富每日计划
            enriched_plan["type"] = "daily_workout"
            
            # 补充训练提示
            if "training_tips" not in enriched_plan:
                enriched_plan["training_tips"] = [
                    "确保每个动作的完整活动范围",
                    "专注于肌肉收缩感觉",
                    "保持良好姿势，避免受伤"
                ]
                
            # 补充或规范化训练目标
            if "target" not in enriched_plan and "target_body_parts" in enriched_plan:
                body_parts = enriched_plan["target_body_parts"]
                target = "全身训练" if not body_parts else f"{'、'.join(body_parts)}训练"
                enriched_plan["target"] = target
                
            # 确保每个动作都有完整信息
            if "exercises" in enriched_plan:
                for i, exercise in enumerate(enriched_plan["exercises"]):
                    # 确保有组数和次数
                    if "sets" not in exercise:
                        exercise["sets"] = 3
                    if "reps" not in exercise:
                        exercise["reps"] = "8-12"
                    if "rest_seconds" not in exercise:
                        exercise["rest_seconds"] = 60
                        
                    # 确保有ID
                    if "id" not in exercise:
                        exercise["id"] = f"exercise_{i+1}"
                        
                    # 添加序号
                    exercise["order"] = i + 1
        else:
            # 丰富周训练计划
            enriched_plan["type"] = "weekly_training_plan"
            
            # 补充训练目标
            if "goal" not in enriched_plan and "fitness_goal" in training_params:
                goal_map = {1: "增肌", 2: "减脂", 3: "塑形"}
                enriched_plan["goal"] = goal_map.get(training_params.get("fitness_goal"), "综合健身")
                
            # 添加计划难度
            if "level" not in enriched_plan and "experience_level" in training_params:
                enriched_plan["level"] = training_params.get("experience_level", 1)
                
            # 丰富每个训练日
            if "days" in enriched_plan:
                for day_data in enriched_plan["days"]:
                    # 跳过休息日
                    if day_data.get("is_rest_day", False):
                        continue
                        
                    # 确保训练日有名称
                    if "workout_name" not in day_data and "target_body_parts" in day_data:
                        body_parts = day_data["target_body_parts"]
                        day_data["workout_name"] = f"{'、'.join(body_parts)}训练日"
                        
                    # 确保有训练时长
                    if "duration" not in day_data:
                        day_data["duration"] = 60
                        
                    # 确保每个动作都有完整信息
                    if "exercises" in day_data:
                        for i, exercise in enumerate(day_data["exercises"]):
                            # 确保有组数和次数
                            if "sets" not in exercise:
                                exercise["sets"] = 3
                            if "reps" not in exercise:
                                exercise["reps"] = "8-12"
                            if "rest_seconds" not in exercise:
                                exercise["rest_seconds"] = 60
                                
                            # 确保有ID
                            if "id" not in exercise:
                                exercise["id"] = f"day{day_data.get('day_number', 0)}_exercise_{i+1}"
                                
                            # 添加序号
                            exercise["order"] = i + 1
        
        return enriched_plan
    except Exception as e:
        logger.error(f"丰富训练计划数据时出错: {str(e)}")
        return plan_data  # 返回原始数据 