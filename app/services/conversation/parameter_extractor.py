# app/services/conversation/parameter_extractor.py
from __future__ import annotations
import logging
import json
from typing import TYPE_CHECKING, Dict, Any, List, Optional, Tuple, Set
import re
import random
from app.core.param_definitions import (
    BODY_PART_CATEGORIES, MUSCLE_CATEGORIES, EQUIPMENT_CATEGORIES,
    BODY_PART_SYNONYMS, TRAINING_SCENARIOS, TRAINING_GOALS, DEFAULT_TRAINING_PARAMS,
    normalize_input, get_required_params_for_intent
)

if TYPE_CHECKING:
    from .orchestrator import ConversationService
    from app.services.intent_recognizer import IntentData
    from app.models.user import User

logger = logging.getLogger(__name__)

# --- 添加extract_json_from_text函数，避免循环导入 ---
def extract_json_from_text(text: str) -> Any:
    """从文本中提取JSON数据"""
    try:
        return json.loads(text)
    except json.JSONDecodeError:
        json_patterns = [r'(\[[\s\S]*?\])', r'({[\s\S]*?})']
        for pattern in json_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                try: return json.loads(match)
                except: continue
        logger.warning(f"无法从文本中提取JSON: {text[:200]}...")
        return None

# --- Parameter Extractor Class ---

class ParameterExtractor:
    """训练参数提取器，整合各类参数提取功能"""

    @staticmethod
    async def extract_all_parameters(service: 'ConversationService', message: str) -> Dict[str, Any]:
        """综合提取所有训练相关参数

        Args:
            service: ConversationService实例
            message: 用户消息

        Returns:
            包含所有提取参数的字典
        """
        basic_params = await ParameterExtractor.extract_training_parameters(service, message)
        extended_params = {
            "body_parts": ParameterExtractor.extract_body_parts(message),
            "training_goal": ParameterExtractor.extract_training_goal(message),
            "exercise_count": ParameterExtractor.extract_exercise_count(message)
        }
        result = {**basic_params, **extended_params}
        return result

    @staticmethod
    async def extract_training_parameters(service: 'ConversationService', message: str, intent: str = None) -> Dict[str, Any]:
        """提取基础训练参数(身体部位/场景/计划类型)

        Args:
            service: ConversationService实例
            message: 用户消息
            intent: 意图名称，用于确定是否需要提取某些参数

        Returns:
            提取的参数字典
        """
        result = {}
        try:
            # 确保intent不为None，避免后续处理出错
            if intent is None and hasattr(service, 'meta_info') and service.meta_info:
                # 从meta_info中获取intent
                intent = service.meta_info.get("intent")
                logger.info(f"从meta_info中获取意图: {intent}")

            # 根据意图确定需要提取的参数
            required_params = get_required_params_for_intent(intent)
            logger.info(f"意图 {intent} 需要提取的参数: {required_params}")

            # 检查meta_info中是否已有参数，优先使用已有参数
            if hasattr(service, 'meta_info') and service.meta_info and 'intent_parameters' in service.meta_info:
                intent_params = service.meta_info.get('intent_parameters', {})
                if intent_params:
                    logger.info(f"从meta_info中获取参数: {intent_params}")
                    # 合并已有参数
                    for param, value in intent_params.items():
                        if value:  # 只添加非空值
                            result[param] = value

            # 如果已有参数不足，再进行提取
            if not all(param in result for param in required_params if param in ['body_part', 'scenario']):
                # 提取参数
                parameters = await ParameterExtractor._extract_parameters_with_llm(service, message, intent)

                # 过滤参数，只保留需要的参数
                if parameters:
                    filtered_params = {}
                    for param, value in parameters.items():
                        if param in required_params or (isinstance(value, list) and len(value) > 0) or (not isinstance(value, list) and value is not None):
                            filtered_params[param] = value

                    result.update(filtered_params)
                    logger.info(f"过滤后的参数: {filtered_params}")
        except Exception as e:
            logger.error(f"LLM参数提取失败，使用关键词匹配: {str(e)}")
            result = await ParameterExtractor._extract_parameters_with_keywords(service, message, intent)
        return result

    @staticmethod
    async def _extract_parameters_with_llm(service: 'ConversationService', message: str, intent: str = None) -> Dict[str, Any]:
        """使用增强识别器提取训练参数

        Args:
            service: ConversationService实例
            message: 用户消息
            intent: 意图名称，用于确定是否需要提取某些参数

        Returns:
            提取的参数字典
        """
        try:
            # 使用增强识别器进行参数提取
            from app.services.intent_recognition.enhanced_recognizer import EnhancedIntentRecognizer

            # 检查service是否已有enhanced_recognizer实例
            if not hasattr(service, 'enhanced_recognizer') or service.enhanced_recognizer is None:
                logger.info("初始化增强识别器实例")
                service.enhanced_recognizer = EnhancedIntentRecognizer(service.llm_proxy)

            # 构建上下文信息
            context = {}
            if intent == "recommend_exercise":
                # 对于recommend_exercise意图，不需要提取plan_type参数
                logger.info(f"意图为recommend_exercise，不提取plan_type参数")
                context["exclude_params"] = ["plan_type"]

            # 如果正在收集训练参数，添加到上下文
            if hasattr(service, 'meta_info') and service.meta_info:
                if service.meta_info.get("collecting_training_params", False):
                    context["collecting_training_params"] = True
                    if "asking_param" in service.meta_info:
                        context["asking_param"] = service.meta_info["asking_param"]

            logger.info(f"使用增强识别器提取参数，上下文: {context}")

            # 检查meta_info中是否已有高置信度的意图和参数
            if hasattr(service, 'meta_info') and service.meta_info:
                meta_intent = service.meta_info.get("intent")
                meta_confidence = service.meta_info.get("intent_confidence", 0)
                meta_params = service.meta_info.get("intent_parameters", {})

                # 如果meta_info中已有高置信度的意图和参数，直接使用
                if meta_intent and meta_intent == intent and meta_confidence >= 0.9 and meta_params:
                    logger.info(f"使用meta_info中的高置信度参数: {meta_params}")

                    # 确保所有字段都存在，即使为空
                    result = {
                        "body_part": meta_params.get("body_part", []),
                        "muscle": meta_params.get("muscle", []),
                        "scenario": meta_params.get("scenario"),
                        "training_goal": meta_params.get("training_goal"),
                        "plan_type": meta_params.get("plan_type"),
                        "difficulty": meta_params.get("difficulty"),
                        "equipment": meta_params.get("equipment", []),
                    }

                    # 若是recommend_exercise意图，确保移除plan_type
                    if intent == "recommend_exercise" and "plan_type" in result:
                        result.pop("plan_type")

                    return result

            # 使用增强识别器提取参数
            intent_data = await service.enhanced_recognizer.extract_parameters(message, context)
            if intent_data:
                logger.info(f"增强识别器提取结果: {intent_data.parameters}")

                # 若是recommend_exercise意图，确保移除plan_type
                if intent == "recommend_exercise" and "plan_type" in intent_data.parameters:
                    intent_data.parameters.pop("plan_type")
                
                return intent_data.parameters
            return {}
        except Exception as e:
            logger.error(f"增强识别器参数提取失败: {str(e)}")
            return {}

    @staticmethod
    async def _extract_parameters_with_keywords(service: 'ConversationService', message: str, intent: str = None) -> Dict[str, Any]:
        """基于关键词匹配提取训练参数

        Args:
            service: ConversationService实例
            message: 用户消息
            intent: 意图名称，用于确定需要提取的参数

        Returns:
            提取的参数字典
        """
        result = {}

        # 1. 提取训练部位
        body_part = await ParameterExtractor._extract_body_part(service, message)
        if body_part:
            result["body_part"] = [body_part]  # 使用列表格式统一返回

        # 2. 提取训练场景
            scenario = ParameterExtractor._extract_scenario(message)
            if scenario:
                result["scenario"] = scenario
        
        # 3. 提取计划类型 (如果意图不是recommend_exercise)
        if intent != "recommend_exercise":
            plan_type = ParameterExtractor.extract_plan_type(message)
            if plan_type:
                result["plan_type"] = plan_type
        
        # 4. 提取训练目标
        training_goal = ParameterExtractor.extract_training_goal(message)
        if training_goal:
            result["training_goal"] = training_goal

        logger.info(f"关键词参数提取结果: {result}")
        return result

    @staticmethod
    async def _extract_body_part(service: 'ConversationService', message: str, intent_data: Optional['IntentData'] = None) -> Optional[str]:
        """提取训练部位

        Args:
            service: ConversationService实例
            message: 用户消息
            intent_data: 意图数据，如果有则优先使用

        Returns:
            提取的训练部位，未找到则返回None
        """
        msg_lower = message.lower()
        
        # 1. 从intent_data中提取
        if intent_data and intent_data.parameters and "body_part" in intent_data.parameters:
            body_parts = intent_data.parameters["body_part"]
            if isinstance(body_parts, list) and len(body_parts) > 0:
                return body_parts[0]
            if isinstance(body_parts, str) and body_parts:
                return body_parts
        
        # 2. 从用户消息中匹配身体部位
        body_parts_found = ParameterExtractor.extract_body_parts(msg_lower)
        if body_parts_found:
            return body_parts_found[0]  # 返回第一个匹配的身体部位
        
        # 3. 未找到匹配的身体部位
        return None

    @staticmethod
    def _extract_scenario(message: str) -> Optional[str]:
        """提取训练场景(家庭/健身房)

        Args:
            message: 用户消息

        Returns:
            "home"(居家) 或 "gym"(健身房) 或 None
        """
        msg_lower = message.lower()

        # 扩展关键词列表，提高匹配准确性
        home_keywords = [
            "家", "居家", "自重", "徒手", "home", "宿舍", "家里", "简易",
            "弹力带", "阻力带", "小区", "客厅", "卧室", "不去健身房",
            "没有器械", "窝", "家庭", "household", "家用"
        ]

        gym_keywords = [
            "健身房", "健身器械", "哑铃", "杠铃", "健身器材", "固定器械", "gym",
            "健身室", "健身中心", "工作室", "俱乐部", "fitness center",
            "器械齐全", "商业健身房", "工卡", "组合器械", "专业健身"
        ]

        # 使用词边界匹配，避免误匹配（如"训练棍"被误识别为"训练"+"棍"）
        # 添加空格用于词边界检查
        padded_msg = f" {msg_lower} "
        
        # 检查家庭训练关键词
        for kw in home_keywords:
            # 使用词边界检查，确保是完整词语匹配
            pattern = f" {kw} |^{kw} | {kw}$|^{kw}$"
            if re.search(pattern, padded_msg):
                return "home"

        # 检查健身房训练关键词
        for kw in gym_keywords:
            # 使用词边界检查，确保是完整词语匹配
            pattern = f" {kw} |^{kw} | {kw}$|^{kw}$"
            if re.search(pattern, padded_msg):
                return "gym"
        
        # 未找到匹配的训练场景
        return None

    @staticmethod
    def extract_plan_type(message: str) -> Optional[str]:
        """提取计划类型(单日/周期)

        Args:
            message: 用户消息

        Returns:
            "daily"(单日) 或 "weekly"(周期) 或 None
            """
        msg_lower = message.lower()
        
        # 单日计划关键词
        daily_keywords = ["单日", "一天", "单次", "daily", "一次训练", "一个训练"]
        
        # 周期计划关键词
        weekly_keywords = ["周期", "weekly", "几天", "多天", "一周", "一月", "长期", "连续", "计划表"]
        
        # 检查单日计划关键词
        for kw in daily_keywords:
            if kw in msg_lower:
                return "daily"
        
        # 检查周期计划关键词
        for kw in weekly_keywords:
            if kw in msg_lower:
                return "weekly"
        
        # 未找到匹配的计划类型
        return None

    @staticmethod
    def extract_body_parts(text: str) -> List[str]:
        """提取所有匹配的身体部位

        Args:
            text: 用户消息

            Returns:
            提取的身体部位列表
        """
        matched_parts = []
        
        # 使用词边界匹配，避免部分匹配问题
        padded_text = f" {text} "
        
        # 遍历身体部位及其同义词
        for part, synonyms in BODY_PART_SYNONYMS.items():
            # 检查主要名称
            pattern = f" {part} |^{part} | {part}$|^{part}$"
            if re.search(pattern, padded_text):
                if part not in matched_parts:
                    matched_parts.append(part)
                continue
            
            # 检查同义词
            for synonym in synonyms:
                pattern = f" {synonym} |^{synonym} | {synonym}$|^{synonym}$"
                if re.search(pattern, padded_text):
                    if part not in matched_parts:
                        matched_parts.append(part)
                    break
        
        return matched_parts

    @staticmethod
    def extract_training_scenario(text: str) -> Optional[str]:
        """提取训练场景

        Args:
            text: 用户消息

        Returns:
            训练场景: 'home'或'gym'或None
        """
        return ParameterExtractor._extract_scenario(text)

    @staticmethod
    def extract_training_goal(text: str) -> Optional[str]:
        """提取训练目标

        Args:
            text: 用户消息

        Returns:
            提取的训练目标，未找到则返回None
        """
        # 遍历训练目标及其关键词
        for goal, keywords in TRAINING_GOALS.items():
            # 检查主要名称
            if goal in text:
                return goal
            
            # 检查关键词
            for kw in keywords:
                if kw in text:
                    return goal
        
        # 未找到匹配的训练目标
        return None

    @staticmethod
    def extract_exercise_count(text: str) -> int:
        """提取动作数量

        Args:
            text: 用户消息

        Returns:
            提取的动作数量，默认为5
        """
        default_count = 5
        max_count = 15  # 设置最大动作数量
        
        # 匹配模式：数字+[个/种/项/动作/组/exercises]
        patterns = [
            r'(\d+)\s*个动作',
            r'(\d+)\s*种动作',
            r'(\d+)\s*个训练',
            r'(\d+)\s*项训练',
            r'(\d+)\s*组动作',
            r'(\d+)\s*exercises',
            r'(\d+)\s*个'
        ]
        
        for pattern in patterns:
            matches = re.search(pattern, text)
            if matches:
                count = int(matches.group(1))
                return min(count, max_count)  # 限制最大数量
        
        # 未找到匹配的数量，返回默认值
        return default_count

    @staticmethod
    def extract_experience_level(text: str, user_profile: Optional[Dict[str, Any]] = None) -> int:
        """提取用户经验级别

        Args:
            text: 用户消息
            user_profile: 用户资料，包含训练统计等信息

        Returns:
            经验级别（1-5）
        """
        # 如果用户有健身经验记录，根据经验调整级别
        if user_profile and "workout_count" in user_profile:
            workout_count = user_profile.get("workout_count", 0)
            if workout_count > 100:
                return 4  # 较高级别
            elif workout_count > 50:
                return 3  # 中级
            elif workout_count > 20:
                return 2  # 初中级
            else:
                return 1  # 初级
        
        # 根据用户消息中的关键词判断
        beginner_keywords = ["新手", "初学者", "刚开始", "没经验", "入门", "beginner"]
        intermediate_keywords = ["有一定经验", "练过一段时间", "中等水平", "intermediate"]
        advanced_keywords = ["高级", "有经验", "健身达人", "老手", "expert", "advanced"]
        
        for kw in advanced_keywords:
            if kw in text:
                return 4  # 高级
        
        for kw in intermediate_keywords:
            if kw in text:
                return 3  # 中级
        
        for kw in beginner_keywords:
            if kw in text:
                return 1  # 初级
        
        # 默认为初中级
        return 2

    @staticmethod
    def extract_difficulty_level(text: str, user_profile: Optional[Dict[str, Any]] = None) -> int:
        """提取训练难度级别

        Args:
            text: 用户消息
            user_profile: 用户资料，包含训练统计等信息

        Returns:
            难度级别（1-5）
        """
        # 难度关键词映射
        difficulty_map = {
            1: ["初级", "简单", "入门", "beginner", "easy", "新手", "轻松"],
            2: ["初中级", "低强度", "适中"],
            3: ["中级", "medium", "普通", "标准", "intermediate"],
            4: ["中高级", "高强度"],
            5: ["高级", "困难", "挑战", "hard", "advanced", "expert", "极限"]
        }
        
        # 从文本中查找难度关键词
        for level, keywords in difficulty_map.items():
            for kw in keywords:
                if kw in text:
                    return level

        # 如果找不到难度关键词，根据用户经验设置默认难度
        experience = ParameterExtractor.extract_experience_level(text, user_profile)
        
        # 默认难度略高于用户经验级别，鼓励进步
        return min(experience + 1, 5)

    @staticmethod
    def extract_training_params(user_input: str, user_profile: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """提取训练所需的所有参数

        Args:
            user_input: 用户输入
            user_profile: 用户资料，包含训练统计等信息

        Returns:
            包含所有训练参数的字典
        """
        params = {}
        
        # 提取训练部位
        body_parts = ParameterExtractor.extract_body_parts(user_input)
        if body_parts:
            params["body_part"] = body_parts
        
        # 提取训练场景
        scenario = ParameterExtractor._extract_scenario(user_input)
        if scenario:
            params["scenario"] = scenario
        
        # 提取计划类型
        plan_type = ParameterExtractor.extract_plan_type(user_input)
        if plan_type:
            params["plan_type"] = plan_type
        
        # 提取训练目标
        training_goal = ParameterExtractor.extract_training_goal(user_input)
        if training_goal:
            params["training_goal"] = training_goal
        
        # 提取难度级别
        difficulty = ParameterExtractor.extract_difficulty_level(user_input, user_profile)
        params["difficulty"] = difficulty
        
        # 提取动作数量
        exercise_count = ParameterExtractor.extract_exercise_count(user_input)
        params["exercise_count"] = exercise_count
        
        # 提取经验级别
        experience_level = ParameterExtractor.extract_experience_level(user_input, user_profile)
        params["experience_level"] = experience_level

        return params