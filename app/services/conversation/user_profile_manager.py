# app/services/conversation/user_profile_manager.py
"""用户信息管理模块，处理用户信息的检查、收集和更新"""

from __future__ import annotations
import logging
from typing import TYPE_CHECKING, Dict, Any, List, Optional, Tuple, Union
from sqlalchemy.orm import Session

if TYPE_CHECKING:
    from app.models.user import User

logger = logging.getLogger(__name__)

class UserProfileManager:
    """用户信息管理器，用于处理用户资料的缺失检查、收集和更新"""
    
    # 用户字段到显示名称的映射
    FIELD_NAME_MAP = {
        "height": "身高",
        "weight": "体重",
        "age": "年龄",
        "gender": "性别",
        "fitness_goal": "健身目标",
        "experience_level": "健身经验",
        "activity_level": "活动水平",
        "health_conditions": "健康状况",
        "allergies": "过敏情况"
    }
    
    # 用户字段到枚举值的映射
    FIELD_ENUM_MAP = {
        "gender": {1: "男", 2: "女", 3: "其他"},
        "fitness_goal": {1: "增肌", 2: "减脂", 3: "塑形", 4: "增强体能", 5: "康复"},
        "experience_level": {1: "初学者", 2: "中级", 3: "高级"},
        "activity_level": {1: "久坐不动", 2: "轻度活动", 3: "中度活动", 4: "高度活动", 5: "极高活动"}
    }
    
    # 用户字段验证规则
    FIELD_VALIDATION = {
        "height": {"type": "float", "min": 100, "max": 250, "unit": "厘米"},
        "weight": {"type": "float", "min": 30, "max": 200, "unit": "公斤"},
        "age": {"type": "int", "min": 16, "max": 100, "unit": "岁"},
        "gender": {"type": "enum", "values": [1, 2, 3]},
        "fitness_goal": {"type": "enum", "values": [1, 2, 3, 4, 5]},
        "experience_level": {"type": "enum", "values": [1, 2, 3]},
        "activity_level": {"type": "enum", "values": [1, 2, 3, 4, 5]},
        "health_conditions": {"type": "text"},
        "allergies": {"type": "text"}
    }
    
    # 用户信息收集时的显示顺序和必要性
    FIELD_COLLECTION_ORDER = [
        {"field": "gender", "required": True},
        {"field": "age", "required": True},
        {"field": "height", "required": True},
        {"field": "weight", "required": True},
        {"field": "fitness_goal", "required": True},
        {"field": "experience_level", "required": True},
        {"field": "activity_level", "required": False},
        {"field": "health_conditions", "required": False},
        {"field": "allergies", "required": False}
    ]
    
    def __init__(self, llm_proxy_service):
        """初始化用户信息管理器
        
        Args:
            llm_proxy_service: LLM代理服务，用于生成提示和验证消息
        """
        self.llm_proxy = llm_proxy_service
    
    def get_missing_fields(self, user_data: Dict[str, Any], skipped_fields: List[str] = None) -> List[str]:
        """获取用户信息中缺失的字段列表
        
        Args:
            user_data: 用户信息字典
            skipped_fields: 已跳过的字段列表
            
        Returns:
            缺失的字段列表
        """
        if skipped_fields is None:
            skipped_fields = []
        
        missing_fields = []
        
        # 按照收集顺序检查必要字段
        for field_info in self.FIELD_COLLECTION_ORDER:
            field = field_info["field"]
            required = field_info["required"]
            
            # 如果是必要字段，且值为空（None、空字符串、0），且未被跳过
            if required and field not in skipped_fields:
                if field not in user_data or user_data[field] is None or user_data[field] == "" or user_data[field] == 0:
                    missing_fields.append(field)
        
        return missing_fields
    
    async def validate_and_parse(self, field: str, value: str) -> Tuple[bool, Any]:
        """验证并解析用户输入的字段值
        
        Args:
            field: 字段名称
            value: 用户输入的值
            
        Returns:
            (是否有效, 解析后的值)
        """
        # 获取字段验证规则
        if field not in self.FIELD_VALIDATION:
            logger.warning(f"未知字段: {field}")
            return False, None
        
        validation = self.FIELD_VALIDATION[field]
        field_type = validation["type"]
        
        # 使用LLM协助验证和解析
        try:
            # 构建提示，根据字段类型
            system_message = """你是一个健身助手，负责验证用户提供的信息。
你需要验证用户输入是否符合要求，并将其转换为正确的格式。"""
            
            user_message = f"""请验证以下用户输入是否符合要求:
字段: {self.FIELD_NAME_MAP.get(field, field)}
用户输入: {value}
字段类型: {field_type}
"""
            
            # 添加特定字段的验证规则
            if field_type == "float":
                user_message += f"""
要求: 浮点数，范围: {validation.get('min')} - {validation.get('max')} {validation.get('unit', '')}
"""
            elif field_type == "int":
                user_message += f"""
要求: 整数，范围: {validation.get('min')} - {validation.get('max')} {validation.get('unit', '')}
"""
            elif field_type == "enum":
                enum_map = self.FIELD_ENUM_MAP.get(field, {})
                enum_values = [f"{k}: {v}" for k, v in enum_map.items()]
                enum_values_str = ", ".join(enum_values)
                user_message += f"""
要求: 枚举值，可选值: {enum_values_str}
"""
            
            user_message += """
请分析用户输入并返回以下JSON格式:
{
  "is_valid": true或false,
  "parsed_value": 解析后的值,
  "error": 错误信息(如果有)
}
"""
            
            # 发送请求到LLM
            result = await self.llm_proxy.aget_chat_response(
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message}
                ],
                temperature=0
            )
            
            # 解析JSON响应
            from app.services.conversation.parameter_extractor import extract_json_from_text
            parsed_result = extract_json_from_text(result)
            
            if parsed_result and "is_valid" in parsed_result:
                is_valid = parsed_result["is_valid"]
                parsed_value = parsed_result.get("parsed_value")
                
                # 对于枚举类型，尝试通过名称查找ID
                if is_valid and field_type == "enum" and isinstance(parsed_value, str):
                    enum_map = self.FIELD_ENUM_MAP.get(field, {})
                    for k, v in enum_map.items():
                        if v.lower() == parsed_value.lower():
                            parsed_value = k
                            break
                
                return is_valid, parsed_value
            
            logger.warning(f"LLM未返回有效的验证结果: {result}")
            return False, None
            
        except Exception as e:
            logger.error(f"验证用户输入时出错: {str(e)}")
            return False, None
    
    async def generate_query_message(self, fields: List[str], is_retry: bool = False) -> str:
        """生成询问用户信息的消息
        
        Args:
            fields: 要询问的字段列表
            is_retry: 是否是重试询问
            
        Returns:
            生成的询问消息
        """
        if not fields:
            return "请告诉我您的一些信息，以便我能更好地为您提供建议。"
        
        field = fields[0]  # 目前只处理第一个字段
        
        # 使用LLM生成更自然的询问
        try:
            field_name = self.FIELD_NAME_MAP.get(field, field)
            field_type = self.FIELD_VALIDATION.get(field, {}).get("type", "text")
            
            system_message = """你是一个健身助手，负责以自然、友好的方式询问用户的基本信息。
你的问题应该简短、明确、礼貌，并提供必要的解释。"""
            
            user_message = f"""请生成一个询问用户{field_name}的问题。

字段: {field_name}
字段类型: {field_type}
是否重试: {'是' if is_retry else '否'}
"""
            
            # 添加特定字段的提示
            if field_type == "float" or field_type == "int":
                validation = self.FIELD_VALIDATION.get(field, {})
                user_message += f"""
要求: 数值范围 {validation.get('min')} - {validation.get('max')} {validation.get('unit', '')}
"""
            elif field_type == "enum":
                enum_map = self.FIELD_ENUM_MAP.get(field, {})
                enum_values = [f"{v}" for k, v in enum_map.items()]
                enum_values_str = "、".join(enum_values)
                user_message += f"""
可选值: {enum_values_str}
"""
            
            user_message += """
要求:
1. 问题应简短自然，不超过30个字
2. 不要使用表情符号
3. 如果是重试询问，请友善提醒用户按格式回答
4. 不要使用"请问"、"您好"等开场白，直接提问
5. 直接返回一个简短的问题，不需要任何额外解释或前缀
"""
            
            # 发送请求到LLM
            query = await self.llm_proxy.aget_chat_response(
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message}
                ],
                temperature=0.7
            )
            
            # 清理和格式化响应
            query = query.strip('"\' \n')
            
            return query
            
        except Exception as e:
            logger.error(f"生成询问消息时出错: {str(e)}")
            
            # 回退到预定义问题
            if is_retry:
                return f"请再次告诉我您的{self.FIELD_NAME_MAP.get(field, field)}。"
            else:
                if field == "height":
                    return "您的身高是多少厘米？"
                elif field == "weight":
                    return "您的体重是多少公斤？"
                elif field == "age":
                    return "您的年龄是多少岁？"
                elif field == "gender":
                    return "您的性别是？（男/女/其他）"
                elif field == "fitness_goal":
                    return "您的健身目标是什么？（增肌/减脂/塑形/增强体能/康复）"
                elif field == "experience_level":
                    return "您的健身经验水平如何？（初学者/中级/高级）"
                elif field == "activity_level":
                    return "您平时的活动水平如何？（久坐不动/轻度活动/中度活动/高度活动/极高活动）"
                else:
                    return f"请告诉我您的{self.FIELD_NAME_MAP.get(field, field)}。"
    
    def format_user_info(self, user_data: Dict[str, Any]) -> str:
        """格式化用户信息为可读文本
        
        Args:
            user_data: 用户信息字典
            
        Returns:
            格式化后的用户信息文本
        """
        info_lines = []
        
        # 按照收集顺序格式化信息
        for field_info in self.FIELD_COLLECTION_ORDER:
            field = field_info["field"]
            
            if field in user_data and user_data[field]:
                field_name = self.FIELD_NAME_MAP.get(field, field)
                value = user_data[field]
                
                # 处理枚举字段
                if field in self.FIELD_ENUM_MAP and isinstance(value, int):
                    value = self.FIELD_ENUM_MAP[field].get(value, value)
                
                # 添加单位
                if field == "height":
                    value = f"{value} 厘米"
                elif field == "weight":
                    value = f"{value} 公斤"
                elif field == "age":
                    value = f"{value} 岁"
                
                info_lines.append(f"- {field_name}: {value}")
        
        # 如果有BMI，添加到信息中
        if "bmi" in user_data and user_data["bmi"]:
            bmi = user_data["bmi"]
            bmi_category = "未知"
            if bmi < 18.5:
                bmi_category = "偏瘦"
            elif bmi < 24:
                bmi_category = "正常"
            elif bmi < 28:
                bmi_category = "超重"
            else:
                bmi_category = "肥胖"
            
            info_lines.append(f"- BMI: {bmi} ({bmi_category})")
        
        return "\n".join(info_lines)
    
    async def handle_user_info_update(self, db: Session, user_id: int, field: str, value: Any) -> Tuple[str, Dict[str, Any]]:
        """处理用户信息更新
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            field: 要更新的字段
            value: 字段值
            
        Returns:
            (更新结果消息, 更新的数据字典)
        """
        from app.crud import crud_user
        
        try:
            # 获取用户信息
            user = crud_user.get(db, id=user_id)
            if not user:
                logger.error(f"找不到用户信息: user_id={user_id}")
                return "抱歉，找不到用户信息，请重新登录。", {}
            
            # 处理枚举字段的值转换
            update_value = value
            if field in self.FIELD_ENUM_MAP and isinstance(value, str):
                field_mapping = self.FIELD_ENUM_MAP[field]
                # 尝试直接查找文本值
                for code, text in field_mapping.items():
                    if text == value:
                        update_value = code
                        logger.info(f"字段 '{field}' 的值从文本 '{value}' 映射到整数代码: {update_value}")
                        break
                else:
                    logger.warning(f"无法将 '{field}' 的值 '{value}' 映射到整数代码，将尝试直接使用")
            
            # 更新用户信息
            update_data = {field: update_value}
            updated_user = crud_user.update(db, db_obj=user, obj_in=update_data)
            
            if updated_user:
                logger.info(f"用户信息更新成功: user_id={user_id}, field={field}")
                
                # 对于枚举字段，在响应中使用可读的文本值
                display_value = value  # 默认使用原始值
                if field in self.FIELD_ENUM_MAP and isinstance(update_value, int):
                    field_mapping = self.FIELD_ENUM_MAP[field]
                    display_value = field_mapping.get(update_value, update_value)
                
                # 如果更新的是身高或体重，自动计算BMI
                if field in ["height", "weight"]:
                    try:
                        if updated_user.height and updated_user.weight:
                            height_m = updated_user.height / 100
                            bmi = updated_user.weight / (height_m * height_m)
                            crud_user.update(db, db_obj=updated_user, obj_in={"bmi": round(bmi, 2)})
                            logger.info(f"已自动更新用户BMI: {round(bmi, 2)}")
                    except Exception as e:
                        logger.error(f"BMI计算失败: {str(e)}")
                
                return f"您的{self.FIELD_NAME_MAP.get(field, field)}已成功更新为{display_value}。", {"updated_field": field, "value": update_value}
            else:
                logger.error(f"用户信息更新失败: user_id={user_id}, field={field}")
                return "抱歉，更新信息失败，请稍后再试。", {}
            
        except Exception as e:
            logger.error(f"更新用户信息错误: {str(e)}")
            return f"更新信息时发生错误: {str(e)}", {}