# app/services/conversation/intent_handler/exercise_handler.py
from __future__ import annotations
from typing import Dict, Any, List, Union, AsyncGenerator, Optional, TYPE_CHECKING
import logging
import json
import time
import traceback
from sqlalchemy.orm import Session
from app import models, crud
from app.services.intent_recognizer import IntentData
from app.services.llm_proxy_service import LLMProxyService
from app.services.sql_tool_service import SQLToolService
from app.services.training_plan_service import TrainingPlanService

from .base_handler import BaseIntentHandler
from .utils import format_user_info

# 导入辅助模块
from app.services.conversation.parameter_extractor import ParameterExtractor
from app.services.conversation.exercise_helper import (get_candidate_exercises, personalized_filtering, _rule_based_filtering)
from app.services.conversation.training_plan_manager import TrainingPlanManager
from app.services.conversation.profile_helper import (_get_user_profile, _get_recommended_difficulty,
                                                     _get_training_params, _get_level_text)

if TYPE_CHECKING:
    from app.services.conversation.orchestrator import ConversationService

logger = logging.getLogger(__name__)

async def _handle_exercise_intent(
    service: 'ConversationService',
    message: str,
    intent_data: IntentData,
    user: models.User,
    response_meta_info: Dict[str, Any]
) -> AsyncGenerator[Union[str, Dict[str, Any]], None]:
    """处理锻炼相关的意图，生成锻炼建议或方案 (流式)

    Assumes body_part and scenario are already resolved by process_message_stream.

    Args:
        service: The ConversationService instance.
        message: 用户消息 (用于个性化筛选)
        intent_data: 意图识别结果
        user: 当前用户对象
        response_meta_info: 当前的元数据 (包含已确定的 scenario 和 body_part)

    Yields:
        回复消息块 (str) 或 结构化数据 (dict)
    """
    # 1. Get body part and scenario from metadata (already resolved)
    body_part = response_meta_info.get("identified_body_part") or await ParameterExtractor._extract_body_part(service, message, intent_data) # Fallback extraction
    scenario_code = response_meta_info.get("scenario") # e.g., "gym" or "home"

    # Basic check - should ideally be guaranteed by caller
    if not body_part:
        logger.warning("_handle_exercise_intent called without body_part")
        yield "请先告诉我您想锻炼哪个部位。"
        return
    if not scenario_code:
        logger.warning("_handle_exercise_intent called without scenario")
        # 使用新的参数收集逻辑，设置collecting_training_params状态
        response_meta_info["collecting_training_params"] = True
        response_meta_info["asking_param"] = "scenario"
        # 确保training_params存在并包含已知的body_part
        if "training_params" not in response_meta_info:
            response_meta_info["training_params"] = {}
        response_meta_info["training_params"]["body_part"] = body_part
        yield {"meta_info_update": {"collecting_training_params": True, "asking_param": "scenario"}}
        yield f"请告诉我您想在健身房还是在家锻炼{body_part}。"
        return

    # 2. 获取用户信息
    user_profile = _get_user_profile(service, user)

    # 3. 确定推荐难度和训练参数
    difficulty_range = _get_recommended_difficulty(service, user_profile)
    training_params = _get_training_params(service, user_profile)

    # 4. 获取候选训练动作
    # Map scenario code to Chinese for DB query if needed
    cn_scenario = "健身房" if scenario_code == "gym" else "居家"

    try:
        # 如果是recommend_exercise意图，调用generate_daily_workout生成训练计划
        if intent_data.intent == "recommend_exercise":
            # 创建训练计划服务实例
            from app.services.training_plan_service import TrainingPlanService
            from app.services.sql_tool_service import BODY_PART_CATEGORIES

            # 获取用户ID
            user_id = user.id
            if not user_id:
                logger.error("用户ID为空，无法生成训练计划")
                yield {"type": "message", "content": "抱歉，无法获取您的用户信息，请重新登录后再试。", "role": "assistant"}
                return

            # 获取body_part_id
            body_part_map = {bp["name"]: bp["id"] for bp in BODY_PART_CATEGORIES}

            # 将body_part转换为body_part_id
            body_part_ids = []
            if isinstance(body_part, list):
                for bp in body_part:
                    if bp in body_part_map:
                        body_part_ids.append(body_part_map[bp])
                    else:
                        # 如果找不到映射，记录警告并使用默认值
                        logger.warning(f"无法将身体部位 '{bp}' 映射到ID，使用默认值1(胸部)")
                        body_part_ids.append(1)  # 默认使用胸部(ID=1)
            else:
                if body_part in body_part_map:
                    body_part_ids.append(body_part_map[body_part])
                else:
                    logger.warning(f"无法将身体部位 '{body_part}' 映射到ID，使用默认值1(胸部)")
                    body_part_ids.append(1)  # 默认使用胸部(ID=1)

            if not body_part_ids:
                # 如果没有有效的body_part_id，使用默认值
                body_part_ids = [1]  # 默认使用胸部(ID=1)

            logger.info(f"转换后的body_part_ids: {body_part_ids}")

            # 设置可用器材
            available_equipment = []
            if scenario_code == "home":
                # 居家训练默认使用自重器材(ID=2)和哑铃(ID=4)
                available_equipment = [2, 4]
                logger.info(f"设置为居家训练，使用自重器材(ID=2)和哑铃(ID=4)")
            elif scenario_code == "gym":
                # 健身房场景可以使用更多器材
                available_equipment = [1, 3, 4, 6, 7]
                logger.info(f"设置为健身房训练，使用多种器材(IDs=[1,3,4,6,7])")
            else:
                # 默认使用自重器材
                available_equipment = [2]
                logger.info(f"未知场景，默认使用自重器材(ID=2)")

            # 记录调用开始时间
            import time
            start_time = time.time()

            # 调用服务生成单日训练计划
            yield f"正在为您生成{body_part}的训练计划..."

            try:
                training_plan_service = TrainingPlanService(service.db, service.llm_proxy, service.sql_tool)
                plan = await training_plan_service.generate_daily_workout(
                    user_id=user_id,
                    target_body_parts=body_part_ids,
                    available_time=60,  # 默认60分钟
                    available_equipment=available_equipment,
                    recovery_level=5,  # 默认恢复程度
                    additional_notes=None  # 无额外备注
                )

                # 记录调用结束时间和耗时
                end_time = time.time()
                logger.info(f"单日训练计划生成成功，耗时: {end_time - start_time:.2f}秒")

                # 将workout_id添加到meta_info
                if "id" in plan:
                    workout_id = plan["id"]
                    if "training_params" not in response_meta_info:
                        response_meta_info["training_params"] = {}
                    response_meta_info["training_params"]["related_workout_id"] = workout_id
                    logger.info(f"保存workout_id到元数据: {workout_id}")

                    # 发送元数据更新
                    yield {"type": "meta", "data": response_meta_info}

                # 获取训练计划名称
                workout_name = plan.get("workout_name", plan.get("name", f"{body_part}训练计划"))

                # 返回计划和元数据
                yield {"type": "message", "content": f"已为您生成{body_part}训练计划: {workout_name}", "role": "assistant"}
                yield {"type": "structured_data", "data": plan, "data_type": "workout"}

                # 提前返回，不执行后续的候选动作筛选逻辑
                return

            except Exception as e:
                logger.error(f"生成训练计划时出错: {str(e)}", exc_info=True)
                import traceback
                logger.error(f"错误堆栈: {traceback.format_exc()}")

                # 如果生成失败，继续执行原有的候选动作筛选逻辑
                yield f"生成训练计划失败，将为您推荐一些适合的训练动作..."

        # 如果不是recommend_exercise意图或者生成计划失败，执行原有的候选动作筛选逻辑
        yield f"正在为您查找适合在{cn_scenario}进行的{body_part}训练动作..."

        candidate_exercises = await get_candidate_exercises(
            service,
            body_part=body_part,
            scenario=cn_scenario,
            difficulty_range=difficulty_range,
            limit=100  # 获取足够多的候选
        )

        if not candidate_exercises:
            # No suitable exercises found in DB, use LLM to generate suggestions
            yield f"数据库中暂未找到完全匹配的{body_part}动作，正在尝试为您生成一些建议..."

            context = f"""
            用户想要锻炼{body_part}，训练场景为{cn_scenario}。
            用户的健身经验水平是{_get_level_text(service, user_profile, 'experience_level')}，
            健身目标是{_get_level_text(service, user_profile, 'fitness_goal')}。
            请生成3-5个适合该用户情况的{body_part}锻炼动作建议，每个动作包括:
            1. 动作名称
            2. 动作描述/要点
            3. 推荐组数和次数 (基于用户目标)
            """

            try:
                # Use streaming for LLM fallback as well
                async for chunk in service.llm_proxy.stream_chat(
                     messages=[
                        {"role": "system", "content": "你是一位专业的健身教练，请根据用户情况提供专业、安全、有效的锻炼建议。"},
                        {"role": "user", "content": context}
                    ],
                    model=service.conversation_model,
                    temperature=0.7
                ):
                     yield chunk
                return # Exit after LLM fallback

            except Exception as e:
                logger.error(f"生成锻炼建议时出错: {e}")
                yield f"很抱歉，我在为您查找{body_part}的锻炼动作时遇到了问题。请稍后再试或换一个身体部位。"
                return # Exit on error

        # 5. 使用LLM进行个性化二次筛选
        # (Consider adding a yield message here too if filtering takes time)
        yield "正在根据您的个人情况筛选最合适的动作..."

        selected_exercises = await personalized_filtering(
            service,
            candidate_exercises,
            user_profile,
            training_params,
            message=message,
            limit=5  # 最终选择5个最合适的
        )

        if not selected_exercises:
             # Filtering failed or returned empty, fallback to rule-based or provide message
             yield f"未能完成个性化筛选，为您提供一些基于规则的{body_part}推荐："
             selected_exercises = _rule_based_filtering(
                 service, candidate_exercises, user_profile, training_params, limit=5
             )
             if not selected_exercises: # Still no exercises
                 yield f"抱歉，暂时无法为您推荐合适的{body_part}动作。"
                 return


        # 6. Format and yield the result (always list for this intent)
        formatted_result = TrainingPlanManager._format_exercise_list(service, selected_exercises, body_part)
        formatted_data = json.loads(formatted_result)

        # 7. 如果是search_exercise意图，保存workout到数据库
        # 注意：recommend_exercise意图已经在前面通过generate_daily_workout处理了
        if intent_data.intent == "search_exercise":
            try:
                # 创建训练计划服务实例
                from app.services.training_plan_service import TrainingPlanService
                training_plan_service = TrainingPlanService(service.db, service.llm_proxy, service.sql_tool)

                # 准备workout数据
                workout_data = {
                    "name": f"{body_part}训练",
                    "description": f"针对{body_part}的训练动作推荐",
                    "estimated_duration": 45,  # 默认45分钟
                    "workout_exercises": []
                }

                # 将选中的动作添加到workout_exercises
                for i, exercise in enumerate(selected_exercises):
                    workout_exercise = {
                        "name": exercise.get("name", ""),
                        "description": exercise.get("description", ""),
                        "sets": 3,  # 默认3组
                        "reps": "10-12",  # 默认10-12次
                        "rest_seconds": 60,  # 默认休息60秒
                        "exercise_id": exercise.get("id"),
                        "body_part_id": exercise.get("body_part_id", 1),
                        "order": i + 1
                    }
                    workout_data["workout_exercises"].append(workout_exercise)

                # 获取body_part_id
                body_part_id = None
                if selected_exercises and len(selected_exercises) > 0:
                    body_part_id = selected_exercises[0].get("body_part_id")

                # 保存workout到数据库，不创建组记录
                saved_workout = await training_plan_service.save_workout_only(
                    user_id=user.id,
                    workout_data=workout_data,
                    target_body_parts=[body_part_id] if body_part_id else None,
                    training_scenario=scenario_code
                )

                # 将workout_id添加到meta_info
                if saved_workout and "id" in saved_workout:
                    workout_id = saved_workout["id"]
                    logger.info(f"保存workout成功，ID: {workout_id}")

                    # 更新meta_info
                    if "training_params" not in response_meta_info:
                        response_meta_info["training_params"] = {}
                    response_meta_info["training_params"]["related_workout_id"] = workout_id

                    # 确保collecting_training_params为false
                    response_meta_info["collecting_training_params"] = False

                    # 发送元数据更新
                    yield {"meta_info_update": {
                        "training_params": response_meta_info["training_params"],
                        "collecting_training_params": False
                    }}
                else:
                    logger.warning("保存workout失败，没有获取到ID")
            except Exception as e:
                logger.error(f"保存workout时出错: {str(e)}", exc_info=True)
                # 即使保存失败，也要确保collecting_training_params为false
                response_meta_info["collecting_training_params"] = False
                yield {"meta_info_update": {"collecting_training_params": False}}

        # Yield the structured data (client can handle rendering)
        yield formatted_data

    except Exception as e:
        logger.error(f"处理锻炼意图出错: {str(e)}", exc_info=True)
        yield f"抱歉，在为您查找{body_part}的锻炼动作时遇到了技术问题。"


class ExerciseIntentHandler(BaseIntentHandler):
    """处理锻炼相关意图的处理器"""

    def __init__(
        self,
        db: Session,
        llm_proxy: LLMProxyService,
        sql_tool: SQLToolService,
        agent_executor,
        conversation_model: str = None
    ):
        """初始化锻炼意图处理器

        Args:
            db: 数据库会话
            llm_proxy: LLM代理服务
            sql_tool: SQL工具服务
            agent_executor: Agent执行器
            conversation_model: 对话模型名称
        """
        super().__init__(db, llm_proxy, sql_tool, conversation_model)
        self.agent_executor = agent_executor

    async def handle(
        self,
        intent_data: IntentData,
        user_data: Dict[str, Any],
        history: List[Dict[str, Any]],
        meta_info: Dict[str, Any]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """处理锻炼相关意图

        Args:
            intent_data: 意图数据
            user_data: 用户数据
            history: 对话历史
            meta_info: 元数据

        Yields:
            响应数据
        """
        logger.info(f"处理训练动作意图: {intent_data.intent}")
        logger.info(f"意图参数: {intent_data.parameters}")
        logger.info(f"元数据: {meta_info}")

        try:
            # 获取用户对象
            from app import crud
            user_id = user_data.get("id")
            if not user_id:
                logger.error("用户ID为空，无法处理锻炼意图")
                yield {"type": "message", "content": "抱歉，无法获取您的用户信息，请重新登录后再试。", "role": "assistant"}
                return

            # 获取用户对象
            user = crud.crud_user.get(self.db, id=user_id)
            if not user:
                logger.error(f"找不到用户: user_id={user_id}")
                yield {"type": "message", "content": "抱歉，无法获取您的用户信息，请重新登录后再试。", "role": "assistant"}
                return

            # 获取最后一条消息内容
            message = ""
            if history and len(history) > 0:
                last_message = history[-1]
                if last_message.get("role") == "user":
                    message = last_message.get("content", "")

            # 从intent_data中提取参数并更新meta_info
            if intent_data.parameters:
                # 提取body_part
                if "body_part" in intent_data.parameters:
                    body_part = intent_data.parameters["body_part"]
                    meta_info["identified_body_part"] = body_part
                    logger.info(f"从意图参数中提取body_part: {body_part}")

                # 提取scenario
                if "scenario" in intent_data.parameters:
                    scenario = intent_data.parameters["scenario"]
                    meta_info["scenario"] = scenario
                    logger.info(f"从意图参数中提取scenario: {scenario}")

            # 创建ConversationService对象
            from app.services.conversation.orchestrator import ConversationService
            service = ConversationService(
                db=self.db,
                llm_proxy_service=self.llm_proxy,
                conversation_model=self.conversation_model
            )

            # 调用迁移后的_handle_exercise_intent函数
            logger.info(f"调用_handle_exercise_intent处理{intent_data.intent}意图")
            async for response in _handle_exercise_intent(
                service=service,
                message=message,
                intent_data=intent_data,
                user=user,
                response_meta_info=meta_info
            ):
                # 记录返回的响应类型
                if isinstance(response, dict):
                    logger.info(f"_handle_exercise_intent返回字典类型响应: {list(response.keys())}")
                else:
                    logger.info(f"_handle_exercise_intent返回字符串类型响应: {response[:50]}...")
                yield response

        except Exception as e:
            logger.error(f"处理训练动作意图时出错: {str(e)}", exc_info=True)
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            yield {"type": "message", "content": f"抱歉，处理您的请求时出现了问题: {str(e)}", "role": "assistant"}
