# app/services/conversation/intent_handler/training_plan_handler.py
from __future__ import annotations
from typing import Dict, Any, List, Union, AsyncGenerator, Optional, TYPE_CHECKING
import logging
import json
import traceback
import asyncio
from sqlalchemy.orm import Session
from app import models, crud
from app.services.intent_recognizer import IntentData
from app.services.llm_proxy_service import LLMProxyService
from app.services.sql_tool_service import SQLToolService
from app.services.training_plan_service import TrainingPlanService

from .base_handler import BaseIntentHandler

if TYPE_CHECKING:
    from app.services.conversation.orchestrator import ConversationService

logger = logging.getLogger(__name__)

async def _handle_training_plan_intent(
    service: 'ConversationService',
    message: str,
    intent_data: IntentData,
    user: models.User,
    response_meta_info: Dict[str, Any]
) -> AsyncGenerator[Union[str, Dict[str, Any]], None]:
    """处理创建训练计划意图"""
    from app import crud
    from app.services.training_plan_service import TrainingPlanService
    from app.services.conversation.profile_helper import _get_user_profile

    # 记录函数调用开始
    logger.info(f"进入_handle_training_plan_intent函数，处理意图: {intent_data.intent}")

    # 安全获取用户ID，防止user对象已分离
    user_id = getattr(user, 'id', None) if user is not None else None
    logger.info(f"处理创建训练计划意图: user_id={user_id}, intent={intent_data.intent}")
    logger.info(f"意图参数: {intent_data.parameters}")
    logger.info(f"元数据: {response_meta_info}")

    # 记录用户消息内容，可能有助于调试
    logger.info(f"用户消息: {message[:100]}{'...' if len(message) > 100 else ''}")

    if not user_id:
        logger.error("无法处理训练计划：用户ID为空")
        yield "抱歉，处理您的训练计划请求时出现了问题，无法获取您的用户信息。"
        return

    # 获取最新的用户对象，确保连接到当前会话
    try:
        user_fresh = crud.crud_user.get(service.db, id=user_id)
        if not user_fresh:
            logger.error(f"无法获取用户信息: user_id={user_id}")
            yield "抱歉，无法获取您的用户信息，请尝试重新登录。"
            return
        # 使用新鲜的用户对象替换可能已分离的对象
        user = user_fresh
        logger.info(f"获取用户信息成功: id={user.id}, 性别={user.gender}, 年龄={user.age}, 健身目标={user.fitness_goal}")
    except Exception as e:
        logger.error(f"获取用户信息时出错: {str(e)}")
        yield f"抱歉，获取您的用户信息时出现了错误: {str(e)}"
        return

    training_plan_service = TrainingPlanService(service.db, service.llm_proxy, service.sql_tool)
    user_profile = _get_user_profile(service, user)
    logger.info(f"用户资料: {user_profile}")

    # 确定计划类型 (日常计划还是完整训练计划)
    is_daily_plan = "daily" in intent_data.intent
    logger.info(f"计划类型: {'单日训练计划' if is_daily_plan else '完整训练计划'}")

    # 1. 从意图参数和元数据中提取信息
    body_part = None
    body_part_id = None

    # 首先从训练参数中获取
    training_params = response_meta_info.get("training_params", {})
    if training_params and "body_part" in training_params:
        body_part = training_params["body_part"]
        # 如果body_part是列表，取第一个元素用于兼容性
        if isinstance(body_part, list) and len(body_part) > 0:
            body_part = body_part[0]
        logger.info(f"从训练参数中获取到身体部位: {body_part}")

    # 尝试从意图参数中获取
    if not body_part and hasattr(intent_data, 'parameters') and intent_data.parameters:
        # 尝试获取body_part
        body_part = intent_data.parameters.get('body_part')
        logger.info(f"从意图参数中获取到身体部位: {body_part}")

        # 尝试从body_parts数组获取
        if not body_part and 'body_parts' in intent_data.parameters:
            body_parts = intent_data.parameters.get('body_parts')
            if body_parts and isinstance(body_parts, list) and len(body_parts) > 0:
                body_part = body_parts[0]
                logger.info(f"从意图参数body_parts数组中获取到身体部位: {body_part}")

    # 如果意图参数中没有，尝试从元数据获取
    if not body_part:
        body_part = response_meta_info.get("identified_body_part")
        logger.info(f"从元数据中获取到身体部位: {body_part}")

    # 记录识别到的身体部位
    if body_part:
        logger.info(f"最终确定训练计划将针对身体部位: {body_part}")
        # 尝试将body_part映射到ID
        from app.services.sql_tool_service import BODY_PART_CATEGORIES
        body_part_map = {bp["name"]: bp["id"] for bp in BODY_PART_CATEGORIES}
        body_part_id = body_part_map.get(body_part)
        logger.info(f"身体部位'{body_part}'映射到ID: {body_part_id}")
    else:
        logger.warning("未能确定身体部位")

    # 2. 确定训练计划参数
    duration_weeks = 4  # 默认值
    if is_daily_plan:
        duration_weeks = 1  # 日常计划只需一周

    # 根据活动水平确定每周训练天数
    days_per_week = 3  # 默认值
    if "activity_level" in user_profile and user_profile["activity_level"]:
        activity_level = user_profile["activity_level"]
        # 确保是整数类型
        if isinstance(activity_level, (int, float)):
            activity_level = int(activity_level)
            if activity_level == 1:  # 低活动量
                days_per_week = 2
            elif activity_level == 2:  # 轻度活动
                days_per_week = 3
            elif activity_level == 3:  # 中度活动
                days_per_week = 4
            elif activity_level == 4:  # 高度活动
                days_per_week = 5

    logger.info(f"训练计划参数: duration_weeks={duration_weeks}, days_per_week={days_per_week}")

    try:
        if is_daily_plan:
            # 生成单日训练计划
            workout_params = {
                "user_id": user.id,
                "available_time": 60  # 默认60分钟
            }

            # 如果有特定部位，添加到参数
            if body_part_id:
                # 将body_part转换为列表形式的target_body_parts参数
                workout_params["target_body_parts"] = [body_part_id]
                logger.info(f"添加目标部位ID到单日训练计划参数: target_body_parts=[{body_part_id}]")
            else:
                logger.warning("没有目标部位ID，将生成默认单日训练计划")

            # 提取场景信息并设置对应的设备参数
            scenario = None
            if training_params and "scenario" in training_params:
                scenario = training_params["scenario"]
                logger.info(f"从训练参数中获取到场景: {scenario}")

                # 根据场景设置器材参数
                if scenario == "home":
                    # 居家训练默认使用自重器材(ID=2)和哑铃(ID=4)
                    workout_params["available_equipment"] = [2, 4]
                    logger.info(f"设置为居家训练，使用自重器材(ID=2)和哑铃(ID=4)")
                elif scenario == "gym":
                    # 健身房场景可以使用更多器材，设置常用健身房器材
                    # 包括自重(ID=2)、哑铃(ID=1)、杠铃(ID=3)、器械(ID=4)、缆绳(ID=5)
                    workout_params["available_equipment"] = [1, 3, 4, 6, 7]
                    logger.info(f"设置为健身房训练，使用多种器材(IDs=[1,3,4,6,7])")
                else:
                    logger.warning(f"未知的训练场景: {scenario}")
                    # 默认使用自重器材
                    workout_params["available_equipment"] = [2]
                    logger.info(f"未知场景，默认使用自重器材(ID=2)")
            else:
                # 没有指定场景，默认使用自重器材
                workout_params["available_equipment"] = [2]
                logger.info(f"未指定场景，默认使用自重器材(ID=2)")

            logger.info(f"调用训练计划服务生成单日训练计划，参数: {workout_params}")

            try:
                # 记录调用开始时间
                import time
                start_time = time.time()

                logger.info(f"开始调用training_plan_service.generate_daily_workout，参数: {workout_params}")

                # 调用服务生成单日训练计划
                workout = await training_plan_service.generate_daily_workout(**workout_params)

                # 记录调用结束时间和耗时
                end_time = time.time()
                logger.info(f"单日训练计划生成成功，耗时: {end_time - start_time:.2f}秒")
                logger.info(f"单日训练计划生成成功: 名称={workout['name']}, 动作数量={len(workout['workout_exercises'])}")

                # 记录部分动作信息，便于调试
                if workout['workout_exercises']:
                    sample_exercises = workout['workout_exercises'][:3]  # 只记录前3个
                    for i, ex in enumerate(sample_exercises):
                        logger.info(f"示例动作 {i+1}: ID={ex.get('id')}, 名称={ex.get('name')}, 部位ID={ex.get('body_part_id')}")
                else:
                    logger.warning("生成的训练计划没有包含任何训练动作")

                # 检查是否有ID字段，将其添加到元数据
                if 'id' in workout:
                    plan_id = workout['id']
                    logger.info(f"保存训练计划ID到元数据: {plan_id}")
                    if "training_params" not in response_meta_info:
                        response_meta_info["training_params"] = {}
                    response_meta_info["training_params"]["related_plan_id"] = plan_id
                    response_meta_info["active_flow"] = "training_plan_discussion" # Set active flow
                    # 发送元数据更新
                    yield {"meta_info_update": {
                        "training_params": response_meta_info["training_params"],
                        "active_flow": "training_plan_discussion"
                        }
                    }
                else:
                    logger.warning("生成的训练计划数据中没有ID字段 (单日计划)")

                # 检查生成的训练计划是否与目标部位匹配
                if body_part and workout['workout_exercises']:
                    names = [ex['name'] for ex in workout['workout_exercises']]
                    logger.info(f"生成的训练动作列表: {names}")

                    # 检查计划名称是否包含目标部位
                    if body_part.lower() not in workout['name'].lower():
                        logger.warning(f"警告：训练计划名称'{workout['name']}'可能与目标部位'{body_part}'不匹配")

                    # 检查动作数量是否合理
                    if len(workout['workout_exercises']) < 3:
                        logger.warning(f"警告：训练计划动作数量较少，仅有{len(workout['workout_exercises'])}个动作")

                # 确保每个exercise包含所需字段
                for exercise in workout['workout_exercises']:
                    if 'image_name' not in exercise:
                        exercise['image_name'] = exercise.get('image_url', '')
                    if 'gif_url' not in exercise:
                        exercise['gif_url'] = ''
                    if 'exercise_type' not in exercise:
                        exercise['exercise_type'] = 'weight_reps'
                    if 'body_part_id' not in exercise:
                        exercise['body_part_id'] = body_part_id if body_part_id else 1

                logger.info(f"已确保所有动作包含必要字段: image_name, gif_url, exercise_type, body_part_id")

                # 记录完整的训练动作信息，确保数据完整性
                for exercise in workout['workout_exercises']:
                    logger.debug(f"构建完整动作信息: {exercise}")

                # 流式返回响应
                yield f"已为您生成：\"{workout['name']}\"\n\n"
                yield f"训练时长：{workout['estimated_duration']}分钟\n"

                # 最后返回结构化数据，格式化为客户端期望的格式
                # 确保返回完整的训练计划数据，包括所有必要字段
                structured_data = {
                    "type": "daily_workout",
                    "data": workout,
                    "meta_info": {
                        "plan_id": workout.get('id'),
                        "complete_data": True  # 标记数据是完整的
                    }
                }
                # 记录返回的结构化数据大小
                logger.info(f"返回结构化数据，包含 {len(workout['workout_exercises'])} 个训练动作")
                yield structured_data

            except Exception as e:
                # 生成计划失败，但保持参数状态不变
                logger.error(f"生成单日训练计划时出错: {str(e)}", exc_info=True)

                # 记录更详细的错误信息
                import traceback
                error_trace = traceback.format_exc()
                logger.error(f"错误堆栈: {error_trace}")

                # 记录参数信息，便于调试
                logger.error(f"出错时的参数: workout_params={workout_params}")
                logger.error(f"出错时的body_part={body_part}, body_part_id={body_part_id}")

                # 确保不重置收集的参数状态
                response_meta_info["collecting_training_params"] = False
                logger.info("保持参数状态不变，collecting_training_params=False")

                # 发送元数据更新，确保前端知道collecting_training_params=False
                yield {"meta_info_update": {"collecting_training_params": False}}

                # 给用户提供明确的错误信息和可能的解决方法
                error_msg = f"抱歉，生成训练计划时出错: {str(e)}"
                suggestion_msg = "\n\n您可以尝试以下方法：\n1. 重新请求训练计划\n2. 选择不同的训练部位\n3. 稍后再试"

                logger.info(f"返回错误信息给用户: {error_msg}")
                yield error_msg + suggestion_msg

        else:
            # 生成完整训练计划
            plan_params = {
                "user_id": user.id,
                "duration_weeks": duration_weeks,
                "days_per_week": days_per_week
            }

            # 如果有特定部位，添加到参数
            if body_part_id:
                # 将body_part转换为列表形式的focus_body_parts参数
                plan_params["focus_body_parts"] = [body_part_id]
                logger.info(f"添加目标部位ID到完整训练计划参数: focus_body_parts=[{body_part_id}]")
            else:
                logger.warning("没有目标部位ID，将生成默认完整训练计划")

            # 提取场景信息并设置对应的设备参数
            scenario = None
            if training_params and "scenario" in training_params:
                scenario = training_params["scenario"]
                logger.info(f"从训练参数中获取到场景: {scenario}")

                # 根据场景设置器材参数
                if scenario == "home":
                    # 居家训练默认使用自重器材(ID=2)和哑铃(ID=1)
                    plan_params["available_equipment"] = [2, 1]
                    logger.info(f"设置为居家训练，使用自重器材(ID=2)和哑铃(ID=1)")
                elif scenario == "gym":
                    # 健身房场景可以使用更多器材，设置常用健身房器材
                    # 包括自重(ID=2)、哑铃(ID=1)、杠铃(ID=3)、器械(ID=4)、缆绳(ID=5)
                    plan_params["available_equipment"] = [1, 2, 3, 4, 5]
                    logger.info(f"设置为健身房训练，使用多种器材(IDs=[1,2,3,4,5])")
                else:
                    logger.warning(f"未知的训练场景: {scenario}")
                    # 默认使用自重器材
                    plan_params["available_equipment"] = [2]
                    logger.info(f"未知场景，默认使用自重器材(ID=2)")
            else:
                # 没有指定场景，默认使用自重器材
                plan_params["available_equipment"] = [2]
                logger.info(f"未指定场景，默认使用自重器材(ID=2)")

            logger.info(f"调用训练计划服务生成完整训练计划，参数: {plan_params}")

            try:
                plan = await training_plan_service.generate_training_plan(**plan_params)
                logger.info(f"完整训练计划生成成功: 名称={plan['plan_name']}, 训练日数量={len(plan['workouts'])}")

                # 检查是否有ID字段，将其添加到元数据
                if 'id' in plan:
                    plan_id = plan['id']
                    logger.info(f"保存训练计划ID到元数据: {plan_id}")
                    if "training_params" not in response_meta_info:
                        response_meta_info["training_params"] = {}
                    response_meta_info["training_params"]["related_plan_id"] = plan_id
                    response_meta_info["active_flow"] = "training_plan_discussion" # Set active flow
                    # 发送元数据更新
                    yield {"meta_info_update": {
                        "training_params": response_meta_info["training_params"],
                        "active_flow": "training_plan_discussion"
                        }
                    }
                else:
                    logger.warning("生成的训练计划数据中没有ID字段 (完整计划)")

                # 确保每个workout中的每个exercise包含必要字段
                for workout in plan['workouts']:
                    for exercise in workout.get('exercises', []):
                        if 'image_name' not in exercise:
                            exercise['image_name'] = exercise.get('image_url', '')
                        if 'gif_url' not in exercise:
                            exercise['gif_url'] = ''
                        if 'exercise_type' not in exercise:
                            exercise['exercise_type'] = 'weight_reps'
                        if 'body_part_id' not in exercise:
                            exercise['body_part_id'] = body_part_id if body_part_id else 1

                logger.info(f"已确保所有训练日的所有动作包含必要字段: image_name, gif_url, exercise_type, body_part_id")

                # Stream the response gradually
                yield f"已为您生成一个{plan['duration_weeks']}周的训练计划：\"{plan['plan_name']}\"\n\n"
                yield f"计划说明：{plan['description']}\n\n"

                # 确保返回完整的训练计划数据，包括所有必要字段
                structured_data = {
                    "type": "training_plan_generated",
                    "data": plan,
                    "meta_info": {
                        "plan_id": plan.get('id'),
                        "complete_data": True  # 标记数据是完整的
                    }
                }
                # 记录返回的结构化数据大小
                total_exercises = sum(len(workout.get('exercises', [])) for workout in plan['workouts'])
                logger.info(f"返回结构化数据，包含 {len(plan['workouts'])} 个训练日，共 {total_exercises} 个训练动作")
                yield structured_data

            except Exception as e:
                # 生成计划失败，但保持参数状态不变
                logger.error(f"生成完整训练计划时出错: {str(e)}", exc_info=True)

                # 确保不重置收集的参数状态
                response_meta_info["collecting_training_params"] = False

                # 发送元数据更新，确保前端知道collecting_training_params=False
                yield {"meta_info_update": {"collecting_training_params": False}}

                # 给用户提供明确的错误信息和可能的解决方法
                error_msg = f"抱歉，生成训练计划时出错: {str(e)}"
                suggestion_msg = "\n\n您可以尝试以下方法：\n1. 重新请求训练计划\n2. 选择不同的训练部位\n3. 稍后再试"

                yield error_msg + suggestion_msg

    except Exception as e:
        # 处理其他未捕获的异常
        logger.error(f"处理训练计划意图时出错: {str(e)}", exc_info=True)

        # 确保不重置收集的参数状态
        response_meta_info["collecting_training_params"] = False

        # 发送元数据更新，确保前端知道collecting_training_params=False
        yield {"meta_info_update": {"collecting_training_params": False}}

        yield f"抱歉，处理您的训练计划请求时出错: {str(e)}\n\n请稍后重试。"


class TrainingPlanIntentHandler(BaseIntentHandler):
    """处理训练计划相关意图的处理器"""

    def __init__(
        self,
        db: Session,
        llm_proxy: LLMProxyService,
        sql_tool: SQLToolService,
        training_plan_service: Optional[TrainingPlanService] = None,
        conversation_model: str = None
    ):
        """初始化训练计划意图处理器

        Args:
            db: 数据库会话
            llm_proxy: LLM代理服务
            sql_tool: SQL工具服务
            training_plan_service: 训练计划服务
            conversation_model: 对话模型名称
        """
        super().__init__(db, llm_proxy, sql_tool, conversation_model)
        self.training_plan_service = training_plan_service

    async def handle(
        self,
        intent_data: IntentData,
        user_data: Dict[str, Any],
        history: List[Dict[str, Any]],
        meta_info: Dict[str, Any]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """处理训练计划相关意图

        Args:
            intent_data: 意图数据
            user_data: 用户数据
            history: 对话历史
            meta_info: 元数据

        Yields:
            响应数据
        """
        logger.info(f"处理训练计划意图: {intent_data.intent}")
        logger.info(f"意图参数: {intent_data.parameters}")
        logger.info(f"用户数据: id={user_data.get('id')}, 性别={user_data.get('gender')}, 年龄={user_data.get('age')}")
        logger.info(f"元数据: {meta_info}")

        try:
            if self.training_plan_service is None:
                # 如果训练计划服务未初始化，初始化它
                logger.info("训练计划服务未初始化，正在创建新实例")
                self.training_plan_service = TrainingPlanService(
                    db_session=self.db,
                    llm_proxy_service=self.llm_proxy,
                    sql_tool_service=self.sql_tool
                )
                logger.info("训练计划服务初始化完成")

            # 获取用户对象
            from app import crud
            user_id = user_data.get("id")
            if not user_id:
                logger.error("用户ID为空，无法处理训练计划意图")
                yield {"type": "message", "content": "抱歉，无法获取您的用户信息，请重新登录后再试。", "role": "assistant"}
                return

            # 获取用户对象
            user = crud.crud_user.get(self.db, id=user_id)
            if not user:
                logger.error(f"找不到用户: user_id={user_id}")
                yield {"type": "message", "content": "抱歉，无法获取您的用户信息，请重新登录后再试。", "role": "assistant"}
                return

            # 获取最后一条消息内容
            message = ""
            if history and len(history) > 0:
                last_message = history[-1]
                if last_message.get("role") == "user":
                    message = last_message.get("content", "")

            # 从intent_data中提取参数并更新meta_info
            if intent_data.parameters:
                # 提取body_part
                if "body_part" in intent_data.parameters:
                    body_part = intent_data.parameters["body_part"]
                    meta_info["identified_body_part"] = body_part
                    logger.info(f"从意图参数中提取body_part: {body_part}")

                # 提取scenario
                if "scenario" in intent_data.parameters:
                    scenario = intent_data.parameters["scenario"]
                    meta_info["scenario"] = scenario
                    logger.info(f"从意图参数中提取scenario: {scenario}")

            # 创建ConversationService对象
            from app.services.conversation.orchestrator import ConversationService
            service = ConversationService(
                db=self.db,
                llm_proxy=self.llm_proxy,
                sql_tool=self.sql_tool,
                conversation_model=self.conversation_model
            )

            # 调用迁移后的_handle_training_plan_intent函数
            logger.info(f"调用_handle_training_plan_intent处理{intent_data.intent}意图")
            async for response in _handle_training_plan_intent(
                service=service,
                message=message,
                intent_data=intent_data,
                user=user,
                response_meta_info=meta_info
            ):
                # 记录返回的响应类型
                if isinstance(response, dict):
                    logger.info(f"_handle_training_plan_intent返回字典类型响应: {list(response.keys())}")
                else:
                    logger.info(f"_handle_training_plan_intent返回字符串类型响应: {response[:50]}...")
                yield response

        except Exception as e:
            logger.error(f"处理训练计划意图时出错: {str(e)}", exc_info=True)
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            yield {"type": "message", "content": f"抱歉，处理您的训练计划请求时出现了问题: {str(e)}", "role": "assistant"}
