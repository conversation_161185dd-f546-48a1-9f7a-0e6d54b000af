# app/services/conversation/intent_handler/__init__.py
from typing import Dict, Any, List, Union, AsyncGenerator, Optional
import logging
import json
import asyncio
import traceback
from sqlalchemy.orm import Session
from app import models
from app.services.intent_recognizer import IntentData
from app.services.llm_proxy_service import LLMProxyService
from app.services.sql_tool_service import SQLToolService
from app.services.training_plan_service import TrainingPlanService

# 导入各个处理器
from .base_handler import BaseIntentHandler
from .exercise_handler import ExerciseIntentHandler
from .training_plan_handler import TrainingPlanIntentHandler
from .fitness_advice_handler import FitnessAdviceIntentHandler
from .general_chat_handler import GeneralChatIntentHandler
from .discuss_training_plan_handler import DiscussTrainingPlanIntentHandler
from .parameter_handler import ParameterHandler
from .utils import process_equipment_input, process_body_part_input, determine_training_scenario, format_user_info

__all__ = [
    'IntentHandler',
    'BaseIntentHandler',
    'ExerciseIntentHandler',
    'TrainingPlanIntentHandler',
    'FitnessAdviceIntentHandler',
    'GeneralChatIntentHandler',
    'DiscussTrainingPlanIntentHandler',
    'ParameterHandler',
    'process_equipment_input',
    'process_body_part_input',
    'determine_training_scenario',
    'format_user_info'
]

logger = logging.getLogger(__name__)

class IntentHandler:
    """统一的意图处理入口，负责分发意图到具体的处理器"""

    def __init__(
        self,
        db: Session,
        llm_proxy: LLMProxyService,
        agent_executor,
        sql_tool: SQLToolService,
        training_plan_service: Optional[TrainingPlanService] = None,
        conversation_model: str = None
    ):
        """初始化意图处理器

        Args:
            db: 数据库会话
            llm_proxy: LLM代理服务
            agent_executor: LangChain Agent执行器
            sql_tool: SQL工具服务
            training_plan_service: 训练计划服务
            conversation_model: 对话模型名称
        """
        self.db = db
        self.llm_proxy = llm_proxy
        self.agent_executor = agent_executor
        self.sql_tool = sql_tool
        self.training_plan_service = training_plan_service
        self.conversation_model = conversation_model

        # 初始化模型服务
        from app.services.model_service import ModelService
        self.model_service = ModelService(llm_proxy)

        # 初始化各个具体的处理器
        self.exercise_handler = ExerciseIntentHandler(db, llm_proxy, sql_tool, agent_executor, conversation_model)
        self.training_plan_handler = TrainingPlanIntentHandler(db, llm_proxy, sql_tool, training_plan_service, conversation_model)
        self.fitness_advice_handler = FitnessAdviceIntentHandler(db, llm_proxy, sql_tool, conversation_model)
        self.general_chat_handler = GeneralChatIntentHandler(db, llm_proxy, sql_tool, conversation_model)

    async def handle_intent(
        self,
        intent_data: IntentData,
        meta_info: Dict[str, Any],
        user_data: Dict[str, Any],
        history: List[Dict[str, Any]]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """根据意图类型分发到具体的处理函数

        Args:
            intent_data: 意图数据
            meta_info: 会话元数据
            user_data: 用户数据
            history: 对话历史

        Yields:
            异步生成器，生成响应内容
        """
        try:
            logger.info(f"处理意图: {intent_data.intent}, 参数: {intent_data.parameters}")
            logger.info(f"元数据: {meta_info}")
            logger.info(f"用户数据: id={user_data.get('id')}, 性别={user_data.get('gender')}, 年龄={user_data.get('age')}")
            logger.info(f"历史消息数量: {len(history)}")

            intent = intent_data.intent
            logger.info(f"开始分发意图: {intent}")

            # 根据意图类型分发
            if intent in ["search_exercise", "recommend_exercise"]:
                logger.info(f"分发到exercise_handler处理")
                async for response in self.exercise_handler.handle(intent_data, user_data, history, meta_info):
                    yield response
            elif intent in ["daily_workout_plan", "weekly_workout_plan"]:
                logger.info(f"分发到training_plan_handler处理")
                try:
                    async for response in self.training_plan_handler.handle(intent_data, user_data, history, meta_info):
                        logger.info(f"training_plan_handler返回响应: {type(response)}")
                        yield response
                except Exception as e:
                    logger.error(f"training_plan_handler处理出错: {str(e)}", exc_info=True)
                    yield {"type": "message", "content": f"抱歉，生成训练计划时出现了问题: {str(e)}", "role": "assistant"}
            elif intent in ["nutrition_advice", "diet_suggestion", "macro_calculation"]:
                logger.info(f"分发到fitness_advice_handler处理")
                async for response in self.fitness_advice_handler.handle(intent_data, user_data, history, meta_info):
                    yield response
            else:
                logger.info(f"分发到general_chat_handler处理")
                async for response in self.general_chat_handler.handle(intent_data, user_data, history, meta_info):
                    yield response
        except Exception as e:
            logger.error(f"handle_intent处理意图时出错: {str(e)}", exc_info=True)
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            yield {"type": "message", "content": f"抱歉，处理您的请求时出现了问题: {str(e)}", "role": "assistant"}
