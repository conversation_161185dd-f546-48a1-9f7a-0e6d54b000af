# app/services/conversation/intent_handler/parameter_handler.py
from typing import Dict, Any, List, Union, AsyncGenerator, Optional, Tuple
import logging
import json
import re
from sqlalchemy.orm import Session
from app.services.intent_recognizer import IntentData
from app.services.llm_proxy_service import LLMProxyService

logger = logging.getLogger(__name__)

class ParameterHandler:
    """处理参数提取和验证的工具类"""
    
    def __init__(
        self,
        db: Session,
        llm_proxy: LLMProxyService
    ):
        """初始化参数处理器
        
        Args:
            db: 数据库会话
            llm_proxy: LLM代理服务
        """
        self.db = db
        self.llm_proxy = llm_proxy
        
    async def extract_body_part(self, message: str, intent_data: IntentData) -> Union[str, List[str]]:
        """从消息中提取身体部位
        
        Args:
            message: 用户消息
            intent_data: 意图数据
            
        Returns:
            提取的身体部位，可能是字符串或字符串列表
        """
        # 首先检查意图参数中是否已有body_part
        if intent_data and hasattr(intent_data, 'parameters') and intent_data.parameters:
            body_part = intent_data.parameters.get('body_part')
            if body_part:
                logger.info(f"从意图参数中获取到body_part: {body_part}")
                return body_part
        
        # 如果意图参数中没有，尝试从消息中提取
        try:
            # 构建提示
            prompt = f"""
            请从以下用户消息中提取用户想要锻炼的身体部位：
            
            用户消息: "{message}"
            
            常见的身体部位包括：胸部、背部、肩部、手臂、腿部、腹部、臀部、核心等。
            如果用户提到多个部位，请全部提取。
            如果用户没有明确提到任何身体部位，请返回"全身"。
            
            请以JSON格式返回结果，格式为：{{"body_part": ["部位1", "部位2", ...]}}
            如果只有一个部位，也使用数组格式。
            """
            
            # 调用LLM提取
            response = await self.llm_proxy.aget_chat_response(
                messages=[
                    {"role": "system", "content": "你是一个参数提取助手，负责从用户消息中提取健身相关的参数。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1
            )
            
            # 解析响应
            try:
                # 尝试直接解析JSON
                result = json.loads(response)
                if "body_part" in result:
                    body_part = result["body_part"]
                    logger.info(f"成功从消息中提取body_part: {body_part}")
                    return body_part
            except json.JSONDecodeError:
                # 如果直接解析失败，尝试从文本中提取JSON部分
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    try:
                        result = json.loads(json_match.group(0))
                        if "body_part" in result:
                            body_part = result["body_part"]
                            logger.info(f"从文本中提取JSON后获取到body_part: {body_part}")
                            return body_part
                    except json.JSONDecodeError:
                        pass
            
            # 如果上述方法都失败，使用正则表达式直接从文本中提取
            body_parts = re.findall(r'["\']([\u4e00-\u9fa5a-zA-Z]+)["\']', response)
            if body_parts:
                logger.info(f"使用正则表达式从响应中提取到body_part: {body_parts}")
                return body_parts if len(body_parts) > 1 else body_parts[0]
            
            # 如果所有方法都失败，返回默认值
            logger.warning("无法从消息中提取body_part，使用默认值'全身'")
            return "全身"
            
        except Exception as e:
            logger.error(f"提取body_part时出错: {str(e)}")
            return "全身"  # 默认返回全身
    
    async def extract_scenario(self, message: str) -> str:
        """从消息中提取训练场景
        
        Args:
            message: 用户消息
            
        Returns:
            提取的训练场景代码，"gym"或"home"
        """
        try:
            # 构建提示
            prompt = f"""
            请从以下用户消息中提取用户想要进行训练的场景：
            
            用户消息: "{message}"
            
            场景只有两种可能：
            1. 健身房 (gym)
            2. 家里/居家 (home)
            
            如果用户明确提到在健身房、去健身房、去健身中心等，返回"gym"。
            如果用户明确提到在家、在家里、居家等，返回"home"。
            如果用户没有明确提到场景，请根据上下文推断最可能的场景。
            如果实在无法判断，默认返回"gym"。
            
            请直接返回"gym"或"home"，不要返回其他内容。
            """
            
            # 调用LLM提取
            response = await self.llm_proxy.aget_chat_response(
                messages=[
                    {"role": "system", "content": "你是一个参数提取助手，负责从用户消息中提取健身相关的参数。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1
            )
            
            # 解析响应
            response = response.strip().lower()
            if "gym" in response:
                logger.info("从消息中提取到场景: gym")
                return "gym"
            elif "home" in response:
                logger.info("从消息中提取到场景: home")
                return "home"
            else:
                # 默认返回gym
                logger.warning(f"无法从响应 '{response}' 中提取场景，使用默认值'gym'")
                return "gym"
                
        except Exception as e:
            logger.error(f"提取scenario时出错: {str(e)}")
            return "gym"  # 默认返回健身房
    
    async def extract_training_parameters(self, message: str, intent: str) -> Dict[str, Any]:
        """从消息中提取训练参数
        
        Args:
            message: 用户消息
            intent: 意图类型
            
        Returns:
            提取的训练参数字典
        """
        try:
            # 构建提示
            prompt = f"""
            请从以下用户消息中提取健身训练相关的参数：
            
            用户消息: "{message}"
            意图类型: "{intent}"
            
            请提取以下参数（如果存在）：
            1. body_part: 用户想要锻炼的身体部位，如胸部、背部、肩部、手臂、腿部等
            2. scenario: 训练场景，只能是"gym"（健身房）或"home"（居家）
            3. plan_type: 训练计划类型，只能是"daily"（单日计划）或"weekly"（周计划）
            4. equipment: 用户想要使用的器材，如哑铃、杠铃、器械等
            5. muscle: 用户想要锻炼的肌肉，如胸大肌、三角肌、二头肌等
            
            请以JSON格式返回结果，只包含能够确定的参数。
            """
            
            # 调用LLM提取
            response = await self.llm_proxy.aget_chat_response(
                messages=[
                    {"role": "system", "content": "你是一个参数提取助手，负责从用户消息中提取健身相关的参数。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1
            )
            
            # 解析响应
            try:
                # 尝试直接解析JSON
                result = json.loads(response)
                logger.info(f"成功从消息中提取训练参数: {result}")
                return result
            except json.JSONDecodeError:
                # 如果直接解析失败，尝试从文本中提取JSON部分
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    try:
                        result = json.loads(json_match.group(0))
                        logger.info(f"从文本中提取JSON后获取到训练参数: {result}")
                        return result
                    except json.JSONDecodeError:
                        pass
            
            # 如果上述方法都失败，返回空字典
            logger.warning("无法从响应中提取训练参数，返回空字典")
            return {}
            
        except Exception as e:
            logger.error(f"提取训练参数时出错: {str(e)}")
            return {}
