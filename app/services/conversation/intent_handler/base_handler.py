# app/services/conversation/intent_handler/base_handler.py
from typing import Dict, Any, List, Union, AsyncGenerator, Optional
import logging
from sqlalchemy.orm import Session
from app import models
from app.services.intent_recognizer import IntentData
from app.services.llm_proxy_service import LLMProxyService
from app.services.sql_tool_service import SQLToolService

logger = logging.getLogger(__name__)

class BaseIntentHandler:
    """意图处理器的基类，提供共用功能"""
    
    def __init__(
        self,
        db: Session,
        llm_proxy: LLMProxyService,
        sql_tool: SQLToolService,
        conversation_model: str = None
    ):
        """初始化基础处理器
        
        Args:
            db: 数据库会话
            llm_proxy: LLM代理服务
            sql_tool: SQL工具服务
            conversation_model: 对话模型名称
        """
        self.db = db
        self.llm_proxy = llm_proxy
        self.sql_tool = sql_tool
        self.conversation_model = conversation_model
        
    async def handle(
        self,
        intent_data: IntentData,
        user_data: Dict[str, Any],
        history: List[Dict[str, Any]],
        meta_info: Dict[str, Any]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """处理意图的基础方法，子类应该重写此方法
        
        Args:
            intent_data: 意图数据
            user_data: 用户数据
            history: 对话历史
            meta_info: 元数据
            
        Yields:
            响应数据
        """
        raise NotImplementedError("子类必须实现handle方法")
    
    def _format_user_info(self, user_data: Dict[str, Any]) -> str:
        """格式化用户信息为字符串
        
        Args:
            user_data: 用户数据字典
            
        Returns:
            格式化后的用户信息字符串
        """
        if not user_data:
            return "无用户信息"
            
        gender_text = "男" if user_data.get("gender") == 1 else "女" if user_data.get("gender") == 2 else "未知"
        
        # 健身目标映射
        fitness_goal_map = {
            1: "增肌",
            2: "减脂",
            3: "塑形",
            4: "增强体能",
            5: "保持健康"
        }
        fitness_goal = fitness_goal_map.get(user_data.get("fitness_goal"), "未设置")
        
        # 经验水平映射
        experience_level_map = {
            1: "初学者",
            2: "中级",
            3: "高级"
        }
        experience = experience_level_map.get(user_data.get("experience_level"), "未设置")
        
        user_info = f"用户信息: 性别={gender_text}, 年龄={user_data.get('age', '未设置')}, "
        user_info += f"身高={user_data.get('height', '未设置')}cm, 体重={user_data.get('weight', '未设置')}kg, "
        user_info += f"健身目标={fitness_goal}, 健身经验={experience}"
        
        return user_info
        
    async def _call_agent_streaming(self, input_text: str) -> AsyncGenerator[Union[str, Dict[str, Any]], None]:
        """调用Agent并以流式方式返回结果
        
        Args:
            input_text: 输入文本
            
        Yields:
            Agent的响应
        """
        try:
            # 创建回调处理器
            from langchain.callbacks.streaming_aiter import AsyncIteratorCallbackHandler
            callback = AsyncIteratorCallbackHandler()
            
            # 配置运行时参数
            from langchain.schema.runnable import RunnableConfig
            config = RunnableConfig(
                callbacks=[callback],
                run_name="Agent执行"
            )
            
            # 异步调用Agent
            import asyncio
            task = asyncio.create_task(
                self.agent_executor.ainvoke(
                    {"input": input_text},
                    config=config
                )
            )
            
            # 流式返回中间结果
            async for chunk in callback.aiter():
                yield chunk
                
            # 等待最终结果
            result = await task
            
            # 处理最终结果
            if "output" in result:
                # 检查是否包含结构化数据
                if "exercises" in result:
                    # 附加动作数据
                    yield {"type": "structured_data", "data": result["exercises"], "data_type": "exercises"}
                    
            # 检查是否有错误信息
            if "error" in result:
                logger.error(f"Agent返回错误: {result['error']}")
                yield {"type": "message", "content": f"抱歉，处理您的请求时出现了问题: {result['error']}", "role": "assistant"}
                
        except Exception as e:
            logger.error(f"调用Agent时出错: {str(e)}", exc_info=True)
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            yield {"type": "message", "content": f"抱歉，处理您的请求时出现了问题: {str(e)}", "role": "assistant"}
