# app/services/conversation/intent_handler/utils.py
from typing import Dict, Any, List, Union, Optional
import logging
import json

logger = logging.getLogger(__name__)

def process_equipment_input(equipment_input) -> List[int]:
    """处理各种格式的器材输入，转换为器材ID列表

    Args:
        equipment_input: 可以是单个器材名称(字符串)、单个器材ID(整数)或器材列表

    Returns:
        器材ID列表
    """
    from app.services.sql_tool_service import EQUIPMENT_CATEGORIES
    equipment_map = {eq["name"].lower(): eq["id"] for eq in EQUIPMENT_CATEGORIES}
    equipment_ids = []

    logger.info(f"处理器材输入: {equipment_input}, 类型: {type(equipment_input)}")

    # 处理不同类型的输入
    if isinstance(equipment_input, list):
        # 如果已经是列表，处理列表中的每个元素
        for eq in equipment_input:
            if isinstance(eq, int):
                # 直接是ID
                equipment_ids.append(eq)
            elif isinstance(eq, str):
                # 器材名称，尝试转换为ID
                eq_lower = eq.lower()
                if eq_lower in equipment_map:
                    equipment_ids.append(equipment_map[eq_lower])
                else:
                    # 尝试将字符串转换为整数ID
                    try:
                        eq_id = int(eq)
                        equipment_ids.append(eq_id)
                    except ValueError:
                        logger.warning(f"无法将器材名称 '{eq}' 映射到ID")
    elif isinstance(equipment_input, int):
        # 单个器材ID
        equipment_ids.append(equipment_input)
    elif isinstance(equipment_input, str):
        # 单个器材名称，可能是逗号分隔的多个名称
        for name in equipment_input.split(','):
            name = name.strip().lower()
            if name in equipment_map:
                equipment_ids.append(equipment_map[name])
            else:
                # 尝试将字符串转换为整数ID
                try:
                    eq_id = int(name)
                    equipment_ids.append(eq_id)
                except ValueError:
                    logger.warning(f"无法将器材名称 '{name}' 映射到ID")

    # 过滤掉重复项，但保持原有顺序
    unique_ids = []
    seen = set()
    for id in equipment_ids:
        if id not in seen:
            seen.add(id)
            unique_ids.append(id)

    logger.info(f"处理后的器材ID列表: {unique_ids}")
    return unique_ids

def process_body_part_input(body_part_input) -> List[int]:
    """处理各种格式的身体部位输入，转换为身体部位ID列表

    Args:
        body_part_input: 可以是单个身体部位名称(字符串)、单个身体部位ID(整数)或身体部位列表

    Returns:
        身体部位ID列表
    """
    from app.services.sql_tool_service import BODY_PART_CATEGORIES
    body_part_map = {bp["name"]: bp["id"] for bp in BODY_PART_CATEGORIES}
    body_part_ids = []

    logger.info(f"处理身体部位输入: {body_part_input}, 类型: {type(body_part_input)}")

    # 处理不同类型的输入
    if isinstance(body_part_input, list):
        # 如果已经是列表，处理列表中的每个元素
        for bp in body_part_input:
            if isinstance(bp, int):
                # 直接是ID
                body_part_ids.append(bp)
            elif isinstance(bp, str):
                # 身体部位名称，尝试转换为ID
                if bp in body_part_map:
                    body_part_ids.append(body_part_map[bp])
                else:
                    # 尝试将字符串转换为整数ID
                    try:
                        bp_id = int(bp)
                        body_part_ids.append(bp_id)
                    except ValueError:
                        logger.warning(f"无法将身体部位名称 '{bp}' 映射到ID")
    elif isinstance(body_part_input, int):
        # 单个身体部位ID
        body_part_ids.append(body_part_input)
    elif isinstance(body_part_input, str):
        # 单个身体部位名称，可能是逗号分隔的多个名称
        for name in body_part_input.split(','):
            name = name.strip()
            if name in body_part_map:
                body_part_ids.append(body_part_map[name])
            else:
                # 尝试将字符串转换为整数ID
                try:
                    bp_id = int(name)
                    body_part_ids.append(bp_id)
                except ValueError:
                    logger.warning(f"无法将身体部位名称 '{name}' 映射到ID")

    # 过滤掉重复项，但保持原有顺序
    unique_ids = []
    seen = set()
    for id in body_part_ids:
        if id not in seen:
            seen.add(id)
            unique_ids.append(id)

    logger.info(f"处理后的身体部位ID列表: {unique_ids}")
    return unique_ids

def determine_training_scenario(scenario_code: str) -> Dict[str, Any]:
    """根据场景代码确定训练场景和可用器材

    Args:
        scenario_code: 场景代码，如 "gym" 或 "home"

    Returns:
        包含场景信息和可用器材的字典
    """
    result = {
        "scenario": scenario_code,
        "cn_scenario": "健身房" if scenario_code == "gym" else "居家",
        "available_equipment": []
    }

    if scenario_code == "home":
        # 居家训练默认使用自重器材(ID=2)和哑铃(ID=4)
        result["available_equipment"] = [2, 4]
        logger.info(f"设置为居家训练，使用自重器材(ID=2)和哑铃(ID=4)")
    elif scenario_code == "gym":
        # 健身房场景可以使用更多器材
        result["available_equipment"] = [1, 3, 4, 6, 7]
        logger.info(f"设置为健身房训练，使用多种器材(IDs=[1,3,4,6,7])")
    else:
        # 默认使用自重器材
        result["available_equipment"] = [2]
        logger.info(f"未知场景，默认使用自重器材(ID=2)")

    return result

def format_user_info(user_data: Dict[str, Any]) -> str:
    """格式化用户信息为字符串

    Args:
        user_data: 用户数据字典，包含用户的基本信息

    Returns:
        格式化后的用户信息字符串
    """
    info_parts = []

    # 基本信息
    if user_data.get("nickname"):
        info_parts.append(f"昵称: {user_data['nickname']}")
    if user_data.get("gender") is not None:
        gender = "男" if user_data["gender"] == 1 else "女"
        info_parts.append(f"性别: {gender}")
    if user_data.get("age"):
        info_parts.append(f"年龄: {user_data['age']}岁")
    if user_data.get("height"):
        info_parts.append(f"身高: {user_data['height']}cm")
    if user_data.get("weight"):
        info_parts.append(f"体重: {user_data['weight']}kg")

    # 健身目标
    if user_data.get("fitness_goal") is not None:
        goal_map = {
            1: "减脂",
            2: "增肌",
            3: "保持健康",
            4: "增强力量",
            5: "提高耐力"
        }
        goal = goal_map.get(user_data["fitness_goal"], "未知")
        info_parts.append(f"健身目标: {goal}")

    # 经验水平
    if user_data.get("experience_level") is not None:
        level_map = {
            1: "初学者",
            2: "中级",
            3: "高级"
        }
        level = level_map.get(user_data["experience_level"], "未知")
        info_parts.append(f"经验水平: {level}")

    return ", ".join(info_parts)
