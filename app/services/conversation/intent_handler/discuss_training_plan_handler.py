# app/services/conversation/intent_handler/discuss_training_plan_handler.py
from typing import Dict, Any, List, Union, AsyncGenerator, Optional
import logging
import traceback
from sqlalchemy.orm import Session
from app import models, crud
from app.services.intent_recognizer import IntentData
from app.services.llm_proxy_service import LLMProxyService
from app.services.sql_tool_service import SQLToolService

from .base_handler import BaseIntentHandler

logger = logging.getLogger(__name__)

class DiscussTrainingPlanIntentHandler(BaseIntentHandler):
    """处理讨论训练计划相关意图的处理器"""
    
    def __init__(
        self,
        db: Session,
        llm_proxy: LLMProxyService,
        sql_tool: SQLToolService,
        conversation_model: str = None
    ):
        """初始化讨论训练计划意图处理器
        
        Args:
            db: 数据库会话
            llm_proxy: LLM代理服务
            sql_tool: SQL工具服务
            conversation_model: 对话模型名称
        """
        super().__init__(db, llm_proxy, sql_tool, conversation_model)
        
    async def handle(
        self,
        intent_data: IntentData,
        user_data: Dict[str, Any],
        history: List[Dict[str, Any]],
        meta_info: Dict[str, Any]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """处理讨论训练计划相关意图
        
        Args:
            intent_data: 意图数据
            user_data: 用户数据
            history: 对话历史
            meta_info: 元数据
            
        Yields:
            响应数据
        """
        logger.info(f"处理讨论训练计划意图: {intent_data.intent}")
        logger.info(f"意图参数: {intent_data.parameters}")
        logger.info(f"元数据: {meta_info}")
        
        try:
            # 获取用户ID
            user_id = user_data.get("id")
            if not user_id:
                logger.error("用户ID为空，无法处理讨论训练计划")
                yield {"type": "message", "content": "抱歉，无法获取您的用户信息，请重新登录后再试。", "role": "assistant"}
                return
            
            # 获取用户消息
            message = ""
            if history and len(history) > 0:
                last_message = history[-1]
                if last_message.get("role") == "user":
                    message = last_message.get("content", "")
            
            # 获取相关训练计划ID
            related_plan_id = meta_info.get("training_params", {}).get("related_plan_id")
            active_flow = meta_info.get("active_flow")
            
            if not related_plan_id:
                logger.warning("讨论训练计划意图被触发，但 meta_info 中没有 related_plan_id")
                yield {"type": "message", "content": "抱歉，我需要知道我们正在讨论哪个训练计划。您可以重新发起一个训练计划请求吗？", "role": "assistant"}
                meta_info.pop("active_flow", None)  # 清除流程状态
                yield {"type": "meta", "data": {"active_flow": None}}
                return
            
            try:
                # 1. 获取完整的训练计划详情
                plan_details = crud.crud_training_plan.get_with_workouts(db=self.db, id=related_plan_id)
                if not plan_details:
                    logger.error(f"无法获取训练计划详情: plan_id={related_plan_id} for user_id={user_id}")
                    yield {"type": "message", "content": f"抱歉，我找不到ID为 {related_plan_id} 的训练计划的详细信息。", "role": "assistant"}
                    meta_info.pop("active_flow", None)  # 清除流程状态
                    yield {"type": "meta", "data": {"active_flow": None}}
                    return
                
                # 2. 格式化训练计划摘要供LLM使用
                plan_summary_parts = [
                    f"当前讨论的训练计划: '{plan_details.get('plan_name', '未命名')}' (ID: {related_plan_id}).",
                    f"计划描述: {plan_details.get('description', '无')}",
                    f"计划周期: {plan_details.get('duration_weeks', '未知')} 周."
                ]
                workouts_summary = []
                for i, workout in enumerate(plan_details.get("workouts", [])):
                    workout_name = workout.get("name", f"训练日 {i+1}")
                    num_exercises = len(workout.get("exercises", []))
                    workouts_summary.append(f"- {workout_name}: 共 {num_exercises} 个动作.")
                
                if workouts_summary:
                    plan_summary_parts.append("训练日安排:")
                    plan_summary_parts.extend(workouts_summary)
                else:
                    plan_summary_parts.append("该计划目前没有具体的训练日安排。")
                
                plan_context_for_llm = "\n".join(plan_summary_parts)
                
                # 3. 获取用户资料文本
                from app.services.conversation.profile_helper import _get_user_profile, _get_user_profile_text
                user = crud.crud_user.get(self.db, id=user_id)
                if not user:
                    logger.error(f"找不到用户: user_id={user_id}")
                    yield {"type": "message", "content": "抱歉，无法获取您的用户信息，请重新登录后再试。", "role": "assistant"}
                    return
                    
                user_profile = _get_user_profile(None, user)
                user_profile_text = _get_user_profile_text(None, user_profile)
                
                # 4. 创建LLM提示
                llm_prompt = f"""
                您是一位专业的健身教练。
                {user_profile_text}
                
                我们当前正在讨论以下训练计划：
                {plan_context_for_llm}
                
                用户关于此计划的最新消息是: "{message}"
                
                请根据用户的消息和计划内容，提供清晰、相关的回复或操作建议。例如：
                - 如果用户提问，请回答问题。
                - 如果用户想修改计划（例如替换动作、调整组数/次数），请确认修改意图并说明如何操作（或说明您无法直接修改，建议重新生成）。
                - 如果用户表达困惑，请尝试澄清。
                - 如果用户表示满意或结束讨论，可以礼貌回应并询问是否还有其他帮助。
                
                保持对话的上下文连贯性。如果用户的问题不明确，可以适当追问。
                """
                
                formatted_messages = [
                    {"role": "system", "content": "你是一个专业的健身AI教练。"},  # 通用系统角色
                    {"role": "user", "content": llm_prompt}
                ]
                
                # 5. 流式输出LLM响应
                response_text = ""
                async for chunk in self.llm_proxy.stream_chat(
                    messages=formatted_messages,
                    model=self.conversation_model  # 或者使用专门的问答模型
                ):
                    yield chunk
                    if isinstance(chunk, dict) and "content" in chunk:
                        response_text += chunk["content"]
                    elif isinstance(chunk, str):
                        response_text += chunk
                
                # 6. 管理active_flow（目前保持不变以鼓励继续讨论）
                # 更高级：LLM可以在讨论结束时输出特殊标记或短语
                # 根据response_text决定是否清除active_flow
                # 例如，如果response_text包含"还有其他可以帮您的吗？"或类似内容，可能清除active_flow
                # 这是一个简单的启发式方法，可以改进
                if any(phrase in response_text for phrase in ["还有其他可以帮您", "还有什么可以帮您", "训练计划讨论结束"]):
                    logger.info("讨论训练计划的流程可能已结束，清除active_flow")
                    meta_info.pop("active_flow", None)
                    yield {"type": "meta", "data": {"active_flow": None}}
                else:
                    # 保持流程活跃以便进一步讨论
                    meta_info["active_flow"] = "training_plan_discussion"
                    yield {"type": "meta", "data": {"active_flow": "training_plan_discussion"}}
                
            except Exception as e:
                logger.error(f"处理讨论训练计划意图时出错: {str(e)}", exc_info=True)
                logger.error(f"错误堆栈: {traceback.format_exc()}")
                yield {"type": "message", "content": f"抱歉，讨论您的训练计划时遇到了问题: {str(e)}。", "role": "assistant"}
                meta_info.pop("active_flow", None)  # 出错时清除流程状态
                yield {"type": "meta", "data": {"active_flow": None}}
                
        except Exception as e:
            logger.error(f"处理讨论训练计划意图时出错: {str(e)}", exc_info=True)
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            yield {"type": "message", "content": f"抱歉，处理您的请求时出现了问题: {str(e)}", "role": "assistant"}
