# app/services/conversation/intent_handler/general_chat_handler.py
from __future__ import annotations
from typing import Dict, Any, List, Union, AsyncGenerator, Optional, TYPE_CHECKING
import logging
import re
import traceback
from sqlalchemy.orm import Session
from app import models, crud
from app.services.intent_recognizer import IntentData
from app.services.llm_proxy_service import LLMProxyService
from app.services.sql_tool_service import SQLToolService

from .base_handler import BaseIntentHandler

if TYPE_CHECKING:
    from app.services.conversation.orchestrator import ConversationService

logger = logging.getLogger(__name__)

async def _handle_general_chat(
    service: 'ConversationService',
    message: str,
    user: models.User,
    history_messages: List[Dict] # Pass raw history
) -> AsyncGenerator[Union[str, Dict[str, Any]], None]:
    """处理一般聊天意图"""
    from app.services.conversation.profile_helper import _get_user_profile, _get_user_profile_text
    from app import crud
    import re
    logger.info("处理一般聊天意图")

    # 安全获取用户ID，防止user对象已分离
    user_id = getattr(user, 'id', None) if user is not None else None
    logger.info(f"处理一般聊天意图: user_id={user_id}")

    if not user_id:
        logger.error("无法处理一般聊天：用户ID为空")
        yield "抱歉，处理您的请求时出现了问题，无法获取您的用户信息。"
        return

    # 获取最新的用户对象，确保连接到当前会话
    try:
        user_fresh = crud.crud_user.get(service.db, id=user_id)
        if not user_fresh:
            logger.error(f"无法获取用户信息: user_id={user_id}")
            yield "抱歉，无法获取您的用户信息，请尝试重新登录。"
            return
        # 使用新鲜的用户对象替换可能已分离的对象
        user = user_fresh
    except Exception as e:
        logger.error(f"获取用户信息时出错: {str(e)}")
        yield f"抱歉，获取您的用户信息时出现了错误: {str(e)}"
        return

    # 获取用户完整信息
    user_profile = _get_user_profile(service, user)
    user_profile_text = _get_user_profile_text(service, user_profile)

    # 构建包含用户信息的上下文
    context = f"""
{user_profile_text}

用户问题: {message}

请根据用户的个人情况和问题，提供专业、简洁、安全的健身或营养建议。
"""

    # Prepare messages for LLM
    formatted_messages = []
    formatted_messages.append({"role": "system", "content": service.system_message})

    # 过滤和处理历史消息，避免重复信息
    filtered_messages = []
    seen_contents = set()

    # 简化消息内容用于去重判断的函数
    def _simplify_message_for_dedup(content):
        if not content:
            return ""
        # 移除空白字符
        simplified = re.sub(r'\s+', ' ', content).strip()
        # 截取前100个字符作为指纹
        return simplified[:100]

    # 限制历史消息数量，只保留最近的几条
    recent_messages = history_messages[-3:] if len(history_messages) > 3 else history_messages

    for msg in recent_messages:
        role = msg.get("role")
        content = msg.get("content")

        if role in ["user", "assistant"] and content:
            # 简化消息内容用于去重判断
            simplified_content = _simplify_message_for_dedup(content)

            # 如果是重复内容，跳过
            if simplified_content in seen_contents:
                continue

            # 添加到已见内容集合
            seen_contents.add(simplified_content)

            # 检查消息是否已包含用户信息，避免重复
            if role == "user" and user_profile_text in content:
                continue

            # 添加到过滤后的消息列表
            filtered_messages.append({"role": role, "content": content})

    # 添加过滤后的历史消息
    formatted_messages.extend(filtered_messages)

    # 添加当前用户消息（包含用户信息上下文）
    formatted_messages.append({"role": "user", "content": context})

    try:
        # 使用agent-app应用进行回复
        try:
            # 首先尝试使用agent-app模型
            logger.info("尝试使用agent-app模型进行回复")
            async for chunk in service.llm_proxy.stream_chat(
                messages=formatted_messages,
                model="agent-app"  # 使用agent-app应用
            ):
                # 处理可能的字典类型响应
                if isinstance(chunk, dict):
                    # 如果是字典类型，提取text字段
                    if "text" in chunk:
                        yield chunk["text"]
                    elif "content" in chunk:
                        yield chunk["content"]
                    else:
                        # 如果没有text或content字段，转换为字符串
                        logger.warning(f"agent-app返回了未知格式的字典: {chunk}")
                        yield str(chunk)
                else:
                    # 如果是字符串类型，直接返回
                    yield chunk
        except Exception as agent_app_error:
            # 如果agent-app失败，尝试使用fitness_advice模型作为备用
            logger.warning(f"agent-app模型调用失败，切换到fitness_advice模型: {str(agent_app_error)}")
            async for chunk in service.llm_proxy.stream_chat(
                messages=formatted_messages,
                model="fitness_advice"  # 使用fitness_advice作为备用
            ):
                # 同样处理可能的字典类型响应
                if isinstance(chunk, dict):
                    # 如果是字典类型，提取text字段
                    if "text" in chunk:
                        yield chunk["text"]
                    elif "content" in chunk:
                        yield chunk["content"]
                    else:
                        # 如果没有text或content字段，转换为字符串
                        logger.warning(f"fitness_advice返回了未知格式的字典: {chunk}")
                        yield str(chunk)
                else:
                    # 如果是字符串类型，直接返回
                    yield chunk
    except Exception as e:
        logger.error(f"处理一般聊天时出错: {str(e)}", exc_info=True)
        yield f"抱歉，处理您的请求时出错: {str(e)}"


class GeneralChatIntentHandler(BaseIntentHandler):
    """处理一般聊天意图的处理器"""

    def __init__(
        self,
        db: Session,
        llm_proxy: LLMProxyService,
        sql_tool: SQLToolService,
        conversation_model: str = None
    ):
        """初始化一般聊天意图处理器

        Args:
            db: 数据库会话
            llm_proxy: LLM代理服务
            sql_tool: SQL工具服务
            conversation_model: 对话模型名称
        """
        super().__init__(db, llm_proxy, sql_tool, conversation_model)

    async def handle(
        self,
        intent_data: IntentData,
        user_data: Dict[str, Any],
        history: List[Dict[str, Any]],
        meta_info: Dict[str, Any]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """处理一般聊天意图

        Args:
            intent_data: 意图数据
            user_data: 用户数据
            history: 对话历史
            meta_info: 元数据

        Yields:
            响应数据
        """
        logger.info(f"处理一般聊天意图: {intent_data.intent}")
        logger.info(f"意图参数: {intent_data.parameters}")
        logger.info(f"元数据: {meta_info}")

        try:
            # 获取用户对象
            user_id = user_data.get("id")
            if not user_id:
                logger.error("用户ID为空，无法处理一般聊天")
                yield {"type": "message", "content": "抱歉，无法获取您的用户信息，请重新登录后再试。", "role": "assistant"}
                return

            # 获取用户对象
            user = crud.crud_user.get(self.db, id=user_id)
            if not user:
                logger.error(f"找不到用户: user_id={user_id}")
                yield {"type": "message", "content": "抱歉，无法获取您的用户信息，请重新登录后再试。", "role": "assistant"}
                return

            # 创建ConversationService对象
            from app.services.conversation.orchestrator import ConversationService
            service = ConversationService(
                db=self.db,
                llm_proxy=self.llm_proxy,
                sql_tool=self.sql_tool,
                conversation_model=self.conversation_model
            )

            # 获取用户消息
            message = ""
            if history and len(history) > 0:
                last_message = history[-1]
                if last_message.get("role") == "user":
                    message = last_message.get("content", "")

            # 调用迁移后的_handle_general_chat函数
            logger.info(f"调用_handle_general_chat处理{intent_data.intent}意图")
            async for response in _handle_general_chat(
                service=service,
                message=message,
                user=user,
                history_messages=history
            ):
                # 记录返回的响应类型
                if isinstance(response, dict):
                    logger.info(f"_handle_general_chat返回字典类型响应: {list(response.keys())}")
                else:
                    logger.info(f"_handle_general_chat返回字符串类型响应: {response[:50]}...")
                yield response

        except Exception as e:
            logger.error(f"处理一般聊天意图时出错: {str(e)}", exc_info=True)
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            yield {"type": "message", "content": f"抱歉，处理您的请求时出现了问题: {str(e)}", "role": "assistant"}
