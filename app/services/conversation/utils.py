from __future__ import annotations
import logging
from typing import TYPE_CHECKING, Dict, Any, List, Optional
from datetime import datetime
from sqlalchemy.orm import Session

if TYPE_CHECKING:
    from .orchestrator import ConversationService
    from app import crud, schemas

logger = logging.getLogger(__name__)

# 定义消息角色
class MessageRole:
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"

async def _save_ai_response(
    service: 'ConversationService',
    conversation_id: int,
    user_id: int,
    content: str,
    meta_info: Dict[str, Any],
    apply_character: bool = True
) -> None:
    """保存AI响应到数据库

    Args:
        service: ConversationService实例
        conversation_id: 会话ID
        user_id: 用户ID
        content: 消息内容
        meta_info: 元数据
    """
    try:
        # 如果需要应用角色处理
        if apply_character:
            try:
                # 获取用户设置
                from app import crud
                user_settings = crud.crud_user_setting.get_by_user_id(service.db, user_id=user_id)

                # 获取用户偏好的角色类型
                character_type = None
                if user_settings and hasattr(user_settings, 'ai_character_type'):
                    character_type = user_settings.ai_character_type
                    logger.info(f"获取到用户角色偏好: {character_type}")

                # 使用角色管理器处理响应
                character_response = await service.character_manager.process_response(
                    content,
                    character_type=character_type
                )

                # 使用处理后的响应
                if character_response:
                    logger.info(f"已应用角色处理，原始长度: {len(content)}，处理后长度: {len(character_response)}")
                    content = character_response
                else:
                    logger.warning("角色处理返回空响应，使用原始响应")
            except Exception as e:
                logger.error(f"应用角色处理时出错: {str(e)}")
                # 出错时继续使用原始响应
        from app import crud, schemas

        # 检查是否有训练计划ID，如果有，获取完整的训练计划数据
        if meta_info and "training_params" in meta_info and "related_plan_id" in meta_info["training_params"]:
            plan_id = meta_info["training_params"]["related_plan_id"]
            logger.info(f"检测到训练计划ID: {plan_id}，获取完整训练计划数据")

            try:
                # 导入训练计划服务
                from app.services.training_plan_service import TrainingPlanService
                training_plan_service = TrainingPlanService(service.db)

                # 获取完整的训练计划数据
                training_plan_data = training_plan_service.get_enhanced_plan(plan_id)

                # 确保训练计划数据中的每个动作包含所有必要字段
                if "workouts" in training_plan_data:
                    # 完整训练计划
                    for workout in training_plan_data["workouts"]:
                        if "exercises" in workout:
                            for exercise in workout["exercises"]:
                                _ensure_exercise_fields(exercise)
                    logger.info(f"已确保完整训练计划中所有动作包含必要字段")
                elif "exercises" in training_plan_data:
                    # 单日训练计划
                    for exercise in training_plan_data["exercises"]:
                        _ensure_exercise_fields(exercise)
                    logger.info(f"已确保单日训练计划中所有动作包含必要字段")

                # 记录训练计划数据大小
                exercises_count = 0
                if "workouts" in training_plan_data:
                    for workout in training_plan_data["workouts"]:
                        exercises_count += len(workout.get("exercises", []))
                elif "exercises" in training_plan_data:
                    exercises_count = len(training_plan_data["exercises"])
                logger.info(f"获取到训练计划数据，包含 {exercises_count} 个训练动作")

                # 将完整的训练计划数据添加到元数据中
                meta_info["complete_training_plan"] = training_plan_data
                logger.info(f"已将完整的训练计划数据添加到元数据中")
            except Exception as e:
                logger.error(f"获取训练计划数据时出错: {str(e)}")
                # 继续处理，不中断流程

        # 创建消息
        message = crud.crud_message.create(
            service.db,
            obj_in=schemas.MessageCreate(
                conversation_id=conversation_id,
                user_id=user_id,
                content=content,
                role="assistant",
                meta_info=meta_info
            )
        )
        logger.debug(f"AI响应已保存到数据库: id={message.id}, content={content[:30]}...")
    except Exception as e:
        logger.error(f"保存AI响应时出错: {str(e)}")
        raise

def _ensure_exercise_fields(exercise):
    """确保训练动作包含所有必要字段

    Args:
        exercise: 训练动作字典
    """
    # 确保包含必要字段
    if 'image_name' not in exercise:
        exercise['image_name'] = exercise.get('image_url', '')
    if 'gif_url' not in exercise:
        exercise['gif_url'] = ''
    if 'exercise_type' not in exercise:
        exercise['exercise_type'] = 'weight_reps'
    if 'body_part_id' not in exercise:
        # 默认设置为1（腿部）
        exercise['body_part_id'] = 1

class ConversationUtils:
    """会话工具类，提供通用的会话相关功能"""

    @staticmethod
    async def save_ai_response(
        db: Session,
        conversation_id: int,
        text: str,
        meta_info: Optional[Dict[str, Any]] = None
    ) -> int:
        """
        保存AI响应消息到数据库

        Args:
            db: 数据库会话
            conversation_id: 会话ID
            text: AI响应文本
            meta_info: 元数据

        Returns:
            创建的消息ID
        """
        try:
            message_data = {
                "conversation_id": conversation_id,
                "content": text,
                "role": MessageRole.ASSISTANT,
                "metadata": meta_info
            }

            message = await crud.crud_message.create(db, obj_in=message_data)
            return message.id
        except Exception as e:
            logger.error(f"保存AI响应失败: {str(e)}")
            return -1

    @staticmethod
    async def update_conversation_metadata(
        db: Session,
        conversation_id: int,
        meta_info: Dict[str, Any]
    ) -> bool:
        """
        更新会话元数据

        Args:
            db: 数据库会话
            conversation_id: 会话ID
            meta_info: 新的元数据

        Returns:
            是否更新成功
        """
        try:
            await crud.crud_conversation.update(
                db,
                id=conversation_id,
                obj_in={"metadata": meta_info, "last_active": datetime.now()}
            )
            return True
        except Exception as e:
            logger.error(f"更新会话元数据失败: {str(e)}")
            return False

    @staticmethod
    def process_history(
        raw_messages: List[Dict[str, Any]],
        max_messages: int = 10
    ) -> List[Dict[str, Any]]:
        """
        处理原始消息列表为LLM可用的历史格式

        Args:
            raw_messages: 原始消息列表
            max_messages: 最大消息数量

        Returns:
            处理后的消息列表
        """
        # 限制消息数量
        messages = raw_messages[-max_messages:] if raw_messages else []

        # 转换格式
        processed_history = []
        for msg in messages:
            # 处理 Message 对象
            if hasattr(msg, 'role') and msg.role in [MessageRole.USER, MessageRole.ASSISTANT, MessageRole.SYSTEM]:
                processed_history.append({
                    "content": msg.content,
                    "role": msg.role
                })
            # 处理字典对象
            elif isinstance(msg, dict) and "role" in msg and "content" in msg:
                role = msg["role"]
                # 标准化角色名称
                if role == "user" or role == "human":
                    role = MessageRole.USER
                elif role == "assistant" or role == "ai":
                    role = MessageRole.ASSISTANT
                elif role == "system":
                    role = MessageRole.SYSTEM
                else:
                    # 跳过未知角色
                    continue

                processed_history.append({
                    "content": msg["content"],
                    "role": role
                })

        return processed_history

    @staticmethod
    def prepare_conversation_context(
        user_data: Dict[str, Any],
        system_message: str
    ) -> Dict[str, Any]:
        """
        准备会话上下文

        Args:
            user_data: 用户数据
            system_message: 系统消息模板

        Returns:
            会话上下文字典
        """
        # 格式化用户信息
        user_info = []

        if user_data.get("nickname"):
            user_info.append(f"用户昵称: {user_data['nickname']}")

        if user_data.get("gender") is not None:
            gender = "男" if user_data["gender"] == 1 else "女"
            user_info.append(f"性别: {gender}")

        if user_data.get("age"):
            user_info.append(f"年龄: {user_data['age']}岁")

        if user_data.get("height") and user_data.get("weight"):
            user_info.append(f"身高: {user_data['height']}cm, 体重: {user_data['weight']}kg")

            # 计算BMI
            if user_data.get("height") > 0:
                bmi = user_data["weight"] / ((user_data["height"] / 100) ** 2)
                user_info.append(f"BMI: {bmi:.1f}")

        # 健身目标和经验
        if user_data.get("fitness_goal") is not None:
            goal_map = {
                1: "减脂",
                2: "增肌",
                3: "保持健康",
                4: "增强力量",
                5: "提高耐力"
            }
            goal = goal_map.get(user_data["fitness_goal"], "未知")
            user_info.append(f"健身目标: {goal}")

        if user_data.get("experience_level") is not None:
            level_map = {
                1: "初学者",
                2: "中级",
                3: "高级"
            }
            level = level_map.get(user_data["experience_level"], "未知")
            user_info.append(f"健身经验: {level}")

        # 格式化系统消息
        formatted_system_message = system_message
        if user_info:
            user_info_str = "\n".join(user_info)
            formatted_system_message += f"\n\n用户信息:\n{user_info_str}"

        return {
            "system_message": formatted_system_message,
            "user_info": user_info
        }