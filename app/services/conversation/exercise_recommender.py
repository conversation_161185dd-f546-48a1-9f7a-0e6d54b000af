# app/services/conversation/exercise_recommender.py
from __future__ import annotations
import logging
import json
from typing import TYPE_CHECKING, Dict, Any, List, Optional, Tuple, Set
import re
import random

# from .exercise_helper import get_candidate_exercises, personalized_filtering, _rule_based_filtering, extract_json_from_text
from .profile_helper import _get_recommended_difficulty # Assuming this helper exists

if TYPE_CHECKING:
    from .orchestrator import ConversationService
    from app.models.user import User

logger = logging.getLogger(__name__)

# --- Exercise Recommender Class ---

class ExerciseRecommender:
    """训练动作推荐器，负责动作筛选和组合推荐"""
    
    @staticmethod
    async def recommend_exercise_combination(
        service: 'ConversationService',
        body_part: str,
        scenario: str,
        user_profile: Dict[str, Any],
        training_params: Dict[str, Any],
        exercise_count: int = 5
    ) -> List[Dict[str, Any]]:
        """推荐科学有效的训练动作组合"""
        logger.info(f"为用户推荐训练动作组合: body_part={body_part}, scenario={scenario}, exercise_count={exercise_count}")
        
        difficulty_range = _get_recommended_difficulty(service, user_profile)
        
        from .exercise_helper import get_candidate_exercises, personalized_filtering
        
        candidates = await get_candidate_exercises(
            service, body_part, scenario, difficulty_range, limit=100
        )
        
        if not candidates:
            logger.warning(f"找不到匹配的训练动作，无法推荐组合")
            return []
        
        selected_exercises = await personalized_filtering(
            service, candidates, user_profile, training_params, limit=exercise_count
        )
        
        enriched_exercises = ExerciseRecommender.enrich_exercises_with_recommendations(
            selected_exercises, user_profile, training_params
        )
        
        balanced_exercises = ExerciseRecommender.ensure_training_balance(enriched_exercises, body_part)
        
        return balanced_exercises
    
    @staticmethod
    def enrich_exercises_with_recommendations(
        exercises: List[Dict[str, Any]],
        user_profile: Dict[str, Any],
        training_params: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """增强动作信息，添加针对性建议"""
        enriched = []
        experience_level = user_profile.get("experience_level", 1)
        
        for i, exercise in enumerate(exercises):
            enriched_exercise = exercise.copy()
            enriched_exercise["order"] = i + 1
            
            if not enriched_exercise.get("tips"):
                if experience_level == 1: enriched_exercise["tips"] = "专注于正确的动作姿势，控制重量，避免过度用力"
                elif experience_level == 2: enriched_exercise["tips"] = "注意肌肉收缩感受，可以尝试控制离心阶段速度"
                else: enriched_exercise["tips"] = "可以尝试超级组、递减组或增加动作难度变式"
            
            if enriched_exercise.get("level", 2) > experience_level:
                enriched_exercise["notes"] = f"{enriched_exercise.get('notes', '')} 这个动作对您来说有些挑战，建议先减轻重量掌握技术动作。".strip()
            
            enriched.append(enriched_exercise)
        
        return enriched
    
    @staticmethod
    def ensure_training_balance(exercises: List[Dict[str, Any]], body_part: str) -> List[Dict[str, Any]]:
        """确保训练动作组合的平衡性和科学性"""
        if len(exercises) <= 2: return exercises
        
        compound_keywords = ["深蹲", "硬拉", "卧推", "引体向上", "划船", "推举", "杠铃", "squat", "deadlift", "bench", "row", "press"]
        
        def is_compound(exercise):
            name = exercise.get("name", "").lower()
            description = exercise.get("description", "").lower()
            return any(keyword.lower() in name or keyword.lower() in description for keyword in compound_keywords)
        
        compound_exercises = [e for e in exercises if is_compound(e)]
        isolation_exercises = [e for e in exercises if not is_compound(e)]
        balanced = compound_exercises + isolation_exercises
        
        for i, exercise in enumerate(balanced):
            exercise["order"] = i + 1
        
        return balanced
    
    @staticmethod
    def get_exercise_by_id(service: 'ConversationService', exercise_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取训练动作详情"""
        try:
            tool = service.sql_tool.get_exercise_by_id_tool()
            exercise = tool.func(exercise_id=exercise_id)
            return exercise[0] if exercise else None
        except Exception as e:
            logger.error(f"通过ID获取训练动作失败: {str(e)}")
            return None
    
    @staticmethod
    def get_training_progression(
        service: 'ConversationService',
        exercise_id: int, 
        user_profile: Dict[str, Any]
    ) -> Dict[str, Any]:
        """获取训练动作的进阶建议"""
        exercise = ExerciseRecommender.get_exercise_by_id(service, exercise_id)
        if not exercise:
            return {"success": False, "message": "未找到该训练动作"}
        
        experience_level = user_profile.get("experience_level", 1)
        current_level = exercise.get("level", 2)
        
        progression = {
            "success": True,
            "exercise_name": exercise["name"],
            "current_level": current_level,
            "recommendations": []
        }
        
        if experience_level == 1:
            progression["recommendations"] = [
                "专注于正确的动作姿势和技术",
                "逐步增加重量，但优先保证姿势正确",
                "控制好动作速度，感受目标肌肉的收缩"
            ]
        elif experience_level == 2:
            progression["recommendations"] = [
                "尝试调整训练变量，如增加组数或缩短休息时间",
                "可以使用超级组方式增加训练密度",
                "考虑2-1技术或递减组等高级技巧"
            ]
        else:
            progression["recommendations"] = [
                "尝试更高级的动作变式",
                "引入预疲劳技术或爆发式训练",
                "可以考虑周期化训练或负荷区分训练"
            ]
        
        if "杠铃" in exercise["name"] or "哑铃" in exercise["name"]:
            progression["recommendations"].append("可以尝试相同动作的不同器械变式")
        
        if current_level < experience_level:
            progression["recommendations"].append("这个动作对您来说相对较简单，可以考虑增加难度变式")
        elif current_level > experience_level:
            progression["recommendations"].append("这个动作对您来说有挑战性，建议先掌握基础版本")
        
        return progression
    
    @staticmethod
    def filter_exercises_by_params(
        all_exercises: List[Dict[str, Any]],
        training_params: Dict[str, Any],
        max_count: int = 6
    ) -> List[Dict[str, Any]]:
        """根据训练参数筛选合适的训练动作"""
        from .exercise_helper import extract_json_from_text
        
        if not all_exercises: return []
        
        target_body_parts = training_params.get("body_parts", [])
        scenario = training_params.get("scenario", "家庭")
        experience_level = training_params.get("experience_level", 1)
        
        filtered_exercises = []
        for exercise in all_exercises:
            if scenario == "家庭" and exercise.get("equipment_required", False) and not exercise.get("at_home", False):
                continue
            
            exercise_level = exercise.get("difficulty_level", 1)
            if abs(exercise_level - experience_level) > 1:
                continue
                
            filtered_exercises.append(exercise)
        
        if target_body_parts:
            body_part_exercises = []
            for exercise in filtered_exercises:
                exercise_body_parts = exercise.get("body_parts", [])
                if "全身" in exercise_body_parts or "全身" in target_body_parts or any(bp in exercise_body_parts for bp in target_body_parts):
                    body_part_exercises.append(exercise)
            filtered_exercises = body_part_exercises
        
        if len(filtered_exercises) > max_count:
            random.shuffle(filtered_exercises)
            filtered_exercises = filtered_exercises[:max_count]
        
        return filtered_exercises 