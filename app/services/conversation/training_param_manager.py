# app/services/conversation/training_param_manager.py
"""训练参数管理模块，处理训练相关参数的提取、收集和验证"""

from __future__ import annotations
import logging
from typing import TYPE_CHECKING, Dict, Any, List, Optional, Tuple, Set
from app.core.param_definitions import (
    PARAM_COLLECTION_ORDER, PARAM_NAME_MAP, PARAM_ENUM_MAP, BODY_PART_CATEGORIES,
    get_param_collection_order, get_param_question, get_param_confirmation,
    get_required_params_for_intent
)
from app.services.conversation.parameter_extractor import ParameterExtractor

if TYPE_CHECKING:
    from app.services.conversation.orchestrator import ConversationService

logger = logging.getLogger(__name__)

class TrainingParamManager:
    """训练参数管理器，用于处理训练相关参数的收集和验证"""

    def __init__(self, conversation_service: 'ConversationService'):
        """初始化训练参数管理器

        Args:
            conversation_service: 会话服务实例
        """
        self.service = conversation_service
        self.llm_proxy = conversation_service.llm_proxy

    @staticmethod
    def check_required_training_params(params: Dict[str, Any], intent: str = None) -> List[str]:
        """检查训练参数中缺失的必要参数

        Args:
            params: 训练参数字典
            intent: 意图类型，用于确定需要检查的参数

        Returns:
            缺失的参数列表
        """
        missing_params = []

        # 根据意图获取参数收集顺序
        param_collection_order = get_param_collection_order(intent)
        logger.info(f"意图 {intent} 的参数收集顺序: {param_collection_order}")
        
        # 特殊处理recommend_exercise意图，确保不检查plan_type参数
        if intent == "recommend_exercise":
            logger.info("recommend_exercise意图不需要plan_type参数")
            param_collection_order = [p for p in param_collection_order if p["param"] != "plan_type"]

        for param_info in param_collection_order:
            param = param_info["param"]
            required = param_info["required"]

            # 检查参数是否缺失
            is_missing = False

            if param not in params:
                is_missing = True
            elif params[param] is None:
                is_missing = True
            elif params[param] == "":
                is_missing = True
            elif isinstance(params[param], list) and len(params[param]) == 0:
                is_missing = True

            if required and is_missing:
                missing_params.append(param)
                logger.info(f"缺失必要参数: {param}")

        return missing_params

    @staticmethod
    def get_training_prompt_message(params: Dict[str, Any], intent: str = None) -> str:
        """根据当前已收集的参数生成下一个参数的提示消息

        Args:
            params: 已收集的训练参数
            intent: 意图类型，用于确定参数收集顺序

        Returns:
            提示消息
        """
        # 确定下一个要询问的参数
        missing_params = TrainingParamManager.check_required_training_params(params, intent)
        if not missing_params:
            return "所有必要的训练参数已收集完成。"

        next_param = missing_params[0]
        logger.info(f"下一个要询问的参数: {next_param}, 意图: {intent}")

        # 根据参数类型生成提示
        if next_param == "body_part":
            return get_param_question("body_part")
        elif next_param == "scenario":
            return get_param_question("scenario")
        elif next_param == "plan_type":
            body_part = params.get("body_part")
            # 处理body_part可能是列表的情况
            if isinstance(body_part, list) and len(body_part) > 0:
                body_part = body_part[0]  # 使用第一个元素
            body_part_text = f"的{body_part}" if body_part else ""
            return f"您需要的是单日训练计划还是周期训练计划{body_part_text}？"
        elif next_param == "training_goal":
            body_part = params.get("body_part")
            # 处理body_part可能是列表的情况
            if isinstance(body_part, list) and len(body_part) > 0:
                body_part = body_part[0]  # 使用第一个元素
            body_part_text = f"{body_part}训练" if body_part else "训练"
            return f"您{body_part_text}的目标是什么？增肌、减脂、力量、耐力还是塑形？"
        elif next_param == "difficulty":
            return get_param_question("difficulty")
        elif next_param == "exercise_name":
            return get_param_question("exercise_name")

        # 使用默认提示模板
        return get_param_question(next_param)

    def get_param_question(self, param: str) -> str:
        """获取参数问题提示

        Args:
            param: 参数名称

        Returns:
            问题提示
        """
        return get_param_question(param)

    def get_param_confirmation(self, param: str, value: Any) -> str:
        """获取参数确认提示

        Args:
            param: 参数名称
            value: 参数值

        Returns:
            确认提示
        """
        return get_param_confirmation(param, value)

    async def extract_training_parameters(self, message: str, existing_params: Dict[str, Any] = None, intent: str = None) -> Dict[str, Any]:
        """提取训练相关参数

        Args:
            message: 用户消息
            existing_params: 已有的参数字典，用于更新
            intent: 意图类型，用于确定需要提取的参数

        Returns:
            提取的训练参数字典
        """
        # 初始化结果字典
        result = existing_params.copy() if existing_params else {}
        
        # 提取训练参数
        parameters = await ParameterExtractor.extract_training_parameters(self.service, message, intent)
        logger.info(f"提取到的训练参数: {parameters}")

        # 合并参数，优先使用新提取的非空参数
        for key, value in parameters.items():
            if value is not None and (not isinstance(value, list) or len(value) > 0):
                result[key] = value
        
        # 确保recommend_exercise意图不包含plan_type参数
        if intent == "recommend_exercise" and "plan_type" in result:
            logger.info("移除recommend_exercise意图的plan_type参数")
            result.pop("plan_type")

        # 规范化body_part格式 - 确保始终为列表
        if "body_part" in result and not isinstance(result["body_part"], list):
            result["body_part"] = [result["body_part"]]

        # 如果提取到body_parts而不是body_part，进行转换
        if "body_parts" in result and "body_part" not in result:
            result["body_part"] = result.pop("body_parts")

        # 对于特定参数，尝试标准化
        if "scenario" in result:
            scenario = result["scenario"]
            if scenario == "居家" or scenario == "家庭" or scenario == "home":
                result["scenario"] = "home"
            elif scenario == "健身房" or scenario == "gym":
                result["scenario"] = "gym"
        
        if "plan_type" in result:
            plan_type = result["plan_type"]
            if plan_type == "单日" or plan_type == "daily" or plan_type == "单日计划":
                result["plan_type"] = "daily"
            elif plan_type == "周期" or plan_type == "weekly" or plan_type == "周期计划":
                result["plan_type"] = "weekly"
        
        # 验证和规范化身体部位
        if "body_part" in result and result["body_part"]:
            valid_body_parts = []
            for bp in result["body_part"]:
                is_valid, normalized_bp = await self.validate_and_normalize_body_part(bp)
                if is_valid and normalized_bp:
                    valid_body_parts.append(normalized_bp)
            
            if valid_body_parts:
                result["body_part"] = valid_body_parts
        
        logger.info(f"最终训练参数: {result}")
        return result

    def format_training_params(self, params: Dict[str, Any], intent: str = None) -> str:
        """格式化训练参数，生成可读的参数描述

        Args:
            params: 训练参数字典
            intent: 意图类型

        Returns:
            格式化后的参数字符串
        """
        if not params:
            return "未找到训练参数。"
        
        # 格式化body_part
        body_part_text = ""
        if "body_part" in params and params["body_part"]:
            bp = params["body_part"]
            if isinstance(bp, list):
                if len(bp) == 1:
                    body_part_text = f"训练部位：{bp[0]}\n"
                else:
                    body_part_text = f"训练部位：{'、'.join(bp)}\n"
            else:
                body_part_text = f"训练部位：{bp}\n"
        
        # 格式化scenario
        scenario_text = ""
        if "scenario" in params and params["scenario"]:
            scenario = params["scenario"]
            if scenario == "home":
                scenario_text = "训练场景：居家训练\n"
            elif scenario == "gym":
                scenario_text = "训练场景：健身房训练\n"
            else:
                scenario_text = f"训练场景：{scenario}训练\n"
        
        # 格式化plan_type
        plan_type_text = ""
        if "plan_type" in params and params["plan_type"] and intent != "recommend_exercise":
            plan_type = params["plan_type"]
            if plan_type == "daily":
                plan_type_text = "计划类型：单日训练计划\n"
            elif plan_type == "weekly":
                plan_type_text = "计划类型：周期训练计划\n"
            else:
                plan_type_text = f"计划类型：{plan_type}\n"
        
        # 格式化training_goal
        goal_text = ""
        if "training_goal" in params and params["training_goal"]:
            goal_text = f"训练目标：{params['training_goal']}\n"
        
        # 格式化difficulty
        difficulty_text = ""
        if "difficulty" in params and params["difficulty"]:
            difficulty = params["difficulty"]
            if isinstance(difficulty, int) and 1 <= difficulty <= 5:
                difficulty_map = {1: "初级", 2: "初中级", 3: "中级", 4: "中高级", 5: "高级"}
                difficulty_text = f"训练难度：{difficulty_map.get(difficulty, str(difficulty))}\n"
            else:
                difficulty_text = f"训练难度：{difficulty}\n"
        
        # 组合所有参数
        result = f"{body_part_text}{scenario_text}{plan_type_text}{goal_text}{difficulty_text}".strip()
        
        if not result:
            return "未找到有效的训练参数。"
        
        return result

    async def validate_and_normalize_body_part(self, body_part: str) -> Tuple[bool, Optional[str]]:
        """验证并标准化身体部位名称

        Args:
            body_part: 要验证的身体部位名称

        Returns:
            是否有效的布尔值和标准化后的身体部位名称
        """
        if not body_part:
            return False, None

        # 常见的身体部位变体映射
        body_part_map = {
            "胸": "胸部", "chest": "胸部", "pecs": "胸部",
            "背": "背部", "back": "背部", "lats": "背部",
            "肩": "肩部", "肩膀": "肩部", "shoulders": "肩部", "delts": "肩部",
            "手臂": "手臂", "手部": "手臂", "上肢": "手臂", "arms": "手臂", 
            "二头": "二头肌", "biceps": "二头肌",
            "三头": "三头肌", "triceps": "三头肌",
            "腿": "腿部", "腿部": "腿部", "下肢": "腿部", "legs": "腿部",
            "大腿前侧": "股四头肌", "quadriceps": "股四头肌", "quads": "股四头肌",
            "大腿后侧": "腘绳肌", "hamstrings": "腘绳肌", "hams": "腘绳肌",
            "臀": "臀部", "臀部": "臀部", "glutes": "臀部",
            "小腿": "小腿", "calves": "小腿",
            "腹部": "腰腹部", "腹肌": "腰腹部", "核心": "腰腹部", "腰部": "腰腹部", "abs": "腰腹部", "core": "腰腹部"
        }
        
        # 标准化身体部位名称
        normalized_body_part = body_part_map.get(body_part.lower(), body_part)
        
        # 验证是否在预定义的身体部位分类中
        for category in BODY_PART_CATEGORIES:
            if category["name"] == normalized_body_part:
                return True, normalized_body_part

        # 如果不在预定义分类中，返回原始名称但标记为未验证
        return True, normalized_body_part