"""
中断处理器 - 负责检测和处理对话中断，维护对话连贯性
"""
from typing import Dict, Any, Optional, AsyncGenerator
import logging
from datetime import datetime
from app.models.message import Message
from app.services.llm_proxy_service import LLMProxyService
from app.core.config import settings
from app.utils.datetime_utils import get_utc_now, ensure_timezone, calculate_time_diff_seconds

logger = logging.getLogger(__name__)

class InterruptionHandler:
    """中断处理器，负责检测和处理对话中断，维护对话连贯性"""

    def __init__(self, llm_proxy_service: LLMProxyService):
        """初始化中断处理器

        Args:
            llm_proxy_service: LLM代理服务，用于判断消息相关性
        """
        self.llm_proxy = llm_proxy_service
        # 中断检测配置
        self.TIME_THRESHOLD = 60  # 60秒，超过此时间视为可能中断
        self.RELEVANCE_THRESHOLD = 0.6  # 相关性阈值，低于此值视为话题转换

    async def check_interruption(self,
                                message: str,
                                last_assistant_message: Optional[Message],
                                meta_info: Dict[str, Any]) -> bool:
        """检查是否发生中断

        Args:
            message: 用户消息
            last_assistant_message: 上一条AI消息
            meta_info: 元数据

        Returns:
            是否发生中断
        """
        # 如果没有上一条消息，不视为中断
        if not last_assistant_message:
            return False

        # 检查是否处于特殊状态
        is_in_special_state = (
            meta_info.get("waiting_for_info") or
            meta_info.get("collecting_training_params") or
            meta_info.get("active_flow")
        )

        if not is_in_special_state:
            # 不在特殊状态中，不需要检测中断
            return False

        # 计算时间差
        try:
            # 获取上一条消息的时间
            last_message_time = last_assistant_message.created_at

            if not last_message_time:
                return False

            # 使用工具函数获取当前时间（带时区）
            now = get_utc_now()

            # 确保消息时间有时区信息
            last_message_time = ensure_timezone(last_message_time)

            # 使用工具函数计算时间差（秒）
            time_diff = calculate_time_diff_seconds(now, last_message_time)

            if time_diff is None:
                logger.error("计算时间差失败，返回None")
                return False

            logger.debug(f"时间差计算: now={now}, last_message_time={last_message_time}, diff={time_diff}秒")
        except Exception as e:
            logger.error(f"计算时间差时出错: {str(e)}")
            return False

        # 如果时间差小于阈值，不视为中断
        if time_diff < self.TIME_THRESHOLD:
            return False

        # 检查消息相关性
        is_relevant = await self._check_message_relevance(message, last_assistant_message)

        # 如果消息相关，不视为中断
        if is_relevant:
            return False

        # 时间差大且消息不相关，视为中断
        logger.info(f"检测到对话中断: 时间差={time_diff}秒, 消息相关性=False")
        return True

    async def _check_message_relevance(self, message: str, last_assistant_message: Message) -> bool:
        """检查用户新消息与之前流程的相关性

        Args:
            message: 用户新消息
            last_assistant_message: 上一条AI助手消息

        Returns:
            消息是否相关
        """
        try:
            # 获取上一条AI消息的内容
            last_content = last_assistant_message.content

            # 构建提示
            prompt = f"""
            请分析用户的新消息是否与上一个对话上下文相关。

            上一条AI助手消息:
            "{last_content}"

            用户新消息:
            "{message}"

            这条新消息是否与上一个对话上下文相关？请只回答"相关"或"不相关"。
            """

            # 调用LLM判断相关性
            response = await self.llm_proxy.aget_chat_response(
                messages=[
                    {"role": "system", "content": "你是一个对话分析助手，负责判断用户消息是否与当前对话上下文相关。"},
                    {"role": "user", "content": prompt}
                ],
                model=settings.LLM_INTENT_RECOGNITION_MODEL,
                temperature=0.1
            )

            # 解析响应
            is_relevant = "相关" in response.lower()
            logger.info(f"消息相关性检查结果: {'相关' if is_relevant else '不相关'}")

            return is_relevant
        except Exception as e:
            logger.error(f"检查消息相关性时出错: {str(e)}")
            # 出错时默认为相关，避免错误中断
            return True

    async def analyze_continuation_response(self, message: str) -> bool:
        """分析用户对中断确认的回复，判断是继续原流程还是处理新问题

        Args:
            message: 用户回复消息

        Returns:
            是否继续原流程
        """
        try:
            # 构建提示
            prompt = f"""
            请分析用户的回复，判断用户是想继续之前的对话流程，还是处理新的问题。

            用户回复:
            "{message}"

            用户是想继续之前的对话流程，还是处理新的问题？请只回答"继续之前的流程"或"处理新问题"。
            """

            # 调用LLM判断用户意图
            response = await self.llm_proxy.aget_chat_response(
                messages=[
                    {"role": "system", "content": "你是一个对话分析助手，负责判断用户意图。"},
                    {"role": "user", "content": prompt}
                ],
                model=settings.LLM_INTENT_RECOGNITION_MODEL,
                temperature=0.1
            )

            # 解析响应
            continue_previous = "继续" in response.lower() or "之前" in response.lower()
            logger.info(f"用户选择: {'继续之前的流程' if continue_previous else '处理新问题'}")

            return continue_previous
        except Exception as e:
            logger.error(f"分析用户回复时出错: {str(e)}")
            # 出错时默认处理新问题
            return False

    async def handle_interruption(self,
                                 message: str,
                                 meta_info: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """处理中断，询问用户是继续原流程还是处理新问题

        Args:
            message: 用户消息
            meta_info: 元数据

        Returns:
            异步生成器，生成响应内容
        """
        # 保存当前状态
        response_meta_info = meta_info.copy()
        response_meta_info["confirming_continuation"] = True
        response_meta_info["pending_new_message"] = message

        # 生成询问消息
        confirmation_message = "您好，我们之前的对话似乎被中断了。您想继续之前的对话，还是处理新的问题？"

        # 返回询问消息
        yield {"type": "message", "content": confirmation_message, "role": "assistant"}

        # 同时发送元数据更新事件
        yield {"type": "meta_info_update", "meta_info_update": response_meta_info}
