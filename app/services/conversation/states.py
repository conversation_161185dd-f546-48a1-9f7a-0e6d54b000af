from abc import ABC, abstractmethod
import logging
from typing import AsyncGenerator, Dict, Any, Tuple, Optional, List

from sqlalchemy.orm import Session

from app.services.conversation.user_profile_manager import UserProfileManager
from app.services.conversation.training_param_manager import TrainingParamManager
from app.services.conversation.parameter_extractor import ParameterExtractor
from app.services.intent_recognizer import IntentData

logger = logging.getLogger(__name__)

class ConversationState(ABC):
    """基础会话状态抽象类，定义所有状态需要实现的接口"""

    @abstractmethod
    async def handle_message(self, service, message: str, meta_info: Dict[str, Any], user_id: int = None, conversation_id: int = None) -> AsyncGenerator[Dict[str, Any], None]:
        """处理用户消息并生成响应

        Args:
            service: 对话服务实例
            message: 用户消息
            meta_info: 元数据
            user_id: 用户ID
            conversation_id: 会话ID
        """
        pass

class NormalConversationState(ConversationState):
    """正常对话状态：处理意图识别和执行"""

    async def handle_message(self, service, message: str, meta_info: Dict[str, Any], user_id: int = None, conversation_id: int = None) -> AsyncGenerator[Dict[str, Any], None]:
        """处理正常流程的消息

        Args:
            service: 对话服务实例
            message: 用户消息
            meta_info: 元数据
            user_id: 用户ID
            conversation_id: 会话ID
        """
        # 更新会话活动时间
        from app import crud
        crud.crud_conversation.update(service.db, db_obj=crud.crud_conversation.get(service.db, id=service.conversation_id), obj_in={"last_active_at": "now()"})

        # 意图识别
        intent_data = None
        if service.quick_intent:
            intent_data = service.quick_intent
        else:
            intent_data = await service.intent_recognizer.recognize_intent(message)

        # 更新元数据中的意图信息
        response_meta_info = meta_info.copy()
        response_meta_info.update({
            "intent": intent_data.intent,
            "intent_confidence": intent_data.confidence,
            "intent_parameters": intent_data.parameters
        })

        # 发送意图识别结果 - 使用meta_info_update事件
        yield {"type": "meta_info_update", "meta_info_update": response_meta_info}

        # 训练相关意图的特殊处理
        if intent_data.intent in ["search_exercise", "recommend_exercise", "daily_workout_plan", "weekly_workout_plan"]:
            # 提取训练参数，传递意图信息
            training_params = await service.training_param_manager.extract_training_parameters(message, intent=intent_data.intent)
            if training_params:
                response_meta_info["training_params"] = training_params

            # 检查必要参数是否齐全
            # 将intent添加到training_params中
            params_with_intent = training_params.copy() if training_params else {}
            params_with_intent["intent"] = intent_data.intent
            missing_params = service.training_param_manager.check_required_training_params(params_with_intent)

            if missing_params and len(missing_params) > 0:
                # 获取第一个缺失的参数
                missing_param = missing_params[0]

                # 进入训练参数收集状态
                response_meta_info["collecting_training_params"] = True
                response_meta_info["asking_param"] = missing_param

                # 生成问询消息
                question = service.training_param_manager.get_param_question(missing_param)
                # 将元数据包含在消息响应中
                yield {"type": "message", "content": question, "role": "assistant", "meta_info": response_meta_info}

                # 同时发送元数据更新事件，确保前端能够接收到状态更新
                yield {"type": "meta_info_update", "meta_info_update": response_meta_info}
                return

            # 参数齐全，更新意图数据
            # 只有当原始意图不是recommend_exercise时才根据plan_type更改意图
            if "plan_type" in training_params and intent_data.intent != "recommend_exercise":
                if training_params["plan_type"] == "daily":
                    intent_data.intent = "daily_workout_plan"
                elif training_params["plan_type"] == "weekly":
                    intent_data.intent = "weekly_workout_plan"

            # 更新意图参数
            for key, value in training_params.items():
                intent_data.parameters[key] = value

        # 用户信息完整性检查
        if intent_data.intent in ["create_training_plan", "nutrition_advice", "recommend_exercise",
                                 "daily_workout_plan", "weekly_workout_plan", "fitness_progress"]:
            # 获取用户数据
            from app import crud
            user = crud.crud_user.get(service.db, id=service.user_id)
            user_data = user.__dict__ if user else {}
            missing_fields = service.user_profile_manager.get_missing_fields(user_data)

            if missing_fields:
                # 将当前请求暂存
                response_meta_info["pending_request"] = {
                    "message": message,
                    "intent_data": intent_data.dict(),
                    "meta_info": response_meta_info.copy()
                }

                # 进入引导模式
                response_meta_info["conversation_state"] = "guided"

                # 生成信息收集提示
                yield {"type": "message", "content": service.user_profile_manager.get_info_collection_intro(), "role": "assistant"}

                # 设置等待信息字段
                first_field = missing_fields[0]
                response_meta_info["waiting_for_info"] = {"field": first_field, "attempt": 0}

                # 生成第一个问题
                question = service.user_profile_manager.get_field_question(first_field)
                # 将元数据包含在消息响应中
                yield {"type": "message", "content": question, "role": "assistant", "meta_info": response_meta_info}
                # 同时发送元数据更新事件
                yield {"type": "meta_info_update", "meta_info_update": response_meta_info}
                return

        # 调用意图处理函数
        async for response in service._handle_intent_execution(intent_data, response_meta_info):
            yield response


class UserProfileCollectionState(ConversationState):
    """用户信息收集状态：处理用户资料字段收集"""

    async def handle_message(self, service, message: str, meta_info: Dict[str, Any], user_id: int = None, conversation_id: int = None) -> AsyncGenerator[Dict[str, Any], None]:
        """处理用户信息收集状态的消息

        Args:
            service: 对话服务实例
            message: 用户消息
            meta_info: 元数据
            user_id: 用户ID
            conversation_id: 会话ID
        """
        response_meta_info = meta_info.copy()
        waiting_info = meta_info.get("waiting_for_info", {})
        field = waiting_info.get("field")
        attempt = waiting_info.get("attempt", 0)

        if not field:
            # 如果没有待收集字段，回到正常状态
            response_meta_info["waiting_for_info"] = None
            yield {"type": "meta", "data": response_meta_info}
            return

        # 验证用户输入
        validation_result = await service.user_profile_manager.validate_and_parse(field, message)

        if validation_result["valid"]:
            # 输入有效，更新用户信息
            value = validation_result["value"]
            from app import crud
            user = crud.crud_user.get(service.db, id=service.user_id)
            crud.crud_user.update(service.db, db_obj=user, obj_in={field: value})

            # 确认收到
            yield {"type": "message", "content": f"谢谢！已更新您的{service.user_profile_manager.get_field_name(field)}。", "role": "assistant"}

            # 清除等待状态
            response_meta_info["waiting_for_info"] = None

            # 检查是否还有其他缺失字段
            from app import crud
            user = crud.crud_user.get(service.db, id=service.user_id)
            user_data = user.__dict__ if user else {}
            missing_fields = service.user_profile_manager.get_missing_fields(user_data)
            skipped_fields = response_meta_info.get("skipped_fields", [])

            # 过滤已跳过的字段
            missing_fields = [f for f in missing_fields if f not in skipped_fields]

            if missing_fields and response_meta_info.get("conversation_state") == "guided":
                # 还有缺失字段，询问下一个
                next_field = missing_fields[0]
                response_meta_info["waiting_for_info"] = {"field": next_field, "attempt": 0}

                question = service.user_profile_manager.get_field_question(next_field)
                yield {"type": "message", "content": question, "role": "assistant"}
            else:
                # 所有必要信息已收集完毕
                if response_meta_info.get("conversation_state") == "guided":
                    response_meta_info["conversation_state"] = "normal"

                # 是否有待处理的请求
                pending_request = response_meta_info.get("pending_request")
                if pending_request:
                    # 恢复之前的请求
                    yield {"type": "message", "content": "谢谢您提供的信息，现在我可以回答您之前的问题了。", "role": "assistant"}

                    # 清除待处理请求
                    original_intent = IntentData(**pending_request["intent_data"])
                    response_meta_info["pending_request"] = None

                    # 执行原始意图
                    async for response in service._handle_intent_execution(original_intent, response_meta_info):
                        yield response
                else:
                    # 没有待处理请求，确认收集完成
                    yield {"type": "message", "content": "谢谢您提供的信息，现在我们可以继续了。有什么我可以帮您的吗？", "role": "assistant"}
        else:
            # 输入无效，增加尝试次数
            attempt += 1
            max_attempts = service.user_profile_manager.MAX_ATTEMPTS

            if attempt < max_attempts:
                # 重试
                response_meta_info["waiting_for_info"] = {"field": field, "attempt": attempt}

                # 生成重试提示
                retry_message = validation_result["message"]
                yield {"type": "message", "content": retry_message, "role": "assistant"}
            else:
                # 超过最大尝试次数，跳过该字段
                skipped_fields = response_meta_info.get("skipped_fields", [])
                skipped_fields.append(field)
                response_meta_info["skipped_fields"] = skipped_fields
                response_meta_info["waiting_for_info"] = None

                # 提示跳过
                yield {"type": "message", "content": f"让我们先跳过{service.user_profile_manager.get_field_name(field)}这个问题。", "role": "assistant"}

                # 检查是否还有其他缺失字段
                from app import crud
                user = crud.crud_user.get(service.db, id=service.user_id)
                user_data = user.__dict__ if user else {}
                missing_fields = service.user_profile_manager.get_missing_fields(user_data)
                missing_fields = [f for f in missing_fields if f not in skipped_fields]

                if missing_fields and response_meta_info.get("conversation_state") == "guided":
                    # 还有缺失字段，询问下一个
                    next_field = missing_fields[0]
                    response_meta_info["waiting_for_info"] = {"field": next_field, "attempt": 0}

                    question = service.user_profile_manager.get_field_question(next_field)
                    yield {"type": "message", "content": question, "role": "assistant"}
                else:
                    # 所有可能的字段已处理完毕
                    if response_meta_info.get("conversation_state") == "guided":
                        response_meta_info["conversation_state"] = "normal"

                    # 是否有待处理的请求
                    pending_request = response_meta_info.get("pending_request")
                    if pending_request:
                        # 恢复之前的请求
                        yield {"type": "message", "content": "我们继续回到您之前的问题。", "role": "assistant"}

                        # 清除待处理请求
                        original_intent = IntentData(**pending_request["intent_data"])
                        response_meta_info["pending_request"] = None

                        # 执行原始意图
                        async for response in service._handle_intent_execution(original_intent, response_meta_info):
                            yield response
                    else:
                        # 没有待处理请求，确认收集完成
                        yield {"type": "message", "content": "感谢您提供的信息，有什么我可以帮您的吗？", "role": "assistant"}

        # 更新元数据 - 使用meta_info_update事件
        yield {"type": "meta_info_update", "meta_info_update": response_meta_info}


class TrainingParamCollectionState(ConversationState):
    """训练参数收集状态：处理训练相关参数收集"""

    async def handle_message(self, service, message: str, meta_info: Dict[str, Any], user_id: int = None, conversation_id: int = None) -> AsyncGenerator[Dict[str, Any], None]:
        """处理训练参数收集状态的消息

        Args:
            service: 对话服务实例
            message: 用户消息
            meta_info: 元数据
            user_id: 用户ID
            conversation_id: 会话ID
        """
        # 深度复制元数据，避免修改原始数据
        response_meta_info = meta_info.copy()
        asking_param = meta_info.get("asking_param")
        
        # 保存原始意图，避免在参数收集过程中被改变
        original_intent = meta_info.get("intent")

        # 确保明确记录意图，如果meta_info中没有，尝试从其他地方获取
        if not original_intent and "intent_parameters" in meta_info:
            intent_params = meta_info.get("intent_parameters", {})
            if "intent" in intent_params:
                original_intent = intent_params["intent"]
                response_meta_info["intent"] = original_intent
                logger.info(f"从intent_parameters中恢复意图: {original_intent}")

        logger.info(f"参数收集状态接收消息: intent={original_intent}, asking_param={asking_param}, message={message}")
        
        # 确保训练参数管理器已初始化
        if not hasattr(service, 'training_param_manager'):
            from app.services.conversation.training_param_manager import TrainingParamManager
            service.training_param_manager = TrainingParamManager(service)
            logger.info("初始化训练参数管理器")
        
        # 获取当前已收集的参数
        intent_parameters = meta_info.get("intent_parameters", {})
        collected_params = intent_parameters.copy()
        logger.info(f"已收集的参数: {collected_params}")
        
        # 如果正在询问某个参数，尝试从用户消息中提取该参数
        if asking_param:
            logger.info(f"正在询问参数: {asking_param}")
            
            # 提取用户回答的参数
            new_params = await service.training_param_manager.extract_training_parameters(
                message, 
                existing_params=collected_params, 
                intent=original_intent
            )
            
            logger.info(f"从用户回答中提取的参数: {new_params}")

            # 检查是否成功提取到询问的参数
            param_extracted = asking_param in new_params and new_params[asking_param] is not None and (
                not isinstance(new_params[asking_param], list) or 
                len(new_params[asking_param]) > 0
            )
            
            if param_extracted:
                # 更新已收集的参数
                collected_params.update(new_params)
                logger.info(f"更新后的已收集参数: {collected_params}")
                
                # 移除asking_param，表示已收集完成
                response_meta_info.pop("asking_param", None)

                # 生成参数确认消息
                extracted_value = new_params[asking_param]
                confirm_message = service.training_param_manager.get_param_confirmation(asking_param, extracted_value)
                
                # 发送确认消息 - 使用统一的消息格式
                yield {
                    "type": "message", 
                    "content": confirm_message,
                    "meta_info": response_meta_info
                }
        
        # 更新intent_parameters，保存已收集的参数
        response_meta_info["intent_parameters"] = collected_params
        response_meta_info["intent"] = original_intent  # 确保保持原始意图
        
        # 发送元数据更新
        yield {"type": "meta_info_update", "meta_info_update": response_meta_info}
        
        # 检查是否还有缺失的必要参数
        missing_params = service.training_param_manager.check_required_training_params(collected_params, original_intent)
        logger.info(f"缺失的必要参数: {missing_params}")
        
        if missing_params:
            # 还有必要参数需要收集
            next_param = missing_params[0]
            logger.info(f"需要收集的下一个参数: {next_param}")

            # 设置asking_param，标记正在收集的参数
            response_meta_info["asking_param"] = next_param
            
            # 生成询问该参数的提示消息
            prompt_message = service.training_param_manager.get_training_prompt_message(collected_params, original_intent)
            
            # 再次发送元数据更新，确保asking_param更新被传递
            yield {"type": "meta_info_update", "meta_info_update": response_meta_info}
            
            # 发送询问消息 - 使用统一的消息格式
            yield {
                "type": "message", 
                "content": prompt_message,
                "role": "assistant"
            }
        else:
            # 所有必要参数已收集完成，转换到下一状态
            logger.info("所有必要参数已收集完成，准备处理用户意图")
            
            # 移除收集参数相关的状态标记
            response_meta_info.pop("collecting_training_params", None)
            response_meta_info.pop("asking_param", None)
            
            # 设置已收集完成的标记
            response_meta_info["params_collected"] = True
            
            # 再次发送元数据更新，确保状态更新被传递
            yield {"type": "meta_info_update", "meta_info_update": response_meta_info}
            
            # 生成参数摘要
            param_summary = service.training_param_manager.format_training_params(collected_params, original_intent)
            param_summary_message = f"已收集完成所有必要参数:\n{param_summary}\n\n正在根据您的需求生成回复..."
            
            # 发送参数摘要消息 - 使用统一的消息格式
            yield {
                "type": "message",
                "content": param_summary_message,
                "role": "assistant",
                "next_state": "DefaultState"  # 切换到默认状态，处理用户意图
            }