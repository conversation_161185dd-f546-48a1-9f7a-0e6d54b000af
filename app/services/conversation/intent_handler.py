# app/services/conversation/intent_handler.py
"""
意图处理模块，负责处理用户意图并生成响应。

此文件提供向后兼容性，实际实现已移至 intent_handler/ 目录下的模块化结构中。
"""

from __future__ import annotations
import logging
from typing import TYPE_CHECKING, List, Dict, Any, Union, AsyncGenerator, Optional
import json
import asyncio
import traceback
from app import models
from app.services.intent_recognizer import IntentData
from langchain.schema.runnable import RunnableConfig
from langchain.callbacks.streaming_aiter import AsyncIteratorCallbackHandler
from sqlalchemy.orm import Session
from app.services.llm_proxy_service import LLMProxyService
from app.services.sql_tool_service import SQLToolService
from app.services.training_plan_service import TrainingPlanService

# 导入新的模块化结构
from app.services.conversation.intent_handler.base_handler import BaseIntentHandler
from app.services.conversation.intent_handler.utils import process_equipment_input

if TYPE_CHECKING:
    from .orchestrator import ConversationService

logger = logging.getLogger(__name__)

# 为了向后兼容，保留原有的函数名，但实现已移至新的模块化结构中
def _process_equipment_input(equipment_input) -> List[int]:
    """处理各种格式的器材输入，转换为器材ID列表

    此函数已被废弃，仅为向后兼容而保留。请使用 process_equipment_input 代替。

    Args:
        equipment_input: 可以是单个器材名称(字符串)、单个器材ID(整数)或器材列表

    Returns:
        器材ID列表
    """
    logger.warning("使用已废弃的 _process_equipment_input 函数，请使用 process_equipment_input 代替")
    return process_equipment_input(equipment_input)

class IntentHandler:
    """专门处理意图执行的类，减轻ConversationService的负担

    此类已被重构，实际实现已移至 intent_handler/ 目录下的模块化结构中。
    此处保留的实现仅为向后兼容。
    """

    def __init__(
        self,
        db: Session,
        llm_proxy=None,
        agent_executor=None,
        sql_tool=None,
        conversation_model=None
    ):
        """
        初始化意图处理器

        Args:
            db: 数据库会话
            llm_proxy: LLM代理服务
            agent_executor: LangChain代理执行器
            sql_tool: SQL工具服务
            conversation_model: 对话模型名称
        """
        self.db = db
        
        # 兼容处理，支持llm_proxy和llm_proxy_service两种参数名
        # 如果参数名为llm_proxy_service，将其赋值给llm_proxy
        if hasattr(llm_proxy, 'llm_proxy') and not isinstance(llm_proxy, LLMProxyService):
            self.llm_proxy = llm_proxy.llm_proxy
        else:
        self.llm_proxy = llm_proxy
            
        if not self.llm_proxy:
            self.llm_proxy = LLMProxyService()
            
        self.agent_executor = agent_executor
        self.sql_tool = sql_tool
        self.conversation_model = conversation_model or settings.LLM_MODEL

        # 初始化需要的服务
        self._initialize_services()

    def _initialize_services(self):
        """初始化处理意图所需的各种服务组件"""
        # 导入需要的服务和组件
        from app.services.model_service import ModelService
        from app.services.conversation.intent_handler.exercise_handler import ExerciseIntentHandler
        from app.services.conversation.intent_handler.training_plan_handler import TrainingPlanIntentHandler
        from app.services.conversation.intent_handler.fitness_advice_handler import FitnessAdviceIntentHandler
        from app.services.conversation.intent_handler.general_chat_handler import GeneralChatIntentHandler
        from app.services.conversation.intent_handler.discuss_training_plan_handler import DiscussTrainingPlanIntentHandler

        try:
            # 初始化模型服务
            self.model_service = ModelService(self.llm_proxy)
            
            # 检查是否需要初始化训练计划服务
            training_plan_service = None
            try:
                from app.services.training_plan_service import TrainingPlanService
                training_plan_service = TrainingPlanService(self.db, self.llm_proxy)
            except ImportError:
                logger.warning("TrainingPlanService未找到，相关功能可能受限")
            except Exception as e:
                logger.error(f"初始化TrainingPlanService失败: {str(e)}")
                
            # 初始化专门的处理器
            self.exercise_handler = ExerciseIntentHandler(self.db, self.llm_proxy, self.sql_tool, self.agent_executor, self.conversation_model)
            self.training_plan_handler = TrainingPlanIntentHandler(self.db, self.llm_proxy, self.sql_tool, training_plan_service, self.conversation_model)
            self.fitness_advice_handler = FitnessAdviceIntentHandler(self.db, self.llm_proxy, self.sql_tool, self.conversation_model)
            self.general_chat_handler = GeneralChatIntentHandler(self.db, self.llm_proxy, self.sql_tool, self.conversation_model)
            self.discuss_training_plan_handler = DiscussTrainingPlanIntentHandler(self.db, self.llm_proxy, self.sql_tool, self.conversation_model)
            
            logger.info("意图处理器服务初始化完成")
        except Exception as e:
            logger.error(f"初始化意图处理器服务失败: {str(e)}")
            # 创建最基本的服务，确保系统能够运行
            if not hasattr(self, 'model_service'):
                self.model_service = ModelService(self.llm_proxy)

    async def handle_intent(
        self,
        intent_data: IntentData,
        meta_info: Dict[str, Any],
        user_data: Dict[str, Any],
        history: List[Dict[str, Any]]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        根据意图类型分发到具体的处理函数

        Args:
            intent_data: 意图数据
            meta_info: 会话元数据
            user_data: 用户数据
            history: 对话历史

        Returns:
            异步生成器，生成响应内容
        """
        try:
            logger.info(f"处理意图: {intent_data.intent}, 参数: {intent_data.parameters}")
            logger.info(f"元数据: {meta_info}")
            logger.info(f"用户数据: id={user_data.get('id')}, 性别={user_data.get('gender')}, 年龄={user_data.get('age')}")
            logger.info(f"历史消息数量: {len(history)}")

            intent = intent_data.intent
            logger.info(f"开始分发意图: {intent}")

            # 根据意图类型分发到新的模块化处理器
            if intent in ["search_exercise", "recommend_exercise"]:
                logger.info(f"分发到exercise_handler处理")
                async for response in self.exercise_handler.handle(intent_data, user_data, history, meta_info):
                    yield response
            elif intent in ["daily_workout_plan", "weekly_workout_plan"]:
                logger.info(f"分发到training_plan_handler处理")
                try:
                    async for response in self.training_plan_handler.handle(intent_data, user_data, history, meta_info):
                        logger.info(f"training_plan_handler返回响应: {type(response)}")
                        yield response
                except Exception as e:
                    logger.error(f"training_plan_handler处理出错: {str(e)}", exc_info=True)
                    yield {"type": "message", "content": f"抱歉，生成训练计划时出现了问题: {str(e)}", "role": "assistant"}
            elif intent in ["nutrition_advice", "diet_suggestion", "macro_calculation"]:
                logger.info(f"分发到fitness_advice_handler处理")
                async for response in self.fitness_advice_handler.handle(intent_data, user_data, history, meta_info):
                    yield response
            elif intent == "discuss_training_plan":
                logger.info(f"分发到discuss_training_plan_handler处理")
                async for response in self.discuss_training_plan_handler.handle(intent_data, user_data, history, meta_info):
                    yield response
            else:
                logger.info(f"分发到general_chat_handler处理")
                async for response in self.general_chat_handler.handle(intent_data, user_data, history, meta_info):
                    yield response
        except Exception as e:
            logger.error(f"handle_intent处理意图时出错: {str(e)}", exc_info=True)
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            yield {"type": "message", "content": f"抱歉，处理您的请求时出现了问题: {str(e)}", "role": "assistant"}

    async def handle_exercise_intent(
        self,
        intent_data: IntentData,
        user_data: Dict[str, Any],
        history: List[Dict[str, Any]],
        meta_info: Dict[str, Any]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """处理训练动作相关意图

        此方法已被废弃，仅为向后兼容而保留。请使用 ExerciseIntentHandler.handle 代替。
        """
        logger.warning("使用已废弃的 handle_exercise_intent 方法，请使用 ExerciseIntentHandler.handle 代替")

        # 使用新的模块化处理器
        async for response in self.exercise_handler.handle(intent_data, user_data, history, meta_info):
            yield response

    async def handle_training_plan_intent(
        self,
        intent_data: IntentData,
        user_data: Dict[str, Any],
        history: List[Dict[str, Any]],
        meta_info: Dict[str, Any]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """处理训练计划生成相关意图

        此方法已被废弃，仅为向后兼容而保留。请使用 TrainingPlanIntentHandler.handle 代替。
        """
        logger.warning("使用已废弃的 handle_training_plan_intent 方法，请使用 TrainingPlanIntentHandler.handle 代替")

        # 使用新的模块化处理器
        async for response in self.training_plan_handler.handle(intent_data, user_data, history, meta_info):
            yield response

    async def handle_fitness_advice_intent(
        self,
        intent_data: IntentData,
        user_data: Dict[str, Any],
        history: List[Dict[str, Any]],
        meta_info: Dict[str, Any]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """处理健身建议相关意图

        此方法已被废弃，仅为向后兼容而保留。请使用 FitnessAdviceIntentHandler.handle 代替。
        """
        logger.warning("使用已废弃的 handle_fitness_advice_intent 方法，请使用 FitnessAdviceIntentHandler.handle 代替")

        # 使用新的模块化处理器
        async for response in self.fitness_advice_handler.handle(intent_data, user_data, history, meta_info):
            yield response

    async def handle_general_chat(
        self,
        intent_data: IntentData,
        user_data: Dict[str, Any],
        history: List[Dict[str, Any]],
        meta_info: Dict[str, Any]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """处理一般聊天意图

        此方法已被废弃，仅为向后兼容而保留。请使用 GeneralChatIntentHandler.handle 代替。
        """
        logger.warning("使用已废弃的 handle_general_chat 方法，请使用 GeneralChatIntentHandler.handle 代替")

        # 使用新的模块化处理器
        async for response in self.general_chat_handler.handle(intent_data, user_data, history, meta_info):
            yield response

    def _format_user_info(self, user_data: Dict[str, Any]) -> str:
        """格式化用户信息为字符串

        此方法已被废弃，仅为向后兼容而保留。请使用 format_user_info 代替。
        """
        logger.warning("使用已废弃的 _format_user_info 方法，请使用 format_user_info 代替")
        from app.services.conversation.intent_handler.utils import format_user_info
        return format_user_info(user_data)

    async def _call_agent_streaming(self, input_text: str) -> AsyncGenerator[Dict[str, Any], None]:
        """调用Agent并流式返回结果"""
        try:
            # 创建回调处理器
            callback_handler = AsyncIteratorCallbackHandler()

            # 设置运行配置
            config = RunnableConfig(
                callbacks=[callback_handler]
            )

            # 记录开始时间
            import time
            start_time = time.time()
            logger.info(f"开始调用Agent: {input_text[:100]}...")

            # 异步运行Agent
            task = self.agent_executor.ainvoke(
                {"input": input_text},
                config=config
            )

            # 流式输出tokens
            token_count = 0
            async for token in callback_handler.aiter():
                token_count += 1
                if token_count % 50 == 0:  # 每50个token记录一次
                    logger.info(f"已接收 {token_count} 个token")
                yield {"type": "token", "content": token}

            # 等待任务完成
            result = await task
            final_response = result.get("output", "")

            # 记录结束时间和耗时
            end_time = time.time()
            logger.info(f"Agent调用完成，耗时: {end_time - start_time:.2f}秒，共接收 {token_count} 个token")
            logger.info(f"Agent返回结果类型: {type(result)}")

            if isinstance(result, dict):
                logger.info(f"Agent返回结果键: {list(result.keys())}")

            # 检查是否有结构化数据
            if isinstance(result, dict):
                # 检查是否有训练计划数据
                if "training_plan" in result:
                    logger.info(f"检测到训练计划数据")
                    # 附加计划数据
                    yield {"type": "structured_data", "data": result["training_plan"], "data_type": "training_plan"}

                # 检查是否有推荐动作数据
                if "exercises" in result:
                    logger.info(f"检测到推荐动作数据: {len(result['exercises'])} 个动作")
                    # 附加动作数据
                    yield {"type": "structured_data", "data": result["exercises"], "data_type": "exercises"}

                # 检查是否有错误信息
                if "error" in result:
                    logger.error(f"Agent返回错误: {result['error']}")
                    yield {"type": "message", "content": f"抱歉，处理您的请求时出现了问题: {result['error']}", "role": "assistant"}

        except Exception as e:
            logger.error(f"调用Agent时出错: {str(e)}", exc_info=True)
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            yield {"type": "message", "content": f"抱歉，处理您的请求时出现了问题: {str(e)}", "role": "assistant"}
