# app/services/conversation/profile_helper.py
from __future__ import annotations
import logging
from typing import TYPE_CHECKING, Dict, Any
from app import models

if TYPE_CHECKING:
    from .orchestrator import ConversationService

logger = logging.getLogger(__name__)

def _get_user_profile(service: 'ConversationService', user: models.User) -> Dict[str, Any]:
    """获取用户个性化信息

    Args:
        service: The ConversationService instance.
        user: 用户模型对象

    Returns:
        用户个性化信息字典
    """
    return {
        "id": user.id,
        "nickname": user.nickname,
        "gender": user.gender,
        "age": user.age,
        "height": user.height,
        "weight": user.weight,
        "bmi": user.bmi,
        "fitness_goal": user.fitness_goal,
        "experience_level": user.experience_level,
        "activity_level": user.activity_level,
        "health_conditions": user.health_conditions,
        "allergies": user.allergies
    }

def _get_user_profile_text(service: 'ConversationService', user_profile: Dict[str, Any]) -> str:
    """将用户个性化信息转换为文本

    Args:
        service: The ConversationService instance.
        user_profile: 用户个性化信息字典

    Returns:
        用户个性化信息文本
    """
    gender_text = "未知"
    if user_profile["gender"] in service.active_query_manager.field_enum_map.get("gender", {}):
        gender_text = service.active_query_manager.field_enum_map["gender"][user_profile["gender"]]

    fitness_goal_text = "未设置"
    if user_profile["fitness_goal"] in service.active_query_manager.field_enum_map.get("fitness_goal", {}):
        fitness_goal_text = service.active_query_manager.field_enum_map["fitness_goal"][user_profile["fitness_goal"]]

    experience_level_text = "未设置"
    if user_profile["experience_level"] in service.active_query_manager.field_enum_map.get("experience_level", {}):
        experience_level_text = service.active_query_manager.field_enum_map["experience_level"][user_profile["experience_level"]]

    activity_level_text = "未设置"
    if user_profile["activity_level"] in service.active_query_manager.field_enum_map.get("activity_level", {}):
        activity_level_text = service.active_query_manager.field_enum_map["activity_level"][user_profile["activity_level"]]

    return f"""
用户信息:
- 性别: {gender_text}
- 年龄: {user_profile["age"] or "未设置"} 岁
- 身高: {user_profile["height"] or "未设置"} cm
- 体重: {user_profile["weight"] or "未设置"} kg
- BMI: {user_profile["bmi"] or "未计算"}
- 健身目标: {fitness_goal_text}
- 健身经验: {experience_level_text}
- 日常活动水平: {activity_level_text}
- 健康状况: {', '.join(user_profile["health_conditions"]) if user_profile["health_conditions"] else "无特殊情况"}
"""

def _get_recommended_difficulty(service: 'ConversationService', user_profile: Dict[str, Any]) -> Dict[str, int]:
    """根据用户信息获取推荐的训练难度级别
    
    Args:
        service: ConversationService实例
        user_profile: 用户信息字典
        
    Returns:
        包含推荐难度范围的字典，如{"min": 1, "max": 3, "preferred": 2}
    """
    # 默认中等难度
    result = {"min": 1, "max": 3, "preferred": 2}
    
    # 根据经验水平调整
    experience_level = user_profile.get("experience_level")
    if experience_level is not None:
        # 确保是整数
        if isinstance(experience_level, str) and experience_level.isdigit():
            experience_level = int(experience_level)
        elif not isinstance(experience_level, int):
            experience_level = 2  # 默认中级水平
        
        # 根据经验水平确定难度
        if experience_level == 1:  # 初学者
            result = {"min": 1, "max": 2, "preferred": 1}
        elif experience_level == 2:  # 中级
            result = {"min": 1, "max": 3, "preferred": 2}
        elif experience_level == 3:  # 高级
            result = {"min": 2, "max": 4, "preferred": 3}
        elif experience_level >= 4:  # 专业
            result = {"min": 3, "max": 5, "preferred": 4}
    
    return result

def _get_training_params(service: 'ConversationService', user_profile: Dict[str, Any]) -> Dict[str, Any]:
    """根据用户信息获取推荐的训练参数
    
    Args:
        service: ConversationService实例
        user_profile: 用户信息字典
        
    Returns:
        包含训练组数、次数、休息时间等参数的字典
    """
    # 默认训练参数（中级水平）
    default_params = {
        "sets": 3,
        "reps": "10-12",
        "rest_seconds": 60,
        "experience_level": 2
    }
    
    # 根据经验水平调整
    experience_level = user_profile.get("experience_level")
    if experience_level is not None:
        # 确保是整数
        if isinstance(experience_level, str) and experience_level.isdigit():
            experience_level = int(experience_level)
        elif not isinstance(experience_level, int):
            experience_level = 2  # 默认中级水平
        
        default_params["experience_level"] = experience_level
        
        # 根据经验水平调整参数
        if experience_level == 1:  # 初学者
            default_params.update({
                "sets": 2,
                "reps": "12-15",
                "rest_seconds": 90
            })
        elif experience_level == 2:  # 中级
            default_params.update({
                "sets": 3,
                "reps": "10-12",
                "rest_seconds": 60
            })
        elif experience_level == 3:  # 高级
            default_params.update({
                "sets": 4,
                "reps": "8-10",
                "rest_seconds": 45
            })
        elif experience_level >= 4:  # 专业
            default_params.update({
                "sets": 5,
                "reps": "5-8",
                "rest_seconds": 60
            })
    
    # 根据健身目标调整
    fitness_goal = user_profile.get("fitness_goal")
    if fitness_goal is not None:
        # 确保是整数
        if isinstance(fitness_goal, str) and fitness_goal.isdigit():
            fitness_goal = int(fitness_goal)
        
        if fitness_goal == 1:  # 减脂
            default_params.update({
                "reps": "15-20",
                "rest_seconds": max(30, default_params["rest_seconds"] - 15)
            })
        elif fitness_goal == 2:  # 增肌
            default_params.update({
                "reps": "8-12",
                "rest_seconds": 90
            })
        elif fitness_goal == 3:  # 力量
            default_params.update({
                "reps": "5-8",
                "rest_seconds": 120
            })
        elif fitness_goal == 4:  # 塑形
            default_params.update({
                "reps": "12-15",
                "rest_seconds": 45
            })
    
    return default_params

def _get_level_text(service: 'ConversationService', user_profile: Dict[str, Any], field: str) -> str:
    """获取用户级别文本描述"""
    level_value = user_profile.get(field)
    if not level_value:
        return "未知"

    level_map = service.active_query_manager.field_enum_map.get(field, {})
    if level_value in level_map:
        return level_map[level_value]
    return str(level_value) 