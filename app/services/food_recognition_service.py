import logging
import os
import base64
import json
import re
import time
import tempfile
from typing import Dict, Any, List, Optional, Union
from datetime import date, datetime

from fastapi import UploadFile
from sqlalchemy.orm import Session
from openai import OpenAI

from app import crud, models
from app.schemas import meal
# 明确导入food_recognition中的模型
from app.schemas.food_recognition import FoodItemCreate, FoodRecognitionCreate, FoodRecognitionUpdate, FoodRecognitionResponse, FoodRecognitionConfirmation
from app.schemas.meal import MealType
from app.core.config import settings
from app.services.storage import StorageService
from app.services.llm_log_service import LLMLogService


logger = logging.getLogger(__name__)

# 初始化OpenAI客户端，使用settings中配置的默认AI服务
ai_client = OpenAI(
    api_key=settings.AI_API_KEY,
    base_url=settings.AI_BASE_URL
)

class FoodRecognitionService:
    """食物识别服务"""
    
    @staticmethod
    async def analyze_image(
        db: Session,
        user_id: str,
        image_data: bytes,
        meal_type: MealType,
        meal_date: date,
        service_name: str = None,
        optimize: bool = True
    ) -> Dict[str, Any]:
        """分析食物图片并识别其中的食物项，存储临时记录"""
        try:
            start_time = time.time()
            
            # 上传图片到存储服务
            upload_result = await StorageService.upload_image(
                image_data=image_data, 
                user_id=user_id, 
                meal_date=meal_date, 
                meal_type=meal_type.value,
                optimize=optimize  # 使用优化选项
            )
            
            # 生成并存储缩略图
            thumb_result = await StorageService.create_thumbnail(
                image_data=image_data,
                user_id=user_id,
                meal_date=meal_date,
                meal_type=meal_type.value,
                original_filename=upload_result.get("filename")
            )
            
            # 获取缩略图URL
            thumb_image_url = thumb_result.get("url")
            
            # 构建临时识别记录
            recognition = models.FoodRecognition(
                user_id=user_id,
                meal_date=meal_date,
                meal_type=meal_type.value,
                image_url=upload_result.get("url"),
                secure_path=upload_result.get("secure_path"),
                status="processing"
            )
            
            # 尝试设置缩略图URL（如果模型支持）
            try:
                recognition.thumb_image_url = thumb_image_url
            except AttributeError:
                logger.warning("数据库模型不支持thumb_image_url字段，缩略图URL将不会被存储")
            
            db.add(recognition)
            db.commit()
            db.refresh(recognition)
            
            # 打印处理时间
            upload_time = time.time() - start_time
            logger.info(f"图片上传和缩略图生成耗时: {upload_time:.2f}秒")
            
            # 调用AI识别服务
            recognized_result = await FoodRecognitionService._ai_recognition(
                db, 
                image_data=image_data, 
                image_url=upload_result.get("url"),
                meal_type=meal_type,
                service_name=service_name,
                user_id=user_id
            )
            
            # 提取识别结果
            food_items = recognized_result.get("food_items", [])
            health_recommendation = recognized_result.get("health_recommendation", "这是一顿营养均衡的餐食。")
            nutrition_totals = recognized_result.get("nutrition_totals", {})
            matched_foods = recognized_result.get("matched_foods", [])
            
            # 更新临时识别记录状态为已完成，存储完整识别结果
            recognition.status = "completed"
            recognition.recognition_result = {
                "food_items": [item.dict() for item in food_items],
                "meal_name": recognized_result.get("meal_name", ""),
                "health_recommendation": health_recommendation
            }
            recognition.matched_foods = matched_foods
            recognition.nutrition_totals = nutrition_totals
            db.add(recognition)
            db.commit()
            
            total_time = time.time() - start_time
            logger.info(f"食物识别总耗时: {total_time:.2f}秒")
            
            # 确保所有食物项都有code字段
            food_items_with_code = []
            for item in food_items:
                try:
                    if not item.code:
                        # 使用 dict() 方法获取现有属性
                        item_dict = item.dict()
                        # 如果存在food_id，使用food_id生成code
                        if item.food_id:
                            item_dict["code"] = f"food_{item.food_id}"
                        else:
                            # 否则使用名称和时间戳生成唯一code
                            item_dict["code"] = f"auto_{int(time.time())}_{item.name.replace(' ', '_')}"
                        # 创建新的对象替代旧对象
                        food_items_with_code.append(FoodItemCreate(**item_dict))
                    else:
                        food_items_with_code.append(item)
                except AttributeError:
                    # 如果item没有code属性，添加code字段
                    item_dict = item.dict()
                    if hasattr(item, 'food_id') and item.food_id:
                        item_dict["code"] = f"food_{item.food_id}"
                    else:
                        item_dict["code"] = f"auto_{int(time.time())}_{item.name.replace(' ', '_')}"
                    food_items_with_code.append(FoodItemCreate(**item_dict))
            
            # 使用新的食物项列表
            food_items = food_items_with_code
            
            # 返回完整数据，包括临时记录ID用于后续确认，但不单独返回matched_foods字段
            response_data = {
                "success": True,
                "recognition_id": recognition.id,
                "food_items": food_items,
                "meal_name": recognized_result.get("meal_name", ""),
                "health_recommendation": health_recommendation,
                "image_url": upload_result.get("url"),
                "nutrition_totals": nutrition_totals,
                "matched_foods": matched_foods,
                "status": "completed",
                "created_at": recognition.created_at,
                "meal_date": recognition.meal_date,
                "meal_type": recognition.meal_type
            }
            
            # 如果有缩略图，添加到响应
            if thumb_image_url:
                response_data["thumb_image_url"] = thumb_image_url
            logger.info(f"食物识别结果: {response_data}")
            return response_data
            
        except Exception as e:
            logger.error(f"食物识别失败: {str(e)}")
            # 如果存在临时记录，更新状态为失败
            if 'recognition' in locals() and recognition.id:
                recognition.status = "error"
                recognition.recognition_result = {"error": str(e)}
                db.add(recognition)
                db.commit()
            raise e
    
    @staticmethod
    async def analyze_base64_image(
        db: Session,
        user_id: str,
        base64_image: str,
        meal_type: MealType,
        meal_date: date,
        service_name: str = None,
        optimize: bool = True
    ) -> Dict[str, Any]:
        """分析base64编码的食物图片并识别其中的食物项，存储临时记录"""
        try:
            start_time = time.time()
            
            # 上传base64图片到存储服务
            upload_result = await StorageService.upload_base64_image(
                base64_image=base64_image, 
                user_id=user_id, 
                meal_date=meal_date, 
                meal_type=meal_type.value,
                optimize=optimize  # 使用优化选项
            )
            
            # 生成并存储缩略图
            thumb_result = await StorageService.create_thumbnail_from_base64(
                base64_image=base64_image,
                user_id=user_id,
                meal_date=meal_date,
                meal_type=meal_type.value,
                original_filename=upload_result.get("filename")
            )
            
            # 获取缩略图URL
            thumb_image_url = thumb_result.get("url")
            
            # 构建临时识别记录
            recognition = models.FoodRecognition(
                user_id=user_id,
                meal_date=meal_date,
                meal_type=meal_type.value,
                image_url=upload_result.get("url"),
                secure_path=upload_result.get("secure_path"),
                status="processing"
            )
            
            # 尝试设置缩略图URL（如果模型支持）
            try:
                recognition.thumb_image_url = thumb_image_url
            except AttributeError:
                logger.warning("数据库模型不支持thumb_image_url字段，缩略图URL将不会被存储")
            
            db.add(recognition)
            db.commit()
            db.refresh(recognition)
            
            # 打印处理时间
            upload_time = time.time() - start_time
            logger.info(f"Base64图片上传和缩略图生成耗时: {upload_time:.2f}秒")
            
            # 判断是否包含前缀
            image_data = None
            if ',' in base64_image:
                base64_data = base64_image.split(',', 1)[1]
                image_data = base64.b64decode(base64_data)
            else:
                image_data = base64.b64decode(base64_image)
            
            # 调用AI识别服务（直接传递base64数据供API使用）
            recognized_result = await FoodRecognitionService._ai_recognition(
                db, 
                image_data=image_data,
                image_url=upload_result.get("url"),
                meal_type=meal_type,
                service_name=service_name,
                base64_image=base64_image,  # 传递原始base64数据
                user_id=user_id
            )
            
            # 提取识别结果
            food_items = recognized_result.get("food_items", [])
            health_recommendation = recognized_result.get("health_recommendation", "这是一顿营养均衡的餐食。")
            nutrition_totals = recognized_result.get("nutrition_totals", {})
            matched_foods = recognized_result.get("matched_foods", [])
            
            # 更新临时识别记录状态为已完成，存储完整识别结果
            recognition.status = "completed"
            recognition.recognition_result = {
                "food_items": [item.dict() for item in food_items],
                "meal_name": recognized_result.get("meal_name", ""),
                "health_recommendation": health_recommendation
            }
            recognition.matched_foods = matched_foods
            recognition.nutrition_totals = nutrition_totals
            db.add(recognition)
            db.commit()
            
            # 确保所有食物项都有code字段
            food_items_with_code = []
            for item in food_items:
                try:
                    if not item.code:
                        # 使用 dict() 方法获取现有属性
                        item_dict = item.dict()
                        # 如果存在food_id，使用food_id生成code
                        if item.food_id:
                            item_dict["code"] = f"food_{item.food_id}"
                        else:
                            # 否则使用名称和时间戳生成唯一code
                            item_dict["code"] = f"auto_{int(time.time())}_{item.name.replace(' ', '_')}"
                        # 创建新的对象替代旧对象
                        food_items_with_code.append(FoodItemCreate(**item_dict))
                    else:
                        food_items_with_code.append(item)
                except AttributeError:
                    # 如果item没有code属性，添加code字段
                    item_dict = item.dict()
                    if hasattr(item, 'food_id') and item.food_id:
                        item_dict["code"] = f"food_{item.food_id}"
                    else:
                        item_dict["code"] = f"auto_{int(time.time())}_{item.name.replace(' ', '_')}"
                    food_items_with_code.append(FoodItemCreate(**item_dict))
            
            # 使用新的食物项列表
            food_items = food_items_with_code
            
            # 返回完整数据，包括临时记录ID用于后续确认，但不单独返回matched_foods字段
            response_data = {
                "success": True,
                "recognition_id": recognition.id,
                "food_items": food_items,
                "meal_name": recognized_result.get("meal_name", ""),
                "health_recommendation": health_recommendation,
                "image_url": upload_result.get("url"),
                "nutrition_totals": nutrition_totals,
                "matched_foods": matched_foods,
                "status": "completed",
                "created_at": recognition.created_at,
                "meal_date": recognition.meal_date,
                "meal_type": recognition.meal_type
            }
            
            # 如果有缩略图，添加到响应
            if thumb_image_url:
                response_data["thumb_image_url"] = thumb_image_url
                
            return response_data
            
        except Exception as e:
            logger.error(f"Base64食物图像识别失败: {str(e)}")
            # 如果存在临时记录，更新状态为失败
            if 'recognition' in locals() and recognition.id:
                recognition.status = "error"
                recognition.recognition_result = {"error": str(e)}
                db.add(recognition)
                db.commit()
            raise e
    
    @staticmethod
    async def confirm_recognition(
        db: Session,
        recognition_id: int,
        confirmation: FoodRecognitionConfirmation,
        image_url: Optional[str] = None,
        thumb_image_url: Optional[str] = None,
        optimize: bool = True
    ) -> Dict[str, Any]:
        """确认或修正识别结果，并创建正式的餐食记录"""
        try:
            # 获取识别记录
            recognition = crud.food_recognition.get(db, id=recognition_id)
            if not recognition:
                raise ValueError("识别记录不存在")
            
            # 确定使用的图像URL
            final_image_url = image_url if image_url else recognition.image_url
            
            # 创建餐食记录
            meal_record_data = {
                "user_id": recognition.user_id,
                "date": recognition.meal_date,
                "meal_type": recognition.meal_type,
                "image_url": final_image_url,
                "is_ai_recognized": True
            }
            
            # 如果有缩略图，添加到记录
            if thumb_image_url:
                try:
                    meal_record_data["thumb_image_url"] = thumb_image_url
                except Exception as e:
                    logger.warning(f"无法设置缩略图URL: {str(e)}")
            
            # 如果有餐食名称，添加到记录
            if confirmation.meal_name:
                meal_record_data["meal_name"] = confirmation.meal_name
                
            # 如果前端提供了营养总量，使用前端的数据
            if confirmation.nutrition_totals:
                if "calories" in confirmation.nutrition_totals:
                    meal_record_data["total_calory"] = confirmation.nutrition_totals.get("calories", 0)
                elif "total_calory" in confirmation.nutrition_totals:
                    meal_record_data["total_calory"] = confirmation.nutrition_totals.get("total_calory", 0)
                
                meal_record_data["total_protein"] = confirmation.nutrition_totals.get("protein", 0)
                meal_record_data["total_fat"] = confirmation.nutrition_totals.get("fat", 0)
                meal_record_data["total_carbohydrate"] = confirmation.nutrition_totals.get("carbohydrate", 0)
            
            meal_record = models.MealRecord(**meal_record_data)
            db.add(meal_record)
            db.commit()
            db.refresh(meal_record)
            
            # 添加食物项
            from app.services.meal_service import MealService
            for food_item_data in confirmation.food_items:
                # 查找匹配的食品库条目
                food = None
                if food_item_data.food_id:
                    food = crud.food.get(db, id=food_item_data.food_id)
                else:
                    # 尝试通过名称匹配
                    foods = crud.food.get_by_name(db, name=food_item_data.name, limit=1)
                    if foods:
                        food = foods[0]
                        food_item_data.food_id = food.id
                
                # 优先使用matching_info中的营养数据
                if food_item_data.matching_info:
                    # 获取matching_info中的营养数据
                    matching_info = food_item_data.matching_info
                    if "calory" in matching_info and matching_info["calory"] is not None:
                        food_item_data.calory = matching_info["calory"]
                    if "protein" in matching_info and matching_info["protein"] is not None:
                        food_item_data.protein = matching_info["protein"]
                    if "fat" in matching_info and matching_info["fat"] is not None:
                        food_item_data.fat = matching_info["fat"]
                    if "carbohydrate" in matching_info and matching_info["carbohydrate"] is not None:
                        food_item_data.carbohydrate = matching_info["carbohydrate"]
                
                # 添加食物项
                food_item = MealService.add_food_item_to_meal(
                    db=db,
                    meal_id=meal_record.id,
                    food_item_in=food_item_data
                )
                
                # 如果食物项中有图像URL，将其关联到食物项
                if hasattr(food_item_data, "image_url") and food_item_data.image_url:
                    food_item.image_url = food_item_data.image_url
                    db.add(food_item)
                    db.commit()
            
            # 如果前端未提供营养总量，则计算更新餐食营养总值
            if not confirmation.nutrition_totals:
                MealService.update_meal_nutrition_totals(db=db, meal_id=meal_record.id)
            
            # 添加健康建议
            if confirmation.health_recommendation:
                health_rec = models.HealthRecommendation(
                    meal_record_id=meal_record.id,
                    recommendation_text=confirmation.health_recommendation,
                    recommendation_type="ai_generated",
                    priority=1
                )
                db.add(health_rec)
                db.commit()
            
            # 更新识别记录状态
            recognition.status = "confirmed"
            recognition.meal_record_id = meal_record.id
            recognition.user_modified = True  # 标记为用户已修改
            
            # 如果有匹配的食物，保存到识别记录
            if confirmation.matched_foods:
                recognition.matched_foods = confirmation.matched_foods
            
            db.add(recognition)
            db.commit()
            
            # 获取完整的餐食记录
            complete_meal_record = crud.meal.get(db, id=meal_record.id)
            
            # 返回成功信息和完整餐食记录
            return {
                "success": True,
                "message": "餐食记录创建成功",
                "meal_record": complete_meal_record,
                "recognition_id": recognition_id
            }
            
        except Exception as e:
            logger.error(f"确认识别结果失败: {str(e)}")
            return {
                "success": False,
                "message": f"确认识别结果失败: {str(e)}",
                "meal_record": None,
                "recognition_id": recognition_id
            }
    
    @staticmethod
    async def confirm_recognition_with_base64(
        db: Session,
        recognition_id: int,
        confirmation: FoodRecognitionConfirmation,
        base64_image: Optional[str] = None,
        food_base64_images: Optional[Dict[str, str]] = None,  # 食物项base64图像，以code为键
        optimize: bool = True
    ) -> Dict[str, Any]:
        """使用base64图像确认或修正识别结果，并创建正式的餐食记录"""
        try:
            # 获取识别记录
            recognition = crud.food_recognition.get(db, id=recognition_id)
            if not recognition:
                raise ValueError("识别记录不存在")
            
            # 初始化图像URL和缩略图URL
            image_url = recognition.image_url
            thumb_image_url = getattr(recognition, 'thumb_image_url', None)
            
            # 如果提供了base64图像，处理并存储
            if base64_image:
                try:
                    start_time = time.time()
                    
                    # 上传原始图像
                    from app.services.storage import StorageService
                    upload_result = await StorageService.upload_base64_image(
                        base64_image=base64_image,
                        user_id=recognition.user_id,
                        meal_date=recognition.meal_date,
                        meal_type=recognition.meal_type,
                        is_original=True,  # 标记为原图
                        optimize=optimize   # 使用优化选项
                    )
                    
                    # 使用新的图像URL
                    image_url = upload_result.get("url")
                    logger.info(f"Base64上传图像URL: {image_url}")
                    
                    # 生成并存储缩略图
                    thumb_result = await StorageService.create_thumbnail_from_base64(
                        base64_image=base64_image,
                        user_id=recognition.user_id,
                        meal_date=recognition.meal_date,
                        meal_type=recognition.meal_type,
                        original_filename=upload_result.get("filename")
                    )
                    
                    # 获取缩略图URL
                    thumb_image_url = thumb_result.get("url")
                    logger.info(f"Base64生成缩略图URL: {thumb_image_url}")
                    
                    image_process_time = time.time() - start_time
                    logger.info(f"确认阶段图像处理耗时: {image_process_time:.2f}秒")
                    
                except Exception as e:
                    logger.error(f"Base64图像处理失败: {str(e)}")
                    # 图像处理失败不影响确认过程，记录错误并继续
            
            # 如果提供了食物项base64图像，为每个食物项处理图像
            if food_base64_images:
                for item in confirmation.food_items:
                    if item.code and item.code in food_base64_images:
                        food_base64 = food_base64_images[item.code]
                        # 验证base64图像格式
                        if not food_base64.strip().startswith(("data:image/", "iVBORw0K", "/9j/")):
                            logger.warning(f"食物项 {item.name} 的base64数据不是有效的图像格式，将跳过")
                            continue
                        
                        try:
                            # 上传食物项图像
                            food_upload_result = await StorageService.upload_base64_image(
                                base64_image=food_base64,
                                user_id=recognition.user_id,
                                meal_date=recognition.meal_date,
                                meal_type=recognition.meal_type,
                                file_prefix=f"food_{item.code}_",  # 添加食物标识前缀
                                is_original=True,
                                optimize=optimize
                            )
                            # 设置食物项图像URL
                            item.image_url = food_upload_result.get("url")
                            logger.info(f"为食物项 {item.name} 上传图像成功，URL: {item.image_url}")
                        except Exception as e:
                            logger.error(f"为食物项 {item.name} 上传图像失败: {str(e)}")
                            # 单个食物项图像上传失败不影响整体确认过程
            
            # 调用标准确认方法
            result = await FoodRecognitionService.confirm_recognition(
                db=db,
                recognition_id=recognition_id,
                confirmation=confirmation,
                image_url=image_url,
                thumb_image_url=thumb_image_url,
                optimize=optimize
            )
            
            # 确保返回结果包含recognition_id
            if "recognition_id" not in result:
                result["recognition_id"] = recognition_id
                
            return result
            
        except Exception as e:
            logger.error(f"使用base64图像确认识别结果失败: {str(e)}")
            return {
                "success": False,
                "message": f"确认识别结果失败: {str(e)}",
                "meal_record": None,
                "recognition_id": recognition_id
            }
    
    @staticmethod
    async def _ai_recognition(
        db: Session, 
        image_data: bytes, 
        image_url: str, 
        meal_type: MealType = None,
        service_name: str = None,
        base64_image: Optional[str] = None,
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """调用大模型进行食物识别并匹配数据库食物"""
        start_time = time.time()
        
        # 定义变量用于记录原始响应
        original_response = None
        original_request = None
        
        # 如果meal_type为空，根据当前时间自动确定
        if meal_type is None:
            current_hour = datetime.now().hour
            if 6 <= current_hour < 9:
                meal_type = MealType.BREAKFAST
            elif 11 <= current_hour < 13:
                meal_type = MealType.LUNCH
            elif 15 <= current_hour < 20:
                meal_type = MealType.DINNER
            else:
                meal_type = MealType.SNACK
            logger.info(f"根据当前时间({current_hour}点)自动设置餐食类型为: {meal_type.value}")
        
        logger.info(f"开始AI食物识别，meal_type={meal_type.value}")
        
        try:
            # 准备base64格式的图像数据
            image_base64 = ""
            if base64_image:
                # 如果提供了base64字符串，直接使用（确保没有前缀）
                if ',' in base64_image:
                    image_base64 = base64_image.split(',', 1)[1]
                else:
                    image_base64 = base64_image
            else:
                # 否则从二进制数据转换
                image_base64 = base64.b64encode(image_data).decode('utf-8')
            
            # 获取AI服务配置 - 增强错误处理和查询逻辑
            service = service_name or settings.AI_DEFAULT_SERVICE
            api_config = settings.AI_API_CONFIGS.get(service)
            
            if not api_config:
                logger.warning(f"未找到AI服务配置: {service}，使用默认服务: {settings.AI_DEFAULT_SERVICE}")
                service = settings.AI_DEFAULT_SERVICE
                api_config = settings.AI_API_CONFIGS.get(service, {})
                if not api_config:
                    raise ValueError(f"无法找到有效的AI服务配置，服务名称: {service}")
            
            # 从配置中提取关键参数
            api_key = api_config.get("api_key")
            base_url = api_config.get("base_url")
            model_name = api_config.get("model")
            
            if not api_key or not base_url or not model_name:
                raise ValueError(f"AI服务配置不完整: {service}")
            
            logger.info(f"使用AI服务 [{service}] 的模型 [{model_name}] 进行食物识别")
            
            # 根据选择的服务创建客户端
            client = OpenAI(
                api_key=api_key,
                base_url=base_url
            )
            
            # 调用大模型API进行食物识别
            meal_type_str = meal_type.value if hasattr(meal_type, 'value') else str(meal_type)
            
            # 使用扁平化字段结构的提示词 (移除了fraction字段)
            request_body = {
                "model": model_name,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": f"""分析图片中的食物，生成JSON格式数据，包含以下信息：

                            {{
                                "meal_name": "基于{meal_type_str}和识别的食物创建的有意义名称",
                                "food_item_list": [
                                {{
                                    "name": "识别出的食物名称",
                                    "unit_name": "单位（盘、碗、瓶等）",
                                    "quantity": 数量（整数）,
                                    "weight": 重量（g）,
                                    "category": "食物类别（蛋类/肉类/蔬菜类等）",
                                    "cuisine_type": "菜系大类（亚洲/欧洲/美洲等）",
                                    "cuisine_type_detail": "具体菜系（川菜/粤菜等）",
                                    "calory": 热量（千卡）,
                                    "protein": 蛋白质（g）,
                                    "fat": 脂肪（g）,
                                    "carbohydrate": 碳水化合物（g）,
                                    "health_light": 健康评分（1-10分）,
                                    "lights": ["有益特点"],
                                    "warnings": ["健康警告"],
                                    "warning_scenes": ["不适宜人群"],
                                    "is_takeout": 是否为外卖（true/false）
                                }},
                                ...
                                ],
                                "health_recommendation": "根据食物营养信息给出的健康建议"
                            }}

                            请注意：
                            1. 食物名称要精确，单位使用常见的计量单位
                            2. 热量计算：1g蛋白质=4千卡，1g脂肪=9千卡，1g碳水=4千卡
                            3. 健康评分满分10分，高蛋白/高纤维为有益特点，高脂肪/高糖为警告信息
                            4. 判断是否为外卖：通过包装、餐盒、标签等特征来判断
                            5. 所有数值使用数字而非字符串，布尔值使用true/false
                            6. 请仅返回JSON数据，不要有其他说明文字，确保JSON语法正确无误
                            7. 确保关闭所有JSON对象和数组，不遗漏逗号和括号
                            """
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{image_base64}"
                                }
                            },
                        ],
                    },
                ],
                "max_tokens": 3000  # 增加token上限，确保完整输出
            }
            
            # 创建请求日志副本，移除base64图像数据
            request_log = {
                "model": model_name,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": request_body["messages"][0]["content"][0]["text"][:200]},
                            {"type": "image_url", "image_url": {"url": "[BASE64_IMAGE_DATA_REMOVED]"}}
                        ]
                    }
                ],
                "max_tokens": request_body["max_tokens"]
            }
            
            # 保存原始请求
            original_request = request_log
            
            # 调用API
            response = client.chat.completions.create(**request_body)
            
            # 解析模型响应
            result = response.choices[0].message.content
            
            # 保存原始响应
            original_response = result
            
            logger.info(f"AI服务 [{service}] 识别结果原始响应前200字符: {result}")
            
            # 立即尝试记录原始请求和响应，确保即使后续处理失败也能保存原始数据
            try:
                # 准备元数据
                metadata = {
                    "service_name": service,
                    "model_name": model_name,
                    "api_base_url": base_url,
                    "image_url": image_url,
                    "meal_type": meal_type_str,
                    "processing_status": "raw_response_received",
                    "response_time": time.time() - start_time
                }
                
                # 构建安全的响应数据结构
                raw_response_data = {
                    "raw_content": original_response,
                    # 添加以下字段以保持数据结构一致性
                    "food_items": 0,  # 表示暂未处理食物项
                    "matched_foods": 0  # 表示暂未匹配食物
                }
                
                # 记录原始请求和响应
                await LLMLogService.log_llm_call(
                    user_id=user_id,
                    request_data=original_request,
                    response_data=raw_response_data,
                    image_path=image_url,
                    base64_image=None,
                    meal_type=meal_type_str,
                    image_data=None,
                    processing_time=time.time() - start_time,
                    metadata=metadata,
                    log_type="raw_response"  # 标记为原始响应日志
                )
            except Exception as e:
                logger.error(f"记录原始响应日志失败: {str(e)}")
                # 日志记录失败不影响主流程继续执行
            
            # 增强JSON提取逻辑，更鲁棒地处理各种情况
            recognition_data = {}
            
            # 首先尝试清理响应格式（移除可能存在的代码块格式）
            cleaned_result = result
            # 移除开头的```json和结尾的```
            if cleaned_result.startswith('```'):
                # 查找第一个换行
                first_newline = cleaned_result.find('\n')
                if first_newline > 0:
                    # 查找最后的```
                    last_backticks = cleaned_result.rfind('```')
                    if last_backticks > first_newline:
                        # 提取代码块内容
                        cleaned_result = cleaned_result[first_newline+1:last_backticks].strip()
                        logger.info(f"检测到Markdown代码块格式，已提取内部JSON内容")
            
            try:
                # 首先尝试直接将整个响应解析为JSON
                recognition_data = json.loads(cleaned_result)
                logger.info("成功直接解析完整JSON")
            except json.JSONDecodeError:
                logger.warning("直接解析JSON失败，尝试提取JSON部分")
                
                # 使用正则表达式提取JSON数据部分
                json_match = re.search(r'\{.*\}', cleaned_result, re.DOTALL)
                
                if json_match:
                    json_data = json_match.group()
                    try:
                        # 尝试直接解析JSON数据
                        recognition_data = json.loads(json_data)
                        logger.info("成功提取并解析JSON部分")
                    except json.JSONDecodeError as e:
                        logger.error(f"解析JSON失败: {str(e)}")
                        
                        # 开始一系列修复尝试
                        try:
                            # 0. 先记录原始JSON内容用于诊断
                            logger.debug(f"修复前的JSON内容(前500字符): {json_data[:500]}")
                            
                            # 1. 替换可能的单引号为双引号
                            fixed_json = json_data.replace("'", "\"")
                            
                            # 2. 确保属性名有双引号 - 增强正则表达式匹配更多模式
                            # 匹配没有引号的键或使用单引号的键
                            fixed_json = re.sub(r'([{,]\s*)([a-zA-Z0-9_]+)(\s*:)', r'\1"\2"\3', fixed_json)
                            fixed_json = re.sub(r'([{,]\s*)\'([a-zA-Z0-9_]+)\'(\s*:)', r'\1"\2"\3', fixed_json)
                            
                            # 2.1 修复 "property name: value" 格式为 "property_name": value
                            fixed_json = re.sub(r'"([^"]+)"(\s*):([^,}]+)', r'"\1"\2:\3', fixed_json)
                            
                            # 3. 确保属性值中的布尔值是小写
                            fixed_json = re.sub(r':\s*True', r':true', fixed_json)
                            fixed_json = re.sub(r':\s*False', r':false', fixed_json)
                            fixed_json = re.sub(r':\s*None', r':null', fixed_json)
                            
                            # 4. 移除末尾多余的逗号
                            fixed_json = re.sub(r',\s*}', '}', fixed_json)
                            fixed_json = re.sub(r',\s*]', ']', fixed_json)
                            
                            # 5. 修复转义问题
                            fixed_json = fixed_json.replace('\\"', '"')
                            fixed_json = re.sub(r'(?<!\\)\\(?!["\\/bfnrt]|u[0-9a-fA-F]{4})', r'\\\\', fixed_json)
                            
                            # 6. 检查和修复未闭合的括号和引号
                            open_braces = fixed_json.count('{')
                            close_braces = fixed_json.count('}')
                            open_brackets = fixed_json.count('[')
                            close_brackets = fixed_json.count(']')
                            
                            # 添加缺失的闭合括号
                            if open_braces > close_braces:
                                fixed_json += "}" * (open_braces - close_braces)
                            if open_brackets > close_brackets:
                                fixed_json += "]" * (open_brackets - close_brackets)
                            
                            # 记录修复后的JSON
                            logger.debug(f"修复后的JSON内容(前500字符): {fixed_json[:500]}")
                            
                            # 7. 尝试解析修复后的JSON
                            try:
                                recognition_data = json.loads(fixed_json)
                                logger.info("成功修复并解析JSON数据")
                            except json.JSONDecodeError as inner_e:
                                logger.warning(f"第一次修复尝试失败: {str(inner_e)}，尝试更强力的修复")
                                
                                # 8. 如果仍然失败，尝试使用第三方库进行修复
                                try:
                                    # 尝试使用一个自定义的修复功能
                                    fixed_json = FoodRecognitionService._advanced_json_repair(fixed_json, str(inner_e))
                                    recognition_data = json.loads(fixed_json)
                                    logger.info("使用高级修复成功解析JSON")
                                except Exception as repair_error:
                                    logger.error(f"高级JSON修复失败: {str(repair_error)}")
                                    
                                    # 9. 尝试提取部分有效数据
                                    try:
                                        partial_data = FoodRecognitionService._extract_partial_json(json_data)
                                        if partial_data:
                                            recognition_data = partial_data
                                            logger.info("成功提取部分有效JSON数据")
                                        else:
                                            # 使用默认数据
                                            recognition_data = {
                                                "meal_name": "未能完全识别的餐食",
                                                "food_item_list": [],
                                                "health_recommendation": "无法正确解析AI识别结果。"
                                            }
                                            logger.error("所有JSON修复尝试均失败，使用默认数据")
                                    except Exception as extract_error:
                                        logger.error(f"提取部分JSON数据失败: {str(extract_error)}")
                                        # 使用默认数据
                                        recognition_data = {
                                            "meal_name": "未能完全识别的餐食",
                                            "food_item_list": [],
                                            "health_recommendation": "无法正确解析AI识别结果。"
                                        }
                                
                        except Exception as fix_error:
                            logger.error(f"修复JSON失败: {str(fix_error)}")
                            recognition_data = {
                                "meal_name": "未能识别的餐食",
                                "food_item_list": [],
                                "health_recommendation": "无法解析AI识别结果。",
                                "error": "Failed to parse JSON"
                            }
                else:
                    # 这里是正确的位置，只有在没有匹配到JSON时才执行
                    logger.error("未找到JSON格式数据")
                    recognition_data = {
                        "meal_name": "未能识别的餐食",
                        "food_item_list": [],
                        "health_recommendation": "AI响应中未找到有效的JSON数据。",
                        "error": "No JSON data found"
                    }
            
            # 确保recognition_data结构完整
            if "food_item_list" not in recognition_data:
                logger.warning("识别结果中缺少food_item_list字段，添加空列表")
                recognition_data["food_item_list"] = []
            
            if "meal_name" not in recognition_data:
                logger.warning("识别结果中缺少meal_name字段，添加默认值")
                recognition_data["meal_name"] = f"{meal_type_str}餐食"
            
            if "health_recommendation" not in recognition_data:
                logger.warning("识别结果中缺少health_recommendation字段，添加默认值")
                recognition_data["health_recommendation"] = "这是一顿营养均衡的餐食。"
            
            # 处理识别结果
            food_items = []
            matched_foods_info = []
            
            # 打印识别出的食物列表，用于调试
            food_item_list = recognition_data.get("food_item_list", [])
            logger.info(f"识别出 {len(food_item_list)} 个食物项: {json.dumps([item.get('name', '未知') for item in food_item_list], ensure_ascii=False)}")
            
            for item in food_item_list:
                try:
                    food_name = item.get("name", "未知食物")
                    
                    # 预处理warning_scenes，确保它是字符串列表
                    warning_scenes_orig = item.get("warning_scenes", [])
                    warning_scenes = []
                    
                    # 更健壮地处理warning_scenes
                    if warning_scenes_orig:
                        if isinstance(warning_scenes_orig, list):
                            for scene in warning_scenes_orig:
                                if isinstance(scene, dict) and 'name' in scene:
                                    warning_scenes.append(scene['name'])
                                elif isinstance(scene, str):
                                    warning_scenes.append(scene)
                                # 忽略其他类型
                        elif isinstance(warning_scenes_orig, str):
                            # 如果是单个字符串，放入列表
                            warning_scenes = [warning_scenes_orig]
                        # 忽略其他类型
                    
                    # 确保所有数值字段都是有效的数字
                    try:
                        # 提取数值并确保它们是合法的浮点数
                        quantity = float(item.get("quantity", 1))
                        weight = float(item.get("weight", 100))
                        calory = float(item.get("calory", 0))
                        protein = float(item.get("protein", 0))
                        fat = float(item.get("fat", 0))
                        carbohydrate = float(item.get("carbohydrate", 0))
                        health_light = int(item.get("health_light", 5))
                    except (ValueError, TypeError):
                        # 如果转换失败，使用默认值
                        logger.warning(f"食物 '{food_name}' 的数值字段转换失败，使用默认值")
                        quantity = 1.0
                        weight = 100.0
                        calory = 0.0
                        protein = 0.0
                        fat = 0.0
                        carbohydrate = 0.0
                        health_light = 5
                    
                    # 确保所有数值有效
                    protein = max(0, protein)
                    fat = max(0, fat)
                    carbohydrate = max(0, carbohydrate)
                    calory = max(0, calory)
                    quantity = max(0.1, quantity)  # 数量至少为0.1
                    weight = max(1, weight)  # 重量至少为1g
                    health_light = max(1, min(10, health_light))  # 健康指数在1-10之间
                    
                    # 计算各营养素占比 - 根据能量供给计算
                    protein_fraction = 0
                    fat_fraction = 0
                    carb_fraction = 0
                    
                    # 计算总能量 = 蛋白质4千卡/g + 脂肪9千卡/g + 碳水4千卡/g
                    calculated_calory = protein * 4 + fat * 9 + carbohydrate * 4
                    
                    # 如果总能量与提供的calory差异过大(超过5%)，以计算的为准
                    if abs(calculated_calory - calory) > calory * 0.05 and calculated_calory > 0:
                        calory = calculated_calory
                        logger.info(f"食物 '{food_name}' 的热量值已重新计算: {calory} 千卡")
                    
                    if calory > 0:
                        protein_fraction = (protein * 4) / calory
                        fat_fraction = (fat * 9) / calory
                        carb_fraction = (carbohydrate * 4) / calory
                    else:
                        # 如果总量为0，使用默认平衡分布
                        protein_fraction = 0.3
                        fat_fraction = 0.3
                        carb_fraction = 0.4
                    
                    # 查找匹配的食物
                    matched_foods = crud.food.search_by_name(
                        db, name=food_name, limit=10,sort_by_hot=True
                    )
                    
                    # 打印匹配结果，用于调试
                    logger.info(f"食物 '{food_name}' 匹配到 {len(matched_foods)} 个数据库食物")
                    
                    is_matched = False
                    matched_food_id = None
                    food_details = None
                    nutritional_profile_data = None
                    
                    # 构建基础食物项对象，保留原始识别数据
                    food_item = FoodItemCreate(
                        name=food_name,
                        code=f"custom_{int(time.time())}_{food_name.replace(' ', '_')}",
                        quantity=quantity,
                        unit_name=item.get("unit_name", "份"),
                        weight=weight,
                        category=item.get("category", ""),
                        cuisine_type=item.get("cuisine_type", ""),
                        cuisine_type_detail=item.get("cuisine_type_detail", ""),
                        # 营养数据
                        calory=calory,
                        protein=protein,
                        fat=fat,
                        carbohydrate=carbohydrate,
                        # 计算的营养比例
                        protein_fraction=protein_fraction,
                        fat_fraction=fat_fraction,
                        carb_fraction=carb_fraction,
                        # 红绿灯评价
                        health_light=health_light,
                        lights=item.get("lights", []),
                        warnings=item.get("warnings", []),
                        warning_scenes=warning_scenes,  # 使用处理过的warning_scenes
                        # 是否外卖
                        is_takeout=bool(item.get("is_takeout", False)),
                        is_custom=True,  # 初始设为自定义项
                    )
                    
                    # 如果有匹配的食物，添加匹配信息、食物详情和营养档案
                    if matched_foods:
                        best_match = matched_foods[0]  # 最佳匹配
                        matched_food_id = best_match.id
                        is_matched = True
                        
                        # 更新is_custom标志
                        food_item.is_custom = False
                        
                        # 获取食物详细信息
                        food_details = {
                            "id": best_match.id,
                            "name": best_match.name,
                            "category": best_match.category if hasattr(best_match, 'category') else "",
                            "food_type": best_match.food_type if hasattr(best_match, 'food_type') else "",
                            "thumb_image_url": best_match.thumb_image_url if hasattr(best_match, 'thumb_image_url') else None
                        }
                        
                        # 从数据库中获取营养信息
                        nutritional_profile = best_match.nutritional_profile if hasattr(best_match, 'nutritional_profile') else None
                        if nutritional_profile:
                            # 处理nutritional_profile数据
                            nutritional_profile_data = {
                                "health_light": nutritional_profile.health_light if hasattr(nutritional_profile, 'health_light') else None,
                                "lights": nutritional_profile.lights if hasattr(nutritional_profile, 'lights') else [],
                                "warnings": nutritional_profile.warnings if hasattr(nutritional_profile, 'warnings') else [],
                                "warning_scenes": nutritional_profile.warning_scenes if hasattr(nutritional_profile, 'warning_scenes') else [],
                                "calory": nutritional_profile.calory if hasattr(nutritional_profile, 'calory') else 0,
                                "protein_fraction": nutritional_profile.protein_fraction if hasattr(nutritional_profile, 'protein_fraction') else 0,
                                "fat_fraction": nutritional_profile.fat_fraction if hasattr(nutritional_profile, 'fat_fraction') else 0,
                                "carb_fraction": nutritional_profile.carb_fraction if hasattr(nutritional_profile, 'carb_fraction') else 0,
                                "food_rank": nutritional_profile.food_rank if hasattr(nutritional_profile, 'food_rank') else 0
                            }
                        
                        # 如果有营养档案信息，计算基于重量的营养素信息
                        if nutritional_profile_data:
                            # 获取100g计算的基准热量
                            base_calory = nutritional_profile_data.get("calory", 0)
                            
                            # 根据食物实际重量计算热量和营养素
                            weight = weight  # 使用前面处理过的weight
                            weight_ratio = weight / 100
                            
                            # 计算基于重量的热量
                            scaled_calory = base_calory * weight_ratio
                            
                            # 根据营养素占比和总热量计算各营养素重量
                            pf = nutritional_profile_data.get("protein_fraction", 0)
                            ff = nutritional_profile_data.get("fat_fraction", 0)
                            cf = nutritional_profile_data.get("carb_fraction", 0)
                            
                            protein_weight = (scaled_calory * pf) / 4 if pf else 0
                            fat_weight = (scaled_calory * ff) / 9 if ff else 0
                            carb_weight = (scaled_calory * cf) / 4 if cf else 0
                            
                            # 添加匹配信息到food_item，保留原始recognition_name
                            # 注意：保留food_item的原始识别数据，只在matching_info中添加数据库匹配的数据
                            food_item.matching_info = {
                                "recognition_name": food_name,  # 原始识别的名称
                                "similarity": 0.95 if is_matched else 0.0,
                                "matched_id": best_match.id,
                                "matched_name": best_match.name,
                                "calory": scaled_calory,  # 重命名为db_calory表示是数据库的值
                                "protein": protein_weight,  # 重命名为db_protein
                                "fat": fat_weight,  # 重命名为db_fat
                                "carbohydrate": carb_weight,  # 重命名为db_carbohydrate
                                "original_calory": food_item.calory,  # 保留原始识别的热量
                                "original_protein": food_item.protein,  # 保留原始识别的蛋白质
                                "original_fat": food_item.fat,  # 保留原始识别的脂肪
                                "original_carbohydrate": food_item.carbohydrate  # 保留原始识别的碳水
                            }
                        else:
                            # 添加匹配信息到food_item（无营养档案时），保留原始数据
                            food_item.matching_info = {
                                "recognition_name": food_name,
                                "similarity": 0.95 if is_matched else 0.0,
                                "matched_id": best_match.id,
                                "matched_name": best_match.name,
                                "calory": food_item.calory,
                                "protein": food_item.protein,
                                "fat": food_item.fat,
                                "carbohydrate": food_item.carbohydrate
                            }
                        
                        # 添加食物详情，但不修改food_item的原始识别数据
                        food_item.food_details = food_details
                        
                        # 添加营养档案，作为附加信息，不替换原始识别数据
                        food_item.nutritional_profile = nutritional_profile_data
                    else:
                        # 如果没有匹配的食物，添加默认的匹配信息，使用原始识别数据
                        food_item.matching_info = {
                            "recognition_name": food_name,
                            "similarity": 0.0,
                            "original_calory": food_item.calory,
                            "original_protein": food_item.protein,
                            "original_fat": food_item.fat,
                            "original_carbohydrate": food_item.carbohydrate
                        }
                        food_item.food_details = None
                        food_item.nutritional_profile = {
                            "health_light": health_light,
                            "lights": item.get("lights", []),
                            "warnings": item.get("warnings", []),
                            "warning_scenes": warning_scenes,
                            "calory": calory,
                            "protein_fraction": protein_fraction,
                            "fat_fraction": fat_fraction,
                            "carb_fraction": carb_fraction,
                            "food_rank": 0
                        }
                    
                    # 添加到食物列表
                    food_items.append(food_item)
                    
                    # 保存匹配信息
                    matched_foods_info.append({
                        "name": food_name,
                        "is_matched": is_matched,
                        "food_id": matched_food_id,
                        "alternatives": [
                            {
                                "id": food.id,
                                "name": food.name, 
                                "calory": food.nutritional_profile.calory if hasattr(food, 'nutritional_profile') and food.nutritional_profile else 0
                            } 
                            for food in matched_foods[:5]  # 取前5个替代选项
                        ] if matched_foods else []
                    })
                    
                    # 记录匹配信息
                    logger.info(f"食物 '{food_name}' 的匹配信息: is_matched={is_matched}, food_id={matched_food_id}, alternatives={len(matched_foods[:5]) if matched_foods else 0}个")
                
                except Exception as e:
                    logger.error(f"处理食物项时出错: {str(e)}")
                    continue
            
            # 如果没有识别出食物项，添加一个默认项
            if not food_items:
                logger.warning("没有识别出任何食物项，添加默认食物项")
                
                # 默认热量计算
                default_calory = 200
                default_protein_fraction = 0.14  # 蛋白质能量占比14%
                default_fat_fraction = 0.29      # 脂肪能量占比29%
                default_carb_fraction = 0.57     # 碳水能量占比57%
                
                # 根据能量比例计算各营养素重量
                default_protein = (default_calory * default_protein_fraction) / 4  # 约7g
                default_fat = (default_calory * default_fat_fraction) / 9  # 约6.4g
                default_carb = (default_calory * default_carb_fraction) / 4  # 约28.5g
                
                default_food_item = FoodItemCreate(
                    name="未识别食物",
                    code="unknown_food",  # 添加必需的code字段
                    quantity=1.0,
                    unit_name="份",
                    weight=100,
                    calory=default_calory,
                    protein=default_protein,
                    fat=default_fat,
                    carbohydrate=default_carb,
                    protein_fraction=default_protein_fraction,
                    fat_fraction=default_fat_fraction,
                    carb_fraction=default_carb_fraction,
                    is_custom=True,
                    is_takeout=False,
                    # 新增字段
                    matching_info={
                        "recognition_name": "未识别食物",
                        "similarity": 0.0,
                        "calory": default_calory,
                        "protein": default_protein,
                        "fat": default_fat,
                        "carbohydrate": default_carb
                    },
                    food_details=None,
                    nutritional_profile={
                        "health_light": 5,
                        "lights": [],
                        "warnings": ["未能识别食物"],
                        "warning_scenes": [],
                        "calory": default_calory,
                        "protein_fraction": default_protein_fraction,
                        "fat_fraction": default_fat_fraction,
                        "carb_fraction": default_carb_fraction,
                        "food_rank": 0
                    }
                )
                
                food_items.append(default_food_item)
                
                matched_foods_info.append({
                    "name": "未识别食物",
                    "is_matched": False,
                    "food_id": None,
                    "alternatives": []
                })
            
            # 计算总营养值
            try:
                total_calory = sum(item.calory * item.quantity for item in food_items)
                total_protein = sum(item.protein * item.quantity for item in food_items)
                total_fat = sum(item.fat * item.quantity for item in food_items)
                total_carbohydrate = sum(item.carbohydrate * item.quantity for item in food_items)
                
                nutrition_totals = {
                    "total_calory": total_calory,
                    "total_protein": total_protein,
                    "total_fat": total_fat,
                    "total_carbohydrate": total_carbohydrate
                }
            except Exception as e:
                logger.error(f"计算总营养值失败: {str(e)}")
                # 如果计算失败，使用默认值
                nutrition_totals = {
                    "total_calory": 0,
                    "total_protein": 0,
                    "total_fat": 0,
                    "total_carbohydrate": 0
                }
            
            # 记录处理时间
            processing_time = time.time() - start_time
            logger.info(f"AI食物识别完成，处理时间: {processing_time:.2f}秒")
            
            # 确保所有食物项都有code字段
            food_items_with_code = []
            for item in food_items:
                try:
                    if not item.code:
                        # 使用 dict() 方法获取现有属性
                        item_dict = item.dict()
                        # 使用名称和时间戳生成唯一code
                        item_dict["code"] = f"auto_{int(time.time())}_{item.name.replace(' ', '_')}"
                        # 创建新的对象替代旧对象
                        food_items_with_code.append(FoodItemCreate(**item_dict))
                    else:
                        food_items_with_code.append(item)
                except Exception as e:
                    logger.error(f"处理食物项code时出错: {str(e)}")
                    # 如果处理失败，尝试使用最基本的方法添加code
                    try:
                        if hasattr(item, 'dict'):
                            item_dict = item.dict()
                            item_dict["code"] = f"auto_{int(time.time())}_{item_dict.get('name', 'unknown').replace(' ', '_')}"
                            food_items_with_code.append(FoodItemCreate(**item_dict))
                        else:
                            # 如果item甚至没有dict方法，创建一个新的基本项目
                            food_items_with_code.append(FoodItemCreate(
                                name="未知食物项",
                                code=f"auto_{int(time.time())}_unknown",
                                quantity=1.0,
                                unit_name="份",
                                weight=100,
                                calory=0,
                                protein=0,
                                fat=0,
                                carbohydrate=0,
                                is_custom=True
                            ))
                    except Exception as inner_e:
                        logger.error(f"创建备用食物项失败: {str(inner_e)}")
                        # 极端情况下，跳过此项
            
            # 使用新的食物项列表
            food_items = food_items_with_code
            
            # 返回处理后的数据
            response_data = {
                "meal_name": recognition_data.get("meal_name", ""),
                "food_items": food_items,
                "health_recommendation": recognition_data.get("health_recommendation", ""),
                "nutrition_totals": nutrition_totals,
                "matched_foods": matched_foods_info,
                "processing_time": processing_time
            }
            
            # 记录大模型调用日志
            try:
                # 构建响应数据，包含模型输出和处理后的结果
                response_data_for_log = {
                    "raw_content": original_response,  # 使用保存的原始响应
                    "parsed_data": recognition_data,
                    "processed_result": {
                        "meal_name": recognition_data.get("meal_name", ""),
                        "food_items_count": len(food_items),  # 为了避免食物项对象可能引起的序列化问题，只传递数量
                        "health_recommendation": recognition_data.get("health_recommendation", ""),
                        "nutrition_totals": nutrition_totals,
                        "matched_foods_count": len(matched_foods_info),  # 只记录数量，避免序列化问题
                        "processing_time": processing_time
                    },
                    # 为了确保日志服务有正确的数据处理，添加这些直接字段
                    "food_items": len(food_items),  # 使用整数表示数量
                    "matched_foods": len(matched_foods_info)  # 使用整数表示数量
                }
                
                # 准备元数据
                metadata = {
                    "service_name": service,
                    "model_name": model_name,
                    "api_base_url": base_url,
                    "image_url": image_url,
                    "meal_type": meal_type_str,
                    "processing_status": "success",
                    "food_items_count": len(food_items),
                    "json_parse_success": True
                }
                
                # 异步记录日志，不阻塞主流程
                await LLMLogService.log_llm_call(
                    user_id=user_id,
                    request_data=original_request,  # 使用保存的原始请求
                    response_data=response_data_for_log,
                    image_path=image_url,
                    base64_image=None,
                    meal_type=meal_type_str,
                    image_data=None,
                    processing_time=processing_time,
                    metadata=metadata,
                    log_type="processed_response"  # 标记为处理后的响应日志
                )
            except Exception as e:
                logger.error(f"记录大模型调用日志失败: {str(e)}")
                # 日志记录失败不影响主流程继续执行
            
            return response_data
            
        except Exception as e:
            logger.error(f"AI识别失败: {str(e)}")
            
            # 记录错误日志
            try:
                # 构建错误响应数据
                error_response = {
                    "error": str(e),
                    "error_type": type(e).__name__,
                    "status": "error"
                }
                
                # 准备元数据
                metadata = {
                    "service_name": service if 'service' in locals() else "unknown",
                    "model_name": model_name if 'model_name' in locals() else "unknown",
                    "api_base_url": base_url if 'base_url' in locals() else "unknown",
                    "image_url": image_url,
                    "meal_type": meal_type.value if hasattr(meal_type, 'value') else str(meal_type),
                    "processing_status": "error",
                    "error_details": str(e)
                }
                
                # 尝试记录请求信息
                request_info = {}
                if 'request_log' in locals():
                    request_info = request_log
                elif 'original_request' in locals():
                    request_info = original_request
                
                # 尝试记录原始响应
                original_content = None
                if 'original_response' in locals() and original_response:
                    original_content = original_response
                elif 'result' in locals() and result:
                    original_content = result
                
                if original_content:
                    error_response["raw_content"] = original_content
                
                # 为确保与成功时的数据结构一致，添加一些安全的默认值
                error_response["food_items"] = 0  # 表示0个食物项
                error_response["matched_foods"] = 0  # 表示0个匹配食物
                    
                # 异步记录错误日志
                await LLMLogService.log_llm_call(
                    user_id=user_id,
                    request_data=request_info,
                    response_data=error_response,
                    image_path=image_url,
                    base64_image=None,
                    meal_type=meal_type.value if hasattr(meal_type, 'value') else str(meal_type),
                    image_data=None,
                    processing_time=time.time() - start_time,
                    metadata=metadata,
                    log_type="error_response"  # 标记为错误响应日志
                )
            except Exception as log_error:
                logger.error(f"记录AI识别错误日志失败: {str(log_error)}")
                # 日志记录失败不影响错误处理继续执行
            
            # 异常情况下，返回默认食物
            # 默认热量计算
            default_calory = 200
            default_protein_fraction = 0.14  # 蛋白质能量占比14%
            default_fat_fraction = 0.29      # 脂肪能量占比29%
            default_carb_fraction = 0.57     # 碳水能量占比57%
            
            # 根据能量比例计算各营养素重量
            default_protein = (default_calory * default_protein_fraction) / 4  # 约7g
            default_fat = (default_calory * default_fat_fraction) / 9  # 约6.4g
            default_carb = (default_calory * default_carb_fraction) / 4  # 约28.5g
            
            default_food_item = FoodItemCreate(
                name="未识别食物",
                code="unknown_food",  # 添加必需的code字段
                quantity=1.0,
                unit_name="份",
                weight=100,
                calory=default_calory,
                protein=default_protein,
                fat=default_fat,
                carbohydrate=default_carb,
                protein_fraction=default_protein_fraction,
                fat_fraction=default_fat_fraction,
                carb_fraction=default_carb_fraction,
                is_custom=True,
                is_takeout=False,
                # 新增字段
                matching_info={
                    "recognition_name": "未识别食物",
                    "similarity": 0.0,
                    "calory": default_calory,
                    "protein": default_protein,
                    "fat": default_fat,
                    "carbohydrate": default_carb
                },
                food_details=None,
                nutritional_profile={
                    "health_light": 5,
                    "lights": [],
                    "warnings": ["未能识别食物"],
                    "warning_scenes": [],
                    "calory": default_calory,
                    "protein_fraction": default_protein_fraction,
                    "fat_fraction": default_fat_fraction,
                    "carb_fraction": default_carb_fraction,
                    "food_rank": 0
                }
            )
            
            return {
                "meal_name": f"{meal_type.value if hasattr(meal_type, 'value') else str(meal_type)}",
                "food_items": [default_food_item],
                "health_recommendation": "无法分析此图片，请尝试使用更清晰的食物图片。",
                "nutrition_totals": {
                    "total_calory": default_calory,
                    "total_protein": default_protein,
                    "total_fat": default_fat,
                    "total_carbohydrate": default_carb
                },
                "matched_foods": [
                    {
                        "name": "未识别食物",
                        "is_matched": False,
                        "food_id": None,
                        "alternatives": []
                    }
                ],
                "processing_time": time.time() - start_time
            } 

    @staticmethod
    def _advanced_json_repair(json_str, error_msg):
        """高级JSON修复，根据错误消息进行有针对性的修复"""
        logger.info(f"执行高级JSON修复，错误: {error_msg}")
        
        # 针对特定错误的修复策略
        if "Expecting property name enclosed in double quotes" in error_msg:
            # 提取错误位置
            match = re.search(r'line (\d+) column (\d+)', error_msg)
            if match:
                line_num, col_num = int(match.group(1)), int(match.group(2))
                
                # 将JSON字符串分行
                lines = json_str.split('\n')
                
                # 确保有足够的行
                if line_num <= len(lines):
                    # 获取出错的行
                    error_line = lines[line_num - 1]
                    
                    # 查找问题位置并修复
                    if col_num <= len(error_line):
                        # 从错误位置向前搜索，找到可能的属性名开始位置
                        prop_start = col_num - 1
                        while prop_start > 0 and error_line[prop_start-1].isalnum() or error_line[prop_start-1] == '_':
                            prop_start -= 1
                        
                        # 提取可能的属性名
                        prop_name = error_line[prop_start:col_num]
                        
                        # 在属性名周围添加双引号
                        fixed_line = error_line[:prop_start] + f'"{prop_name}"' + error_line[col_num:]
                        
                        # 更新JSON字符串
                        lines[line_num - 1] = fixed_line
                        json_str = '\n'.join(lines)
                        logger.info(f"在第{line_num}行第{col_num}列修复了未引用的属性名: {prop_name}")
        
        # 尝试更多高级修复策略
        # 1. 替换所有未引用的属性名
        json_str = re.sub(r'([{,]\s*)([a-zA-Z0-9_]+)(\s*:)', r'\1"\2"\3', json_str)
        
        # 2. 修复可能的JSON转义问题
        json_str = json_str.replace('\\"', '"').replace('\\\\', '\\')
        
        # 3. 修复Unicode转义序列
        json_str = re.sub(r'\\u([0-9a-fA-F]{4})', lambda m: chr(int(m.group(1), 16)), json_str)
        
        return json_str

    @staticmethod
    def _extract_partial_json(json_str):
        """尝试从损坏的JSON中提取部分有效数据"""
        logger.info("尝试从损坏的JSON中提取部分有效数据")
        
        partial_data = {"food_item_list": []}
        
        # 尝试提取餐食名称
        meal_name_match = re.search(r'"meal_name"\s*:\s*"([^"]+)"', json_str)
        if meal_name_match:
            partial_data["meal_name"] = meal_name_match.group(1)
        else:
            partial_data["meal_name"] = "部分识别的餐食"
        
        # 尝试提取健康建议
        health_rec_match = re.search(r'"health_recommendation"\s*:\s*"([^"]+)"', json_str)
        if health_rec_match:
            partial_data["health_recommendation"] = health_rec_match.group(1)
        else:
            partial_data["health_recommendation"] = "无法完全解析健康建议。"
        
        # 尝试提取食物项
        # 查找food_item_list数组的起始和结束位置
        list_start = json_str.find('"food_item_list"')
        if list_start > -1:
            # 找到数组开始的位置
            array_start = json_str.find('[', list_start)
            if array_start > -1:
                # 计算匹配的括号来找到数组结束的位置
                bracket_count = 1
                array_end = -1
                
                for i in range(array_start + 1, len(json_str)):
                    if json_str[i] == '[':
                        bracket_count += 1
                    elif json_str[i] == ']':
                        bracket_count -= 1
                        if bracket_count == 0:
                            array_end = i
                            break
                
                if array_end > -1:
                    # 提取食物项数组
                    food_items_str = json_str[array_start:array_end+1]
                    
                    # 尝试解析单个食物项
                    try:
                        # 先尝试直接解析整个数组
                        food_items = json.loads(food_items_str)
                        if isinstance(food_items, list):
                            partial_data["food_item_list"] = food_items
                    except json.JSONDecodeError:
                        # 如果整个数组解析失败，尝试提取单个食物项
                        food_items = []
                        
                        # 提取每个可能的食物项对象
                        item_matches = re.finditer(r'\{[^{}]*\}', food_items_str)
                        for item_match in item_matches:
                            try:
                                item_str = item_match.group()
                                # 修复可能的JSON格式问题
                                item_str = re.sub(r'([{,]\s*)([a-zA-Z0-9_]+)(\s*:)', r'\1"\2"\3', item_str)
                                food_item = json.loads(item_str)
                                if isinstance(food_item, dict) and "name" in food_item:
                                    food_items.append(food_item)
                            except json.JSONDecodeError:
                                continue
                        
                        partial_data["food_item_list"] = food_items
        
        return partial_data 