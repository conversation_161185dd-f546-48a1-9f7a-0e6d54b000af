"""
模型服务 - 提供统一的模型管理和调用接口
"""
from typing import Dict, Any, Optional, List, Union, AsyncGenerator
import logging
import asyncio
from functools import lru_cache
from app.core.model_config import (
    MODEL_MAPPING, MODEL_TYPES, INTENT_MODEL_MAPPING,
    CACHE_CONFIG, PROVIDER_FALLBACK, BAILIAN_APP_MAPPING
)
from app.core.chat_config import PROVIDERS, BAILIAN_APPS
from app.core.config import settings
from http import HTTPStatus
from dashscope import Application

logger = logging.getLogger(__name__)

class ModelService:
    """模型服务，提供统一的模型管理和调用接口"""

    def __init__(self, llm_proxy_service):
        """初始化模型服务

        Args:
            llm_proxy_service: LLM代理服务
        """
        self.llm_proxy = llm_proxy_service

        # 缓存配置
        self.cache_enabled = CACHE_CONFIG["enabled"]
        self.cache_ttl = CACHE_CONFIG["ttl"]
        self.max_cache_size = CACHE_CONFIG["max_size"]

        # 模型实例缓存
        self.model_cache = {}
        self.cache_timestamps = {}

        logger.info("模型服务初始化完成")

    def get_model_config(self, model_type: str) -> Dict[str, Any]:
        """获取模型配置

        Args:
            model_type: 模型类型，如"intent_recognition"、"conversation"等

        Returns:
            模型配置字典
        """
        if model_type in MODEL_MAPPING:
            config = MODEL_MAPPING[model_type].copy()
            # 添加类型特定配置
            if model_type in MODEL_TYPES:
                config.update(MODEL_TYPES[model_type])
            return config
        else:
            # 默认返回对话模型配置
            logger.warning(f"未找到模型类型 {model_type} 的配置，使用默认对话模型配置")
            return MODEL_MAPPING["conversation"]

    def get_model_for_intent(self, intent: str) -> str:
        """根据意图获取合适的模型类型

        Args:
            intent: 意图名称

        Returns:
            模型类型
        """
        # 直接从model_config.py导入INTENT_MODEL_MAPPING
        from app.core.model_config import INTENT_MODEL_MAPPING
        model_type = INTENT_MODEL_MAPPING.get(intent, "conversation")
        logger.info(f"为意图 {intent} 选择模型类型: {model_type}")
        return model_type

    @lru_cache(maxsize=32)
    def get_provider_config(self, provider_name: str) -> Dict[str, Any]:
        """获取提供商配置

        Args:
            provider_name: 提供商名称

        Returns:
            提供商配置字典
        """
        if provider_name in PROVIDERS:
            return PROVIDERS[provider_name]
        else:
            logger.warning(f"未找到提供商 {provider_name} 的配置，使用默认提供商配置")
            return PROVIDERS["qwen"]

    def get_bailian_app_id(self, app_name: str) -> str:
        """获取百炼应用ID

        Args:
            app_name: 应用名称

        Returns:
            应用ID
        """
        if app_name in BAILIAN_APPS:
            return BAILIAN_APPS[app_name]["app_id"]
        else:
            logger.warning(f"未找到百炼应用 {app_name} 的配置，使用默认健身教练应用")
            return BAILIAN_APPS["fitness-coach-app"]["app_id"]

    def get_bailian_app_for_model_type(self, model_type: str) -> str:
        """根据模型类型获取对应的百炼应用名称

        Args:
            model_type: 模型类型

        Returns:
            百炼应用名称
        """
        return BAILIAN_APP_MAPPING.get(model_type, "fitness-coach-app")

    async def get_response(self,
                         messages: List[Dict[str, str]],
                         model_type: str = None,
                         intent: str = None,
                         temperature: float = None,
                         streaming: bool = False,
                         use_bailian: bool = None,
                         meta_info: Dict[str, Any] = None,
                         **kwargs) -> Union[str, AsyncGenerator[str, None]]:
        """获取模型响应

        Args:
            messages: 消息列表
            model_type: 模型类型，如"conversation"、"fitness_advice"等
            intent: 意图名称，如果提供则根据意图选择模型类型
            temperature: 温度参数
            streaming: 是否使用流式响应
            use_bailian: 是否优先使用百炼应用，None表示自动选择
            meta_info: 元数据信息，用于保存模型调用参数
            **kwargs: 其他参数

        Returns:
            模型响应文本或流式生成器
        """
        # 确定模型类型
        if intent and not model_type:
            model_type = self.get_model_for_intent(intent)
        elif not model_type:
            model_type = "conversation"

        # 获取模型配置
        config = self.get_model_config(model_type)
        model_name = config["default"]

        # 确定温度参数
        if temperature is None:
            temperature = config.get("default_temperature", 0.7)

        # 确定是否使用流式响应
        if streaming is None:
            streaming = config.get("streaming", True)

        # 特殊处理：训练计划生成模型的思考模式
        if model_type == "training_plan" and config.get("thinking_mode", False):
            kwargs["enable_thinking"] = True

        # 确定是否使用百炼应用
        if use_bailian is None:
            # 根据模型类型的默认提供商决定
            use_bailian = config.get("provider") == "bailian"

        # 如果使用百炼应用
        if use_bailian:
            try:
                bailian_app = self.get_bailian_app_for_model_type(model_type)
                app_id = self.get_bailian_app_id(bailian_app)

                # 尝试使用百炼应用
                logger.info(f"尝试使用百炼应用: {bailian_app}, app_id={app_id}")

                # 保存百炼应用信息到元数据
                if meta_info is not None:
                    # 确保llm_params字段存在
                    if "llm_params" not in meta_info:
                        meta_info["llm_params"] = {}

                    # 保存app_id
                    meta_info["llm_params"]["app_id"] = app_id
                    meta_info["llm_params"]["bailian_app"] = bailian_app
                    logger.info(f"保存百炼应用信息到元数据: app_id={app_id}, bailian_app={bailian_app}")

                if streaming:
                    # 流式响应 - 直接返回异步生成器
                    return self._stream_bailian_response(
                        messages=messages,
                        app_id=app_id,
                        model_type=model_type,
                        meta_info=meta_info,
                        **kwargs
                    )
                else:
                    # 非流式响应
                    return await self._get_bailian_response(
                        messages=messages,
                        app_id=app_id,
                        model_type=model_type,
                        meta_info=meta_info,
                        **kwargs
                    )
            except Exception as e:
                logger.error(f"使用百炼应用失败: {str(e)}，尝试使用原生API")
                # 失败后尝试使用原生API

        # 使用原生API
        if streaming:
            logger.info(f"使用流式响应，模型类型: {model_type}, 模型名称: {model_name}")
            # 直接返回异步生成器，不需要await
            return self.llm_proxy.stream_chat(
                messages=messages,
                model=model_name,
                temperature=temperature,
                **kwargs
            )
        else:
            logger.info(f"使用非流式响应，模型类型: {model_type}, 模型名称: {model_name}")
            # 记录模型调用信息
            if meta_info is not None:
                if "llm_params" not in meta_info:
                    meta_info["llm_params"] = {}
                meta_info["llm_params"]["model_type"] = model_type
                meta_info["llm_params"]["model_name"] = model_name
                meta_info["llm_params"]["model_id"] = model_name  # 使用model_name作为model_id
                meta_info["llm_params"]["temperature"] = temperature
                logger.info(f"保存模型信息到元数据: model_type={model_type}, model_name={model_name}, model_id={model_name}")

            return await self.llm_proxy.aget_chat_response(
                messages=messages,
                model=model_name,
                temperature=temperature,
                **kwargs
            )
    async def _get_bailian_response(self, messages: List[Dict[str, str]], app_id: str, model_type: str, meta_info: Dict[str, Any] = None, **kwargs) -> str:
        """获取百炼应用的非流式响应

        Args:
            messages: 消息列表
            app_id: 百炼应用ID
            model_type: 模型类型
            meta_info: 元数据信息，用于保存模型调用参数
            **kwargs: 其他参数

        Returns:
            响应文本
        """
        try:
            # 获取用户消息
            user_message = None
            for msg in messages:
                if msg["role"] == "user":
                    user_message = msg["content"]
                    break

            if not user_message:
                return "无法获取用户消息"

            # 根据不同的应用构建不同的提示词
            prompt = user_message

            # 导入提示词服务
            from app.services.prompt_service import PromptService

            # 根据模型类型选择不同的提示词处理
            if model_type == "intent_recognition":
                prompt = PromptService.get_intent_recognition_prompt(user_message)
            elif model_type == "agent":
                prompt = PromptService.get_agent_app_prompt(user_message)
            elif model_type == "fitness_advice":
                prompt = PromptService.get_fitness_advice_prompt(user_message)
            elif model_type == "nutrition_advice":
                # 获取用户信息
                user_info_str = ""
                if "user_info" in kwargs:
                    user_info = kwargs.get("user_info", {})
                    user_info_str = "\n".join([f"- {k}: {v}" for k, v in user_info.items() if k != "user_id"])
                    logger.info(f"为nutrition_advice模型添加用户信息: {user_info_str[:100]}...")
                elif meta_info and "user_info" in meta_info:
                    user_info = meta_info.get("user_info", {})
                    user_info_str = "\n".join([f"- {k}: {v}" for k, v in user_info.items() if k != "user_id"])
                    logger.info(f"从meta_info获取用户信息: {user_info_str[:100]}...")

                prompt = PromptService.get_nutrition_advice_prompt(
                    user_message=user_message,
                    user_info=f"用户信息:\n{user_info_str}" if user_info_str else ""
                )

            # 获取API密钥
            provider_config = self.get_provider_config("bailian")
            api_key = str(provider_config["api_key"])

            # 调用百炼应用
            logger.info(f"调用百炼应用 app_id={app_id}, 模型类型={model_type}")

            # 使用dashscope库调用百炼应用
            response = Application.call(
                api_key=api_key,
                app_id=app_id,
                prompt=prompt,
                **kwargs
            )

            if response.status_code != HTTPStatus.OK:
                error_msg = f"百炼应用调用失败: code={response.status_code}, message={response.message}"
                logger.error(error_msg)
                return error_msg

            result = response.output.text
            logger.info(f"百炼应用调用成功: request_id={response.request_id}")

            # 保存session_id和model_id到元数据
            if meta_info is not None:
                if "llm_params" not in meta_info:
                    meta_info["llm_params"] = {}

                # 保存session_id
                if hasattr(response.output, "session_id"):
                    session_id = response.output.session_id
                    meta_info["llm_params"]["session_id"] = session_id
                    logger.info(f"保存session_id到元数据: session_id={session_id}")

                # 保存model_id - 从response.usage.models[0].model_id获取
                if hasattr(response, "usage") and hasattr(response.usage, "models") and len(response.usage.models) > 0:
                    model = response.usage.models[0]
                    if hasattr(model, "model_id"):
                        model_id = model.model_id
                        meta_info["llm_params"]["model_id"] = model_id
                        logger.info(f"从usage.models[0]保存model_id到元数据: model_id={model_id}")
                # 备用方法
                elif hasattr(response.output, "model_id"):
                    model_id = response.output.model_id
                    meta_info["llm_params"]["model_id"] = model_id
                    logger.info(f"保存model_id到元数据: model_id={model_id}")
                elif hasattr(response, "model_id"):
                    model_id = response.model_id
                    meta_info["llm_params"]["model_id"] = model_id
                    logger.info(f"保存model_id到元数据: model_id={model_id}")
                elif hasattr(response, "model"):
                    model_id = response.model
                    meta_info["llm_params"]["model_id"] = model_id
                    logger.info(f"保存model到元数据: model={model_id}")

            return result
        except Exception as e:
            error_msg = f"调用百炼应用时出错: {str(e)}"
            logger.error(error_msg)
            raise

    async def _stream_bailian_response(self, messages: List[Dict[str, str]], app_id: str, model_type: str, meta_info: Dict[str, Any] = None, **kwargs) -> AsyncGenerator[str, None]:
        """获取百炼应用的流式响应

        Args:
            messages: 消息列表
            app_id: 百炼应用ID
            model_type: 模型类型
            meta_info: 元数据信息，用于保存模型调用参数
            **kwargs: 其他参数

        Returns:
            流式响应生成器
        """
        try:
            # 获取用户消息
            user_message = None
            for msg in messages:
                if msg["role"] == "user":
                    user_message = msg["content"]
                    break

            if not user_message:
                yield "无法获取用户消息"
                return

            # 根据不同的应用构建不同的提示词
            prompt = user_message

            # 导入提示词服务
            from app.services.prompt_service import PromptService

            # 根据模型类型选择不同的提示词处理
            if model_type == "intent_recognition":
                prompt = PromptService.get_intent_recognition_prompt(user_message)
            elif model_type == "agent":
                prompt = PromptService.get_agent_app_prompt(user_message)
            elif model_type == "fitness_advice":
                prompt = PromptService.get_fitness_advice_prompt(user_message)
            elif model_type == "nutrition_advice":
                # 获取用户信息
                user_info_str = ""
                if "user_info" in kwargs:
                    user_info = kwargs.get("user_info", {})
                    user_info_str = "\n".join([f"- {k}: {v}" for k, v in user_info.items() if k != "user_id"])
                    logger.info(f"为nutrition_advice模型添加用户信息: {user_info_str[:100]}...")
                elif meta_info and "user_info" in meta_info:
                    user_info = meta_info.get("user_info", {})
                    user_info_str = "\n".join([f"- {k}: {v}" for k, v in user_info.items() if k != "user_id"])
                    logger.info(f"从meta_info获取用户信息: {user_info_str[:100]}...")

                prompt = PromptService.get_nutrition_advice_prompt(
                    user_message=user_message,
                    user_info=f"用户信息:\n{user_info_str}" if user_info_str else ""
                )

            # 获取API密钥
            provider_config = self.get_provider_config("bailian")
            api_key = str(provider_config["api_key"])

            # 调用百炼应用
            logger.info(f"流式调用百炼应用 app_id={app_id}, 模型类型={model_type}")

            # 使用dashscope库调用百炼应用
            response = Application.call(
                api_key=api_key,
                app_id=app_id,
                prompt=prompt,
                **kwargs
            )

            if response.status_code != HTTPStatus.OK:
                error_msg = f"百炼应用调用失败: code={response.status_code}, message={response.message}"
                logger.error(error_msg)
                yield error_msg
                return

            # 模拟流式输出
            result = response.output.text
            chunk_size = 10  # 每个块的大小
            for i in range(0, len(result), chunk_size):
                chunk = result[i:i+chunk_size]
                # 添加一个小延迟，模拟真实的流式输出
                await asyncio.sleep(0.01)
                yield chunk

            logger.info(f"百炼应用流式调用成功: request_id={response.request_id}")

            # 保存session_id和model_id到元数据
            if meta_info is not None:
                if "llm_params" not in meta_info:
                    meta_info["llm_params"] = {}

                # 保存session_id
                if hasattr(response.output, "session_id"):
                    session_id = response.output.session_id
                    meta_info["llm_params"]["session_id"] = session_id
                    logger.info(f"保存session_id到元数据: session_id={session_id}")

                    # 发送元数据更新事件
                    yield {
                        "type": "meta_info_update",
                        "meta_info_update": {
                            "llm_params": {"session_id": session_id}
                        }
                    }

                # 保存model_id - 从response.usage.models[0].model_id获取
                if hasattr(response, "usage") and hasattr(response.usage, "models") and len(response.usage.models) > 0:
                    model = response.usage.models[0]
                    if hasattr(model, "model_id"):
                        model_id = model.model_id
                        meta_info["llm_params"]["model_id"] = model_id
                        logger.info(f"从usage.models[0]保存model_id到元数据: model_id={model_id}")

                        # 发送元数据更新事件
                        yield {
                            "type": "meta_info_update",
                            "meta_info_update": {
                                "llm_params": {"model_id": model_id}
                            }
                        }
                # 备用方法
                elif hasattr(response.output, "model_id"):
                    model_id = response.output.model_id
                    meta_info["llm_params"]["model_id"] = model_id
                    logger.info(f"保存model_id到元数据: model_id={model_id}")

                    # 发送元数据更新事件
                    yield {
                        "type": "meta_info_update",
                        "meta_info_update": {
                            "llm_params": {"model_id": model_id}
                        }
                    }
                elif hasattr(response, "model_id"):
                    model_id = response.model_id
                    meta_info["llm_params"]["model_id"] = model_id
                    logger.info(f"保存model_id到元数据: model_id={model_id}")

                    # 发送元数据更新事件
                    yield {
                        "type": "meta_info_update",
                        "meta_info_update": {
                            "llm_params": {"model_id": model_id}
                        }
                    }
                elif hasattr(response, "model"):
                    model_id = response.model
                    meta_info["llm_params"]["model_id"] = model_id
                    logger.info(f"保存model到元数据: model={model_id}")

                    # 发送元数据更新事件
                    yield {
                        "type": "meta_info_update",
                        "meta_info_update": {
                            "llm_params": {"model_id": model_id}
                        }
                    }

        except Exception as e:
            error_msg = f"流式调用百炼应用时出错: {str(e)}"
            logger.error(error_msg)
            yield error_msg

    def clear_cache(self, model_type: Optional[str] = None):
        """清除模型缓存

        Args:
            model_type: 要清除的模型类型，如果为None则清除所有缓存
        """
        if model_type is None:
            logger.info("清除所有模型缓存")
            self.model_cache.clear()
            self.cache_timestamps.clear()
        elif model_type in self.model_cache:
            logger.info(f"清除模型缓存: {model_type}")
            del self.model_cache[model_type]
            if model_type in self.cache_timestamps:
                del self.cache_timestamps[model_type]
