"""
LLM 服务 - 处理 LLM 调用和响应处理
"""
import logging
from typing import Dict, Any, List, Optional, Type
from pydantic import BaseModel
from langchain.output_parsers import PydanticOutputParser

from app.services.llm_proxy_service import LLMProxyService
from app.helpers.json_helper import <PERSON>sonHel<PERSON>
from app.utils.training_plan_utils import extract_json_from_response

logger = logging.getLogger(__name__)

class LLMService:
    """LLM 服务类，用于处理 LLM 调用和响应处理"""

    def __init__(self, llm_proxy_service: LLMProxyService):
        """
        初始化 LLM 服务

        Args:
            llm_proxy_service: LLM 代理服务
        """
        self.llm_proxy_service = llm_proxy_service

    async def generate_structured_data(
        self,
        system_prompt: str,
        user_prompt: str,
        schema_model: Type[BaseModel],
        temperature: float = 0.2,
        enable_thinking: bool = True
    ) -> BaseModel:
        """
        生成结构化数据

        Args:
            system_prompt: 系统提示词
            user_prompt: 用户提示词
            schema_model: Pydantic 模型类
            temperature: 温度参数

        Returns:
            结构化数据对象
        """
        # 创建输出解析器
        parser = PydanticOutputParser(pydantic_object=schema_model)

        # 调用 LLM
        # 从配置中获取训练计划生成模型
        from app.core.config import settings
        exercise_generation_model = settings.LLM_EXERCISE_GENERATION_MODEL

        logger.info(f"调用 LLM 生成结构化数据，模型: {schema_model.__name__}, 使用训练计划生成模型: {exercise_generation_model}")

        # 准备额外参数
        extra_params = {}
        if enable_thinking:
            logger.info("启用思考模式")
            extra_params["extra_body"] = {"enable_thinking": True}

        # 调用 LLM
        response = await self.llm_proxy_service.aget_chat_response(
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            model=exercise_generation_model,
            temperature=temperature,
            **extra_params
        )

        logger.info(f"LLM 返回响应，长度: {len(response)}")

        try:
            # 预处理响应，尝试提取有效的 JSON 部分
            processed_response = extract_json_from_response(response)
            logger.debug(f"处理后的 JSON 响应: {processed_response[:200]}...")  # 只记录前200个字符

            # 解析结构化输出
            result = parser.parse(processed_response)
            logger.info(f"成功解析 JSON 为 {schema_model.__name__} 对象")

            return result
        except Exception as e:
            logger.error(f"解析 LLM 响应失败: {str(e)}")
            logger.error(f"原始响应: {response[:500]}...")  # 只记录前500个字符
            raise ValueError(f"生成结构化数据失败: {str(e)}")

    def create_exercise_info_prompt(self, candidate_exercises: List[Dict[str, Any]]) -> str:
        """
        创建候选动作信息提示

        Args:
            candidate_exercises: 候选动作列表

        Returns:
            候选动作信息提示
        """
        if not candidate_exercises:
            return ""

        exercise_info = "可选的训练动作（LLM必须从这些动作中选择）:\n"
        for i, ex in enumerate(candidate_exercises, 1):
            muscles_str = ", ".join(ex.get("muscles", []))
            equip_str = ", ".join(ex.get("equipment", []))
            body_parts_str = ", ".join(ex.get("body_parts", []))
            exercise_info += (
                f"{i}. ID: {ex['id']}, 名称: {ex['name']}\n"
                f"   部位: {body_parts_str}\n"
                f"   部位ID: {ex.get('body_part_id')}\n"
                f"   肌肉: {muscles_str}\n"
                f"   器材: {equip_str}\n"
                f"   难度: {ex.get('level', 2)}\n\n"
            )

        return exercise_info
