from typing import Dict, Any, Optional
from sqlalchemy.orm import Session
from app.crud.crud_qa_pair import crud_qa_pair
import logging

logger = logging.getLogger(__name__)

class ChatLogService:
    def __init__(self, db: Session):
        self.db = db
    
    def log_interaction(
        self,
        user_id: int,
        session_id: Optional[str],
        question: str,
        answer: str,
        intent: Optional[str] = None,
        retrieved_docs: Optional[Dict] = None,
        tool_used: Optional[str] = None,
        meta_info: Optional[Dict] = None
    ) -> None:
        """记录用户与LLM的聊天交互"""
        try:
            qa_pair = crud_qa_pair.create(
                self.db,
                obj_in={
                    "user_id": user_id,
                    "session_id": session_id,
                    "question": question,
                    "answer": answer,
                    "intent": intent,
                    "retrieved_docs": retrieved_docs,
                    "tool_used": tool_used,
                    "meta_info": meta_info
                }
            )
            logger.info(f"成功记录QA对, ID: {qa_pair.id}, Session: {session_id}")
        except Exception as e:
            logger.error(f"记录聊天交互失败: {e}")
            # 不中断操作，只记录错误 