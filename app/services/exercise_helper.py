from typing import List, Dict, Any

async def get_candidate_exercises(
    sql_service,
    body_part,
    scenario=None,
    difficulty_range=None,
    min_count=3,  # 每个部位最少动作数
    limit=10,     # 每个部位最多返回的动作数
) -> List[Dict[str, Any]]:
    """获取候选训练动作列表
    
    Args:
        sql_service: SQL工具服务实例
        body_part: 身体部位ID(int)或名称(str)
        scenario: 训练场景（可选）
        difficulty_range: 难度范围(dict, min和max)
        min_count: 每个部位至少返回的动作数量
        limit: 每个部位最多返回的动作数量
        
    Returns:
        候选训练动作列表
    """
    logger.info(f"请求候选训练动作: body_part={body_part}, scenario={scenario}, difficulty_range={difficulty_range}, limit={limit}")
    
    search_exercises_tool = sql_service.search_exercises_tool()
    
    # 如果直接提供了整数ID，则直接使用
    if isinstance(body_part, int):
        logger.info(f"使用身体部位ID: {body_part} 直接查询训练动作")
        candidates = await search_exercises_tool.ainvoke(body_part=body_part, scenario=scenario, limit=limit)
        logger.info(f"为身体部位ID {body_part} 找到 {len(candidates)} 个动作")
    else:
        # 如果是字符串名称，则正常传递
        logger.info(f"使用身体部位名称: {body_part} 查询训练动作")
        candidates = await search_exercises_tool.ainvoke(body_part=body_part, scenario=scenario, limit=limit)
        logger.info(f"为身体部位 '{body_part}' 找到 {len(candidates)} 个动作")

    # 记录获取结果
    if candidates:
        logger.info(f"获取到 {len(candidates)} 个候选动作")
        for i, ex in enumerate(candidates[:min(3, len(candidates))]):  # 只记录前3个
            exercise_id = ex.get("exercise_id") if "exercise_id" in ex else ex.get("id")
            exercise_name = ex.get("exercise_name") if "exercise_name" in ex else ex.get("name")
            logger.info(f"候选动作 {i+1}: ID={exercise_id}, 名称={exercise_name}")
    else:
        logger.warning(f"未找到任何符合条件的候选动作: body_part={body_part}, scenario={scenario}")
    
    # 标准化动作字段名称
    standardized_candidates = []
    for ex in candidates:
        # 确保所有返回的动作都有标准化的字段名称
        standardized_ex = {
            "id": ex.get("exercise_id") if "exercise_id" in ex else ex.get("id"),
            "name": ex.get("exercise_name") if "exercise_name" in ex else ex.get("name"),
            "description": ex.get("description", ""),
            "body_part_id": ex.get("body_part_id"),
            "body_parts": [ex.get("body_part_name")] if ex.get("body_part_name") else [],
            "equipment": [ex.get("equipment_name")] if ex.get("equipment_name") else [],
            "equipment_id": ex.get("equipment_id"),
            "muscles": [ex.get("muscle_name")] if ex.get("muscle_name") else [],
            "level": ex.get("difficulty", 2)  # 使用difficulty作为level
        }
        standardized_candidates.append(standardized_ex)
    
    # 进行个性化筛选
    if difficulty_range and len(standardized_candidates) > min_count:
        logger.info(f"应用难度筛选: {difficulty_range}")
        min_difficulty = difficulty_range.get('min', 1)
        max_difficulty = difficulty_range.get('max', 5)
        
        # 筛选难度范围内的动作
        filtered_by_difficulty = [
            ex for ex in standardized_candidates 
            if min_difficulty <= ex.get('level', 3) <= max_difficulty
        ]
        
        # 如果筛选后数量不足，则补充一些接近范围的动作
        if len(filtered_by_difficulty) < min_count:
            logger.warning(f"难度范围筛选后剩余 {len(filtered_by_difficulty)} 个动作，少于最小要求 {min_count}")
            
            # 对原始列表按照难度排序
            sorted_candidates = sorted(standardized_candidates, key=lambda x: abs(x.get('level', 3) - (min_difficulty + max_difficulty) / 2))
            
            # 补充到最小数量
            while len(filtered_by_difficulty) < min_count and sorted_candidates:
                next_candidate = sorted_candidates.pop(0)
                if next_candidate not in filtered_by_difficulty:
                    filtered_by_difficulty.append(next_candidate)
            
            logger.info(f"补充后的动作数量: {len(filtered_by_difficulty)}")
        
        standardized_candidates = filtered_by_difficulty
    
    # 确保返回足够的动作
    if len(standardized_candidates) < min_count:
        logger.warning(f"候选动作数量({len(standardized_candidates)})少于最小要求({min_count})")
    
    # 限制返回数量
    result = standardized_candidates[:limit]
    logger.info(f"最终返回 {len(result)} 个候选动作")
    
    return result 