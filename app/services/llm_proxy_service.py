from typing import Dict, Any, Optional, List, AsyncGenerator, Callable, Union
from langchain_community.chat_models import ChatOpenAI
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.language_models import BaseChatModel
from app.core.config import settings
import os
import logging
import time
import asyncio
from http import HTTPStatus
from dashscope import Application
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import json
import traceback
from functools import wraps
import random

logger = logging.getLogger(__name__)

# 定义重试装饰器
def with_llm_retry(max_retries=3, initial_delay=1, exponential_base=2, jitter=0.1):
    """
    重试装饰器，适用于LLM API调用
    
    Args:
        max_retries: 最大重试次数
        initial_delay: 初始延迟时间（秒）
        exponential_base: 指数退避的底数
        jitter: 随机抖动范围
    """
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            delay = initial_delay
            last_exception = None
            
            for retry in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    
                    # 判断是否可以重试的错误
                    if isinstance(e, (requests.RequestException, asyncio.TimeoutError)) or "rate limit" in str(e).lower():
                        # 可重试的错误
                        if retry < max_retries:
                            # 添加随机抖动
                            jitter_value = delay * jitter * (2 * (0.5 - random.random()))
                            sleep_time = delay + jitter_value
                            
                            logger.warning(f"LLM调用失败 (尝试 {retry+1}/{max_retries}): {str(e)}. 将在 {sleep_time:.2f}秒后重试")
                            await asyncio.sleep(sleep_time)
                            
                            # 指数退避
                            delay *= exponential_base
                        else:
                            # 超过最大重试次数
                            logger.error(f"LLM调用失败，超过最大重试次数 {max_retries}: {str(e)}")
                            raise
                    else:
                        # 不可重试的错误
                        logger.error(f"LLM调用发生不可重试的错误: {str(e)}")
                        raise
            
            # 如果所有重试都失败
            raise last_exception
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            delay = initial_delay
            last_exception = None
            
            for retry in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    
                    # 判断是否可以重试的错误
                    if isinstance(e, requests.RequestException) or "rate limit" in str(e).lower():
                        # 可重试的错误
                        if retry < max_retries:
                            # 添加随机抖动
                            jitter_value = delay * jitter * (2 * (0.5 - random.random()))
                            sleep_time = delay + jitter_value
                            
                            logger.warning(f"LLM调用失败 (尝试 {retry+1}/{max_retries}): {str(e)}. 将在 {sleep_time:.2f}秒后重试")
                            time.sleep(sleep_time)
                            
                            # 指数退避
                            delay *= exponential_base
                        else:
                            # 超过最大重试次数
                            logger.error(f"LLM调用失败，超过最大重试次数 {max_retries}: {str(e)}")
                            raise
                    else:
                        # 不可重试的错误
                        logger.error(f"LLM调用发生不可重试的错误: {str(e)}")
                        raise
            
            # 如果所有重试都失败
            raise last_exception
        
        # 根据函数是否为异步选择不同的包装器
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


class LLMProxyService:
    """大语言模型代理服务，统一处理不同LLM提供商的API调用"""

    def __init__(self):
        """初始化LLM代理服务"""
        self.openai_api_key = settings.OPENAI_API_KEY
        self.qwen_api_key = settings.QWEN_API_KEY
        self.qwen_api_base = settings.QWEN_API_BASE

        # 创建一个会话对象，配置重试策略
        self.session = requests.Session()
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["POST"]
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("https://", adapter)

        # 默认模型映射
        self.default_models = {
            "openai": settings.DEFAULT_LLM_MODEL,
            "gpt-3.5-turbo": "gpt-3.5-turbo",
            "gpt-4": "gpt-4",
            "qwen": "qwen-turbo",
            "qwen-turbo": "qwen-turbo",
            "qwen-plus": "qwen-plus",
            "qwen-max": "qwen-max"
        }

        # 错误计数器 - 用于跟踪模型错误状态
        self.error_counters = {}
        self.max_errors = 3  # 某个模型连续失败次数达到此值将被标记为不可用
        self.model_recovery_time = 300  # 模型恢复时间（秒）
        self.unavailable_models = {}  # 不可用模型及其恢复时间的映射

        # 初始化默认聊天模型
        # 注意：这里需要在导入循环依赖之前初始化chat_model属性
        self.chat_model = None

        # 在初始化完成后，初始化默认聊天模型
        self._initialize_default_chat_model()

    def _initialize_default_chat_model(self):
        """初始化默认聊天模型

        这个方法在__init__方法的最后被调用，用于初始化默认的聊天模型
        """
        try:
            # 导入配置，避免循环导入
            from app.core.chat_config import MODELS, MODEL_PARAMS, PROVIDERS

            # 使用默认模型创建聊天模型实例，确保是字符串类型
            default_model = str(MODELS.get("default", "qwen-turbo"))

            # 确保温度参数是浮点数类型
            default_temp = float(MODEL_PARAMS.get("default_temperature", 0.7))

            # 确保streaming参数是布尔类型
            is_streaming = bool(MODEL_PARAMS.get("streaming", True))

            # 根据模型名称选择不同的配置
            if "deepseek" in default_model.lower():
                # 使用DeepSeek配置
                logger.info(f"初始化默认DeepSeek模型: {default_model}")
                self.chat_model = ChatOpenAI(
                    model=default_model,
                    temperature=default_temp,
                    openai_api_key=str(PROVIDERS["deepseek"]["api_key"]),
                    openai_api_base=str(PROVIDERS["deepseek"]["api_base"]),
                    streaming=is_streaming,
                    request_timeout=90  # 增加超时时间到90秒
                )
            else:
                # 使用默认配置 (千问)
                logger.info(f"初始化默认千问模型: {default_model}")
                self.chat_model = ChatOpenAI(
                    model=default_model,
                    temperature=default_temp,
                    openai_api_key=str(PROVIDERS["qwen"]["api_key"]),
                    openai_api_base=str(PROVIDERS["qwen"]["api_base"]),
                    streaming=is_streaming,
                    request_timeout=90  # 增加超时时间到90秒
                )
        except Exception as e:
            # 如果初始化失败，记录错误但不中断程序
            logger.error(f"初始化默认聊天模型失败: {str(e)}")
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            # 设置为None，后续方法会检查并处理
            self.chat_model = None

    def is_model_available(self, model_name: str) -> bool:
        """检查模型是否可用（未被标记为不可用）"""
        if model_name in self.unavailable_models:
            recovery_time = self.unavailable_models[model_name]
            # 检查恢复时间是否已到
            if time.time() >= recovery_time:
                # 恢复时间已到，从不可用列表中移除
                del self.unavailable_models[model_name]
                # 重置错误计数
                if model_name in self.error_counters:
                    self.error_counters[model_name] = 0
                logger.info(f"模型 {model_name} 已恢复可用状态")
                return True
            else:
                # 恢复时间未到，仍然不可用
                return False
        
        # 未在不可用列表中，可用
        return True
    
    def mark_model_error(self, model_name: str) -> None:
        """标记模型发生错误"""
        if model_name not in self.error_counters:
            self.error_counters[model_name] = 1
        else:
            self.error_counters[model_name] += 1
        
        # 如果连续错误次数达到阈值，标记为不可用
        if self.error_counters[model_name] >= self.max_errors:
            recovery_time = time.time() + self.model_recovery_time
            self.unavailable_models[model_name] = recovery_time
            logger.warning(f"模型 {model_name} 连续失败达到阈值，标记为不可用，将在 {self.model_recovery_time} 秒后恢复")
    
    def mark_model_success(self, model_name: str) -> None:
        """标记模型调用成功，重置错误计数"""
        if model_name in self.error_counters:
            self.error_counters[model_name] = 0

    # 使用重试装饰器
    @with_llm_retry(max_retries=2)
    async def acompletion_with_retry(self, model, messages, **kwargs):
        """带有重试逻辑的异步完成请求"""
        try:
            response = await model.agenerate([messages], **kwargs)
            self.mark_model_success(model.model_name)
            return response
        except Exception as e:
            self.mark_model_error(model.model_name)
            raise

    def select_fallback_model(self, model_name: str) -> str:
        """根据失败的模型选择备选模型"""
        from app.core.model_config import PROVIDER_FALLBACK
        
        # 获取模型类型
        model_type = None
        for mtype, models in PROVIDER_FALLBACK.items():
            if model_name in models:
                model_type = mtype
                break
        
        if not model_type:
            # 默认使用对话模型类型
            model_type = "conversation"
        
        # 获取该类型的备选模型列表
        fallback_models = PROVIDER_FALLBACK.get(model_type, ["qwen-turbo"])
        
        # 找到第一个可用的备选模型
        for fallback_model in fallback_models:
            if fallback_model != model_name and self.is_model_available(fallback_model):
                logger.info(f"为 {model_name} 选择备选模型: {fallback_model}")
                return fallback_model
        
        # 如果没有找到可用的备选模型，返回默认模型
        logger.warning(f"没有可用的备选模型，使用默认模型 qwen-turbo")
        return "qwen-turbo"

    def create_llm(self,
                  model: str = None,
                  temperature: float = None,
                  streaming: bool = True,
                  **kwargs) -> BaseChatModel:
        """创建LLM实例，可以指定模型、温度等参数"""

        # 导入配置，避免循环导入
        from app.core.chat_config import MODELS, MODEL_PARAMS, PROVIDERS

        # 使用默认值处理None参数
        model_name = model or MODELS.get("default", "qwen-turbo")
        temp = temperature if temperature is not None else MODEL_PARAMS.get("default_temperature", 0.7)
        stream = streaming
        
        # 检查模型是否可用，如果不可用则选择备选模型
        if not self.is_model_available(model_name):
            fallback_model = self.select_fallback_model(model_name)
            logger.warning(f"模型 {model_name} 不可用，使用备选模型 {fallback_model}")
            model_name = fallback_model
        
        # 确保温度参数在有效范围内
        if temp < 0.0:
            temp = 0.0
        elif temp > 1.0:
            temp = 1.0
            
        # 确保这些值都是正确的类型
        model_name = str(model_name)
        temp = float(temp)
        stream = bool(stream)
        
        # 增加默认的请求超时设置
        if 'request_timeout' not in kwargs:
            kwargs['request_timeout'] = 90  # 默认90秒超时

        # 根据模型名称选择不同的配置
        if "deepseek" in model_name.lower():
            # 使用DeepSeek配置
            logger.info(f"使用DeepSeek模型: {model_name}")
            return ChatOpenAI(
                model=model_name,
                temperature=temp,
                openai_api_key=str(PROVIDERS["deepseek"]["api_key"]),
                openai_api_base=str(PROVIDERS["deepseek"]["api_base"]),
                streaming=stream,
                **kwargs
            )
        elif "bailian" in model_name.lower() or model_name in PROVIDERS.get("bailian", {}).get("models", []):
            # 使用百炼应用配置
            logger.info(f"使用百炼应用模型: {model_name}")
            from langchain_community.chat_models import ChatDashScope
            
            return ChatDashScope(
                model=model_name,
                api_key=str(PROVIDERS["bailian"]["api_key"]),
                streaming=stream,
                **kwargs
            )
        else:
            # 使用默认配置 (千问)
            logger.info(f"使用千问模型: {model_name}")
            return ChatOpenAI(
                model=model_name,
                temperature=temp,
                openai_api_key=str(PROVIDERS["qwen"]["api_key"]),
                openai_api_base=str(PROVIDERS["qwen"]["api_base"]),
                streaming=stream,
                **kwargs
            )

    def get_llm(self,
               model: str = None,
               temperature: float = None,
               streaming: bool = False,
               **kwargs) -> BaseChatModel:
        """获取LLM实例，可以指定模型、温度等参数

        如果不指定参数，则使用默认参数创建新实例（非流式）
        这个方法主要提供给Agent等组件使用
        """
        # 首先检查chat_model是否为None
        if self.chat_model is None:
            logger.warning("默认聊天模型未初始化，创建新实例")
            return self.create_llm(
                model=model,
                temperature=temperature,
                streaming=streaming,
                **kwargs
            )

        # 当需要非默认参数的LLM时，创建新实例
        if model or temperature is not None or kwargs:
            return self.create_llm(
                model=model,
                temperature=temperature,
                streaming=streaming,
                **kwargs
            )

        # 如果默认流式模型不适合当前场景（比如Agent使用），创建非流式版本
        if self.chat_model.streaming and not streaming:
            return self.create_llm(streaming=False)

        # 否则返回默认实例
        return self.chat_model

    def invoke(self, prompt: str, **kwargs) -> str:
        """单一文本输入的LLM调用"""
        try:
            # 确保chat_model已初始化
            if self.chat_model is None:
                logger.warning("聊天模型未初始化，尝试初始化")
                self._initialize_default_chat_model()

                # 如果仍然为None，创建一个新的实例
                if self.chat_model is None:
                    logger.warning("初始化默认聊天模型失败，创建新实例")
                    self.chat_model = self.create_llm(streaming=False)

            messages = [HumanMessage(content=prompt)]
            response = self.chat_model.invoke(messages, **kwargs)
            return response.content
        except Exception as e:
            logger.error(f"LLM调用失败: {e}")
            # 标记错误
            if self.chat_model:
                self.mark_model_error(self.chat_model.model_name)
                
                # 尝试使用备选模型
                fallback_model_name = self.select_fallback_model(self.chat_model.model_name)
                if fallback_model_name != self.chat_model.model_name:
                    try:
                        logger.info(f"尝试使用备选模型 {fallback_model_name} 重试请求")
                        fallback_model = self.create_llm(model=fallback_model_name, streaming=False)
                        response = fallback_model.invoke(messages, **kwargs)
                        return response.content
                    except Exception as fallback_e:
                        logger.error(f"备选模型调用也失败: {fallback_e}")
            
            # 如果所有尝试都失败，返回一个友好的错误消息
            return f"抱歉，AI服务暂时不可用。我们正在解决这个问题，请稍后再试。"

    @with_llm_retry(max_retries=2)
    async def ainvoke(self, prompt: str, **kwargs) -> str:
        """异步单一文本输入的LLM调用"""
        try:
            # 确保chat_model已初始化
            if self.chat_model is None:
                logger.warning("聊天模型未初始化，尝试初始化")
                self._initialize_default_chat_model()

                # 如果仍然为None，创建一个新的实例
                if self.chat_model is None:
                    logger.warning("初始化默认聊天模型失败，创建新实例")
                    self.chat_model = self.create_llm(streaming=False)

            messages = [HumanMessage(content=prompt)]
            response = await self.chat_model.ainvoke(messages, **kwargs)
            
            # 标记成功
            if self.chat_model:
                self.mark_model_success(self.chat_model.model_name)
                
            return response.content
        except Exception as e:
            logger.error(f"异步LLM调用失败: {e}")
            # 标记错误
            if self.chat_model:
                self.mark_model_error(self.chat_model.model_name)
                
                # 尝试使用备选模型
                fallback_model_name = self.select_fallback_model(self.chat_model.model_name)
                if fallback_model_name != self.chat_model.model_name:
                    try:
                        logger.info(f"尝试使用备选模型 {fallback_model_name} 重试异步请求")
                        fallback_model = self.create_llm(model=fallback_model_name, streaming=False)
                        response = await fallback_model.ainvoke(messages, **kwargs)
                        return response.content
                    except Exception as fallback_e:
                        logger.error(f"备选模型异步调用也失败: {fallback_e}")
            
            # 如果所有尝试都失败，返回一个友好的错误消息
            return f"抱歉，AI服务暂时不可用。我们正在解决这个问题，请稍后再试。"

    async def async_streaming_invoke(self, prompt: str, **kwargs) -> AsyncGenerator[str, None]:
        """异步流式LLM调用，返回生成器"""
        from langchain.callbacks.streaming_aiter import AsyncIteratorCallbackHandler
        
        callback = AsyncIteratorCallbackHandler()
        model = self.get_llm(streaming=True, callbacks=[callback])
        
        messages = [HumanMessage(content=prompt)]
        
        # 启动任务但不等待
        task = asyncio.create_task(model.ainvoke(messages, **kwargs))
        
        # 设置任务完成或取消的标志
        task_complete = False
        
        try:
            # 从回调处理器获取响应token
            async for token in callback.aiter():
                yield token
            
            # 等待任务完成
            await task
            self.mark_model_success(model.model_name)
            task_complete = True
        except Exception as e:
            logger.error(f"异步流式LLM调用失败: {e}")
            self.mark_model_error(model.model_name)
            
            # 如果任务仍在运行，取消它
            if not task.done():
                task.cancel()
                
            # 尝试使用备选模型
            if not task_complete:
                fallback_model_name = self.select_fallback_model(model.model_name)
                if fallback_model_name != model.model_name:
                    try:
                        logger.info(f"尝试使用备选模型 {fallback_model_name} 重试流式请求")
                        
                        # 创建新的回调和模型
                        fallback_callback = AsyncIteratorCallbackHandler()
                        fallback_model = self.create_llm(
                            model=fallback_model_name, 
                            streaming=True, 
                            callbacks=[fallback_callback]
                        )
                        
                        # 启动新任务
                        fallback_task = asyncio.create_task(fallback_model.ainvoke(messages, **kwargs))
                        
                        # 将错误信息发送出去
                        yield "\n\n[AI服务暂时遇到问题，正在切换到备用服务...]"
                        
                        # 从备选模型获取响应
                        async for token in fallback_callback.aiter():
                            yield token
                            
                        # 等待任务完成
                        await fallback_task
                        return
                    except Exception as fallback_e:
                        logger.error(f"备选模型流式调用也失败: {fallback_e}")
            
            # 如果所有尝试都失败，返回一个友好的错误消息
            yield "\n\n抱歉，AI服务暂时不可用。我们正在解决这个问题，请稍后再试。"

    def get_chat_response(self,
                         messages: List[Dict[str, str]],
                         model: str = None,
                         temperature: float = None,
                         **kwargs) -> str:
        """处理聊天消息格式的请求，可选择指定不同的模型"""
        from app.core.chat_config import MODELS, MODEL_PARAMS

        try:
            # 转换消息格式为LangChain格式
            langchain_messages = []
            for msg in messages:
                if msg["role"] == "user":
                    langchain_messages.append(HumanMessage(content=msg["content"]))
                elif msg["role"] == "assistant":
                    langchain_messages.append(AIMessage(content=msg["content"]))
                elif msg["role"] == "system":
                    langchain_messages.append(SystemMessage(content=msg["content"]))

            # 根据需要选择模型
            if model or temperature is not None:
                # 如果是健身建议意图，使用百炼应用模型
                if model == "fitness_advice":
                    model = MODELS["fitness_advice"]

                chat_model = self.create_llm(
                    model=model,
                    temperature=temperature or MODEL_PARAMS["default_temperature"],
                    streaming=False
                )
            else:
                # 确保chat_model已初始化
                if self.chat_model is None:
                    logger.warning("聊天模型未初始化，尝试初始化")
                    self._initialize_default_chat_model()

                    # 如果仍然为None，创建一个新的实例
                    if self.chat_model is None:
                        logger.warning("初始化默认聊天模型失败，创建新实例")
                        self.chat_model = self.create_llm(streaming=False)

                chat_model = self.chat_model

            # 调用模型获取回复
            response = chat_model.invoke(langchain_messages)
            self.mark_model_success(chat_model.model_name)
            return response.content

        except Exception as e:
            logger.error(f"获取聊天回复失败: {str(e)}")
            # 标记错误
            if 'chat_model' in locals() and chat_model:
                self.mark_model_error(chat_model.model_name)
                
                # 尝试使用备选模型
                try:
                    fallback_model_name = self.select_fallback_model(chat_model.model_name)
                    if fallback_model_name != chat_model.model_name:
                        logger.info(f"尝试使用备选模型 {fallback_model_name} 重试聊天请求")
                        fallback_model = self.create_llm(model=fallback_model_name, streaming=False)
                        response = fallback_model.invoke(langchain_messages)
                        return response.content
                except Exception as fallback_e:
                    logger.error(f"备选模型聊天调用也失败: {fallback_e}")
            
            # 如果所有尝试都失败，返回一个友好的错误消息
            return "抱歉，AI服务暂时不可用。我们正在解决这个问题，请稍后再试。"

    async def aget_chat_response(self,
                               messages: List[Dict[str, str]],
                               model: str = None,
                               temperature: float = None,
                               **kwargs) -> str:
        """异步处理聊天消息格式的请求，可选择指定不同的模型
        
        Args:
            messages: 消息列表，每个消息应包含role和content字段
            model: 要使用的模型，可选
            temperature: 温度参数，可选
            **kwargs: 其他参数
            
        Returns:
            模型响应内容
        """
        from app.core.chat_config import MODELS, MODEL_PARAMS

        try:
            # 转换消息格式为LangChain格式
            langchain_messages = []
            for msg in messages:
                if msg["role"] == "user":
                    langchain_messages.append(HumanMessage(content=msg["content"]))
                elif msg["role"] == "assistant":
                    langchain_messages.append(AIMessage(content=msg["content"]))
                elif msg["role"] == "system":
                    langchain_messages.append(SystemMessage(content=msg["content"]))

            # 根据需要选择模型
            if model or temperature is not None:
                # 如果是健身建议意图，使用百炼应用模型
                if model == "fitness_advice":
                    model = MODELS["fitness_advice"]

                chat_model = self.create_llm(
                    model=model,
                    temperature=temperature or MODEL_PARAMS["default_temperature"],
                    streaming=False
                )
            else:
                # 确保chat_model已初始化
                if self.chat_model is None:
                    logger.warning("聊天模型未初始化，尝试初始化")
                    self._initialize_default_chat_model()

                    # 如果仍然为None，创建一个新的实例
                    if self.chat_model is None:
                        logger.warning("初始化默认聊天模型失败，创建新实例")
                        self.chat_model = self.create_llm(streaming=False)

                chat_model = self.chat_model

            # 异步调用LLM
            response = await chat_model.ainvoke(langchain_messages, **kwargs)
            
            # 标记成功
            self.mark_model_success(chat_model.model_name)
            
            return response.content
        
        except Exception as e:
            logger.error(f"异步获取聊天响应失败: {str(e)}")
            # 标记错误
            if 'chat_model' in locals() and chat_model:
                self.mark_model_error(chat_model.model_name)
                
                # 尝试使用备选模型
                try:
                    fallback_model_name = self.select_fallback_model(chat_model.model_name)
                    if fallback_model_name != chat_model.model_name:
                        logger.info(f"尝试使用备选模型 {fallback_model_name} 重试请求")
                        fallback_model = self.create_llm(model=fallback_model_name, streaming=False)
                        response = await fallback_model.ainvoke(langchain_messages, **kwargs)
                        return response.content
                except Exception as fallback_e:
                    logger.error(f"备选模型异步调用也失败: {fallback_e}")
            
            return f"抱歉，AI服务暂时不可用。我们正在解决这个问题，请稍后再试。"

    async def stream_chat_response(self,
                                 messages: List[Dict[str, str]],
                                 model: str = None,
                                 temperature: float = None) -> AsyncGenerator[str, None]:
        """流式处理聊天消息，返回异步生成器"""
        from app.core.chat_config import MODELS, MODEL_PARAMS
        from langchain.callbacks.streaming_aiter import AsyncIteratorCallbackHandler
        
        callback = AsyncIteratorCallbackHandler()
        
        try:
            # 转换消息格式为LangChain格式
            langchain_messages = []
            for msg in messages:
                if msg["role"] == "user":
                    langchain_messages.append(HumanMessage(content=msg["content"]))
                elif msg["role"] == "assistant":
                    langchain_messages.append(AIMessage(content=msg["content"]))
                elif msg["role"] == "system":
                    langchain_messages.append(SystemMessage(content=msg["content"]))

            # 根据需要选择模型
            if model or temperature is not None:
                chat_model = self.create_llm(
                    model=model,
                    temperature=temperature or MODEL_PARAMS["default_temperature"],
                    streaming=True,
                    callbacks=[callback]
                )
            else:
                # 创建流式模型实例
                chat_model = self.create_llm(
                    streaming=True,
                    callbacks=[callback]
                )

            # 启动任务但不等待
            task = asyncio.create_task(chat_model.ainvoke(langchain_messages))

            # 设置任务完成或取消的标志
            task_complete = False
            
            # 从回调处理器获取响应token
            async for token in callback.aiter():
                yield token
            
            # 等待任务完成
            await task
            self.mark_model_success(chat_model.model_name)
            task_complete = True

        except Exception as e:
            logger.error(f"流式聊天响应失败: {str(e)}")
            
            # 如果有chat_model，标记错误
            if 'chat_model' in locals() and chat_model:
                self.mark_model_error(chat_model.model_name)

                # 尝试使用备选模型
                if not task_complete:
                    try:
                        fallback_model_name = self.select_fallback_model(chat_model.model_name)
                        if fallback_model_name != chat_model.model_name:
                            logger.info(f"尝试使用备选模型 {fallback_model_name} 重试流式聊天请求")
                            
                            # 创建新的回调和模型
                            fallback_callback = AsyncIteratorCallbackHandler()
                            fallback_model = self.create_llm(
                                model=fallback_model_name, 
                                streaming=True, 
                                callbacks=[fallback_callback]
                            )
                            
                            # 启动新任务
                            fallback_task = asyncio.create_task(fallback_model.ainvoke(langchain_messages))
                            
                            # 将错误信息发送出去
                            yield "\n\n[AI服务暂时遇到问题，正在切换到备用服务...]"
                            
                            # 从备选模型获取响应
                            async for token in fallback_callback.aiter():
                                yield token
                                
                            # 等待任务完成
                            await fallback_task
                            return
                    except Exception as fallback_e:
                        logger.error(f"备选模型流式聊天调用也失败: {fallback_e}")
            
            # 如果所有尝试都失败，返回一个友好的错误消息
            yield "\n\n抱歉，AI服务暂时不可用。我们正在解决这个问题，请稍后再试。"