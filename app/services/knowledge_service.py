import logging
import os
from typing import Dict, Any, List, Optional
from langchain_community.vectorstores import FAISS
from langchain_openai import OpenAIEmbeddings
from langchain_core.documents import Document
import json

from ..core.config import settings

logger = logging.getLogger(__name__)

class KnowledgeService:
    """知识库服务 - 负责管理和查询基于向量存储的知识库"""
    
    def __init__(self):
        """初始化知识库服务"""
        self.vector_store_path = settings.VECTOR_STORE_PATH
        self.openai_api_key = settings.OPENAI_API_KEY
        self._vector_store = None
        
        # 确保向量存储目录存在
        os.makedirs(self.vector_store_path, exist_ok=True)
    
    @property
    def vector_store(self) -> Optional[FAISS]:
        """获取向量存储实例"""
        if self._vector_store is None:
            # 尝试加载现有的向量存储
            try:
                embeddings = OpenAIEmbeddings(openai_api_key=self.openai_api_key)
                vector_store_path = os.path.join(self.vector_store_path, "fitness_knowledge")
                
                if os.path.exists(vector_store_path):
                    logger.info(f"正在加载向量存储: {vector_store_path}")
                    self._vector_store = FAISS.load_local(
                        vector_store_path,
                        embeddings
                    )
                else:
                    logger.warning(f"向量存储未找到: {vector_store_path}")
                    # 创建空的向量存储
                    self._vector_store = FAISS.from_documents(
                        [Document(page_content="Fitness knowledge base placeholder")],
                        embeddings
                    )
                    # 保存向量存储
                    self._vector_store.save_local(vector_store_path)
            except Exception as e:
                logger.error(f"加载向量存储时出错: {str(e)}")
                return None
                
        return self._vector_store
    
    def query_knowledge(self, query: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """查询知识库
        
        Args:
            query: 查询文本
            top_k: 返回结果数量
            
        Returns:
            相关文档列表
        """
        try:
            if not self.vector_store:
                logger.error("向量存储未初始化")
                return []
                
            # 执行相似性搜索
            docs_and_scores = self.vector_store.similarity_search_with_score(query, k=top_k)
            
            # 格式化结果
            results = []
            for doc, score in docs_and_scores:
                results.append({
                    "content": doc.page_content,
                    "metadata": doc.metadata,
                    "score": float(score)
                })
                
            return results
        except Exception as e:
            logger.error(f"查询知识库时出错: {str(e)}")
            return []
    
    def add_to_knowledge(self, content: str, metadata: Dict[str, Any] = None) -> bool:
        """添加内容到知识库
        
        Args:
            content: 文本内容
            metadata: 元数据
            
        Returns:
            是否成功添加
        """
        try:
            if not self.vector_store:
                logger.error("向量存储未初始化")
                return False
                
            # 创建文档
            doc = Document(
                page_content=content,
                metadata=metadata or {}
            )
            
            # 添加到向量存储
            self.vector_store.add_documents([doc])
            
            # 保存向量存储
            vector_store_path = os.path.join(self.vector_store_path, "fitness_knowledge")
            self.vector_store.save_local(vector_store_path)
            
            return True
        except Exception as e:
            logger.error(f"添加内容到知识库时出错: {str(e)}")
            return False
    
    def load_knowledge_from_json(self, json_file_path: str) -> int:
        """从JSON文件加载知识
        
        Args:
            json_file_path: JSON文件路径
            
        Returns:
            添加的文档数量
        """
        try:
            if not os.path.exists(json_file_path):
                logger.error(f"JSON文件不存在: {json_file_path}")
                return 0
                
            # 加载JSON数据
            with open(json_file_path, 'r', encoding='utf-8') as file:
                data = json.load(file)
                
            if not isinstance(data, list):
                logger.error(f"JSON文件格式错误: {json_file_path}")
                return 0
                
            # 创建文档列表
            docs = []
            for item in data:
                if not isinstance(item, dict) or "content" not in item:
                    continue
                    
                content = item.get("content", "")
                metadata = item.get("metadata", {})
                
                docs.append(Document(
                    page_content=content,
                    metadata=metadata
                ))
                
            if not docs:
                logger.warning(f"JSON文件不包含有效文档: {json_file_path}")
                return 0
                
            # 创建或更新向量存储
            embeddings = OpenAIEmbeddings(openai_api_key=self.openai_api_key)
            
            vector_store_path = os.path.join(self.vector_store_path, "fitness_knowledge")
            if os.path.exists(vector_store_path) and self.vector_store:
                # 添加到现有向量存储
                self.vector_store.add_documents(docs)
            else:
                # 创建新的向量存储
                self._vector_store = FAISS.from_documents(docs, embeddings)
                
            # 保存向量存储
            self.vector_store.save_local(vector_store_path)
            
            logger.info(f"已加载 {len(docs)} 个文档到知识库")
            return len(docs)
        except Exception as e:
            logger.error(f"从JSON文件加载知识时出错: {str(e)}")
            return 0 