from typing import Dict, List, Any, Optional
import json

from app.services.state_definitions import ConversationState
from app.services.llm_proxy_service import LLMProxyService
from app.core.config import settings

# 全局LLM实例
llm_service = LLMProxyService()

# 训练参数映射
TRAINING_PARAMS_PROMPTS = {
    "training_days": "您一周能训练几天？",
    "available_time": "您每次训练大约有多少时间可用？(分钟)",
    "equipment_available": "您可以使用哪些训练器材？(如哑铃、杠铃、健身房等)",
    "body_parts_focus": "您希望重点训练哪些部位？(如胸部、背部、腿部等)",
    "training_goal": "您的训练目标是什么？(如增肌、减脂、增强力量等)"
}

# 必填参数
REQUIRED_PARAMS = ["training_days", "available_time", "equipment_available"]

async def param_collector_node(state: ConversationState) -> ConversationState:
    """参数收集节点：收集训练计划所需参数"""

    # 获取当前收集状态
    current_param = state.flow_state.get("asking_param")

    # 如果有用户回复，尝试解析当前参数
    if current_param:
        user_message = ""
        for msg in reversed(state.messages):
            if msg.role == "user":
                user_message = msg.content
                break

        # 解析用户回复
        if user_message:
            param_value = await _extract_param_value(current_param, user_message)
            if param_value:
                # 保存参数
                if "training_params" not in state.flow_state:
                    state.flow_state["training_params"] = {}
                state.flow_state["training_params"][current_param] = param_value
                state.training_params[current_param] = param_value

                # 清除当前参数，准备收集下一个
                state.flow_state["asking_param"] = None

    # 检查是否完成所有必要参数
    if _check_required_params_complete(state.training_params):
        # 标记参数收集完成
        state.flow_state["params_complete"] = True

        # 添加AI确认消息
        confirmation_msg = await _generate_confirmation_message(state.training_params)
        state.messages.append(confirmation_msg)

        return state

    # 找出下一个需要询问的参数
    next_param = _get_next_param_to_collect(state.training_params)

    if next_param:
        # 设置当前询问的参数
        state.flow_state["asking_param"] = next_param

        # 添加询问消息
        prompt_message = _create_prompt_message(next_param)
        state.messages.append(prompt_message)

    return state

def _create_prompt_message(param: str) -> Any:
    """创建参数询问消息"""
    from app.services.state_definitions import AnyMessage

    prompt = TRAINING_PARAMS_PROMPTS.get(param, f"请提供您的{param}:")
    return AnyMessage(role="assistant", content=prompt)

async def _extract_param_value(param: str, user_message: str) -> Any:
    """从用户消息中提取参数值"""
    # 使用LLM解析用户回复
    extract_prompt = f"""
    从以下用户消息中提取"{param}"信息:

    用户消息: "{user_message}"

    只提取关键信息，并以简洁形式返回。如果无法确定，请返回"未知"。
    """

    response = await llm_service.aget_chat_response(
        messages=[
            {"role": "system", "content": "你是一个参数提取助手。你的任务是从用户消息中提取特定参数的值。"},
            {"role": "user", "content": extract_prompt}
        ],
        model=settings.LLM_MODEL,
        temperature=0.1
    )

    # 处理特殊参数类型
    if param == "training_days":
        # 尝试提取数字
        import re
        digits = re.findall(r'\d+', response)
        if digits:
            return int(digits[0])
        # 转换文本数字
        number_words = {"一": 1, "二": 2, "三": 3, "四": 4, "五": 5, "六": 6, "七": 7}
        for word, num in number_words.items():
            if word in response:
                return num

    elif param == "available_time":
        # 提取分钟数
        import re
        minutes = re.findall(r'\d+', response)
        if minutes:
            return int(minutes[0])

    elif param == "equipment_available" or param == "body_parts_focus":
        # 返回列表
        return [item.strip() for item in response.split(',')]

    # 对于其他参数，直接返回处理后的文本
    return response.strip()

def _check_required_params_complete(params: Dict[str, Any]) -> bool:
    """检查是否已收集所有必要参数"""
    for param in REQUIRED_PARAMS:
        if param not in params or not params[param]:
            return False
    return True

def _get_next_param_to_collect(params: Dict[str, Any]) -> Optional[str]:
    """获取下一个需要收集的参数"""
    # 先检查必填参数
    for param in REQUIRED_PARAMS:
        if param not in params or not params[param]:
            return param

    # 然后检查可选参数
    for param in TRAINING_PARAMS_PROMPTS:
        if param not in REQUIRED_PARAMS and (param not in params or not params[param]):
            return param

    return None

async def _generate_confirmation_message(params: Dict[str, Any]) -> Any:
    """生成参数确认消息"""
    from app.services.state_definitions import AnyMessage

    # 格式化参数
    params_str = "\n".join([f"- {param}: {value}" for param, value in params.items()])

    # 构造确认消息
    confirmation = f"""
    非常好！我已经收集了以下训练参数：

    {params_str}

    现在我可以根据这些信息为您生成训练计划了。
    """

    return AnyMessage(role="assistant", content=confirmation.strip())