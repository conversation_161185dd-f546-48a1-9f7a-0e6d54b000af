"""
增强版训练计划专家节点 - 使用配置中定义的模型，提供更高效的训练计划生成
"""
from typing import Dict, Any, List, Optional
import logging
import uuid
import time
from langchain_core.messages import AIMessage
from app.services.state_definitions import ConversationState
from app.services.llm_proxy_service import LLMProxyService
from app.services.memory_cache_service import MemoryCacheService
from app.core.config import settings
from app.db.session import SessionLocal
from app.crud import crud_user, crud_exercise, crud_body_part, crud_equipment
from sqlalchemy.orm import Session
from sqlalchemy import text

logger = logging.getLogger(__name__)

# 全局LLM实例
llm_service = LLMProxyService()
cache_service = MemoryCacheService()

async def training_plan_expert_node(state: ConversationState) -> ConversationState:
    """增强版训练计划专家节点：负责生成个性化训练计划"""
    start_time = time.time()

    # 获取用户最新消息
    user_message = ""
    for msg in reversed(state.messages):
        if msg.role == "user":
            user_message = msg.content
            break

    # 获取用户信息
    user_info = state.user_info
    training_params = state.training_params

    # 创建数据库会话
    db = SessionLocal()

    try:
        # 检查用户信息完整性
        missing_fields = _check_user_info_completeness(user_info)
        if missing_fields:
            # 需要收集用户信息
            logger.info(f"缺少用户信息字段: {missing_fields}")
            state.flow_state["needs_user_info"] = True
            return state

        # 检查训练参数完整性
        missing_params = _check_training_params_completeness(training_params)
        if missing_params:
            # 需要收集训练参数
            logger.info(f"缺少训练参数: {missing_params}")
            state.flow_state["needs_param_collection"] = True
            return state

        # 所有必要信息已具备，生成训练计划
        logger.info(f"开始生成训练计划: 用户={user_info.get('user_id')}, 部位={training_params.get('body_part')}, 场景={training_params.get('training_scene')}")

        # 获取计划类型
        plan_type = training_params.get("plan_type", "单日")

        # 获取候选训练动作
        candidate_exercises = await _get_candidate_exercises(
            db,
            user_info,
            training_params.get("body_part"),
            training_params.get("training_scene")
        )

        if not candidate_exercises:
            logger.warning(f"未找到针对部位 {training_params.get('body_part')} 的候选动作")
            response = "抱歉，我无法找到针对您指定部位的训练动作。请尝试选择其他部位或训练场景。"
            state.messages.append(AIMessage(role="assistant", content=response))
            return state

        # 生成训练计划
        plan_result = await _generate_training_plan(
            user_info,
            training_params,
            candidate_exercises,
            plan_type
        )

        # 获取或生成计划ID
        plan_id = plan_result.get("plan_id", f"plan_{uuid.uuid4().hex[:8]}")

        # 格式化响应
        plan_type_text = "周期" if "周" in plan_type or "cycle" in plan_type.lower() else "单日"
        response_content = f"已为您制定{plan_type_text}训练计划，针对{training_params.get('body_part')}，适用于{training_params.get('training_scene')}场景。\n\n"
        response_content += plan_result.get("plan_description", "")
        response_content += "\n\n您可以随时询问关于这个计划的任何问题，如具体动作细节、训练注意事项等。"

        # 添加回答到消息列表
        state.messages.append(AIMessage(role="assistant", content=response_content))

        # 更新元信息
        state.meta_info["related_plan_id"] = plan_id
        state.meta_info["active_flow"] = "training_plan_discussion"

        # 清除收集状态
        state.flow_state["needs_param_collection"] = False

        # 记录处理时间
        process_time = time.time() - start_time
        logger.info(f"训练计划生成完成，耗时: {process_time:.2f}秒")

        return state

    except Exception as e:
        logger.error(f"生成训练计划时出错: {str(e)}")
        error_msg = "抱歉，生成训练计划时遇到了问题。请稍后再试。"
        state.messages.append(AIMessage(role="assistant", content=error_msg))
        return state

    finally:
        # 关闭数据库会话
        db.close()

def _check_user_info_completeness(user_info: Dict[str, Any]) -> List[str]:
    """检查用户信息完整性，返回缺失字段列表"""
    required_fields = ["gender", "age", "weight", "height", "fitness_goal", "fitness_level"]
    return [field for field in required_fields if field not in user_info or not user_info[field]]

def _check_training_params_completeness(training_params: Dict[str, Any]) -> List[str]:
    """检查训练参数完整性，返回缺失参数列表"""
    required_params = ["body_part", "training_scene", "plan_type"]
    return [param for param in required_params if param not in training_params or not training_params[param]]

async def _get_candidate_exercises(
    db: Session,
    user_info: Dict[str, Any],
    body_part: str,
    training_scene: str
) -> List[Dict[str, Any]]:
    """获取候选训练动作"""
    # 尝试从缓存获取
    cache_key = f"exercises:{body_part}:{training_scene}"
    cached_exercises = cache_service.get_query_result(cache_key)
    if cached_exercises:
        logger.debug(f"从缓存获取候选动作: {cache_key}")
        return cached_exercises

    # 确定身体部位ID
    body_part_id = None
    if isinstance(body_part, int) or body_part.isdigit():
        body_part_id = int(body_part)
    else:
        # 查询部位ID
        try:
            body_part_obj = crud_body_part.get_by_name(db, name=body_part)
            if body_part_obj:
                body_part_id = body_part_obj.id
        except Exception as e:
            logger.error(f"查询部位ID时出错: {str(e)}")

    # 确定训练场景对应的器材
    equipment_ids = []
    if training_scene.lower() in ["gym", "健身房"]:
        # 健身房场景包含所有器材
        equipment_ids = []  # 空列表表示所有器材
    elif training_scene.lower() in ["home", "家", "家庭"]:
        # 家庭场景通常只有哑铃、弹力带和自重
        try:
            home_equipment = ["哑铃", "弹力带", "自重"]
            for eq_name in home_equipment:
                eq = crud_equipment.get_by_name(db, name=eq_name)
                if eq:
                    equipment_ids.append(eq.id)
        except Exception as e:
            logger.error(f"查询家庭器材ID时出错: {str(e)}")
            # 默认家庭器材ID
            equipment_ids = [1, 2, 4]  # 假设这些ID对应哑铃、自重和弹力带

    # 构建查询
    try:
        # 使用原生SQL查询，提高性能
        query = """
        SELECT e.id, e.name, e.description, e.difficulty, e.image_url,
               e.video_url, e.body_part_id, bp.name as body_part_name,
               e.equipment_id, eq.name as equipment_name
        FROM exercises e
        JOIN body_parts bp ON e.body_part_id = bp.id
        JOIN equipment eq ON e.equipment_id = eq.id
        WHERE 1=1
        """
        params = {}

        # 添加部位过滤条件
        if body_part_id:
            query += " AND e.body_part_id = :body_part_id"
            params["body_part_id"] = body_part_id

        # 添加器材过滤条件
        if equipment_ids:
            query += " AND e.equipment_id IN :equipment_ids"
            params["equipment_ids"] = tuple(equipment_ids) if len(equipment_ids) > 1 else f"({equipment_ids[0]})"

        # 添加难度过滤条件（基于用户健身水平）
        fitness_level = user_info.get("fitness_level", "beginner")
        if fitness_level == "beginner":
            query += " AND e.difficulty <= 3"
        elif fitness_level == "intermediate":
            query += " AND e.difficulty BETWEEN 2 AND 4"
        elif fitness_level == "advanced":
            query += " AND e.difficulty >= 3"

        # 限制结果数量
        query += " ORDER BY e.difficulty ASC LIMIT 20"

        # 执行查询
        result = db.execute(text(query), params)

        # 转换结果
        exercises = []
        for row in result:
            exercise = {
                "id": row.id,
                "name": row.name,
                "description": row.description,
                "difficulty": row.difficulty,
                "image_url": row.image_url,
                "video_url": row.video_url,
                "body_part_id": row.body_part_id,
                "body_part_name": row.body_part_name,
                "equipment_id": row.equipment_id,
                "equipment_name": row.equipment_name
            }
            exercises.append(exercise)

        # 缓存结果
        if exercises:
            cache_service.set_query_result(cache_key, None, exercises)

        return exercises

    except Exception as e:
        logger.error(f"查询候选动作时出错: {str(e)}")
        return []

async def _generate_training_plan(
    user_info: Dict[str, Any],
    training_params: Dict[str, Any],
    candidate_exercises: List[Dict[str, Any]],
    plan_type: str
) -> Dict[str, Any]:
    """生成训练计划"""
    from app.core.chat_config import MODELS

    # 使用配置中定义的模型
    model = MODELS["exercise_generation"]
    logger.debug(f"使用训练计划生成模型: {model}")

    # 构建提示词
    prompt = _build_training_plan_prompt(user_info, training_params, candidate_exercises, plan_type)

    # 调用LLM生成训练计划
    try:
        # 生成初步训练计划
        initial_response = await llm_service.aget_chat_response(
            system="你是一位专业的健身教练，擅长制定个性化训练计划。请根据用户信息和可用动作，创建一个详细、科学的训练计划。",
            user=prompt,
            model=model,
            temperature=0.3
        )

        # 使用conversation模型包装回答
        wrapped_response = await llm_service.aget_chat_response(
            messages=[
                {"role": "system", "content": "你是一位专业的健身教练，请用专业、友好的语气包装训练计划。"},
                {"role": "user", "content": f"请用专业的语气包装以下训练计划，保持其结构和内容不变：\n\n{initial_response}"}
            ],
            model=MODELS["conversation"],
            temperature=0.5
        )

        # 解析响应
        plan_id = f"plan_{uuid.uuid4().hex[:8]}"

        return {
            "plan_id": plan_id,
            "plan_description": wrapped_response,
            "plan_type": plan_type,
            "body_part": training_params.get("body_part"),
            "training_scene": training_params.get("training_scene"),
            "exercises": candidate_exercises
        }

    except Exception as e:
        logger.error(f"调用LLM生成训练计划时出错: {str(e)}")
        raise ValueError(f"生成训练计划失败: {str(e)}")

def _build_training_plan_prompt(
    user_info: Dict[str, Any],
    training_params: Dict[str, Any],
    candidate_exercises: List[Dict[str, Any]],
    plan_type: str
) -> str:
    """构建训练计划生成提示词"""
    # 用户信息部分
    user_info_text = f"""
用户信息:
- 性别: {user_info.get('gender', '未知')}
- 年龄: {user_info.get('age', '未知')}
- 身高: {user_info.get('height', '未知')} cm
- 体重: {user_info.get('weight', '未知')} kg
- 健身水平: {user_info.get('fitness_level', '初级')}
- 健身目标: {user_info.get('fitness_goal', '增肌')}
    """

    # 训练参数部分
    training_params_text = f"""
训练需求:
- 训练部位: {training_params.get('body_part', '全身')}
- 训练场景: {training_params.get('training_scene', '健身房')}
- 计划类型: {plan_type}
    """

    # 候选动作部分
    exercises_text = "可用训练动作:\n"
    for i, ex in enumerate(candidate_exercises[:10], 1):  # 限制为前10个动作
        exercises_text += f"{i}. {ex['name']} - 难度:{ex['difficulty']}, 器材:{ex.get('equipment_name', '未知')}\n"

    # 完整提示词
    prompt = f"""请为用户制定一个详细的{plan_type}训练计划。

{user_info_text}

{training_params_text}

{exercises_text}

请包含以下内容:
1. 训练计划概述
2. 详细的训练安排，包括每个动作的组数、次数和休息时间
3. 热身和拉伸建议
4. 训练注意事项
5. 饮食建议

请确保计划科学合理，符合用户的健身水平和目标。
"""

    return prompt
