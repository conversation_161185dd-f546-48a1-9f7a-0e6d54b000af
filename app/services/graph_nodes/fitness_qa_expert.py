from typing import Dict, Any
import logging
from langchain_core.messages import AIMessage
from langchain_core.prompts import ChatPromptTemplate
from ...services.state_definitions import FitnessAssistantState
from ...services.llm_proxy_service import LLMProxyService
from ...db.session import SessionLocal

logger = logging.getLogger(__name__)

class FitnessQAExpert:
    def __init__(self):
        self.db = SessionLocal()
        self.llm_proxy_service = LLMProxyService()
        
        # 系统提示
        self.system_prompt = """你是一位专业的健身教练和营养顾问，拥有丰富的健身、运动科学和营养学知识。
回答用户的健身和营养问题时，提供科学、准确且实用的建议。根据用户的个人情况（如有）定制回答。
保持专业、友好的语气，避免过度专业术语，确保建议易于理解和执行。
不提供医疗建议，对于医疗相关问题，建议用户咨询医疗专业人士。"""
    
    def __call__(self, state: FitnessAssistantState) -> Dict[str, Any]:
        """健身咨询专家节点"""
        messages = state["messages"]
        user_info = state.get("user_info", {})
        metadata = state.get("metadata", {})
        
        # 获取用户问题
        user_message = messages[-1].content if messages and messages[-1].type == "human" else ""
        
        if not user_message:
            return {
                "messages": [AIMessage(content="您有什么健身或营养方面的问题吗？")],
                "dialog_state": "pop"
            }
        
        try:
            # 构建用户信息上下文
            user_context = ""
            if user_info:
                user_context = f"""用户信息:
- 性别: {user_info.get('gender', '未知')}
- 年龄: {user_info.get('age', '未知')}
- 体重: {user_info.get('weight', '未知')} kg
- 身高: {user_info.get('height', '未知')} cm
- 活动水平: {user_info.get('activity_level', '未知')}
- 健身水平: {user_info.get('fitness_level', '未知')}
- 健身目标: {user_info.get('fitness_goal', '未知')}
"""
            
            # 构建提示
            fitness_qa_prompt = f"{self.system_prompt}\n\n{user_context}\n\n用户问题: {user_message}"
            
            # 调用LLM生成回答
            response = self.llm_proxy_service.chat_completion(
                messages=[
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": f"{user_context}\n\n用户问题: {user_message}"}
                ],
                model_type="gpt-3.5-turbo"  # 使用默认模型
            )
            
            result = response.get('content', "抱歉，无法获取回答。请稍后再试。")
            
            return {
                "messages": [AIMessage(content=result)],
                "dialog_state": "pop"  # 返回路由器
            }
            
        except Exception as e:
            logger.error(f"Error in fitness QA: {e}", exc_info=True)
            return {
                "messages": [AIMessage(content="抱歉，处理您的问题时遇到了技术问题。请稍后再试。")],
                "dialog_state": "pop"
            }

# 创建实例供图使用
fitness_qa_expert = FitnessQAExpert() 