"""
状态监控节点 - 负责监控状态转换，检测潜在的循环和异常状态
"""
from typing import Dict, Any, List, Optional
import logging
import time
from collections import Counter

from app.services.state_definitions import ConversationState

# 配置日志
logger = logging.getLogger(__name__)

# 状态监控配置
MONITOR_CONFIG = {
    "max_transitions": 20,  # 最大状态转换次数
    "cycle_detection_window": 10,  # 循环检测窗口大小
    "cycle_threshold": 3,  # 循环检测阈值，同一模式重复此次数视为循环
}

async def state_monitor_node(state: ConversationState) -> ConversationState:
    """状态监控节点：监控状态转换，检测潜在的循环和异常状态"""
    
    # 初始化转换历史
    if "transition_history" not in state.flow_state:
        state.flow_state["transition_history"] = []
    
    # 获取当前活动节点
    current_node = state.flow_state.get("current_node", "unknown")
    
    # 记录转换
    state.flow_state["transition_history"].append(current_node)
    
    # 检测循环
    history = state.flow_state["transition_history"]
    
    # 检查转换次数是否超过限制
    if len(history) > MONITOR_CONFIG["max_transitions"]:
        logger.warning(f"状态转换次数超过限制: {len(history)}次，会话ID: {state.session_id}")
        # 强制中断循环
        state.flow_state["force_exit"] = True
        # 添加错误消息
        from app.services.state_definitions import AnyMessage
        state.messages.append(AnyMessage(
            role="assistant",
            content="抱歉，我遇到了一些处理问题。让我们重新开始对话，请告诉我您需要什么帮助？"
        ))
        # 清除状态
        state.flow_state = {
            "force_exit": True,
            "transition_history": history
        }
        return state
    
    # 检测循环模式
    if len(history) >= MONITOR_CONFIG["cycle_detection_window"]:
        recent_history = history[-MONITOR_CONFIG["cycle_detection_window"]:]
        cycles = detect_cycles(recent_history)
        
        if cycles:
            cycle_pattern, cycle_count = cycles[0]
            if cycle_count >= MONITOR_CONFIG["cycle_threshold"]:
                logger.warning(f"检测到循环模式: {cycle_pattern}，重复{cycle_count}次，会话ID: {state.session_id}")
                # 强制中断循环
                state.flow_state["force_exit"] = True
                # 添加错误消息
                from app.services.state_definitions import AnyMessage
                state.messages.append(AnyMessage(
                    role="assistant",
                    content="抱歉，我似乎陷入了重复的思考模式。让我们换一种方式解决您的问题。请告诉我您具体需要什么帮助？"
                ))
    
    return state

def detect_cycles(history: List[str]) -> List[tuple]:
    """检测历史记录中的循环模式
    
    Args:
        history: 状态转换历史记录
        
    Returns:
        检测到的循环模式列表，每个元素为(模式, 重复次数)
    """
    cycles = []
    
    # 检查长度为1的循环（同一节点重复）
    node_counts = Counter(history)
    for node, count in node_counts.items():
        if count >= MONITOR_CONFIG["cycle_threshold"]:
            cycles.append(((node,), count))
    
    # 检查长度为2的循环（两个节点交替）
    if len(history) >= 4:
        for i in range(len(history) - 3):
            pattern = (history[i], history[i+1])
            if (history[i+2], history[i+3]) == pattern:
                # 计算这个模式在历史中出现的次数
                pattern_count = 0
                for j in range(0, len(history) - 1, 2):
                    if j + 1 < len(history) and (history[j], history[j+1]) == pattern:
                        pattern_count += 1
                
                if pattern_count >= MONITOR_CONFIG["cycle_threshold"]:
                    cycles.append((pattern, pattern_count))
    
    # 检查长度为3的循环
    if len(history) >= 6:
        for i in range(len(history) - 5):
            pattern = (history[i], history[i+1], history[i+2])
            if (history[i+3], history[i+4], history[i+5]) == pattern:
                # 计算这个模式在历史中出现的次数
                pattern_count = 0
                for j in range(0, len(history) - 2, 3):
                    if j + 2 < len(history) and (history[j], history[j+1], history[j+2]) == pattern:
                        pattern_count += 1
                
                if pattern_count >= MONITOR_CONFIG["cycle_threshold"]:
                    cycles.append((pattern, pattern_count))
    
    # 按重复次数排序
    cycles.sort(key=lambda x: x[1], reverse=True)
    
    return cycles
