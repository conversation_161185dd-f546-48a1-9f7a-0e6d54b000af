from typing import Dict, Any, List
import logging
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage
from ...services.state_definitions import FitnessAssistantState
from ...services.llm_proxy_service import LLMProxyService
from ...db.session import SessionLocal

logger = logging.getLogger(__name__)

class GeneralChatExpert:
    def __init__(self):
        self.db = SessionLocal()
        self.llm_proxy_service = LLMProxyService()
        
        # 系统提示
        self.system_prompt = """你是一位友好的健身AI助手，专注于帮助用户实现健身目标。
你擅长回答健身相关问题，但也可以进行一般闲聊。保持积极、鼓励的语气，让用户感到受到支持。
如果用户询问的问题超出健身、营养或一般健康范围，礼貌地引导话题回到你的专业领域。
你的回答应简明扼要，易于理解，避免过于技术性的术语，除非用户明确要求深入解释。"""
    
    def __call__(self, state: FitnessAssistantState) -> Dict[str, Any]:
        """通用聊天专家节点"""
        messages = state["messages"]
        user_info = state.get("user_info", {})
        
        # 获取用户的最新消息
        user_message = messages[-1].content if messages and messages[-1].type == "human" else ""
        
        if not user_message:
            return {
                "messages": [AIMessage(content="您好！有什么我可以帮您的吗？")],
                "dialog_state": "pop"
            }
        
        try:
            # 构建用户信息上下文（如果有）
            user_context = ""
            if user_info:
                user_context = "用户基本信息摘要：\n"
                if user_info.get('gender'):
                    user_context += f"- 性别: {user_info.get('gender')}\n"
                if user_info.get('age'):
                    user_context += f"- 年龄: {user_info.get('age')}\n"
                if user_info.get('fitness_level'):
                    user_context += f"- 健身水平: {user_info.get('fitness_level')}\n"
                if user_info.get('fitness_goal'):
                    user_context += f"- 健身目标: {user_info.get('fitness_goal')}\n"
            
            # 提取历史消息（最多5轮对话）
            history = []
            for msg in messages[-10:]:  # 最近10条消息
                if msg.type == "human":
                    history.append({"role": "user", "content": msg.content})
                elif msg.type == "ai":
                    history.append({"role": "assistant", "content": msg.content})
            
            # 若历史为空，只使用当前消息
            if not history or len(history) <= 1:
                history = [{"role": "user", "content": user_message}]
            
            # 构建完整的提示
            full_system_prompt = self.system_prompt
            if user_context:
                full_system_prompt += f"\n\n{user_context}"
            
            # 调用LLM生成回复
            response = self.llm_proxy_service.chat_completion(
                messages=[
                    {"role": "system", "content": full_system_prompt},
                    *history
                ],
                model_type="gpt-3.5-turbo"
            )
            
            result = response.get('content', "抱歉，我目前无法回答您的问题。请稍后再试。")
            
            return {
                "messages": [AIMessage(content=result)],
                "dialog_state": "pop"  # 返回路由器
            }
            
        except Exception as e:
            logger.error(f"Error in general chat: {e}", exc_info=True)
            return {
                "messages": [AIMessage(content="抱歉，我暂时无法回答您的问题。请稍后再试。")],
                "dialog_state": "pop"
            }

# 创建实例供图使用
general_chat_expert = GeneralChatExpert() 