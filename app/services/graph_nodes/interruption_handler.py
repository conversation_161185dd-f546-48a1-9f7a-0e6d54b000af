from typing import Dict, Any
import logging
import copy
from datetime import datetime
from langchain_core.messages import AIMessage
from ...services.state_definitions import FitnessAssistantState

logger = logging.getLogger(__name__)

class InterruptionHandler:
    def __init__(self):
        # 中断超时时间（秒）
        self.INTERRUPTION_TIMEOUT = 3600  # 1小时
    
    def __call__(self, state: FitnessAssistantState) -> Dict[str, Any]:
        """中断处理专家节点"""
        messages = state["messages"]
        metadata = state.get("metadata", {})
        
        # 获取中断状态
        interrupt_state = metadata.get("interrupt_state", {})
        
        if not interrupt_state:
            # 没有中断状态，返回路由器
            return {"dialog_state": "pop"}
        
        # 获取用户的最新消息
        user_message = messages[-1].content if messages and messages[-1].type == "human" else ""
        
        # 检查中断是否已超时
        current_time = datetime.now().timestamp()
        interrupt_time = interrupt_state.get("timestamp", 0)
        
        if current_time - interrupt_time > self.INTERRUPTION_TIMEOUT:
            # 中断已超时，清除中断状态，视为新对话
            metadata_update = copy.deepcopy(metadata)
            metadata_update.pop("interrupt_state", None)
            
            return {
                "messages": [AIMessage(content="您的上一个会话已超时。我可以帮您处理新的问题。")],
                "metadata": metadata_update,
                "dialog_state": "router"  # 返回路由器重新路由
            }
        
        # 检查用户响应
        if any(keyword in user_message.lower() for keyword in ["继续", "之前", "原来", "上一个"]):
            # 用户想继续之前的对话
            logger.info("User chose to continue previous conversation")
            
            # 还原之前的状态
            original_state = interrupt_state.get("original_state", {})
            
            # 清除中断状态
            metadata_update = copy.deepcopy(metadata)
            metadata_update.pop("interrupt_state", None)
            
            if original_state:
                # 合并原始状态中的关键信息
                for key in ["training_params", "active_flow", "related_plan_id", 
                           "collecting_training_params", "asking_param", "waiting_info"]:
                    if key in original_state.get("metadata", {}):
                        metadata_update[key] = original_state["metadata"][key]
            
            return {
                "messages": [AIMessage(content="好的，让我们继续之前的对话。")],
                "metadata": metadata_update,
                "dialog_state": "router"  # 返回路由器重新路由
            }
        else:
            # 用户想处理新问题
            logger.info("User chose to handle new message")
            
            # 清除中断状态和可能的活跃流程
            metadata_update = copy.deepcopy(metadata)
            metadata_update.pop("interrupt_state", None)
            metadata_update.pop("active_flow", None)
            metadata_update.pop("collecting_training_params", None)
            metadata_update.pop("asking_param", None)
            metadata_update.pop("waiting_info", None)
            
            return {
                "messages": [AIMessage(content="好的，让我们处理您的新问题。")],
                "metadata": metadata_update,
                "dialog_state": "router"  # 返回路由器以处理新问题
            }

# 创建实例供图使用
interruption_handler = InterruptionHandler() 