from typing import Dict, Any, List
import logging
import re
from langchain_core.messages import AIMessage
from ...services.state_definitions import FitnessAssistantState
from ...models.user import User
from ...db.session import SessionLocal
from ...crud.crud_user import update_user_info

logger = logging.getLogger(__name__)

class UserInfoCollector:
    def __init__(self):
        self.db = SessionLocal()
        self.field_questions = {
            "gender": "请问您的性别是？(男/女)",
            "age": "请问您的年龄是？",
            "weight": "请问您的体重是多少公斤？",
            "height": "请问您的身高是多少厘米？",
            "activity_level": "请问您的日常活动水平是？(久坐/轻度活动/中度活动/高度活动)",
            "fitness_level": "请问您的健身水平是？(初级/中级/高级)",
            "fitness_goal": "请问您的健身目标是？(增肌/减脂/塑形/增强力量)"
        }
        
        # 字段验证函数
        self.field_validators = {
            "gender": lambda v: v in ["男", "女", "male", "female"],
            "age": lambda v: self._is_valid_number(v, 10, 100),
            "weight": lambda v: self._is_valid_number(v, 30, 200),
            "height": lambda v: self._is_valid_number(v, 100, 250),
            "activity_level": lambda v: v in ["久坐", "轻度活动", "中度活动", "高度活动"],
            "fitness_level": lambda v: v in ["初级", "中级", "高级"],
            "fitness_goal": lambda v: v in ["增肌", "减脂", "塑形", "增强力量"]
        }
    
    def _is_valid_number(self, value, min_val, max_val):
        """验证数值是否在有效范围内"""
        try:
            num = float(value.replace('kg', '').replace('cm', '').strip())
            return min_val <= num <= max_val
        except (ValueError, AttributeError):
            return False
    
    def parse_user_info_input(self, field: str, value: str) -> Any:
        """解析用户输入的信息"""
        value = value.strip().lower()
        
        # 性别特殊处理
        if field == "gender":
            if value in ["男", "man", "male", "m"]:
                return "男"
            elif value in ["女", "woman", "female", "f"]:
                return "女"
            return value
        
        # 数值类型处理
        if field in ["age", "weight", "height"]:
            # 提取数字
            match = re.search(r'(\d+\.?\d*)', value)
            if match:
                return float(match.group(1))
            return value
        
        # 活动水平处理
        if field == "activity_level":
            if "久坐" in value or "不活动" in value:
                return "久坐"
            elif "轻度" in value:
                return "轻度活动"
            elif "中度" in value:
                return "中度活动"
            elif "高度" in value or "剧烈" in value:
                return "高度活动"
            return value
        
        # 健身水平处理
        if field == "fitness_level":
            if "初" in value or "新手" in value or "beginner" in value:
                return "初级"
            elif "中" in value or "intermediate" in value:
                return "中级"
            elif "高" in value or "advanced" in value:
                return "高级"
            return value
        
        # 健身目标处理
        if field == "fitness_goal":
            if "增肌" in value or "肌肉" in value:
                return "增肌"
            elif "减脂" in value or "减肥" in value or "瘦" in value:
                return "减脂"
            elif "塑形" in value or "形体" in value:
                return "塑形"
            elif "力量" in value or "强化" in value:
                return "增强力量"
            return value
        
        return value
    
    def __call__(self, state: FitnessAssistantState) -> Dict[str, Any]:
        """用户信息收集专家节点"""
        messages = state["messages"]
        user_info = state.get("user_info", {})
        metadata = state.get("metadata", {})
        user_id = metadata.get("user_id")  # 从元数据中获取用户ID
        
        # 获取等待收集的字段
        waiting_info = metadata.get("waiting_info", [])
        original_request = metadata.get("original_request", {})
        
        if not waiting_info:
            # 没有需要收集的信息，返回到路由器
            return {"dialog_state": "pop"}
        
        # 将等待信息转换为列表以统一处理
        if isinstance(waiting_info, dict):
            waiting_info = list(waiting_info.keys())
        
        # 获取当前正在收集的字段
        current_field = waiting_info[0] if waiting_info else None
        
        if not current_field:
            # 没有需要收集的字段，返回到路由器
            return {"dialog_state": "pop"}
        
        # 获取用户的最新消息
        user_message = messages[-1].content if messages and messages[-1].type == "human" else ""
        
        if not user_message:
            # 没有用户消息，发送收集字段的问题
            question = self.field_questions.get(current_field, f"请提供您的{current_field}：")
            return {
                "messages": [AIMessage(content=question)]
            }
        
        # 解析用户回答并验证
        parsed_value = self.parse_user_info_input(current_field, user_message)
        
        # 验证值是否有效
        validator = self.field_validators.get(current_field, lambda x: True)
        if not validator(parsed_value):
            # 值无效，重新询问
            error_msg = f"抱歉，您提供的{current_field}似乎不正确。"
            question = self.field_questions.get(current_field, f"请重新提供您的{current_field}：")
            return {
                "messages": [AIMessage(content=f"{error_msg} {question}")]
            }
        
        # 更新用户信息
        user_info[current_field] = parsed_value
        logger.info(f"Updated user info {current_field}: {parsed_value}")
        
        # 如果有用户ID，更新数据库中的用户信息
        if user_id:
            try:
                update_user_info(self.db, user_id=user_id, field=current_field, value=parsed_value)
                logger.info(f"Updated user {user_id} database record for field {current_field}")
            except Exception as e:
                logger.error(f"Failed to update user info in database: {e}")
        
        # 从待收集列表中移除已收集的字段
        waiting_info = waiting_info[1:] if len(waiting_info) > 1 else []
        
        # 检查是否还有需要收集的字段
        if waiting_info:
            # 还需收集更多字段
            next_field = waiting_info[0]
            question = self.field_questions.get(next_field, f"请提供您的{next_field}：")
            
            return {
                "messages": [AIMessage(content=question)],
                "user_info": user_info,
                "metadata": {
                    **metadata,
                    "waiting_info": waiting_info
                }
            }
        else:
            # 所有信息收集完毕
            thank_you_msg = "谢谢您提供的信息！"
            
            # 如果有原始请求，提示恢复处理
            if original_request:
                thank_you_msg += " 现在我可以继续处理您的请求了。"
            
            # 清除等待信息标记，恢复原始请求
            next_state = {
                "messages": [AIMessage(content=thank_you_msg)],
                "user_info": user_info,
                "metadata": {
                    **metadata,
                    "waiting_info": None
                }
            }
            
            # 根据原始请求类型确定下一步
            if original_request:
                intent = original_request.get("intent")
                if intent:
                    return {
                        **next_state,
                        "dialog_state": f"{intent}_expert"
                    }
            
            # 默认返回路由器
            return {
                **next_state,
                "dialog_state": "pop"
            }

# 创建实例供图使用
user_info_collector = UserInfoCollector() 