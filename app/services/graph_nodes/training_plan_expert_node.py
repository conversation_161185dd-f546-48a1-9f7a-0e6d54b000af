from typing import Dict, List, Any, Optional
import json
import logging

from app.services.state_definitions import ConversationState
from app.services.llm_proxy_service import LLMProxyService
from app.core.config import settings
from app.db.session import SessionLocal
from app.crud import crud_training_plan, crud_user, crud_exercise

# 配置日志
logger = logging.getLogger(__name__)

# 全局LLM实例
llm_service = LLMProxyService()

async def training_plan_expert_node(state: ConversationState) -> ConversationState:
    """训练计划专家节点：负责生成个性化训练计划"""

    # 获取用户最新消息
    user_message = ""
    for msg in reversed(state.messages):
        if msg.role == "user":
            user_message = msg.content
            break

    # 收集训练计划生成所需信息
    user_info = state.user_info
    training_params = state.training_params

    # 生成训练计划
    plan_data = await _generate_training_plan(user_message, user_info, training_params)

    # 保存训练计划到数据库（如果有用户ID）
    if user_info.get("user_id"):
        try:
            # 获取数据库会话
            db = SessionLocal()
            try:
                # 保存训练计划
                user_id = user_info["user_id"]
                plan_obj = {
                    "user_id": user_id,
                    "plan_name": plan_data.get("plan_name", "个性化训练计划"),
                    "description": plan_data.get("description", ""),
                    "fitness_goal": user_info.get("fitness_goal"),
                    "experience_level": user_info.get("fitness_level"),
                    "duration_weeks": plan_data.get("duration_weeks", 4),
                    "is_active": True,
                    "is_template": False,
                    "privacy_setting": 1  # 默认私有
                }

                # 创建计划记录
                training_plan = crud_training_plan.create(db, obj_in=plan_obj)

                # 更新元数据
                state.meta_info["training_plan_id"] = training_plan.id
                state.meta_info["training_plan_created"] = True

                # TODO: 保存Workouts和WorkoutExercises
                # 这部分需要根据计划数据结构和数据库模型扩展

            finally:
                db.close()
        except Exception as e:
            logger.error(f"保存训练计划失败: {str(e)}")

    # 构建训练计划消息
    plan_message = _format_training_plan_message(plan_data)

    # 添加训练计划消息
    from app.services.state_definitions import AnyMessage
    state.messages.append(AnyMessage(role="assistant", content=plan_message))

    # 触发训练计划事件（为WebSocket流式响应）
    state.meta_info["event"] = "training_plan"
    state.meta_info["training_plan_data"] = plan_data

    return state

async def _generate_training_plan(user_message: str, user_info: Dict, training_params: Dict) -> Dict:
    """生成训练计划"""
    # 连接数据库获取动作库
    exercise_list = []
    try:
        db = SessionLocal()
        try:
            # 获取动作列表
            exercises = crud_exercise.get_multi_by_filters(
                db,
                filters={},  # 可以根据器材、部位等筛选
                limit=100
            )
            exercise_list = [{
                "id": e.id,
                "name": e.name,
                "body_part": e.body_part.name if e.body_part else "未知",
                "equipment": e.equipment.name if e.equipment else "无器械",
                "level": e.level
            } for e in exercises]
        finally:
            db.close()
    except Exception as e:
        logger.error(f"获取动作库失败: {str(e)}")

    # 构建训练计划生成提示
    system_prompt = """
    你是一位专业的健身教练，擅长根据用户的个人信息和需求创建个性化的训练计划。
    你需要生成一个结构清晰的训练计划，包括计划概述和详细的每日训练内容。
    必须输出结构化JSON格式，每个训练日包括目标部位、动作列表（包括组数、次数和休息时间）以及注意事项。
    所有动作必须从我提供的动作库中选择，不要编造不存在的动作ID。
    为初学者提供更基础的动作和较低的强度，为高级健身者提供更复杂的动作组合和高强度训练。
    """

    # 提取用户基本信息和训练参数
    user_info_str = "\n".join([f"- {k}: {v}" for k, v in user_info.items() if k != "user_id"])
    training_params_str = "\n".join([f"- {k}: {v}" for k, v in training_params.items()])

    # 构造动作库信息
    exercise_library = json.dumps(exercise_list, ensure_ascii=False, indent=2)

    # 用户请求
    user_prompt = f"""
    请根据以下信息为我制定一个个性化的训练计划:

    ## 用户信息:
    {user_info_str}

    ## 训练参数:
    {training_params_str}

    ## 用户请求:
    {user_message}

    ## 可用动作库:
    {exercise_library}

    请提供一个包含计划概述和详细训练日安排的训练计划，以JSON格式返回。JSON结构要求如下:
    ```json
    {{
        "plan_name": "计划名称",
        "description": "计划描述",
        "duration_weeks": 周数,
        "days_per_week": 每周训练天数,
        "target_goal": "目标(增肌/减脂/etc)",
        "workouts": [
            {{
                "day": 1,
                "name": "训练日名称",
                "focus": "训练重点",
                "duration_minutes": 预计时长,
                "exercises": [
                    {{
                        "exercise_id": 动作ID,
                        "name": "动作名称",
                        "sets": 组数,
                        "reps": "次数/时间",
                        "rest_seconds": 休息秒数,
                        "notes": "注意事项"
                    }},
                    // 更多动作...
                ]
            }},
            // 更多训练日...
        ]
    }}
    ```
    """

    # 调用高级模型生成训练计划
    response = await llm_service.aget_chat_response(
        system=system_prompt,
        user=user_prompt,
        model=settings.LLM_EXERCISE_GENERATION_MODEL,  # 使用专门的训练计划生成模型
        temperature=0.7
    )

    # 解析JSON响应
    try:
        # 提取JSON部分
        import re
        json_match = re.search(r'```json\s*([\s\S]*?)\s*```', response)
        if json_match:
            json_str = json_match.group(1)
        else:
            # 尝试直接解析整个响应
            json_str = response

        plan_data = json.loads(json_str)
        return plan_data
    except Exception as e:
        logger.error(f"解析训练计划JSON失败: {str(e)}")
        # 返回简单的错误计划
        return {
            "plan_name": "基础训练计划",
            "description": "抱歉，生成详细计划时出现错误。以下是基础建议。",
            "duration_weeks": 4,
            "days_per_week": 3,
            "target_goal": "综合健身",
            "workouts": []
        }

def _format_training_plan_message(plan_data: Dict) -> str:
    """格式化训练计划为可读消息"""
    plan_name = plan_data.get("plan_name", "个性化训练计划")
    description = plan_data.get("description", "")
    duration = plan_data.get("duration_weeks", 4)
    days_per_week = plan_data.get("days_per_week", 3)

    message = f"""
# {plan_name}

{description}

**计划概述:**
- 周期: {duration}周
- 频率: 每周{days_per_week}天
- 目标: {plan_data.get('target_goal', '综合健身')}

**训练安排:**
"""

    # 添加训练日
    workouts = plan_data.get("workouts", [])
    for workout in workouts:
        day = workout.get("day", "")
        name = workout.get("name", "")
        focus = workout.get("focus", "")
        duration = workout.get("duration_minutes", 0)

        message += f"""
## 训练日 {day}: {name}
- 重点: {focus}
- 时长: 约{duration}分钟

**动作安排:**
"""

        # 添加动作
        exercises = workout.get("exercises", [])
        for i, exercise in enumerate(exercises, 1):
            name = exercise.get("name", "")
            sets = exercise.get("sets", 0)
            reps = exercise.get("reps", "")
            rest = exercise.get("rest_seconds", 0)
            notes = exercise.get("notes", "")

            message += f"""
{i}. **{name}**
   - {sets}组 x {reps}
   - 休息: {rest}秒
   - 注意: {notes}
"""

    # 添加结束语
    message += """
**注意事项:**
1. 每次训练前务必进行5-10分钟的热身
2. 保持正确的动作姿势，避免受伤
3. 根据自身情况调整重量和强度
4. 确保充分休息和营养补充
5. 如有不适，立即停止训练

祝您训练顺利！
"""

    return message.strip()