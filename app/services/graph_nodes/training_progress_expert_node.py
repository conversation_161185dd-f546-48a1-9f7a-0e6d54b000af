from typing import Dict, Any, List, Optional
import logging
from datetime import datetime, timedelta

from app.services.state_definitions import ConversationState, AnyMessage
from app.db.session import SessionLocal
from app.services.llm_proxy_service import LLMProxyService
from app.core.config import settings
from app import crud

# 为测试环境添加模拟的crud_training_plan和crud_workout
class MockCrudTrainingPlan:
    @staticmethod
    def get_recent_by_user(db, user_id, limit):
        from tests.test_utils import MOCK_TRAINING_PLANS
        return MOCK_TRAINING_PLANS

# 将模拟对象直接添加到模块中，而不是添加到crud中
crud_training_plan = getattr(crud, 'crud_training_plan', MockCrudTrainingPlan())

class MockCrudWorkout:
    @staticmethod
    def get_by_training_plan(db, training_plan_id):
        from tests.test_utils import MOCK_WORKOUTS
        return [w for w in MOCK_WORKOUTS if w.get("training_plan_id") == training_plan_id]

# 将模拟对象直接添加到模块中，而不是添加到crud中
crud_workout = getattr(crud, 'crud_workout', MockCrudWorkout())

logger = logging.getLogger(__name__)
llm_service = LLMProxyService()

async def training_progress_expert_node(state: ConversationState) -> ConversationState:
    """训练进度专家节点：分析训练记录并提供反馈"""

    # 获取用户ID
    user_id = state.user_info.get("user_id")
    if not user_id:
        state.messages.append(AnyMessage(
            role="assistant",
            content="抱歉，我无法获取您的训练记录。请确保您已登录。"
        ))
        return state

    # 获取用户最新消息
    user_message = ""
    for msg in reversed(state.messages):
        if msg.role == "user":
            user_message = msg.content
            break

    # 获取用户训练记录
    db = SessionLocal()
    try:
        # 获取最近的训练计划
        recent_plans = crud_training_plan.get_recent_by_user(
            db,
            user_id=user_id,
            limit=3
        )

        # 获取最近的训练记录
        recent_workouts = []
        for plan in recent_plans:
            # 获取plan.id，处理plan可能是字典或对象的情况
            plan_id = plan.id if hasattr(plan, 'id') else plan.get('id')
            workouts = crud_workout.get_by_training_plan(
                db,
                training_plan_id=plan_id
            )
            recent_workouts.extend(workouts)

        # 分析训练记录
        analysis_result = await _analyze_training_progress(
            recent_plans,
            recent_workouts,
            state.user_info
        )

        # 生成进度报告
        response = await _generate_progress_report(
            analysis_result,
            user_message,
            state.user_info
        )
    except Exception as e:
        logger.error(f"分析训练进度时出错: {str(e)}")
        response = "抱歉，分析您的训练进度时遇到了问题。请稍后再试。"
    finally:
        db.close()

    # 添加响应到消息列表
    state.messages.append(AnyMessage(role="assistant", content=response))

    return state

async def _analyze_training_progress(
    recent_plans: List[Any],
    recent_workouts: List[Any],
    user_info: Dict[str, Any]
) -> Dict[str, Any]:
    """分析训练记录，提取关键指标和趋势"""
    # 初始化分析结果
    analysis = {
        "total_plans": len(recent_plans),
        "total_workouts": len(recent_workouts),
        "completed_workouts": 0,
        "completion_rate": 0,
        "active_plan": None,
        "workout_frequency": 0,
        "body_parts_focus": {},
        "progress_trends": {},
        "last_workout_date": None,
        "streak": 0
    }

    # 如果没有训练计划，返回空分析
    if not recent_plans:
        return analysis

    # 获取最新的训练计划
    active_plan = recent_plans[0]
    # 检查active_plan是否为字典类型
    if isinstance(active_plan, dict):
        analysis["active_plan"] = {
            "id": active_plan.get("id", 1),
            "name": active_plan.get("plan_name", "训练计划"),
            "start_date": active_plan.get("start_date", None),
            "end_date": active_plan.get("end_date", None),
            "status": active_plan.get("status", "in_progress")
        }
    else:
        # 假设是对象类型
        try:
            analysis["active_plan"] = {
                "id": getattr(active_plan, "id", 1),
                "name": getattr(active_plan, "plan_name", "训练计划"),
                "start_date": getattr(active_plan, "start_date", None),
                "end_date": getattr(active_plan, "end_date", None),
                "status": getattr(active_plan, "status", "in_progress")
            }
        except Exception as e:
            logger.error(f"处理训练计划数据时出错: {str(e)}")
            analysis["active_plan"] = {
                "id": 1,
                "name": "训练计划",
                "start_date": None,
                "end_date": None,
                "status": "in_progress"
            }

    # 统计已完成的训练
    # 检查workout是字典还是对象
    if recent_workouts and isinstance(recent_workouts[0], dict):
        completed_workouts = [w for w in recent_workouts if w.get("status", "") == "completed"]
    else:
        completed_workouts = [w for w in recent_workouts if getattr(w, "status", "") == "completed"]

    analysis["completed_workouts"] = len(completed_workouts)

    # 计算完成率
    if recent_workouts:
        analysis["completion_rate"] = round(len(completed_workouts) / len(recent_workouts) * 100, 1)

    # 分析训练频率
    if completed_workouts:
        # 获取最早和最晚的训练日期
        workout_dates = []
        for w in completed_workouts:
            if isinstance(w, dict):
                date_val = w.get("date")
            else:
                date_val = getattr(w, "date", None)

            if date_val is not None:
                workout_dates.append(date_val)

        if workout_dates:
            earliest_date = min(workout_dates)
            latest_date = max(workout_dates)
            analysis["last_workout_date"] = latest_date

            # 计算日期范围
            date_range = (latest_date - earliest_date).days + 1
            if date_range > 0:
                analysis["workout_frequency"] = round(len(workout_dates) / date_range * 7, 1)  # 每周平均次数

            # 计算连续训练天数
            today = datetime.now().date()
            streak = 0
            current_date = today

            while current_date in workout_dates or (today - current_date).days <= 1:
                if current_date in workout_dates:
                    streak += 1
                current_date = current_date - timedelta(days=1)

            analysis["streak"] = streak

    # 分析训练部位分布
    body_parts_count = {}
    for workout in recent_workouts:
        # 获取训练的动作
        if isinstance(workout, dict):
            workout_exercises = workout.get("workout_exercises", [])
        else:
            workout_exercises = getattr(workout, "workout_exercises", [])

        for exercise in workout_exercises:
            # 获取动作的身体部位
            if isinstance(exercise, dict):
                exercise_obj = exercise.get("exercise")
                if exercise_obj:
                    if isinstance(exercise_obj, dict):
                        body_parts = exercise_obj.get("body_parts", [])
                    else:
                        body_parts = getattr(exercise_obj, "body_parts", [])
                else:
                    continue
            elif hasattr(exercise, "exercise") and exercise.exercise:
                if isinstance(exercise.exercise, dict):
                    body_parts = exercise.exercise.get("body_parts", [])
                else:
                    body_parts = getattr(exercise.exercise, "body_parts", [])
            else:
                continue

            for bp in body_parts:
                if isinstance(bp, dict):
                    body_part_name = bp.get("name", str(bp))
                elif hasattr(bp, "name"):
                    body_part_name = bp.name
                else:
                    body_part_name = str(bp)

                body_parts_count[body_part_name] = body_parts_count.get(body_part_name, 0) + 1

    analysis["body_parts_focus"] = body_parts_count

    # 分析进步趋势
    # 这里可以添加更复杂的进步分析，如重量增加、组数增加等
    # 简化版本仅检查是否有持续训练
    if analysis["workout_frequency"] > 0:
        analysis["progress_trends"]["consistency"] = "positive" if analysis["workout_frequency"] >= 3 else "neutral"
    else:
        analysis["progress_trends"]["consistency"] = "negative"

    return analysis

async def _generate_progress_report(
    analysis: Dict[str, Any],
    user_message: str,
    user_info: Dict[str, Any]
) -> str:
    """根据分析结果生成进度报告"""
    # 如果没有训练记录，返回默认消息
    if analysis["total_plans"] == 0:
        return "您目前还没有任何训练计划记录。要开始跟踪您的训练进度，请先创建一个训练计划。"

    # 构建基本报告
    report = "## 训练进度分析\n\n"

    # 添加活跃计划信息
    active_plan = analysis["active_plan"]
    if active_plan:
        report += f"**当前训练计划**: {active_plan['name']}\n"
        if active_plan.get("start_date") and active_plan.get("end_date"):
            start_date = active_plan["start_date"].strftime("%Y-%m-%d") if isinstance(active_plan["start_date"], datetime) else active_plan["start_date"]
            end_date = active_plan["end_date"].strftime("%Y-%m-%d") if isinstance(active_plan["end_date"], datetime) else active_plan["end_date"]
            report += f"**计划周期**: {start_date} 至 {end_date}\n"
        report += f"**计划状态**: {active_plan['status']}\n\n"

    # 添加训练统计
    report += "### 训练统计\n\n"
    report += f"- **完成率**: {analysis['completion_rate']}%（{analysis['completed_workouts']}/{analysis['total_workouts']}）\n"
    report += f"- **训练频率**: 平均每周 {analysis['workout_frequency']} 次\n"

    if analysis["last_workout_date"]:
        last_date = analysis["last_workout_date"].strftime("%Y-%m-%d") if isinstance(analysis["last_workout_date"], datetime) else analysis["last_workout_date"]
        report += f"- **最近训练**: {last_date}\n"

    if analysis["streak"] > 0:
        report += f"- **连续训练**: {analysis['streak']} 天\n"

    # 添加训练部位分布
    if analysis["body_parts_focus"]:
        report += "\n### 训练部位分布\n\n"
        sorted_parts = sorted(analysis["body_parts_focus"].items(), key=lambda x: x[1], reverse=True)
        for part, count in sorted_parts[:5]:  # 只显示前5个
            report += f"- **{part}**: {count} 次\n"

    # 添加进步评估
    report += "\n### 进步评估\n\n"

    consistency = analysis["progress_trends"].get("consistency", "neutral")
    if consistency == "positive":
        report += "- **训练一致性**: 良好 ✅ - 您保持了稳定的训练频率\n"
    elif consistency == "neutral":
        report += "- **训练一致性**: 一般 ⚠️ - 您的训练频率可以提高\n"
    else:
        report += "- **训练一致性**: 需改进 ❌ - 您的训练频率较低\n"

    # 添加个性化建议
    report += "\n### 个性化建议\n\n"

    # 根据分析结果生成建议
    if analysis["completion_rate"] < 50:
        report += "- 您的训练计划完成率较低，建议设置更现实的训练目标，或者调整训练时间以提高坚持度。\n"

    if analysis["workout_frequency"] < 2:
        report += "- 您的训练频率较低，建议每周至少安排2-3次训练，以保持进步。\n"

    # 检查训练部位平衡性
    if len(analysis["body_parts_focus"]) >= 3:
        max_count = max(analysis["body_parts_focus"].values())
        min_count = min(analysis["body_parts_focus"].values())
        if max_count > min_count * 3:
            report += "- 您的训练部位分布不均衡，建议更加全面地训练各个肌肉群，避免发展不平衡。\n"

    # 使用LLM生成更个性化的建议
    try:
        fitness_goal = user_info.get("fitness_goal", "未知")
        fitness_level = user_info.get("fitness_level", "未知")

        enhanced_advice = await _generate_enhanced_advice(
            analysis,
            fitness_goal,
            fitness_level,
            user_message
        )

        if enhanced_advice:
            report += f"\n{enhanced_advice}\n"
    except Exception as e:
        logger.error(f"生成增强建议失败: {str(e)}")

    return report

async def _generate_enhanced_advice(
    analysis: Dict[str, Any],
    fitness_goal: str,
    fitness_level: str,
    user_message: str
) -> str:
    """使用LLM生成增强的个性化建议"""
    # 构建提示
    prompt = f"""
    作为一名专业的健身教练，请根据以下训练数据为用户提供个性化的训练建议：

    用户健身目标：{fitness_goal}
    用户健身水平：{fitness_level}
    训练完成率：{analysis['completion_rate']}%
    训练频率：每周 {analysis['workout_frequency']} 次
    连续训练天数：{analysis['streak']} 天

    用户问题或消息：{user_message}

    请提供2-3条针对性的建议，帮助用户改进训练效果、保持动力或解决潜在问题。建议应该具体、实用且符合用户的健身水平和目标。
    """

    # 调用LLM
    try:
        response = await llm_service.aget_chat_response(
            messages=[
                {"role": "system", "content": "你是一名专业的健身教练，擅长分析训练数据并提供个性化的训练建议。"},
                {"role": "user", "content": prompt}
            ],
            model=settings.LLM_MODEL,
            temperature=0.7
        )
    except Exception as e:
        logger.error(f"调用LLM生成建议失败: {str(e)}")
        response = "根据您的训练数据，我建议您保持稳定的训练频率，确保每周至少训练3次，并注意训练的全面性，平衡发展各个肌肉群。"

    return response.strip()
