from typing import Dict, List, Any, Optional
import logging

from app.services.state_definitions import ConversationState
from app.services.llm_proxy_service import LLMProxyService
from app.core.config import settings

# 配置日志
logger = logging.getLogger(__name__)

# 全局LLM实例
llm_service = LLMProxyService()

async def general_chat_expert_node(state: ConversationState) -> ConversationState:
    """通用聊天专家节点：处理一般对话和闲聊"""

    # 获取用户最新消息
    user_message = ""
    for msg in reversed(state.messages):
        if msg.role == "user":
            user_message = msg.content
            break

    if not user_message:
        # 没有用户消息，返回默认回复
        from app.services.state_definitions import AnyMessage
        state.messages.append(AnyMessage(
            role="assistant",
            content="您好！我是您的健身助手。有什么我可以帮助您的吗？"
        ))
        return state

    # 构建历史消息列表(最多5轮对话)
    history = []
    msg_count = 0
    for msg in reversed(state.messages):
        if msg_count >= 10:  # 最多5轮对话(10条消息)
            break
        history.insert(0, {"role": msg.role, "content": msg.content})
        msg_count += 1

    # 提取用户信息用于个性化回复
    user_info = state.user_info
    user_name = user_info.get("name", "")

    # 生成回复
    response = await _generate_chat_response(history, user_info)

    # 添加回复到消息列表
    from app.services.state_definitions import AnyMessage
    state.messages.append(AnyMessage(role="assistant", content=response))

    return state

async def _generate_chat_response(history: List[Dict[str, str]], user_info: Dict) -> str:
    """生成通用聊天回复"""
    from app.core.chat_config import MODELS

    # 构建系统提示
    system_prompt = f"""
    你是一位专业、友好且有趣的健身助手，名叫"健身AI助手"。你的主要职责是帮助用户解决健身相关问题，但也能进行轻松愉快的日常对话。

    交流风格:
    1. 自然、友好、亲切但不过分热情
    2. 简洁明了，避免冗长回复
    3. 适当使用表情符号增加亲和力
    4. 鼓励和积极的态度，但不做夸张的承诺
    5. 保持专业但平易近人的语气

    回复特点:
    1. 对健身话题展现专业知识
    2. 关注用户需求，提供有价值的回应
    3. 避免空洞的官方语言
    4. 适当使用用户名称增加个人化体验
    5. 在闲聊中保持自然、有趣的对话流

    禁止事项:
    1. 不提供医疗诊断或治疗建议
    2. 不推荐可能危险的极端健身方法
    3. 不做出超出健身教练范围的承诺
    4. 不对敏感话题发表个人观点

    始终记住你是健身领域的专业助手，即使在闲聊中也要保持这一形象。
    """

    # 添加用户的个性化信息到系统提示(如果有)
    if user_info:
        user_info_str = "\n".join([f"- {k}: {v}" for k, v in user_info.items() if k != "user_id"])
        system_prompt += f"\n\n用户信息:\n{user_info_str}"

    # 首先使用agent-app生成初步回复
    try:
        # 尝试使用agent-app模型
        logger.info("尝试使用agent-app模型生成回复")

        # 获取最后一条用户消息
        last_user_message = ""
        for msg in reversed(history):
            if msg["role"] == "user":
                last_user_message = msg["content"]
                break

        initial_response = await llm_service.aget_chat_response(
            messages=[{"role": "user", "content": last_user_message}],
            model="agent-app",  # 使用agent-app应用
            temperature=0.7
        )
    except Exception as e:
        # 如果agent-app失败，使用默认角色模型
        logger.warning(f"agent-app模型调用失败，使用默认角色模型: {str(e)}")
        initial_response = await llm_service.aget_chat_response(
            system=system_prompt,
            messages=history,
            model=settings.LLM_CHARACTER_MODEL,  # 使用角色模型
            temperature=0.8
        )

    # 使用conversation模型包装回复
    wrapped_response = await llm_service.aget_chat_response(
        messages=[
            {"role": "system", "content": "你是一位专业的健身教练，请用专业、友好的语气包装回复。"},
            {"role": "user", "content": f"请用专业的语气包装以下回答：\n\n{initial_response}"}
        ],
        model=MODELS["conversation"],
        temperature=0.5
    )

    return wrapped_response.strip()