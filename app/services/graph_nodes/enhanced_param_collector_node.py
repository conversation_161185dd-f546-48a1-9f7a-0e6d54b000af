"""
增强版参数收集器节点 - 提供更高效的参数收集功能
"""
from typing import Dict, List, Any, Optional, Tuple
import logging
import json

from app.services.state_definitions import ConversationState
from app.services.llm_proxy_service import LLMProxyService
from app.services.enhanced_parameter_extractor import EnhancedParameterExtractor
from app.core.config import settings

# 配置日志
logger = logging.getLogger(__name__)

# 全局LLM实例
llm_service = LLMProxyService()

# 训练参数配置
TRAINING_PARAMS_CONFIG = {
    # 参数名: [显示名称, 提示语, 是否必需, 优先级(数字越小优先级越高)]
    "body_part": ["训练部位", "您想重点训练哪个部位？", True, 1],
    "training_scene": ["训练场景", "您计划在哪里训练？(健身房/家里)", True, 2],
    "plan_type": ["计划类型", "您需要什么类型的训练计划？(单次/每周)", True, 3],
    "training_goal": ["训练目标", "您的训练目标是什么？(增肌/减脂/力量)", False, 4],
    "fitness_level": ["健身水平", "您的健身水平如何？(初级/中级/高级)", False, 5],
    "equipment": ["可用器材", "您有哪些训练器材可以使用？", False, 6],
    "duration_minutes": ["训练时长", "您每次训练大约有多少时间？(分钟)", False, 7],
    "frequency": ["训练频率", "您一周计划训练几次？", False, 8],
    "intensity": ["训练强度", "您期望的训练强度是什么？(低/中/高)", False, 9],
}

async def enhanced_param_collector_node(state: ConversationState) -> ConversationState:
    """增强版参数收集器节点：更高效地收集训练计划所需参数"""

    # 获取当前意图
    intent = state.flow_state.get("intent", "")

    # 获取已有参数
    existing_params = state.training_params

    # 获取当前正在收集的参数
    current_param = state.flow_state.get("collecting_param")

    # 添加参数收集计数器，防止无限循环
    param_collection_count = state.flow_state.get("param_collection_count", 0)
    state.flow_state["param_collection_count"] = param_collection_count + 1

    # 如果参数收集次数过多，强制退出
    if param_collection_count > 10:
        logger.warning(f"参数收集次数过多({param_collection_count}次)，强制完成参数收集")
        state.flow_state["params_complete"] = True

        # 添加提示消息
        from app.services.state_definitions import AnyMessage
        state.messages.append(AnyMessage(
            role="assistant",
            content="我将根据您已提供的信息为您生成训练计划。如果您有更多需求，请在计划生成后告诉我。"
        ))

        return state

    # 如果有用户回复，尝试解析当前参数
    if current_param:
        user_message = ""
        for msg in reversed(state.messages):
            if msg.role == "user":
                user_message = msg.content
                break

        # 尝试从用户消息中提取参数
        if user_message:
            # 使用增强参数提取器提取参数
            message_history = [{"role": m.role, "content": m.content} for m in state.messages[-5:]]
            extracted_params = await EnhancedParameterExtractor.extract_parameters(
                user_message,
                context={"training_params": existing_params},
                message_history=message_history
            )

            # 更新当前收集的参数
            if current_param in extracted_params and extracted_params[current_param]:
                state.training_params[current_param] = extracted_params[current_param]
                logger.info(f"从用户消息中提取参数 {current_param}: {extracted_params[current_param]}")

            # 同时检查是否提取到了其他参数
            for param, value in extracted_params.items():
                if param != current_param and param in TRAINING_PARAMS_CONFIG and value:
                    state.training_params[param] = value
                    logger.info(f"额外提取到参数 {param}: {value}")

            # 清除当前参数，准备收集下一个
            state.flow_state["collecting_param"] = None

    # 检查是否需要进行批量参数提取
    if not current_param and not state.flow_state.get("params_complete"):
        # 尝试从最近的用户消息中批量提取参数
        user_message = ""
        for msg in reversed(state.messages):
            if msg.role == "user":
                user_message = msg.content
                break

        if user_message:
            # 使用增强参数提取器提取参数
            message_history = [{"role": m.role, "content": m.content} for m in state.messages[-5:]]
            extracted_params = await EnhancedParameterExtractor.extract_parameters(
                user_message,
                context={"training_params": existing_params},
                message_history=message_history
            )

            # 更新参数
            for param, value in extracted_params.items():
                if param in TRAINING_PARAMS_CONFIG and value:
                    state.training_params[param] = value
                    logger.info(f"批量提取参数 {param}: {value}")

    # 检查是否完成所有必要参数
    required_params, missing_params = get_required_and_missing_params(intent, state.training_params)

    if not missing_params:
        # 标记参数收集完成
        state.flow_state["params_complete"] = True

        # 添加AI确认消息
        confirmation_msg = await generate_confirmation_message(state.training_params)
        from app.services.state_definitions import AnyMessage
        state.messages.append(AnyMessage(role="assistant", content=confirmation_msg))

        return state

    # 按优先级排序缺失参数
    missing_params.sort(key=lambda p: TRAINING_PARAMS_CONFIG[p][3])

    # 获取下一个要收集的参数
    next_param = missing_params[0]

    # 设置当前询问的参数
    state.flow_state["collecting_param"] = next_param

    # 添加询问消息
    prompt = generate_param_collection_prompt(next_param, state.training_params, state.user_info)
    from app.services.state_definitions import AnyMessage
    state.messages.append(AnyMessage(role="assistant", content=prompt))

    return state

def get_required_and_missing_params(intent: str, existing_params: Dict[str, Any]) -> Tuple[List[str], List[str]]:
    """获取必要参数和缺失参数

    Args:
        intent: 当前意图
        existing_params: 已有参数

    Returns:
        (必要参数列表, 缺失参数列表)
    """
    # 根据意图确定必要参数
    intent_param_map = {
        "training_plan": ["body_part", "training_scene", "plan_type"],
        "fitness_qa": [],
        "general_chat": []
    }

    # 获取当前意图的必要参数
    required_params = intent_param_map.get(intent, [])

    # 添加配置中标记为必需的参数
    for param, config in TRAINING_PARAMS_CONFIG.items():
        if config[2] and param not in required_params:  # 如果是必需参数且不在列表中
            required_params.append(param)

    # 找出缺失的参数
    missing_params = [p for p in required_params if p not in existing_params or not existing_params[p]]

    return required_params, missing_params

def generate_param_collection_prompt(param: str, existing_params: Dict[str, Any], user_info: Dict[str, Any]) -> str:
    """生成参数收集提示

    Args:
        param: 要收集的参数
        existing_params: 已有参数
        user_info: 用户信息

    Returns:
        参数收集提示
    """
    # 获取参数配置
    param_config = TRAINING_PARAMS_CONFIG.get(param)
    if not param_config:
        return f"请提供您的{param}:"

    display_name, prompt, _, _ = param_config

    # 根据已有参数和用户信息个性化提示
    if param == "body_part" and user_info.get("fitness_goal") == "增肌":
        prompt = "您想重点增肌哪个部位？"
    elif param == "training_scene" and "home" in str(user_info.get("preferred_training_location", "")):
        prompt = "您是打算在家里训练吗？"
    elif param == "equipment" and existing_params.get("training_scene") == "home":
        prompt = "您家里有哪些训练器材可以使用？"

    # 添加上下文信息
    context_info = ""
    if existing_params:
        context_parts = []
        for p, v in existing_params.items():
            if p in TRAINING_PARAMS_CONFIG:
                context_parts.append(f"{TRAINING_PARAMS_CONFIG[p][0]}是{v}")
        if context_parts:
            context_info = f"根据您提供的信息（{', '.join(context_parts)}），"

    # 组合最终提示
    final_prompt = f"{context_info}{prompt}"

    return final_prompt

async def generate_confirmation_message(params: Dict[str, Any]) -> str:
    """生成参数确认消息

    Args:
        params: 收集到的参数

    Returns:
        确认消息
    """
    # 格式化参数
    formatted_params = []
    for param, value in params.items():
        if param in TRAINING_PARAMS_CONFIG:
            display_name = TRAINING_PARAMS_CONFIG[param][0]
            formatted_params.append(f"- {display_name}: {value}")

    params_str = "\n".join(formatted_params)

    # 构造确认消息
    confirmation = f"""
    非常好！我已经收集了以下训练参数：

    {params_str}

    现在我可以根据这些信息为您生成训练计划了。
    """

    return confirmation.strip()
