import re
from typing import Dict, Any
import logging
from ...services.state_definitions import FitnessAssistantState
from typing import Dict, List
import json

from app.services.state_definitions import ConversationState
from app.services.llm_proxy_service import LLMProxyService
from app.core.config import settings

logger = logging.getLogger(__name__)

# 全局LLM实例
llm_service = LLMProxyService()

def intent_router(state: FitnessAssistantState) -> Dict[str, Any]:
    """根据用户消息识别意图并路由到相应专家"""
    messages = state["messages"]
    metadata = state.get("metadata", {})

    # 获取最新的用户消息
    if not messages or messages[-1].type != "human":
        # 没有用户消息，保持当前状态
        return {"dialog_state": state.get("dialog_state", ["router"])}

    user_message = messages[-1].content
    logger.info(f"Routing user message: {user_message[:50]}...")

    # 获取上下文信息
    related_plan_id = metadata.get("related_plan_id")
    active_flow = metadata.get("active_flow")

    # 检查特殊状态
    if metadata.get("waiting_info"):
        logger.info("User in info collection flow")
        return {"dialog_state": "user_info_collector"}

    if metadata.get("collecting_training_params"):
        logger.info("User in parameter collection flow")
        return {"dialog_state": "param_collector"}

    # 检查中断恢复状态
    if metadata.get("interrupt_state"):
        logger.info("Handling potential interruption")
        return {"dialog_state": "interruption_handler"}

    # 意图识别
    # 1. 训练计划相关
    if (related_plan_id and ("计划" in user_message or "训练" in user_message)) or \
       re.search(r"(训练|健身)(计划|方案)", user_message) or \
       re.search(r"(每天|每周|一周)(训练|锻炼)", user_message):
        logger.info("Routing to training plan expert")
        return {"dialog_state": "training_plan_expert"}

    # 2. 动作推荐相关
    if re.search(r"([^怎么]+)怎么练", user_message) or \
       re.search(r"(推荐|有哪些)(动作|训练|锻炼)", user_message) or \
       "动作" in user_message:
        logger.info("Routing to exercise recommendation expert")
        return {"dialog_state": "exercise_recommendation_expert"}

    # 3. 健身/营养咨询
    if any(term in user_message for term in ["饮食", "营养", "建议", "如何"]):
        logger.info("Routing to fitness Q&A expert")
        return {"dialog_state": "fitness_qa_expert"}

    # 默认路由
    logger.info("Routing to general chat expert")
    return {"dialog_state": "general_chat_expert"}

async def router_node(state: ConversationState) -> ConversationState:
    """路由节点：分析用户意图并决定下一步处理流程"""

    # 如果已经有明确意图，则直接使用
    if state.flow_state.get("intent"):
        return state

    # 获取用户最新消息
    user_message = ""
    for msg in reversed(state.messages):
        if msg.role == "user":
            user_message = msg.content
            break

    if not user_message:
        # 没有用户消息，直接返回
        state.flow_state["intent"] = "general_chat"
        return state

    # 检查是否需要收集用户信息
    if _check_user_info_needed(state):
        state.flow_state["needs_user_info"] = True
        return state

    # 使用意图识别模型分析意图
    intent = await _detect_intent(user_message)
    state.flow_state["intent"] = intent

    # 对于训练计划意图，检查是否需要收集参数
    if intent == "training_plan" and _check_training_params_needed(state):
        state.flow_state["needs_param_collection"] = True

    return state

async def _detect_intent(message: str) -> str:
    """检测用户消息意图"""
    # 使用百炼应用的agent-app进行意图识别
    from app.core.chat_config import MODELS, BAILIAN_APPS

    # 记录使用的模型
    logger.debug(f"使用百炼应用agent-app进行意图识别")

    try:
        # 使用agent-app进行意图识别
        response = await llm_service.aget_chat_response(
            messages=[{"role": "user", "content": message}],
            model="agent-app",  # 使用agent-app应用
            temperature=0.1
        )

        # 清理响应，提取意图关键词
        response = response.strip().lower()
        logger.debug(f"意图识别原始响应: {response}")

        # 使用更精确的匹配逻辑
        if any(term in response for term in ["训练计划", "健身计划", "锻炼计划", "计划"]):
            return "training_plan"
        elif any(term in response for term in ["动作", "训练动作", "锻炼动作", "健身动作", "怎么练"]):
            return "exercise_info"
        elif any(term in response for term in ["体态", "姿势", "体型", "体姿"]):
            return "fitness_qa"  # 体态相关问题归类到fitness_qa
        elif any(term in response for term in ["饮食", "营养", "饮食建议", "营养咨询", "食物"]):
            return "diet_advice"  # 饮食营养相关问题单独归类
        elif any(term in response for term in ["健身咨询", "健身问题", "健身建议", "肌肉", "力量"]):
            return "fitness_qa"
        else:
            # 如果无法明确匹配，使用备用规则
            if "计划" in message or "方案" in message:
                return "training_plan"
            elif "动作" in message or "怎么练" in message:
                return "exercise_info"
            elif any(term in message for term in ["饮食", "营养", "吃"]):
                return "diet_advice"
            elif any(term in message for term in ["健身", "训练", "锻炼", "体态", "姿势"]):
                return "fitness_qa"
            else:
                return "general_chat"

    except Exception as e:
        # 如果意图识别失败，记录错误并返回默认意图
        logger.error(f"意图识别失败: {str(e)}")
        return "general_chat"

def _check_user_info_needed(state: ConversationState) -> bool:
    """检查是否需要收集用户信息"""
    # 基本的必要字段
    required_fields = ["gender", "age", "height", "weight", "fitness_goal", "fitness_level"]

    # 检查用户信息是否完整
    if not state.user_info:
        return True

    for field in required_fields:
        if field not in state.user_info or not state.user_info[field]:
            return True

    return False

def _check_training_params_needed(state: ConversationState) -> bool:
    """检查是否需要收集训练参数"""
    if not state.training_params:
        return True

    # 训练计划需要的基本参数
    required_params = ["training_days", "available_time", "equipment_available"]

    for param in required_params:
        if param not in state.training_params:
            return True

    return False