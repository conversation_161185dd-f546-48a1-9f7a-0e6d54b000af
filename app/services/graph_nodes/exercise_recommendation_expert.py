from typing import Dict, Any, List
import logging
import re
from langchain_core.messages import AIMessage
from ...services.state_definitions import FitnessAssistantState
from ...services.exercise import ExerciseService
from ...services.active_query_manager import ActiveQueryManager
from ...services.llm_proxy_service import LLMProxyService
from ...db.session import SessionLocal

logger = logging.getLogger(__name__)

class ExerciseRecommendationExpert:
    def __init__(self):
        self.db = SessionLocal()
        self.exercise_service = ExerciseService(self.db)
        # 创建 LLMProxyService 实例
        self.llm_service = LLMProxyService()
        # 传递 llm_service 给 ActiveQueryManager
        self.param_manager = ActiveQueryManager(self.llm_service)
    
    def __call__(self, state: FitnessAssistantState) -> Dict[str, Any]:
        """动作推荐专家节点"""
        messages = state["messages"]
        training_params = state.get("training_params", {})
        user_info = state.get("user_info", {})
        metadata = state.get("metadata", {})
        
        # 获取最新用户消息
        user_message = messages[-1].content if messages and messages[-1].type == "human" else ""
        
        # 尝试从消息中提取身体部位和其他参数
        if not training_params.get("body_part"):
            # 尝试从"X怎么练"模式提取
            match = re.search(r"([^怎么]+)怎么练", user_message)
            if match:
                training_params["body_part"] = match.group(1)
        
        # 使用参数管理器提取更多参数
        try:
            extracted_params = self.param_manager.extract_training_params(user_message)
            for key, value in extracted_params.items():
                if value:  # 只添加非空值
                    training_params[key] = value
        except Exception as e:
            logger.warning(f"Parameter extraction error: {e}")
        
        # 检查必要参数
        required_params = ["body_part"]
        missing_params = [p for p in required_params if p not in training_params]
        
        if missing_params:
            # 需要收集参数
            return {
                "metadata": {
                    **metadata,
                    "collecting_training_params": True,
                    "asking_param": missing_params[0],
                    "original_intent": "exercise_recommendation"
                },
                "dialog_state": "param_collector"
            }
        
        # 所有必要参数已具备，推荐动作
        try:
            # 获取训练场景参数，默认为"健身房"
            training_scene = training_params.get("training_scene", "健身房")
            
            # 获取健身水平参数，默认为"初级"
            fitness_level = user_info.get("fitness_level", "初级")
            
            # 调用服务查询推荐动作
            exercises = self.exercise_service.recommend_exercises(
                body_part=training_params["body_part"],
                scene=training_scene,
                level=fitness_level,
                equipment=training_params.get("equipment", None)
            )
            
            if not exercises:
                return {
                    "messages": [AIMessage(content=f"抱歉，我没有找到适合训练{training_params['body_part']}的动作。请尝试其他身体部位或训练场景。")],
                    "dialog_state": "pop"
                }
            
            # 构建响应
            response = f"以下是针对{training_params['body_part']}的推荐训练动作:\n\n"
            
            for i, exercise in enumerate(exercises[:5], 1):  # 限制为前5个
                response += f"{i}. **{exercise.get('name', '未命名动作')}**\n"
                response += f"   难度: {exercise.get('difficulty', '中等')}\n"
                if exercise.get('target_muscles'):
                    response += f"   主要目标: {exercise.get('target_muscles')}\n"
                if exercise.get('description'):
                    response += f"   简介: {exercise.get('description')}\n"
                response += "\n"
            
            response += "\n您可以询问任何动作的详细做法、注意事项或替代动作。"
            
            # 将找到的动作存储在元数据中，以便后续参考
            exercise_names = [e.get('name', '未命名动作') for e in exercises[:5]]
            exercise_details = {e.get('name', f'动作{i}'): e for i, e in enumerate(exercises[:5])}
            
            return {
                "messages": [AIMessage(content=response)],
                "metadata": {
                    **metadata,
                    "recommended_exercises": exercise_names,
                    "exercise_details": exercise_details
                },
                "dialog_state": "pop"  # 返回路由器
            }
            
        except Exception as e:
            logger.error(f"Error recommending exercises: {e}")
            return {
                "messages": [AIMessage(content="抱歉，推荐训练动作时遇到了问题。请稍后再试。")],
                "dialog_state": "pop"
            }

# 创建实例供图使用
exercise_recommendation_expert = ExerciseRecommendationExpert() 