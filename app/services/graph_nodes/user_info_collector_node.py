from typing import Dict, List, Any, Optional
import json
import logging

from sqlalchemy.orm import Session
from fastapi import Depends

from app.services.state_definitions import ConversationState
from app.services.llm_proxy_service import LLMProxyService
from app.core.config import settings
from app.db.session import SessionLocal
from app.crud import crud_user

# 配置日志
logger = logging.getLogger(__name__)

# 全局LLM实例
llm_service = LLMProxyService()

# 用户信息字段提示词
USER_INFO_PROMPTS = {
    "gender": "您的性别是？(男/女)",
    "age": "您的年龄是？",
    "height": "您的身高是多少？(厘米)",
    "weight": "您的体重是多少？(公斤)",
    "fitness_goal": "您的健身目标是什么？(增肌/减脂/塑形/增强体能)",
    "fitness_level": "您的健身水平如何？(初学者/中级/高级)"
}

# 字段验证函数
async def validate_gender(value: str) -> Optional[str]:
    """验证性别"""
    value = value.strip().lower()
    if "男" in value or "male" in value:
        return "male"
    elif "女" in value or "female" in value:
        return "female"
    return None

async def validate_age(value: str) -> Optional[int]:
    """验证年龄"""
    try:
        age = int(''.join(filter(str.isdigit, value)))
        if 10 <= age <= 100:
            return age
    except:
        pass
    return None

async def validate_height(value: str) -> Optional[float]:
    """验证身高"""
    try:
        # 提取数字
        import re
        digits = re.findall(r'\d+\.?\d*', value)
        if digits:
            height = float(digits[0])
            # 如果是米为单位，转换为厘米
            if height < 3:
                height *= 100
            if 140 <= height <= 220:
                return round(height, 1)
    except:
        pass
    return None

async def validate_weight(value: str) -> Optional[float]:
    """验证体重"""
    try:
        # 提取数字
        import re
        digits = re.findall(r'\d+\.?\d*', value)
        if digits:
            weight = float(digits[0])
            if 30 <= weight <= 200:
                return round(weight, 1)
    except:
        pass
    return None

async def validate_fitness_goal(value: str) -> Optional[int]:
    """验证健身目标"""
    # 映射到数值(与数据库定义一致)
    goal_map = {
        "增肌": 1, "muscle": 1, "增长肌肉": 1, "hypertrophy": 1,
        "减脂": 2, "fat loss": 2, "瘦身": 2, "减肥": 2,
        "塑形": 3, "tone": 3, "紧致": 3, "definition": 3,
        "体能": 4, "endurance": 4, "耐力": 4, "cardio": 4
    }

    value = value.lower()
    for key, val in goal_map.items():
        if key in value:
            return val

    # 使用LLM分析不确定的回答
    prompt = f"""
    分析用户的健身目标回答，并将其归类为以下选项之一：
    1. 增肌/肌肥大
    2. 减脂/减肥
    3. 塑形/紧致
    4. 增强体能/耐力

    用户回答: "{value}"

    只返回数字1-4，不要有任何解释。
    """

    response = await llm_service.aget_chat_response(
        messages=[
            {"role": "system", "content": "你是健身目标分析专家，将用户回答映射到标准类别。"},
            {"role": "user", "content": prompt}
        ],
        model=settings.LLM_MODEL,
        temperature=0.1
    )

    try:
        # 提取数字
        import re
        digits = re.findall(r'\d+', response)
        if digits and 1 <= int(digits[0]) <= 4:
            return int(digits[0])
    except:
        pass

    return None

async def validate_fitness_level(value: str) -> Optional[int]:
    """验证健身水平"""
    # 映射到数值(与数据库定义一致)
    level_map = {
        "初学者": 1, "beginner": 1, "新手": 1, "入门": 1,
        "中级": 2, "intermediate": 2, "有经验": 2,
        "高级": 3, "advanced": 3, "专业": 3, "expert": 3
    }

    value = value.lower()
    for key, val in level_map.items():
        if key in value:
            return val

    # 使用LLM分析不确定的回答
    prompt = f"""
    分析用户的健身水平回答，并将其归类为以下选项之一：
    1. 初学者/新手
    2. 中级/有经验
    3. 高级/专业

    用户回答: "{value}"

    只返回数字1-3，不要有任何解释。
    """

    response = await llm_service.aget_chat_response(
        messages=[
            {"role": "system", "content": "你是健身水平分析专家，将用户回答映射到标准类别。"},
            {"role": "user", "content": prompt}
        ],
        model=settings.LLM_MODEL,
        temperature=0.1
    )

    try:
        # 提取数字
        import re
        digits = re.findall(r'\d+', response)
        if digits and 1 <= int(digits[0]) <= 3:
            return int(digits[0])
    except:
        pass

    return None

# 映射字段名到验证函数
VALIDATION_FUNCTIONS = {
    "gender": validate_gender,
    "age": validate_age,
    "height": validate_height,
    "weight": validate_weight,
    "fitness_goal": validate_fitness_goal,
    "fitness_level": validate_fitness_level
}

async def user_info_collector_node(state: ConversationState) -> ConversationState:
    """用户信息收集节点"""

    # 获取当前收集状态
    current_field = state.flow_state.get("asking_field")

    # 如果有待更新的用户信息，尝试更新到数据库
    if state.flow_state.get("user_info_to_update") and state.user_info.get("user_id"):
        try:
            # 获取数据库会话
            db = SessionLocal()
            try:
                # 更新用户信息
                user_id = state.user_info["user_id"]
                user_data = state.flow_state["user_info_to_update"]

                # 过滤掉无效值
                valid_data = {k: v for k, v in user_data.items() if v is not None}

                if valid_data:
                    crud_user.update(db, db_obj=crud_user.get(db, id=user_id), obj_in=valid_data)

                    # 更新状态中的用户信息
                    state.user_info.update(valid_data)

                # 清除待更新数据
                state.flow_state["user_info_to_update"] = {}
            finally:
                db.close()
        except Exception as e:
            logger.error(f"更新用户信息失败: {str(e)}")

    # 如果有用户回复，尝试解析当前字段
    if current_field:
        user_message = ""
        for msg in reversed(state.messages):
            if msg.role == "user":
                user_message = msg.content
                break

        # 跳过选项处理
        if user_message and "跳过" in user_message:
            # 记录跳过的字段
            if "skipped_fields" not in state.flow_state:
                state.flow_state["skipped_fields"] = []
            state.flow_state["skipped_fields"].append(current_field)

            # 添加AI回复
            from app.services.state_definitions import AnyMessage
            state.messages.append(AnyMessage(
                role="assistant",
                content=f"好的，我们先跳过{current_field}这项信息。"
            ))

            # 清除当前字段，准备下一个
            state.flow_state["asking_field"] = None

        # 正常解析回复
        elif user_message:
            # 验证用户输入
            validator = VALIDATION_FUNCTIONS.get(current_field)
            if validator:
                valid_value = await validator(user_message)

                if valid_value is not None:
                    # 准备更新用户信息
                    if "user_info_to_update" not in state.flow_state:
                        state.flow_state["user_info_to_update"] = {}
                    state.flow_state["user_info_to_update"][current_field] = valid_value

                    # 添加确认消息
                    from app.services.state_definitions import AnyMessage
                    confirm_msg = f"谢谢！我已记录您的{current_field}信息。"
                    state.messages.append(AnyMessage(role="assistant", content=confirm_msg))
                else:
                    # 添加重试消息
                    from app.services.state_definitions import AnyMessage
                    retry_msg = f"抱歉，我无法理解您提供的{current_field}信息，请重新输入或输入'跳过'。"
                    state.messages.append(AnyMessage(role="assistant", content=retry_msg))

                    # 保持当前字段，要求重新输入
                    return state

            # 清除当前字段，准备收集下一个
            state.flow_state["asking_field"] = None

    # 检查是否完成所有必要字段
    if _check_user_info_complete(state):
        # 标记用户信息收集完成
        state.flow_state["user_info_complete"] = True

        # 添加完成消息
        from app.services.state_definitions import AnyMessage
        complete_msg = "谢谢您提供的信息！现在我可以更好地为您提供个性化的健身建议。"
        state.messages.append(AnyMessage(role="assistant", content=complete_msg))

        return state

    # 找出下一个需要收集的字段
    next_field = _get_next_field_to_collect(state)

    if next_field:
        # 设置当前询问的字段
        state.flow_state["asking_field"] = next_field

        # 添加询问消息
        from app.services.state_definitions import AnyMessage
        prompt = USER_INFO_PROMPTS.get(next_field, f"请提供您的{next_field}:")
        state.messages.append(AnyMessage(role="assistant", content=prompt))

    return state

def _check_user_info_complete(state: ConversationState) -> bool:
    """检查用户信息是否完整"""
    # 所有字段
    all_fields = set(USER_INFO_PROMPTS.keys())

    # 已有的字段
    existing_fields = set(k for k, v in state.user_info.items() if v and k in all_fields)

    # 待更新的字段
    to_update = set(state.flow_state.get("user_info_to_update", {}).keys())

    # 已跳过的字段
    skipped = set(state.flow_state.get("skipped_fields", []))

    # 计算还缺少的字段
    missing = all_fields - existing_fields - to_update - skipped

    return len(missing) == 0

def _get_next_field_to_collect(state: ConversationState) -> Optional[str]:
    """获取下一个需要收集的字段"""
    # 所有字段
    all_fields = list(USER_INFO_PROMPTS.keys())

    # 已有的字段
    existing_fields = set(k for k, v in state.user_info.items() if v and k in all_fields)

    # 待更新的字段
    to_update = set(state.flow_state.get("user_info_to_update", {}).keys())

    # 已跳过的字段
    skipped = set(state.flow_state.get("skipped_fields", []))

    # 按顺序检查缺失字段
    for field in all_fields:
        if field not in existing_fields and field not in to_update and field not in skipped:
            return field

    return None