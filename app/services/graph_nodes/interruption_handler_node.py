"""
中断处理节点 - 负责检测和处理对话中断，维护对话连贯性
"""
from typing import Dict, Any, List, Optional
import logging
import time
from datetime import datetime, timedelta

from app.services.state_definitions import ConversationState
from app.services.llm_proxy_service import LLMProxyService
from app.core.chat_config import MODELS

# 配置日志
logger = logging.getLogger(__name__)

# 全局LLM实例
llm_service = LLMProxyService()

# 中断检测配置
INTERRUPTION_CONFIG = {
    "time_threshold": 30 * 60,  # 30分钟，超过此时间视为可能中断
    "relevance_threshold": 0.6,  # 相关性阈值，低于此值视为话题转换
}

async def interruption_handler_node(state: ConversationState) -> ConversationState:
    """中断处理节点：检测和处理对话中断，维护对话连贯性"""

    # 检查是否处于中断确认状态
    if state.meta_info.get("confirming_interruption"):
        # 处理用户对中断确认的回复
        return await _handle_interruption_confirmation(state)

    # 检查是否有中断标记
    if state.meta_info.get("potential_interruption"):
        # 处理潜在中断
        return await _handle_potential_interruption(state)

    # 检查是否需要进行中断检测
    if _should_check_interruption(state):
        # 进行中断检测
        interruption_detected = await _detect_interruption(state)
        if interruption_detected:
            # 标记潜在中断
            state.meta_info["potential_interruption"] = True
            # 保存当前状态
            state.meta_info["original_state"] = {
                "flow_state": state.flow_state.copy(),
                "intent": state.flow_state.get("intent"),
                "active_flow": state.meta_info.get("active_flow"),
                "related_plan_id": state.meta_info.get("related_plan_id"),
                "last_message": state.messages[-1].content if state.messages else ""
            }
            # 询问用户是否继续之前的对话
            from app.services.state_definitions import AnyMessage
            confirmation_message = "我注意到我们之前的对话似乎被中断了。您想继续之前的话题，还是开始新的对话？"
            state.messages.append(AnyMessage(role="assistant", content=confirmation_message))
            # 设置确认状态
            state.meta_info["confirming_interruption"] = True
            return state

    # 没有中断或不需要检测，继续正常流程
    return state

async def _handle_interruption_confirmation(state: ConversationState) -> ConversationState:
    """处理用户对中断确认的回复"""
    # 获取用户最新消息
    user_message = ""
    for msg in reversed(state.messages):
        if msg.role == "user":
            user_message = msg.content
            break

    # 判断用户是否想继续之前的对话
    continue_previous = await _check_continue_previous(user_message)

    if continue_previous:
        # 用户想继续之前的对话，恢复原始状态
        logger.info("用户选择继续之前的对话，恢复原始状态")
        original_state = state.meta_info.get("original_state", {})

        # 恢复流程状态
        if "flow_state" in original_state:
            state.flow_state = original_state["flow_state"]

        # 恢复活跃流程和关联计划ID
        if "active_flow" in original_state:
            state.meta_info["active_flow"] = original_state["active_flow"]
        if "related_plan_id" in original_state:
            state.meta_info["related_plan_id"] = original_state["related_plan_id"]

        # 添加恢复提示
        from app.services.state_definitions import AnyMessage
        resume_message = "好的，让我们继续之前的对话。"
        state.messages.append(AnyMessage(role="assistant", content=resume_message))
    else:
        # 用户想开始新的对话，清除之前的状态
        logger.info("用户选择开始新的对话，清除之前的状态")
        # 清除流程状态
        state.flow_state = {}
        # 清除活跃流程和关联计划ID
        state.meta_info.pop("active_flow", None)
        state.meta_info.pop("related_plan_id", None)

        # 添加新对话提示
        from app.services.state_definitions import AnyMessage
        new_dialog_message = "好的，让我们开始新的对话。有什么我可以帮助您的？"
        state.messages.append(AnyMessage(role="assistant", content=new_dialog_message))

    # 清除中断相关状态
    state.meta_info.pop("confirming_interruption", None)
    state.meta_info.pop("potential_interruption", None)
    state.meta_info.pop("original_state", None)

    return state

async def _handle_potential_interruption(state: ConversationState) -> ConversationState:
    """处理潜在中断"""
    # 获取用户最新消息
    user_message = ""
    for msg in reversed(state.messages):
        if msg.role == "user":
            user_message = msg.content
            break

    # 检查用户消息与之前对话的相关性
    relevance = await _check_message_relevance(
        user_message,
        state.meta_info.get("original_state", {}).get("last_message", "")
    )

    if relevance > INTERRUPTION_CONFIG["relevance_threshold"]:
        # 消息相关，不是真正的中断，继续之前的对话
        logger.info(f"消息相关性高 ({relevance:.2f})，继续之前的对话")
        # 清除中断标记
        state.meta_info.pop("potential_interruption", None)
        return state
    else:
        # 消息不相关，确认是否中断
        logger.info(f"消息相关性低 ({relevance:.2f})，询问用户是否中断")
        # 设置确认状态
        state.meta_info["confirming_interruption"] = True
        # 询问用户是否继续之前的对话
        from app.services.state_definitions import AnyMessage
        confirmation_message = "我注意到您的问题似乎与我们之前的对话不太相关。您想继续之前的话题，还是开始讨论这个新问题？"
        state.messages.append(AnyMessage(role="assistant", content=confirmation_message))
        return state

def _should_check_interruption(state: ConversationState) -> bool:
    """检查是否需要进行中断检测"""
    # 如果没有活跃流程或关联计划ID，不需要检测中断
    if not state.meta_info.get("active_flow") and not state.meta_info.get("related_plan_id"):
        return False

    # 如果消息数量不足，不需要检测中断
    if len(state.messages) < 3:
        return False

    # 获取最新的用户消息和上一条助手消息的时间戳
    user_message_time = None
    assistant_message_time = None

    for msg in reversed(state.messages):
        if msg.role == "user" and user_message_time is None:
            user_message_time = msg.timestamp if hasattr(msg, "timestamp") else None
        elif msg.role == "assistant" and assistant_message_time is None:
            assistant_message_time = msg.timestamp if hasattr(msg, "timestamp") else None

        if user_message_time and assistant_message_time:
            break

    # 如果没有时间戳信息，不进行中断检测
    if not user_message_time or not assistant_message_time:
        return False

    # 计算时间差
    try:
        time_diff = (user_message_time - assistant_message_time).total_seconds()
        return time_diff > INTERRUPTION_CONFIG["time_threshold"]
    except:
        return False

async def _detect_interruption(state: ConversationState) -> bool:
    """检测对话是否中断"""
    # 获取最新的用户消息
    user_message = ""
    for msg in reversed(state.messages):
        if msg.role == "user":
            user_message = msg.content
            break

    # 获取上下文信息
    active_flow = state.meta_info.get("active_flow")
    related_plan_id = state.meta_info.get("related_plan_id")

    # 构建上下文描述
    context = ""
    if active_flow:
        context += f"当前对话流程: {active_flow}. "
    if related_plan_id:
        context += f"正在讨论的训练计划ID: {related_plan_id}. "

    # 如果没有上下文，不视为中断
    if not context:
        return False

    # 使用LLM判断消息是否与当前上下文相关
    prompt = f"""
    请分析用户的最新消息是否与当前对话上下文相关。

    当前对话上下文:
    {context}

    用户最新消息:
    "{user_message}"

    这条消息是否与当前对话上下文相关？请只回答"相关"或"不相关"。
    """

    try:
        response = await llm_service.aget_chat_response(
            messages=[
                {"role": "system", "content": "你是一个对话分析助手，负责判断用户消息是否与当前对话上下文相关。"},
                {"role": "user", "content": prompt}
            ],
            model=MODELS["intent_recognition"],
            temperature=0.1
        )

        # 解析响应
        is_relevant = "相关" in response.lower()
        logger.info(f"中断检测结果: {'相关' if is_relevant else '不相关'}, 原始响应: {response}")

        # 返回中断检测结果（不相关=中断）
        return not is_relevant
    except Exception as e:
        logger.error(f"中断检测失败: {str(e)}")
        # 出错时默认不视为中断
        return False

async def _check_continue_previous(message: str) -> bool:
    """检查用户是否想继续之前的对话"""
    prompt = f"""
    请分析用户的回复，判断用户是想继续之前的对话，还是开始新的对话。

    用户回复:
    "{message}"

    如果用户表示想继续之前的对话（例如："继续"、"是的"、"好的"、"继续之前的"等），判断为"继续"。
    如果用户表示想开始新的对话（例如："新对话"、"不用了"、"开始新的"等），判断为"新对话"。

    请只回答"继续"或"新对话"。
    """

    try:
        response = await llm_service.aget_chat_response(
            messages=[
                {"role": "system", "content": "你是一个对话分析助手，负责判断用户是否想继续之前的对话。"},
                {"role": "user", "content": prompt}
            ],
            model=MODELS["intent_recognition"],
            temperature=0.1
        )

        # 解析响应
        continue_previous = "继续" in response.lower()
        logger.info(f"用户意图分析结果: {'继续之前对话' if continue_previous else '开始新对话'}, 原始响应: {response}")

        return continue_previous
    except Exception as e:
        logger.error(f"分析用户回复失败: {str(e)}")
        # 出错时默认继续之前的对话
        return True

async def _check_message_relevance(message1: str, message2: str) -> float:
    """检查两条消息的相关性"""
    if not message1 or not message2:
        return 0.0

    prompt = f"""
    请分析以下两条消息的相关性，并给出一个0到1之间的相关性分数。
    0表示完全不相关，1表示高度相关。

    消息1:
    "{message1}"

    消息2:
    "{message2}"

    相关性分数(0-1):
    """

    try:
        response = await llm_service.aget_chat_response(
            messages=[
                {"role": "system", "content": "你是一个文本分析助手，负责判断两条消息的相关性。"},
                {"role": "user", "content": prompt}
            ],
            model=MODELS["intent_recognition"],
            temperature=0.1
        )

        # 尝试从响应中提取数字
        import re
        match = re.search(r'(\d+(\.\d+)?)', response)
        if match:
            relevance = float(match.group(1))
            # 确保相关性在0-1范围内
            relevance = max(0.0, min(1.0, relevance))
            return relevance
        else:
            # 如果无法提取数字，根据关键词判断
            if "高度相关" in response or "非常相关" in response:
                return 0.9
            elif "相关" in response:
                return 0.7
            elif "部分相关" in response or "有一定相关性" in response:
                return 0.5
            elif "不太相关" in response:
                return 0.3
            elif "不相关" in response or "完全不相关" in response:
                return 0.1
            else:
                return 0.5
    except Exception as e:
        logger.error(f"检查消息相关性失败: {str(e)}")
        # 出错时默认中等相关性
        return 0.5
