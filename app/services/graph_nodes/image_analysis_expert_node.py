from typing import Dict, Any, List, Optional
import logging
import time
from datetime import datetime

from app.services.state_definitions import ConversationState, AnyMessage
from app.db.session import SessionLocal
from app.services.llm_proxy_service import LLMProxyService
from app.core.config import settings

logger = logging.getLogger(__name__)
llm_service = LLMProxyService()

async def image_analysis_expert_node(state: ConversationState) -> ConversationState:
    """图像分析专家节点：处理图像识别结果并提供建议"""

    # 获取图像分析结果
    image_analysis_result = state.meta_info.get("image_analysis_result")

    if not image_analysis_result:
        state.messages.append(AnyMessage(
            role="assistant",
            content="抱歉，我没有找到相关的图像分析结果。"
        ))
        return state

    # 处理食物识别结果
    if image_analysis_result.get("is_food", False):
        food_items = image_analysis_result.get("food_items", [])
        nutrition_info = image_analysis_result.get("nutrition_info", {})
        meal_type = image_analysis_result.get("meal_type", "未知")

        # 生成食物识别响应
        response = await _generate_food_analysis_response(
            food_items,
            nutrition_info,
            state.user_info,
            meal_type,
            state.training_params
        )
    else:
        # 非食物图像
        response = "这似乎不是食物图像，我无法提供相关的营养分析。"

    # 添加响应到消息列表
    state.messages.append(AnyMessage(role="assistant", content=response))

    # 清除图像分析结果，避免重复处理
    # 但保留基本信息用于后续参考
    if "image_analysis_result" in state.meta_info:
        basic_info = {
            "meal_type": image_analysis_result.get("meal_type", ""),
            "meal_date": image_analysis_result.get("meal_date", ""),
            "processed": True,
            "processed_time": time.time()
        }
        state.meta_info["image_analysis_basic_info"] = basic_info
        # 保留nutrition_info用于后续训练建议
        state.meta_info["last_meal_nutrition"] = image_analysis_result.get("nutrition_info", {})

    return state

async def _generate_food_analysis_response(
    food_items: List[Dict[str, Any]],
    nutrition_info: Dict[str, Any],
    user_info: Dict[str, Any],
    meal_type: str,
    training_params: Optional[Dict[str, Any]] = None
) -> str:
    """生成食物分析响应"""
    if not food_items:
        return "我无法识别图片中的食物，请尝试上传更清晰的图片。"

    # 构建食物列表文本
    food_list_text = ""
    for i, item in enumerate(food_items, 1):
        name = item.get("name", "未知食物")
        calory = item.get("calory", "未知")
        protein = item.get("protein", "未知")
        fat = item.get("fat", "未知")
        carbohydrate = item.get("carbohydrate", "未知")

        food_list_text += f"{i}. {name}\n"
        food_list_text += f"   热量: {calory}千卡, 蛋白质: {protein}g, 脂肪: {fat}g, 碳水: {carbohydrate}g\n"

    # 构建营养总结
    total_calory = nutrition_info.get("calory", "未知")
    total_protein = nutrition_info.get("protein", "未知")
    total_fat = nutrition_info.get("fat", "未知")
    total_carbohydrate = nutrition_info.get("carbohydrate", "未知")

    nutrition_summary = f"总热量: {total_calory}千卡, 总蛋白质: {total_protein}g, 总脂肪: {total_fat}g, 总碳水: {total_carbohydrate}g"

    # 获取健康建议
    health_recommendation = nutrition_info.get("health_recommendation", "")

    # 根据用户健身目标提供个性化建议
    fitness_goal = user_info.get("fitness_goal")
    personalized_advice = ""

    if fitness_goal == "增肌":
        if isinstance(total_protein, (int, float)) and isinstance(total_calory, (int, float)):
            if total_protein < 30:
                personalized_advice = "对于增肌目标，这餐的蛋白质含量偏低。建议增加鸡胸肉、鱼、蛋等高蛋白食物。"
            if total_calory < 500:
                personalized_advice += " 总热量偏低，增肌期间需要保持热量盈余。"
    elif fitness_goal == "减脂":
        if isinstance(total_fat, (int, float)) and isinstance(total_carbohydrate, (int, float)):
            if total_fat > 25:
                personalized_advice = "对于减脂目标，这餐的脂肪含量偏高。建议减少油脂摄入。"
            if total_carbohydrate > 60 and meal_type.lower() == "dinner":
                personalized_advice += " 晚餐碳水含量偏高，可能影响减脂效果。"

    # 如果有训练参数，提供训练相关的营养建议
    training_advice = ""
    if training_params:
        body_part = training_params.get("body_part")
        if body_part:
            if isinstance(total_protein, (int, float)) and total_protein < 20:
                training_advice = f"您计划训练{body_part}，但这餐蛋白质摄入不足，可能影响肌肉恢复。"
            elif isinstance(total_carbohydrate, (int, float)) and total_carbohydrate < 30:
                training_advice = f"您计划训练{body_part}，适当增加碳水摄入可以提供更多训练能量。"

    # 使用LLM生成更个性化的建议
    try:
        enhanced_advice = await _generate_enhanced_advice(
            food_items,
            nutrition_info,
            user_info,
            meal_type,
            training_params
        )
    except Exception as e:
        logger.error(f"生成增强建议失败: {str(e)}")
        enhanced_advice = ""

    # 组合完整响应
    response = f"我已分析您的{meal_type}图片，识别出以下食物：\n\n{food_list_text}\n"
    response += f"营养摄入总结：\n{nutrition_summary}\n\n"

    if health_recommendation:
        response += f"健康建议：\n{health_recommendation}\n\n"

    if personalized_advice:
        response += f"根据您的健身目标({fitness_goal})，我的建议：\n{personalized_advice}\n\n"

    if training_advice:
        response += f"训练相关建议：\n{training_advice}\n\n"

    if enhanced_advice:
        response += f"个性化营养建议：\n{enhanced_advice}\n\n"

    response += "您可以询问我关于这些食物的更多信息，或者寻求更详细的营养建议。"

    return response

async def _generate_enhanced_advice(
    food_items: List[Dict[str, Any]],
    nutrition_info: Dict[str, Any],
    user_info: Dict[str, Any],
    meal_type: str,
    training_params: Optional[Dict[str, Any]] = None
) -> str:
    """使用LLM生成增强的个性化建议"""
    # 构建提示
    food_names = [item.get("name", "未知食物") for item in food_items]
    food_list = ", ".join(food_names)

    fitness_goal = user_info.get("fitness_goal", "未知")
    fitness_level = user_info.get("fitness_level", "未知")

    training_context = ""
    if training_params:
        body_part = training_params.get("body_part", "")
        if body_part:
            training_context = f"用户计划训练{body_part}。"

    prompt = f"""
    作为一名专业的健身营养师，请根据以下信息提供简短的个性化营养建议：

    用户餐食：{meal_type}
    食物清单：{food_list}
    营养摄入：蛋白质 {nutrition_info.get('protein', '未知')}g, 脂肪 {nutrition_info.get('fat', '未知')}g, 碳水 {nutrition_info.get('carbohydrate', '未知')}g
    用户健身目标：{fitness_goal}
    用户健身水平：{fitness_level}
    {training_context}

    请提供1-2条针对性的营养建议，重点关注如何调整饮食以配合用户的健身目标和训练计划。建议应该简洁明了，不超过50字。
    """

    # 调用LLM
    try:
        response = await llm_service.aget_chat_response(
            messages=[
                {"role": "system", "content": "你是一名专业的健身营养师，擅长根据用户的饮食和训练情况提供个性化的营养建议。"},
                {"role": "user", "content": prompt}
            ],
            model=settings.LLM_MODEL,
            temperature=0.7
        )
    except Exception as e:
        logger.error(f"调用LLM生成建议失败: {str(e)}")
        response = "增加蛋白质摄入以支持肌肉恢复，控制碳水摄入时间，优先在训练前后摄入。保持充足的蔬菜摄入以获取必要的维生素和矿物质。"

    return response.strip()
