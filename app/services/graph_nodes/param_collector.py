from typing import Dict, Any, List
import logging
from langchain_core.messages import AIMessage
from ...services.state_definitions import FitnessAssistantState
from ...services.active_query_manager import ActiveQueryManager
from ...services.llm_proxy_service import LLMProxyService

logger = logging.getLogger(__name__)

class ParamCollector:
    def __init__(self, param_manager=None, llm_service=None):
        # 创建或使用提供的LLM服务
        self.llm_service = llm_service or LLMProxyService()
        
        # 传递LLM服务给参数管理器
        self.param_manager = param_manager or ActiveQueryManager(self.llm_service)
        
        self.param_questions = {
            "body_part": "请问您想训练哪个部位？",
            "training_scene": "您想在什么场景训练？(健身房/家庭)",
            "plan_type": "您想要什么类型的计划？(单日/周期)",
            "equipment": "您有哪些器材可以使用？",
            "goal": "您的训练目标是什么？(增肌/减脂/塑形/增强力量)"
        }
    
    def __call__(self, state: FitnessAssistantState) -> Dict[str, Any]:
        """参数收集专家节点"""
        messages = state["messages"]
        training_params = state.get("training_params", {})
        metadata = state.get("metadata", {})
        
        # 获取当前询问的参数
        current_param = metadata.get("asking_param")
        original_intent = metadata.get("original_intent", "general_chat")
        
        if not current_param:
            # 获取需要收集的参数
            intent_param_map = {
                "training_plan": ["body_part", "training_scene", "plan_type"],
                "exercise_recommendation": ["body_part", "training_scene", "equipment"],
                "fitness_qa": []
            }
            
            required_params = intent_param_map.get(original_intent, [])
            missing_params = [p for p in required_params if p not in training_params]
            
            if not missing_params:
                # 所有参数已收集
                return {
                    "metadata": {
                        **metadata,
                        "collecting_training_params": False,
                        "asking_param": None
                    },
                    "dialog_state": "pop"  # 返回路由器
                }
            
            # 开始收集第一个缺失参数
            current_param = missing_params[0]
        else:
            # 解析用户回答并更新参数
            user_message = messages[-1].content if messages and messages[-1].type == "human" else ""
            
            # 尝试使用参数管理器提取参数
            try:
                extracted_params = self.param_manager.extract_training_params(user_message)
                if current_param in extracted_params and extracted_params[current_param]:
                    training_params[current_param] = extracted_params[current_param]
                    logger.info(f"Extracted parameter {current_param}: {extracted_params[current_param]}")
                else:
                    # 使用简单提取方式作为备选
                    training_params[current_param] = user_message.strip()
            except Exception as e:
                logger.warning(f"Parameter extraction error: {e}")
                # 使用简单提取方式作为备选
                training_params[current_param] = user_message.strip()
            
            # 检查下一个需要收集的参数
            intent_param_map = {
                "training_plan": ["body_part", "training_scene", "plan_type"],
                "exercise_recommendation": ["body_part", "training_scene", "equipment"],
                "fitness_qa": []
            }
            
            required_params = intent_param_map.get(original_intent, [])
            all_params = list(required_params)  # 复制一份完整列表
            
            try:
                # 获取当前参数在列表中的索引
                current_index = all_params.index(current_param)
                # 检查是否还有下一个参数
                if current_index + 1 < len(all_params):
                    current_param = all_params[current_index + 1]
                else:
                    # 所有参数已收集完毕
                    return {
                        "training_params": training_params,
                        "metadata": {
                            **metadata,
                            "collecting_training_params": False,
                            "asking_param": None
                        },
                        "dialog_state": "pop"  # 返回路由器
                    }
            except ValueError:
                # 当前参数不在列表中，可能是自定义参数
                current_param = None
                
                # 检查是否有缺失的必需参数
                missing_params = [p for p in required_params if p not in training_params]
                if missing_params:
                    current_param = missing_params[0]
                else:
                    # 所有参数已收集
                    return {
                        "training_params": training_params,
                        "metadata": {
                            **metadata,
                            "collecting_training_params": False,
                            "asking_param": None
                        },
                        "dialog_state": "pop"  # 返回路由器
                    }
        
        # 准备下一个参数问题
        question = self.param_questions.get(current_param, f"请问您的{current_param}是什么？")
        
        # 返回带有问题的响应和更新的元数据
        return {
            "messages": [AIMessage(content=question)],
            "training_params": training_params,
            "metadata": {
                **metadata,
                "collecting_training_params": True,
                "asking_param": current_param
            }
        }

# 创建实例供图使用
param_collector = ParamCollector() 