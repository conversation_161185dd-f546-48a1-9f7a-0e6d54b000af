from typing import Dict, Any
import logging
import uuid
from langchain_core.messages import AIMessage
from ...services.state_definitions import FitnessAssistantState
from ...services.training_plan_service import TrainingPlanService
from ...services.sql_tool_service import SQLToolService
from ...models.user import User
from ...db.session import SessionLocal

logger = logging.getLogger(__name__)

class TrainingPlanExpert:
    def __init__(self):
        self.db = SessionLocal()
        self.sql_tool_service = SQLToolService(self.db)
        self.plan_service = TrainingPlanService(self.db, None, self.sql_tool_service)
        
        # 定义必要的用户信息字段
        self.required_user_info = ["gender", "age", "weight", "height", "activity_level"]
    
    def __call__(self, state: FitnessAssistantState) -> Dict[str, Any]:
        """训练计划专家节点"""
        messages = state["messages"]
        training_params = state.get("training_params", {})
        user_info = state.get("user_info", {})
        metadata = state.get("metadata", {})
        
        # 检查用户信息完整性
        missing_fields = [field for field in self.required_user_info if field not in user_info or not user_info[field]]
        
        if missing_fields:
            # 需要收集用户信息
            logger.info(f"Missing user info fields: {missing_fields}")
            return {
                "metadata": {
                    **metadata,
                    "waiting_info": missing_fields,
                    "original_request": {
                        "intent": "training_plan",
                        "params": training_params
                    }
                },
                "dialog_state": "user_info_collector"
            }
        
        # 检查训练参数完整性
        required_params = ["body_part", "training_scene", "plan_type"]
        missing_params = [p for p in required_params if p not in training_params]
        
        if missing_params:
            # 需要收集训练参数
            logger.info(f"Missing training params: {missing_params}")
            return {
                "metadata": {
                    **metadata,
                    "collecting_training_params": True,
                    "asking_param": missing_params[0],
                    "original_intent": "training_plan"
                },
                "dialog_state": "param_collector"
            }
        
        # 所有必要信息已具备，生成训练计划
        try:
            # 获取计划类型
            plan_type = training_params.get("plan_type", "单日")
            
            # 根据计划类型选择生成方法
            if "周" in plan_type or "cycle" in plan_type.lower():
                # 周期训练计划
                plan_result = self.plan_service.generate_cycle_training_plan(
                    user_info=user_info,
                    body_part=training_params.get("body_part"),
                    training_scene=training_params.get("training_scene")
                )
            else:
                # 单日训练计划
                plan_result = self.plan_service.generate_training_plan(
                    user_info=user_info,
                    body_part=training_params.get("body_part"),
                    training_scene=training_params.get("training_scene")
                )
            
            # 获取或生成计划ID
            plan_id = plan_result.get("plan_id", f"plan_{uuid.uuid4().hex[:8]}")
            
            # 格式化响应
            plan_type_text = "周期" if "周" in plan_type or "cycle" in plan_type.lower() else "单日"
            response_content = f"已为您制定{plan_type_text}训练计划，针对{training_params.get('body_part')}，适用于{training_params.get('training_scene')}场景。\n\n"
            response_content += plan_result.get("plan_description", "")
            response_content += "\n\n您可以随时询问关于这个计划的任何问题，如具体动作细节、训练注意事项等。"
            
            # 更新上下文，关联计划ID
            return {
                "messages": [AIMessage(content=response_content)],
                "metadata": {
                    **metadata,
                    "related_plan_id": plan_id,
                    "active_flow": "training_plan_discussion",
                    # 清除收集状态
                    "collecting_training_params": False,
                    "asking_param": None
                },
                "dialog_state": "pop"  # 返回路由器
            }
            
        except Exception as e:
            logger.error(f"Error generating training plan: {e}")
            error_msg = "抱歉，生成训练计划时遇到了问题。请稍后再试。"
            return {
                "messages": [AIMessage(content=error_msg)],
                "dialog_state": "pop"  # 返回路由器
            }

# 创建实例供图使用
training_plan_expert = TrainingPlanExpert() 