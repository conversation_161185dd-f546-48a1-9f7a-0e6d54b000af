from typing import Dict, List, Any, Optional
import json
import logging

from app.services.state_definitions import ConversationState
from app.services.llm_proxy_service import LLMProxyService
from app.core.config import settings

# 配置日志
logger = logging.getLogger(__name__)

# 全局LLM实例
llm_service = LLMProxyService()

async def fitness_qa_expert_node(state: ConversationState) -> ConversationState:
    """健身QA专家节点：负责回答健身相关问题"""

    # 获取用户最新消息
    user_message = ""
    for msg in reversed(state.messages):
        if msg.role == "user":
            user_message = msg.content
            break

    if not user_message:
        # 没有用户消息，返回默认回复
        from app.services.state_definitions import AnyMessage
        state.messages.append(AnyMessage(
            role="assistant",
            content="您好，我是您的健身助手。请问有什么健身或营养方面的问题需要帮助？"
        ))
        return state

    # 提取用户信息用于个性化回答
    user_info = state.user_info

    # 生成个性化回答
    answer = await _generate_fitness_answer(user_message, user_info)

    # 添加回答到消息列表
    from app.services.state_definitions import AnyMessage
    state.messages.append(AnyMessage(role="assistant", content=answer))

    return state

async def _generate_fitness_answer(question: str, user_info: Dict) -> str:
    """生成健身相关问题的回答"""
    from app.core.chat_config import MODELS, CONVERSATION

    # 提取用户基本信息
    user_info_str = "\n".join([f"- {k}: {v}" for k, v in user_info.items() if k != "user_id"])

    # 构建用户提示
    user_prompt = f"""
    ## 用户信息:
    {user_info_str}

    ## 用户问题:
    {question}
    """

    # 判断问题类型，选择合适的模型
    model_to_use = ""
    if _is_nutrition_question(question):
        # 营养饮食相关问题使用nutrition_advice模型
        logger.info("检测到营养饮食相关问题，使用nutrition_advice模型")
        model_to_use = "nutrition_advice"
    else:
        # 运动、体态等相关问题使用fitness_advice模型
        logger.info("检测到健身/体态相关问题，使用fitness_advice模型")
        model_to_use = "fitness_advice"

    # 调用百炼应用生成回答
    response = await llm_service.aget_chat_response(
        messages=[{"role": "user", "content": user_prompt}],
        model=model_to_use,
        temperature=0.7
    )

    # 使用conversation模型包装回答
    wrapped_response = await llm_service.aget_chat_response(
        messages=[
            {"role": "system", "content": "你是一位专业的健身教练和营养顾问，请用专业、友好的语气回复用户。"},
            {"role": "user", "content": f"请用专业的语气包装以下回答：\n\n{response}"}
        ],
        model=MODELS["conversation"],
        temperature=0.5
    )

    return wrapped_response.strip()

def _is_nutrition_question(question: str) -> bool:
    """判断是否为营养饮食相关问题"""
    nutrition_keywords = [
        "饮食", "营养", "食物", "蛋白质", "碳水", "脂肪", "热量", "卡路里",
        "减脂", "增肌饮食", "代餐", "餐单", "食谱", "吃什么", "怎么吃",
        "补剂", "蛋白粉", "肌酸", "维生素", "营养素"
    ]

    # 检查问题中是否包含营养饮食关键词
    for keyword in nutrition_keywords:
        if keyword in question:
            return True

    return False