from typing import Dict, Any, List, Optional
import logging
import re
import time

from app.services.state_definitions import ConversationState, AnyMessage
from app.db.session import SessionLocal
from app.services.llm_proxy_service import LLMProxyService
from app.services.exercise_search_service import ExerciseSearchService
from app.core.config import settings

logger = logging.getLogger(__name__)
llm_service = LLMProxyService()

async def exercise_recommendation_expert_node(state: ConversationState) -> ConversationState:
    """运动推荐专家节点：推荐训练动作并提供指导"""

    # 获取用户最新消息
    user_message = ""
    for msg in reversed(state.messages):
        if msg.role == "user":
            user_message = msg.content
            break

    if not user_message:
        # 没有用户消息，返回默认回复
        state.messages.append(AnyMessage(
            role="assistant",
            content="您好，我是您的健身助手。请告诉我您想训练哪个部位，我可以为您推荐合适的训练动作。"
        ))
        return state

    # 提取训练参数
    body_part = state.training_params.get("body_part")
    equipment = state.training_params.get("equipment")
    level = state.user_info.get("fitness_level")
    fitness_goal = state.user_info.get("fitness_goal")

    # 如果没有明确的肌肉群，尝试从消息中提取
    if not body_part:
        body_part = await _extract_body_part(user_message)
        if body_part:
            state.training_params["body_part"] = body_part

    # 如果仍然没有肌肉群，尝试使用语义搜索
    if not body_part:
        # 使用智能检索服务进行语义搜索
        db = SessionLocal()
        try:
            search_service = ExerciseSearchService(db)
            exercises = await search_service.semantic_search(user_message, limit=5)

            if exercises:
                # 从搜索结果中提取最常见的肌肉群
                body_parts_count = {}
                for exercise in exercises:
                    for bp in exercise.get("body_parts", []):
                        body_parts_count[bp] = body_parts_count.get(bp, 0) + 1

                if body_parts_count:
                    # 选择最常见的肌肉群
                    body_part = max(body_parts_count.items(), key=lambda x: x[1])[0]
                    state.training_params["body_part"] = body_part
        finally:
            db.close()

    # 如果仍然没有肌肉群，请求用户提供
    if not body_part:
        state.messages.append(AnyMessage(
            role="assistant",
            content="请告诉我您想训练哪个部位，例如胸部、背部、肩部、手臂、腿部或核心。"
        ))
        return state

    # 使用智能检索服务查找合适的动作
    db = SessionLocal()
    try:
        search_service = ExerciseSearchService(db)

        # 如果有健身目标，使用目标导向的推荐
        if fitness_goal:
            exercises = await search_service.recommend_exercises_for_goal(
                fitness_goal=fitness_goal,
                body_part=body_part,
                fitness_level=level,
                limit=5
            )
        else:
            # 否则使用常规搜索
            exercises = await search_service.search_by_criteria(
                body_part=body_part,
                equipment=equipment,
                level=level,
                limit=5
            )
    finally:
        db.close()

    # 生成推荐响应
    if exercises:
        response = await _generate_exercise_recommendations(
            exercises,
            body_part,
            state.user_info,
            state.training_params
        )
    else:
        response = f"抱歉，我没有找到适合训练{body_part}的动作。请尝试其他肌肉群或调整条件。"

    # 添加响应到消息列表
    state.messages.append(AnyMessage(role="assistant", content=response))

    # 将找到的动作存储在元数据中，以便后续参考
    exercise_names = [e.get("name", "未命名动作") for e in exercises]
    exercise_details = {e.get("name", f"动作{i}"): e for i, e in enumerate(exercises)}

    state.meta_info["recommended_exercises"] = exercise_names
    state.meta_info["exercise_details"] = exercise_details
    state.meta_info["last_recommendation_time"] = time.time()

    return state

async def _extract_body_part(message: str) -> Optional[str]:
    """从用户消息中提取身体部位"""
    # 尝试从"X怎么练"模式提取
    match = re.search(r"([^怎么]+)怎么练", message)
    if match:
        extracted_part = match.group(1)
        # 标准化提取的部位
        body_parts = {
            "胸部": ["胸", "胸肌", "胸大肌", "上胸", "中胸", "下胸", "pectoral", "chest"],
            "背部": ["背", "背肌", "背阔肌", "上背", "中背", "下背", "latissimus", "back"],
            "肩部": ["肩", "肩膀", "三角肌", "前束", "中束", "后束", "deltoid", "shoulder"],
            "臂部": ["手臂", "臂", "二头肌", "三头肌", "biceps", "triceps", "arms"],
            "腿部": ["腿", "大腿", "小腿", "股四头肌", "腿筋", "leg", "quad", "hamstring"],
            "核心": ["腹", "腹肌", "腰", "核心", "abs", "core"],
            "全身": ["全身", "复合", "综合", "full body", "compound"]
        }

        for part, keywords in body_parts.items():
            for keyword in keywords:
                if keyword == extracted_part or keyword in extracted_part:
                    return part

        return extracted_part

    # 尝试从关键词中提取
    body_parts = {
        "胸部": ["胸", "胸肌", "胸大肌", "上胸", "中胸", "下胸", "pectoral", "chest"],
        "背部": ["背", "背肌", "背阔肌", "上背", "中背", "下背", "latissimus", "back"],
        "肩部": ["肩", "肩膀", "三角肌", "前束", "中束", "后束", "deltoid", "shoulder"],
        "臂部": ["手臂", "臂", "二头肌", "三头肌", "biceps", "triceps", "arms"],
        "腿部": ["腿", "大腿", "小腿", "股四头肌", "腿筋", "leg", "quad", "hamstring"],
        "核心": ["腹", "腹肌", "腰", "核心", "abs", "core"],
        "全身": ["全身", "复合", "综合", "full body", "compound"]
    }

    for part, keywords in body_parts.items():
        for keyword in keywords:
            if keyword in message.lower():
                return part

    return None

async def _generate_exercise_recommendations(
    exercises: List[Dict[str, Any]],
    body_part: str,
    user_info: Dict[str, Any],
    training_params: Dict[str, Any]
) -> str:
    """生成训练动作推荐响应"""
    # 构建基本响应
    response = f"以下是针对{body_part}的推荐训练动作:\n\n"

    for i, exercise in enumerate(exercises, 1):
        name = exercise.get("name", "未命名动作")
        level_map = {1: "初级", 2: "中级", 3: "高级"}
        level = level_map.get(exercise.get("level"), "中级")
        description = exercise.get("description", "")

        response += f"{i}. **{name}**\n"
        response += f"   难度: {level}\n"

        if description:
            # 截断描述，保持简洁
            short_desc = description[:100] + "..." if len(description) > 100 else description
            response += f"   简介: {short_desc}\n"

        # 添加身体部位信息
        body_parts = exercise.get("body_parts", [])
        if body_parts:
            response += f"   目标部位: {', '.join(body_parts)}\n"

        # 添加器材信息
        equipment = exercise.get("equipment", [])
        if equipment:
            response += f"   所需器材: {', '.join(equipment)}\n"

        response += "\n"

    # 添加个性化建议
    fitness_goal = user_info.get("fitness_goal")
    fitness_level = user_info.get("fitness_level")

    if fitness_goal or fitness_level:
        response += "**个性化建议**:\n"

        if fitness_goal == "增肌":
            response += "- 增肌目标: 建议使用中等重量，每组8-12次，组间休息60-90秒。\n"
        elif fitness_goal == "减脂":
            response += "- 减脂目标: 建议使用较轻重量，每组15-20次，组间休息30-60秒，或考虑将这些动作组合成循环训练。\n"
        elif fitness_goal == "力量":
            response += "- 力量目标: 建议使用较重重量，每组4-6次，组间休息2-3分钟，注重技术正确性。\n"

        if fitness_level == "初级":
            response += "- 初级水平: 先专注于掌握正确的动作技术，使用较轻重量，每个动作做3组。\n"
        elif fitness_level == "中级":
            response += "- 中级水平: 可以尝试增加重量或组数，每个动作做3-4组，考虑添加超级组。\n"
        elif fitness_level == "高级":
            response += "- 高级水平: 可以尝试高级变式，增加训练密度，或使用高级技术如递减组、超级组等。\n"

    response += "\n您可以询问任何动作的详细做法、注意事项或替代动作。"

    return response
