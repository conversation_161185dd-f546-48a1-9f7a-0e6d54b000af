"""
专家节点模块 - 负责管理和提供各种专家工作流节点
"""

# 导出主要节点以便外部访问
from app.services.graph_nodes.router import router_node
from app.services.graph_nodes.param_collector_node import param_collector_node
from app.services.graph_nodes.user_info_collector_node import user_info_collector_node
from app.services.graph_nodes.training_plan_expert_node import training_plan_expert_node
from app.services.graph_nodes.fitness_qa_expert_node import fitness_qa_expert_node
from app.services.graph_nodes.general_chat_expert_node import general_chat_expert_node
from app.services.graph_nodes.interruption_handler_node import interruption_handler_node
from app.services.graph_nodes.state_monitor_node import state_monitor_node
from app.services.graph_nodes.enhanced_param_collector_node import enhanced_param_collector_node

# 导入增强版节点
from app.services.graph_nodes.enhanced_training_plan_expert import training_plan_expert_node as enhanced_training_plan_expert_node

# 默认使用增强版节点替代原始节点
training_plan_expert_node = enhanced_training_plan_expert_node
param_collector_node = enhanced_param_collector_node  # 使用增强版参数收集器

__all__ = [
    "router_node",
    "param_collector_node",
    "user_info_collector_node",
    "training_plan_expert_node",  # 这里使用的是增强版节点
    "fitness_qa_expert_node",
    "general_chat_expert_node",
    "interruption_handler_node",
    "state_monitor_node",
    # 导出原始节点和增强版节点，以便需要时可以单独使用
    "enhanced_training_plan_expert_node",
    "enhanced_param_collector_node"
]