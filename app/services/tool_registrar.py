from typing import List, Dict, Any
from langchain.tools import BaseTool
from app.services.sql_tool_service import SQLToolService
from app.services.base_service import BaseService
import logging

logger = logging.getLogger(__name__)

class ToolRegistrar(BaseService):
    """
    工具注册器，管理Agent可使用的工具集合
    """
    
    def __init__(self, sql_tool_service: SQLToolService = None, db = None):
        """
        初始化工具注册器
        
        Args:
            sql_tool_service: SQL工具服务
            db: 数据库会话，可选
        """
        super().__init__(db)
        self.sql_tool_service = sql_tool_service or SQLToolService(db)
        logger.info("初始化工具注册器")
        
    def get_all_tools(self) -> List[BaseTool]:
        """
        获取所有可用工具
        
        Returns:
            工具列表
        """
        tools = []
        
        # 添加数据库查询工具
        if self.sql_tool_service:
            tools.extend(self.sql_tool_service.get_tools())
            
        logger.info(f"注册了 {len(tools)} 个工具")
        return tools
        
    def get_tool_by_name(self, name: str) -> BaseTool:
        """
        根据名称获取工具
        
        Args:
            name: 工具名称
            
        Returns:
            找到的工具，未找到返回None
        """
        for tool in self.get_all_tools():
            if tool.name == name:
                return tool
        return None
        
    def describe_tools(self) -> Dict[str, str]:
        """
        获取所有工具的描述信息
        
        Returns:
            工具名称及其描述的字典
        """
        return {tool.name: tool.description for tool in self.get_all_tools()} 