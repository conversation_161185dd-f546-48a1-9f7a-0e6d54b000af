"""
数据库检查点存储 - 使用PostgreSQL存储LangGraph检查点
"""
from typing import Dict, Any, Optional, List, Union
import json
import logging
import time
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import text
from app.core.config import settings
from app.services.memory_cache_service import MemoryCacheService

logger = logging.getLogger(__name__)

class PostgreSQLCheckpointer:
    """使用PostgreSQL存储LangGraph检查点"""
    
    def __init__(self, db_session: Session):
        """初始化检查点存储
        
        Args:
            db_session: 数据库会话
        """
        self.db = db_session
        self.cache_service = MemoryCacheService()
        self.table_name = "graph_checkpoints"
        self._ensure_table_exists()
        logger.info("PostgreSQL检查点存储初始化完成")
    
    def _ensure_table_exists(self) -> None:
        """确保检查点表存在"""
        try:
            # 检查表是否存在
            check_query = text(f"""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = '{self.table_name}'
            );
            """)
            
            result = self.db.execute(check_query).scalar()
            
            if not result:
                # 创建表
                create_table_query = text(f"""
                CREATE TABLE IF NOT EXISTS {self.table_name} (
                    key VARCHAR(255) PRIMARY KEY,
                    state JSONB NOT NULL,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                );
                """)
                
                self.db.execute(create_table_query)
                self.db.commit()
                logger.info(f"创建检查点表: {self.table_name}")
        except Exception as e:
            logger.error(f"确保检查点表存在时出错: {str(e)}")
            # 不抛出异常，允许在表不存在的情况下继续运行
    
    async def get(self, key: str) -> Optional[Dict[str, Any]]:
        """从数据库获取检查点
        
        Args:
            key: 检查点键
            
        Returns:
            检查点状态，如果不存在则返回None
        """
        # 首先尝试从缓存获取
        cached_state = self.cache_service.get_session_state(f"checkpoint:{key}")
        if cached_state:
            logger.debug(f"从缓存获取检查点: {key}")
            return cached_state
        
        try:
            # 从数据库获取
            query = text(f"""
            SELECT state FROM {self.table_name}
            WHERE key = :key
            """)
            
            result = self.db.execute(query, {"key": key}).scalar()
            
            if result:
                # 解析JSON
                state = json.loads(result) if isinstance(result, str) else result
                
                # 缓存结果
                self.cache_service.set_session_state(f"checkpoint:{key}", state)
                
                return state
            
            return None
        except Exception as e:
            logger.error(f"获取检查点时出错: {str(e)}")
            return None
    
    async def put(self, key: str, state: Dict[str, Any]) -> None:
        """将检查点保存到数据库
        
        Args:
            key: 检查点键
            state: 检查点状态
        """
        if not state:
            logger.warning(f"尝试保存空状态: {key}")
            return
        
        try:
            # 将状态转换为JSON
            state_json = json.dumps(state)
            
            # 使用UPSERT语法
            query = text(f"""
            INSERT INTO {self.table_name} (key, state, created_at, updated_at)
            VALUES (:key, :state, NOW(), NOW())
            ON CONFLICT (key) 
            DO UPDATE SET 
                state = :state,
                updated_at = NOW()
            """)
            
            self.db.execute(query, {"key": key, "state": state_json})
            self.db.commit()
            
            # 更新缓存
            self.cache_service.set_session_state(f"checkpoint:{key}", state)
            
            logger.debug(f"保存检查点: {key}")
        except Exception as e:
            logger.error(f"保存检查点时出错: {str(e)}")
            self.db.rollback()
    
    async def delete(self, key: str) -> None:
        """删除检查点
        
        Args:
            key: 检查点键
        """
        try:
            query = text(f"""
            DELETE FROM {self.table_name}
            WHERE key = :key
            """)
            
            self.db.execute(query, {"key": key})
            self.db.commit()
            
            # 从缓存中删除
            self.cache_service.delete(f"checkpoint:{key}")
            
            logger.debug(f"删除检查点: {key}")
        except Exception as e:
            logger.error(f"删除检查点时出错: {str(e)}")
            self.db.rollback()
    
    async def list(self, prefix: str = "") -> List[str]:
        """列出所有检查点键
        
        Args:
            prefix: 键前缀过滤器
            
        Returns:
            检查点键列表
        """
        try:
            if prefix:
                query = text(f"""
                SELECT key FROM {self.table_name}
                WHERE key LIKE :prefix
                ORDER BY updated_at DESC
                """)
                
                result = self.db.execute(query, {"prefix": f"{prefix}%"})
            else:
                query = text(f"""
                SELECT key FROM {self.table_name}
                ORDER BY updated_at DESC
                """)
                
                result = self.db.execute(query)
            
            return [row[0] for row in result]
        except Exception as e:
            logger.error(f"列出检查点时出错: {str(e)}")
            return []
    
    async def cleanup(self, max_age_days: int = 7) -> int:
        """清理旧检查点
        
        Args:
            max_age_days: 最大保留天数
            
        Returns:
            删除的检查点数量
        """
        try:
            query = text(f"""
            DELETE FROM {self.table_name}
            WHERE updated_at < NOW() - INTERVAL '{max_age_days} days'
            RETURNING key
            """)
            
            result = self.db.execute(query)
            deleted_keys = [row[0] for row in result]
            self.db.commit()
            
            # 从缓存中删除
            for key in deleted_keys:
                self.cache_service.delete(f"checkpoint:{key}")
            
            logger.info(f"清理了 {len(deleted_keys)} 个旧检查点")
            return len(deleted_keys)
        except Exception as e:
            logger.error(f"清理检查点时出错: {str(e)}")
            self.db.rollback()
            return 0
