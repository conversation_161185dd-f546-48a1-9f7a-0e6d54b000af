from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from app.models.favorite import UserFavoriteExercise, UserFavoriteFood
from app.models.exercise import Exercise
from app.models.food import Food

class FavoriteService:
    """收藏服务类"""
    
    @staticmethod
    def toggle_favorite_exercise(db: Session, user_id: int, exercise_id: int, notes: Optional[str] = None) -> Dict[str, Any]:
        """添加或取消收藏健身动作
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            exercise_id: 健身动作ID
            notes: 备注信息
            
        Returns:
            返回操作结果
        """
        # 检查收藏是否已存在
        favorite = db.query(UserFavoriteExercise).filter(
            UserFavoriteExercise.user_id == user_id,
            UserFavoriteExercise.exercise_id == exercise_id
        ).first()
        
        # 检查动作是否存在
        exercise = db.query(Exercise).filter(Exercise.id == exercise_id).first()
        if not exercise:
            return {"success": False, "message": "健身动作不存在", "is_favorite": False}
        
        if favorite:
            # 如果已收藏，则切换为未收藏状态
            if favorite.is_active:
                favorite.is_active = False
                db.commit()
                return {"success": True, "message": "已取消收藏", "is_favorite": False}
            else:
                # 如果已存在但未激活，则重新激活
                favorite.is_active = True
                if notes:
                    favorite.notes = notes
                db.commit()
                return {"success": True, "message": "已重新收藏", "is_favorite": True}
        else:
            # 添加新收藏
            new_favorite = UserFavoriteExercise(
                user_id=user_id,
                exercise_id=exercise_id,
                notes=notes,
                is_active=True
            )
            db.add(new_favorite)
            db.commit()
            db.refresh(new_favorite)
            return {"success": True, "message": "收藏成功", "is_favorite": True}
    
    @staticmethod
    def toggle_favorite_food(db: Session, user_id: int, food_id: int, notes: Optional[str] = None) -> Dict[str, Any]:
        """添加或取消收藏食物
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            food_id: 食物ID
            notes: 备注信息
            
        Returns:
            返回操作结果
        """
        # 检查收藏是否已存在
        favorite = db.query(UserFavoriteFood).filter(
            UserFavoriteFood.user_id == user_id,
            UserFavoriteFood.food_id == food_id
        ).first()
        
        # 检查食物是否存在
        food = db.query(Food).filter(Food.id == food_id).first()
        if not food:
            return {"success": False, "message": "食物不存在", "is_favorite": False}
        
        if favorite:
            # 如果已收藏，则切换为未收藏状态
            if favorite.is_active:
                favorite.is_active = False
                db.commit()
                return {"success": True, "message": "已取消收藏", "is_favorite": False}
            else:
                # 如果已存在但未激活，则重新激活
                favorite.is_active = True
                if notes:
                    favorite.notes = notes
                db.commit()
                return {"success": True, "message": "已重新收藏", "is_favorite": True}
        else:
            # 添加新收藏
            new_favorite = UserFavoriteFood(
                user_id=user_id,
                food_id=food_id,
                notes=notes,
                is_active=True
            )
            db.add(new_favorite)
            db.commit()
            db.refresh(new_favorite)
            return {"success": True, "message": "收藏成功", "is_favorite": True}
    
    @staticmethod
    def get_user_favorite_exercises(db: Session, user_id: int, skip: int = 0, limit: int = 100) -> List[Dict[str, Any]]:
        """获取用户收藏的健身动作列表
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            skip: 跳过记录数
            limit: 返回记录数
            
        Returns:
            收藏列表
        """
        favorites = db.query(UserFavoriteExercise).filter(
            UserFavoriteExercise.user_id == user_id,
            UserFavoriteExercise.is_active == True
        ).join(Exercise, UserFavoriteExercise.exercise_id == Exercise.id).offset(skip).limit(limit).all()
        
        result = []
        for fav in favorites:
            exercise = fav.exercise
            result.append({
                "id": fav.id,
                "exercise_id": exercise.id,
                "name": exercise.name,
                "en_name": exercise.en_name,
                "image_name": exercise.image_name,
                "gif_url": exercise.gif_url,
                "level": exercise.level,
                "notes": fav.notes,
                "created_at": fav.created_at
            })
        
        return result
    
    @staticmethod
    def get_user_favorite_foods(db: Session, user_id: int, skip: int = 0, limit: int = 100) -> List[Dict[str, Any]]:
        """获取用户收藏的食物列表
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            skip: 跳过记录数
            limit: 返回记录数
            
        Returns:
            收藏列表
        """
        favorites = db.query(UserFavoriteFood).filter(
            UserFavoriteFood.user_id == user_id,
            UserFavoriteFood.is_active == True
        ).join(Food, UserFavoriteFood.food_id == Food.id).offset(skip).limit(limit).all()
        
        result = []
        for fav in favorites:
            food = fav.food
            result.append({
                "id": fav.id,
                "food_id": food.id,
                "name": food.name,
                "category": food.category,
                "food_type": food.food_type,
                "thumb_image_url": food.thumb_image_url,
                "large_image_url": food.large_image_url,
                "notes": fav.notes,
                "created_at": fav.created_at
            })
        
        return result
    
    @staticmethod
    def is_exercise_favorite(db: Session, user_id: int, exercise_id: int) -> bool:
        """检查健身动作是否被用户收藏
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            exercise_id: 健身动作ID
            
        Returns:
            是否收藏
        """
        favorite = db.query(UserFavoriteExercise).filter(
            UserFavoriteExercise.user_id == user_id,
            UserFavoriteExercise.exercise_id == exercise_id,
            UserFavoriteExercise.is_active == True
        ).first()
        
        return favorite is not None
    
    @staticmethod
    def is_food_favorite(db: Session, user_id: int, food_id: int) -> bool:
        """检查食物是否被用户收藏
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            food_id: 食物ID
            
        Returns:
            是否收藏
        """
        favorite = db.query(UserFavoriteFood).filter(
            UserFavoriteFood.user_id == user_id,
            UserFavoriteFood.food_id == food_id,
            UserFavoriteFood.is_active == True
        ).first()
        
        return favorite is not None 