#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
LLM代理服务 - 使用dashscope库调用百炼应用
"""

import os
import logging
from typing import List, Dict, Any, Optional
from http import HTTPStatus
from dashscope import Application

from langchain.chat_models.base import BaseChatModel
from langchain.schema import HumanMessage, AIMessage, SystemMessage

from app.core.config import settings
from app.core.chat_config import MODELS, MODEL_PARAMS, PROVIDERS, BAILIAN_APPS

# 设置日志
logger = logging.getLogger(__name__)

class LLMProxyService:
    """LLM代理服务，负责与各种LLM API交互"""
    
    def __init__(self):
        # 导入配置
        from app.core.chat_config import MODELS, PROVIDERS
        
        # 设置环境变量
        os.environ["OPENAI_API_KEY"] = PROVIDERS["qwen"]["api_key"]
        os.environ["OPENAI_API_BASE"] = PROVIDERS["qwen"]["api_base"]
        
        # 初始化默认Chat模型 - 适用于所有场景
        self.chat_model = self.create_llm()
        
        logger.info(f"初始化LLM服务完成，使用默认模型: {MODELS['default']}")
    
    def create_llm(self, 
                  model: str = None, 
                  temperature: float = None, 
                  streaming: bool = True,
                  **kwargs) -> BaseChatModel:
        """创建LLM实例，可以指定模型、温度等参数"""
        from app.core.chat_config import MODELS, MODEL_PARAMS, PROVIDERS, BAILIAN_APPS
        
        model_name = model or MODELS["default"]
        
        # 根据模型名称选择不同的配置
        if model_name == MODELS["agent"] or "deepseek" in model_name.lower():
            # 使用DeepSeek配置
            logger.info(f"使用DeepSeek模型: {model_name}")
            from langchain.chat_models import ChatOpenAI
            return ChatOpenAI(
                model=model_name,
                temperature=temperature or MODEL_PARAMS["default_temperature"],
                openai_api_key=PROVIDERS["deepseek"]["api_key"],
                openai_api_base=PROVIDERS["deepseek"]["api_base"],
                streaming=streaming,
                **kwargs
            )
        elif model_name == "fitness-coach-app":
            # 使用百炼应用模型 - 返回一个特殊的模型实例
            logger.info(f"使用百炼应用模型: {model_name}")
            # 这里我们不返回实际的模型实例，因为我们会在stream_chat中特殊处理
            from langchain.chat_models import ChatOpenAI
            return ChatOpenAI(
                model=model_name,
                temperature=temperature or MODEL_PARAMS["default_temperature"],
                openai_api_key=PROVIDERS["bailian"]["api_key"],
                openai_api_base=PROVIDERS["bailian"]["api_base"],
                streaming=streaming,
                **kwargs
            )
        else:
            # 使用默认配置 (千问)
            logger.info(f"使用千问模型: {model_name}")
            from langchain.chat_models import ChatOpenAI
            return ChatOpenAI(
                model=model_name,
                temperature=temperature or MODEL_PARAMS["default_temperature"],
                openai_api_key=PROVIDERS["qwen"]["api_key"],
                openai_api_base=PROVIDERS["qwen"]["api_base"],
                streaming=streaming,
                **kwargs
            )
    
    async def stream_chat(self, 
                        messages: List[Dict[str, str]], 
                        model: str = None,
                        temperature: float = None,
                        **kwargs):
        """异步流式处理聊天消息，可选择指定不同的模型"""
        from app.core.chat_config import MODELS, MODEL_PARAMS, BAILIAN_APPS
        
        try:
            # 如果是健身建议意图，使用百炼应用模型
            is_bailian_app = False
            if model == "fitness_advice":
                model = MODELS["fitness_advice"]
                is_bailian_app = True
            
            # 百炼应用特殊处理
            if is_bailian_app:
                # 百炼应用不需要系统提示词，只保留用户和助手的消息
                filtered_messages = []
                for msg in messages:
                    if msg["role"] in ["user", "assistant"]:
                        filtered_messages.append(msg)
                
                # 如果没有历史消息，只使用最后一条用户消息
                if len(filtered_messages) <= 1:
                    # 找到最后一条用户消息
                    user_message = None
                    for msg in reversed(messages):
                        if msg["role"] == "user":
                            user_message = msg["content"]
                            break
                    
                    if user_message:
                        # 使用prompt参数调用百炼应用
                        logger.info(f"使用prompt参数调用百炼应用: {user_message[:50]}...")
                        
                        try:
                            response = Application.call(
                                api_key=PROVIDERS["bailian"]["api_key"],
                                app_id=BAILIAN_APPS["fitness-coach-app"]["app_id"],
                                prompt=user_message
                            )
                            
                            if response.status_code != HTTPStatus.OK:
                                error_msg = f"百炼应用调用失败: code={response.status_code}, message={response.message}"
                                logger.error(error_msg)
                                yield error_msg
                                return
                            
                            # 模拟流式输出
                            result = response.output.text
                            chunk_size = 10  # 每个块的大小
                            for i in range(0, len(result), chunk_size):
                                chunk = result[i:i+chunk_size]
                                yield chunk
                                # 这里不需要await asyncio.sleep，因为这是同步代码
                            
                            logger.info(f"百炼应用调用成功: request_id={response.request_id}")
                            if hasattr(response.output, "session_id"):
                                logger.info(f"session_id={response.output.session_id}")
                        
                        except Exception as e:
                            error_msg = f"调用百炼应用时出错: {str(e)}"
                            logger.error(error_msg)
                            yield error_msg
                        
                        return
                
                # 使用messages参数调用百炼应用
                logger.info(f"使用messages参数调用百炼应用，消息数量: {len(filtered_messages)}")
                
                try:
                    # 转换消息格式为百炼应用需要的格式
                    dashscope_messages = []
                    for msg in filtered_messages:
                        dashscope_messages.append({
                            "role": msg["role"],
                            "content": msg["content"]
                        })
                    
                    response = Application.call(
                        api_key=PROVIDERS["bailian"]["api_key"],
                        app_id=BAILIAN_APPS["fitness-coach-app"]["app_id"],
                        messages=dashscope_messages
                    )
                    
                    if response.status_code != HTTPStatus.OK:
                        error_msg = f"百炼应用调用失败: code={response.status_code}, message={response.message}"
                        logger.error(error_msg)
                        yield error_msg
                        return
                    
                    # 模拟流式输出
                    result = response.output.text
                    chunk_size = 10  # 每个块的大小
                    for i in range(0, len(result), chunk_size):
                        chunk = result[i:i+chunk_size]
                        yield chunk
                        # 这里不需要await asyncio.sleep，因为这是同步代码
                    
                    logger.info(f"百炼应用调用成功: request_id={response.request_id}")
                    if hasattr(response.output, "session_id"):
                        logger.info(f"session_id={response.output.session_id}")
                
                except Exception as e:
                    error_msg = f"调用百炼应用时出错: {str(e)}"
                    logger.error(error_msg)
                    yield error_msg
                
                return
            
            # 非百炼应用的标准处理
            # 创建流式Chat模型（如果需要特定模型）
            streaming_chat = self.chat_model
            if model or temperature is not None:
                streaming_chat = self.create_llm(
                    model=model, 
                    temperature=temperature or MODEL_PARAMS["default_temperature"], 
                    streaming=True
                )
            
            # 转换消息格式
            from langchain.schema import HumanMessage, AIMessage, SystemMessage
            langchain_messages = []
            for msg in messages:
                if msg["role"] == "user":
                    langchain_messages.append(HumanMessage(content=msg["content"]))
                elif msg["role"] == "assistant":
                    langchain_messages.append(AIMessage(content=msg["content"]))
                elif msg["role"] == "system":
                    langchain_messages.append(SystemMessage(content=msg["content"]))
            
            # 返回异步流式生成器
            async for chunk in streaming_chat.astream(langchain_messages, **kwargs):
                yield chunk.content
        except Exception as e:
            logger.error(f"异步流式Chat模型调用失败: {e}")
            # 返回一个异常消息
            yield f"抱歉，AI流式聊天服务暂时不可用。错误: {str(e)}"
    
    # 其他方法保持不变...
    # 这里省略了get_chat_response和aget_chat_response方法，实际使用时需要添加
