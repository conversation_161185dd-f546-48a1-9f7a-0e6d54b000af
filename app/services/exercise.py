from typing import List, Optional, Dict, Any, Union
from sqlalchemy.orm import Session
from sqlalchemy import func, or_
from app import crud
from app.models.exercise import Exercise, ExerciseDetail, Muscle, BodyPart, Equipment
from app.schemas.exercise import (
    ExerciseCreate, ExerciseUpdate,
    ExerciseDetailCreate, ExerciseDetailUpdate,
    MuscleCreate, BodyPartCreate, EquipmentCreate
)
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)

class ExerciseService:
    """运动服务 - 负责查询和推荐健身动作"""
    
    def __init__(self, db: Session):
        """初始化运动服务
        
        Args:
            db: 数据库会话
        """
        self.db = db
        
        # 部位映射字典
        self.body_part_mapping = {
            "胸部": ["胸", "胸肌", "胸大肌", "上胸", "中胸", "下胸", "pectoral", "chest"],
            "背部": ["背", "背肌", "背阔肌", "上背", "中背", "下背", "latissimus", "back"],
            "肩部": ["肩", "肩膀", "三角肌", "前束", "中束", "后束", "deltoid", "shoulder"],
            "臂部": ["手臂", "臂", "二头肌", "三头肌", "biceps", "triceps", "arms"],
            "腿部": ["腿", "大腿", "小腿", "股四头肌", "腿筋", "leg", "quad", "hamstring"],
            "核心": ["腹", "腹肌", "腰", "核心", "abs", "core"],
            "全身": ["全身", "复合", "综合", "full body", "compound"]
        }
        
        # 场景映射字典
        self.scene_mapping = {
            "健身房": ["健身房", "器械", "gym"],
            "家庭": ["家", "家庭", "室内", "home", "bodyweight"],
            "户外": ["户外", "公园", "outdoor", "park"]
        }
        
        # 难度映射字典
        self.level_mapping = {
            "初级": ["初级", "新手", "入门", "beginner", "easy"],
            "中级": ["中级", "进阶", "intermediate", "medium"],
            "高级": ["高级", "专业", "进阶", "advanced", "hard"]
        }
    
    def _normalize_body_part(self, body_part: str) -> str:
        """标准化身体部位
        
        Args:
            body_part: 输入的身体部位
            
        Returns:
            标准化后的身体部位
        """
        if not body_part:
            return "全身"
            
        body_part = body_part.lower().strip()
        
        for standard, variations in self.body_part_mapping.items():
            if any(var.lower() in body_part for var in variations):
                return standard
        
        return body_part
    
    def _normalize_scene(self, scene: str) -> str:
        """标准化训练场景
        
        Args:
            scene: 输入的训练场景
            
        Returns:
            标准化后的训练场景
        """
        if not scene:
            return "健身房"
            
        scene = scene.lower().strip()
        
        for standard, variations in self.scene_mapping.items():
            if any(var.lower() in scene for var in variations):
                return standard
        
        return "健身房"  # 默认返回健身房
    
    def _normalize_level(self, level: str) -> str:
        """标准化训练难度
        
        Args:
            level: 输入的训练难度
            
        Returns:
            标准化后的训练难度
        """
        if not level:
            return "初级"
            
        level = level.lower().strip()
        
        for standard, variations in self.level_mapping.items():
            if any(var.lower() in level for var in variations):
                return standard
        
        return "初级"  # 默认返回初级
    
    def recommend_exercises(self, 
                           body_part: str,
                           scene: str = "健身房",
                           level: str = "初级",
                           equipment: Optional[str] = None,
                           limit: int = 5) -> List[Dict[str, Any]]:
        """推荐健身动作
        
        Args:
            body_part: 训练部位
            scene: 训练场景
            level: 训练难度
            equipment: 器材
            limit: 结果数量限制
            
        Returns:
            推荐的动作列表
        """
        try:
            # 标准化输入
            normalized_body_part = self._normalize_body_part(body_part)
            normalized_scene = self._normalize_scene(scene)
            normalized_level = self._normalize_level(level)
            
            logger.info(f"推荐动作: 部位={normalized_body_part}, 场景={normalized_scene}, 难度={normalized_level}")
            
            # 构建查询
            query = self.db.query(Exercise)
            
            # 应用部位过滤
            if normalized_body_part != "全身":
                # 搜索目标肌肉包含该部位的动作
                query = query.filter(func.lower(Exercise.target_muscles).like(f"%{normalized_body_part.lower()}%"))
            
            # 应用难度过滤
            if normalized_level:
                query = query.filter(Exercise.difficulty == normalized_level)
            
            # 应用场景过滤
            if normalized_scene == "家庭":
                # 家庭场景通常使用体重或简单器材
                query = query.filter(
                    or_(
                        func.lower(Exercise.equipment).like("%bodyweight%"),
                        func.lower(Exercise.equipment).like("%哑铃%"),
                        func.lower(Exercise.equipment).like("%拉力带%"),
                        func.lower(Exercise.equipment).like("%无器械%")
                    )
                )
            elif normalized_scene == "户外":
                # 户外场景通常使用体重或便携器材
                query = query.filter(
                    or_(
                        func.lower(Exercise.equipment).like("%bodyweight%"),
                        func.lower(Exercise.equipment).like("%无器械%"),
                        func.lower(Exercise.equipment).like("%户外%")
                    )
                )
            
            # 应用器材过滤（如果指定）
            if equipment:
                query = query.filter(func.lower(Exercise.equipment).like(f"%{equipment.lower()}%"))
            
            # 获取结果
            exercises = query.limit(limit).all()
            
            # 没有找到结果时，放宽条件再次查询
            if not exercises:
                logger.info(f"使用放宽条件再次查询")
                
                query = self.db.query(Exercise)
                
                if normalized_body_part != "全身":
                    query = query.filter(func.lower(Exercise.target_muscles).like(f"%{normalized_body_part.lower()}%"))
                
                exercises = query.limit(limit).all()
            
            # 转换为字典列表
            result = []
            for ex in exercises:
                result.append({
                    "id": ex.id,
                    "name": ex.name,
                    "description": ex.description,
                    "target_muscles": ex.target_muscles,
                    "equipment": ex.equipment,
                    "difficulty": ex.difficulty,
                    "instructions": ex.instructions,
                    "image_url": getattr(ex, "image_url", None),
                    "video_url": getattr(ex, "video_url", None)
                })
            
            return result
        except Exception as e:
            logger.error(f"推荐动作时出错: {str(e)}")
            return []
    
    def get_exercise_by_name(self, name: str) -> Dict[str, Any]:
        """通过名称获取动作
        
        Args:
            name: 动作名称
            
        Returns:
            动作详情
        """
        try:
            # 模糊匹配动作名称
            exercise = self.db.query(Exercise).filter(
                func.lower(Exercise.name).like(f"%{name.lower()}%")
            ).first()
            
            if not exercise:
                logger.warning(f"未找到名称匹配的动作: {name}")
                return {}
            
            # 转换为字典
            result = {
                "id": exercise.id,
                "name": exercise.name,
                "description": exercise.description,
                "target_muscles": exercise.target_muscles,
                "equipment": exercise.equipment,
                "difficulty": exercise.difficulty,
                "instructions": exercise.instructions,
                "image_url": getattr(exercise, "image_url", None),
                "video_url": getattr(exercise, "video_url", None)
            }
            
            return result
        except Exception as e:
            logger.error(f"通过名称获取动作时出错: {str(e)}")
            return {}
    
    def search_exercises_by_text(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """通过文本搜索动作
        
        Args:
            query: 搜索查询
            limit: 结果数量限制
            
        Returns:
            动作列表
        """
        try:
            # 搜索名称、描述、目标肌肉和器材字段
            exercises = self.db.query(Exercise).filter(
                or_(
                    func.lower(Exercise.name).like(f"%{query.lower()}%"),
                    func.lower(Exercise.description).like(f"%{query.lower()}%"),
                    func.lower(Exercise.target_muscles).like(f"%{query.lower()}%"),
                    func.lower(Exercise.equipment).like(f"%{query.lower()}%")
                )
            ).limit(limit).all()
            
            # 转换为字典列表
            result = []
            for ex in exercises:
                result.append({
                    "id": ex.id,
                    "name": ex.name,
                    "description": ex.description,
                    "target_muscles": ex.target_muscles,
                    "equipment": ex.equipment,
                    "difficulty": ex.difficulty
                })
            
            return result
        except Exception as e:
            logger.error(f"文本搜索动作时出错: {str(e)}")
            return []
    
    @staticmethod
    def get_exercise(db: Session, exercise_id: int) -> Optional[Exercise]:
        """获取单个健身动作"""
        return crud.crud_exercise.get(db, id=exercise_id)
    
    @staticmethod
    def get_exercises(
        db: Session,
        body_part_id: Optional[int] = None,
        equipment_id: Optional[int] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Exercise]:
        """获取健身动作列表"""
        return crud.crud_exercise.get_multi_filtered(
            db, 
            body_part_id=body_part_id, 
            equipment_id=equipment_id, 
            skip=skip, 
            limit=limit
        )
    
    @staticmethod
    def search_exercises(
        db: Session,
        keyword: Optional[str] = None,
        body_part_id: Optional[int] = None,
        equipment_id: Optional[int] = None,
        muscle_id: Optional[int] = None,
        difficulty: Optional[str] = None,
        skip: int = 0,
        limit: int = 5
    ) -> List[Exercise]:
        """搜索健身动作"""
        return crud.crud_exercise.search(
            db,
            keyword=keyword,
            body_part_id=body_part_id,
            equipment_id=equipment_id,
            muscle_id=muscle_id,
            difficulty=difficulty,
            skip=skip,
            limit=limit
        )
    
    @staticmethod
    def create_exercise(db: Session, exercise: Union[ExerciseCreate, Dict[str, Any]]) -> Exercise:
        """创建健身动作"""
        return crud.crud_exercise.create(db, obj_in=exercise)
    
    @staticmethod
    def update_exercise(
        db: Session,
        exercise_id: int,
        exercise: Union[ExerciseUpdate, Dict[str, Any]]
    ) -> Optional[Exercise]:
        """更新健身动作"""
        db_exercise = crud.crud_exercise.get(db, id=exercise_id)
        if db_exercise:
            return crud.crud_exercise.update(db, db_obj=db_exercise, obj_in=exercise)
        return None
    
    @staticmethod
    def delete_exercise(db: Session, exercise_id: int) -> bool:
        """删除健身动作"""
        return crud.crud_exercise.remove(db, id=exercise_id)
    
    @staticmethod
    def increment_hit_count(db: Session, exercise_id: int) -> bool:
        """增加健身动作浏览次数"""
        return crud.crud_exercise.increment_hit_count(db, exercise_id=exercise_id)

class ExerciseDetailService:
    """健身动作详情服务类"""
    
    @staticmethod
    def get_exercise_detail(db: Session, exercise_id: int) -> Optional[ExerciseDetail]:
        """获取健身动作详情"""
        return crud.crud_exercise_detail.get_by_exercise_id(db, exercise_id=exercise_id)
    
    @staticmethod
    def create_exercise_detail(
        db: Session,
        exercise_detail: Union[ExerciseDetailCreate, Dict[str, Any]]
    ) -> ExerciseDetail:
        """创建健身动作详情"""
        exercise_id = exercise_detail.get("exercise_id") if isinstance(exercise_detail, dict) else exercise_detail.exercise_id
        return crud.crud_exercise_detail.create_with_exercise_id(
            db, 
            obj_in=exercise_detail, 
            exercise_id=exercise_id
        )
    
    @staticmethod
    def update_exercise_detail(
        db: Session,
        exercise_id: int,
        exercise_detail: Union[ExerciseDetailUpdate, Dict[str, Any]]
    ) -> Optional[ExerciseDetail]:
        """更新健身动作详情"""
        return crud.crud_exercise_detail.update_by_exercise_id(
            db, 
            exercise_id=exercise_id, 
            obj_in=exercise_detail
        )

class MuscleService:
    """肌肉服务类"""
    
    @staticmethod
    def get_muscles(db: Session) -> List[Muscle]:
        """获取所有肌肉信息"""
        return crud.crud_muscle.get_multi(db)
    
    @staticmethod
    def create_muscle(db: Session, muscle: Union[MuscleCreate, Dict[str, Any]]) -> Muscle:
        """创建肌肉信息"""
        return crud.crud_muscle.create(db, obj_in=muscle)

class BodyPartService:
    """身体部位服务类"""
    
    @staticmethod
    def get_body_parts(db: Session) -> List[BodyPart]:
        """获取所有身体部位"""
        return crud.crud_body_part.get_multi(db)
    
    @staticmethod
    def create_body_part(db: Session, body_part: Union[BodyPartCreate, Dict[str, Any]]) -> BodyPart:
        """创建身体部位"""
        return crud.crud_body_part.create(db, obj_in=body_part)

class EquipmentService:
    """器材服务类"""
    
    @staticmethod
    def get_equipment(db: Session) -> List[Equipment]:
        """获取所有器材"""
        return crud.crud_equipment.get_multi(db)
    
    @staticmethod
    def create_equipment(db: Session, equipment: Union[EquipmentCreate, Dict[str, Any]]) -> Equipment:
        """创建器材"""
        return crud.crud_equipment.create(db, obj_in=equipment) 