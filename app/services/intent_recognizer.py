from langchain.prompts import ChatPromptTemplate
from langchain.output_parsers import PydanticOutputParser
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
import logging
import json

logger = logging.getLogger(__name__)


class IntentData(BaseModel):
    """意图数据模型"""
    intent: str = Field(..., description="识别出的用户意图类型")
    confidence: float = Field(..., description="置信度，范围0-1")
    parameters: Optional[Dict[str, Any]] = Field(None, description="意图参数，例如目标身体部位等")


class IntentRecognizer:
    """意图识别器，用于判断用户输入的意图"""

    # 定义所有支持的意图类型
    INTENTS = [
        "daily_workout_plan",  # 制定单日训练计划
        "weekly_training_plan",  # 制定每周训练计划
        "exercise_info",  # 查询健身动作信息
        "diet_advice",  # 饮食建议
        "fitness_qa",  # 健身相关问答
        "body_fat_calculation",  # 体脂计算
        "calorie_calculation",  # 卡路里计算
        "general_chat",  # 一般聊天
        "discuss_training_plan" # Discuss an existing training plan
    ]

    def __init__(self, llm_service):
        """初始化意图识别器

        Args:
            llm_service: LLM服务，用于调用语言模型
        """
        from app.core.config import settings
        import time

        self.llm_service = llm_service
        self.parser = PydanticOutputParser(pydantic_object=IntentData)

        # 初始化模型服务
        from app.services.model_service import ModelService
        self.model_service = ModelService(llm_service)

        # 获取意图识别模型配置
        self.intent_model_config = self.model_service.get_model_config("intent_recognition")
        # 直接从model_config.py获取意图识别模型
        from app.core.model_config import MODEL_MAPPING
        self.intent_model = MODEL_MAPPING["intent_recognition"]["default"]
        logger.info(f"意图识别器使用模型: {self.intent_model}")

        # 缓存相关
        self.cache_enabled = True
        self.cache = {}  # 简单的内存缓存
        self.cache_ttl = 3600  # 缓存有效期（秒）
        self.cache_timestamps = {}  # 记录每个缓存项的时间戳

        logger.info(f"意图识别器初始化完成，使用模型: {self.intent_model}")

        # 使用PromptService中的模板
        from app.services.prompt_service import PromptService
        self.prompt_service = PromptService

    async def recognize_intent(self, user_input: str, context: Optional[Dict[str, Any]] = None) -> IntentData:
        """识别用户输入的意图

        Args:
            user_input: 用户输入文本
            context: Optional dictionary providing conversational context (e.g., active_plan_id)

        Returns:
            意图数据对象
        """
        # 检查缓存
        cache_key = self._generate_cache_key(user_input, context)
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            logger.info(f"使用缓存的意图识别结果: {cached_result.intent}")
            return cached_result

        # 首先尝试使用关键词匹配进行快速意图识别
        # 这可以作为一个快速的第一道防线，特别是当API调用失败时
        quick_intent_name, quick_confidence, quick_parameters = self._extract_intent_from_text(user_input)

        # 意图名称映射
        intent_mapping = {
            # 中文意图名称映射到英文标准意图
            "训练计划": "create_training_plan",
            "单日训练": "daily_workout_plan",
            "周期训练": "weekly_training_plan",
            "查询动作": "search_exercise",
            "推荐动作": "recommend_exercise",  # 确保"推荐动作"映射到recommend_exercise
            "健身咨询": "fitness_qa",
            "营养建议": "diet_advice",
            "讨论计划": "discuss_training_plan",
            "一般聊天": "general_chat",

            # 确保中文意图名称和英文意图名称都能正确映射
            "create_training_plan": "create_training_plan",
            "daily_workout_plan": "daily_workout_plan",
            "weekly_training_plan": "weekly_training_plan",
            "search_exercise": "search_exercise",
            "recommend_exercise": "recommend_exercise",
            "fitness_qa": "fitness_qa",
            "diet_advice": "diet_advice",
            "discuss_training_plan": "discuss_training_plan",
            "general_chat": "general_chat"
        }

        # 如果关键词匹配的置信度很高，直接返回结果并缓存
        if quick_confidence >= 0.9:
            logger.info(f"关键词匹配高置信度意图: {quick_intent_name}, 置信度: {quick_confidence}")
            # 映射意图名称
            mapped_intent = intent_mapping.get(quick_intent_name, quick_intent_name)
            result = IntentData(
                intent=mapped_intent,
                confidence=quick_confidence,
                parameters=quick_parameters if quick_parameters is not None else {}
            )
            # 缓存结果
            self._add_to_cache(cache_key, result)
            return result

        # 使用模型服务进行意图识别
        try:
            # 获取意图识别提示词
            prompt = self.prompt_service.get_intent_recognition_prompt(user_input)

            # 使用模型服务调用意图识别模型
            logger.debug(f"使用模型服务进行意图识别，模型: {self.intent_model}")
            response = await self.model_service.get_response(
                messages=[{"role": "user", "content": prompt}],
                model_type="intent_recognition",
                streaming=False
            )

            # 尝试解析响应
            try:
                import json
                import re

                # 检查响应是否为空或无效
                if not response or not response.strip():
                    logger.warning("收到空的意图识别响应")
                    raise ValueError("空的意图识别响应")

                # 检查响应是否包含JSON格式
                response_text = response.strip()

                # 处理可能被Markdown代码块包裹的JSON
                if "```json" in response_text:
                    # 提取JSON部分
                    json_match = re.search(r'```json\s*(.*?)\s*```', response_text, re.DOTALL)
                    if json_match:
                        response_text = json_match.group(1).strip()
                        logger.info(f"从Markdown代码块中提取JSON: {response_text[:100]}")

                # 检查是否是JSON格式
                if response_text.startswith('{') and response_text.endswith('}'):
                    # 看起来是JSON格式，尝试解析
                    try:
                        intent_data_json = json.loads(response_text)
                        # 获取意图信息
                        intent_name = intent_data_json.get("intent", "general_chat")
                        confidence = intent_data_json.get("confidence", 1.0)
                        parameters = intent_data_json.get("parameters", {})
                        logger.info(f"成功解析JSON意图: {intent_name}")

                        # 映射意图名称
                        mapped_intent = intent_mapping.get(intent_name, intent_name)
                        if mapped_intent != intent_name:
                            logger.info(f"意图名称映射: {intent_name} -> {mapped_intent}")

                        result = IntentData(
                            intent=mapped_intent,
                            confidence=confidence,
                            parameters=parameters if parameters is not None else {}
                        )

                        logger.debug(f"识别到意图: {result.intent}, 置信度: {result.confidence}")
                        # 缓存结果
                        self._add_to_cache(cache_key, result)
                        return result
                    except json.JSONDecodeError as e:
                        logger.warning(f"JSON解析失败: {str(e)}, 响应内容: {response_text[:100]}")
                        # 继续执行，使用关键词匹配结果
                else:
                    # 不是JSON格式，记录日志
                    logger.info("响应不是JSON格式")
                    # 继续执行，使用关键词匹配结果

            except Exception as e:
                logger.error(f"解析意图识别响应失败: {str(e)}")
                # 继续执行，使用关键词匹配结果

        except Exception as e:
            logger.error(f"模型服务意图识别失败: {str(e)}")
            # 继续执行，使用关键词匹配结果

        # 如果代码执行到这里，说明模型识别失败或解析失败
        # 使用关键词匹配结果作为备用
        logger.debug(f"使用关键词匹配结果作为备用: {quick_intent_name}, 置信度: {quick_confidence}")
        mapped_intent = intent_mapping.get(quick_intent_name, quick_intent_name)
        result = IntentData(
            intent=mapped_intent,
            confidence=quick_confidence,
            parameters=quick_parameters if quick_parameters is not None else {}
        )
        # 缓存结果
        self._add_to_cache(cache_key, result)
        return result

    def _generate_cache_key(self, user_input: str, context: Optional[Dict[str, Any]] = None) -> str:
        """生成缓存键

        Args:
            user_input: 用户输入
            context: 上下文信息

        Returns:
            缓存键
        """
        import hashlib

        # 简化的用户输入（去除空格和标点）
        simplified_input = ''.join(c for c in user_input if c.isalnum())

        # 如果有上下文，将关键上下文信息添加到缓存键
        context_str = ""
        if context:
            if "active_flow" in context:
                context_str += f"_flow_{context['active_flow']}"
            if "related_plan_id" in context:
                context_str += f"_plan_{context['related_plan_id']}"

        # 使用哈希函数生成固定长度的键
        key = f"{simplified_input[:50]}{context_str}"
        return hashlib.md5(key.encode()).hexdigest()

    def _get_from_cache(self, cache_key: str) -> Optional[IntentData]:
        """从缓存中获取结果

        Args:
            cache_key: 缓存键

        Returns:
            缓存的意图数据，如果没有则返回None
        """
        import time

        if not self.cache_enabled:
            return None

        current_time = time.time()
        if cache_key in self.cache:
            # 检查缓存是否过期
            timestamp = self.cache_timestamps.get(cache_key, 0)
            if current_time - timestamp < self.cache_ttl:
                return self.cache[cache_key]
            else:
                # 缓存过期，删除
                del self.cache[cache_key]
                del self.cache_timestamps[cache_key]

        return None

    def _add_to_cache(self, cache_key: str, intent_data: IntentData):
        """添加结果到缓存

        Args:
            cache_key: 缓存键
            intent_data: 意图数据
        """
        import time

        if not self.cache_enabled:
            return

        self.cache[cache_key] = intent_data
        self.cache_timestamps[cache_key] = time.time()

        # 简单的缓存大小控制
        if len(self.cache) > 1000:  # 最多保存1000个缓存项
            # 删除最旧的缓存项
            oldest_key = min(self.cache_timestamps.keys(), key=lambda k: self.cache_timestamps[k])
            del self.cache[oldest_key]
            del self.cache_timestamps[oldest_key]

    def _extract_intent_from_text(self, text: str) -> tuple:
        """从文本中提取意图

        Args:
            text: 响应文本

        Returns:
            tuple: (意图名称, 置信度, 参数字典)
        """
        # 关键词映射到意图
        keyword_intent_map = {
            "训练计划": ["训练计划", "健身计划", "锻炼计划", "健身方案", "锻炼方案", "训练方案", "制定计划"],
            "单日训练": ["今天训练", "单日训练", "一天训练", "今日训练", "单次训练", "今天锻炼", "单日计划", "居家训练", "健身房训练"],
            "周期训练": ["周训练", "周计划", "每周训练", "周期训练", "长期训练", "每周计划", "月计划", "训练周期"],
            "查询动作": ["动作介绍", "动作说明", "如何做", "怎么做", "动作详情", "动作查询", "正确姿势", "标准动作"],
            "推荐动作": ["推荐动作", "建议动作", "适合的动作", "可以做什么动作", "哪些动作好", "动作推荐", "练什么", "练哪些"],
            "健身咨询": ["健身问题", "健身知识", "健身方法", "健身技巧", "健身建议", "肌肉生长", "代谢", "碳循环", "无氧运动", "有氧运动",
                      "超量恢复", "乳酸", "肌纤维", "肌肉酸痛", "健身原理", "健身科学", "训练理论", "健身房", "居家"],
            "营养建议": ["饮食", "营养", "吃什么", "食物", "蛋白质", "碳水", "脂肪", "减肥", "增肌饮食", "热量", "卡路里",
                      "宏量营养素", "微量营养素", "维生素", "矿物质", "补剂", "蛋白粉", "氨基酸", "肌酸"],
            "一般聊天": ["你好", "谢谢", "再见", "聊天", "闲聊", "你是谁", "介绍一下", "能做什么"]
        }

        # 默认为一般聊天
        intent_name = "一般聊天"
        confidence = 0.7
        parameters = {}

        # 检查文本中是否包含关键词
        max_matches = 0
        for intent, keywords in keyword_intent_map.items():
            matches = sum(1 for keyword in keywords if keyword in text)
            if matches > max_matches:
                max_matches = matches
                intent_name = intent
                # 根据匹配数量调整置信度
                confidence = min(0.7 + 0.05 * matches, 0.95)

        # 更精确地识别 "<部位>怎么练" 或 "练<部位>"
        # 常见身体部位关键词
        body_parts_keywords = ["胸", "背", "腿", "肩", "手臂", "二头", "三头", "腹", "腰", "臀", "核心", "胸肌", "背肌",
                              "腿部", "肩部", "手臂", "二头肌", "三头肌", "腹肌", "腰部", "臀部", "股四头肌", "股二头肌",
                              "小腿", "前臂", "上背", "下背", "上胸", "中胸", "下胸", "斜方肌", "三角肌"]
        training_verbs = ["练", "锻炼", "训练", "强化", "增强", "塑造", "雕刻"]
        query_keywords = ["怎么", "如何", "怎样", "什么方法", "什么动作", "哪些动作"]

        # 训练环境关键词
        environment_keywords = {
            "居家": ["居家", "家里", "家中", "在家", "不去健身房", "没有器械", "简易", "徒手"],
            "健身房": ["健身房", "器械", "杠铃", "哑铃", "器材", "健身中心", "健身会所"]
        }

        # 训练目标关键词
        goal_keywords = {
            "增肌": ["增肌", "长肌肉", "增长肌肉", "肌肉增长", "肌肉增大", "肌肉生长", "块头", "壮", "增重"],
            "减脂": ["减脂", "减肥", "瘦身", "减重", "体重管理", "瘦", "塑形", "减掉赘肉", "燃脂"],
            "力量": ["力量", "变强", "更强", "爆发力", "力量训练", "举重", "硬拉", "深蹲", "卧推"],
            "耐力": ["耐力", "持久", "有氧", "跑步", "游泳", "骑行", "心肺", "心肺功能"],
            "灵活性": ["灵活性", "柔韧性", "拉伸", "伸展", "瑜伽", "普拉提"]
        }

        # 识别身体部位
        found_body_part = None
        for bp in body_parts_keywords:
            if bp in text:
                found_body_part = bp
                break

        # 识别训练环境
        found_environment = None
        for env, keywords in environment_keywords.items():
            if any(keyword in text for keyword in keywords):
                found_environment = env
                break

        # 识别训练目标
        found_goal = None
        for goal, keywords in goal_keywords.items():
            if any(keyword in text for keyword in keywords):
                found_goal = goal
                break

        # 识别是否是动作查询
        is_exercise_query = False
        if found_body_part:
            # 检查模式如 "胸怎么练" 或 "怎么练胸" 或 "腿部怎么练"
            # 优先检查常见模式
            if any(f"{found_body_part}怎么练" in text or
                   f"{found_body_part}如何练" in text or
                   f"怎么练{found_body_part}" in text or
                   f"如何练{found_body_part}" in text for found_body_part in [found_body_part]):
                is_exercise_query = True
                logger.info(f"检测到动作查询模式: '{found_body_part}怎么练' 或类似模式")
            else:
                # 更详细的检查
                for verb in training_verbs:
                    for query_word in query_keywords:
                        if (found_body_part + query_word + verb in text) or \
                           (query_word + verb + found_body_part in text) or \
                           (found_body_part + verb in text and (query_word in text or not query_keywords)) or \
                           (verb + found_body_part in text and (query_word in text or not query_keywords)):
                            is_exercise_query = True
                            logger.info(f"检测到动作查询模式: '{query_word}{verb}{found_body_part}' 或类似模式")
                            break
                    if is_exercise_query:
                        break
            # 检查模式如 "练胸"
            if not is_exercise_query:
                for verb in training_verbs:
                    if verb + found_body_part in text or found_body_part + verb in text:
                        is_exercise_query = True
                        break

        # 识别是否是训练计划请求
        is_training_plan_request = False
        if "计划" in text or "方案" in text or "安排" in text:
            if "单日" in text or "一天" in text or "今天" in text or "今日" in text:
                intent_name = "单日训练"
                confidence = 0.90
                is_training_plan_request = True
            elif "周" in text or "每周" in text or "长期" in text or "月" in text:
                intent_name = "周期训练"
                confidence = 0.90
                is_training_plan_request = True
            else:
                # 默认为单日训练计划
                intent_name = "单日训练"
                confidence = 0.85
                is_training_plan_request = True

        # 如果是动作查询
        if is_exercise_query and not is_training_plan_request:
            intent_name = "推荐动作"
            confidence = 0.95  # 提高置信度，确保优先级高于其他意图

        # 添加参数
        if found_body_part:
            parameters["body_part"] = found_body_part

        if found_environment:
            parameters["training_environment"] = found_environment

        if found_goal:
            parameters["training_goal"] = found_goal

        # 如果文本中包含"减肥"、"瘦身"等关键词，可能是营养建议
        if any(keyword in text for keyword in ["减肥", "瘦身", "减重", "体重管理"]):
            if "饮食" in text or "吃" in text or "食物" in text:
                intent_name = "营养建议"
                confidence = 0.85

        # 如果文本中包含"增肌"、"增重"等关键词，可能是训练计划或营养建议
        if any(keyword in text for keyword in ["增肌", "增重", "长肌肉", "肌肉增长"]):
            if "训练" in text or "锻炼" in text:
                intent_name = "训练计划"
                confidence = 0.85
            elif "饮食" in text or "吃" in text or "食物" in text:
                intent_name = "营养建议"
                confidence = 0.85

        # 识别健身科学知识问题
        science_keywords = ["什么是", "原理", "机制", "原因", "为什么", "怎么会", "如何发生", "科学", "理论",
                           "碳循环", "代谢", "肌肉生长", "超量恢复", "乳酸", "肌纤维", "肌肉酸痛", "蛋白质合成",
                           "激素", "睾酮", "生长激素", "胰岛素", "皮质醇", "肾上腺素"]

        if any(keyword in text for keyword in science_keywords):
            # 如果是健身科学知识问题，归类为fitness_qa
            intent_name = "健身咨询"
            confidence = 0.90

        # 识别训练环境比较问题
        if "健身房" in text and "居家" in text and ("区别" in text or "比较" in text or "差异" in text or "不同" in text):
            intent_name = "健身咨询"
            confidence = 0.90

        logger.debug(f"从文本中提取到意图: {intent_name}, 置信度: {confidence}")
        return intent_name, confidence, parameters

    def get_intent_description(self, intent: str) -> str:
        """获取意图的中文描述

        Args:
            intent: 意图类型

        Returns:
            意图的中文描述
        """
        intent_map = {
            "daily_workout_plan": "制定单日训练计划",
            "weekly_training_plan": "制定每周训练计划",
            "exercise_info": "查询健身动作信息",
            "diet_advice": "饮食建议",
            "fitness_qa": "健身问答",
            "body_fat_calculation": "体脂计算",
            "calorie_calculation": "卡路里计算",
            "general_chat": "一般聊天"
        }
        return intent_map.get(intent, "未知意图")