import os
import json
import requests
import urllib.parse
from typing import List, Dict, Any, Optional
import logging
from pathlib import Path

# 设置日志记录
logger = logging.getLogger(__name__)


class FoodSearchService:
    """从外部API获取食物数据的服务"""
    
    # 搜索API
    SEARCH_API = "https://api.boohee.com/meta-interface/v1/search/search"
    # 详情API
    DETAIL_API = "https://food.boohee.com/fb/v3/foods/{code}"
    
    # 请求头
    SEARCH_HEADERS = {
        "Host": "api.boohee.com",
        "Cookie": "acw_tc=0bca316e17449757983192145e44ec8357abe1f95a9c6ec5c47003dff6f961; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22171503444%22%2C%22first_id%22%3A%221926728eac419bf-0b843fc590e41e-1509340a-329160-1926728eac5df1%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTkyNjcyOGVhYzQxOWJmLTBiODQzZmM1OTBlNDFlLTE1MDkzNDBhLTMyOTE2MC0xOTI2NzI4ZWFjNWRmMSIsIiRpZGVudGl0eV9sb2dpbl9pZCI6IjE3MTUwMzQ0NCJ9%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%22171503444%22%7D%2C%22%24device_id%22%3A%221926728eac419bf-0b843fc590e41e-1509340a-329160-1926728eac5df1%22%7D",
        "User-Agent": "boohee/ios",
        "Os-Version": "18.3.2",
        "UserAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
        "channel": "AppStore",
        "App-Device": "ios",
        "Phone-Model": "iPhone",
        "Utc-Offset": "28800",
        "Device-Token": "PX-121DA291-D1AD-4A23-A325-D90CED95CBB3",
        "Anonymous-Token": "821394A6-96FB-45C5-A6B7-7CCBF81A7DCA1728304402",
        "Connection": "keep-alive",
        "Token": "NZKDQhaJhWt9CWyGyDd3M5MrZaEeUYn6",
        "Accept-Language": "zh-Hans-CN, en-us",
        "Phone-Platform": "iPhone 12",
        "Accept": "application/json",
        "Content-Type": "application/json; charset=utf-8",
        "App-Version": "14.0.1",
        "Phone-Device": "iPhone13,2",
        "Accept-Encoding": "gzip",
        "App-Key": "one"
    }
    
    DETAIL_HEADERS = {
        "Host": "food.boohee.com",
        "Cookie": "sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22171503444%22%2C%22first_id%22%3A%221926728eac419bf-0b843fc590e41e-1509340a-329160-1926728eac5df1%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTkyNjcyOGVhYzQxOWJmLTBiODQzZmM1OTBlNDFlLTE1MDkzNDBhLTMyOTE2MC0xOTI2NzI4ZWFjNWRmMSIsIiRpZGVudGl0eV9sb2dpbl9pZCI6IjE3MTUwMzQ0NCJ9%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%22171503444%22%7D%2C%22%24device_id%22%3A%221926728eac419bf-0b843fc590e41e-1509340a-329160-1926728eac5df1%22%7D",
        "User-Agent": "boohee/ios",
        "Os-Version": "18.3.2",
        "UserAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 18_3_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148",
        "channel": "AppStore",
        "App-Device": "ios",
        "Phone-Model": "iPhone",
        "Utc-Offset": "28800",
        "Device-Token": "PX-121DA291-D1AD-4A23-A325-D90CED95CBB3",
        "Anonymous-Token": "821394A6-96FB-45C5-A6B7-7CCBF81A7DCA1728304402",
        "Connection": "keep-alive",
        "Token": "NZKDQhaJhWt9CWyGyDd3M5MrZaEeUYn6",
        "Accept-Language": "zh-Hans-CN, en-us",
        "Phone-Platform": "iPhone 12",
        "Accept": "application/json",
        "Content-Type": "application/json; charset=utf-8",
        "App-Version": "14.0.1",
        "Phone-Device": "iPhone13,2",
        "Accept-Encoding": "gzip",
        "App-Key": "one"
    }
    
    # 图片保存路径
    IMAGE_BASE_PATH = "/data/food/"
    
    def __init__(self):
        """初始化服务"""
        # 确保图片保存目录存在
        Path(self.IMAGE_BASE_PATH).mkdir(parents=True, exist_ok=True)
    
    def search_foods(self, name: str, limit: int = 5) -> List[Dict[str, Any]]:
        """
        搜索食物
        :param name: 食物名称
        :param limit: 限制返回数量
        :return: 搜索结果列表
        """
        try:
            # 构建请求参数
            params = {
                "page_from": "app_homepage",
                "q": name,
                "token": "NZKDQhaJhWt9CWyGyDd3M5MrZaEeUYn6",
                "page": 1
            }
            
            # 发送请求
            response = requests.get(
                self.SEARCH_API,
                params=params,
                headers=self.SEARCH_HEADERS
            )
            
            # 检查响应状态
            if response.status_code != 200:
                logger.error(f"搜索食物失败: {response.status_code} - {response.text}")
                return []
            
            # 解析响应数据
            data = response.json()
            if data.get("code") != 0 or "data" not in data:
                logger.error(f"搜索食物返回错误: {data}")
                return []
            
            # 获取列表数据
            food_list = data["data"].get("list", [])
            
            # 限制返回数量
            return food_list[:limit]
            
        except Exception as e:
            logger.error(f"搜索食物异常: {str(e)}")
            return []
    
    def get_food_detail(self, code: str) -> Optional[Dict[str, Any]]:
        """
        获取食物详情
        :param code: 食物代码
        :return: 食物详情数据
        """
        try:
            # 构建请求URL
            url = self.DETAIL_API.format(code=code)
            params = {"tab": "food_details"}
            
            # 发送请求
            response = requests.get(
                url,
                params=params,
                headers=self.DETAIL_HEADERS
            )
            
            # 检查响应状态
            if response.status_code != 200:
                logger.error(f"获取食物详情失败: {response.status_code} - {response.text}")
                return None
            
            # 解析响应数据
            data = response.json()
            if not data.get("success") or "data" not in data:
                logger.error(f"获取食物详情返回错误: {data}")
                return None
            
            # 返回食物数据
            return data["data"]
            
        except Exception as e:
            logger.error(f"获取食物详情异常: {str(e)}")
            return None
    
    def download_image(self, url: str, filename: str) -> bool:
        """
        下载图片
        :param url: 图片URL
        :param filename: 保存的文件名
        :return: 是否成功
        """
        try:
            response = requests.get(url, stream=True)
            if response.status_code != 200:
                logger.error(f"下载图片失败: {response.status_code}")
                return False
            
            # 确保目录存在
            os.makedirs(os.path.dirname(filename), exist_ok=True)
            
            # 保存图片
            with open(filename, 'wb') as f:
                for chunk in response.iter_content(1024):
                    f.write(chunk)
            
            return True
            
        except Exception as e:
            logger.error(f"下载图片异常: {str(e)}")
            return False
    
    def process_food_data(self, food_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理食物数据，转换为系统需要的格式
        :param food_data: 原始食物数据
        :return: 处理后的食物数据
        """
        result = {}
        
        # 提取食品基本信息
        if "food" in food_data:
            food = food_data["food"]
            result["food"] = {
                "name": food.get("name", ""),
                "code": food.get("code", ""),
                "category": food.get("category"),
                "food_type": food.get("food_type", ""),
                "goods_id": food.get("goods_id"),
                "thumb_image_url": food.get("thumb_image_url", ""),
                "large_image_url": food.get("large_image_url", ""),
                "is_liquid": food.get("is_liquid", False),
                "can_revise": food.get("can_revise", False)
            }
            
            # 下载图片
            code = food.get("code", "")
            if code and result["food"]["thumb_image_url"]:
                thumb_filename = f"{self.IMAGE_BASE_PATH}{code}/{code}_thumb.png"
                self.download_image(result["food"]["thumb_image_url"], thumb_filename)
                result["food"]["thumb_image_url"] = thumb_filename
            
            if code and result["food"]["large_image_url"]:
                large_filename = f"{self.IMAGE_BASE_PATH}{code}/{code}_large.png"
                self.download_image(result["food"]["large_image_url"], large_filename)
                result["food"]["large_image_url"] = large_filename
        
        # 提取营养概况
        if "nutritional_profile" in food_data:
            np = food_data["nutritional_profile"]
            profile = {}
            
            # 获取红绿灯信息
            if "red_green_section" in np:
                rgs = np["red_green_section"]
                profile["health_light"] = rgs.get("health_light")
                profile["lights"] = rgs.get("lights", [])
                profile["warnings"] = rgs.get("warnings", [])
                profile["warning_scenes"] = rgs.get("warning_scenes", [])
            
            # 获取营养素信息
            if "nutrients_section" in np:
                ns = np["nutrients_section"]
                profile["calory"] = ns.get("calory")
                
                # 获取能量构成比例
                if "energy_fractions" in ns:
                    ef = ns["energy_fractions"]
                    profile["protein_fraction"] = ef.get("protein")
                    profile["fat_fraction"] = ef.get("fat")
                    profile["carb_fraction"] = ef.get("carbohydrate")
            
            # 获取食物等级
            if "other_section" in np and "food_rank" in np["other_section"]:
                profile["food_rank"] = np["other_section"]["food_rank"]
            
            result["nutritional_profile"] = profile
        
        # 提取营养素详情
        nutrients = []
        if "nutritional_profile" in food_data and "nutrients_section" in food_data["nutritional_profile"]:
            ns = food_data["nutritional_profile"]["nutrients_section"]
            if "values" in ns:
                values = ns["values"]
                
                # 处理主要营养素
                for category, items in values.items():
                    for item in items:
                        nutrients.append({
                            "name_en": item.get("name_en", ""),
                            "name_cn": item.get("name", ""),
                            "value": item.get("value"),
                            "unit": item.get("unit", ""),
                            "unit_name": item.get("unit_name", ""),
                            "precision": item.get("precision"),
                            "nrv": item.get("nrv"),
                            "category": category
                        })
        
        result["nutrients"] = nutrients
        
        # 提取计量单位
        units = []
        if "nutritional_profile" in food_data and "nutrients_section" in food_data["nutritional_profile"]:
            ns = food_data["nutritional_profile"]["nutrients_section"]
            if "units" in ns:
                for unit in ns["units"]:
                    units.append({
                        "unit_name": unit.get("unit_name", ""),
                        "weight": float(unit.get("weight", 0)),
                        "eat_weight": float(unit.get("eat_weight", 0)),
                        "is_default": unit.get("id", 0) == 0  # 默认单位ID为0
                    })
        
        result["units"] = units
        
        return result
    
    def fetch_and_process_foods(self, name: str, limit: int = 5) -> List[Dict[str, Any]]:
        """
        搜索食物并处理数据
        :param name: 食物名称
        :param limit: 限制返回数量
        :return: 处理后的食物数据列表
        """
        # 搜索食物
        food_list = self.search_foods(name, limit)
        if not food_list:
            return []
        
        # 处理每个食物的详情
        processed_foods = []
        for food in food_list:
            code = food.get("code")
            if not code:
                continue
            
            # 获取食物详情
            detail = self.get_food_detail(code)
            if not detail:
                continue
            
            # 处理数据
            processed_food = self.process_food_data(detail)
            if processed_food:
                processed_foods.append(processed_food)
        
        return processed_foods 