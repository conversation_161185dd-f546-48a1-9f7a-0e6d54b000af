"""
适配版本的图构建器 - 不依赖于LangGraph，而是使用传统方法构建聊天逻辑
"""

import logging
from typing import Dict, Any, List, Callable, Tuple
from .state_definitions import FitnessAssistantState, update_state, UPDATERS

from .graph_nodes.router import intent_router
from .graph_nodes.param_collector import param_collector
from .graph_nodes.training_plan_expert import training_plan_expert
from .graph_nodes.exercise_recommendation_expert import exercise_recommendation_expert
from .graph_nodes.user_info_collector import user_info_collector
from .graph_nodes.fitness_qa_expert import fitness_qa_expert
from .graph_nodes.general_chat_expert import general_chat_expert
from .graph_nodes.interruption_handler import interruption_handler

logger = logging.getLogger(__name__)

# 定义节点类型
NodeType = Callable[[Dict[str, Any]], Dict[str, Any]]

class SimplifiedFitnessGraph:
    """简化版的健身助手流程图，不依赖于LangGraph"""
    
    def __init__(self):
        """初始化图"""
        self.nodes = {
            "router": intent_router,
            "param_collector": param_collector,
            "training_plan_expert": training_plan_expert,
            "exercise_recommendation_expert": exercise_recommendation_expert,
            "user_info_collector": user_info_collector,
            "fitness_qa_expert": fitness_qa_expert,
            "general_chat_expert": general_chat_expert,
            "interruption_handler": interruption_handler
        }
        self.state_cache = {}
    
    def get_state(self, config):
        """获取图的状态"""
        thread_id = config.get("configurable", {}).get("thread_id", "default")
        if thread_id not in self.state_cache:
            # 返回空状态
            return None
        return self.state_cache[thread_id]
    
    def update_state(self, config, state_delta):
        """更新图状态"""
        thread_id = config.get("configurable", {}).get("thread_id", "default")
        if thread_id not in self.state_cache:
            # 创建新状态并应用更新
            empty_state = {"messages": [], "user_info": {}, "training_params": {}, "metadata": {}, "dialog_state": []}
            self.state_cache[thread_id] = update_state(empty_state, state_delta)
        else:
            # 更新现有状态
            state = self.state_cache[thread_id]
            self.state_cache[thread_id] = update_state(state, state_delta)
    
    def _execute_router(self, state):
        """执行路由器确定下一步"""
        # 使用路由节点确定流程
        router_result = self.nodes["router"](state)
        # 获取对话状态
        dialog_state = state.get("dialog_state", [])
        if not dialog_state:
            return "general_chat_expert"
        
        # 获取最新状态
        current_state = dialog_state[-1]
        logger.info(f"Current dialog state: {current_state}")
        
        # 有效的节点名称列表
        valid_nodes = [
            "param_collector", "training_plan_expert", 
            "exercise_recommendation_expert", "user_info_collector",
            "fitness_qa_expert", "general_chat_expert",
            "interruption_handler"
        ]
        
        # 检查当前状态是否为有效节点
        if current_state in valid_nodes:
            return current_state
        
        # 默认回退到通用聊天
        return "general_chat_expert"
    
    def _process_expert_node(self, node_name, state):
        """处理专家节点"""
        # 执行节点处理
        result_state = self.nodes[node_name](state)
        # 检查是否需要继续路由
        dialog_state = result_state.get("dialog_state", [])
        if not dialog_state or dialog_state[-1] == "pop":
            return result_state, "router"
        # 否则结束流程
        return result_state, None
    
    def invoke(self, input_data, config):
        """执行图流程"""
        thread_id = config.get("configurable", {}).get("thread_id", "default")
        if thread_id not in self.state_cache:
            # 初始化新状态
            logger.info(f"为会话创建新状态: {thread_id}")
            self.state_cache[thread_id] = {
                "messages": [],
                "user_info": {},
                "training_params": {},
                "metadata": {},
                "dialog_state": ["router"]
            }
        
        # 获取当前状态
        state = self.state_cache[thread_id]
        
        # 首先更新消息
        if "messages" in input_data:
            state = update_state(state, {"messages": input_data["messages"]})
        
        # 继续执行直到完成
        current_node = "router"
        while current_node:
            logger.info(f"执行节点: {current_node}")
            
            if current_node == "router":
                # 路由器节点决定下一步
                next_node = self._execute_router(state)
                current_node = next_node
            else:
                # 执行专家节点
                result, next_node = self._process_expert_node(current_node, state)
                # 更新状态
                state = result
                current_node = next_node
        
        # 更新缓存状态
        self.state_cache[thread_id] = state
        return state

    def stream(self, input_data, config, stream_mode=None):
        """模拟流式输出"""
        result = self.invoke(input_data, config)
        yield result


def create_fitness_assistant_graph(checkpoint_dir: str = None):
    """创建适配版本的健身助手图
    
    Args:
        checkpoint_dir: 检查点目录（为兼容设计，实际未使用）
    
    Returns:
        SimplifiedFitnessGraph 实例，模拟 LangGraph 接口
    """
    # 创建简化版的图
    graph = SimplifiedFitnessGraph()
    
    # 返回图实例
    return graph 