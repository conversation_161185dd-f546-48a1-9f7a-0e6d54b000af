from sqlalchemy.orm import Session
from sqlalchemy.future import select
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from app.models.team import (
    Team, TeamMembership, TeamRole, ClientRelation, 
    ClientStatus, ClientTrainingPlan, TrainingSession,
    SessionExerciseRecord, TrainingPlanTemplate, TemplateExercise
)
from app.models.user import User
from app.models.exercise import Exercise
from app.schemas.team import (
    ClientPlanCreate, ClientPlanUpdate, ClientTrainingPlanResponse,
    SessionResponse, SetRecordCreate, SessionFeedback,
    TemplateCreate, TemplateResponse
)
from app.services.team_service import TeamService, TeamServiceException
from app.services.team_client_service import TeamClientService

class TrainingPlanNotFoundException(TeamServiceException):
    """训练计划不存在异常"""
    pass

class SessionNotFoundException(TeamServiceException):
    """训练课程不存在异常"""
    pass

class TemplateNotFoundException(TeamServiceException):
    """模板不存在异常"""
    pass

class ScheduleConflictException(TeamServiceException):
    """时间冲突异常"""
    pass

class TeamTrainingService:
    """团队训练服务，处理团队训练相关业务逻辑"""
    
    def __init__(self, db: Session):
        """初始化
        
        Args:
            db (Session): 数据库会话
        """
        self.db = db
        self.team_service = TeamService(db)
        self.client_service = TeamClientService(db)
    
    async def create_training_template(self, team_id: int, template_data: TemplateCreate, current_user: User) -> TrainingPlanTemplate:
        """创建训练计划模板
        
        Args:
            team_id (int): 团队ID
            template_data (TemplateCreate): 模板创建数据
            current_user (User): 当前用户
            
        Returns:
            TrainingPlanTemplate: 创建的模板
        """
        # 检查权限
        await self.team_service._check_team_permission(team_id, current_user.id, [TeamRole.OWNER, TeamRole.ADMIN, TeamRole.COACH])
        
        async with self.db.begin():
            # 创建模板
            template = TrainingPlanTemplate(
                name=template_data.name,
                description=template_data.description,
                creator_id=current_user.id,
                team_id=team_id,
                duration_weeks=template_data.duration_weeks,
                sessions_per_week=template_data.sessions_per_week,
                difficulty_level=template_data.difficulty_level,
                target_audience=template_data.target_audience,
                equipment_required=template_data.equipment_required,
                is_public=template_data.is_public
            )
            self.db.add(template)
            await self.db.flush()
            
            # 创建模板练习
            for i, exercise_data in enumerate(template_data.exercises):
                exercise = TemplateExercise(
                    template_id=template.id,
                    exercise_id=exercise_data.get("exercise_id"),
                    sets=exercise_data.get("sets", 3),
                    reps=exercise_data.get("reps", 10),
                    rest_seconds=exercise_data.get("rest_seconds", 60),
                    order=i + 1,
                    notes=exercise_data.get("notes"),
                    weight=exercise_data.get("weight"),
                    target_rpe=exercise_data.get("target_rpe"),
                    tempo=exercise_data.get("tempo")
                )
                self.db.add(exercise)
        
        return template
    
    async def get_template(self, template_id: int) -> Optional[TrainingPlanTemplate]:
        """获取模板
        
        Args:
            template_id (int): 模板ID
            
        Returns:
            Optional[TrainingPlanTemplate]: 模板对象，如果不存在则返回None
        """
        query = select(TrainingPlanTemplate).filter(TrainingPlanTemplate.id == template_id)
        result = await self.db.execute(query)
        template = result.scalars().first()
        
        if not template:
            raise TemplateNotFoundException(f"Template with ID {template_id} not found")
        
        return template
    
    async def get_team_templates(self, team_id: int, current_user: User = None) -> List[Dict[str, Any]]:
        """获取团队模板列表
        
        Args:
            team_id (int): 团队ID
            current_user (User): 当前用户
            
        Returns:
            List[Dict[str, Any]]: 模板列表
        """
        # 检查访问权限
        if current_user:
            has_access = await self.team_service.check_team_access(team_id, current_user.id)
            if not has_access:
                raise TeamServiceException("No access to this team")
        
        # 查询模板
        query = select(
            TrainingPlanTemplate,
            User.nickname.label("creator_name")
        ).join(
            User, TrainingPlanTemplate.creator_id == User.id
        ).filter(
            TrainingPlanTemplate.team_id == team_id
        ).order_by(TrainingPlanTemplate.created_at.desc())
        
        result = await self.db.execute(query)
        templates = []
        
        for template, creator_name in result:
            template_dict = {
                "id": template.id,
                "name": template.name,
                "description": template.description,
                "creator_id": template.creator_id,
                "team_id": template.team_id,
                "duration_weeks": template.duration_weeks,
                "sessions_per_week": template.sessions_per_week,
                "difficulty_level": template.difficulty_level,
                "is_public": template.is_public,
                "created_at": template.created_at,
                "creator_name": creator_name
            }
            templates.append(template_dict)
        
        return templates
    
    async def assign_training_plan(self, client_relation_id: int, plan_data: ClientPlanCreate, current_user: User) -> ClientTrainingPlan:
        """为客户分配训练计划
        
        Args:
            client_relation_id (int): 客户关系ID
            plan_data (ClientPlanCreate): 计划创建数据
            current_user (User): 当前用户
            
        Returns:
            ClientTrainingPlan: 创建的训练计划
        """
        # 获取客户关系
        client_relation = await self.client_service.get_client_relation(client_relation_id)
        
        # 检查权限
        await self.team_service._check_team_permission(client_relation.team_id, current_user.id, [TeamRole.OWNER, TeamRole.ADMIN, TeamRole.COACH])
        
        # 检查是否是客户的教练或管理员
        if current_user.id != client_relation.coach_id:
            # 如果不是教练，检查是否是管理员或所有者
            membership = await self.db.execute(
                select(TeamMembership).filter(
                    TeamMembership.team_id == client_relation.team_id,
                    TeamMembership.user_id == current_user.id,
                    TeamMembership.status == MembershipStatus.ACTIVE
                )
            )
            membership = membership.scalars().first()
            if not membership or membership.role not in [TeamRole.OWNER, TeamRole.ADMIN]:
                raise TeamServiceException("Only the client's coach, team admin or owner can assign training plans")
        
        # 验证训练计划时间安排
        await self._validate_training_schedule(client_relation.client_id, current_user.id, plan_data)
        
        async with self.db.begin():
            # 创建训练计划
            client_plan = ClientTrainingPlan(
                client_relation_id=client_relation_id,
                training_plan_id=plan_data.training_plan_id,
                coach_id=current_user.id,
                status="scheduled",
                start_date=plan_data.start_date,
                end_date=plan_data.end_date,
                scheduled_time=plan_data.scheduled_time,
                notes=plan_data.notes
            )
            self.db.add(client_plan)
            await self.db.flush()
            
            # 计算并设置下次训练时间
            next_session = await self._calculate_next_session(plan_data.start_date, plan_data.scheduled_time)
            client_plan.next_session = next_session
            
            # 创建训练课程
            await self._create_training_sessions(client_plan, plan_data.scheduled_time)
        
        return client_plan
    
    async def _validate_training_schedule(self, client_id: int, coach_id: int, plan_data: ClientPlanCreate) -> bool:
        """验证训练时间安排
        
        Args:
            client_id (int): 客户ID
            coach_id (int): 教练ID
            plan_data (ClientPlanCreate): 计划创建数据
            
        Returns:
            bool: 是否有效
        """
        # 检查教练时间冲突
        coach_conflicts = await self._check_coach_schedule_conflicts(coach_id, plan_data)
        if coach_conflicts:
            raise ScheduleConflictException("Coach schedule conflict detected")
        
        # 检查客户时间冲突
        client_conflicts = await self._check_client_schedule_conflicts(client_id, plan_data)
        if client_conflicts:
            raise ScheduleConflictException("Client schedule conflict detected")
        
        return True
    
    async def _check_coach_schedule_conflicts(self, coach_id: int, plan_data: ClientPlanCreate) -> List[Dict[str, Any]]:
        """检查教练时间冲突
        
        Args:
            coach_id (int): 教练ID
            plan_data (ClientPlanCreate): 计划创建数据
            
        Returns:
            List[Dict[str, Any]]: 冲突列表
        """
        # 实现教练时间冲突检查逻辑
        # 这里简化处理，实际应该检查教练在该时间段是否有其他训练安排
        return []
    
    async def _check_client_schedule_conflicts(self, client_id: int, plan_data: ClientPlanCreate) -> List[Dict[str, Any]]:
        """检查客户时间冲突
        
        Args:
            client_id (int): 客户ID
            plan_data (ClientPlanCreate): 计划创建数据
            
        Returns:
            List[Dict[str, Any]]: 冲突列表
        """
        # 实现客户时间冲突检查逻辑
        # 这里简化处理，实际应该检查客户在该时间段是否有其他训练安排
        return []
    
    async def _calculate_next_session(self, start_date: datetime, scheduled_time: Dict[str, Any]) -> datetime:
        """计算下次训练时间
        
        Args:
            start_date (datetime): 开始日期
            scheduled_time (Dict[str, Any]): 时间安排
            
        Returns:
            datetime: 下次训练时间
        """
        # 实现下次训练时间计算逻辑
        # 这里简化处理，返回开始日期
        return start_date
    
    async def _create_training_sessions(self, client_plan: ClientTrainingPlan, scheduled_time: Dict[str, Any]) -> List[TrainingSession]:
        """创建训练课程
        
        Args:
            client_plan (ClientTrainingPlan): 客户训练计划
            scheduled_time (Dict[str, Any]): 时间安排
            
        Returns:
            List[TrainingSession]: 创建的训练课程列表
        """
        sessions = []
        current_date = client_plan.start_date
        
        # 简化处理，每周创建一个训练课程
        while current_date <= client_plan.end_date:
            session = TrainingSession(
                client_plan_id=client_plan.id,
                scheduled_start=current_date,
                status="scheduled"
            )
            self.db.add(session)
            sessions.append(session)
            
            # 下一周
            current_date += timedelta(days=7)
        
        return sessions
    
    async def get_client_training_plans(self, client_relation_id: int, status: Optional[str] = None, current_user: User = None) -> List[Dict[str, Any]]:
        """获取客户训练计划列表
        
        Args:
            client_relation_id (int): 客户关系ID
            status (Optional[str]): 状态过滤
            current_user (User): 当前用户
            
        Returns:
            List[Dict[str, Any]]: 训练计划列表
        """
        # 获取客户关系
        client_relation = await self.client_service.get_client_relation(client_relation_id)
        
        # 检查访问权限
        if current_user:
            # 如果是客户本人，或者是客户的教练，或者是团队管理员/所有者，则允许访问
            if current_user.id != client_relation.client_id and current_user.id != client_relation.coach_id:
                has_access = await self.team_service.check_team_access(client_relation.team_id, current_user.id)
                if not has_access:
                    raise TeamServiceException("No access to this client's training plans")
        
        # 构建查询
        query = select(
            ClientTrainingPlan
        ).filter(
            ClientTrainingPlan.client_relation_id == client_relation_id
        )
        
        if status:
            query = query.filter(ClientTrainingPlan.status == status)
        
        query = query.order_by(ClientTrainingPlan.start_date.desc())
        
        result = await self.db.execute(query)
        plans = []
        
        for plan in result.scalars().all():
            # 获取训练课程数量
            sessions_query = select(func.count()).select_from(TrainingSession).filter(
                TrainingSession.client_plan_id == plan.id
            )
            sessions_result = await self.db.execute(sessions_query)
            total_sessions = sessions_result.scalar() or 0
            
            # 获取已完成的训练课程数量
            completed_sessions_query = select(func.count()).select_from(TrainingSession).filter(
                TrainingSession.client_plan_id == plan.id,
                TrainingSession.status == "completed"
            )
            completed_sessions_result = await self.db.execute(completed_sessions_query)
            completed_sessions = completed_sessions_result.scalar() or 0
            
            plan_dict = {
                "id": plan.id,
                "client_relation_id": plan.client_relation_id,
                "training_plan_id": plan.training_plan_id,
                "coach_id": plan.coach_id,
                "status": plan.status,
                "start_date": plan.start_date,
                "end_date": plan.end_date,
                "created_at": plan.created_at,
                "updated_at": plan.updated_at,
                "scheduled_time": plan.scheduled_time,
                "next_session": plan.next_session,
                "completion_rate": plan.completion_rate,
                "last_workout_date": plan.last_workout_date,
                "notes": plan.notes,
                "total_sessions": total_sessions,
                "completed_sessions": completed_sessions
            }
            plans.append(plan_dict)
        
        return plans
    
    async def get_training_plan(self, plan_id: int) -> Optional[ClientTrainingPlan]:
        """获取训练计划
        
        Args:
            plan_id (int): 计划ID
            
        Returns:
            Optional[ClientTrainingPlan]: 训练计划对象，如果不存在则返回None
        """
        query = select(ClientTrainingPlan).filter(ClientTrainingPlan.id == plan_id)
        result = await self.db.execute(query)
        plan = result.scalars().first()
        
        if not plan:
            raise TrainingPlanNotFoundException(f"Training plan with ID {plan_id} not found")
        
        return plan
    
    async def update_training_plan(self, plan_id: int, update_data: ClientPlanUpdate, current_user: User) -> ClientTrainingPlan:
        """更新训练计划
        
        Args:
            plan_id (int): 计划ID
            update_data (ClientPlanUpdate): 更新数据
            current_user (User): 当前用户
            
        Returns:
            ClientTrainingPlan: 更新后的训练计划
        """
        # 获取训练计划
        plan = await self.get_training_plan(plan_id)
        
        # 获取客户关系
        client_relation = await self.client_service.get_client_relation(plan.client_relation_id)
        
        # 检查权限
        if current_user.id != plan.coach_id:
            # 如果不是教练，检查是否是管理员或所有者
            await self.team_service._check_team_permission(client_relation.team_id, current_user.id, [TeamRole.OWNER, TeamRole.ADMIN])
        
        # 更新计划
        update_dict = update_data.dict(exclude_unset=True)
        for key, value in update_dict.items():
            setattr(plan, key, value)
        
        plan.updated_at = datetime.now()
        self.db.add(plan)
        await self.db.commit()
        await self.db.refresh(plan)
        
        return plan
    
    async def get_training_sessions(self, plan_id: int, status: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取训练课程列表
        
        Args:
            plan_id (int): 计划ID
            status (Optional[str]): 状态过滤
            
        Returns:
            List[Dict[str, Any]]: 训练课程列表
        """
        # 构建查询
        query = select(TrainingSession).filter(TrainingSession.client_plan_id == plan_id)
        
        if status:
            query = query.filter(TrainingSession.status == status)
        
        query = query.order_by(TrainingSession.scheduled_start)
        
        result = await self.db.execute(query)
        sessions = []
        
        for session in result.scalars().all():
            # 获取训练动作记录数量
            records_query = select(func.count()).select_from(SessionExerciseRecord).filter(
                SessionExerciseRecord.session_id == session.id
            )
            records_result = await self.db.execute(records_query)
            total_records = records_result.scalar() or 0
            
            session_dict = {
                "id": session.id,
                "client_plan_id": session.client_plan_id,
                "scheduled_start": session.scheduled_start,
                "actual_start": session.actual_start,
                "actual_end": session.actual_end,
                "status": session.status,
                "completion_rate": session.completion_rate,
                "feedback": session.feedback,
                "mood_rating": session.mood_rating,
                "difficulty_rating": session.difficulty_rating,
                "notes": session.notes,
                "total_exercises": total_records
            }
            sessions.append(session_dict)
        
        return sessions
    
    async def get_session(self, session_id: int) -> Optional[TrainingSession]:
        """获取训练课程
        
        Args:
            session_id (int): 课程ID
            
        Returns:
            Optional[TrainingSession]: 训练课程对象，如果不存在则返回None
        """
        query = select(TrainingSession).filter(TrainingSession.id == session_id)
        result = await self.db.execute(query)
        session = result.scalars().first()
        
        if not session:
            raise SessionNotFoundException(f"Training session with ID {session_id} not found")
        
        return session
    
    async def start_session(self, session_id: int, current_user: User) -> TrainingSession:
        """开始训练课程
        
        Args:
            session_id (int): 课程ID
            current_user (User): 当前用户
            
        Returns:
            TrainingSession: 更新后的训练课程
        """
        # 获取训练课程
        session = await self.get_session(session_id)
        
        # 获取训练计划
        plan = await self.get_training_plan(session.client_plan_id)
        
        # 获取客户关系
        client_relation = await self.client_service.get_client_relation(plan.client_relation_id)
        
        # 检查权限
        if current_user.id != plan.coach_id and current_user.id != client_relation.client_id:
            # 如果不是教练或客户本人，检查是否是管理员或所有者
            await self.team_service._check_team_permission(client_relation.team_id, current_user.id, [TeamRole.OWNER, TeamRole.ADMIN])
        
        # 更新课程状态
        session.status = "in_progress"
        session.actual_start = datetime.now()
        self.db.add(session)
        
        # 初始化训练记录
        # 这里应该根据训练计划中的动作创建训练记录
        # 简化处理，创建一些示例记录
        exercise_query = select(Exercise).limit(3)
        exercise_result = await self.db.execute(exercise_query)
        exercises = exercise_result.scalars().all()
        
        for i, exercise in enumerate(exercises):
            record = SessionExerciseRecord(
                session_id=session.id,
                exercise_id=exercise.id,
                order=i + 1,
                sets_planned=3,
                status="pending"
            )
            self.db.add(record)
        
        await self.db.commit()
        await self.db.refresh(session)
        
        return session
    
    async def complete_session(self, session_id: int, feedback_data: SessionFeedback, current_user: User) -> TrainingSession:
        """完成训练课程
        
        Args:
            session_id (int): 课程ID
            feedback_data (SessionFeedback): 反馈数据
            current_user (User): 当前用户
            
        Returns:
            TrainingSession: 更新后的训练课程
        """
        # 获取训练课程
        session = await self.get_session(session_id)
        
        # 获取训练计划
        plan = await self.get_training_plan(session.client_plan_id)
        
        # 获取客户关系
        client_relation = await self.client_service.get_client_relation(plan.client_relation_id)
        
        # 检查权限
        if current_user.id != plan.coach_id and current_user.id != client_relation.client_id:
            # 如果不是教练或客户本人，检查是否是管理员或所有者
            await self.team_service._check_team_permission(client_relation.team_id, current_user.id, [TeamRole.OWNER, TeamRole.ADMIN])
        
        # 更新课程状态
        session.status = "completed"
        session.actual_end = datetime.now()
        session.feedback = feedback_data.feedback
        session.mood_rating = feedback_data.mood_rating
        session.difficulty_rating = feedback_data.difficulty_rating
        session.notes = feedback_data.notes
        
        # 计算完成率
        records_query = select(SessionExerciseRecord).filter(SessionExerciseRecord.session_id == session.id)
        records_result = await self.db.execute(records_query)
        records = records_result.scalars().all()
        
        total_sets_planned = sum(record.sets_planned for record in records)
        total_sets_completed = sum(record.sets_completed for record in records)
        
        if total_sets_planned > 0:
            session.completion_rate = total_sets_completed / total_sets_planned
        else:
            session.completion_rate = 0
        
        self.db.add(session)
        
        # 更新训练计划
        plan.last_workout_date = datetime.now()
        
        # 计算计划完成率
        sessions_query = select(TrainingSession).filter(TrainingSession.client_plan_id == plan.id)
        sessions_result = await self.db.execute(sessions_query)
        sessions = sessions_result.scalars().all()
        
        completed_sessions = sum(1 for s in sessions if s.status == "completed")
        total_sessions = len(sessions)
        
        if total_sessions > 0:
            plan.completion_rate = completed_sessions / total_sessions
        else:
            plan.completion_rate = 0
        
        self.db.add(plan)
        await self.db.commit()
        await self.db.refresh(session)
        
        return session
    
    async def record_exercise_set(self, session_id: int, exercise_id: int, set_data: SetRecordCreate, current_user: User) -> SessionExerciseRecord:
        """记录训练组数据
        
        Args:
            session_id (int): 课程ID
            exercise_id (int): 动作ID
            set_data (SetRecordCreate): 组数据
            current_user (User): 当前用户
            
        Returns:
            SessionExerciseRecord: 更新后的训练记录
        """
        # 获取训练课程
        session = await self.get_session(session_id)
        
        # 获取训练计划
        plan = await self.get_training_plan(session.client_plan_id)
        
        # 获取客户关系
        client_relation = await self.client_service.get_client_relation(plan.client_relation_id)
        
        # 检查权限
        if current_user.id != plan.coach_id and current_user.id != client_relation.client_id:
            # 如果不是教练或客户本人，检查是否是管理员或所有者
            await self.team_service._check_team_permission(client_relation.team_id, current_user.id, [TeamRole.OWNER, TeamRole.ADMIN])
        
        # 获取训练记录
        record_query = select(SessionExerciseRecord).filter(
            SessionExerciseRecord.session_id == session_id,
            SessionExerciseRecord.exercise_id == exercise_id
        )
        record_result = await self.db.execute(record_query)
        record = record_result.scalars().first()
        
        if not record:
            # 如果记录不存在，创建新记录
            record = SessionExerciseRecord(
                session_id=session_id,
                exercise_id=exercise_id,
                order=1,
                sets_planned=1,
                status="in_progress"
            )
            self.db.add(record)
            await self.db.flush()
        
        # 更新组记录
        set_records = record.set_records or []
        set_records.append({
            "set": len(set_records) + 1,
            "weight": set_data.weight,
            "reps": set_data.reps,
            "completed": True,
            "timestamp": datetime.now().isoformat()
        })
        
        record.set_records = set_records
        record.sets_completed = len(set_records)
        
        # 更新状态
        if record.sets_completed >= record.sets_planned:
            record.status = "completed"
        else:
            record.status = "in_progress"
        
        self.db.add(record)
        await self.db.commit()
        await self.db.refresh(record)
        
        return record
