from typing import List, Optional, Dict, Any, Union
from sqlalchemy.orm import Session
from fastapi import UploadFile, HTTPException, status
import os
import aiofiles
import uuid
from datetime import datetime
import enum

from app import crud, models, schemas
from app.core.config import settings
from app.models.user import User
from app.models.daily_workout import DailyWorkout, DailyWorkoutStatus
from app.models.community import (
    Post, PostStatus, Comment, CommentStatus, 
    Notification, NotificationType, UserRelation, Image,
    PostLike, CommentLike, Report, PostReport, CommentReport, ReportStatus
)
from app.schemas.community import (
    DailyWorkoutCreate, DailyWorkoutUpdate, DailyWorkoutWithExercises,
    PostCreate, PostUpdate, PostRead,
    CommentCreate, CommentRead,
    NotificationRead,
    UserRelationResponse,
    ImageCreate, ImageUpdate, ImageResponse
)
from app.services.workout_service import WorkoutService


class CommunityService:
    """社区服务，处理社区相关业务逻辑"""
    
    def __init__(self, db: Session):
        """初始化
        
        Args:
            db: 数据库会话
        """
        self.db = db
        # 确保图片存储目录存在
        self.image_dir = os.path.join(settings.STATIC_DIR, "community_images")
        os.makedirs(self.image_dir, exist_ok=True)
    
    async def create_daily_workout(
        self, *, user_id: int, daily_workout_in: schemas.DailyWorkoutCreate
    ) -> models.DailyWorkout:
        """创建单日训练
        
        Args:
            user_id: 用户ID
            daily_workout_in: 单日训练数据
            
        Returns:
            创建的单日训练
        """
        # 创建单日训练
        daily_workout = crud.crud_daily_workout.create_with_exercises(
            db=self.db, obj_in=daily_workout_in
        )
        return daily_workout
    
    async def get_daily_workouts(
        self, *, skip: int = 0, limit: int = 100
    ) -> List[models.DailyWorkout]:
        """获取单日训练列表
        
        Args:
            skip: 跳过的记录数
            limit: 返回的记录数
            
        Returns:
            单日训练列表
        """
        return crud.crud_daily_workout.get_multi_with_exercises(
            db=self.db, skip=skip, limit=limit
        )
    
    async def save_image(self, *, image: UploadFile) -> str:
        """保存图片
        
        Args:
            image: 上传的图片
            
        Returns:
            图片的相对路径
        """
        # 生成唯一文件名
        file_ext = os.path.splitext(image.filename)[1] if image.filename else ".jpg"
        filename = f"{uuid.uuid4()}{file_ext}"
        file_path = os.path.join(self.image_dir, filename)
        
        # 保存文件
        async with aiofiles.open(file_path, "wb") as f:
            content = await image.read()
            await f.write(content)
        
        # 返回相对路径
        return f"community_images/{filename}"
    
    async def _get_workout_detail_for_daily_workout(self, daily_workout: models.DailyWorkout) -> Optional[Dict[str, Any]]:
        """获取 DailyWorkout 关联的详细训练信息
        
        Args:
            daily_workout: DailyWorkout实例
            
        Returns:
            详细的训练信息，如果没有关联的workout则返回None
        """
        if not daily_workout.workout_id:
            return None
        
        try:
            # 使用 WorkoutService 获取详细训练信息
            workout_service = WorkoutService(self.db)
            return workout_service.get_workout_detail(daily_workout.workout_id)
        except Exception as e:
            # 如果获取失败，记录错误但不抛出异常
            print(f"Error getting workout detail for daily_workout {daily_workout.id}: {str(e)}")
            return None
    
    async def _get_post_images(self, post_id: int) -> List[Dict[str, Any]]:
        """获取帖子的图片列表
        
        Args:
            post_id: 帖子ID
            
        Returns:
            图片信息列表
        """
        try:
            images = self.db.query(models.Image).filter(models.Image.post_id == post_id).all()
            return [
                {
                    "id": img.id,
                    "url": img.url,
                    "title": img.title,
                    "description": img.description,
                    "type": img.type,
                    "created_at": img.created_at,
                    "updated_at": img.updated_at
                }
                for img in images
            ]
        except Exception as e:
            print(f"Error getting images for post {post_id}: {str(e)}")
            return []
    
    async def _get_post_comments_summary(self, post_id: int, limit: int = 5) -> List[Dict[str, Any]]:
        """获取帖子的评论摘要（最新的几条评论）
        
        Args:
            post_id: 帖子ID
            limit: 返回的评论数量限制
            
        Returns:
            评论摘要列表
        """
        try:
            comments = crud.crud_comment.get_by_post(
                db=self.db, post_id=post_id, skip=0, limit=limit
            )
            
            result = []
            for comment in comments:
                # 获取评论用户信息
                user = crud.crud_user.get(db=self.db, id=comment.user_id)
                
                comment_data = {
                    "id": comment.id,
                    "content": comment.content,
                    "user_id": comment.user_id,
                    "user": {
                        "id": user.id,
                        "nickname": user.nickname,
                        "avatar": getattr(user, 'avatar', None)
                    } if user else None,
                    "created_at": comment.created_at,
                    "like_count": comment.like_count
                }
                result.append(comment_data)
            
            return result
        except Exception as e:
            print(f"Error getting comments summary for post {post_id}: {str(e)}")
            return []
    
    async def create_post(self, user_id: int, post_in: PostCreate) -> PostRead:
        post = Post(
            user_id=user_id,
            title=post_in.title,
            content=post_in.content,
            related_workout_id=post_in.related_workout_id,
            visibility=post_in.visibility
        )
        self.db.add(post)
        self.db.commit()
        self.db.refresh(post)
        return post
    
    async def create_post_with_workout(self, user_id: int, post_in: PostCreate) -> PostRead:
        """创建帖子，可以关联已存在的DailyWorkout或者创建新的DailyWorkout
        
        Args:
            user_id: 用户ID
            post_in: 帖子创建数据，包含daily_workout_id或workout_data
            
        Returns:
            创建的帖子
        """
        
        # 创建DailyWorkout（如果包含workout_data）
        daily_workout_id = post_in.daily_workout_id
        if post_in.workout_data and not daily_workout_id:
            # 计算训练时长（分钟）
            duration_minutes = post_in.workout_data.duration_seconds // 60 if post_in.workout_data.duration_seconds else None
            
            # 创建DailyWorkout
            daily_workout = models.DailyWorkout(
                user_id=user_id,
                name=post_in.title,
                title=post_in.title,
                content=post_in.content,
                training_duration=duration_minutes,
                training_sets=post_in.workout_data.total_sets,
                training_volume=post_in.workout_data.total_volume,
                visibility=post_in.visibility,
                training_date=datetime.utcnow()
            )
            
            self.db.add(daily_workout)
            self.db.flush()  # 获取ID但不提交事务
            
            # 添加训练动作
            if post_in.workout_data.exercises:
                for ex_data in post_in.workout_data.exercises:
                    exercise = models.WorkoutExercise(
                        daily_workout_id=daily_workout.id,
                        **ex_data.dict()
                    )
                    self.db.add(exercise)
            
            daily_workout_id = daily_workout.id
        
        # 创建Post
        post = models.Post(
            user_id=user_id,
            title=post_in.title,
            content=post_in.content,
            related_workout_id=daily_workout_id,
            visibility=post_in.visibility,
            tags=post_in.tags if hasattr(post_in, "tags") and post_in.tags else []
        )
        
        self.db.add(post)
        self.db.commit()
        self.db.refresh(post)
        
        # 处理图片URLs (优先使用新的images字段，向后兼容image_urls)
        image_urls = post_in.images if hasattr(post_in, "images") else post_in.image_urls if hasattr(post_in, "image_urls") else None
        
        if image_urls:
            for url in image_urls:
                image = models.Image(
                    url=url,
                    user_id=user_id,
                    post_id=post.id,
                    type="image" if not url.endswith((".mp4", ".mov", ".avi")) else "video"
                )
                self.db.add(image)
            
            self.db.commit()
        
        return post
    
    async def get_post(
        self, *, id: int, current_user_id: Optional[int] = None
    ) -> Optional[Dict[str, Any]]:
        """获取帖子详情
        
        Args:
            id: 帖子ID
            current_user_id: 当前用户ID
            
        Returns:
            帖子详情
        """
        # 获取帖子
        post = crud.crud_post.get(db=self.db, id=id)
        if not post:
            return None
        
        # 增加浏览次数
        post = crud.crud_post.increment_view_count(db=self.db, id=id)
        
        # 获取点赞数
        like_count = crud.crud_post_like.count_by_post(db=self.db, post_id=id)
        
        # 获取评论数
        comment_count = crud.crud_comment.get_by_post_count(db=self.db, post_id=id)
        
        # 获取用户是否点赞
        is_liked = False
        if current_user_id:
            like = crud.crud_post_like.get_by_user_and_post(
                db=self.db, user_id=current_user_id, post_id=id
            )
            is_liked = like is not None
        
        # 获取用户信息
        user = crud.crud_user.get(db=self.db, id=post.user_id)
        
        # 获取关联的单日训练
        related_workout = None
        related_workout_detail = None
        if post.related_workout_id:
            try:
                related_workout = await self.get_daily_workout(workout_id=post.related_workout_id)
                if related_workout:
                    # 获取详细的训练信息
                    related_workout_detail = await self._get_workout_detail_for_daily_workout(related_workout)
            except Exception as e:
                print(f"Error getting workout data for post {id}: {str(e)}")
        
        # 获取帖子图片
        images = await self._get_post_images(id)
        
        # 获取评论摘要
        comments_summary = await self._get_post_comments_summary(id, limit=5)
        
        # 构建返回数据
        post_data = {
            "id": post.id,
            "title": post.title,
            "content": post.content,
            "image_urls": getattr(post, 'image_urls', None),
            "user_id": post.user_id,
            "related_workout_id": post.related_workout_id,
            "view_count": post.view_count or 0,
            "status": post.status,
            "reported_count": post.reported_count or 0,
            "created_at": post.created_at,
            "updated_at": post.updated_at,
            "like_count": like_count,
            "comment_count": comment_count,
            "is_liked_by_current_user": is_liked,
            "user": user,
            "related_workout": related_workout,
            "related_workout_detail": related_workout_detail,
            "images": images,
            "comments_summary": comments_summary,
            "tags": getattr(post, 'tags', [])
        }
        
        return post_data

    async def get_posts(
        self, *, skip: int = 0, limit: int = 100, current_user_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """获取帖子列表
        
        Args:
            skip: 跳过的记录数
            limit: 返回的记录数
            current_user_id: 当前用户ID
            
        Returns:
            帖子列表
        """
        # 获取帖子总数
        total = crud.crud_post.get_total_count(db=self.db)
        
        # 获取帖子列表
        posts = crud.crud_post.get_multi(db=self.db, skip=skip, limit=limit)
        
        # 获取用户点赞的帖子ID列表
        liked_post_ids = []
        if current_user_id:
            liked_post_ids = crud.crud_post_like.get_posts_liked_by_user(
                db=self.db, user_id=current_user_id
            )
        
        # 构建返回数据
        items = []
        for post in posts:
            # 获取点赞数
            like_count = crud.crud_post_like.count_by_post(db=self.db, post_id=post.id)
            
            # 获取评论数
            comment_count = crud.crud_comment.get_by_post_count(db=self.db, post_id=post.id)
            
            # 获取用户是否点赞
            is_liked = post.id in liked_post_ids
            
            # 获取用户信息
            user = crud.crud_user.get(db=self.db, id=post.user_id)
            
            # 获取关联的训练数据
            related_workout = None
            related_workout_detail = None
            if post.related_workout_id:
                try:
                    related_workout = await self.get_daily_workout(workout_id=post.related_workout_id)
                    if related_workout:
                        # 获取详细的训练信息
                        related_workout_detail = await self._get_workout_detail_for_daily_workout(related_workout)
                except Exception as e:
                    print(f"Error getting workout data for post {post.id}: {str(e)}")
            
            # 获取帖子图片
            images = await self._get_post_images(post.id)
            
            # 构建帖子数据
            post_data = {
                "id": post.id,
                "title": post.title,
                "content": post.content,
                "image_urls": getattr(post, 'image_urls', None),
                "user_id": post.user_id,
                "related_workout_id": post.related_workout_id,
                "view_count": post.view_count or 0,
                "status": post.status,
                "reported_count": post.reported_count or 0,
                "created_at": post.created_at,
                "updated_at": post.updated_at,
                "like_count": like_count,
                "comment_count": comment_count,
                "is_liked_by_current_user": is_liked,
                "user": user,
                "related_workout": related_workout,
                "related_workout_detail": related_workout_detail,
                "images": images,
                "tags": getattr(post, 'tags', [])
            }
            
            items.append(post_data)
        
        return {
            "total": total,
            "items": items
        }
    
    async def get_user_posts(
        self, *, user_id: int, skip: int = 0, limit: int = 100, current_user_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """获取用户的帖子列表
        
        Args:
            user_id: 用户ID
            skip: 跳过的记录数
            limit: 返回的记录数
            current_user_id: 当前用户ID
            
        Returns:
            帖子列表
        """
        # 获取用户的帖子总数
        total = crud.crud_post.get_by_user_count(db=self.db, user_id=user_id)
        
        # 获取用户的帖子列表
        posts = crud.crud_post.get_multi_by_user(
            db=self.db, user_id=user_id, skip=skip, limit=limit
        )
        
        # 获取用户点赞的帖子ID列表
        liked_post_ids = []
        if current_user_id:
            liked_post_ids = crud.crud_post_like.get_posts_liked_by_user(
                db=self.db, user_id=current_user_id
            )
        
        # 构建返回数据
        items = []
        for post in posts:
            # 获取点赞数
            like_count = crud.crud_post_like.count_by_post(db=self.db, post_id=post.id)
            
            # 获取评论数
            comment_count = crud.crud_comment.get_by_post_count(db=self.db, post_id=post.id)
            
            # 获取用户是否点赞
            is_liked = post.id in liked_post_ids
            
            # 获取用户信息
            user = crud.crud_user.get(db=self.db, id=post.user_id)
            
            # 获取关联的训练数据
            related_workout = None
            related_workout_detail = None
            if post.related_workout_id:
                try:
                    related_workout = await self.get_daily_workout(workout_id=post.related_workout_id)
                    if related_workout:
                        # 获取详细的训练信息
                        related_workout_detail = await self._get_workout_detail_for_daily_workout(related_workout)
                except Exception as e:
                    print(f"Error getting workout data for post {post.id}: {str(e)}")
            
            # 获取帖子图片
            images = await self._get_post_images(post.id)
            
            # 构建帖子数据
            post_data = {
                "id": post.id,
                "title": post.title,
                "content": post.content,
                "image_urls": getattr(post, 'image_urls', None),
                "user_id": post.user_id,
                "related_workout_id": post.related_workout_id,
                "view_count": post.view_count or 0,
                "status": post.status,
                "reported_count": post.reported_count or 0,
                "created_at": post.created_at,
                "updated_at": post.updated_at,
                "like_count": like_count,
                "comment_count": comment_count,
                "is_liked_by_current_user": is_liked,
                "user": user,
                "related_workout": related_workout,
                "related_workout_detail": related_workout_detail,
                "images": images,
                "tags": getattr(post, 'tags', [])
            }
            
            items.append(post_data)
        
        return {
            "total": total,
            "items": items
        }
    
    async def create_comment(
        self, *, user_id: int, comment_in: schemas.CommentCreate
    ) -> models.Comment:
        """创建评论
        
        Args:
            user_id: 用户ID
            comment_in: 评论数据
            
        Returns:
            创建的评论
        """
        # 创建评论
        comment = crud.crud_comment.create(
            db=self.db, obj_in=comment_in, user_id=user_id
        )
        
        # 如果是回复评论，创建通知
        if comment.parent_id:
            parent_comment = crud.crud_comment.get(db=self.db, id=comment.parent_id)
            if parent_comment and parent_comment.user_id != user_id:
                # 获取用户信息
                user = crud.crud_user.get(db=self.db, id=user_id)
                
                # 创建通知
                notification_content = f"{user.nickname} 回复了你的评论"
                crud.crud_notification.create_notification(
                    db=self.db,
                    user_id=parent_comment.user_id,
                    type="reply",
                    content=notification_content,
                    related_post_id=comment.post_id,
                    related_comment_id=comment.id,
                    related_user_id=user_id
                )
        else:
            # 如果是评论帖子，创建通知
            post = crud.crud_post.get(db=self.db, id=comment.post_id)
            if post and post.user_id != user_id:
                # 获取用户信息
                user = crud.crud_user.get(db=self.db, id=user_id)
                
                # 创建通知
                notification_content = f"{user.nickname} 评论了你的帖子"
                crud.crud_notification.create_notification(
                    db=self.db,
                    user_id=post.user_id,
                    type="comment",
                    content=notification_content,
                    related_post_id=comment.post_id,
                    related_comment_id=comment.id,
                    related_user_id=user_id
                )
        
        return comment
    
    async def get_comments(
        self, *, post_id: int, skip: int = 0, limit: int = 100, current_user_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """获取帖子的评论列表
        
        Args:
            post_id: 帖子ID
            skip: 跳过的记录数
            limit: 返回的记录数
            current_user_id: 当前用户ID
            
        Returns:
            评论列表
        """
        # 获取评论总数
        total = crud.crud_comment.get_by_post_count(db=self.db, post_id=post_id)
        
        # 获取评论列表
        comments = crud.crud_comment.get_by_post(
            db=self.db, post_id=post_id, skip=skip, limit=limit
        )
        
        # 获取用户点赞的评论ID列表
        liked_comment_ids = []
        if current_user_id:
            liked_comment_ids = crud.crud_comment_like.get_comments_liked_by_user(
                db=self.db, user_id=current_user_id
            )
        
        # 构建返回数据
        items = []
        for comment in comments:
            # 获取点赞数
            like_count = crud.crud_comment_like.count_by_comment(db=self.db, comment_id=comment.id)
            
            # 获取用户是否点赞
            is_liked = comment.id in liked_comment_ids
            
            # 获取用户信息
            user = crud.crud_user.get(db=self.db, id=comment.user_id)
            
            # 获取回复列表
            replies = crud.crud_comment.get_replies(db=self.db, comment_id=comment.id)
            
            # 构建回复数据
            reply_items = []
            for reply in replies:
                # 获取回复的点赞数
                reply_like_count = crud.crud_comment_like.count_by_comment(db=self.db, comment_id=reply.id)
                
                # 获取用户是否点赞回复
                reply_is_liked = reply.id in liked_comment_ids
                
                # 获取回复用户信息
                reply_user = crud.crud_user.get(db=self.db, id=reply.user_id)
                
                # 构建回复数据
                reply_data = {
                    "id": reply.id,
                    "content": reply.content,
                    "user_id": reply.user_id,
                    "post_id": reply.post_id,
                    "parent_id": reply.parent_id,
                    "status": reply.status,
                    "reported_count": reply.reported_count,
                    "created_at": reply.created_at,
                    "updated_at": reply.updated_at,
                    "like_count": reply_like_count,
                    "is_liked_by_current_user": reply_is_liked,
                    "user": reply_user
                }
                
                reply_items.append(reply_data)
            
            # 构建评论数据
            comment_data = {
                "id": comment.id,
                "content": comment.content,
                "user_id": comment.user_id,
                "post_id": comment.post_id,
                "parent_id": comment.parent_id,
                "status": comment.status,
                "reported_count": comment.reported_count,
                "created_at": comment.created_at,
                "updated_at": comment.updated_at,
                "like_count": like_count,
                "is_liked_by_current_user": is_liked,
                "user": user,
                "replies": reply_items
            }
            
            items.append(comment_data)
        
        return {
            "total": total,
            "items": items
        }
    
    async def like_post(self, *, user_id: int, post_id: int) -> Dict[str, Any]:
        """点赞帖子
        
        Args:
            user_id: 用户ID
            post_id: 帖子ID
            
        Returns:
            点赞结果
        """
        # 检查帖子是否存在
        post = crud.crud_post.get(db=self.db, id=post_id)
        if not post:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="帖子不存在"
            )
        
        # 检查是否已点赞
        like = crud.crud_post_like.get_by_user_and_post(
            db=self.db, user_id=user_id, post_id=post_id
        )
        
        if like:
            # 已点赞，取消点赞
            crud.crud_post_like.remove_like(
                db=self.db, user_id=user_id, post_id=post_id
            )
            return {"status": "unliked"}
        else:
            # 未点赞，添加点赞
            crud.crud_post_like.create_like(
                db=self.db, user_id=user_id, post_id=post_id
            )
            
            # 如果不是自己的帖子，创建通知
            if post.user_id != user_id:
                # 获取用户信息
                user = crud.crud_user.get(db=self.db, id=user_id)
                
                # 创建通知
                notification_content = f"{user.nickname} 点赞了你的帖子"
                crud.crud_notification.create_notification(
                    db=self.db,
                    user_id=post.user_id,
                    type="like",
                    content=notification_content,
                    related_post_id=post_id,
                    related_user_id=user_id
                )
            
            return {"status": "liked"}
    
    async def like_comment(self, *, user_id: int, comment_id: int) -> Dict[str, Any]:
        """点赞评论
        
        Args:
            user_id: 用户ID
            comment_id: 评论ID
            
        Returns:
            点赞结果
        """
        # 检查评论是否存在
        comment = crud.crud_comment.get(db=self.db, id=comment_id)
        if not comment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="评论不存在"
            )
        
        # 检查是否已点赞
        like = crud.crud_comment_like.get_by_user_and_comment(
            db=self.db, user_id=user_id, comment_id=comment_id
        )
        
        if like:
            # 已点赞，取消点赞
            crud.crud_comment_like.remove_like(
                db=self.db, user_id=user_id, comment_id=comment_id
            )
            return {"status": "unliked"}
        else:
            # 未点赞，添加点赞
            crud.crud_comment_like.create_like(
                db=self.db, user_id=user_id, comment_id=comment_id
            )
            
            # 如果不是自己的评论，创建通知
            if comment.user_id != user_id:
                # 获取用户信息
                user = crud.crud_user.get(db=self.db, id=user_id)
                
                # 创建通知
                notification_content = f"{user.nickname} 点赞了你的评论"
                crud.crud_notification.create_notification(
                    db=self.db,
                    user_id=comment.user_id,
                    type="like",
                    content=notification_content,
                    related_post_id=comment.post_id,
                    related_comment_id=comment_id,
                    related_user_id=user_id
                )
            
            return {"status": "liked"}
    
    async def report_post(self, *, user_id: int, post_id: int, reason: str) -> Dict[str, Any]:
        """举报帖子
        
        Args:
            user_id: 用户ID
            post_id: 帖子ID
            reason: 举报原因
            
        Returns:
            举报结果
        """
        # 检查帖子是否存在
        post = crud.crud_post.get(db=self.db, id=post_id)
        if not post:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="帖子不存在"
            )
        
        # 创建举报
        report = crud.crud_report.create_report(
            db=self.db, reporter_id=user_id, reason=reason, post_id=post_id
        )
        
        # 增加帖子举报次数
        crud.crud_post.increment_report_count(db=self.db, id=post_id)
        
        return {"status": "reported", "report_id": report.id}
    
    async def report_comment(self, *, user_id: int, comment_id: int, reason: str) -> Dict[str, Any]:
        """举报评论
        
        Args:
            user_id: 用户ID
            comment_id: 评论ID
            reason: 举报原因
            
        Returns:
            举报结果
        """
        # 检查评论是否存在
        comment = crud.crud_comment.get(db=self.db, id=comment_id)
        if not comment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="评论不存在"
            )
        
        # 创建举报
        report = crud.crud_report.create_report(
            db=self.db, reporter_id=user_id, reason=reason, comment_id=comment_id
        )
        
        # 增加评论举报次数
        crud.crud_comment.increment_report_count(db=self.db, id=comment_id)
        
        return {"status": "reported", "report_id": report.id}
    
    async def moderate_post(self, *, post_id: int, status: str) -> Dict[str, Any]:
        """审核帖子
        
        Args:
            post_id: 帖子ID
            status: 审核状态
            
        Returns:
            审核结果
        """
        # 检查帖子是否存在
        post = crud.crud_post.get(db=self.db, id=post_id)
        if not post:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="帖子不存在"
            )
        
        # 更新帖子状态
        post = crud.crud_post.update_status(db=self.db, id=post_id, status=status)
        
        return {"status": "moderated", "post_status": post.status}
    
    async def moderate_comment(self, *, comment_id: int, status: str) -> Dict[str, Any]:
        """审核评论
        
        Args:
            comment_id: 评论ID
            status: 审核状态
            
        Returns:
            审核结果
        """
        # 检查评论是否存在
        comment = crud.crud_comment.get(db=self.db, id=comment_id)
        if not comment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="评论不存在"
            )
        
        # 更新评论状态
        comment = crud.crud_comment.update_status(db=self.db, id=comment_id, status=status)
        
        return {"status": "moderated", "comment_status": comment.status}
    
    async def get_notifications(
        self, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> Dict[str, Any]:
        """获取用户的通知列表
        
        Args:
            user_id: 用户ID
            skip: 跳过的记录数
            limit: 返回的记录数
            
        Returns:
            通知列表
        """
        # 获取通知列表
        notifications = crud.crud_notification.get_by_user(
            db=self.db, user_id=user_id, skip=skip, limit=limit
        )
        
        # 获取未读通知数
        unread_count = crud.crud_notification.get_unread_count(db=self.db, user_id=user_id)
        
        # 构建返回数据
        items = []
        for notification in notifications:
            # 获取相关用户信息
            related_user = None
            if notification.related_user_id:
                related_user = crud.crud_user.get(db=self.db, id=notification.related_user_id)
            
            # 获取相关帖子信息
            related_post = None
            if notification.related_post_id:
                related_post = crud.crud_post.get(db=self.db, id=notification.related_post_id)
            
            # 获取相关评论信息
            related_comment = None
            if notification.related_comment_id:
                related_comment = crud.crud_comment.get(db=self.db, id=notification.related_comment_id)
            
            # 构建通知数据
            notification_data = {
                "id": notification.id,
                "user_id": notification.user_id,
                "type": notification.type,
                "content": notification.content,
                "is_read": notification.is_read,
                "related_post_id": notification.related_post_id,
                "related_comment_id": notification.related_comment_id,
                "related_user_id": notification.related_user_id,
                "created_at": notification.created_at,
                "related_user": related_user,
                "related_post": related_post,
                "related_comment": related_comment
            }
            
            items.append(notification_data)
        
        return {
            "total": len(notifications),
            "unread_count": unread_count,
            "items": items
        }
    
    async def mark_notification_as_read(self, *, user_id: int, notification_id: int) -> Dict[str, Any]:
        """将通知标记为已读
        
        Args:
            user_id: 用户ID
            notification_id: 通知ID
            
        Returns:
            标记结果
        """
        # 检查通知是否存在
        notification = crud.crud_notification.get(db=self.db, id=notification_id)
        if not notification:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="通知不存在"
            )
        
        # 检查通知是否属于该用户
        if notification.user_id != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权操作此通知"
            )
        
        # 标记为已读
        notification = crud.crud_notification.mark_as_read(db=self.db, id=notification_id)
        
        return {"status": "read", "notification_id": notification.id}
    
    async def mark_all_notifications_as_read(self, *, user_id: int) -> Dict[str, Any]:
        """将用户的所有通知标记为已读
        
        Args:
            user_id: 用户ID
            
        Returns:
            标记结果
        """
        # 标记所有通知为已读
        crud.crud_notification.mark_all_as_read(db=self.db, user_id=user_id)
        
        return {"status": "all_read"}

    # Daily Workout Methods
    async def create_daily_workout(self, user_id: int, workout_in: DailyWorkoutCreate) -> DailyWorkoutWithExercises:
        workout = DailyWorkout(
            user_id=user_id,
            title=workout_in.title,
            content=workout_in.content
        )
        self.db.add(workout)
        self.db.commit()
        self.db.refresh(workout)
        return workout

    async def update_daily_workout(self, workout_id: int, user_id: int, workout_in: DailyWorkoutUpdate) -> DailyWorkoutWithExercises:
        workout = self.db.query(DailyWorkout).filter(
            DailyWorkout.id == workout_id,
            DailyWorkout.user_id == user_id,
            DailyWorkout.status == DailyWorkoutStatus.ACTIVE
        ).first()
        if not workout:
            raise HTTPException(status_code=404, detail="Daily workout not found")
        
        for field, value in workout_in.dict(exclude_unset=True).items():
            setattr(workout, field, value)
        
        workout.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(workout)
        return workout

    async def delete_daily_workout(self, workout_id: int, user_id: int) -> Dict[str, Any]:
        workout = self.db.query(DailyWorkout).filter(
            DailyWorkout.id == workout_id,
            DailyWorkout.user_id == user_id,
            DailyWorkout.status == DailyWorkoutStatus.ACTIVE
        ).first()
        if not workout:
            raise HTTPException(status_code=404, detail="Daily workout not found")
        
        workout.status = DailyWorkoutStatus.DELETED
        workout.updated_at = datetime.utcnow()
        self.db.commit()
        return {"message": "Daily workout deleted successfully"}

    async def get_daily_workout(self, workout_id: int) -> DailyWorkoutWithExercises:
        workout = self.db.query(DailyWorkout).filter(
            DailyWorkout.id == workout_id,
            DailyWorkout.status == DailyWorkoutStatus.ACTIVE
        ).first()
        if not workout:
            raise HTTPException(status_code=404, detail="Daily workout not found")
        
        # 确保status字段为字符串
        if hasattr(workout, 'status') and isinstance(workout.status, enum.Enum):
            workout.status = str(workout.status.value)
            
        return workout

    async def get_daily_workouts(self, skip: int = 0, limit: int = 100) -> List[DailyWorkoutWithExercises]:
        workouts = self.db.query(DailyWorkout).filter(
            DailyWorkout.status == DailyWorkoutStatus.ACTIVE
        ).offset(skip).limit(limit).all()
        return workouts

    async def search_daily_workouts(self, keyword: str, skip: int = 0, limit: int = 20) -> List[DailyWorkoutWithExercises]:
        workouts = self.db.query(DailyWorkout).filter(
            DailyWorkout.status == DailyWorkoutStatus.ACTIVE,
            (DailyWorkout.title.ilike(f"%{keyword}%") | DailyWorkout.content.ilike(f"%{keyword}%"))
        ).offset(skip).limit(limit).all()
        return workouts

    # Post Methods
    async def create_post(self, user_id: int, post_in: PostCreate) -> PostRead:
        post = Post(
            user_id=user_id,
            title=post_in.title,
            content=post_in.content,
            related_workout_id=post_in.related_workout_id,
            visibility=post_in.visibility
        )
        self.db.add(post)
        self.db.commit()
        self.db.refresh(post)
        return post

    async def update_post(self, post_id: int, user_id: int, post_in: PostUpdate) -> PostRead:
        post = self.db.query(Post).filter(
            Post.id == post_id,
            Post.user_id == user_id,
            Post.status == PostStatus.ACTIVE
        ).first()
        if not post:
            raise HTTPException(status_code=404, detail="Post not found")
        
        for field, value in post_in.dict(exclude_unset=True).items():
            setattr(post, field, value)
        
        post.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(post)
        return post

    async def delete_post(self, post_id: int, user_id: int) -> Dict[str, Any]:
        post = self.db.query(Post).filter(
            Post.id == post_id,
            Post.user_id == user_id,
            Post.status == PostStatus.ACTIVE
        ).first()
        if not post:
            raise HTTPException(status_code=404, detail="Post not found")
        
        post.status = PostStatus.DELETED
        post.updated_at = datetime.utcnow()
        self.db.commit()
        return {"message": "Post deleted successfully"}

    async def search_posts(self, keyword: str, skip: int = 0, limit: int = 20) -> List[PostRead]:
        posts = self.db.query(Post).filter(
            Post.status == PostStatus.ACTIVE,
            (Post.title.ilike(f"%{keyword}%") | Post.content.ilike(f"%{keyword}%"))
        ).offset(skip).limit(limit).all()
        return posts

    async def like_post(self, post_id: int, user_id: int) -> Dict[str, Any]:
        post = self.db.query(Post).filter(
            Post.id == post_id,
            Post.status == PostStatus.ACTIVE
        ).first()
        if not post:
            raise HTTPException(status_code=404, detail="Post not found")
        
        post.like_count += 1
        self.db.commit()
        
        # Create notification
        notification = Notification(
            user_id=post.user_id,
            type=NotificationType.LIKE,
            content=f"User {user_id} liked your post"
        )
        self.db.add(notification)
        self.db.commit()
        
        return {"message": "Post liked successfully"}

    async def report_post(self, post_id: int, user_id: int, reason: str) -> Dict[str, Any]:
        post = self.db.query(Post).filter(
            Post.id == post_id,
            Post.status == PostStatus.ACTIVE
        ).first()
        if not post:
            raise HTTPException(status_code=404, detail="Post not found")
        
        post.status = PostStatus.REPORTED
        post.updated_at = datetime.utcnow()
        self.db.commit()
        
        # Create notification for admin
        notification = Notification(
            user_id=1,  # Assuming admin user_id is 1
            type=NotificationType.SYSTEM,
            content=f"Post {post_id} reported by user {user_id}. Reason: {reason}"
        )
        self.db.add(notification)
        self.db.commit()
        
        return {"message": "Post reported successfully"}

    # Comment Methods
    async def create_comment(self, user_id: int, comment_in: CommentCreate) -> CommentRead:
        comment = Comment(
            user_id=user_id,
            post_id=comment_in.post_id,
            parent_id=comment_in.parent_id,
            content=comment_in.content
        )
        self.db.add(comment)
        self.db.commit()
        self.db.refresh(comment)
        
        # Update post comment count
        post = self.db.query(Post).filter(Post.id == comment_in.post_id).first()
        if post:
            post.comment_count += 1
            self.db.commit()
        
        # Create notification
        notification = Notification(
            user_id=post.user_id,
            type=NotificationType.COMMENT,
            content=f"User {user_id} commented on your post"
        )
        self.db.add(notification)
        self.db.commit()
        
        return comment

    async def update_comment(self, comment_id: int, user_id: int, content: str) -> CommentRead:
        comment = self.db.query(Comment).filter(
            Comment.id == comment_id,
            Comment.user_id == user_id,
            Comment.status == CommentStatus.ACTIVE
        ).first()
        if not comment:
            raise HTTPException(status_code=404, detail="Comment not found")
        
        comment.content = content
        comment.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(comment)
        return comment

    async def delete_comment(self, comment_id: int, user_id: int) -> Dict[str, Any]:
        comment = self.db.query(Comment).filter(
            Comment.id == comment_id,
            Comment.user_id == user_id,
            Comment.status == CommentStatus.ACTIVE
        ).first()
        if not comment:
            raise HTTPException(status_code=404, detail="Comment not found")
        
        comment.status = CommentStatus.DELETED
        comment.updated_at = datetime.utcnow()
        self.db.commit()
        
        # Update post comment count
        post = self.db.query(Post).filter(Post.id == comment.post_id).first()
        if post:
            post.comment_count -= 1
            self.db.commit()
        
        return {"message": "Comment deleted successfully"}

    async def like_comment(self, comment_id: int, user_id: int) -> Dict[str, Any]:
        comment = self.db.query(Comment).filter(
            Comment.id == comment_id,
            Comment.status == CommentStatus.ACTIVE
        ).first()
        if not comment:
            raise HTTPException(status_code=404, detail="Comment not found")
        
        comment.like_count += 1
        self.db.commit()
        
        # Create notification
        notification = Notification(
            user_id=comment.user_id,
            type=NotificationType.LIKE,
            content=f"User {user_id} liked your comment"
        )
        self.db.add(notification)
        self.db.commit()
        
        return {"message": "Comment liked successfully"}

    async def report_comment(self, comment_id: int, user_id: int, reason: str) -> Dict[str, Any]:
        comment = self.db.query(Comment).filter(
            Comment.id == comment_id,
            Comment.status == CommentStatus.ACTIVE
        ).first()
        if not comment:
            raise HTTPException(status_code=404, detail="Comment not found")
        
        comment.status = CommentStatus.REPORTED
        comment.updated_at = datetime.utcnow()
        self.db.commit()
        
        # Create notification for admin
        notification = Notification(
            user_id=1,  # Assuming admin user_id is 1
            type=NotificationType.SYSTEM,
            content=f"Comment {comment_id} reported by user {user_id}. Reason: {reason}"
        )
        self.db.add(notification)
        self.db.commit()
        
        return {"message": "Comment reported successfully"}

    # Notification Methods
    async def get_notifications(self, user_id: int, skip: int = 0, limit: int = 20) -> List[NotificationRead]:
        notifications = self.db.query(Notification).filter(
            Notification.user_id == user_id
        ).offset(skip).limit(limit).all()
        return notifications

    async def filter_notifications(self, user_id: int, type: NotificationType, skip: int = 0, limit: int = 20) -> List[NotificationRead]:
        notifications = self.db.query(Notification).filter(
            Notification.user_id == user_id,
            Notification.type == type
        ).offset(skip).limit(limit).all()
        return notifications

    async def mark_notification_as_read(self, notification_id: int, user_id: int) -> Dict[str, Any]:
        notification = self.db.query(Notification).filter(
            Notification.id == notification_id,
            Notification.user_id == user_id
        ).first()
        if not notification:
            raise HTTPException(status_code=404, detail="Notification not found")
        
        notification.is_read = True
        self.db.commit()
        return {"message": "Notification marked as read"}

    async def mark_all_notifications_as_read(self, user_id: int) -> Dict[str, Any]:
        self.db.query(Notification).filter(
            Notification.user_id == user_id,
            Notification.is_read == False
        ).update({"is_read": True})
        self.db.commit()
        return {"message": "All notifications marked as read"}

    async def delete_notification(self, notification_id: int, user_id: int) -> Dict[str, Any]:
        notification = self.db.query(Notification).filter(
            Notification.id == notification_id,
            Notification.user_id == user_id
        ).first()
        if not notification:
            raise HTTPException(status_code=404, detail="Notification not found")
        
        self.db.delete(notification)
        self.db.commit()
        return {"message": "Notification deleted successfully"}

    # User Relation Methods
    async def follow_user(self, follower_id: int, following_id: int) -> Dict[str, Any]:
        if follower_id == following_id:
            raise HTTPException(status_code=400, detail="Cannot follow yourself")
        
        existing_relation = self.db.query(UserRelation).filter(
            UserRelation.follower_id == follower_id,
            UserRelation.following_id == following_id
        ).first()
        if existing_relation:
            raise HTTPException(status_code=400, detail="Already following this user")
        
        relation = UserRelation(
            follower_id=follower_id,
            following_id=following_id
        )
        self.db.add(relation)
        self.db.commit()
        
        # Create notification
        notification = Notification(
            user_id=following_id,
            type=NotificationType.FOLLOW,
            content=f"User {follower_id} started following you"
        )
        self.db.add(notification)
        self.db.commit()
        
        return {"message": "User followed successfully"}

    async def unfollow_user(self, follower_id: int, following_id: int) -> Dict[str, Any]:
        relation = self.db.query(UserRelation).filter(
            UserRelation.follower_id == follower_id,
            UserRelation.following_id == following_id
        ).first()
        if not relation:
            raise HTTPException(status_code=404, detail="Not following this user")
        
        self.db.delete(relation)
        self.db.commit()
        return {"message": "User unfollowed successfully"}

    async def get_following(self, user_id: int, skip: int = 0, limit: int = 20) -> List[UserRelationResponse]:
        following = self.db.query(UserRelation).filter(
            UserRelation.follower_id == user_id
        ).offset(skip).limit(limit).all()
        return following

    async def get_followers(self, user_id: int, skip: int = 0, limit: int = 20) -> List[UserRelationResponse]:
        followers = self.db.query(UserRelation).filter(
            UserRelation.following_id == user_id
        ).offset(skip).limit(limit).all()
        return followers

    # Image Methods
    async def create_image(self, user_id: int, image_in: ImageCreate) -> ImageResponse:
        image = Image(
            user_id=user_id,
            post_id=image_in.post_id,
            url=image_in.url,
            title=image_in.title,
            description=image_in.description
        )
        self.db.add(image)
        self.db.commit()
        self.db.refresh(image)
        return image

    async def update_image(self, image_id: int, user_id: int, image_in: ImageUpdate) -> ImageResponse:
        image = self.db.query(Image).filter(
            Image.id == image_id,
            Image.user_id == user_id
        ).first()
        if not image:
            raise HTTPException(status_code=404, detail="Image not found")
        
        for field, value in image_in.dict(exclude_unset=True).items():
            setattr(image, field, value)
        
        image.updated_at = datetime.utcnow()
        self.db.commit()
        self.db.refresh(image)
        return image

    async def delete_image(self, image_id: int, user_id: int) -> Dict[str, Any]:
        image = self.db.query(Image).filter(
            Image.id == image_id,
            Image.user_id == user_id
        ).first()
        if not image:
            raise HTTPException(status_code=404, detail="Image not found")
        
        self.db.delete(image)
        self.db.commit()
        return {"message": "Image deleted successfully"}
