import os
import json
import shutil
import logging
import time
from datetime import datetime
from typing import Dict, Any, Optional, Union, List
import base64

logger = logging.getLogger(__name__)

class LLMLogService:
    """大模型API调用日志记录服务"""
    
    BASE_LOG_DIR = "/data/llm_logs"
    
    @staticmethod
    def ensure_directory(directory_path: str) -> str:
        """确保目录存在，如果不存在则创建"""
        if not os.path.exists(directory_path):
            os.makedirs(directory_path, exist_ok=True)
        return directory_path
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """清理文件名，移除不安全字符"""
        return ''.join(c if c.isalnum() or c in ['_', '-', '.'] else '_' for c in filename)
    
    @staticmethod
    def get_log_directory(user_id: str, meal_type: str = None) -> str:
        """获取日志目录路径"""
        # 按日期、用户ID和时间创建目录
        current_date = datetime.now().strftime("%Y-%m-%d")
        timestamp = datetime.now().strftime("%H%M%S")
        
        # 清理用户ID和餐食类型
        user_id_safe = LLMLogService.sanitize_filename(str(user_id))
        
        # 构建目录路径
        if meal_type:
            meal_type_safe = LLMLogService.sanitize_filename(str(meal_type))
            directory = f"{LLMLogService.BASE_LOG_DIR}/{current_date}/user_{user_id_safe}/{timestamp}_{meal_type_safe}"
        else:
            directory = f"{LLMLogService.BASE_LOG_DIR}/{current_date}/user_{user_id_safe}/{timestamp}"
        
        # 确保目录存在
        return LLMLogService.ensure_directory(directory)
    
    @staticmethod
    async def log_llm_call(
        user_id: str,
        request_data: Dict[str, Any],
        response_data: Dict[str, Any],
        image_path: Optional[str] = None,
        base64_image: Optional[str] = None,
        meal_type: Optional[str] = None,
        image_data: Optional[bytes] = None,
        processing_time: Optional[float] = None,
        metadata: Optional[Dict[str, Any]] = None,
        log_type: Optional[str] = None
    ) -> str:
        """记录大模型API调用日志
        
        Args:
            user_id: 用户ID
            request_data: 请求数据
            response_data: 响应数据
            image_path: 图片路径
            base64_image: base64编码的图片
            meal_type: 餐食类型
            image_data: 图片二进制数据
            processing_time: 处理时间
            metadata: 元数据
            log_type: 日志类型，例如 'raw_response' 或 'processed_response'
        """
        try:
            # 获取日志目录
            log_dir = LLMLogService.get_log_directory(user_id, meal_type)
            
            # 如果指定了日志类型，添加到日志目录名
            if log_type:
                # 清理日志类型名
                log_type_safe = LLMLogService.sanitize_filename(log_type)
                # 重命名日志目录，添加日志类型后缀
                new_log_dir = f"{log_dir}_{log_type_safe}"
                log_dir = LLMLogService.ensure_directory(new_log_dir)
            
            # 保存请求数据
            with open(f"{log_dir}/request.json", "w", encoding="utf-8") as f:
                # 移除base64图像数据，避免文件过大
                request_copy = request_data.copy() if request_data else {}
                if 'messages' in request_copy:
                    # 处理OpenAI格式的请求
                    for msg in request_copy.get('messages', []):
                        if isinstance(msg, dict) and 'content' in msg:
                            if isinstance(msg['content'], list):
                                for item in msg['content']:
                                    if isinstance(item, dict) and 'image_url' in item:
                                        # 替换base64数据为标记
                                        if isinstance(item['image_url'], dict) and 'url' in item['image_url']:
                                            if item['image_url']['url'].startswith('data:image'):
                                                item['image_url']['url'] = '[BASE64_IMAGE_REMOVED]'
                
                # 移除其他可能包含base64图像的字段
                if 'base64_image' in request_copy:
                    request_copy['base64_image'] = '[BASE64_IMAGE_REMOVED]'
                
                json.dump(request_copy, f, ensure_ascii=False, indent=2)
            
            # 保存响应数据
            with open(f"{log_dir}/response.json", "w", encoding="utf-8") as f:
                # 确保response_data是可序列化的
                response_copy = response_data.copy() if response_data else {}
                
                # 检查并处理可能导致序列化问题的字段
                if 'raw_content' in response_copy and isinstance(response_copy['raw_content'], str) and len(response_copy['raw_content']) > 10000:
                    # 如果原始内容太长，截断它
                    response_copy['raw_content'] = response_copy['raw_content'][:10000] + "... [截断]"
                
                # 处理特殊的数据结构
                if 'processed_result' in response_copy and isinstance(response_copy['processed_result'], dict):
                    processed = response_copy['processed_result']
                    # 如果food_items是对象列表，转换为简单数据
                    if 'food_items' in processed:
                        if isinstance(processed['food_items'], (int, float)):
                            # 如果food_items是数字（可能是food_items_count），转换为描述字符串
                            processed['food_items'] = f"[{processed['food_items']}个食物项]"
                        elif hasattr(processed['food_items'], '__iter__') and not isinstance(processed['food_items'], (str, bool, int, float)):
                            try:
                                # 尝试转换为简单列表
                                processed['food_items'] = [
                                    {'name': item.get('name', '未知食物') if isinstance(item, dict) else str(item)}
                                    for item in processed['food_items']
                                ]
                            except Exception as e:
                                # 如果转换失败，尝试获取长度
                                try:
                                    count = len(processed['food_items'])
                                    processed['food_items'] = f"[{count}个食物项]"
                                except Exception:
                                    # 如果无法获取长度，使用通用描述
                                    processed['food_items'] = "[食物项列表]"
                    
                    # 同样处理matched_foods_count字段
                    if 'matched_foods_count' in processed and isinstance(processed['matched_foods_count'], (int, float)):
                        processed['matched_foods'] = f"[{processed['matched_foods_count']}个匹配食物]"
                        # 可以移除count字段避免冗余
                        processed.pop('matched_foods_count', None)
                
                # 如果直接在response_data中有food_items字段，也需要处理
                if 'food_items' in response_copy:
                    if isinstance(response_copy['food_items'], (int, float)):
                        response_copy['food_items'] = f"[{response_copy['food_items']}个食物项]"
                    elif not isinstance(response_copy['food_items'], (str, int, float, bool)) and hasattr(response_copy['food_items'], '__iter__'):
                        try:
                            # 尝试转换为简单列表
                            response_copy['food_items'] = [
                                {'name': item.get('name', '未知食物') if isinstance(item, dict) else str(item)}
                                for item in response_copy['food_items']
                            ]
                        except Exception as e:
                            # 如果转换失败，尝试获取长度
                            try:
                                count = len(response_copy['food_items'])
                                response_copy['food_items'] = f"[{count}个食物项]"
                            except Exception:
                                # 如果无法获取长度，使用通用描述
                                response_copy['food_items'] = "[食物项列表]"
                
                # 处理matched_foods字段
                if 'matched_foods' in response_copy:
                    if isinstance(response_copy['matched_foods'], (int, float)):
                        response_copy['matched_foods'] = f"[{response_copy['matched_foods']}个匹配食物]"
                    elif not isinstance(response_copy['matched_foods'], (str, int, float, bool)) and hasattr(response_copy['matched_foods'], '__iter__'):
                        try:
                            # 尝试转换为简单列表
                            response_copy['matched_foods'] = [
                                {'name': item.get('name', '未知食物') if isinstance(item, dict) else str(item)}
                                for item in response_copy['matched_foods']
                            ]
                        except Exception as e:
                            # 如果转换失败，使用简单描述
                            try:
                                count = len(response_copy['matched_foods'])
                                response_copy['matched_foods'] = f"[{count}个匹配食物]"
                            except Exception:
                                response_copy['matched_foods'] = "[匹配食物列表]"
                
                # 确保可以序列化
                try:
                    json.dump(response_copy, f, ensure_ascii=False, indent=2)
                except TypeError as e:
                    # 如果序列化失败，记录错误，并保存简化版本
                    logger.error(f"响应数据序列化失败: {str(e)}，将保存简化版本")
                    simplified_response = {"error": "原始响应无法序列化", "error_message": str(e)}
                    if 'raw_content' in response_copy and isinstance(response_copy['raw_content'], str):
                        # 保留部分原始内容
                        simplified_response['raw_content_preview'] = response_copy['raw_content'][:1000]
                    json.dump(simplified_response, f, ensure_ascii=False, indent=2)
            
            # 保存图片信息
            image_info = {
                "original_path": image_path,
                "has_base64": bool(base64_image),
                "has_image_data": bool(image_data),
                "log_type": log_type
            }
            
            with open(f"{log_dir}/image_info.json", "w", encoding="utf-8") as f:
                json.dump(image_info, f, ensure_ascii=False, indent=2)
            
            # 保存图片副本(如果提供了图片数据)
            if image_data:
                with open(f"{log_dir}/request_image.jpg", "wb") as f:
                    f.write(image_data)
            elif base64_image:
                try:
                    # 尝试从base64保存图片
                    if ',' in base64_image:
                        base64_data = base64_image.split(',', 1)[1]
                    else:
                        base64_data = base64_image
                    
                    image_bytes = base64.b64decode(base64_data)
                    with open(f"{log_dir}/request_image.jpg", "wb") as f:
                        f.write(image_bytes)
                except Exception as e:
                    logger.warning(f"无法从base64保存图片: {str(e)}")
            elif image_path and os.path.exists(image_path):
                # 复制原始图片(如果存在)
                try:
                    shutil.copy2(image_path, f"{log_dir}/request_image.jpg")
                except Exception as e:
                    logger.warning(f"无法复制原始图片: {str(e)}")
            
            # 保存元数据
            meta = metadata or {}
            meta.update({
                "timestamp": datetime.now().isoformat(),
                "user_id": user_id,
                "meal_type": meal_type,
                "processing_time": processing_time,
                "log_directory": log_dir,
                "log_type": log_type
            })
            
            with open(f"{log_dir}/metadata.json", "w", encoding="utf-8") as f:
                json.dump(meta, f, ensure_ascii=False, indent=2)
                
            # 生成问答对
            qa_pairs = LLMLogService.generate_qa_pairs(request_data, response_data, meal_type)
            with open(f"{log_dir}/qa_pairs.json", "w", encoding="utf-8") as f:
                json.dump(qa_pairs, f, ensure_ascii=False, indent=2)
            
            logger.info(f"已记录大模型调用日志: {log_dir}")
            return log_dir
            
        except Exception as e:
            logger.error(f"记录大模型调用日志失败: {str(e)}")
            return ""
    
    @staticmethod
    def generate_qa_pairs(
        request_data: Dict[str, Any],
        response_data: Dict[str, Any],
        meal_type: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """从请求和响应数据生成问答对"""
        qa_pairs = []
        
        try:
            # 生成问题 - 这里实现与具体的API格式相关
            question = "分析这张图片中的食物及营养成分"
            if meal_type:
                question = f"分析这张{meal_type}图片中的食物及营养成分"
            
            # 提取回答 - 从响应中提取关键信息
            answer_parts = []
            
            # 安全处理food_items
            try:
                # 尝试从各种可能的位置提取food_items
                food_items = None
                
                # 直接检查response_data中的food_items
                if 'food_items' in response_data:
                    food_items = response_data['food_items']
                # 检查processed_result中的food_items
                elif 'processed_result' in response_data and isinstance(response_data['processed_result'], dict):
                    processed = response_data['processed_result']
                    if 'food_items' in processed:
                        food_items = processed['food_items']
                    elif 'food_items_count' in processed:
                        food_items = processed['food_items_count']
                
                # 如果找到food_items，安全处理它
                if food_items is not None:
                    # 先检查food_items是否为整数类型
                    if isinstance(food_items, (int, float)):
                        # 如果是数字，可能是食物项数量
                        answer_parts.append(f"识别出 {int(food_items)} 个食物项")
                    # 再检查是否是可迭代对象，排除字符串、数字和布尔值
                    elif not isinstance(food_items, (str, int, float, bool)) and hasattr(food_items, '__iter__'):
                        try:
                            food_names = []
                            # 转换为列表以避免多次迭代
                            items_list = list(food_items)
                            
                            for item in items_list:
                                if isinstance(item, dict):
                                    food_names.append(item.get('name', '未知食物'))
                                elif hasattr(item, 'name'):
                                    food_names.append(item.name)
                                elif hasattr(item, 'dict') and callable(getattr(item, 'dict')):
                                    try:
                                        item_dict = item.dict()
                                        food_names.append(item_dict.get('name', '未知食物'))
                                    except:
                                        food_names.append('未知食物')
                                else:
                                    food_names.append(str(item))
                            
                            if food_names:
                                answer_parts.append(f"识别出的食物: {', '.join(food_names)}")
                            else:
                                answer_parts.append("未识别出任何食物")
                        except Exception as e:
                            logger.error(f"处理food_items列表时出错: {str(e)}")
                            answer_parts.append("识别出的食物: 数据处理错误")
                    elif isinstance(food_items, str):
                        # 如果是字符串，可能是单个食物或已格式化的字符串
                        answer_parts.append(f"识别出的食物: {food_items}")
                    else:
                        # 其他类型，记录错误
                        logger.error(f"food_items类型不支持: {type(food_items)}")
                        answer_parts.append("食物识别结果格式异常")
                else:
                    # 如果没有找到food_items
                    answer_parts.append("未找到食物识别结果")
            except Exception as e:
                logger.error(f"处理food_items时出错: {str(e)}")
                answer_parts.append("识别出的食物: 处理错误")
            
            # 安全提取总营养信息
            try:
                # 尝试从各种位置提取nutrition_totals
                nutrition = None
                
                if 'nutrition_totals' in response_data:
                    nutrition = response_data['nutrition_totals']
                elif 'processed_result' in response_data and isinstance(response_data['processed_result'], dict):
                    processed = response_data['processed_result']
                    if 'nutrition_totals' in processed:
                        nutrition = processed['nutrition_totals']
                
                if nutrition and isinstance(nutrition, dict):
                    calories = nutrition.get('total_calory', 0)
                    protein = nutrition.get('total_protein', 0)
                    fat = nutrition.get('total_fat', 0)
                    carbs = nutrition.get('total_carbohydrate', 0)
                    answer_parts.append(f"总热量: {calories}千卡, 蛋白质: {protein}g, 脂肪: {fat}g, 碳水化合物: {carbs}g")
            except Exception as e:
                logger.error(f"处理营养信息时出错: {str(e)}")
                answer_parts.append("营养信息: 处理错误")
            
            # 安全提取健康建议
            try:
                health_rec = None
                
                if 'health_recommendation' in response_data:
                    health_rec = response_data['health_recommendation']
                elif 'processed_result' in response_data and isinstance(response_data['processed_result'], dict):
                    processed = response_data['processed_result']
                    if 'health_recommendation' in processed:
                        health_rec = processed['health_recommendation']
                
                if health_rec and isinstance(health_rec, str):
                    answer_parts.append(f"健康建议: {health_rec}")
            except Exception as e:
                logger.error(f"处理健康建议时出错: {str(e)}")
            
            # 组装回答
            if answer_parts:
                answer = "\n".join(answer_parts)
            else:
                answer = "无法生成食物识别结果摘要"
            
            # 将问答对添加到结果中
            qa_pairs.append({
                "question": question,
                "answer": answer,
                "timestamp": datetime.now().isoformat(),
                "meal_type": meal_type
            })
            
        except Exception as e:
            logger.error(f"生成问答对时出错: {str(e)}")
            # 添加一个基本问答对，确保返回值有效
            qa_pairs.append({
                "question": "分析食物图片",
                "answer": f"处理食物识别结果时出错: {str(e)}",
                "timestamp": datetime.now().isoformat(),
                "meal_type": meal_type
            })
        
        return qa_pairs
    
    @staticmethod
    def get_logs_by_date(date_str: str) -> List[Dict[str, Any]]:
        """获取指定日期的所有日志信息"""
        try:
            date_dir = f"{LLMLogService.BASE_LOG_DIR}/{date_str}"
            if not os.path.exists(date_dir):
                return []
                
            result = []
            # 遍历用户目录
            for user_dir in os.listdir(date_dir):
                user_path = os.path.join(date_dir, user_dir)
                if os.path.isdir(user_path):
                    # 遍历时间目录
                    for time_dir in os.listdir(user_path):
                        log_dir = os.path.join(user_path, time_dir)
                        if os.path.isdir(log_dir):
                            # 读取元数据
                            metadata_path = os.path.join(log_dir, "metadata.json")
                            if os.path.exists(metadata_path):
                                try:
                                    with open(metadata_path, "r", encoding="utf-8") as f:
                                        metadata = json.load(f)
                                    result.append({
                                        "log_directory": log_dir,
                                        "metadata": metadata,
                                        "date": date_str
                                    })
                                except Exception as e:
                                    logger.error(f"读取日志元数据失败: {str(e)}")
            
            return result
        except Exception as e:
            logger.error(f"获取日期日志失败: {str(e)}")
            return []
    
    @staticmethod
    def get_logs_by_user(user_id: str, start_date: Optional[str] = None, end_date: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取指定用户的所有日志信息"""
        try:
            # 确定日期范围
            if not start_date:
                # 如果没有指定开始日期，使用一个月前的日期
                start_date = (datetime.now() - datetime.timedelta(days=30)).strftime("%Y-%m-%d")
            
            if not end_date:
                # 如果没有指定结束日期，使用今天
                end_date = datetime.now().strftime("%Y-%m-%d")
            
            # 生成日期列表
            start_dt = datetime.strptime(start_date, "%Y-%m-%d")
            end_dt = datetime.strptime(end_date, "%Y-%m-%d")
            date_list = []
            
            current_dt = start_dt
            while current_dt <= end_dt:
                date_list.append(current_dt.strftime("%Y-%m-%d"))
                current_dt = current_dt + datetime.timedelta(days=1)
            
            result = []
            
            # 按日期获取日志
            for date_str in date_list:
                date_dir = f"{LLMLogService.BASE_LOG_DIR}/{date_str}"
                if os.path.exists(date_dir):
                    # 查找用户目录
                    user_safe = LLMLogService.sanitize_filename(str(user_id))
                    user_dir = f"user_{user_safe}"
                    user_path = os.path.join(date_dir, user_dir)
                    
                    if os.path.exists(user_path) and os.path.isdir(user_path):
                        # 遍历时间目录
                        for time_dir in os.listdir(user_path):
                            log_dir = os.path.join(user_path, time_dir)
                            if os.path.isdir(log_dir):
                                # 读取元数据
                                metadata_path = os.path.join(log_dir, "metadata.json")
                                if os.path.exists(metadata_path):
                                    try:
                                        with open(metadata_path, "r", encoding="utf-8") as f:
                                            metadata = json.load(f)
                                        result.append({
                                            "log_directory": log_dir,
                                            "metadata": metadata,
                                            "date": date_str
                                        })
                                    except Exception as e:
                                        logger.error(f"读取日志元数据失败: {str(e)}")
            
            return result
        except Exception as e:
            logger.error(f"获取用户日志失败: {str(e)}")
            return []
    
    @staticmethod
    def get_log_details(log_directory: str) -> Dict[str, Any]:
        """获取指定日志目录的详细信息"""
        try:
            result = {"log_directory": log_directory}
            
            # 读取元数据
            metadata_path = os.path.join(log_directory, "metadata.json")
            if os.path.exists(metadata_path):
                with open(metadata_path, "r", encoding="utf-8") as f:
                    result["metadata"] = json.load(f)
            
            # 读取请求数据
            request_path = os.path.join(log_directory, "request.json")
            if os.path.exists(request_path):
                with open(request_path, "r", encoding="utf-8") as f:
                    result["request"] = json.load(f)
            
            # 读取响应数据
            response_path = os.path.join(log_directory, "response.json")
            if os.path.exists(response_path):
                with open(response_path, "r", encoding="utf-8") as f:
                    result["response"] = json.load(f)
            
            # 读取问答对
            qa_path = os.path.join(log_directory, "qa_pairs.json")
            if os.path.exists(qa_path):
                with open(qa_path, "r", encoding="utf-8") as f:
                    result["qa_pairs"] = json.load(f)
            
            return result
        except Exception as e:
            logger.error(f"获取日志详情失败: {str(e)}")
            return {"error": str(e)}
    
    @staticmethod
    def clean_old_logs(days_to_keep: int = 30) -> int:
        """清理旧日志，保留指定天数的日志"""
        try:
            # 获取当前日期
            current_date = datetime.now()
            
            # 获取所有日期目录
            removed_count = 0
            
            if os.path.exists(LLMLogService.BASE_LOG_DIR):
                for date_dir in os.listdir(LLMLogService.BASE_LOG_DIR):
                    try:
                        # 解析日期
                        log_date = datetime.strptime(date_dir, "%Y-%m-%d")
                        
                        # 计算日期差
                        days_diff = (current_date - log_date).days
                        
                        # 如果超过保留天数，删除目录
                        if days_diff > days_to_keep:
                            dir_path = os.path.join(LLMLogService.BASE_LOG_DIR, date_dir)
                            if os.path.isdir(dir_path):
                                shutil.rmtree(dir_path)
                                removed_count += 1
                                logger.info(f"已删除旧日志目录: {dir_path}")
                    except Exception as e:
                        logger.error(f"处理日期目录失败: {str(e)}")
                        continue
            
            return removed_count
        except Exception as e:
            logger.error(f"清理旧日志失败: {str(e)}")
            return 0 