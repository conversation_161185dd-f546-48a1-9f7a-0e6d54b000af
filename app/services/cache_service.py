import json
import redis
from app.core.config import settings
import logging
import time
from typing import Optional, Dict, Any, List, Union
from functools import wraps

logger = logging.getLogger(__name__)

def with_redis_error_handling(fallback_value=None):
    """Redis操作错误处理装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            try:
                # 检查Redis连接
                if not self.ensure_connection():
                    logger.warning(f"Redis不可用，使用降级策略: {func.__name__}")
                    return fallback_value
                
                # 执行Redis操作
                return func(self, *args, **kwargs)
            except redis.RedisError as e:
                logger.error(f"Redis操作失败: {func.__name__}, 错误: {str(e)}")
                # 标记连接为失败，这样下次调用会尝试重新连接
                self._connection_active = False
                return fallback_value
            except Exception as e:
                logger.error(f"缓存操作发生未预期的错误: {func.__name__}, 错误: {str(e)}")
                return fallback_value
        return wrapper
    return decorator

class CacheService:
    def __init__(self):
        self.redis = None
        self.ttl = 3600  # 默认缓存1小时
        self._last_connection_attempt = 0
        self._connection_active = False
        self._reconnect_interval = 5  # 重连间隔（秒）
        self._max_retries = 3  # 最大重试次数
        
        # 初始化连接
        self._initialize_connection()
        
        # 内存缓存作为降级策略
        self._memory_cache = {}
    
    def _initialize_connection(self):
        """初始化Redis连接"""
        try:
            self.redis = redis.Redis(
                host=settings.REDIS_HOST,
                port=settings.REDIS_PORT,
                db=settings.REDIS_DB,
                password=settings.REDIS_PASSWORD,
                decode_responses=True,
                socket_timeout=3,  # 减少socket超时时间
                socket_connect_timeout=3,  # 减少连接超时时间
                retry_on_timeout=True  # 超时时自动重试
            )
            # 测试连接
            self.redis.ping()
            self._connection_active = True
            logger.info(f"Redis缓存服务初始化成功: {settings.REDIS_HOST}:{settings.REDIS_PORT}")
        except Exception as e:
            logger.error(f"Redis缓存服务初始化失败: {str(e)}")
            self.redis = None
            self._connection_active = False
    
    def ensure_connection(self) -> bool:
        """确保Redis连接可用，如果断开则尝试重连"""
        # 如果连接正常，快速返回
        if self._connection_active and self.redis:
            return True
            
        # 避免频繁重连
        current_time = time.time()
        if current_time - self._last_connection_attempt < self._reconnect_interval:
            return False
            
        self._last_connection_attempt = current_time
        
        # 尝试重新连接
        for attempt in range(self._max_retries):
            try:
                if self.redis is None:
                    self._initialize_connection()
                else:
                    # 测试连接是否可用
                    self.redis.ping()
                    self._connection_active = True
                    logger.info(f"Redis连接恢复正常（尝试{attempt+1}/{self._max_retries}）")
                    return True
            except Exception as e:
                logger.warning(f"Redis重连失败（尝试{attempt+1}/{self._max_retries}）: {str(e)}")
                time.sleep(0.5)  # 短暂延迟后重试
                self._connection_active = False
        
            return False
    
    def is_available(self) -> bool:
        """检查Redis是否可用"""
        return self.ensure_connection()
    
    @with_redis_error_handling(fallback_value=None)
    def get_conversation_messages(self, session_id: str, limit: int = 5) -> Optional[List[Dict[str, Any]]]:
        """从缓存获取会话消息"""
        cache_key = f"chat:messages:{session_id}"
        if self.is_available():
            cached = self.redis.lrange(cache_key, 0, limit-1)
            if cached:
                return [json.loads(msg) for msg in cached]
        
        # 如果Redis中没有，尝试从内存缓存获取
        memory_key = f"memory:{cache_key}"
        if memory_key in self._memory_cache:
            return self._memory_cache[memory_key][:limit]
        
        return None
    
    @with_redis_error_handling()
    def cache_message(self, session_id: str, message_data: Dict[str, Any]) -> None:
        """缓存新消息"""
        cache_key = f"chat:messages:{session_id}"
        memory_key = f"memory:{cache_key}"
            
        # 同时更新Redis和内存缓存
        if self.is_available():
            self.redis.lpush(cache_key, json.dumps(message_data))
            self.redis.ltrim(cache_key, 0, 99)  # 只保留最新的100条消息
            self.redis.expire(cache_key, self.ttl)
        
        # 更新内存缓存
        if memory_key not in self._memory_cache:
            self._memory_cache[memory_key] = []
        self._memory_cache[memory_key].insert(0, message_data)
        if len(self._memory_cache[memory_key]) > 100:
            self._memory_cache[memory_key] = self._memory_cache[memory_key][:100]
    
    @with_redis_error_handling(fallback_value=None)
    def get_cache(self, key: str) -> Optional[str]:
        """获取缓存值"""
        return self.redis.get(key)
    
    @with_redis_error_handling()
    def set_cache(self, key: str, value: str, expire: int = None) -> None:
        """设置缓存值"""
        if expire is None:
            expire = self.ttl
        try:
            self.redis.setex(key, expire, value)
        except redis.RedisError as e:
            if "read only" in str(e).lower():
                logger.warning(f"Redis处于只读模式，无法写入缓存: {key}")
                # 仅更新内存缓存作为降级策略
                memory_key = f"memory:{key}"
                self._memory_cache[memory_key] = value
            else:
                # 重新抛出其他类型的Redis错误，让装饰器处理
                raise
        
        # 同时更新内存缓存
        memory_key = f"memory:{key}"
        self._memory_cache[memory_key] = value
    
    def clear_memory_cache(self) -> None:
        """清理内存缓存"""
        self._memory_cache.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存状态信息"""
        stats = {
            "is_available": self.is_available(),
            "memory_cache_size": len(self._memory_cache),
            "connection_active": self._connection_active
        }
        
        if self.is_available():
            try:
                # 添加Redis服务器信息
                info = self.redis.info()
                stats.update({
                    "redis_version": info.get("redis_version", "unknown"),
                    "connected_clients": info.get("connected_clients", 0),
                    "used_memory_human": info.get("used_memory_human", "unknown"),
                    "total_connections_received": info.get("total_connections_received", 0)
                })
            except Exception as e:
                logger.error(f"获取Redis状态信息失败: {str(e)}")
        
        return stats

# 创建单例实例
cache_service = CacheService() 