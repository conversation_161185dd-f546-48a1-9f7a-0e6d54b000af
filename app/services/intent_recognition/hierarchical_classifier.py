"""
分层意图分类器

实现三层架构的意图分类系统：意图大类识别、具体意图识别、参数提取
"""

import logging
from typing import Dict, Any, List, Optional, Tuple, Union
from app.services.intent_recognition.models import (
    IntentData, INTENT_CATEGORIES, SUPPORTED_INTENTS
)

logger = logging.getLogger(__name__)

class HierarchicalIntentClassifier:
    """分层意图分类器"""
    
    def __init__(self, model_service):
        """初始化分层意图分类器
        
        Args:
            model_service: 模型服务实例
        """
        self.model_service = model_service
        
        # 意图大类定义
        self.intent_categories = INTENT_CATEGORIES
        
        # 反向映射：从具体意图到大类
        self.intent_to_category = {}
        for category, intents in self.intent_categories.items():
            for intent in intents:
                self.intent_to_category[intent] = category
        
        logger.info("分层意图分类器初始化完成")
    
    async def classify(self, text: str, context: Optional[Dict[str, Any]] = None) -> Tuple[str, str, Dict[str, Any], float]:
        """分层分类用户意图
        
        Args:
            text: 用户输入文本
            context: 上下文信息
            
        Returns:
            (意图大类, 具体意图, 参数字典, 置信度)
        """
        # 第一层：意图大类识别
        category, category_confidence = await self._classify_category(text, context)
        
        # 第二层：具体意图识别
        intent, intent_confidence = await self._classify_intent(text, category, context)
        
        # 第三层：参数提取
        parameters = await self._extract_parameters(text, intent, context)
        
        # 计算综合置信度
        confidence = (category_confidence + intent_confidence) / 2
        
        logger.info(f"分层意图分类结果: 大类={category}, 意图={intent}, 置信度={confidence:.2f}")
        
        return category, intent, parameters, confidence
    
    async def _classify_category(self, text: str, context: Optional[Dict[str, Any]] = None) -> Tuple[str, float]:
        """识别意图大类
        
        Args:
            text: 用户输入文本
            context: 上下文信息
            
        Returns:
            (意图大类, 置信度)
        """
        # 构建提示词
        prompt = self._build_category_prompt(text, context)
        
        try:
            # 调用通义千问意图识别模型
            response = await self.model_service.get_response(
                messages=[{"role": "user", "content": prompt}],
                model_type="intent_recognition",
                streaming=False
            )
            
            # 解析响应
            category = response.strip()
            # 验证类别是否有效
            if category in self.intent_categories:
                return category, 0.9
            else:
                # 默认返回general类别
                logger.warning(f"未知意图大类: {category}，默认为general")
                return "general", 0.7
        except Exception as e:
            # 错误处理
            logger.error(f"意图大类识别失败: {str(e)}")
            return "general", 0.6
    
    async def _classify_intent(self, text: str, category: str, context: Optional[Dict[str, Any]] = None) -> Tuple[str, float]:
        """在给定大类的情况下识别具体意图
        
        Args:
            text: 用户输入文本
            category: 意图大类
            context: 上下文信息
            
        Returns:
            (具体意图, 置信度)
        """
        # 获取该类别下的所有可能意图
        possible_intents = self.intent_categories.get(category, ["general_chat"])
        
        # 构建提示词
        prompt = self._build_intent_prompt(text, possible_intents, context)
        
        try:
            # 调用通义千问意图识别模型
            response = await self.model_service.get_response(
                messages=[{"role": "user", "content": prompt}],
                model_type="intent_recognition",
                streaming=False
            )
            
            # 解析响应
            intent = response.strip()
            # 验证意图是否在可能的意图列表中
            if intent in possible_intents:
                return intent, 0.9
            else:
                # 默认返回该类别的第一个意图
                logger.warning(f"未知具体意图: {intent}，默认为{possible_intents[0]}")
                return possible_intents[0], 0.7
        except Exception as e:
            # 错误处理
            logger.error(f"具体意图识别失败: {str(e)}")
            return possible_intents[0], 0.6
    
    async def _extract_parameters(self, text: str, intent: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """提取参数
        
        Args:
            text: 用户输入文本
            intent: 识别的意图
            context: 上下文信息
            
        Returns:
            参数字典
        """
        # 这里可以调用参数提取器，但为了简化，我们返回一个空字典
        # 实际实现中，应该使用ParameterExtractor类提取参数
        return {}
    
    def _build_category_prompt(self, text: str, context: Optional[Dict[str, Any]] = None) -> str:
        """构建意图大类识别提示词
        
        Args:
            text: 用户输入文本
            context: 上下文信息
            
        Returns:
            提示词
        """
        prompt = f"""你是一个健身对话系统的意图分类组件。
        请判断以下用户输入属于哪个大类：
        
        - training: 与训练计划、训练动作相关的请求
        - nutrition: 与饮食、营养相关的请求
        - fitness_qa: 与健身知识、体脂计算、卡路里计算相关的请求
        - general: 一般聊天或其他不属于上述类别的请求
        
        用户输入: {text}
        
        请只回复一个类别名称，不要有任何其他内容。"""
        
        return prompt
    
    def _build_intent_prompt(self, text: str, possible_intents: List[str], context: Optional[Dict[str, Any]] = None) -> str:
        """构建具体意图识别提示词
        
        Args:
            text: 用户输入文本
            possible_intents: 可能的意图列表
            context: 上下文信息
            
        Returns:
            提示词
        """
        # 构建意图描述
        intent_descriptions = {
            "daily_workout_plan": "制定单日训练计划",
            "weekly_training_plan": "制定每周训练计划",
            "search_exercise": "查询健身动作信息",
            "recommend_exercise": "推荐训练动作",
            "diet_advice": "饮食建议",
            "fitness_qa": "健身问答",
            "body_fat_calculation": "体脂计算",
            "calorie_calculation": "卡路里计算",
            "general_chat": "一般聊天",
            "discuss_training_plan": "讨论已有训练计划"
        }
        
        # 构建可能意图的描述列表
        intent_options = "\n".join([f"- {intent}: {intent_descriptions.get(intent, '')}" for intent in possible_intents])
        
        prompt = f"""你是一个健身对话系统的意图识别组件。
        请判断以下用户输入最可能的意图类型：
        
        {intent_options}
        
        用户输入: {text}
        
        请只回复一个意图类型，不要有任何其他内容。"""
        
        return prompt
