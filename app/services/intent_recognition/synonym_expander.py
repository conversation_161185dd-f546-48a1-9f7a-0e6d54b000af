"""
同义词扩展系统

提供同义词扩展功能，支持模糊匹配和同义词识别
"""

import logging
from typing import List, Dict, Any, Optional
from difflib import SequenceMatcher

logger = logging.getLogger(__name__)

def normalize_text(text: str) -> str:
    """将输入文本标准化（去除空格，转为小写）
    
    Args:
        text: 输入文本
        
    Returns:
        标准化后的文本
    """
    if not text:
        return ""
    return text.strip().lower()

class SynonymExpander:
    """同义词扩展系统"""
    
    def __init__(self):
        """初始化同义词扩展系统"""
        # 初始化同义词字典
        self.synonym_dict = {
            # 身体部位同义词
            "胸部": ["胸", "胸肌", "上胸", "中胸", "下胸", "胸大肌"],
            "背部": ["背", "背肌", "上背", "下背", "背阔肌", "斜方肌"],
            "腿部": ["腿", "大腿", "腿肌", "股四头肌", "股二头肌", "小腿"],
            "肩部": ["肩", "肩膀", "三角肌", "肩袖"],
            "手臂": ["臂", "上臂", "二头肌", "三头肌", "肱二头肌", "肱三头肌"],
            "腰腹部": ["腹", "腹肌", "腹直肌", "腹外斜肌", "核心", "腰部"],
            "二头": ["二头肌", "肱二头肌", "肱肌", "肱桡肌"],
            "三头": ["三头肌", "肱三头肌"],
            "臀部": ["臀", "臀肌", "臀大肌", "臀中肌", "臀小肌"],
            "小腿": ["腓肠肌", "比目鱼肌", "胫骨肌"],
            "颈部": ["颈", "脖子", "颈椎"],
            "全身": ["整体", "全部", "综合"],
            
            # 训练器械同义词
            "杠铃": ["杠", "奥杆", "奥林匹克杠铃", "杠铃杆"],
            "哑铃": ["铃", "小哑铃", "重量哑铃"],
            "自重": ["徒手", "无器械", "体重", "家庭训练"],
            "固定器械": ["器械", "健身器材", "固定器材", "机器"],
            "史密斯": ["史密斯机", "史密斯架", "smith"],
            "阻力带": ["弹力带", "拉力带", "健身带"],
            "壶铃": ["kettlebell", "壶铃球"],
            "TRX": ["悬挂训练", "悬吊训练"],
            
            # 训练场景同义词
            "居家": ["家里", "家中", "在家", "家庭", "无器械环境"],
            "健身房": ["健身中心", "健身会所", "器械环境", "专业环境"],
            
            # 训练目标同义词
            "增肌": ["增长肌肉", "肌肉增长", "肌肉生长", "肌肥大", "肌肉增大"],
            "减脂": ["减肥", "瘦身", "减重", "燃脂", "减掉脂肪"],
            "力量": ["力量增长", "变强", "增强力量", "爆发力"],
            "耐力": ["持久力", "心肺", "有氧能力", "心肺功能"],
            "灵活性": ["柔韧性", "伸展性", "关节活动度"]
        }
        
        # 反向映射：从同义词到主术语
        self.reverse_mapping = {}
        for main_term, synonyms in self.synonym_dict.items():
            for synonym in synonyms:
                self.reverse_mapping[normalize_text(synonym)] = main_term
        
        logger.info("同义词扩展系统初始化完成")
    
    def expand(self, term: str) -> List[str]:
        """扩展术语为同义词列表
        
        Args:
            term: 原始术语
            
        Returns:
            同义词列表，包括原始术语
        """
        # 标准化输入
        normalized_term = normalize_text(term)
        
        # 查找同义词
        if normalized_term in self.synonym_dict:
            # 如果是主术语，返回它和所有同义词
            return [term] + self.synonym_dict[normalized_term]
        elif normalized_term in self.reverse_mapping:
            # 如果是同义词，返回主术语和所有同义词
            main_term = self.reverse_mapping[normalized_term]
            return [main_term] + self.synonym_dict[main_term]
        
        # 如果没有找到精确匹配，尝试模糊匹配
        best_match = None
        best_score = 0
        
        # 先检查主术语
        for main_term in self.synonym_dict.keys():
            score = SequenceMatcher(None, normalized_term, normalize_text(main_term)).ratio()
            if score > best_score and score >= 0.8:  # 80%相似度阈值
                best_match = main_term
                best_score = score
        
        # 如果没有找到匹配的主术语，检查同义词
        if not best_match:
            for synonym, main_term in self.reverse_mapping.items():
                score = SequenceMatcher(None, normalized_term, synonym).ratio()
                if score > best_score and score >= 0.8:  # 80%相似度阈值
                    best_match = main_term
                    best_score = score
        
        if best_match:
            logger.info(f"模糊匹配: '{term}' -> '{best_match}' (相似度: {best_score:.2f})")
            return [best_match] + self.synonym_dict[best_match]
        
        # 如果没有找到匹配，返回原始术语
        return [term]
    
    def get_main_term(self, term: str) -> str:
        """获取术语的标准主术语
        
        Args:
            term: 原始术语
            
        Returns:
            标准主术语，如果没有找到匹配则返回原始术语
        """
        normalized_term = normalize_text(term)
        
        # 如果是主术语，直接返回
        if normalized_term in self.synonym_dict:
            return term
        
        # 如果是同义词，返回主术语
        if normalized_term in self.reverse_mapping:
            return self.reverse_mapping[normalized_term]
        
        # 尝试模糊匹配
        expanded = self.expand(term)
        if len(expanded) > 1:
            return expanded[0]
        
        # 没有找到匹配，返回原始术语
        return term
