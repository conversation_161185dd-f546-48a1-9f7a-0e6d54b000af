"""
通义千问意图识别器

集成通义千问意图识别模型，提供高精度的意图识别和函数调用信息提取
"""

import logging
import json
import re
from typing import Dict, Any, List, Optional, Tuple, Union, AsyncGenerator
from app.services.intent_recognition.models import IntentData

logger = logging.getLogger(__name__)

class TongyiIntentRecognizer:
    """通义千问意图识别器"""

    def __init__(self, model_service):
        """初始化通义千问意图识别器

        Args:
            model_service: 模型服务实例
        """
        self.model_service = model_service
        self.model_name = "tongyi-intent-detect-v3"

        # 意图映射配置
        self.intent_mapping = {
            # 训练计划相关
            "A": "daily_workout_plan",     # 单日训练计划
            "B": "weekly_training_plan",   # 周训练计划
            "C": "search_exercise",        # 查询训练动作
            "D": "recommend_exercise",     # 推荐训练动作

            # 健身咨询相关
            "E": "fitness_qa",             # 健身问答
            "F": "body_fat_calculation",   # 体脂计算
            "G": "calorie_calculation",    # 卡路里计算

            # 营养相关
            "H": "diet_advice",            # 饮食建议
            "I": "nutrition_advice",       # 营养建议
            "J": "macro_calculation",      # 宏量营养素计算

            # 其他
            "K": "general_chat",           # 一般聊天
            "L": "discuss_training_plan"   # 讨论训练计划
        }

        # 反向映射
        self.reverse_mapping = {v: k for k, v in self.intent_mapping.items()}

        logger.info("通义千问意图识别器初始化完成")

    def build_intent_prompt(self, user_input: str, context: Optional[Dict[str, Any]] = None) -> List[Dict[str, str]]:
        """构建通义千问意图识别提示词

        Args:
            user_input: 用户输入
            context: 上下文信息

        Returns:
            格式化的提示词
        """
        # 构建意图字典，使用单字母代码提高响应速度
        intent_dict = {
            "A": "单日训练计划 - 制定一天的训练计划，包括具体动作、组数和重量",
            "B": "周训练计划 - 制定一周的训练计划，包括每天训练的身体部位和安排",
            "C": "查询训练动作 - 查询特定训练动作的详细信息，如正确姿势和注意事项",
            "D": "推荐训练动作 - 根据目标肌肉群或身体部位推荐合适的训练动作",
            "E": "健身问答 - 回答关于健身、训练方法、肌肉生长等问题",
            "F": "体脂计算 - 计算体脂率或提供体脂相关建议",
            "G": "卡路里计算 - 计算每日所需卡路里或能量消耗",
            "H": "饮食建议 - 提供健康饮食、减肥或增肌饮食建议",
            "I": "营养建议 - 提供关于营养素、补充剂等方面的建议",
            "J": "宏量营养素计算 - 计算蛋白质、碳水化合物和脂肪的摄入量",
            "K": "一般聊天 - 与用户进行一般性对话",
            "L": "讨论训练计划 - 讨论或修改已有的训练计划"
        }

        # 将意图字典转换为JSON字符串
        intent_string = json.dumps(intent_dict, ensure_ascii=False)

        # 构建系统提示词
        system_prompt = f"""You are Qwen, created by Alibaba Cloud. You are a helpful assistant.
        You should choose one tag from the tag list:
        {intent_string}
        Just reply with the chosen tag."""

        # 如果有上下文，添加到用户输入中
        if context:
            # 提取关键上下文信息
            relevant_context = self._extract_relevant_context(context)
            context_str = "\n上下文信息:\n" + json.dumps(relevant_context, ensure_ascii=False)
            user_message = user_input + context_str
        else:
            user_message = user_input

        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_message}
        ]

    def _extract_relevant_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """提取相关上下文信息

        Args:
            context: 完整上下文

        Returns:
            相关上下文信息
        """
        relevant = {}

        # 提取可能影响意图识别的关键信息
        if "active_flow" in context:
            relevant["active_flow"] = context["active_flow"]

        if "collecting_training_params" in context:
            relevant["collecting_training_params"] = context["collecting_training_params"]

        if "asking_param" in context and context["asking_param"]:
            relevant["asking_param"] = context["asking_param"]

        if "training_params" in context and context["training_params"]:
            relevant["training_params"] = context["training_params"]

        if "related_plan_id" in context:
            relevant["related_plan_id"] = context["related_plan_id"]

        return relevant

    async def recognize_intent(self, user_input: str, context: Optional[Dict[str, Any]] = None) -> IntentData:
        """识别用户意图

        Args:
            user_input: 用户输入
            context: 上下文信息

        Returns:
            意图数据对象
        """
        # 构建提示词
        messages = self.build_intent_prompt(user_input, context)

        try:
            # 调用通义千问意图识别模型
            response = await self.model_service.get_response(
                messages=messages,
                model_type="intent_recognition",
                streaming=False
            )

            # 解析响应
            intent_code = response.strip()

            # 映射到实际意图
            if intent_code in self.intent_mapping:
                intent = self.intent_mapping[intent_code]
                confidence = 0.9  # 高置信度
            else:
                # 默认为一般聊天
                intent = "general_chat"
                confidence = 0.7  # 中等置信度
                logger.warning(f"未知意图代码: {intent_code}，默认为general_chat")

            # 提取参数 - 这里不提取参数，由参数提取器负责
            parameters = {}

            logger.info(f"通义千问识别意图: {intent} (代码: {intent_code}), 置信度: {confidence}")

            return IntentData(
                intent=intent,
                confidence=confidence,
                parameters=parameters
            )

        except Exception as e:
            # 错误处理，返回默认意图
            logger.error(f"通义千问意图识别失败: {str(e)}")
            return IntentData(
                intent="general_chat",
                confidence=0.6,
                parameters={}
            )


class TongyiIntentWithFunctionCalls:
    """支持函数调用的通义千问意图识别器"""

    def __init__(self, model_service):
        """初始化支持函数调用的通义千问意图识别器

        Args:
            model_service: 模型服务实例
        """
        self.model_service = model_service
        self.model_name = "tongyi-intent-detect-v3"

        # 定义工具
        self.tools = [
            {
                "name": "search_exercise",
                "description": "搜索训练动作，可以根据身体部位、器材等条件筛选",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "body_part": {
                            "type": "string",
                            "description": "目标身体部位，如胸部、背部、腿部等"
                        },
                        "equipment": {
                            "type": "string",
                            "description": "训练器材，如杠铃、哑铃、自重等"
                        }
                    },
                    "required": ["body_part"]
                }
            },
            {
                "name": "generate_training_plan",
                "description": "生成训练计划",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "plan_type": {
                            "type": "string",
                            "description": "计划类型，daily(单日)或weekly(每周)"
                        },
                        "body_part": {
                            "type": "string",
                            "description": "目标身体部位"
                        },
                        "scenario": {
                            "type": "string",
                            "description": "训练场景，gym(健身房)或home(居家)"
                        }
                    },
                    "required": ["plan_type"]
                }
            }
        ]

        logger.info("支持函数调用的通义千问意图识别器初始化完成")

    def build_function_call_prompt(self, user_input: str, context: Optional[Dict[str, Any]] = None) -> List[Dict[str, str]]:
        """构建支持函数调用的提示词

        Args:
            user_input: 用户输入
            context: 上下文信息

        Returns:
            格式化的提示词
        """
        # 将工具信息转换为JSON字符串
        tools_string = json.dumps(self.tools, ensure_ascii=False)

        # 构建系统提示词
        system_prompt = f"""You are Qwen, created by Alibaba Cloud. You are a helpful assistant. You may call one or more tools to assist with the user query. The tools you can use are as follows:
        {tools_string}
        Response in INTENT_MODE."""

        # 如果有上下文，添加到用户输入中
        if context:
            # 提取关键上下文信息
            relevant_context = self._extract_relevant_context(context)
            context_str = "\n上下文信息:\n" + json.dumps(relevant_context, ensure_ascii=False)
            user_message = user_input + context_str
        else:
            user_message = user_input

        return [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_message}
        ]

    def _extract_relevant_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """提取相关上下文信息

        Args:
            context: 完整上下文

        Returns:
            相关上下文信息
        """
        relevant = {}

        # 提取可能影响意图识别的关键信息
        if "active_flow" in context:
            relevant["active_flow"] = context["active_flow"]

        if "collecting_training_params" in context:
            relevant["collecting_training_params"] = context["collecting_training_params"]

        if "asking_param" in context and context["asking_param"]:
            relevant["asking_param"] = context["asking_param"]

        if "training_params" in context and context["training_params"]:
            relevant["training_params"] = context["training_params"]

        return relevant

    async def recognize_intent_with_function(self, user_input: str, context: Optional[Dict[str, Any]] = None) -> Tuple[IntentData, Optional[str]]:
        """识别用户意图并提取函数调用信息

        Args:
            user_input: 用户输入
            context: 上下文信息

        Returns:
            (意图数据对象, 函数调用信息)
        """
        # 构建提示词
        messages = self.build_function_call_prompt(user_input, context)

        try:
            # 调用通义千问意图识别模型
            response = await self.model_service.get_response(
                messages=messages,
                model_type="intent_recognition",
                streaming=False
            )

            # 解析响应
            parsed = self._parse_intent_response(response)

            # 提取意图和函数调用信息
            tags = parsed.get("tags", "")
            tool_call = parsed.get("tool_call", "")
            content = parsed.get("content", "")

            # 确定意图
            intent = "general_chat"  # 默认意图
            confidence = 0.7  # 默认置信度
            parameters = {}  # 默认参数

            # 从tags中提取意图
            if "training plan" in tags.lower():
                intent = "daily_workout_plan"
                confidence = 0.9
            elif "exercise" in tags.lower():
                intent = "search_exercise"
                confidence = 0.9

            # 从tool_call中提取参数
            if tool_call:
                try:
                    # 尝试解析JSON
                    tool_data = json.loads(tool_call)

                    # 处理不同格式的工具调用数据
                    if isinstance(tool_data, list) and len(tool_data) > 0:
                        # 列表格式的工具调用
                        tool = tool_data[0]
                        if isinstance(tool, dict) and "name" in tool:
                            if tool.get("name") == "search_exercise":
                                intent = "search_exercise"
                                parameters = tool.get("arguments", {})
                            elif tool.get("name") == "generate_training_plan":
                                plan_type = tool.get("arguments", {}).get("plan_type", "daily")
                                intent = "daily_workout_plan" if plan_type == "daily" else "weekly_training_plan"
                                parameters = tool.get("arguments", {})
                    elif isinstance(tool_data, dict):
                        # 字典格式的工具调用
                        if "name" in tool_data:
                            if tool_data.get("name") == "search_exercise":
                                intent = "search_exercise"
                                parameters = tool_data.get("arguments", {})
                            elif tool_data.get("name") == "generate_training_plan":
                                plan_type = tool_data.get("arguments", {}).get("plan_type", "daily")
                                intent = "daily_workout_plan" if plan_type == "daily" else "weekly_training_plan"
                                parameters = tool_data.get("arguments", {})
                        elif "intent" in tool_data:
                            # 直接包含意图和参数的格式
                            intent = tool_data.get("intent", "general_chat")
                            confidence = tool_data.get("confidence", 0.7)
                            parameters = tool_data.get("parameters", {})
                except Exception as e:
                    # 解析错误，使用默认值
                    logger.error(f"解析函数调用信息失败: {str(e)}")
                    # 尝试直接返回tool_call作为参数，以便后续处理
                    return IntentData(
                        intent="general_chat",
                        confidence=0.6,
                        parameters={}
                    ), tool_call

            logger.info(f"通义千问识别意图(函数调用): {intent}, 置信度: {confidence}, 参数: {parameters}")

            return IntentData(
                intent=intent,
                confidence=confidence,
                parameters=parameters
            ), tool_call

        except Exception as e:
            # 错误处理，返回默认意图
            logger.error(f"通义千问意图识别(函数调用)失败: {str(e)}")
            return IntentData(
                intent="general_chat",
                confidence=0.6,
                parameters={}
            ), None

    def _parse_intent_response(self, text: str) -> Dict[str, str]:
        """解析通义千问意图识别响应

        Args:
            text: 响应文本

        Returns:
            解析后的结果字典
        """
        # 定义正则表达式模式
        tags_pattern = r'<tags>(.*?)</tags>'
        tool_call_pattern = r'<tool_call>(.*?)</tool_call>'
        content_pattern = r'<content>(.*?)</content>'

        # 使用正则表达式查找匹配的内容
        tags_match = re.search(tags_pattern, text, re.DOTALL)
        tool_call_match = re.search(tool_call_pattern, text, re.DOTALL)
        content_match = re.search(content_pattern, text, re.DOTALL)

        # 提取匹配的内容
        tags = tags_match.group(1).strip() if tags_match else ""
        tool_call = tool_call_match.group(1).strip() if tool_call_match else ""
        content = content_match.group(1).strip() if content_match else ""

        # 返回解析结果
        return {
            "tags": tags,
            "tool_call": tool_call,
            "content": content
        }
