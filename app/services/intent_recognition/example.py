"""
增强版意图识别系统使用示例

展示如何在现有系统中使用新的意图识别系统
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from app.services.llm_proxy_service import LLMProxyService
from app.services.intent_recognition import EnhancedIntentRecognizer, IntentData

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def recognize_intent_example(user_input: str, context: Optional[Dict[str, Any]] = None) -> IntentData:
    """意图识别示例
    
    Args:
        user_input: 用户输入
        context: 上下文信息
        
    Returns:
        意图数据对象
    """
    # 创建LLM代理服务
    llm_proxy = LLMProxyService()
    
    # 创建增强版意图识别器
    intent_recognizer = EnhancedIntentRecognizer(llm_proxy)
    
    # 识别意图
    intent_data = await intent_recognizer.recognize_intent(user_input, context)
    
    # 打印结果
    logger.info(f"识别到意图: {intent_data.intent}, 置信度: {intent_data.confidence}")
    logger.info(f"参数: {intent_data.parameters}")
    
    return intent_data

async def main():
    """主函数"""
    # 示例1：训练计划意图
    await recognize_intent_example("我想做一个胸部训练计划")
    
    # 示例2：推荐动作意图
    await recognize_intent_example("推荐一些居家练背的动作")
    
    # 示例3：饮食建议意图
    await recognize_intent_example("减肥期间应该吃什么")
    
    # 示例4：健身问答意图
    await recognize_intent_example("肌肉生长的原理是什么")
    
    # 示例5：带上下文的意图识别
    context = {
        "collecting_training_params": True,
        "training_params": {"body_part": "胸部"},
        "asking_param": "scenario"
    }
    await recognize_intent_example("在健身房", context)

def integrate_with_conversation_service():
    """与对话服务集成的示例"""
    from app.db.session import get_db
    from app.services.conversation.orchestrator import ConversationService
    
    # 获取数据库会话
    db = next(get_db())
    
    # 创建LLM代理服务
    llm_proxy = LLMProxyService()
    
    # 创建增强版意图识别器
    intent_recognizer = EnhancedIntentRecognizer(llm_proxy)
    
    # 创建对话服务实例
    conversation_service = ConversationService(db, llm_proxy)
    
    # 替换对话服务中的意图识别器
    conversation_service.intent_recognizer = intent_recognizer
    
    logger.info("已将增强版意图识别器集成到对话服务中")
    
    return conversation_service

if __name__ == "__main__":
    # 运行异步示例
    asyncio.run(main())
    
    # 集成示例
    conversation_service = integrate_with_conversation_service()
    logger.info("集成示例完成")
