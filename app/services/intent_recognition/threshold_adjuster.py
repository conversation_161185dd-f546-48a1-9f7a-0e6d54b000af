"""
动态阈值调整机制

根据查询复杂性、意图类型和上下文动态调整置信度阈值
"""

import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

def calculate_query_complexity(query: str) -> float:
    """计算查询复杂性

    Args:
        query: 用户查询

    Returns:
        复杂性得分，范围0-1
    """
    # 基于查询长度的复杂性
    length_factor = min(len(query) / 100, 1.0) * 0.5  # 最多贡献0.5分

    # 基于特殊字符和标点符号的复杂性
    special_chars = sum(1 for c in query if not c.isalnum() and not c.isspace())
    special_factor = min(special_chars / 10, 1.0) * 0.2  # 最多贡献0.2分

    # 基于句子数量的复杂性
    sentence_count = query.count('。') + query.count('！') + query.count('？') + query.count('.') + query.count('!') + query.count('?')
    sentence_factor = min(sentence_count / 3, 1.0) * 0.3  # 最多贡献0.3分

    # 计算总复杂性
    complexity = length_factor + special_factor + sentence_factor

    logger.debug(f"查询复杂性: {complexity:.2f} (长度: {length_factor:.2f}, 特殊字符: {special_factor:.2f}, 句子: {sentence_factor:.2f})")

    return complexity

def calculate_dynamic_threshold(query: str, intent: str, context: Optional[Dict[str, Any]] = None) -> float:
    """计算动态置信度阈值

    Args:
        query: 用户查询
        intent: 识别的意图
        context: 上下文信息

    Returns:
        动态阈值
    """
    # 基础阈值
    base_threshold = 0.7

    # 根据查询复杂性调整
    complexity = calculate_query_complexity(query)
    complexity_factor = -0.1 * complexity  # 复杂查询降低阈值

    # 检查是否包含训练相关参数
    from app.services.intent_recognition.models import BODY_PARTS_KEYWORDS, COMMON_EXERCISE_QUERY_PHRASES

    # 检查是否包含常见训练动作查询短语
    contains_exercise_query = any(phrase in query for phrase in COMMON_EXERCISE_QUERY_PHRASES)

    # 检查是否包含身体部位关键词
    contains_body_part = any(bp in query for bp in BODY_PARTS_KEYWORDS)

    # 检查是否包含"怎么练"、"如何练"等训练动作查询模式
    contains_training_pattern = "怎么练" in query or "如何练" in query or "锻炼方法" in query

    # 根据意图类型调整
    intent_factor = 0
    if intent in ["daily_workout_plan", "weekly_training_plan"]:
        # 训练计划意图需要更高的确定性
        intent_factor = 0.1
    elif intent in ["general_chat"]:
        # 一般聊天可以有更低的阈值
        intent_factor = -0.1

        # 如果包含训练相关参数，提高general_chat的阈值，使其更难被选中
        if contains_body_part and contains_training_pattern:
            intent_factor = 0.2  # 显著提高阈值
            logger.info(f"检测到训练相关参数和模式，提高general_chat阈值: {intent_factor}")
        elif contains_body_part:
            intent_factor = 0.1  # 适度提高阈值
            logger.info(f"检测到身体部位关键词，提高general_chat阈值: {intent_factor}")

    elif intent in ["recommend_exercise", "search_exercise"]:
        # 推荐动作和查询动作需要中等确定性
        intent_factor = 0.05

        # 如果包含训练动作查询模式，降低阈值，使其更容易被选中
        if contains_exercise_query:
            intent_factor = -0.2  # 显著降低阈值
            logger.info(f"检测到常见训练动作查询短语，降低recommend_exercise阈值: {intent_factor}")
        elif contains_body_part and contains_training_pattern:
            intent_factor = -0.15  # 显著降低阈值
            logger.info(f"检测到身体部位和训练模式，降低recommend_exercise阈值: {intent_factor}")

    # 根据上下文调整
    context_factor = 0
    if context:
        # 如果有活跃流程，提高阈值
        if "active_flow" in context:
            context_factor += 0.05

        # 如果正在收集参数，提高阈值
        if context.get("collecting_training_params", False):
            context_factor += 0.05

        # 如果有相关计划ID，提高阈值
        if "related_plan_id" in context:
            context_factor += 0.05

    # 计算最终阈值
    threshold = base_threshold + complexity_factor + intent_factor + context_factor

    # 确保阈值在合理范围内
    threshold = max(0.6, min(threshold, 0.95))

    logger.debug(f"动态阈值: {threshold:.2f} (基础: {base_threshold}, 复杂性: {complexity_factor:.2f}, 意图: {intent_factor:.2f}, 上下文: {context_factor:.2f})")

    return threshold
