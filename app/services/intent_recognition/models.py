"""
意图识别系统 - 数据模型

定义意图识别系统使用的数据模型和常量
"""

from pydantic import BaseModel, Field
from typing import Dict, Any, Optional, List

# 意图数据模型
class IntentData(BaseModel):
    """意图数据模型"""
    intent: str = Field(..., description="识别出的用户意图类型")
    confidence: float = Field(..., description="置信度，范围0-1")
    parameters: Optional[Dict[str, Any]] = Field(None, description="意图参数，例如目标身体部位等")

# 支持的意图类型
SUPPORTED_INTENTS = [
    "daily_workout_plan",  # 制定单日训练计划
    "weekly_training_plan",  # 制定每周训练计划
    "search_exercise",  # 查询健身动作信息
    "recommend_exercise",  # 推荐训练动作
    "diet_advice",  # 饮食建议
    "fitness_qa",  # 健身相关问答
    "body_fat_calculation",  # 体脂计算
    "calorie_calculation",  # 卡路里计算
    "general_chat",  # 一般聊天
    "discuss_training_plan"  # 讨论已有训练计划
]

# 意图大类定义
INTENT_CATEGORIES = {
    "training": ["daily_workout_plan", "weekly_training_plan", "search_exercise", "recommend_exercise"],
    "nutrition": ["diet_advice", "macro_calculation", "nutrition_advice"],
    "fitness_qa": ["fitness_qa", "body_fat_calculation", "calorie_calculation"],
    "general": ["general_chat", "discuss_training_plan"]
}

# 意图名称映射
INTENT_MAPPING = {
    # 中文意图名称映射到英文标准意图
    "训练计划": "create_training_plan",
    "单日训练": "daily_workout_plan",
    "周期训练": "weekly_training_plan",
    "查询动作": "search_exercise",
    "推荐动作": "recommend_exercise",
    "健身咨询": "fitness_qa",
    "营养建议": "diet_advice",
    "讨论计划": "discuss_training_plan",
    "一般聊天": "general_chat",

    # 确保中文意图名称和英文意图名称都能正确映射
    "create_training_plan": "create_training_plan",
    "daily_workout_plan": "daily_workout_plan",
    "weekly_training_plan": "weekly_training_plan",
    "search_exercise": "search_exercise",
    "recommend_exercise": "recommend_exercise",
    "fitness_qa": "fitness_qa",
    "diet_advice": "diet_advice",
    "discuss_training_plan": "discuss_training_plan",
    "general_chat": "general_chat"
}

# 关键词映射到意图
KEYWORD_INTENT_MAP = {
    "训练计划": ["训练计划", "健身计划", "锻炼计划", "健身方案", "锻炼方案", "训练方案", "制定计划"],
    "单日训练": ["今天训练", "单日训练", "一天训练", "今日训练", "单次训练", "今天锻炼", "单日计划", "居家训练", "健身房训练"],
    "周期训练": ["周训练", "周计划", "每周训练", "周期训练", "长期训练", "每周计划", "月计划", "训练周期"],
    "查询动作": ["动作介绍", "动作说明", "如何做", "怎么做", "动作详情", "动作查询", "正确姿势", "标准动作"],
    "推荐动作": ["推荐动作", "建议动作", "适合的动作", "可以做什么动作", "哪些动作好", "动作推荐", "练什么", "练哪些"],
    "健身咨询": ["健身问题", "健身知识", "健身方法", "健身技巧", "健身建议", "肌肉生长", "代谢", "碳循环", "无氧运动", "有氧运动",
                "超量恢复", "乳酸", "肌纤维", "肌肉酸痛", "健身原理", "健身科学", "训练理论", "健身房", "居家"],
    "营养建议": ["饮食", "营养", "吃什么", "食物", "蛋白质", "碳水", "脂肪", "减肥", "增肌饮食", "热量", "卡路里",
                "宏量营养素", "微量营养素", "维生素", "矿物质", "补剂", "蛋白粉", "氨基酸", "肌酸"],
    "一般聊天": ["你好", "谢谢", "再见", "聊天", "闲聊", "你是谁", "介绍一下", "能做什么"]
}

# 常见身体部位关键词
BODY_PARTS_KEYWORDS = ["胸", "背", "腿", "肩", "手臂", "二头", "三头", "腹", "腰", "臀", "核心", "胸肌", "背肌",
                      "腿部", "肩部", "手臂", "二头肌", "三头肌", "腹肌", "腰部", "臀部", "股四头肌", "股二头肌",
                      "小腿", "前臂", "上背", "下背", "上胸", "中胸", "下胸", "斜方肌", "三角肌"]

# 训练动词关键词
TRAINING_VERBS = ["练", "锻炼", "训练", "强化", "增强", "塑造", "雕刻"]

# 查询关键词
QUERY_KEYWORDS = ["怎么", "如何", "怎样", "什么方法", "什么动作", "哪些动作"]

# 训练环境关键词
ENVIRONMENT_KEYWORDS = {
    "居家": ["居家", "家里", "家中", "在家", "不去健身房", "没有器械", "简易", "徒手"],
    "健身房": ["健身房", "器械", "杠铃", "哑铃", "器材", "健身中心", "健身会所"]
}

# 训练目标关键词
GOAL_KEYWORDS = {
    "增肌": ["增肌", "长肌肉", "增长肌肉", "肌肉增长", "肌肉增大", "肌肉生长", "块头", "壮", "增重"],
    "减脂": ["减脂", "减肥", "瘦身", "减重", "体重管理", "瘦", "塑形", "减掉赘肉", "燃脂"],
    "力量": ["力量", "变强", "更强", "爆发力", "力量训练", "举重", "硬拉", "深蹲", "卧推"],
    "耐力": ["耐力", "持久", "有氧", "跑步", "游泳", "骑行", "心肺", "心肺功能"],
    "灵活性": ["灵活性", "柔韧性", "拉伸", "伸展", "瑜伽", "普拉提"]
}

# 健身科学知识关键词
SCIENCE_KEYWORDS = ["什么是", "原理", "机制", "原因", "为什么", "怎么会", "如何发生", "科学", "理论",
                   "碳循环", "代谢", "肌肉生长", "超量恢复", "乳酸", "肌纤维", "肌肉酸痛", "蛋白质合成",
                   "激素", "睾酮", "生长激素", "胰岛素", "皮质醇", "肾上腺素"]

# 训练动作查询模式 - 用于直接匹配
EXERCISE_QUERY_PATTERNS = [
    r"(.+)怎么练", r"(.+)如何练", r"怎么练(.+)", r"如何练(.+)",
    r"练(.+)的方法", r"(.+)的训练方法", r"(.+)锻炼方法", r"锻炼(.+)的方法",
    r"(.+)训练动作", r"训练(.+)的动作", r"(.+)的动作推荐", r"推荐(.+)的动作"
]

# 常见训练动作查询短语 - 用于精确匹配
COMMON_EXERCISE_QUERY_PHRASES = [
    "胸肌怎么练", "胸部怎么练", "背部怎么练", "腿部怎么练", "肩部怎么练",
    "手臂怎么练", "二头肌怎么练", "三头肌怎么练", "腹肌怎么练",
    "怎么练胸肌", "怎么练胸部", "怎么练背部", "怎么练腿部", "怎么练肩部",
    "怎么练手臂", "怎么练二头肌", "怎么练三头肌", "怎么练腹肌",
    "如何练胸肌", "如何练背部", "如何练腿部", "如何锻炼胸肌", "如何锻炼背部"
]
