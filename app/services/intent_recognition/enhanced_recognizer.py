"""
增强版意图识别器

集成关键词匹配、分层意图识别和通义千问意图识别，提供更准确、更高效的意图识别
"""

import logging
import time
import hashlib
from typing import Dict, Any, List, Optional, Tuple, Union
from app.services.intent_recognition.models import (
    IntentData, KEYWORD_INTENT_MAP, INTENT_MAPPING,
    BODY_PARTS_KEYWORDS, TRAINING_VERBS, QUERY_KEYWORDS,
    ENVIRONMENT_KEYWORDS, GOAL_KEYWORDS, SCIENCE_KEYWORDS,
    EXERCISE_QUERY_PATTERNS, COMMON_EXERCISE_QUERY_PHRASES
)
from app.services.intent_recognition.synonym_expander import SynonymExpander
from app.services.intent_recognition.parameter_extractor import ParameterExtractor
from app.services.intent_recognition.tongyi_recognizer import TongyiIntentRecognizer, TongyiIntentWithFunctionCalls
from app.services.intent_recognition.hierarchical_classifier import HierarchicalIntentClassifier
from app.services.intent_recognition.threshold_adjuster import calculate_dynamic_threshold

logger = logging.getLogger(__name__)

class EnhancedIntentRecognizer:
    """增强版意图识别器"""

    def __init__(self, llm_service):
        """初始化增强版意图识别器

        Args:
            llm_service: LLM服务，用于调用语言模型
        """
        self.llm_service = llm_service

        # 初始化模型服务
        from app.services.model_service import ModelService
        self.model_service = ModelService(llm_service)

        # 获取意图识别模型配置
        from app.core.model_config import MODEL_MAPPING
        self.intent_model = MODEL_MAPPING["intent_recognition"]["default"]
        logger.info(f"意图识别器使用模型: {self.intent_model}")

        # 初始化同义词扩展器
        self.synonym_expander = SynonymExpander()

        # 初始化参数提取器
        from app.services.sql_tool_service import SQLToolService
        sql_tool = SQLToolService()
        self.parameter_extractor = ParameterExtractor(sql_tool)

        # 初始化通义千问意图识别器
        self.tongyi_intent_recognizer = TongyiIntentRecognizer(self.model_service)

        # 初始化函数调用支持
        self.function_call_recognizer = TongyiIntentWithFunctionCalls(self.model_service)

        # 初始化分层意图分类器
        self.hierarchical_classifier = HierarchicalIntentClassifier(self.model_service)

        # 缓存相关
        self.cache_enabled = True
        self.cache = {}  # 简单的内存缓存
        self.cache_ttl = 3600  # 缓存有效期（秒）
        self.cache_timestamps = {}  # 记录每个缓存项的时间戳

        logger.info("增强版意图识别器初始化完成")

    async def recognize_intent(self, user_input: str, context: Optional[Dict[str, Any]] = None) -> IntentData:
        """识别用户输入的意图

        Args:
            user_input: 用户输入文本
            context: 上下文信息

        Returns:
            意图数据对象
        """
        # 检查缓存
        cache_key = self._generate_cache_key(user_input, context)
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            logger.info(f"使用缓存的意图识别结果: {cached_result.intent}")
            return cached_result

        # 首先尝试使用关键词匹配进行快速意图识别
        quick_intent_name, quick_confidence, quick_parameters = self._extract_intent_from_text(user_input)

        # 提取参数并根据参数调整意图和置信度
        extracted_params = self.parameter_extractor.extract_parameters(user_input, quick_intent_name)

        # 如果提取到了身体部位参数，但意图不是训练相关，调整意图和置信度
        if "body_part" in extracted_params and quick_intent_name == "一般聊天":
            # 检查是否包含训练动作查询模式
            if "怎么练" in user_input or "如何练" in user_input or "锻炼方法" in user_input:
                logger.info(f"根据参数和查询模式调整意图: 从 {quick_intent_name} 到 推荐动作")
                quick_intent_name = "推荐动作"
                quick_confidence = 0.95  # 提高置信度，确保不会被后续模型覆盖
                quick_parameters.update(extracted_params)

                # 记录调整后的意图和参数
                logger.info(f"调整后的意图: {quick_intent_name}, 置信度: {quick_confidence}, 参数: {quick_parameters}")

        # 合并提取的参数
        if extracted_params:
            for key, value in extracted_params.items():
                if key not in quick_parameters:
                    quick_parameters[key] = value

            # 确保body_part是列表类型
            if "body_part" in quick_parameters and not isinstance(quick_parameters["body_part"], list):
                quick_parameters["body_part"] = [quick_parameters["body_part"]]

        # 计算动态阈值
        threshold = calculate_dynamic_threshold(user_input, quick_intent_name, context)

        # 如果关键词匹配的置信度很高，直接返回结果并缓存
        if quick_confidence >= threshold:
            logger.info(f"关键词匹配高置信度意图: {quick_intent_name}, 置信度: {quick_confidence}, 阈值: {threshold}")
            # 映射意图名称
            mapped_intent = INTENT_MAPPING.get(quick_intent_name, quick_intent_name)

            # 标准化参数
            standardized_parameters = self.parameter_extractor.standardize_parameters(quick_parameters, mapped_intent)

            result = IntentData(
                intent=mapped_intent,
                confidence=quick_confidence,
                parameters=standardized_parameters
            )
            # 缓存结果
            self._add_to_cache(cache_key, result)
            return result

        # 使用通义千问意图识别模型
        try:
            # 根据上下文决定是否使用函数调用
            if context and context.get("collecting_training_params", False):
                # 如果正在收集训练参数，使用函数调用版本
                intent_data, _ = await self.function_call_recognizer.recognize_intent_with_function(user_input, context)
            else:
                # 否则使用标准版本
                intent_data = await self.tongyi_intent_recognizer.recognize_intent(user_input, context)

            # 如果通义千问识别成功且置信度高于关键词匹配
            if intent_data.confidence > quick_confidence:
                logger.info(f"使用通义千问识别结果: {intent_data.intent}, 置信度: {intent_data.confidence}")

                # 提取参数
                parameters = self.parameter_extractor.extract_parameters(user_input, intent_data.intent)

                # 标准化参数
                standardized_parameters = self.parameter_extractor.standardize_parameters(parameters, intent_data.intent)

                # 更新参数
                intent_data.parameters = standardized_parameters

                # 缓存结果
                self._add_to_cache(cache_key, intent_data)
                return intent_data
        except Exception as e:
            logger.error(f"通义千问意图识别失败: {str(e)}")
            # 继续使用关键词匹配结果

        # 如果代码执行到这里，说明模型识别失败或置信度不够高
        # 使用关键词匹配结果作为备用
        logger.info(f"使用关键词匹配结果作为备用: {quick_intent_name}, 置信度: {quick_confidence}")

        # 映射意图名称
        mapped_intent = INTENT_MAPPING.get(quick_intent_name, quick_intent_name)

        # 标准化参数
        standardized_parameters = self.parameter_extractor.standardize_parameters(quick_parameters, mapped_intent)

        result = IntentData(
            intent=mapped_intent,
            confidence=quick_confidence,
            parameters=standardized_parameters
        )
        # 缓存结果
        self._add_to_cache(cache_key, result)
        return result

    async def extract_parameters(self, user_input: str, context: Optional[Dict[str, Any]] = None) -> IntentData:
        """从用户输入中提取参数，返回意图数据对象

        这个方法设计为仅提取参数，而不进行完整的意图识别。
        它主要由ParameterExtractor类调用，用于在已知意图的情况下提取其参数。

        Args:
            user_input: 用户输入文本
            context: 上下文信息，可能包含正在提取的参数类型等

        Returns:
            意图数据对象，主要包含参数信息
        """
        # 检查缓存
        cache_key = self._generate_cache_key(user_input, context) + "_params"
        cached_result = self._get_from_cache(cache_key)
        if cached_result:
            logger.info(f"使用缓存的参数提取结果: {cached_result.parameters}")
            return cached_result

        # 确定当前处理的意图类型和参数
        intent_name = None
        asking_param = None
        
        if context:
            # 从上下文获取意图类型
            if "intent" in context:
                intent_name = context["intent"]
            elif "collecting_training_params" in context and context["collecting_training_params"]:
                # 如果正在收集训练参数，默认为训练相关意图
                intent_name = "recommend_exercise"
            
            # 获取当前正在询问的参数
            if "asking_param" in context:
                asking_param = context["asking_param"]
            
            # 获取需要排除的参数
            exclude_params = context.get("exclude_params", [])
        
        # 尝试使用通义千问的函数调用功能提取参数
        parameters = {}
        try:
            # 如果有明确的意图和参数，使用函数调用方式
            if intent_name or asking_param:
                # 构建提示和函数调用
                intent_data, _ = await self.function_call_recognizer.recognize_intent_with_function(
                    user_input, 
                    context={
                        "intent": intent_name,
                        "asking_param": asking_param,
                        "extract_only": True  # 标记仅提取参数，不进行意图识别
                    }
                )
                parameters = intent_data.parameters
                logger.info(f"通过函数调用提取参数: {parameters}")
            else:
                # 否则使用标准意图识别提取参数
                intent_data = await self.tongyi_intent_recognizer.recognize_intent(
                    user_input, 
                    context={"extract_only": True}
                )
                parameters = intent_data.parameters
                logger.info(f"通过标准识别提取参数: {parameters}")
                
            # 如果未能提取参数，或参数为空，使用关键词匹配
            if not parameters:
                # 使用参数提取器从用户输入中提取参数
                extracted_params = self.parameter_extractor.extract_parameters(user_input, intent_name)
                parameters = extracted_params
                logger.info(f"通过关键词匹配提取参数: {parameters}")
        except Exception as e:
            logger.error(f"通过LLM提取参数失败: {str(e)}")
            # 使用参数提取器从用户输入中提取参数
            extracted_params = self.parameter_extractor.extract_parameters(user_input, intent_name)
            parameters = extracted_params
            logger.info(f"错误后回退到关键词匹配提取参数: {parameters}")
        
        # 如果参数为空，使用默认值
        if not parameters:
            parameters = {}
        
        # 标准化参数
        standardized_parameters = self.parameter_extractor.standardize_parameters(parameters, intent_name or "general_chat")
        
        # 创建IntentData对象
        # 注意：此处没有进行意图识别，而是创建一个主要用于携带参数的IntentData对象
        result = IntentData(
            intent=intent_name or "parameter_extraction",  # 使用默认值或context中的意图
            confidence=0.95 if parameters else 0.5,  # 如果提取到参数，置信度高；否则低
            parameters=standardized_parameters
        )
        
        # 缓存结果
        self._add_to_cache(cache_key, result)
        
        return result

    def _generate_cache_key(self, user_input: str, context: Optional[Dict[str, Any]] = None) -> str:
        """生成缓存键

        Args:
            user_input: 用户输入
            context: 上下文信息

        Returns:
            缓存键
        """
        # 简化的用户输入（去除空格和标点）
        simplified_input = ''.join(c for c in user_input if c.isalnum())

        # 如果有上下文，将关键上下文信息添加到缓存键
        context_str = ""
        if context:
            if "active_flow" in context:
                context_str += f"_flow_{context['active_flow']}"
            if "related_plan_id" in context:
                context_str += f"_plan_{context['related_plan_id']}"

        # 使用哈希函数生成固定长度的键
        key = f"{simplified_input[:50]}{context_str}"
        return hashlib.md5(key.encode()).hexdigest()

    def _get_from_cache(self, cache_key: str) -> Optional[IntentData]:
        """从缓存中获取结果

        Args:
            cache_key: 缓存键

        Returns:
            缓存的意图数据，如果没有则返回None
        """
        if not self.cache_enabled:
            return None

        current_time = time.time()
        if cache_key in self.cache:
            # 检查缓存是否过期
            timestamp = self.cache_timestamps.get(cache_key, 0)
            if current_time - timestamp < self.cache_ttl:
                return self.cache[cache_key]
            else:
                # 缓存过期，删除
                del self.cache[cache_key]
                del self.cache_timestamps[cache_key]

        return None

    def _add_to_cache(self, cache_key: str, intent_data: IntentData):
        """添加结果到缓存

        Args:
            cache_key: 缓存键
            intent_data: 意图数据
        """
        if not self.cache_enabled:
            return

        self.cache[cache_key] = intent_data
        self.cache_timestamps[cache_key] = time.time()

        # 简单的缓存大小控制
        if len(self.cache) > 1000:  # 最多保存1000个缓存项
            # 删除最旧的缓存项
            oldest_key = min(self.cache_timestamps.keys(), key=lambda k: self.cache_timestamps[k])
            del self.cache[oldest_key]
            del self.cache_timestamps[oldest_key]

    def _extract_intent_from_text(self, text: str) -> tuple:
        """从文本中提取意图

        Args:
            text: 响应文本

        Returns:
            tuple: (意图名称, 置信度, 参数字典)
        """
        # 默认为一般聊天
        intent_name = "一般聊天"
        confidence = 0.7
        parameters = {}

        # 首先检查是否匹配常见训练动作查询短语
        for phrase in COMMON_EXERCISE_QUERY_PHRASES:
            if phrase in text:
                intent_name = "推荐动作"
                confidence = 0.95  # 非常高的置信度
                # 提取身体部位
                for bp in BODY_PARTS_KEYWORDS:
                    if bp in phrase:
                        parameters["body_part"] = bp
                        break
                logger.info(f"直接匹配常见训练动作查询短语: '{phrase}'")
                return intent_name, confidence, parameters

        # 使用正则表达式匹配训练动作查询模式
        import re
        for pattern in EXERCISE_QUERY_PATTERNS:
            match = re.search(pattern, text)
            if match:
                potential_body_part = match.group(1)
                # 验证提取的文本是否包含身体部位关键词
                if any(bp in potential_body_part for bp in BODY_PARTS_KEYWORDS):
                    intent_name = "推荐动作"
                    confidence = 0.95  # 非常高的置信度
                    parameters["body_part"] = potential_body_part
                    logger.info(f"正则匹配训练动作查询模式: '{pattern}', 身体部位: {potential_body_part}")
                    return intent_name, confidence, parameters

        # 检查文本中是否包含关键词
        max_matches = 0
        for intent, keywords in KEYWORD_INTENT_MAP.items():
            matches = sum(1 for keyword in keywords if keyword in text)
            if matches > max_matches:
                max_matches = matches
                intent_name = intent
                # 根据匹配数量调整置信度
                confidence = min(0.7 + 0.05 * matches, 0.95)

        # 识别身体部位
        found_body_part = None
        for bp in BODY_PARTS_KEYWORDS:
            if bp in text:
                found_body_part = bp
                break

        # 识别训练环境
        found_environment = None
        for env, keywords in ENVIRONMENT_KEYWORDS.items():
            if any(keyword in text for keyword in keywords):
                found_environment = env
                break

        # 识别训练目标
        found_goal = None
        for goal, keywords in GOAL_KEYWORDS.items():
            if any(keyword in text for keyword in keywords):
                found_goal = goal
                break

        # 识别是否是动作查询 - 增强版
        is_exercise_query = False
        if found_body_part:
            # 检查常见训练动作查询模式
            common_patterns = [
                f"{found_body_part}怎么练", f"{found_body_part}如何练",
                f"怎么练{found_body_part}", f"如何练{found_body_part}",
                f"{found_body_part}锻炼", f"锻炼{found_body_part}",
                f"{found_body_part}训练", f"训练{found_body_part}",
                f"练{found_body_part}", f"{found_body_part}练习"
            ]

            if any(pattern in text for pattern in common_patterns):
                is_exercise_query = True
                logger.info(f"检测到动作查询常见模式: '{found_body_part}怎么练' 或类似模式")
            else:
                # 更详细的检查
                for verb in TRAINING_VERBS:
                    for query_word in QUERY_KEYWORDS:
                        if (found_body_part + query_word + verb in text) or \
                           (query_word + verb + found_body_part in text) or \
                           (found_body_part + verb in text and (query_word in text or not QUERY_KEYWORDS)) or \
                           (verb + found_body_part in text and (query_word in text or not QUERY_KEYWORDS)):
                            is_exercise_query = True
                            logger.info(f"检测到动作查询模式: '{query_word}{verb}{found_body_part}' 或类似模式")
                            break
                    if is_exercise_query:
                        break

            # 检查模式如 "练胸"
            if not is_exercise_query:
                for verb in TRAINING_VERBS:
                    if verb + found_body_part in text or found_body_part + verb in text:
                        is_exercise_query = True
                        logger.info(f"检测到简单动作查询模式: '{verb}{found_body_part}' 或 '{found_body_part}{verb}'")
                        break

            # 如果包含身体部位和"怎么"或"如何"，也视为动作查询
            if not is_exercise_query and ("怎么" in text or "如何" in text):
                is_exercise_query = True
                logger.info(f"检测到含身体部位和疑问词的查询: '{found_body_part}' + '怎么/如何'")

            # 如果包含身体部位和"动作"或"训练"，也视为动作查询
            if not is_exercise_query and ("动作" in text or "训练" in text):
                is_exercise_query = True
                logger.info(f"检测到含身体部位和动作/训练关键词的查询: '{found_body_part}' + '动作/训练'")

        # 识别是否是训练计划请求
        is_training_plan_request = False
        if "计划" in text or "方案" in text or "安排" in text:
            if "单日" in text or "一天" in text or "今天" in text or "今日" in text:
                intent_name = "单日训练"
                confidence = 0.90
                is_training_plan_request = True
            elif "周" in text or "每周" in text or "长期" in text or "月" in text:
                intent_name = "周期训练"
                confidence = 0.90
                is_training_plan_request = True
            else:
                # 默认为单日训练计划
                intent_name = "单日训练"
                confidence = 0.85
                is_training_plan_request = True

        # 如果是动作查询
        if is_exercise_query and not is_training_plan_request:
            intent_name = "推荐动作"
            confidence = 0.95  # 提高置信度，确保优先级高于其他意图

        # 添加参数
        if found_body_part:
            parameters["body_part"] = found_body_part

        if found_environment:
            parameters["scenario"] = found_environment

        if found_goal:
            parameters["training_goal"] = found_goal

        # 如果文本中包含"减肥"、"瘦身"等关键词，可能是营养建议
        if any(keyword in text for keyword in ["减肥", "瘦身", "减重", "体重管理"]):
            if "饮食" in text or "吃" in text or "食物" in text:
                intent_name = "营养建议"
                confidence = 0.85

        # 如果文本中包含"增肌"、"增重"等关键词，可能是训练计划或营养建议
        if any(keyword in text for keyword in ["增肌", "增重", "长肌肉", "肌肉增长"]):
            if "训练" in text or "锻炼" in text:
                intent_name = "训练计划"
                confidence = 0.85
            elif "饮食" in text or "吃" in text or "食物" in text:
                intent_name = "营养建议"
                confidence = 0.85

        # 识别健身科学知识问题
        if any(keyword in text for keyword in SCIENCE_KEYWORDS):
            # 如果是健身科学知识问题，归类为fitness_qa
            intent_name = "健身咨询"
            confidence = 0.90

        # 识别训练环境比较问题
        if "健身房" in text and "居家" in text and ("区别" in text or "比较" in text or "差异" in text or "不同" in text):
            intent_name = "健身咨询"
            confidence = 0.90

        logger.debug(f"从文本中提取到意图: {intent_name}, 置信度: {confidence}")
        return intent_name, confidence, parameters

    def get_intent_description(self, intent: str) -> str:
        """获取意图的中文描述

        Args:
            intent: 意图类型

        Returns:
            意图的中文描述
        """
        intent_map = {
            "daily_workout_plan": "制定单日训练计划",
            "weekly_training_plan": "制定每周训练计划",
            "search_exercise": "查询健身动作信息",
            "recommend_exercise": "推荐训练动作",
            "diet_advice": "饮食建议",
            "fitness_qa": "健身问答",
            "body_fat_calculation": "体脂计算",
            "calorie_calculation": "卡路里计算",
            "general_chat": "一般聊天",
            "discuss_training_plan": "讨论训练计划"
        }
        return intent_map.get(intent, "未知意图")
