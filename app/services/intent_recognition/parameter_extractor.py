"""
参数提取器

提供从用户输入中提取参数的功能，支持身体部位、训练器械和肌肉群等参数的提取和标准化
"""

import logging
import re
from typing import Dict, Any, List, Optional, Tuple, Union
from app.services.intent_recognition.synonym_expander import SynonymExpander, normalize_text
from app.services.intent_recognition.models import (
    BODY_PARTS_KEYWORDS, TRAINING_VERBS, QUERY_KEYWORDS,
    ENVIRONMENT_KEYWORDS, GOAL_KEYWORDS
)

logger = logging.getLogger(__name__)

class ParameterExtractor:
    """参数提取器"""
    
    def __init__(self, sql_tool_service=None):
        """初始化参数提取器
        
        Args:
            sql_tool_service: SQL工具服务实例，用于数据库查询
        """
        self.sql_tool = sql_tool_service
        self.synonym_expander = SynonymExpander()
        
        # 从SQLToolService加载类别数据
        from app.services.sql_tool_service import (
            BODY_PART_CATEGORIES, 
            EQUIPMENT_CATEGORIES, 
            MUSCLE_CATEGORIES
        )
        self.body_part_categories = BODY_PART_CATEGORIES
        self.equipment_categories = EQUIPMENT_CATEGORIES
        self.muscle_categories = MUSCLE_CATEGORIES
        
        # 初始化正则表达式模式
        self._init_regex_patterns()
        
        logger.info("参数提取器初始化完成")
    
    def _init_regex_patterns(self):
        """初始化正则表达式模式"""
        # 身体部位提取模式
        body_parts = "|".join([bp["name"] for bp in self.body_part_categories])
        self.body_part_pattern = re.compile(f"({'|'.join(body_parts)})")
        
        # 训练器械提取模式
        equipments = "|".join([eq["name"] for eq in self.equipment_categories])
        self.equipment_pattern = re.compile(f"({'|'.join(equipments)})")
        
        # 肌肉群提取模式
        muscles = "|".join([m["name"] for m in self.muscle_categories])
        self.muscle_pattern = re.compile(f"({'|'.join(muscles)})")
        
        # 训练场景提取模式
        scenarios = ["居家", "健身房"]
        self.scenario_pattern = re.compile(f"({'|'.join(scenarios)})")
        
        # 训练目标提取模式
        goals = ["增肌", "减脂", "力量", "耐力", "灵活性"]
        self.goal_pattern = re.compile(f"({'|'.join(goals)})")
    
    def extract_parameters(self, text: str, intent: str) -> Dict[str, Any]:
        """从文本中提取参数
        
        Args:
            text: 用户输入文本
            intent: 识别的意图
            
        Returns:
            参数字典
        """
        parameters = {}
        
        # 根据意图类型决定需要提取的参数
        if intent in ["daily_workout_plan", "weekly_training_plan", "recommend_exercise"]:
            # 提取身体部位
            body_parts = self.extract_body_parts(text)
            if body_parts:
                parameters["body_part"] = body_parts
            
            # 提取训练场景
            scenario = self.extract_scenario(text)
            if scenario:
                parameters["scenario"] = scenario
            
            # 对于训练计划，提取计划类型
            if intent in ["daily_workout_plan", "weekly_training_plan"]:
                parameters["plan_type"] = "daily" if intent == "daily_workout_plan" else "weekly"
        
        if intent in ["search_exercise", "recommend_exercise"]:
            # 提取器材
            equipment = self.extract_equipment(text)
            if equipment:
                parameters["equipment"] = equipment
        
        # 提取训练目标
        goal = self.extract_training_goal(text)
        if goal:
            parameters["training_goal"] = goal
        
        return parameters
    
    def extract_body_parts(self, text: str) -> List[str]:
        """提取身体部位
        
        Args:
            text: 用户输入文本
            
        Returns:
            身体部位列表
        """
        # 使用正则表达式匹配
        matches = self.body_part_pattern.findall(text)
        
        # 如果没有直接匹配，尝试同义词扩展
        if not matches:
            for bp in self.body_part_categories:
                synonyms = self.synonym_expander.expand(bp["name"])
                for synonym in synonyms:
                    if synonym in text:
                        matches.append(bp["name"])
                        break
        
        # 标准化结果
        result = []
        for match in matches:
            name, _ = self.standardize_parameter('body_part', match)
            if name and name not in result:
                result.append(name)
        
        return result
    
    def extract_equipment(self, text: str) -> List[str]:
        """提取训练器械
        
        Args:
            text: 用户输入文本
            
        Returns:
            训练器械列表
        """
        # 使用正则表达式匹配
        matches = self.equipment_pattern.findall(text)
        
        # 如果没有直接匹配，尝试同义词扩展
        if not matches:
            for eq in self.equipment_categories:
                synonyms = self.synonym_expander.expand(eq["name"])
                for synonym in synonyms:
                    if synonym in text:
                        matches.append(eq["name"])
                        break
        
        # 标准化结果
        result = []
        for match in matches:
            name, _ = self.standardize_parameter('equipment', match)
            if name and name not in result:
                result.append(name)
        
        return result
    
    def extract_scenario(self, text: str) -> Optional[str]:
        """提取训练场景
        
        Args:
            text: 用户输入文本
            
        Returns:
            训练场景，如"居家"或"健身房"
        """
        # 检查环境关键词
        for scenario, keywords in ENVIRONMENT_KEYWORDS.items():
            if any(keyword in text for keyword in keywords):
                return scenario
        
        # 使用正则表达式匹配
        matches = self.scenario_pattern.findall(text)
        if matches:
            return matches[0]
        
        return None
    
    def extract_training_goal(self, text: str) -> Optional[str]:
        """提取训练目标
        
        Args:
            text: 用户输入文本
            
        Returns:
            训练目标，如"增肌"或"减脂"
        """
        # 检查目标关键词
        for goal, keywords in GOAL_KEYWORDS.items():
            if any(keyword in text for keyword in keywords):
                return goal
        
        # 使用正则表达式匹配
        matches = self.goal_pattern.findall(text)
        if matches:
            return matches[0]
        
        return None
    
    def standardize_parameter(self, param_type: str, param_value: str) -> Tuple[Optional[str], Optional[int]]:
        """标准化参数值
        
        Args:
            param_type: 参数类型 ('body_part', 'equipment', 'muscle')
            param_value: 参数值
            
        Returns:
            标准化后的参数值和ID
        """
        # 选择适当的类别列表和别名映射
        if param_type == 'body_part':
            categories = self.body_part_categories
            aliases = self.sql_tool.body_part_aliases if self.sql_tool else None
        elif param_type == 'equipment':
            categories = self.equipment_categories
            aliases = self.sql_tool.equipment_aliases if self.sql_tool else None
        elif param_type == 'muscle':
            categories = self.muscle_categories
            aliases = self.sql_tool.muscle_aliases if self.sql_tool else None
        else:
            return None, None
        
        # 使用SQLToolService中的方法获取最佳匹配
        if self.sql_tool:
            param_id, param_name = self.sql_tool._get_best_match_id(param_value, categories, aliases)
            return param_name, param_id
        
        # 如果没有SQLToolService，使用简单匹配
        normalized_value = normalize_text(param_value)
        for category in categories:
            if normalize_text(category["name"]) == normalized_value:
                return category["name"], category["id"]
        
        # 模糊匹配
        best_match = None
        best_score = 0
        
        for category in categories:
            category_name = normalize_text(category.get("name", ""))
            
            # 如果搜索词包含在类别名称中，或类别名称包含在搜索词中
            if normalized_value in category_name or category_name in normalized_value:
                # 计算匹配得分：长度越接近，得分越高
                match_score = min(len(normalized_value), len(category_name)) / max(len(normalized_value), len(category_name))
                if match_score > best_score:
                    best_match = category
                    best_score = match_score
        
        if best_match:
            return best_match.get("name"), best_match.get("id")
        
        return None, None
    
    def standardize_parameters(self, parameters: Dict[str, Any], intent: str) -> Dict[str, Any]:
        """标准化参数
        
        Args:
            parameters: 原始参数字典
            intent: 意图类型
            
        Returns:
            标准化后的参数字典
        """
        standardized = {}
        
        # 处理身体部位
        if "body_part" in parameters:
            body_parts = parameters["body_part"]
            if isinstance(body_parts, str):
                body_parts = [body_parts]
            
            std_body_parts = []
            body_part_ids = []
            
            for bp in body_parts:
                name, id = self.standardize_parameter('body_part', bp)
                if name and name not in std_body_parts:
                    std_body_parts.append(name)
                    if id:
                        body_part_ids.append(id)
            
            standardized["body_part"] = std_body_parts
            standardized["body_part_id"] = body_part_ids
        
        # 处理器材
        if "equipment" in parameters:
            equipment = parameters["equipment"]
            if isinstance(equipment, str):
                equipment = [equipment]
            
            std_equipment = []
            equipment_ids = []
            
            for eq in equipment:
                name, id = self.standardize_parameter('equipment', eq)
                if name and name not in std_equipment:
                    std_equipment.append(name)
                    if id:
                        equipment_ids.append(id)
            
            standardized["equipment"] = std_equipment
            standardized["equipment_id"] = equipment_ids
        
        # 处理训练场景
        if "scenario" in parameters:
            scenario = parameters["scenario"]
            if scenario == "居家":
                # 居家场景默认使用自重器材
                if "equipment" not in standardized:
                    name, id = self.standardize_parameter('equipment', "自重")
                    if name:
                        standardized["equipment"] = [name]
                        standardized["equipment_id"] = [id] if id else []
            
            standardized["scenario"] = scenario
        
        # 处理计划类型
        if "plan_type" in parameters:
            standardized["plan_type"] = parameters["plan_type"]
        
        # 处理训练目标
        if "training_goal" in parameters:
            standardized["training_goal"] = parameters["training_goal"]
        
        return standardized
