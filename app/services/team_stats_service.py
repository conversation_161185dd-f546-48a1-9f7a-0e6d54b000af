from sqlalchemy.orm import Session
from sqlalchemy.future import select
from sqlalchemy import func, and_, or_
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from app.models.team import (
    Team, TeamMembership, TeamStats, TeamRole, TeamStatus, 
    MembershipStatus, ClientRelation, ClientStatus,
    ClientTrainingPlan, TrainingSession
)
from app.models.user import User
from app.services.team_service import TeamService, TeamServiceException

class TeamStatsService:
    """团队统计服务，处理团队统计相关业务逻辑"""
    
    def __init__(self, db: Session):
        """初始化
        
        Args:
            db (Session): 数据库会话
        """
        self.db = db
        self.team_service = TeamService(db)
    
    async def get_team_stats(self, team_id: int, current_user: User) -> Dict[str, Any]:
        """获取团队统计数据
        
        Args:
            team_id (int): 团队ID
            current_user (User): 当前用户
            
        Returns:
            Dict[str, Any]: 统计数据
        """
        # 检查访问权限
        has_access = await self.team_service.check_team_access(team_id, current_user.id)
        if not has_access:
            raise TeamServiceException("No access to this team")
        
        # 获取团队统计对象
        query = select(TeamStats).filter(TeamStats.team_id == team_id)
        result = await self.db.execute(query)
        stats = result.scalars().first()
        
        if not stats:
            # 如果统计对象不存在，创建并计算
            stats = await self.update_team_stats(team_id)
        
        # 构建统计数据
        stats_dict = {
            "total_members": stats.total_members,
            "total_clients": stats.total_clients,
            "active_clients_30d": stats.active_clients_30d,
            "total_sessions": stats.total_sessions,
            "completed_sessions": stats.completed_sessions,
            "completion_rate": stats.completion_rate,
            "growth_rate": stats.growth_rate,
            "monthly_stats": stats.monthly_stats or {},
            "updated_at": stats.updated_at
        }
        
        # 添加额外的统计数据
        stats_dict.update(await self._get_additional_stats(team_id))
        
        return stats_dict
    
    async def update_team_stats(self, team_id: int) -> TeamStats:
        """更新团队统计数据
        
        Args:
            team_id (int): 团队ID
            
        Returns:
            TeamStats: 更新后的统计数据
        """
        # 获取团队统计对象
        query = select(TeamStats).filter(TeamStats.team_id == team_id)
        result = await self.db.execute(query)
        stats = result.scalars().first()
        
        if not stats:
            stats = TeamStats(team_id=team_id)
            self.db.add(stats)
        
        # 计算成员数量
        member_query = select(func.count()).select_from(TeamMembership).filter(
            TeamMembership.team_id == team_id,
            TeamMembership.status == MembershipStatus.ACTIVE
        )
        member_result = await self.db.execute(member_query)
        stats.total_members = member_result.scalar() or 0
        
        # 计算客户数量
        client_query = select(func.count()).select_from(ClientRelation).filter(
            ClientRelation.team_id == team_id,
            ClientRelation.status == ClientStatus.ACTIVE
        )
        client_result = await self.db.execute(client_query)
        stats.total_clients = client_result.scalar() or 0
        
        # 计算30天内活跃客户数量
        thirty_days_ago = datetime.now() - timedelta(days=30)
        active_client_query = select(func.count(ClientRelation.id.distinct())).select_from(ClientRelation).join(
            ClientTrainingPlan,
            ClientTrainingPlan.client_relation_id == ClientRelation.id
        ).filter(
            ClientRelation.team_id == team_id,
            ClientRelation.status == ClientStatus.ACTIVE,
            ClientTrainingPlan.updated_at >= thirty_days_ago
        )
        active_client_result = await self.db.execute(active_client_query)
        stats.active_clients_30d = active_client_result.scalar() or 0
        
        # 计算训练课程数量
        session_query = select(func.count()).select_from(TrainingSession).join(
            ClientTrainingPlan,
            ClientTrainingPlan.id == TrainingSession.client_plan_id
        ).join(
            ClientRelation,
            ClientRelation.id == ClientTrainingPlan.client_relation_id
        ).filter(
            ClientRelation.team_id == team_id
        )
        session_result = await self.db.execute(session_query)
        stats.total_sessions = session_result.scalar() or 0
        
        # 计算已完成的训练课程数量
        completed_session_query = select(func.count()).select_from(TrainingSession).join(
            ClientTrainingPlan,
            ClientTrainingPlan.id == TrainingSession.client_plan_id
        ).join(
            ClientRelation,
            ClientRelation.id == ClientTrainingPlan.client_relation_id
        ).filter(
            ClientRelation.team_id == team_id,
            TrainingSession.status == "completed"
        )
        completed_session_result = await self.db.execute(completed_session_query)
        stats.completed_sessions = completed_session_result.scalar() or 0
        
        # 计算完成率
        if stats.total_sessions > 0:
            stats.completion_rate = stats.completed_sessions / stats.total_sessions
        else:
            stats.completion_rate = 0
        
        # 计算增长率
        # 这里简化处理，实际应该比较上个月的数据
        stats.growth_rate = 0
        
        # 更新月度统计
        stats.monthly_stats = await self._calculate_monthly_stats(team_id)
        
        # 更新时间
        stats.updated_at = datetime.now()
        
        self.db.add(stats)
        await self.db.commit()
        await self.db.refresh(stats)
        
        return stats
    
    async def _calculate_monthly_stats(self, team_id: int) -> Dict[str, Any]:
        """计算月度统计数据
        
        Args:
            team_id (int): 团队ID
            
        Returns:
            Dict[str, Any]: 月度统计数据
        """
        # 获取当前月份
        now = datetime.now()
        current_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        
        # 获取过去6个月的月份
        months = []
        for i in range(6):
            month = current_month - timedelta(days=30 * i)
            months.append(month.strftime("%Y-%m"))
        
        # 初始化月度统计
        monthly_stats = {month: {"new_clients": 0, "sessions": 0, "completion_rate": 0} for month in months}
        
        # 计算每月新增客户数量
        for i, month in enumerate(months):
            month_start = datetime.strptime(month, "%Y-%m")
            if i == 0:
                month_end = now
            else:
                next_month = datetime.strptime(months[i-1], "%Y-%m")
                month_end = next_month
            
            # 新增客户
            new_client_query = select(func.count()).select_from(ClientRelation).filter(
                ClientRelation.team_id == team_id,
                ClientRelation.created_at >= month_start,
                ClientRelation.created_at < month_end
            )
            new_client_result = await self.db.execute(new_client_query)
            monthly_stats[month]["new_clients"] = new_client_result.scalar() or 0
            
            # 训练课程
            session_query = select(func.count()).select_from(TrainingSession).join(
                ClientTrainingPlan,
                ClientTrainingPlan.id == TrainingSession.client_plan_id
            ).join(
                ClientRelation,
                ClientRelation.id == ClientTrainingPlan.client_relation_id
            ).filter(
                ClientRelation.team_id == team_id,
                TrainingSession.scheduled_start >= month_start,
                TrainingSession.scheduled_start < month_end
            )
            session_result = await self.db.execute(session_query)
            monthly_stats[month]["sessions"] = session_result.scalar() or 0
            
            # 完成率
            completed_session_query = select(func.count()).select_from(TrainingSession).join(
                ClientTrainingPlan,
                ClientTrainingPlan.id == TrainingSession.client_plan_id
            ).join(
                ClientRelation,
                ClientRelation.id == ClientTrainingPlan.client_relation_id
            ).filter(
                ClientRelation.team_id == team_id,
                TrainingSession.scheduled_start >= month_start,
                TrainingSession.scheduled_start < month_end,
                TrainingSession.status == "completed"
            )
            completed_session_result = await self.db.execute(completed_session_query)
            completed_sessions = completed_session_result.scalar() or 0
            
            if monthly_stats[month]["sessions"] > 0:
                monthly_stats[month]["completion_rate"] = completed_sessions / monthly_stats[month]["sessions"]
            else:
                monthly_stats[month]["completion_rate"] = 0
        
        return monthly_stats
    
    async def _get_additional_stats(self, team_id: int) -> Dict[str, Any]:
        """获取额外的统计数据
        
        Args:
            team_id (int): 团队ID
            
        Returns:
            Dict[str, Any]: 额外的统计数据
        """
        additional_stats = {}
        
        # 计算教练数量
        coach_query = select(func.count()).select_from(TeamMembership).filter(
            TeamMembership.team_id == team_id,
            TeamMembership.status == MembershipStatus.ACTIVE,
            TeamMembership.role == TeamRole.COACH
        )
        coach_result = await self.db.execute(coach_query)
        additional_stats["total_coaches"] = coach_result.scalar() or 0
        
        # 计算每个教练的客户数量
        coach_client_query = select(
            TeamMembership.user_id,
            func.count(ClientRelation.id).label("client_count")
        ).select_from(TeamMembership).outerjoin(
            ClientRelation,
            and_(
                ClientRelation.team_id == TeamMembership.team_id,
                ClientRelation.coach_id == TeamMembership.user_id,
                ClientRelation.status == ClientStatus.ACTIVE
            )
        ).filter(
            TeamMembership.team_id == team_id,
            TeamMembership.status == MembershipStatus.ACTIVE,
            TeamMembership.role == TeamRole.COACH
        ).group_by(TeamMembership.user_id)
        
        coach_client_result = await self.db.execute(coach_client_query)
        coach_clients = []
        
        for coach_id, client_count in coach_client_result:
            # 获取教练信息
            coach_query = select(User).filter(User.id == coach_id)
            coach_result = await self.db.execute(coach_query)
            coach = coach_result.scalars().first()
            
            if coach:
                coach_clients.append({
                    "coach_id": coach_id,
                    "coach_name": coach.nickname,
                    "client_count": client_count
                })
        
        additional_stats["coach_clients"] = coach_clients
        
        # 计算最近的训练课程
        recent_session_query = select(
            TrainingSession,
            ClientTrainingPlan,
            ClientRelation
        ).join(
            ClientTrainingPlan,
            ClientTrainingPlan.id == TrainingSession.client_plan_id
        ).join(
            ClientRelation,
            ClientRelation.id == ClientTrainingPlan.client_relation_id
        ).filter(
            ClientRelation.team_id == team_id,
            TrainingSession.scheduled_start >= datetime.now() - timedelta(days=7)
        ).order_by(TrainingSession.scheduled_start.desc()).limit(5)
        
        recent_session_result = await self.db.execute(recent_session_query)
        recent_sessions = []
        
        for session, plan, relation in recent_session_result:
            # 获取客户信息
            client_query = select(User).filter(User.id == relation.client_id)
            client_result = await self.db.execute(client_query)
            client = client_result.scalars().first()
            
            # 获取教练信息
            coach_query = select(User).filter(User.id == relation.coach_id)
            coach_result = await self.db.execute(coach_query)
            coach = coach_result.scalars().first()
            
            if client and coach:
                recent_sessions.append({
                    "session_id": session.id,
                    "scheduled_start": session.scheduled_start,
                    "status": session.status,
                    "client_name": client.nickname,
                    "coach_name": coach.nickname
                })
        
        additional_stats["recent_sessions"] = recent_sessions
        
        return additional_stats
    
    async def get_client_stats(self, client_relation_id: int, current_user: User) -> Dict[str, Any]:
        """获取客户统计数据
        
        Args:
            client_relation_id (int): 客户关系ID
            current_user (User): 当前用户
            
        Returns:
            Dict[str, Any]: 统计数据
        """
        # 获取客户关系
        client_relation_query = select(ClientRelation).filter(ClientRelation.id == client_relation_id)
        client_relation_result = await self.db.execute(client_relation_query)
        client_relation = client_relation_result.scalars().first()
        
        if not client_relation:
            raise TeamServiceException(f"Client relation with ID {client_relation_id} not found")
        
        # 检查访问权限
        if current_user.id != client_relation.client_id and current_user.id != client_relation.coach_id:
            has_access = await self.team_service.check_team_access(client_relation.team_id, current_user.id)
            if not has_access:
                raise TeamServiceException("No access to this client")
        
        # 计算统计数据
        stats = {}
        
        # 计算训练计划数量
        plan_query = select(func.count()).select_from(ClientTrainingPlan).filter(
            ClientTrainingPlan.client_relation_id == client_relation_id
        )
        plan_result = await self.db.execute(plan_query)
        stats["total_plans"] = plan_result.scalar() or 0
        
        # 计算已完成的训练计划数量
        completed_plan_query = select(func.count()).select_from(ClientTrainingPlan).filter(
            ClientTrainingPlan.client_relation_id == client_relation_id,
            ClientTrainingPlan.status == "completed"
        )
        completed_plan_result = await self.db.execute(completed_plan_query)
        stats["completed_plans"] = completed_plan_result.scalar() or 0
        
        # 计算训练课程数量
        session_query = select(func.count()).select_from(TrainingSession).join(
            ClientTrainingPlan,
            ClientTrainingPlan.id == TrainingSession.client_plan_id
        ).filter(
            ClientTrainingPlan.client_relation_id == client_relation_id
        )
        session_result = await self.db.execute(session_query)
        stats["total_sessions"] = session_result.scalar() or 0
        
        # 计算已完成的训练课程数量
        completed_session_query = select(func.count()).select_from(TrainingSession).join(
            ClientTrainingPlan,
            ClientTrainingPlan.id == TrainingSession.client_plan_id
        ).filter(
            ClientTrainingPlan.client_relation_id == client_relation_id,
            TrainingSession.status == "completed"
        )
        completed_session_result = await self.db.execute(completed_session_query)
        stats["completed_sessions"] = completed_session_result.scalar() or 0
        
        # 计算完成率
        if stats["total_sessions"] > 0:
            stats["completion_rate"] = stats["completed_sessions"] / stats["total_sessions"]
        else:
            stats["completion_rate"] = 0
        
        # 获取最近的训练课程
        recent_session_query = select(
            TrainingSession,
            ClientTrainingPlan
        ).join(
            ClientTrainingPlan,
            ClientTrainingPlan.id == TrainingSession.client_plan_id
        ).filter(
            ClientTrainingPlan.client_relation_id == client_relation_id
        ).order_by(TrainingSession.scheduled_start.desc()).limit(5)
        
        recent_session_result = await self.db.execute(recent_session_query)
        recent_sessions = []
        
        for session, plan in recent_session_result:
            recent_sessions.append({
                "session_id": session.id,
                "scheduled_start": session.scheduled_start,
                "status": session.status,
                "completion_rate": session.completion_rate,
                "plan_id": plan.id
            })
        
        stats["recent_sessions"] = recent_sessions
        
        # 获取转移历史
        transfer_query = select(
            ClientTransferHistory,
            User.nickname.label("from_coach_name"),
            User.nickname.label("to_coach_name")
        ).join(
            User,
            User.id == ClientTransferHistory.from_coach_id,
            isouter=True
        ).join(
            User,
            User.id == ClientTransferHistory.to_coach_id,
            isouter=True
        ).filter(
            ClientTransferHistory.client_relation_id == client_relation_id
        ).order_by(ClientTransferHistory.transferred_at.desc())
        
        transfer_result = await self.db.execute(transfer_query)
        transfer_history = []
        
        for history, from_coach_name, to_coach_name in transfer_result:
            transfer_history.append({
                "id": history.id,
                "from_coach_id": history.from_coach_id,
                "from_coach_name": from_coach_name,
                "to_coach_id": history.to_coach_id,
                "to_coach_name": to_coach_name,
                "reason": history.reason,
                "transferred_at": history.transferred_at
            })
        
        stats["transfer_history"] = transfer_history
        
        return stats
    
    async def calculate_team_growth(self, team_id: int) -> Dict[str, Any]:
        """计算团队增长数据
        
        Args:
            team_id (int): 团队ID
            
        Returns:
            Dict[str, Any]: 增长数据
        """
        # 获取当前月份
        now = datetime.now()
        current_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        last_month = current_month - timedelta(days=30)
        
        # 计算当前月新增客户数量
        current_month_client_query = select(func.count()).select_from(ClientRelation).filter(
            ClientRelation.team_id == team_id,
            ClientRelation.created_at >= current_month,
            ClientRelation.created_at < now
        )
        current_month_client_result = await self.db.execute(current_month_client_query)
        current_month_clients = current_month_client_result.scalar() or 0
        
        # 计算上月新增客户数量
        last_month_client_query = select(func.count()).select_from(ClientRelation).filter(
            ClientRelation.team_id == team_id,
            ClientRelation.created_at >= last_month,
            ClientRelation.created_at < current_month
        )
        last_month_client_result = await self.db.execute(last_month_client_query)
        last_month_clients = last_month_client_result.scalar() or 0
        
        # 计算增长率
        if last_month_clients > 0:
            client_growth_rate = (current_month_clients - last_month_clients) / last_month_clients
        else:
            client_growth_rate = 0 if current_month_clients == 0 else 1
        
        # 计算当前月训练课程数量
        current_month_session_query = select(func.count()).select_from(TrainingSession).join(
            ClientTrainingPlan,
            ClientTrainingPlan.id == TrainingSession.client_plan_id
        ).join(
            ClientRelation,
            ClientRelation.id == ClientTrainingPlan.client_relation_id
        ).filter(
            ClientRelation.team_id == team_id,
            TrainingSession.scheduled_start >= current_month,
            TrainingSession.scheduled_start < now
        )
        current_month_session_result = await self.db.execute(current_month_session_query)
        current_month_sessions = current_month_session_result.scalar() or 0
        
        # 计算上月训练课程数量
        last_month_session_query = select(func.count()).select_from(TrainingSession).join(
            ClientTrainingPlan,
            ClientTrainingPlan.id == TrainingSession.client_plan_id
        ).join(
            ClientRelation,
            ClientRelation.id == ClientTrainingPlan.client_relation_id
        ).filter(
            ClientRelation.team_id == team_id,
            TrainingSession.scheduled_start >= last_month,
            TrainingSession.scheduled_start < current_month
        )
        last_month_session_result = await self.db.execute(last_month_session_query)
        last_month_sessions = last_month_session_result.scalar() or 0
        
        # 计算增长率
        if last_month_sessions > 0:
            session_growth_rate = (current_month_sessions - last_month_sessions) / last_month_sessions
        else:
            session_growth_rate = 0 if current_month_sessions == 0 else 1
        
        # 更新团队统计对象
        query = select(TeamStats).filter(TeamStats.team_id == team_id)
        result = await self.db.execute(query)
        stats = result.scalars().first()
        
        if stats:
            stats.growth_rate = client_growth_rate
            self.db.add(stats)
            await self.db.commit()
        
        return {
            "client_growth_rate": client_growth_rate,
            "session_growth_rate": session_growth_rate,
            "current_month_clients": current_month_clients,
            "last_month_clients": last_month_clients,
            "current_month_sessions": current_month_sessions,
            "last_month_sessions": last_month_sessions
        }
