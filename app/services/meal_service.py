from typing import Dict, Any, List, Optional
from datetime import date

from sqlalchemy.orm import Session
import logging
from app import crud, models
# 明确导入meal模块中的模型
from app.schemas.meal import FoodItemCreate, DailyNutritionSummary, MealRecordSummary
import re
logger = logging.getLogger(__name__)
# 添加URL转换函数，与meal.py中保持一致
def convert_to_simplified_image_url(original_url: str) -> str:
    """
    将完整的食物图片URL转换为简化版本
    原始格式: /food/images/users/.../food_recognition/.../filename.jpg
    简化格式: /meal-images/filename.jpg
    
    注意：
    - 对于存储在/data/users下的餐食识别图片，使用简化格式
    - 对于存储在/data/food下的食物项图片，保留完整路径
    """
    if not original_url:
        return original_url
    
    # 检查是否是食物项图片路径(存储在/data/food中)
    if '/data/food/' in original_url or original_url.startswith('/food/'):
        # 食物项图片保留原路径
        return original_url
        
    # 提取文件名
    match = re.search(r'([^/]+\.(jpg|jpeg|png|gif))$', original_url)
    if match:
        filename = match.group(1)
        return f"/meal-images/{filename}"
    
    return original_url


class MealService:
    """餐食相关业务逻辑服务"""
    
    @staticmethod
    def add_food_item_to_meal(
        db: Session, 
        meal_id: int, 
        food_item_in: FoodItemCreate
    ) -> models.FoodItem:
        """添加食物项到餐食记录，同时处理营养素计算"""
        
        # 创建基础食物项
        db_food_item = models.FoodItem(
            meal_record_id=meal_id,
            name=food_item_in.name,
            quantity=food_item_in.quantity,
            unit_name=food_item_in.unit_name,
            weight=food_item_in.weight,
            category=food_item_in.category,
            cuisine_type=food_item_in.cuisine_type,
            cuisine_type_detail=food_item_in.cuisine_type_detail,
            image_url=food_item_in.image_url,
            is_custom=food_item_in.is_custom
        )
        
        # 如果有关联到食品库
        if food_item_in.food_id:
            # 获取食品库中的食物数据
            food = crud.food.get(db, id=food_item_in.food_id)
            if food:
                db_food_item.food_id = food.id
                
                # 获取食物的营养概况
                if food.nutritional_profile:
                    profile = food.nutritional_profile
                    
                    # 复制健康信号灯数据
                    db_food_item.health_light = profile.health_light
                    db_food_item.lights = profile.lights
                    db_food_item.warnings = profile.warnings
                    db_food_item.warning_scenes = profile.warning_scenes
                    
                    # 能量分配比例
                    db_food_item.protein_fraction = profile.protein_fraction
                    db_food_item.fat_fraction = profile.fat_fraction
                    db_food_item.carb_fraction = profile.carb_fraction
                    
                    # 获取指定单位的重量转换
                    unit = None
                    for u in food.units:
                        if u.unit_name == food_item_in.unit_name:
                            unit = u
                            break
                    
                    # 计算营养素摄入量
                    base_weight = 100  # 默认基准重量(100克)
                    if unit:
                        base_weight = unit.weight
                    
                    # 计算转换因子
                    conversion_factor = food_item_in.weight / base_weight * food_item_in.quantity
                    
                    # 计算卡路里和宏量营养素
                    if profile.calory is not None:
                        db_food_item.calory = profile.calory * conversion_factor
                    
                    # 计算蛋白质、脂肪、碳水的克数
                    # 假设calory有值，否则无法计算
                    if profile.calory is not None:
                        if profile.protein_fraction is not None:
                            # 蛋白质1克 = 4千卡
                            db_food_item.protein = (profile.calory * profile.protein_fraction / 100 / 4) * conversion_factor
                        
                        if profile.fat_fraction is not None:
                            # 脂肪1克 = 9千卡
                            db_food_item.fat = (profile.calory * profile.fat_fraction / 100 / 9) * conversion_factor
                        
                        if profile.carb_fraction is not None:
                            # 碳水1克 = 4千卡
                            db_food_item.carbohydrate = (profile.calory * profile.carb_fraction / 100 / 4) * conversion_factor
                    
                    # 处理详细营养素摄入
                    for nutrient in food.nutrients:
                        if nutrient.value is not None:
                            # 创建营养素摄入记录
                            nutrient_intake = models.FoodItemNutrientIntake(
                                name_en=nutrient.name_en,
                                name_cn=nutrient.name_cn,
                                value=nutrient.value * conversion_factor,
                                unit=nutrient.unit,
                                unit_name=nutrient.unit_name,
                                nrv_percentage=nutrient.nrv * conversion_factor if nutrient.nrv else None,
                                category=nutrient.category
                            )
                            db_food_item.nutrient_intakes.append(nutrient_intake)
        else:
            # 自定义食物，直接使用用户提供的营养数据
            db_food_item.health_light = food_item_in.health_light
            db_food_item.lights = food_item_in.lights
            db_food_item.warnings = food_item_in.warnings
            db_food_item.warning_scenes = food_item_in.warning_scenes
            db_food_item.calory = food_item_in.calory
            db_food_item.protein = food_item_in.protein
            db_food_item.fat = food_item_in.fat
            db_food_item.carbohydrate = food_item_in.carbohydrate
            db_food_item.protein_fraction = food_item_in.protein_fraction
            db_food_item.fat_fraction = food_item_in.fat_fraction
            db_food_item.carb_fraction = food_item_in.carb_fraction
            
            # 处理自定义营养素数据
            if food_item_in.nutrient_intakes:
                for intake_in in food_item_in.nutrient_intakes:
                    nutrient_intake = models.FoodItemNutrientIntake(**intake_in.dict())
                    db_food_item.nutrient_intakes.append(nutrient_intake)
        
        # 保存食物项
        db.add(db_food_item)
        db.commit()
        db.refresh(db_food_item)
        
        return db_food_item
    
    @staticmethod
    def update_meal_nutrition_totals(db: Session, meal_id: int) -> models.MealRecord:
        """更新餐食记录的总营养值"""
        meal = crud.meal.get(db=db, id=meal_id)
        if not meal:
            return None
        
        # 重新计算总营养值
        total_calory = 0
        total_protein = 0
        total_fat = 0
        total_carbohydrate = 0
        
        for food_item in meal.food_items:
            if food_item.calory:
                total_calory += food_item.calory
            if food_item.protein:
                total_protein += food_item.protein
            if food_item.fat:
                total_fat += food_item.fat
            if food_item.carbohydrate:
                total_carbohydrate += food_item.carbohydrate
        
        # 更新餐食记录
        meal.total_calory = total_calory
        meal.total_protein = total_protein
        meal.total_fat = total_fat
        meal.total_carbohydrate = total_carbohydrate
        
        db.add(meal)
        db.commit()
        db.refresh(meal)
        
        return meal
    
    @staticmethod
    def get_daily_nutrition(db: Session, user_id: str, query_date: date) -> DailyNutritionSummary:
        """获取用户某天的营养摄入汇总"""
        # 获取该日期的所有餐食记录
        meals = crud.meal.get_user_meals_by_date(
            db=db, 
            user_id=user_id, 
            date=query_date
        )
        
        # 计算总营养值
        total_calory = 0
        total_protein = 0
        total_fat = 0
        total_carbohydrate = 0
        
        meal_summaries = []
        
        for meal in meals:
            # 过滤掉总卡路里为0的餐食记录
            if meal.total_calory == 0:
                continue
                
            # 转换图片URL为简化格式
            image_url = meal.image_url
            if image_url:
                image_url = convert_to_simplified_image_url(image_url)
            
            # 转换缩略图URL为简化格式
            thumb_image_url = meal.thumb_image_url
            if thumb_image_url:
                thumb_image_url = convert_to_simplified_image_url(thumb_image_url)

            # 将每个餐食添加到列表
            meal_summary = MealRecordSummary(
                id=meal.id,
                date=meal.date,
                meal_type=meal.meal_type,
                image_url=image_url,
                thumb_image_url=thumb_image_url,
                total_calory=meal.total_calory,
                total_protein=meal.total_protein,
                total_fat=meal.total_fat,
                total_carbohydrate=meal.total_carbohydrate
            )
            meal_summaries.append(meal_summary)
            
            # 累加总营养值
            total_calory += meal.total_calory
            total_protein += meal.total_protein
            total_fat += meal.total_fat
            total_carbohydrate += meal.total_carbohydrate
        
        # 构建日营养摄入汇总
        return DailyNutritionSummary(
            date=query_date,
            total_calory=total_calory,
            total_protein=total_protein,
            total_fat=total_fat,
            total_carbohydrate=total_carbohydrate,
            meals=meal_summaries
        ) 