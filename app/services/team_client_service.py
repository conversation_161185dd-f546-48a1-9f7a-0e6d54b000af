from sqlalchemy.orm import Session
from sqlalchemy.future import select
from typing import List, Optional, Dict, Any
from datetime import datetime

from app.models.team import (
    Team, TeamMembership, TeamRole, ClientRelation, 
    ClientStatus, ClientTransferHistory
)
from app.models.user import User
from app.schemas.team import (
    ClientAssignment, ClientTransfer, ClientRelationResponse
)
from app.crud.team import (
    assign_client, get_client_relation, transfer_client, 
    get_team_clients, get_client_with_stats
)
from app.services.team_service import TeamService, TeamServiceException

class ClientRelationNotFoundException(TeamServiceException):
    """客户关系不存在异常"""
    pass

class TeamClientService:
    """团队客户服务，处理团队客户相关业务逻辑"""
    
    def __init__(self, db: Session):
        """初始化
        
        Args:
            db (Session): 数据库会话
        """
        self.db = db
        self.team_service = TeamService(db)
    
    async def assign_client(self, team_id: int, client_data: ClientAssignment, current_user: User) -> ClientRelation:
        """分配客户
        
        Args:
            team_id (int): 团队ID
            client_data (ClientAssignment): 客户分配数据
            current_user (User): 当前用户
            
        Returns:
            ClientRelation: 创建的客户关系
        """
        # 检查权限
        await self.team_service._check_team_permission(team_id, current_user.id, [TeamRole.OWNER, TeamRole.ADMIN])
        
        # 检查客户是否已经分配
        existing_relation = await self._get_client_relation_by_client(team_id, client_data.client_id)
        if existing_relation and existing_relation.status == ClientStatus.ACTIVE:
            raise TeamServiceException(f"Client {client_data.client_id} is already assigned in team {team_id}")
        
        # 分配客户
        client_relation = await assign_client(self.db, team_id, client_data)
        
        # 更新团队统计
        await self.team_service.update_team_stats(team_id)
        
        return client_relation
    
    async def get_client_relation(self, relation_id: int) -> Optional[ClientRelation]:
        """获取客户关系
        
        Args:
            relation_id (int): 关系ID
            
        Returns:
            Optional[ClientRelation]: 客户关系对象，如果不存在则返回None
        """
        relation = await get_client_relation(self.db, relation_id)
        if not relation:
            raise ClientRelationNotFoundException(f"Client relation with ID {relation_id} not found")
        return relation
    
    async def _get_client_relation_by_client(self, team_id: int, client_id: int) -> Optional[ClientRelation]:
        """根据客户ID获取客户关系
        
        Args:
            team_id (int): 团队ID
            client_id (int): 客户ID
            
        Returns:
            Optional[ClientRelation]: 客户关系对象，如果不存在则返回None
        """
        query = select(ClientRelation).filter(
            ClientRelation.team_id == team_id,
            ClientRelation.client_id == client_id
        )
        result = await self.db.execute(query)
        return result.scalars().first()
    
    async def transfer_client(self, relation_id: int, transfer_data: ClientTransfer, current_user: User) -> ClientRelation:
        """转移客户
        
        Args:
            relation_id (int): 关系ID
            transfer_data (ClientTransfer): 转移数据
            current_user (User): 当前用户
            
        Returns:
            ClientRelation: 更新后的客户关系
        """
        # 获取客户关系
        relation = await self.get_client_relation(relation_id)
        
        # 检查权限
        await self.team_service._check_team_permission(relation.team_id, current_user.id, [TeamRole.OWNER, TeamRole.ADMIN])
        
        # 检查新教练是否在团队中
        coach_membership = await self.db.execute(
            select(TeamMembership).filter(
                TeamMembership.team_id == relation.team_id,
                TeamMembership.user_id == transfer_data.new_coach_id,
                TeamMembership.status == MembershipStatus.ACTIVE
            )
        )
        if not coach_membership.scalars().first():
            raise TeamServiceException(f"User {transfer_data.new_coach_id} is not a member of team {relation.team_id}")
        
        # 转移客户
        updated_relation = await self._transfer_client_with_history(relation, transfer_data.new_coach_id, transfer_data.reason)
        
        return updated_relation
    
    async def _transfer_client_with_history(self, client_relation: ClientRelation, new_coach_id: int, reason: Optional[str] = None) -> ClientRelation:
        """带历史记录的客户转移
        
        Args:
            client_relation (ClientRelation): 客户关系
            new_coach_id (int): 新教练ID
            reason (Optional[str]): 转移原因
            
        Returns:
            ClientRelation: 更新后的客户关系
        """
        async with self.db.begin():
            # 记录转移历史
            history = ClientTransferHistory(
                client_relation_id=client_relation.id,
                from_coach_id=client_relation.coach_id,
                to_coach_id=new_coach_id,
                reason=reason,
                transferred_at=datetime.now()
            )
            self.db.add(history)
            
            # 更新关系
            client_relation.coach_id = new_coach_id
            client_relation.updated_at = datetime.now()
            self.db.add(client_relation)
        
        return client_relation
    
    async def get_team_clients(self, team_id: int, status: Optional[ClientStatus] = None, coach_id: Optional[int] = None, current_user: User = None) -> List[Dict[str, Any]]:
        """获取团队客户列表
        
        Args:
            team_id (int): 团队ID
            status (Optional[ClientStatus]): 状态过滤
            coach_id (Optional[int]): 教练ID过滤
            current_user (User): 当前用户
            
        Returns:
            List[Dict[str, Any]]: 客户列表
        """
        # 检查访问权限
        if current_user:
            has_access = await self.team_service.check_team_access(team_id, current_user.id)
            if not has_access:
                raise TeamServiceException("No access to this team")
        
        return await get_team_clients(self.db, team_id, status, coach_id)
    
    async def get_client_detail(self, relation_id: int, current_user: User) -> Dict[str, Any]:
        """获取客户详情
        
        Args:
            relation_id (int): 关系ID
            current_user (User): 当前用户
            
        Returns:
            Dict[str, Any]: 客户详情
        """
        # 获取客户关系
        relation = await self.get_client_relation(relation_id)
        
        # 检查访问权限
        has_access = await self.team_service.check_team_access(relation.team_id, current_user.id)
        if not has_access:
            raise TeamServiceException("No access to this client")
        
        return await get_client_with_stats(self.db, relation_id, current_user.id)
    
    async def deactivate_client(self, relation_id: int, current_user: User) -> ClientRelation:
        """停用客户
        
        Args:
            relation_id (int): 关系ID
            current_user (User): 当前用户
            
        Returns:
            ClientRelation: 更新后的客户关系
        """
        # 获取客户关系
        relation = await self.get_client_relation(relation_id)
        
        # 检查权限
        await self.team_service._check_team_permission(relation.team_id, current_user.id, [TeamRole.OWNER, TeamRole.ADMIN])
        
        # 停用客户
        relation.status = ClientStatus.INACTIVE
        relation.updated_at = datetime.now()
        self.db.add(relation)
        await self.db.commit()
        await self.db.refresh(relation)
        
        # 更新团队统计
        await self.team_service.update_team_stats(relation.team_id)
        
        return relation
    
    async def reactivate_client(self, relation_id: int, current_user: User) -> ClientRelation:
        """重新激活客户
        
        Args:
            relation_id (int): 关系ID
            current_user (User): 当前用户
            
        Returns:
            ClientRelation: 更新后的客户关系
        """
        # 获取客户关系
        relation = await self.get_client_relation(relation_id)
        
        # 检查权限
        await self.team_service._check_team_permission(relation.team_id, current_user.id, [TeamRole.OWNER, TeamRole.ADMIN])
        
        # 重新激活客户
        relation.status = ClientStatus.ACTIVE
        relation.updated_at = datetime.now()
        self.db.add(relation)
        await self.db.commit()
        await self.db.refresh(relation)
        
        # 更新团队统计
        await self.team_service.update_team_stats(relation.team_id)
        
        return relation
