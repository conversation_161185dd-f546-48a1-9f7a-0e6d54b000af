"""
用户资料管理器 - 负责管理用户信息的收集、验证和更新
"""
from typing import Dict, Any, List, Optional, Tuple
import logging
import re
import json
from app.services.llm_proxy_service import LLMProxyService
from app.core.chat_config import MODELS

# 配置日志
logger = logging.getLogger(__name__)

# 全局LLM实例
llm_service = LLMProxyService()

class UserProfileManager:
    """用户资料管理器类，负责管理用户信息的收集、验证和更新"""
    
    # 必要的用户信息字段
    REQUIRED_FIELDS = [
        "gender",       # 性别
        "age",          # 年龄
        "height",       # 身高(cm)
        "weight",       # 体重(kg)
        "fitness_goal", # 健身目标
        "fitness_level" # 健身水平
    ]
    
    # 可选的用户信息字段
    OPTIONAL_FIELDS = [
        "name",                # 姓名
        "activity_level",      # 活动水平
        "training_frequency",  # 训练频率
        "medical_conditions",  # 健康状况
        "dietary_restrictions" # 饮食限制
    ]
    
    # 字段验证规则
    FIELD_VALIDATION = {
        "gender": {
            "type": "enum",
            "values": ["男", "女", "其他", "male", "female", "other"]
        },
        "age": {
            "type": "int",
            "min": 12,
            "max": 100
        },
        "height": {
            "type": "float",
            "min": 100,
            "max": 250,
            "unit": "cm"
        },
        "weight": {
            "type": "float",
            "min": 30,
            "max": 300,
            "unit": "kg"
        },
        "fitness_goal": {
            "type": "enum",
            "values": ["增肌", "减脂", "力量", "耐力", "塑形", "健康", "muscle gain", "fat loss", "strength", "endurance", "toning", "health"]
        },
        "fitness_level": {
            "type": "enum",
            "values": ["初级", "中级", "高级", "beginner", "intermediate", "advanced"]
        },
        "activity_level": {
            "type": "enum",
            "values": ["久坐", "轻度活动", "中度活动", "高度活动", "sedentary", "lightly active", "moderately active", "very active"]
        }
    }
    
    # 字段提示信息
    FIELD_PROMPTS = {
        "gender": "您的性别是？(男/女/其他)",
        "age": "您的年龄是？",
        "height": "您的身高是多少厘米？",
        "weight": "您的体重是多少公斤？",
        "fitness_goal": "您的健身目标是什么？(增肌/减脂/力量/耐力/塑形/健康)",
        "fitness_level": "您的健身水平如何？(初级/中级/高级)",
        "activity_level": "您的日常活动水平如何？(久坐/轻度活动/中度活动/高度活动)",
        "training_frequency": "您每周训练几次？",
        "medical_conditions": "您有任何需要注意的健康状况吗？",
        "dietary_restrictions": "您有任何饮食限制或偏好吗？"
    }
    
    @classmethod
    def get_missing_fields(cls, user_info: Dict[str, Any]) -> List[str]:
        """
        获取缺失的必要用户信息字段
        
        Args:
            user_info: 用户信息字典
            
        Returns:
            缺失的字段列表
        """
        if not user_info:
            return cls.REQUIRED_FIELDS
        
        missing_fields = []
        for field in cls.REQUIRED_FIELDS:
            if field not in user_info or not user_info[field]:
                missing_fields.append(field)
        
        return missing_fields
    
    @classmethod
    def get_field_prompt(cls, field_name: str) -> str:
        """
        获取字段收集提示
        
        Args:
            field_name: 字段名称
            
        Returns:
            字段收集提示
        """
        return cls.FIELD_PROMPTS.get(field_name, f"请提供您的{field_name}")
    
    @classmethod
    def validate_field(cls, field_name: str, value: Any) -> Tuple[bool, str, Any]:
        """
        验证字段值
        
        Args:
            field_name: 字段名称
            value: 字段值
            
        Returns:
            (是否有效, 错误消息, 标准化值)
        """
        if field_name not in cls.FIELD_VALIDATION:
            # 没有验证规则的字段默认有效
            return True, "", value
        
        validation = cls.FIELD_VALIDATION[field_name]
        field_type = validation["type"]
        
        # 字符串类型转换
        if isinstance(value, str):
            value = value.strip()
        
        # 空值检查
        if not value and value != 0:
            return False, f"{field_name}不能为空", None
        
        # 根据字段类型进行验证
        if field_type == "enum":
            allowed_values = validation["values"]
            # 不区分大小写的匹配
            if isinstance(value, str) and value.lower() in [v.lower() for v in allowed_values]:
                # 返回标准化的值
                for v in allowed_values:
                    if value.lower() == v.lower():
                        return True, "", v
            return False, f"{field_name}必须是以下值之一: {', '.join(allowed_values)}", None
        
        elif field_type == "int":
            try:
                int_value = int(value)
                min_val = validation.get("min")
                max_val = validation.get("max")
                
                if min_val is not None and int_value < min_val:
                    return False, f"{field_name}不能小于{min_val}", None
                if max_val is not None and int_value > max_val:
                    return False, f"{field_name}不能大于{max_val}", None
                
                return True, "", int_value
            except (ValueError, TypeError):
                return False, f"{field_name}必须是整数", None
        
        elif field_type == "float":
            try:
                float_value = float(value)
                min_val = validation.get("min")
                max_val = validation.get("max")
                
                if min_val is not None and float_value < min_val:
                    return False, f"{field_name}不能小于{min_val}", None
                if max_val is not None and float_value > max_val:
                    return False, f"{field_name}不能大于{max_val}", None
                
                return True, "", float_value
            except (ValueError, TypeError):
                return False, f"{field_name}必须是数字", None
        
        # 默认返回有效
        return True, "", value
    
    @classmethod
    async def extract_field_value(cls, field_name: str, message: str) -> Any:
        """
        从用户消息中提取字段值
        
        Args:
            field_name: 字段名称
            message: 用户消息
            
        Returns:
            提取的字段值
        """
        # 使用正则表达式提取常见格式
        if field_name == "age":
            # 提取年龄
            age_match = re.search(r'(\d+)\s*岁', message)
            if age_match:
                return int(age_match.group(1))
        
        elif field_name == "height":
            # 提取身高(cm)
            height_match = re.search(r'(\d+(?:\.\d+)?)\s*(cm|厘米)', message)
            if height_match:
                return float(height_match.group(1))
        
        elif field_name == "weight":
            # 提取体重(kg)
            weight_match = re.search(r'(\d+(?:\.\d+)?)\s*(kg|公斤)', message)
            if weight_match:
                return float(weight_match.group(1))
        
        # 使用LLM提取更复杂的字段值
        try:
            prompt = f"""
            请从用户消息中提取"{field_name}"的值。

            用户消息: "{message}"

            如果无法确定，请返回null。只返回提取的值，不要有任何其他文字。
            """
            
            response = await llm_service.aget_chat_response(
                messages=[
                    {"role": "system", "content": "你是一个字段提取助手，负责从用户消息中提取特定字段的值。"},
                    {"role": "user", "content": prompt}
                ],
                model="agent-app",
                temperature=0.1
            )
            
            # 清理响应
            response = response.strip()
            if response.lower() == "null" or not response:
                return None
            
            # 根据字段类型进行转换
            if field_name in cls.FIELD_VALIDATION:
                field_type = cls.FIELD_VALIDATION[field_name]["type"]
                if field_type == "int":
                    try:
                        return int(response)
                    except (ValueError, TypeError):
                        pass
                elif field_type == "float":
                    try:
                        return float(response)
                    except (ValueError, TypeError):
                        pass
            
            return response
        except Exception as e:
            logger.error(f"使用LLM提取字段值失败: {str(e)}")
            return None
    
    @classmethod
    async def process_user_info_input(cls, field_name: str, message: str) -> Tuple[bool, str, Any]:
        """
        处理用户信息输入
        
        Args:
            field_name: 字段名称
            message: 用户消息
            
        Returns:
            (是否有效, 反馈消息, 处理后的值)
        """
        # 提取字段值
        value = await cls.extract_field_value(field_name, message)
        
        # 验证字段值
        is_valid, error_message, normalized_value = cls.validate_field(field_name, value)
        
        if is_valid:
            return True, f"已更新您的{field_name}为: {normalized_value}", normalized_value
        else:
            return False, error_message, None
    
    @classmethod
    def get_next_field(cls, user_info: Dict[str, Any]) -> Optional[str]:
        """
        获取下一个需要收集的字段
        
        Args:
            user_info: 用户信息字典
            
        Returns:
            下一个需要收集的字段名称，如果没有则返回None
        """
        # 先检查必要字段
        for field in cls.REQUIRED_FIELDS:
            if field not in user_info or not user_info[field]:
                return field
        
        # 再检查可选字段
        for field in cls.OPTIONAL_FIELDS:
            if field not in user_info or not user_info[field]:
                return field
        
        return None
    
    @classmethod
    def get_user_info_summary(cls, user_info: Dict[str, Any]) -> str:
        """
        获取用户信息摘要
        
        Args:
            user_info: 用户信息字典
            
        Returns:
            用户信息摘要
        """
        if not user_info:
            return "未提供用户信息"
        
        summary = "您的个人信息:\n"
        
        # 添加必要字段
        for field in cls.REQUIRED_FIELDS:
            if field in user_info and user_info[field]:
                field_name = field.replace("_", " ").title()
                summary += f"- {field_name}: {user_info[field]}\n"
        
        # 添加可选字段
        for field in cls.OPTIONAL_FIELDS:
            if field in user_info and user_info[field]:
                field_name = field.replace("_", " ").title()
                summary += f"- {field_name}: {user_info[field]}\n"
        
        return summary
    
    @classmethod
    async def infer_missing_fields(cls, user_info: Dict[str, Any], message: str) -> Dict[str, Any]:
        """
        推断缺失的字段
        
        Args:
            user_info: 用户信息字典
            message: 用户消息
            
        Returns:
            推断的字段字典
        """
        # 获取缺失的字段
        missing_fields = cls.get_missing_fields(user_info)
        if not missing_fields:
            return {}
        
        try:
            # 构建提示词
            fields_str = ", ".join(missing_fields)
            prompt = f"""
            请从用户消息中推断以下缺失的用户信息字段: {fields_str}

            用户消息: "{message}"

            如果无法推断某个字段，请返回null。请返回JSON格式，包含推断的字段值。
            """
            
            response = await llm_service.aget_chat_response(
                messages=[
                    {"role": "system", "content": "你是一个用户信息推断助手，负责从用户消息中推断缺失的用户信息。"},
                    {"role": "user", "content": prompt}
                ],
                model="agent-app",
                temperature=0.1
            )
            
            # 解析JSON响应
            try:
                inferred_fields = json.loads(response)
                # 验证推断的字段
                validated_fields = {}
                for field, value in inferred_fields.items():
                    if value is not None and field in missing_fields:
                        is_valid, _, normalized_value = cls.validate_field(field, value)
                        if is_valid:
                            validated_fields[field] = normalized_value
                
                return validated_fields
            except json.JSONDecodeError:
                logger.warning(f"无法解析推断字段响应: {response}")
                return {}
        except Exception as e:
            logger.error(f"推断缺失字段失败: {str(e)}")
            return {}
