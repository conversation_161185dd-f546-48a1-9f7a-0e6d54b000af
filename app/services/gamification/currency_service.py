from typing import Optional, List, Dict, Any, Tuple, Union
from sqlalchemy.orm import Session
from datetime import datetime, date, timedelta

from app.crud.gamification import currency, currency_transaction, shop_item, user_purchase
from app.models.gamification import Currency, CurrencyTransaction, ShopItem, UserPurchase
from app.schemas.gamification import (
    CurrencyCreate, CurrencyUpdate, CurrencyTransactionCreate,
    ShopItemCreate, ShopItemUpdate, UserPurchaseCreate, UserPurchaseUpdate
)


class CurrencyService:
    """虚拟货币服务，处理用户虚拟货币和商店相关的业务逻辑"""
    
    @staticmethod
    async def get_user_currency(db: Session, user_id: int) -> Optional[Currency]:
        """获取用户货币信息"""
        return currency.get_by_user_id(db, user_id=user_id)
    
    @staticmethod
    async def initialize_user_currency(db: Session, user_id: int, initial_amount: int = 100) -> Currency:
        """初始化用户货币系统（用户注册后调用）"""
        # 检查用户是否已有货币记录
        existing_currency = currency.get_by_user_id(db, user_id=user_id)
        if existing_currency:
            return existing_currency
        
        # 创建货币记录，给予初始金额
        return currency.create_with_user(db, user_id=user_id, initial_amount=initial_amount)
    
    @staticmethod
    async def add_currency(
        db: Session, user_id: int, amount: int, description: str,
        transaction_type: str = "EARN", related_entity_type: str = None,
        related_entity_id: int = None
    ) -> Tuple[Currency, CurrencyTransaction]:
        """
        为用户添加货币
        返回: (更新后的货币记录, 交易记录)
        """
        # 检查用户是否有货币记录
        user_currency = currency.get_by_user_id(db, user_id=user_id)
        if not user_currency:
            # 如果没有记录，先初始化
            user_currency = await CurrencyService.initialize_user_currency(db, user_id)
        
        # 添加货币
        return currency.add_currency(
            db,
            user_id=user_id,
            amount=amount,
            description=description,
            transaction_type=transaction_type,
            related_entity_type=related_entity_type,
            related_entity_id=related_entity_id
        )
    
    @staticmethod
    async def spend_currency(
        db: Session, user_id: int, amount: int, description: str,
        transaction_type: str = "SPEND", related_entity_type: str = None,
        related_entity_id: int = None
    ) -> Tuple[bool, Optional[Currency], Optional[CurrencyTransaction]]:
        """
        用户花费货币
        返回: (是否成功, 更新后的货币记录, 交易记录)
        """
        # 检查用户是否有货币记录
        user_currency = currency.get_by_user_id(db, user_id=user_id)
        if not user_currency:
            # 如果没有记录，先初始化
            user_currency = await CurrencyService.initialize_user_currency(db, user_id)
        
        # 检查余额是否足够
        if user_currency.amount < amount:
            return False, user_currency, None
        
        # 扣除货币
        return currency.spend_currency(
            db,
            user_id=user_id,
            amount=amount,
            description=description,
            transaction_type=transaction_type,
            related_entity_type=related_entity_type,
            related_entity_id=related_entity_id
        )
    
    @staticmethod
    async def get_user_transactions(
        db: Session, user_id: int, skip: int = 0, limit: int = 20
    ) -> List[CurrencyTransaction]:
        """获取用户的交易记录"""
        return currency_transaction.get_by_user_id(db, user_id=user_id, skip=skip, limit=limit)
    
    @staticmethod
    async def get_daily_transactions(db: Session, user_id: int, day: date = None) -> List[CurrencyTransaction]:
        """获取用户某一天的交易记录"""
        return currency_transaction.get_daily_transactions(db, user_id=user_id, day=day)
    
    @staticmethod
    async def get_shop_items(
        db: Session, skip: int = 0, limit: int = 50, category: str = None,
        user_level_type: str = None, user_level: int = None
    ) -> List[ShopItem]:
        """获取商店物品列表，考虑用户等级进行筛选"""
        if category:
            items = shop_item.get_multi_by_category(db, category=category, skip=skip, limit=limit)
        else:
            items = shop_item.get_available_items(
                db, user_level_type=user_level_type, user_level=user_level, 
                skip=skip, limit=limit
            )
        return items
    
    @staticmethod
    async def get_shop_item(db: Session, item_id: int) -> Optional[ShopItem]:
        """获取商店物品详情"""
        return shop_item.get(db, id=item_id)
    
    @staticmethod
    async def get_discounted_price(
        db: Session, item_id: int, user_level_type: str = None, user_level: int = None
    ) -> Tuple[Optional[ShopItem], int]:
        """获取商品的折扣价格"""
        return shop_item.get_discounted_price(
            db, item_id=item_id, user_level_type=user_level_type, user_level=user_level
        )
    
    @staticmethod
    async def purchase_item(
        db: Session, user_id: int, item_id: int, quantity: int = 1
    ) -> Tuple[bool, Optional[UserPurchase], str]:
        """
        用户购买物品
        返回: (是否成功, 购买记录, 消息)
        """
        # 获取物品及其折扣价格
        item, discounted_price = await CurrencyService.get_discounted_price(db, item_id)
        if not item:
            return False, None, "商品不存在"
        
        # 计算总价
        total_price = discounted_price * quantity
        
        # 扣除货币
        success, _, _ = await CurrencyService.spend_currency(
            db,
            user_id=user_id,
            amount=total_price,
            description=f"购买 {item.name} x{quantity}",
            transaction_type="PURCHASE",
            related_entity_type="SHOP_ITEM",
            related_entity_id=item.id
        )
        
        if not success:
            return False, None, "余额不足"
        
        # 创建购买记录
        success, purchase = user_purchase.purchase_item(
            db,
            user_id=user_id,
            item_id=item_id,
            quantity=quantity,
            price_paid=discounted_price
        )
        
        if not success:
            # 退还货币
            await CurrencyService.add_currency(
                db,
                user_id=user_id,
                amount=total_price,
                description=f"退款: 购买 {item.name} x{quantity} 失败",
                transaction_type="REFUND"
            )
            return False, None, "购买失败，已退款"
        
        # 处理购买后的效果（这里可以扩展，例如添加卡片或其他物品）
        if item.item_type == "CARD" and item.related_item_id:
            from app.services.gamification.card_service import CardService
            await CardService.add_card_to_user(
                db, user_id=user_id, card_id=item.related_item_id, quantity=quantity
            )
        
        return True, purchase, "购买成功"
    
    @staticmethod
    async def get_user_purchases(
        db: Session, user_id: int, skip: int = 0, limit: int = 20
    ) -> List[UserPurchase]:
        """获取用户的购买记录"""
        return user_purchase.get_by_user_id(db, user_id=user_id, skip=skip, limit=limit)
    
    @staticmethod
    async def check_daily_earning_limit(
        db: Session, user_id: int, limit: int = 1000
    ) -> Tuple[bool, int]:
        """
        检查用户当日获取的货币是否超过限制
        返回: (是否未超限, 今日已获取货币)
        """
        user_currency = currency.get_by_user_id(db, user_id=user_id)
        if not user_currency:
            return True, 0
        
        # 检查是否是新的一天
        today = datetime.now().date()
        if user_currency.last_reset_date != today:
            # 如果是新的一天，重置当日获取的货币
            user_currency.daily_earned_today = 0
            user_currency.last_reset_date = today
            db.add(user_currency)
            db.commit()
            db.refresh(user_currency)
            return True, 0
        
        # 检查是否超过限制
        return user_currency.daily_earned_today < limit, user_currency.daily_earned_today 