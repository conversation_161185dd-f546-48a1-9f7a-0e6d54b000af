from typing import Optional, List, Dict, Any, Tuple, Union
from sqlalchemy.orm import Session
from datetime import datetime, date, timedelta

from app.crud.gamification import task, user_task, daily_checkin
from app.models.gamification import Task, UserTask, DailyCheckIn
from app.schemas.gamification import (
    TaskCreate, TaskUpdate, UserTaskCreate, UserTaskUpdate,
    DailyCheckInCreate, DailyCheckInUpdate
)


class TaskService:
    """任务系统服务，处理日常任务、周常任务、挑战任务和签到相关的业务逻辑"""
    
    @staticmethod
    async def get_all_tasks(
        db: Session, skip: int = 0, limit: int = 100
    ) -> List[Task]:
        """获取所有任务"""
        return task.get_multi(db, skip=skip, limit=limit)
    
    @staticmethod
    async def get_tasks_by_type(
        db: Session, task_type: str, skip: int = 0, limit: int = 50
    ) -> List[Task]:
        """根据任务类型获取任务"""
        return task.get_multi_by_type(db, task_type=task_type, skip=skip, limit=limit)
    
    @staticmethod
    async def get_tasks_by_category(
        db: Session, category: str, skip: int = 0, limit: int = 50
    ) -> List[Task]:
        """根据任务类别获取任务"""
        return task.get_multi_by_category(db, category=category, skip=skip, limit=limit)
    
    @staticmethod
    async def get_user_active_tasks(
        db: Session, user_id: int, skip: int = 0, limit: int = 50
    ) -> List[UserTask]:
        """获取用户未完成的任务"""
        return user_task.get_active_by_user(db, user_id=user_id, skip=skip, limit=limit)
    
    @staticmethod
    async def get_user_completed_tasks(
        db: Session, user_id: int, skip: int = 0, limit: int = 50
    ) -> List[UserTask]:
        """获取用户已完成的任务"""
        return user_task.get_completed_by_user(db, user_id=user_id, skip=skip, limit=limit)
    
    @staticmethod
    async def generate_daily_tasks(
        db: Session, user_id: int, user_level_type: str = "EXERCISE", user_level: int = 1
    ) -> List[Dict[str, Any]]:
        """
        为用户生成每日任务
        返回: 生成的任务列表
        """
        # 生成任务
        user_tasks = user_task.generate_daily_tasks(
            db, user_id=user_id, user_level_type=user_level_type, user_level=user_level
        )
        
        # 格式化任务信息
        result = []
        for user_task_obj in user_tasks:
            task_obj = task.get(db, id=user_task_obj.task_id)
            if task_obj:
                result.append({
                    "id": user_task_obj.id,
                    "task_id": task_obj.id,
                    "name": task_obj.name,
                    "description": task_obj.description,
                    "task_type": task_obj.task_type,
                    "category": task_obj.category,
                    "progress": user_task_obj.progress,
                    "requirement": task_obj.requirement_value,
                    "currency_reward": task_obj.currency_reward,
                    "experience_reward": task_obj.experience_reward,
                    "reward_type": task_obj.reward_type,
                    "expires_at": user_task_obj.expires_at,
                    "completed": user_task_obj.completed,
                    "reward_claimed": user_task_obj.reward_claimed
                })
        
        return result
    
    @staticmethod
    async def generate_weekly_tasks(
        db: Session, user_id: int, user_level_type: str = "EXERCISE", user_level: int = 1
    ) -> List[Dict[str, Any]]:
        """
        为用户生成每周任务
        返回: 生成的任务列表
        """
        # 生成任务
        user_tasks = user_task.generate_weekly_tasks(
            db, user_id=user_id, user_level_type=user_level_type, user_level=user_level
        )
        
        # 格式化任务信息
        result = []
        for user_task_obj in user_tasks:
            task_obj = task.get(db, id=user_task_obj.task_id)
            if task_obj:
                result.append({
                    "id": user_task_obj.id,
                    "task_id": task_obj.id,
                    "name": task_obj.name,
                    "description": task_obj.description,
                    "task_type": task_obj.task_type,
                    "category": task_obj.category,
                    "progress": user_task_obj.progress,
                    "requirement": task_obj.requirement_value,
                    "currency_reward": task_obj.currency_reward,
                    "experience_reward": task_obj.experience_reward,
                    "reward_type": task_obj.reward_type,
                    "expires_at": user_task_obj.expires_at,
                    "completed": user_task_obj.completed,
                    "reward_claimed": user_task_obj.reward_claimed
                })
        
        return result
    
    @staticmethod
    async def update_task_progress(
        db: Session, user_id: int, task_id: int, progress_increment: int = 1
    ) -> Tuple[UserTask, bool, Task]:
        """
        更新用户任务进度
        返回: (更新后的用户任务记录, 是否刚完成, 任务对象)
        """
        # 获取任务信息
        task_obj = task.get(db, id=task_id)
        if not task_obj or not task_obj.is_active:
            raise ValueError(f"任务ID {task_id} 不存在或未激活")
        
        # 更新进度
        user_task_obj, just_completed = user_task.update_progress(
            db, user_id=user_id, task_id=task_id, 
            progress_increment=progress_increment
        )
        
        return user_task_obj, just_completed, task_obj
    
    @staticmethod
    async def claim_task_reward(
        db: Session, user_id: int, task_id: int
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        领取任务奖励
        返回: (是否成功, 奖励信息)
        """
        # 尝试领取奖励
        success, user_task_obj = user_task.claim_reward(
            db, user_id=user_id, task_id=task_id
        )
        
        if not success:
            return False, {"message": "无法领取奖励，可能任务未完成或已领取"}
        
        # 获取任务信息
        task_obj = task.get(db, id=task_id)
        
        # 根据任务类型发放奖励
        rewards = {}
        
        # 如果有奖励虚拟货币
        if task_obj.currency_reward and task_obj.currency_reward > 0:
            from app.services.gamification.currency_service import CurrencyService
            currency_obj, tx = await CurrencyService.add_currency(
                db,
                user_id=user_id,
                amount=task_obj.currency_reward,
                description=f"任务奖励: {task_obj.name}",
                transaction_type="TASK_REWARD",
                related_entity_type="TASK",
                related_entity_id=task_id
            )
            rewards["currency"] = task_obj.currency_reward
        
        # 如果有奖励经验值
        if task_obj.experience_reward and task_obj.experience_reward > 0:
            from app.services.gamification.level_service import LevelService
            
            # 根据任务类别决定增加哪种类型的经验值
            if task_obj.category == "EXERCISE":
                level_obj, level_up = await LevelService.add_exercise_experience(
                    db,
                    user_id=user_id,
                    experience=task_obj.experience_reward
                )
                rewards["exercise_exp"] = task_obj.experience_reward
                rewards["exercise_level_up"] = level_up
            elif task_obj.category == "DIET":
                level_obj, level_up = await LevelService.add_diet_experience(
                    db,
                    user_id=user_id,
                    experience=task_obj.experience_reward
                )
                rewards["diet_exp"] = task_obj.experience_reward
                rewards["diet_level_up"] = level_up
            else:
                # 默认分配到运动经验
                level_obj, level_up = await LevelService.add_exercise_experience(
                    db,
                    user_id=user_id,
                    experience=task_obj.experience_reward
                )
                rewards["exercise_exp"] = task_obj.experience_reward
                rewards["exercise_level_up"] = level_up
        
        # 如果有奖励卡片
        if task_obj.reward_type == "CARD" and task_obj.reward_item_id:
            from app.services.gamification.card_service import CardService
            await CardService.add_card_to_user(
                db, user_id=user_id, card_id=task_obj.reward_item_id
            )
            rewards["card_id"] = task_obj.reward_item_id
        
        # 检查任务类型，触发相关成就
        from app.services.gamification.achievement_service import AchievementService
        event_type = f"TASK_{task_obj.task_type}"  # 例如 TASK_DAILY
        event_data = {"task_id": task_id, "task_category": task_obj.category}
        
        achievement_results = await AchievementService.check_and_trigger_achievements(
            db, user_id=user_id, event_type=event_type, event_data=event_data
        )
        
        if achievement_results:
            rewards["achievements"] = achievement_results
        
        return True, {
            "message": "奖励领取成功",
            "task_name": task_obj.name,
            "rewards": rewards
        }
    
    @staticmethod
    async def check_daily_checkin(db: Session, user_id: int) -> Tuple[bool, Optional[DailyCheckIn]]:
        """
        检查用户今日是否已签到
        返回: (是否已签到, 签到记录)
        """
        checkin = daily_checkin.get_todays_checkin(db, user_id=user_id)
        return bool(checkin), checkin
    
    @staticmethod
    async def create_daily_checkin(
        db: Session, user_id: int
    ) -> Tuple[DailyCheckIn, bool, Dict[str, Any]]:
        """
        创建签到记录并发放奖励
        返回: (签到记录, 是否连续签到, 奖励信息)
        """
        # 检查是否已签到
        is_checked, existing_checkin = await TaskService.check_daily_checkin(db, user_id=user_id)
        if is_checked:
            return existing_checkin, False, {"message": "今日已签到"}
        
        # 确定奖励金额
        from app.services.gamification.level_service import LevelService
        user_level_obj = await LevelService.get_user_level(db, user_id=user_id)
        
        # 根据等级调整奖励
        base_currency = 10
        base_exp = 5
        
        if user_level_obj:
            # 等级越高奖励越多
            exercise_level = user_level_obj.exercise_level
            diet_level = user_level_obj.diet_level
            
            # 计算奖励系数，每10级增加50%
            exercise_bonus = 1 + (exercise_level // 10) * 0.5
            diet_bonus = 1 + (diet_level // 10) * 0.5
            
            currency_reward = int(base_currency * max(exercise_bonus, diet_bonus))
            exercise_exp_reward = int(base_exp * exercise_bonus)
            diet_exp_reward = int(base_exp * diet_bonus)
        else:
            # 默认奖励
            currency_reward = base_currency
            exercise_exp_reward = base_exp
            diet_exp_reward = base_exp
        
        # 创建签到记录
        checkin, is_streak = daily_checkin.create_checkin(
            db,
            user_id=user_id,
            currency_reward=currency_reward,
            exercise_exp_reward=exercise_exp_reward,
            diet_exp_reward=diet_exp_reward
        )
        
        # 如果连续签到，额外奖励
        streak_bonus = 0
        if is_streak and checkin.streak_count > 1:
            # 连续签到额外奖励：每连续签到一天增加1个币
            streak_bonus = min(checkin.streak_count, 30)  # 最多额外30个币
        
        # 发放奖励
        rewards = {}
        
        # 虚拟货币奖励
        from app.services.gamification.currency_service import CurrencyService
        total_currency = currency_reward + streak_bonus
        
        currency_obj, tx = await CurrencyService.add_currency(
            db,
            user_id=user_id,
            amount=total_currency,
            description=f"每日签到奖励 (连续{checkin.streak_count}天)",
            transaction_type="CHECKIN_REWARD"
        )
        rewards["currency"] = total_currency
        rewards["streak_bonus"] = streak_bonus
        
        # 经验值奖励
        from app.services.gamification.level_service import LevelService
        
        # 运动经验
        exercise_level, exercise_level_up = await LevelService.add_exercise_experience(
            db,
            user_id=user_id,
            experience=exercise_exp_reward
        )
        rewards["exercise_exp"] = exercise_exp_reward
        rewards["exercise_level_up"] = exercise_level_up
        
        # 饮食经验
        diet_level, diet_level_up = await LevelService.add_diet_experience(
            db,
            user_id=user_id,
            experience=diet_exp_reward
        )
        rewards["diet_exp"] = diet_exp_reward
        rewards["diet_level_up"] = diet_level_up
        
        # 检查是否触发签到相关成就
        from app.services.gamification.achievement_service import AchievementService
        event_type = "CHECKIN"
        event_data = {"streak_count": checkin.streak_count}
        
        achievement_results = await AchievementService.check_and_trigger_achievements(
            db, user_id=user_id, event_type=event_type, event_data=event_data
        )
        
        if achievement_results:
            rewards["achievements"] = achievement_results
        
        # 为连续签到7天、14天、21天、30天的用户额外奖励
        if checkin.streak_count in [7, 14, 21, 30]:
            # 随机掉落卡片
            from app.services.gamification.card_service import CardService
            
            # 根据连续天数调整卡片稀有度权重
            if checkin.streak_count == 7:
                rarity_weights = {1: 0.8, 2: 0.18, 3: 0.02}  # 普通80%，稀有18%，史诗2%
            elif checkin.streak_count == 14:
                rarity_weights = {1: 0.7, 2: 0.25, 3: 0.05}  # 普通70%，稀有25%，史诗5%
            elif checkin.streak_count == 21:
                rarity_weights = {1: 0.6, 2: 0.3, 3: 0.1}    # 普通60%，稀有30%，史诗10%
            else:  # 30天
                rarity_weights = {1: 0.4, 2: 0.4, 3: 0.2}    # 普通40%，稀有40%，史诗20%
            
            card_obj, user_card_obj = await CardService.random_drop_card(
                db, user_id=user_id, rarity_weights=rarity_weights
            )
            
            rewards["bonus_card"] = {
                "id": card_obj.id,
                "name": card_obj.name,
                "rarity": card_obj.rarity,
                "description": card_obj.description
            }
        
        return checkin, is_streak, {
            "message": f"签到成功，连续签到{checkin.streak_count}天",
            "rewards": rewards
        }
    
    @staticmethod
    async def get_user_checkin_history(
        db: Session, user_id: int, skip: int = 0, limit: int = 30
    ) -> List[DailyCheckIn]:
        """获取用户签到历史"""
        return daily_checkin.get_user_checkins(db, user_id=user_id, skip=skip, limit=limit)
    
    @staticmethod
    async def check_and_trigger_tasks(
        db: Session, user_id: int, event_type: str, 
        event_data: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """
        检查和触发与特定事件相关的任务进度
        返回: 更新的任务列表，包含进度和是否刚完成
        """
        if event_data is None:
            event_data = {}
        
        # 获取用户所有活跃任务
        active_tasks = await TaskService.get_user_active_tasks(db, user_id=user_id)
        
        results = []
        for user_task_obj in active_tasks:
            task_obj = task.get(db, id=user_task_obj.task_id)
            
            if not task_obj or not task_obj.is_active:
                continue
            
            # 根据任务类型和事件数据判断是否需要更新进度
            should_update = False
            progress_increment = 1
            
            # 这里可以根据具体的任务类型和条件进行更详细的判断
            if (task_obj.category == event_type or task_obj.condition_type == event_type):
                # 对于特定事件类型的额外判断
                if event_type == "EXERCISE" and event_data.get("exercise_id") == task_obj.condition_value:
                    should_update = True
                elif event_type == "DIET" and event_data.get("food_id") == task_obj.condition_value:
                    should_update = True
                elif event_type == "LOGIN":
                    should_update = True
                elif event_type == "PURCHASE" and event_data.get("item_id") == task_obj.condition_value:
                    should_update = True
                # 可以继续添加其他条件判断...
                elif task_obj.condition_type == "ANY":  # 通用任务
                    should_update = True
            
            if should_update:
                try:
                    user_task_obj, just_completed, _ = await TaskService.update_task_progress(
                        db, user_id=user_id, task_id=task_obj.id, 
                        progress_increment=progress_increment
                    )
                    
                    results.append({
                        "task_id": task_obj.id,
                        "task_name": task_obj.name,
                        "progress": user_task_obj.progress,
                        "requirement": task_obj.requirement_value,
                        "just_completed": just_completed
                    })
                except Exception as e:
                    # 记录错误但不中断其他任务的检查
                    print(f"Error updating task {task_obj.id}: {str(e)}")
        
        return results 