from typing import Optional, List, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import func
from datetime import datetime
import random

from app.crud.gamification import card, user_card, card_synthesis_recipe
from app.models.gamification import Card, UserCard, CardSynthesisRecipe, CardSynthesisIngredient
from app.schemas.gamification import (
    CardCreate, CardUpdate, UserCardCreate, UserCardUpdate,
    CardSynthesisRecipeCreate, CardSynthesisRecipeUpdate,
    CardSynthesisIngredientCreate
)


class CardService:
    """卡片系统服务，处理卡片收集、装备和合成相关的业务逻辑"""
    
    @staticmethod
    async def get_card_by_id(db: Session, card_id: int) -> Optional[Card]:
        """通过ID获取卡片"""
        return card.get(db, id=card_id)
    
    @staticmethod
    async def get_card_by_name(db: Session, name: str) -> Optional[Card]:
        """通过名称获取卡片"""
        return card.get_by_name(db, name=name)
    
    @staticmethod
    async def get_all_cards(
        db: Session, skip: int = 0, limit: int = 100
    ) -> List[Card]:
        """获取所有卡片"""
        return card.get_all_active(db, skip=skip, limit=limit)
    
    @staticmethod
    async def get_cards_by_type(
        db: Session, card_type: str, skip: int = 0, limit: int = 100
    ) -> List[Card]:
        """根据类型获取卡片"""
        return card.get_multi_by_type(db, card_type=card_type, skip=skip, limit=limit)
    
    @staticmethod
    async def get_user_cards(
        db: Session, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[UserCard]:
        """获取用户拥有的所有卡片"""
        return user_card.get_all_by_user(db, user_id=user_id, skip=skip, limit=limit)
    
    @staticmethod
    async def get_equipped_cards(db: Session, user_id: int) -> List[UserCard]:
        """获取用户已装备的卡片"""
        return user_card.get_equipped_cards(db, user_id=user_id)
    
    @staticmethod
    async def add_card_to_user(
        db: Session, user_id: int, card_id: int, quantity: int = 1
    ) -> UserCard:
        """为用户添加卡片"""
        return user_card.add_card_to_user(
            db, user_id=user_id, card_id=card_id, quantity=quantity
        )
    
    @staticmethod
    async def toggle_equip_status(
        db: Session, user_id: int, card_id: int
    ) -> Optional[UserCard]:
        """切换卡片装备状态"""
        return user_card.toggle_equip_status(db, user_id=user_id, card_id=card_id)
    
    @staticmethod
    async def get_synthesis_recipe(
        db: Session, result_card_id: int
    ) -> Optional[CardSynthesisRecipe]:
        """获取卡片合成配方"""
        return card_synthesis_recipe.get_by_result_card(db, result_card_id=result_card_id)
    
    @staticmethod
    async def get_synthesis_recipe_with_ingredients(
        db: Session, recipe_id: int
    ) -> Tuple[Optional[CardSynthesisRecipe], List[CardSynthesisIngredient]]:
        """获取卡片合成配方及其原料"""
        return card_synthesis_recipe.get_recipe_with_ingredients(db, recipe_id=recipe_id)
    
    @staticmethod
    async def get_all_synthesis_recipes(
        db: Session, skip: int = 0, limit: int = 100
    ) -> List[CardSynthesisRecipe]:
        """获取所有卡片合成配方"""
        return card_synthesis_recipe.get_all_active(db, skip=skip, limit=limit)
    
    @staticmethod
    async def synthesize_card(
        db: Session, user_id: int, recipe_id: int
    ) -> Tuple[bool, Optional[Card], str]:
        """
        尝试合成卡片
        返回: (是否成功, 合成的卡片, 信息消息)
        """
        # 获取合成配方及其原料
        recipe, ingredients = await CardService.get_synthesis_recipe_with_ingredients(db, recipe_id)
        if not recipe or not ingredients:
            return False, None, "合成配方不存在"
        
        # 获取合成结果卡片
        result_card = await CardService.get_card_by_id(db, recipe.result_card_id)
        if not result_card:
            return False, None, "合成结果卡片不存在"
        
        # 检查用户是否有足够的原料卡片
        for ingredient in ingredients:
            user_card_item = user_card.get_by_user_and_card(
                db, user_id=user_id, card_id=ingredient.card_id
            )
            if not user_card_item or user_card_item.quantity < ingredient.quantity:
                return False, None, "材料不足，无法合成"
        
        # 扣除原料卡片
        for ingredient in ingredients:
            success, _ = user_card.use_card(
                db, user_id=user_id, card_id=ingredient.card_id, quantity=ingredient.quantity
            )
            if not success:
                # 理论上不应该走到这里，因为前面已经检查过了
                return False, None, "扣除材料失败"
        
        # 添加结果卡片
        await CardService.add_card_to_user(db, user_id=user_id, card_id=result_card.id)
        
        return True, result_card, "合成成功"
    
    @staticmethod
    async def random_drop_card(
        db: Session, user_id: int, rarity_weights: Dict[int, float] = None
    ) -> Tuple[Card, UserCard]:
        """
        随机掉落一张卡片给用户
        rarity_weights: 各稀有度权重，例如 {1: 0.6, 2: 0.3, 3: 0.1}
        返回: (掉落的卡片, 用户卡片记录)
        """
        # 默认稀有度权重
        if not rarity_weights:
            rarity_weights = {1: 0.7, 2: 0.25, 3: 0.05}  # 普通70%，稀有25%，史诗5%
        
        # 按权重随机选择稀有度
        rarities = list(rarity_weights.keys())
        weights = list(rarity_weights.values())
        chosen_rarity = random.choices(rarities, weights=weights, k=1)[0]
        
        # 获取指定稀有度的随机卡片
        dropped_card = card.get_random_by_rarity(db, rarity=chosen_rarity)
        
        # 如果没有找到卡片，降级尝试
        if not dropped_card and chosen_rarity > 1:
            for r in range(chosen_rarity - 1, 0, -1):
                dropped_card = card.get_random_by_rarity(db, rarity=r)
                if dropped_card:
                    break
        
        # 如果仍然没有找到卡片，随便选一张
        if not dropped_card:
            dropped_card = db.query(Card).filter(Card.is_active == True).order_by(func.random()).first()
        
        # 如果依然没有卡片，返回失败
        if not dropped_card:
            raise ValueError("找不到任何可用卡片")
        
        # 添加到用户
        user_card_record = await CardService.add_card_to_user(db, user_id=user_id, card_id=dropped_card.id)
        
        return dropped_card, user_card_record
    
    @staticmethod
    async def get_card_effects(db: Session, user_id: int) -> Dict[str, float]:
        """
        计算用户已装备卡片的综合效果
        返回: {效果类型: 效果值} 的字典
        """
        equipped_cards = await CardService.get_equipped_cards(db, user_id=user_id)
        
        # 初始化效果字典
        effects = {
            "strength_bonus": 0.0,
            "endurance_bonus": 0.0,
            "flexibility_bonus": 0.0,
            "nutrition_bonus": 0.0,
            "cooking_bonus": 0.0,
            "planning_bonus": 0.0,
            "exp_gain_bonus": 0.0,
            "currency_gain_bonus": 0.0
        }
        
        # 累加各卡片效果
        for user_card_item in equipped_cards:
            card_item = await CardService.get_card_by_id(db, user_card_item.card_id)
            if card_item and card_item.effect_type and card_item.effect_value:
                if card_item.effect_type in effects:
                    effects[card_item.effect_type] += card_item.effect_value
        
        return effects 