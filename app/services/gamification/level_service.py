from typing import Optional, List, Dict, Any, Tuple
from sqlalchemy.orm import Session
from datetime import datetime

from app.crud.gamification import user_level, user_attribute, user_title
from app.models.gamification import UserLevel, UserAttribute, UserTitle
from app.schemas.gamification import (
    UserLevelCreate, UserLevelUpdate, UserAttributeCreate, 
    UserAttributeUpdate, UserTitleCreate, UserTitleUpdate
)


class LevelService:
    """用户等级和属性服务，处理用户等级、属性和称号相关的业务逻辑"""
    
    @staticmethod
    async def get_user_level(db: Session, user_id: int) -> Optional[UserLevel]:
        """获取用户等级信息"""
        return user_level.get_by_user_id(db, user_id=user_id)
    
    @staticmethod
    async def initialize_user_level(db: Session, user_id: int) -> UserLevel:
        """初始化用户等级系统（用户注册后调用）"""
        # 检查用户是否已有等级记录
        existing_level = user_level.get_by_user_id(db, user_id=user_id)
        if existing_level:
            return existing_level
        
        # 创建初始等级记录
        level_data = UserLevelCreate(
            exercise_level=1,
            exercise_experience=0,
            diet_level=1,
            diet_experience=0
        )
        user_level_obj = user_level.create_with_user(db, obj_in=level_data, user_id=user_id)
        
        # 创建初始属性记录
        attr_data = UserAttributeCreate(
            strength=10,
            endurance=10,
            flexibility=10,
            nutrition_knowledge=10,
            cooking_skill=10,
            diet_planning=10
        )
        user_attribute.create_with_user(db, obj_in=attr_data, user_id=user_id)
        
        # 创建初始称号"健身新手"
        title_data = UserTitleCreate(
            title_name="健身新手",
            title_type="EXERCISE",
            is_active=True
        )
        user_title.create_with_user(
            db, 
            obj_in=title_data, 
            user_id=user_id, 
            level_id=user_level_obj.id
        )
        
        return user_level_obj
    
    @staticmethod
    async def add_exercise_experience(
        db: Session, user_id: int, experience: int, 
        update_attributes: bool = True
    ) -> Tuple[UserLevel, bool]:
        """
        增加用户运动经验值
        返回: (更新后的用户等级对象, 是否升级)
        """
        old_level = user_level.get_by_user_id(db, user_id=user_id)
        if not old_level:
            # 如果用户没有等级记录，先初始化
            await LevelService.initialize_user_level(db, user_id)
            old_level = user_level.get_by_user_id(db, user_id=user_id)
        
        old_exercise_level = old_level.exercise_level
        updated_level = user_level.add_exercise_experience(db, user_id=user_id, experience=experience)
        
        # 检查是否升级
        level_up = updated_level.exercise_level > old_exercise_level
        
        # 如果升级且需要更新属性
        if level_up and update_attributes:
            # 每升一级增加相应属性
            attr_updates = {
                "strength": 2,
                "endurance": 2,
                "flexibility": 1
            }
            user_attribute.update_attributes(db, user_id=user_id, attributes=attr_updates)
            
            # 检查是否达到特定等级里程碑，添加新称号
            if updated_level.exercise_level == 5:
                title_data = UserTitleCreate(
                    title_name="健身爱好者",
                    title_type="EXERCISE",
                    is_active=False
                )
                user_title.create_with_user(
                    db, 
                    obj_in=title_data, 
                    user_id=user_id, 
                    level_id=updated_level.id
                )
            elif updated_level.exercise_level == 10:
                title_data = UserTitleCreate(
                    title_name="健身达人",
                    title_type="EXERCISE",
                    is_active=False
                )
                user_title.create_with_user(
                    db, 
                    obj_in=title_data, 
                    user_id=user_id, 
                    level_id=updated_level.id
                )
            elif updated_level.exercise_level == 20:
                title_data = UserTitleCreate(
                    title_name="健身专家",
                    title_type="EXERCISE",
                    is_active=False
                )
                user_title.create_with_user(
                    db, 
                    obj_in=title_data, 
                    user_id=user_id, 
                    level_id=updated_level.id
                )
        
        return updated_level, level_up
    
    @staticmethod
    async def add_diet_experience(
        db: Session, user_id: int, experience: int, 
        update_attributes: bool = True
    ) -> Tuple[UserLevel, bool]:
        """
        增加用户饮食经验值
        返回: (更新后的用户等级对象, 是否升级)
        """
        old_level = user_level.get_by_user_id(db, user_id=user_id)
        if not old_level:
            # 如果用户没有等级记录，先初始化
            await LevelService.initialize_user_level(db, user_id)
            old_level = user_level.get_by_user_id(db, user_id=user_id)
        
        old_diet_level = old_level.diet_level
        updated_level = user_level.add_diet_experience(db, user_id=user_id, experience=experience)
        
        # 检查是否升级
        level_up = updated_level.diet_level > old_diet_level
        
        # 如果升级且需要更新属性
        if level_up and update_attributes:
            # 每升一级增加相应属性
            attr_updates = {
                "nutrition_knowledge": 2,
                "cooking_skill": 1,
                "diet_planning": 2
            }
            user_attribute.update_attributes(db, user_id=user_id, attributes=attr_updates)
            
            # 检查是否达到特定等级里程碑，添加新称号
            if updated_level.diet_level == 5:
                title_data = UserTitleCreate(
                    title_name="营养学徒",
                    title_type="DIET",
                    is_active=False
                )
                user_title.create_with_user(
                    db, 
                    obj_in=title_data, 
                    user_id=user_id, 
                    level_id=updated_level.id
                )
            elif updated_level.diet_level == 10:
                title_data = UserTitleCreate(
                    title_name="营养师",
                    title_type="DIET",
                    is_active=False
                )
                user_title.create_with_user(
                    db, 
                    obj_in=title_data, 
                    user_id=user_id, 
                    level_id=updated_level.id
                )
            elif updated_level.diet_level == 20:
                title_data = UserTitleCreate(
                    title_name="营养大师",
                    title_type="DIET",
                    is_active=False
                )
                user_title.create_with_user(
                    db, 
                    obj_in=title_data, 
                    user_id=user_id, 
                    level_id=updated_level.id
                )
        
        return updated_level, level_up
    
    @staticmethod
    async def get_user_attributes(db: Session, user_id: int) -> Optional[UserAttribute]:
        """获取用户属性信息"""
        return user_attribute.get_by_user_id(db, user_id=user_id)
    
    @staticmethod
    async def get_user_titles(db: Session, user_id: int) -> List[UserTitle]:
        """获取用户所有称号"""
        return user_title.get_by_user_id(db, user_id=user_id)
    
    @staticmethod
    async def get_active_title(db: Session, user_id: int) -> Optional[UserTitle]:
        """获取用户当前激活的称号"""
        return user_title.get_active_title(db, user_id=user_id)
    
    @staticmethod
    async def set_active_title(db: Session, user_id: int, title_id: int) -> Optional[UserTitle]:
        """设置用户当前激活的称号"""
        return user_title.set_active_title(db, user_id=user_id, title_id=title_id)
    
    @staticmethod
    async def calculate_required_exp(level: int) -> int:
        """计算升级所需的经验值 - 经验值曲线可以根据需求调整"""
        return level * 100  # 简单线性公式，等级越高需要的经验越多 