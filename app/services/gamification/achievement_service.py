from typing import Optional, List, Dict, Any, Tuple, Union
from sqlalchemy.orm import Session
from datetime import datetime, date

from app.crud.gamification import achievement, user_achievement, milestone, user_milestone
from app.models.gamification import Achievement, UserAchievement, Milestone, UserMilestone
from app.schemas.gamification import (
    AchievementCreate, AchievementUpdate, UserAchievementCreate, UserAchievementUpdate,
    MilestoneCreate, MilestoneUpdate, UserMilestoneCreate, UserMilestoneUpdate
)


class AchievementService:
    """成就和里程碑服务，处理用户成就解锁和里程碑追踪相关的业务逻辑"""
    
    @staticmethod
    async def get_all_achievements(
        db: Session, skip: int = 0, limit: int = 100, visible_only: bool = True
    ) -> List[Achievement]:
        """获取所有成就（可选择是否只获取可见成就）"""
        if visible_only:
            return achievement.get_visible_achievements(db, skip=skip, limit=limit)
        else:
            return achievement.get_all_active(db, skip=skip, limit=limit)
    
    @staticmethod
    async def get_achievements_by_category(
        db: Session, category: str, skip: int = 0, limit: int = 100
    ) -> List[Achievement]:
        """根据类别获取成就"""
        return achievement.get_multi_by_category(db, category=category, skip=skip, limit=limit)
    
    @staticmethod
    async def get_user_achievement_progress(
        db: Session, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[UserAchievement]:
        """获取用户所有成就进度"""
        return user_achievement.get_all_by_user(db, user_id=user_id, skip=skip, limit=limit)
    
    @staticmethod
    async def get_user_completed_achievements(
        db: Session, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[UserAchievement]:
        """获取用户已完成的成就"""
        return user_achievement.get_completed_by_user(db, user_id=user_id, skip=skip, limit=limit)
    
    @staticmethod
    async def update_achievement_progress(
        db: Session, user_id: int, achievement_id: int, progress_increment: int = 1
    ) -> Tuple[UserAchievement, bool, Achievement]:
        """
        更新用户成就进度
        返回: (更新后的用户成就记录, 是否刚完成, 成就对象)
        """
        # 获取成就信息
        achievement_obj = achievement.get(db, id=achievement_id)
        if not achievement_obj or not achievement_obj.is_active:
            raise ValueError(f"成就ID {achievement_id} 不存在或未激活")
        
        # 更新进度
        user_achievement_obj, just_completed = user_achievement.update_progress(
            db, user_id=user_id, achievement_id=achievement_id, 
            progress_increment=progress_increment
        )
        
        return user_achievement_obj, just_completed, achievement_obj
    
    @staticmethod
    async def claim_achievement_reward(
        db: Session, user_id: int, achievement_id: int
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        领取成就奖励
        返回: (是否成功, 奖励信息)
        """
        # 尝试领取奖励
        success, user_achievement_obj = user_achievement.claim_reward(
            db, user_id=user_id, achievement_id=achievement_id
        )
        
        if not success:
            return False, {"message": "无法领取奖励，可能成就未完成或已领取"}
        
        # 获取成就信息
        achievement_obj = achievement.get(db, id=achievement_id)
        
        # 根据成就类型发放奖励
        rewards = {}
        
        # 如果有奖励虚拟货币
        if achievement_obj.currency_reward and achievement_obj.currency_reward > 0:
            from app.services.gamification.currency_service import CurrencyService
            currency_obj, tx = await CurrencyService.add_currency(
                db,
                user_id=user_id,
                amount=achievement_obj.currency_reward,
                description=f"成就奖励: {achievement_obj.name}",
                transaction_type="ACHIEVEMENT_REWARD",
                related_entity_type="ACHIEVEMENT",
                related_entity_id=achievement_id
            )
            rewards["currency"] = achievement_obj.currency_reward
        
        # 如果有奖励经验值
        if achievement_obj.experience_reward and achievement_obj.experience_reward > 0:
            from app.services.gamification.level_service import LevelService
            
            # 根据成就类别决定增加哪种类型的经验值
            if achievement_obj.category == "EXERCISE":
                level_obj, level_up = await LevelService.add_exercise_experience(
                    db,
                    user_id=user_id,
                    experience=achievement_obj.experience_reward
                )
                rewards["exercise_exp"] = achievement_obj.experience_reward
                rewards["exercise_level_up"] = level_up
            elif achievement_obj.category == "DIET":
                level_obj, level_up = await LevelService.add_diet_experience(
                    db,
                    user_id=user_id,
                    experience=achievement_obj.experience_reward
                )
                rewards["diet_exp"] = achievement_obj.experience_reward
                rewards["diet_level_up"] = level_up
            else:
                # 默认分配到运动经验
                level_obj, level_up = await LevelService.add_exercise_experience(
                    db,
                    user_id=user_id,
                    experience=achievement_obj.experience_reward
                )
                rewards["exercise_exp"] = achievement_obj.experience_reward
                rewards["exercise_level_up"] = level_up
        
        # 如果有奖励卡片
        if achievement_obj.card_reward_id:
            from app.services.gamification.card_service import CardService
            card_obj, user_card_obj = await CardService.add_card_to_user(
                db, user_id=user_id, card_id=achievement_obj.card_reward_id
            )
            rewards["card"] = achievement_obj.card_reward_id
        
        return True, {
            "message": "奖励领取成功",
            "achievement_name": achievement_obj.name,
            "rewards": rewards
        }
    
    @staticmethod
    async def get_all_milestones(
        db: Session, skip: int = 0, limit: int = 100
    ) -> List[Milestone]:
        """获取所有里程碑"""
        return milestone.get_all_active(db, skip=skip, limit=limit)
    
    @staticmethod
    async def get_user_milestone_progress(
        db: Session, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[UserMilestone]:
        """获取用户所有里程碑进度"""
        return user_milestone.get_all_by_user(db, user_id=user_id, skip=skip, limit=limit)
    
    @staticmethod
    async def get_user_completed_milestones(
        db: Session, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[UserMilestone]:
        """获取用户已完成的里程碑"""
        return user_milestone.get_completed_by_user(db, user_id=user_id, skip=skip, limit=limit)
    
    @staticmethod
    async def update_milestone_active_date(
        db: Session, user_id: int, milestone_id: int
    ) -> Tuple[UserMilestone, bool, Milestone]:
        """
        更新里程碑活跃日期（打卡）
        返回: (更新后的用户里程碑记录, 是否刚完成, 里程碑对象)
        """
        # 获取里程碑信息
        milestone_obj = milestone.get(db, id=milestone_id)
        if not milestone_obj or not milestone_obj.is_active:
            raise ValueError(f"里程碑ID {milestone_id} 不存在或未激活")
        
        # 更新活跃日期
        user_milestone_obj, just_completed = user_milestone.update_active_date(
            db, user_id=user_id, milestone_id=milestone_id
        )
        
        return user_milestone_obj, just_completed, milestone_obj
    
    @staticmethod
    async def claim_milestone_reward(
        db: Session, user_id: int, milestone_id: int
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        领取里程碑奖励
        返回: (是否成功, 奖励信息)
        """
        # 尝试领取奖励
        success, user_milestone_obj = user_milestone.claim_reward(
            db, user_id=user_id, milestone_id=milestone_id
        )
        
        if not success:
            return False, {"message": "无法领取奖励，可能里程碑未完成或已领取"}
        
        # 获取里程碑信息
        milestone_obj = milestone.get(db, id=milestone_id)
        
        # 根据里程碑类型发放奖励
        rewards = {}
        
        # 如果有奖励虚拟货币
        if milestone_obj.currency_reward and milestone_obj.currency_reward > 0:
            from app.services.gamification.currency_service import CurrencyService
            currency_obj, tx = await CurrencyService.add_currency(
                db,
                user_id=user_id,
                amount=milestone_obj.currency_reward,
                description=f"里程碑奖励: {milestone_obj.name}",
                transaction_type="MILESTONE_REWARD",
                related_entity_type="MILESTONE",
                related_entity_id=milestone_id
            )
            rewards["currency"] = milestone_obj.currency_reward
        
        # 如果有奖励经验值
        if milestone_obj.experience_reward and milestone_obj.experience_reward > 0:
            from app.services.gamification.level_service import LevelService
            
            # 根据里程碑类别决定增加哪种类型的经验值
            if milestone_obj.milestone_type == "EXERCISE":
                level_obj, level_up = await LevelService.add_exercise_experience(
                    db,
                    user_id=user_id,
                    experience=milestone_obj.experience_reward
                )
                rewards["exercise_exp"] = milestone_obj.experience_reward
                rewards["exercise_level_up"] = level_up
            elif milestone_obj.milestone_type == "DIET":
                level_obj, level_up = await LevelService.add_diet_experience(
                    db,
                    user_id=user_id,
                    experience=milestone_obj.experience_reward
                )
                rewards["diet_exp"] = milestone_obj.experience_reward
                rewards["diet_level_up"] = level_up
            else:
                # 默认分配到运动经验
                level_obj, level_up = await LevelService.add_exercise_experience(
                    db,
                    user_id=user_id,
                    experience=milestone_obj.experience_reward
                )
                rewards["exercise_exp"] = milestone_obj.experience_reward
                rewards["exercise_level_up"] = level_up
        
        # 如果有奖励称号
        if milestone_obj.title_reward:
            from app.services.gamification.level_service import LevelService
            
            # 获取用户等级
            user_level_obj = await LevelService.get_user_level(db, user_id=user_id)
            
            # 创建称号
            from app.crud.gamification import user_title
            from app.schemas.gamification import UserTitleCreate
            
            title_data = UserTitleCreate(
                title_name=milestone_obj.title_reward,
                title_type=milestone_obj.milestone_type,
                is_active=False  # 默认不激活，用户可以自己选择激活
            )
            
            title_obj = user_title.create_with_user(
                db,
                obj_in=title_data,
                user_id=user_id,
                level_id=user_level_obj.id
            )
            
            rewards["title"] = milestone_obj.title_reward
        
        return True, {
            "message": "奖励领取成功",
            "milestone_name": milestone_obj.name,
            "rewards": rewards
        }
    
    @staticmethod
    async def check_and_trigger_achievements(
        db: Session, user_id: int, event_type: str, 
        event_data: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """
        检查和触发可能达成的成就
        返回: 触发的成就列表，包含进度和是否刚完成
        """
        if event_data is None:
            event_data = {}
        
        # 获取与事件类型相关的成就
        achievements_list = await AchievementService.get_achievements_by_category(
            db, category=event_type
        )
        
        results = []
        for achievement_obj in achievements_list:
            # 根据成就类型和事件数据判断是否需要更新进度
            should_update = False
            progress_increment = 1
            
            # 这里可以根据具体的成就类型和条件进行更详细的判断
            # 例如，健身相关的成就可能需要检查运动类型是否匹配
            if event_type == "EXERCISE" and event_data.get("exercise_type") == achievement_obj.condition_type:
                should_update = True
            elif event_type == "DIET" and event_data.get("food_type") == achievement_obj.condition_type:
                should_update = True
            elif event_type == "LOGIN":
                should_update = True
            elif event_type == "CARD_COLLECT" and event_data.get("card_id") == achievement_obj.condition_value:
                should_update = True
            # 可以继续添加其他条件判断...
            
            if should_update:
                try:
                    user_achievement_obj, just_completed, _ = await AchievementService.update_achievement_progress(
                        db, user_id=user_id, achievement_id=achievement_obj.id, 
                        progress_increment=progress_increment
                    )
                    
                    results.append({
                        "achievement_id": achievement_obj.id,
                        "achievement_name": achievement_obj.name,
                        "progress": user_achievement_obj.progress,
                        "requirement": achievement_obj.requirement_value,
                        "just_completed": just_completed
                    })
                except Exception as e:
                    # 记录错误但不中断其他成就的检查
                    print(f"Error updating achievement {achievement_obj.id}: {str(e)}")
        
        return results 