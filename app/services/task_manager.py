"""
异步任务管理器

提供异步任务的跟踪、管理和清理功能，确保任务在适当的时候被取消和清理。
"""

import asyncio
import logging
from typing import Dict, Set, Optional, List
import weakref
import time

logger = logging.getLogger(__name__)

class TaskManager:
    """异步任务管理器，用于跟踪和清理异步任务"""
    
    _instance = None
    
    def __new__(cls):
        """单例模式实现，确保全局只有一个任务管理器实例"""
        if cls._instance is None:
            cls._instance = super(TaskManager, cls).__new__(cls)
            cls._instance._tasks: Dict[str, Set[asyncio.Task]] = {}
            cls._instance._task_info: Dict[asyncio.Task, Dict] = {}
            cls._instance._last_cleanup = time.time()
        return cls._instance
    
    def register_task(self, conversation_id: str, task: asyncio.Task, description: str = ""):
        """注册一个与会话关联的任务
        
        Args:
            conversation_id: 会话ID
            task: 异步任务
            description: 任务描述，用于日志记录
        """
        if conversation_id not in self._tasks:
            self._tasks[conversation_id] = set()
        
        self._tasks[conversation_id].add(task)
        
        # 记录任务信息
        self._task_info[task] = {
            "conversation_id": conversation_id,
            "created_at": time.time(),
            "description": description
        }
        
        # 添加任务完成回调，以便自动清理
        task.add_done_callback(
            lambda t: self._clean_task(conversation_id, t)
        )
        
        logger.info(f"注册任务: conversation_id={conversation_id}, description={description}, 当前任务数={len(self._tasks[conversation_id])}")
        
        # 定期清理已完成但未被正确清理的任务
        self._periodic_cleanup()
    
    def _clean_task(self, conversation_id: str, task: asyncio.Task):
        """清理已完成的任务
        
        Args:
            conversation_id: 会话ID
            task: 异步任务
        """
        try:
            # 检查任务是否有异常
            if task.done() and not task.cancelled():
                try:
                    exception = task.exception()
                    if exception:
                        task_info = self._task_info.get(task, {})
                        description = task_info.get("description", "未知任务")
                        logger.error(f"任务异常: conversation_id={conversation_id}, description={description}, exception={exception}")
                except asyncio.CancelledError:
                    # 任务被取消，这是正常的
                    pass
                except Exception as e:
                    logger.error(f"获取任务异常时出错: {str(e)}")
            
            # 从任务集合中移除
            if conversation_id in self._tasks and task in self._tasks[conversation_id]:
                self._tasks[conversation_id].remove(task)
                logger.info(f"清理任务: conversation_id={conversation_id}, 剩余任务数={len(self._tasks[conversation_id])}")
                
                # 如果会话没有任务了，清理会话
                if not self._tasks[conversation_id]:
                    del self._tasks[conversation_id]
                    logger.info(f"清理会话: conversation_id={conversation_id}")
            
            # 从任务信息字典中移除
            if task in self._task_info:
                del self._task_info[task]
        except Exception as e:
            logger.error(f"清理任务时出错: {str(e)}")
    
    def _periodic_cleanup(self):
        """定期清理已完成但未被正确清理的任务"""
        current_time = time.time()
        # 每10分钟执行一次完整清理
        if current_time - self._last_cleanup > 600:
            try:
                logger.info("执行定期任务清理")
                # 查找所有已完成但未被清理的任务
                for conversation_id, tasks in list(self._tasks.items()):
                    for task in list(tasks):
                        if task.done():
                            self._clean_task(conversation_id, task)
                
                # 更新最后清理时间
                self._last_cleanup = current_time
                logger.info("定期任务清理完成")
            except Exception as e:
                logger.error(f"定期清理任务时出错: {str(e)}")
    
    def cancel_conversation_tasks(self, conversation_id: str):
        """取消与会话关联的所有任务
        
        Args:
            conversation_id: 会话ID
        """
        if conversation_id in self._tasks:
            tasks = list(self._tasks[conversation_id])
            for task in tasks:
                if not task.done():
                    logger.info(f"取消任务: conversation_id={conversation_id}")
                    task.cancel()
            
            # 清理会话
            del self._tasks[conversation_id]
            logger.info(f"清理会话: conversation_id={conversation_id}")
    
    def get_active_task_count(self, conversation_id: Optional[str] = None) -> int:
        """获取活动任务数量
        
        Args:
            conversation_id: 会话ID，如果为None则返回所有任务数量
            
        Returns:
            活动任务数量
        """
        if conversation_id:
            return len(self._tasks.get(conversation_id, set()))
        else:
            return sum(len(tasks) for tasks in self._tasks.values())
    
    def get_task_info(self, conversation_id: Optional[str] = None) -> List[Dict]:
        """获取任务信息
        
        Args:
            conversation_id: 会话ID，如果为None则返回所有任务信息
            
        Returns:
            任务信息列表
        """
        result = []
        
        if conversation_id:
            # 只返回指定会话的任务信息
            tasks = self._tasks.get(conversation_id, set())
            for task in tasks:
                task_info = self._task_info.get(task, {}).copy()
                task_info["done"] = task.done()
                task_info["cancelled"] = task.cancelled()
                result.append(task_info)
        else:
            # 返回所有任务信息
            for conversation_id, tasks in self._tasks.items():
                for task in tasks:
                    task_info = self._task_info.get(task, {}).copy()
                    task_info["done"] = task.done()
                    task_info["cancelled"] = task.cancelled()
                    result.append(task_info)
        
        return result
