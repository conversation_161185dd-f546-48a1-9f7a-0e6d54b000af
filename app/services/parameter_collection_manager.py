"""
参数收集状态管理器

专门处理参数收集过程中的状态维护，确保参数收集的连续性和正确性。
"""

import logging
from typing import Dict, Any, Optional, List, Tuple, Set

logger = logging.getLogger(__name__)

# 定义训练参数列表
TRAINING_PARAMS = [
    "body_part",
    "scenario",
    "equipment",
    "training_goal",
    "difficulty",
    "duration",
    "frequency"
]

# 定义用户信息字段列表
USER_INFO_FIELDS = [
    "gender",
    "age",
    "height",
    "weight",
    "fitness_goal",
    "experience_level"
]


class ParameterCollectionManager:
    """参数收集状态管理器，负责处理参数收集过程中的状态维护"""

    @staticmethod
    def is_collecting_parameters(meta_info: Dict[str, Any]) -> bool:
        """检查是否处于参数收集状态

        Args:
            meta_info: 元数据

        Returns:
            是否处于参数收集状态
        """
        return meta_info.get("collecting_training_params", False) or meta_info.get("waiting_for_info") is not None

    @staticmethod
    def get_current_collection_state(meta_info: Dict[str, Any]) -> Tuple[str, Optional[str]]:
        """获取当前参数收集状态

        Args:
            meta_info: 元数据

        Returns:
            (状态类型, 当前参数)，状态类型可能是 "training_params", "user_info" 或 "none"
        """
        if meta_info.get("collecting_training_params", False):
            return ("training_params", meta_info.get("asking_param"))
        elif meta_info.get("waiting_for_info") is not None:
            field = meta_info.get("waiting_for_info", {}).get("field")
            return ("user_info", field)
        else:
            return ("none", None)

    @staticmethod
    def start_training_param_collection(meta_info: Dict[str, Any], param: str) -> Dict[str, Any]:
        """开始训练参数收集

        Args:
            meta_info: 元数据
            param: 要收集的参数

        Returns:
            更新后的元数据
        """
        result = meta_info.copy()
        result["collecting_training_params"] = True
        result["asking_param"] = param
        
        # 确保训练参数字典存在
        if "training_params" not in result:
            result["training_params"] = {}
            
        logger.info(f"开始收集训练参数: {param}")
        return result

    @staticmethod
    def start_user_info_collection(meta_info: Dict[str, Any], field: str) -> Dict[str, Any]:
        """开始用户信息收集

        Args:
            meta_info: 元数据
            field: 要收集的字段

        Returns:
            更新后的元数据
        """
        result = meta_info.copy()
        result["waiting_for_info"] = {"field": field, "attempt": 0}
        
        logger.info(f"开始收集用户信息: {field}")
        return result

    @staticmethod
    def update_training_param(meta_info: Dict[str, Any], param: str, value: Any) -> Dict[str, Any]:
        """更新训练参数

        Args:
            meta_info: 元数据
            param: 参数名
            value: 参数值

        Returns:
            更新后的元数据
        """
        result = meta_info.copy()
        
        # 确保训练参数字典存在
        if "training_params" not in result:
            result["training_params"] = {}
            
        # 更新参数值
        result["training_params"][param] = value
        logger.info(f"更新训练参数: {param}={value}")
        
        return result

    @staticmethod
    def complete_parameter_collection(meta_info: Dict[str, Any]) -> Dict[str, Any]:
        """完成参数收集

        Args:
            meta_info: 元数据

        Returns:
            更新后的元数据
        """
        result = meta_info.copy()
        
        # 重置参数收集状态
        result.pop("collecting_training_params", None)
        result.pop("asking_param", None)
        result.pop("waiting_for_info", None)
        
        logger.info("完成参数收集")
        return result

    @staticmethod
    def get_missing_training_params(meta_info: Dict[str, Any], required_params: List[str]) -> List[str]:
        """获取缺失的训练参数

        Args:
            meta_info: 元数据
            required_params: 必需的参数列表

        Returns:
            缺失的参数列表
        """
        # 获取当前训练参数
        training_params = meta_info.get("training_params", {})
        
        # 检查缺失的参数
        missing_params = []
        for param in required_params:
            if param not in training_params or not training_params[param]:
                missing_params.append(param)
                
        return missing_params

    @staticmethod
    def get_missing_user_info(meta_info: Dict[str, Any], user_data: Dict[str, Any], required_fields: List[str]) -> List[str]:
        """获取缺失的用户信息

        Args:
            meta_info: 元数据
            user_data: 用户数据
            required_fields: 必需的字段列表

        Returns:
            缺失的字段列表
        """
        # 检查缺失的字段
        missing_fields = []
        for field in required_fields:
            if field not in user_data or not user_data[field]:
                missing_fields.append(field)
                
        return missing_fields

    @staticmethod
    def get_next_parameter_to_collect(meta_info: Dict[str, Any], intent: str) -> Optional[str]:
        """获取下一个要收集的参数

        Args:
            meta_info: 元数据
            intent: 当前意图

        Returns:
            下一个要收集的参数，如果没有则返回None
        """
        # 根据意图确定必需的参数
        required_params = ParameterCollectionManager.get_required_params_for_intent(intent)
        
        # 获取缺失的参数
        missing_params = ParameterCollectionManager.get_missing_training_params(meta_info, required_params)
        
        # 返回第一个缺失的参数
        return missing_params[0] if missing_params else None

    @staticmethod
    def get_required_params_for_intent(intent: str) -> List[str]:
        """获取指定意图所需的参数

        Args:
            intent: 意图

        Returns:
            所需的参数列表
        """
        # 根据意图确定必需的参数
        if intent == "recommend_exercise":
            return ["body_part", "scenario", "equipment"]
        elif intent == "daily_workout_plan":
            return ["body_part", "scenario", "training_goal", "duration"]
        elif intent == "weekly_workout_plan":
            return ["body_part", "scenario", "training_goal", "frequency"]
        elif intent == "search_exercise":
            return ["body_part", "equipment"]
        else:
            return []
