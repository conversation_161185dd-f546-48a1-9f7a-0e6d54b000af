"""
元数据管理模块

提供元数据处理、合并和管理的功能，确保元数据在不同意图之间的正确转换和参数保留。
"""

import logging
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime

logger = logging.getLogger(__name__)

# 定义意图类别
TRAINING_INTENTS = ["daily_workout_plan", "weekly_workout_plan", "search_exercise", "recommend_exercise", "discuss_training_plan"]
DIET_INTENTS = ["diet_advice", "nutrition_advice", "macro_calculation", "diet_suggestion"]
FITNESS_QA_INTENTS = ["fitness_qa", "exercise_info", "body_fat_calculation", "calorie_calculation"]
GENERAL_INTENTS = ["general_chat", "greeting", "farewell"]

# 定义通用训练参数
COMMON_TRAINING_PARAMS = ["body_part", "scenario", "equipment", "training_goal"]

# 定义保留的基本字段
PRESERVED_BASIC_FIELDS = ["user_id", "db_message_id", "session_id", "conversation_id"]


class MetaInfoManager:
    """元数据管理器，负责处理、合并和管理元数据"""

    @staticmethod
    def merge_meta_info(base_meta_info: Dict[str, Any], update_meta_info: Dict[str, Any], original_intent: str = None) -> Dict[str, Any]:
        """合并元数据，特别处理训练参数，并智能处理意图变更

        Args:
            base_meta_info: 基础元数据
            update_meta_info: 要合并的元数据
            original_intent: 原始意图，如果提供则保持不变

        Returns:
            合并后的元数据
        """
        # 创建副本避免修改原始对象
        result = base_meta_info.copy()

        # 检查新旧意图
        old_intent = result.get("intent")
        new_intent = update_meta_info.get("intent")

        # 记录意图变更情况
        if old_intent and new_intent and old_intent != new_intent:
            logger.info(f"检测到意图变更: {old_intent} -> {new_intent}, 置信度: {update_meta_info.get('confidence', 'unknown')}")

            # 检查意图类别
            old_is_training = old_intent in TRAINING_INTENTS
            new_is_training = new_intent in TRAINING_INTENTS
            old_is_diet = old_intent in DIET_INTENTS
            new_is_diet = new_intent in DIET_INTENTS
            old_is_qa = old_intent in FITNESS_QA_INTENTS
            new_is_qa = new_intent in FITNESS_QA_INTENTS
            old_is_general = old_intent in GENERAL_INTENTS
            new_is_general = new_intent in GENERAL_INTENTS

            # 检查是否处于参数收集状态
            is_collecting_params = base_meta_info.get("collecting_training_params", False)
            is_waiting_for_info = base_meta_info.get("waiting_for_info") is not None

            # 检查新意图的置信度
            new_confidence = update_meta_info.get("confidence", 0)

            # 如果正在收集参数，且新意图置信度不够高，保持原意图
            if (is_collecting_params or is_waiting_for_info) and new_confidence < 0.85:
                if original_intent:
                    logger.info(f"正在收集参数，保持原始意图: {original_intent}，忽略低置信度新意图: {new_intent} ({new_confidence})")
                    # 不进行意图变更，保持原始状态
                    update_meta_info.pop("intent", None)  # 从更新中移除意图
                    # 确保后续代码不会处理意图变更
                    new_intent = old_intent
            else:
                # 意图类别变更处理
                intent_category_changed = (
                    (old_is_training and not new_is_training) or
                    (old_is_diet and not new_is_diet) or
                    (old_is_qa and not new_is_qa) or
                    (old_is_general and not new_is_general)
                )

                # 如果意图类别发生变化，或从一般聊天变为特定意图
                if intent_category_changed or (old_is_general and not new_is_general):
                    # 保留用户ID等基本信息，但重置意图相关信息
                    preserved_data = {k: v for k, v in result.items() if k in PRESERVED_BASIC_FIELDS}

                    # 重置result，只保留基本信息
                    result = preserved_data
                    logger.info(f"意图类别变更: {old_intent} -> {new_intent}，完全重置meta_info")

                    # 重置参数收集状态
                    result.pop("collecting_training_params", None)
                    result.pop("waiting_for_info", None)
                    result.pop("asking_param", None)
                    result.pop("training_params", None)

                    # 记录重置操作
                    logger.info("重置所有参数收集状态和训练参数")
                elif old_is_training and new_is_training:
                    # 同为训练相关意图，但具体意图不同，保留部分训练参数
                    logger.info(f"训练相关意图变更: {old_intent} -> {new_intent}，保留通用训练参数")

                    # 保留通用训练参数（如body_part, scenario等），但重置特定意图参数
                    if "training_params" in result:
                        # 保存需要保留的通用参数
                        common_params = {}
                        for param in COMMON_TRAINING_PARAMS:
                            if param in result.get("training_params", {}) and result["training_params"][param]:
                                common_params[param] = result["training_params"][param]

                        # 重置训练参数，只保留通用参数
                        result["training_params"] = common_params
                        logger.info(f"保留通用训练参数: {common_params}")

                    # 重置参数收集状态
                    result.pop("collecting_training_params", None)
                    result.pop("asking_param", None)
                    logger.info("重置参数收集状态")

        # 保存当前训练参数，避免被覆盖
        current_training_params = result.get("training_params", {}).copy() if "training_params" in result else {}

        # 更新元数据
        for key, value in update_meta_info.items():
            if key == "training_params" and isinstance(value, dict):
                # 特殊处理训练参数，确保正确合并而不是覆盖
                if "training_params" not in result:
                    result["training_params"] = {}

                # 合并训练参数，保留所有非空值
                for param_key, param_value in value.items():
                    # 只有当新值非空或当前值为空时才更新
                    if param_value or not current_training_params.get(param_key):
                        result["training_params"][param_key] = param_value
                        logger.info(f"更新参数: {param_key}={param_value}")
            elif key == "intent":
                # 处理意图更新
                if original_intent and (base_meta_info.get("collecting_training_params") or base_meta_info.get("waiting_for_info")):
                    # 在参数收集状态下保持原始意图不变
                    result[key] = original_intent
                    logger.info(f"保持原始意图: {original_intent}，忽略新意图: {value}")
                else:
                    # 在正常对话状态下，允许更新意图
                    result[key] = value
                    if old_intent and old_intent != value:
                        logger.info(f"更新意图: {old_intent} -> {value}")
                    else:
                        logger.info(f"设置意图: {value}")
            else:
                # 其他元数据直接更新
                result[key] = value

        # 确保body_part参数不会被清空
        if ("training_params" in result and
            "body_part" in current_training_params and
            current_training_params["body_part"] and
            (not result["training_params"].get("body_part"))):
            result["training_params"]["body_part"] = current_training_params["body_part"]
            logger.info(f"恢复body_part参数: {current_training_params['body_part']}")

        return result

    @staticmethod
    def get_intent_category(intent: str) -> str:
        """获取意图所属的类别

        Args:
            intent: 意图名称

        Returns:
            意图类别: "training", "diet", "fitness_qa", "general" 或 "unknown"
        """
        if intent in TRAINING_INTENTS:
            return "training"
        elif intent in DIET_INTENTS:
            return "diet"
        elif intent in FITNESS_QA_INTENTS:
            return "fitness_qa"
        elif intent in GENERAL_INTENTS:
            return "general"
        else:
            return "unknown"

    @staticmethod
    def extract_common_params(training_params: Dict[str, Any]) -> Dict[str, Any]:
        """提取通用训练参数

        Args:
            training_params: 训练参数字典

        Returns:
            只包含通用参数的字典
        """
        if not training_params:
            return {}

        common_params = {}
        for param in COMMON_TRAINING_PARAMS:
            if param in training_params and training_params[param]:
                common_params[param] = training_params[param]

        return common_params

    @staticmethod
    def reset_collection_state(meta_info: Dict[str, Any]) -> Dict[str, Any]:
        """重置参数收集状态

        Args:
            meta_info: 元数据字典

        Returns:
            重置参数收集状态后的元数据字典
        """
        result = meta_info.copy()
        result.pop("collecting_training_params", None)
        result.pop("waiting_for_info", None)
        result.pop("asking_param", None)

        return result

    @staticmethod
    def check_session_timeout(last_message_time: Any, current_time: Any, meta_info: Dict[str, Any]) -> Dict[str, Any]:
        """检查会话时间间隔，如果超过阈值则重置元数据

        Args:
            last_message_time: 上一条消息的时间
            current_time: 当前时间
            meta_info: 当前元数据

        Returns:
            更新后的元数据，如果超时则重置
        """
        # 设置超时阈值为5分钟（300秒）
        TIMEOUT_THRESHOLD = 300

        # 如果没有元数据或时间信息，直接返回原始元数据
        if not meta_info or not last_message_time or not current_time:
            return meta_info

        # 创建副本避免修改原始对象
        result = meta_info.copy()

        # 计算时间差（秒）
        try:
            # 导入时间工具函数
            try:
                from app.utils.time_utils import calculate_time_diff_seconds, ensure_timezone
            except ImportError:
                # 如果找不到专用工具，使用内置方法
                def ensure_timezone(dt):
                    """确保时间有时区信息"""
                    if isinstance(dt, datetime) and dt.tzinfo is None:
                        from datetime import timezone
                        return dt.replace(tzinfo=timezone.utc)
                    return dt

                def calculate_time_diff_seconds(dt1, dt2):
                    """计算两个时间的差值（秒）"""
                    if isinstance(dt1, datetime) and isinstance(dt2, datetime):
                        diff = dt1 - dt2
                        return abs(diff.total_seconds())
                    return None

            # 确保时间有时区信息
            last_message_time = ensure_timezone(last_message_time)
            current_time = ensure_timezone(current_time)

            # 计算时间差
            time_diff = calculate_time_diff_seconds(current_time, last_message_time)

            if time_diff is None:
                logger.error("计算时间差失败，返回None")
                return result

            logger.info(f"会话时间间隔: {time_diff}秒")

            # 如果时间差超过阈值，重置元数据
            if time_diff > TIMEOUT_THRESHOLD:
                logger.info(f"会话时间间隔超过阈值({TIMEOUT_THRESHOLD}秒)，重置元数据")

                # 保留基本信息
                preserved_data = {k: v for k, v in result.items() if k in PRESERVED_BASIC_FIELDS}

                # 记录重置操作
                logger.info(f"会话超时重置元数据: 原始元数据={result}, 保留字段={preserved_data}")

                return preserved_data

            return result
        except Exception as e:
            logger.error(f"检查会话时间间隔时出错: {str(e)}")
            return result
