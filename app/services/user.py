from typing import Optional
from sqlalchemy.orm import Session
from app.models.user import User, Gender
from app.schemas.user import UserCreate, UserUpdate
from app.core.security import get_password_hash, verify_password
from app.services.wechat import WechatService
import logging

logger = logging.getLogger(__name__)

def get_user(db: Session, user_id: int) -> Optional[User]:
    return db.query(User).filter(User.id == user_id).first()

def get_user_by_phone(db: Session, phone: str) -> Optional[User]:
    return db.query(User).filter(User.phone == phone).first()

def get_user_by_openid(db: Session, openid: str) -> Optional[User]:
    return db.query(User).filter(User.openid == openid).first()

def create_user(db: Session, user: UserCreate) -> User:
    hashed_password = get_password_hash(user.password)
    db_user = User(
        phone=user.phone,
        hashed_password=hashed_password,
        nickname=user.nickname,
        gender=user.gender,
        birthday=user.birthday,
        height=user.height,
        weight=user.weight,
        body_type=user.body_type,
        experience_level=user.experience_level,
        fitness_goal=user.fitness_goal
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

def update_user(db: Session, user_id: int, user_update: UserUpdate) -> Optional[User]:
    db_user = get_user(db, user_id)
    if not db_user:
        return None
    
    update_data = user_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_user, field, value)
    
    db.commit()
    db.refresh(db_user)
    return db_user

def authenticate_user(db: Session, phone: str, password: str) -> Optional[User]:
    user = get_user_by_phone(db, phone)
    if not user:
        return None
    if not verify_password(password, user.hashed_password):
        return None
    return user

class UserService:
    @staticmethod
    def get_by_email(db: Session, email: str) -> Optional[User]:
        return db.query(User).filter(User.email == email).first()
    
    @staticmethod
    def get_by_openid(db: Session, openid: str) -> Optional[User]:
        """
        根据openid获取用户，使用更高效的查询
        """
        if not openid:
            return None
            
        # 使用更高效的查询方式
        return db.query(User).filter(User.openid == openid).first()
    
    @staticmethod
    def get_by_phone(db: Session, phone: str) -> Optional[User]:
        return db.query(User).filter(User.phone == phone).first()
    
    @staticmethod
    async def wechat_login(
        db: Session, 
        code: Optional[str] = None, 
        nickname: Optional[str] = None,
        avatar_url: Optional[str] = None,
        phone: Optional[str] = None,
        openid: Optional[str] = None,
        unionid: Optional[str] = None
    ):
        """创建或更新微信用户
        
        Args:
            db: 数据库会话
            code: 微信登录code
            nickname: 微信昵称
            avatar_url: 头像URL
            phone: 手机号
            openid: 用户openid
            unionid: 用户unionid
        
        Returns:
            User: 用户对象
        """
        try:
            # 确认必要参数
            if not openid and not code:
                error_msg = "缺少必要参数: openid或code必须提供至少一个"
                logger.error(error_msg)
                print(f"【调试错误】{error_msg}")
                raise ValueError(error_msg)
            
            # 获取微信openid和session_key
            if not openid and code:
                try:
                    # 简化日志记录
                    code_prefix = code[:5] + "***" if code and len(code) > 5 else code
                    logger.info(f"使用code获取openid: {code_prefix}")
                    print(f"【调试信息】使用code获取openid: {code_prefix}")
                    
                    wx_result = await WechatService.code2session(code)
                    openid = wx_result.get("openid")
                    # unionid可能会在code2session中返回
                    if not unionid and "unionid" in wx_result:
                        unionid = wx_result.get("unionid")
                    
                    # 简化日志记录
                    openid_prefix = openid[:5] + "***" if openid and len(openid) > 5 else None
                    logger.info(f"成功从code获取openid: {openid_prefix}")
                    print(f"【调试信息】成功从code获取openid: {openid_prefix}")
                except Exception as e:
                    error_msg = f"通过code获取openid失败: {str(e)}"
                    logger.error(error_msg)
                    print(f"【调试错误】{error_msg}")
                    raise ValueError(error_msg)
                
            # 确保openid存在
            if not openid:
                error_msg = "获取openid失败"
                logger.error(error_msg)
                print(f"【调试错误】{error_msg}")
                raise ValueError(error_msg)
            
            # 简化日志记录
            openid_prefix = openid[:5] + "***" if openid else None
            
            # 使用默认值确保必要的信息存在
            if not nickname:
                nickname = "微信用户"
            
            if not avatar_url:
                avatar_url = "https://example.com/default-avatar.jpg"
            
            # 查找用户是否已存在 - 使用索引字段提高查询速度
            user = UserService.get_by_openid(db, openid)
            
            # 创建或更新用户
            try:
                if not user:
                    # 创建新用户
                    logger.info(f"创建新用户: openid={openid_prefix}, nickname={nickname}")
                    print(f"【调试信息】创建新用户: openid={openid_prefix}, nickname={nickname}")
                    
                    user = User(
                        openid=openid,
                        unionid=unionid,
                        nickname=nickname,
                        avatar_url=avatar_url,
                        phone=phone,
                        gender=0,  # 使用整数 0 表示未知性别
                        country="",
                        province="",
                        city="",
                        completed=False    # 新用户默认未完成资料
                    )
                    
                    try:
                        db.add(user)
                        db.commit()
                        db.refresh(user)
                        logger.info(f"用户创建成功: ID={user.id}")
                        print(f"【调试信息】用户创建成功: ID={user.id}")
                    except Exception as db_error:
                        logger.error(f"数据库操作失败 - 创建用户: {str(db_error)}")
                        print(f"【调试错误】数据库操作失败 - 创建用户: {str(db_error)}")
                        db.rollback()  # 回滚事务
                        raise ValueError(f"创建用户失败: {str(db_error)}")
                else:
                    # 更新现有用户信息 - 只在必要时更新
                    logger.info(f"更新现有用户: ID={user.id}, openid={openid_prefix}")
                    print(f"【调试信息】更新现有用户: ID={user.id}, openid={openid_prefix}")
                    
                    update_needed = False
                    
                    # 只有在值不为空且与当前值不同时才更新
                    if nickname and nickname != "微信用户" and nickname != user.nickname:
                        user.nickname = nickname
                        update_needed = True
                    
                    if avatar_url and avatar_url != "https://example.com/default-avatar.jpg" and avatar_url != user.avatar_url:
                        user.avatar_url = avatar_url
                        update_needed = True
                    
                    if phone and not user.phone:
                        user.phone = phone
                        update_needed = True
                    
                    if unionid and not user.unionid:
                        user.unionid = unionid
                        update_needed = True
                    
                    # 只有在需要更新时才执行数据库操作
                    if update_needed:
                        try:
                            db.commit()
                            db.refresh(user)
                            logger.info(f"用户信息已更新: ID={user.id}")
                            print(f"【调试信息】用户信息已更新: ID={user.id}")
                        except Exception as db_error:
                            logger.error(f"数据库操作失败 - 更新用户: {str(db_error)}")
                            print(f"【调试错误】数据库操作失败 - 更新用户: {str(db_error)}")
                            db.rollback()  # 回滚事务
                            raise ValueError(f"更新用户失败: {str(db_error)}")
                    else:
                        logger.info(f"无需更新用户信息: ID={user.id}")
                        print(f"【调试信息】无需更新用户信息: ID={user.id}")
                
                return user
                
            except Exception as e:
                logger.error(f"创建或更新用户失败: {str(e)}")
                print(f"【调试错误】创建或更新用户失败: {str(e)}")
                raise ValueError(f"创建或更新用户失败: {str(e)}")
            
        except Exception as e:
            logger.error(f"微信登录处理失败: {str(e)}")
            print(f"【调试错误】微信登录处理失败: {str(e)}")
            raise ValueError(f"微信登录处理失败: {str(e)}") 