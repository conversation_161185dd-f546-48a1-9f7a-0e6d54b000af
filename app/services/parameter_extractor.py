"""
参数提取器 - 负责从用户消息中提取训练相关参数
"""
from typing import Dict, Any, List, Optional, Tuple
import logging
import re
import json
from app.services.llm_proxy_service import LLMProxyService
from app.core.chat_config import MODELS, BAILIAN_APPS

# 配置日志
logger = logging.getLogger(__name__)

# 全局LLM实例
llm_service = LLMProxyService()

class ParameterExtractor:
    """参数提取器类，负责从用户消息中提取训练相关参数"""
    
    # 身体部位关键词映射
    BODY_PARTS_KEYWORDS = {
        "胸部": ["胸", "胸肌", "胸大肌", "上胸", "中胸", "下胸", "pecs", "chest"],
        "背部": ["背", "背肌", "背阔肌", "斜方肌", "菱形肌", "back", "lats"],
        "肩部": ["肩", "肩膀", "三角肌", "前束", "中束", "后束", "shoulder", "deltoid"],
        "手臂": ["手臂", "二头肌", "三头肌", "肱二头肌", "肱三头肌", "biceps", "triceps", "arms"],
        "腿部": ["腿", "大腿", "小腿", "股四头肌", "腘绳肌", "腓肠肌", "legs", "quads", "hamstrings"],
        "臀部": ["臀", "臀大肌", "臀中肌", "臀小肌", "glutes"],
        "核心": ["核心", "腹肌", "腹直肌", "腹外斜肌", "腹内斜肌", "腹横肌", "core", "abs"],
        "全身": ["全身", "整体", "full body", "全部"]
    }
    
    # 训练场景关键词映射
    TRAINING_SCENE_KEYWORDS = {
        "健身房": ["健身房", "gym", "器械", "器材", "健身中心", "健身会所"],
        "家庭": ["家", "家里", "家庭", "居家", "home", "自重"]
    }
    
    # 计划类型关键词映射
    PLAN_TYPE_KEYWORDS = {
        "单日": ["单日", "一天", "今天", "单次", "一次", "daily", "单练"],
        "周计划": ["周", "一周", "每周", "weekly", "7天", "七天", "week"],
        "月计划": ["月", "一个月", "monthly", "30天", "三十天", "month"]
    }
    
    # 训练目标关键词映射
    TRAINING_GOAL_KEYWORDS = {
        "增肌": ["增肌", "长肌肉", "增长肌肉", "肌肥大", "muscle gain", "hypertrophy"],
        "减脂": ["减脂", "减肥", "瘦身", "减重", "fat loss", "weight loss"],
        "力量": ["力量", "增强力量", "变强", "strength"],
        "耐力": ["耐力", "持久力", "cardio", "endurance"],
        "塑形": ["塑形", "线条", "紧致", "tone", "shaping"]
    }
    
    # 健身水平关键词映射
    FITNESS_LEVEL_KEYWORDS = {
        "初级": ["初级", "初学者", "新手", "beginner", "入门"],
        "中级": ["中级", "有经验", "intermediate"],
        "高级": ["高级", "专业", "advanced", "expert"]
    }
    
    @classmethod
    async def extract_parameters(cls, message: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        从用户消息中提取训练相关参数
        
        Args:
            message: 用户消息
            context: 上下文信息，包含已知参数等
            
        Returns:
            提取的参数字典
        """
        # 初始化结果
        parameters = {}
        
        # 合并上下文中的已知参数
        if context and "training_params" in context:
            parameters.update(context["training_params"])
        
        # 1. 使用关键词匹配提取基本参数
        basic_params = cls._extract_basic_parameters(message)
        parameters.update(basic_params)
        
        # 2. 使用百炼应用提取复杂参数
        try:
            llm_params = await cls._extract_parameters_with_llm(message, parameters)
            # 合并LLM提取的参数，但不覆盖已有参数
            for key, value in llm_params.items():
                if key not in parameters or not parameters[key]:
                    parameters[key] = value
        except Exception as e:
            logger.error(f"使用LLM提取参数失败: {str(e)}")
        
        # 3. 参数标准化和验证
        normalized_params = cls._normalize_parameters(parameters)
        
        return normalized_params
    
    @classmethod
    def _extract_basic_parameters(cls, message: str) -> Dict[str, Any]:
        """使用关键词匹配提取基本参数"""
        parameters = {}
        
        # 提取身体部位
        body_part = cls._extract_keyword_match(message, cls.BODY_PARTS_KEYWORDS)
        if body_part:
            parameters["body_part"] = body_part
        
        # 提取训练场景
        training_scene = cls._extract_keyword_match(message, cls.TRAINING_SCENE_KEYWORDS)
        if training_scene:
            parameters["training_scene"] = training_scene
        
        # 提取计划类型
        plan_type = cls._extract_keyword_match(message, cls.PLAN_TYPE_KEYWORDS)
        if plan_type:
            parameters["plan_type"] = plan_type
        
        # 提取训练目标
        training_goal = cls._extract_keyword_match(message, cls.TRAINING_GOAL_KEYWORDS)
        if training_goal:
            parameters["training_goal"] = training_goal
        
        # 提取健身水平
        fitness_level = cls._extract_keyword_match(message, cls.FITNESS_LEVEL_KEYWORDS)
        if fitness_level:
            parameters["fitness_level"] = fitness_level
        
        # 提取训练时长（分钟）
        duration_match = re.search(r'(\d+)\s*(分钟|min)', message)
        if duration_match:
            parameters["duration_minutes"] = int(duration_match.group(1))
        
        # 提取训练频率（每周几次）
        frequency_match = re.search(r'每周\s*(\d+)\s*次', message)
        if frequency_match:
            parameters["frequency_per_week"] = int(frequency_match.group(1))
        
        return parameters
    
    @classmethod
    def _extract_keyword_match(cls, message: str, keyword_mapping: Dict[str, List[str]]) -> Optional[str]:
        """从消息中提取关键词匹配"""
        for key, keywords in keyword_mapping.items():
            for keyword in keywords:
                if keyword in message:
                    return key
        return None
    
    @classmethod
    async def _extract_parameters_with_llm(cls, message: str, existing_params: Dict[str, Any]) -> Dict[str, Any]:
        """使用大语言模型提取复杂参数"""
        # 构建提示词
        existing_params_str = ", ".join([f"{k}: {v}" for k, v in existing_params.items()])
        
        prompt = f"""
        请从用户消息中提取训练相关参数。如果无法确定某个参数，请返回null。

        用户消息: "{message}"

        已知参数: {existing_params_str}

        请提取以下参数（JSON格式）:
        - body_part: 训练部位，如胸部、背部、腿部等
        - training_scene: 训练场景，如健身房、家庭等
        - plan_type: 计划类型，如单日、周计划等
        - training_goal: 训练目标，如增肌、减脂等
        - fitness_level: 健身水平，如初级、中级、高级等
        - duration_minutes: 训练时长（分钟）
        - frequency_per_week: 训练频率（每周几次）
        - equipment: 训练器材，如哑铃、杠铃等

        只返回JSON格式，不要有任何其他文字。
        """
        
        # 调用百炼应用提取参数
        response = await llm_service.aget_chat_response(
            messages=[
                {"role": "system", "content": "你是一个参数提取助手，负责从用户消息中提取训练相关参数。"},
                {"role": "user", "content": prompt}
            ],
            model="agent-app",
            temperature=0.1
        )
        
        # 解析JSON响应
        try:
            # 尝试直接解析JSON
            parameters = json.loads(response)
            # 过滤掉None值
            return {k: v for k, v in parameters.items() if v is not None}
        except json.JSONDecodeError:
            # 如果直接解析失败，尝试从文本中提取JSON
            try:
                # 查找JSON开始和结束的位置
                start_idx = response.find('{')
                end_idx = response.rfind('}') + 1
                if start_idx >= 0 and end_idx > start_idx:
                    json_str = response[start_idx:end_idx]
                    parameters = json.loads(json_str)
                    # 过滤掉None值
                    return {k: v for k, v in parameters.items() if v is not None}
                else:
                    logger.warning(f"无法从响应中提取JSON: {response}")
                    return {}
            except Exception as e:
                logger.error(f"解析参数JSON失败: {str(e)}")
                return {}
    
    @classmethod
    def _normalize_parameters(cls, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """参数标准化和验证"""
        normalized = {}
        
        # 标准化身体部位
        if "body_part" in parameters:
            body_part = parameters["body_part"]
            # 确保使用标准名称
            for standard_name, keywords in cls.BODY_PARTS_KEYWORDS.items():
                if body_part.lower() in [k.lower() for k in keywords]:
                    normalized["body_part"] = standard_name
                    break
            if "body_part" not in normalized:
                normalized["body_part"] = body_part
        
        # 标准化训练场景
        if "training_scene" in parameters:
            scene = parameters["training_scene"]
            # 确保使用标准名称
            for standard_name, keywords in cls.TRAINING_SCENE_KEYWORDS.items():
                if scene.lower() in [k.lower() for k in keywords]:
                    normalized["training_scene"] = standard_name
                    break
            if "training_scene" not in normalized:
                normalized["training_scene"] = scene
        
        # 标准化计划类型
        if "plan_type" in parameters:
            plan_type = parameters["plan_type"]
            # 确保使用标准名称
            for standard_name, keywords in cls.PLAN_TYPE_KEYWORDS.items():
                if plan_type.lower() in [k.lower() for k in keywords]:
                    normalized["plan_type"] = standard_name
                    break
            if "plan_type" not in normalized:
                normalized["plan_type"] = plan_type
        
        # 标准化训练目标
        if "training_goal" in parameters:
            goal = parameters["training_goal"]
            # 确保使用标准名称
            for standard_name, keywords in cls.TRAINING_GOAL_KEYWORDS.items():
                if goal.lower() in [k.lower() for k in keywords]:
                    normalized["training_goal"] = standard_name
                    break
            if "training_goal" not in normalized:
                normalized["training_goal"] = goal
        
        # 标准化健身水平
        if "fitness_level" in parameters:
            level = parameters["fitness_level"]
            # 确保使用标准名称
            for standard_name, keywords in cls.FITNESS_LEVEL_KEYWORDS.items():
                if level.lower() in [k.lower() for k in keywords]:
                    normalized["fitness_level"] = standard_name
                    break
            if "fitness_level" not in normalized:
                normalized["fitness_level"] = level
        
        # 复制其他参数
        for key, value in parameters.items():
            if key not in normalized:
                normalized[key] = value
        
        return normalized
    
    @classmethod
    def check_parameters_completeness(cls, parameters: Dict[str, Any], required_params: List[str]) -> List[str]:
        """
        检查参数完整性，返回缺失的参数列表
        
        Args:
            parameters: 参数字典
            required_params: 必需的参数列表
            
        Returns:
            缺失的参数列表
        """
        missing_params = []
        
        for param in required_params:
            if param not in parameters or not parameters[param]:
                missing_params.append(param)
        
        return missing_params
    
    @classmethod
    def get_parameter_prompt(cls, param_name: str) -> str:
        """
        获取参数收集提示
        
        Args:
            param_name: 参数名称
            
        Returns:
            参数收集提示
        """
        prompts = {
            "body_part": "您想训练哪个部位？例如：胸部、背部、腿部等",
            "training_scene": "您计划在哪里训练？健身房还是家里？",
            "plan_type": "您需要单日训练计划还是周计划？",
            "training_goal": "您的训练目标是什么？增肌、减脂还是其他？",
            "fitness_level": "您的健身水平如何？初级、中级还是高级？",
            "duration_minutes": "您每次训练的时间大约是多少分钟？",
            "frequency_per_week": "您计划每周训练几次？"
        }
        
        return prompts.get(param_name, f"请提供{param_name}的信息")
    
    @classmethod
    async def infer_default_parameters(cls, user_info: Dict[str, Any], message: str) -> Dict[str, Any]:
        """
        根据用户信息和消息推断默认参数
        
        Args:
            user_info: 用户信息
            message: 用户消息
            
        Returns:
            推断的默认参数
        """
        defaults = {}
        
        # 根据健身水平推断默认训练频率
        if "fitness_level" in user_info:
            level = user_info["fitness_level"]
            if level == "初级":
                defaults["frequency_per_week"] = 3
            elif level == "中级":
                defaults["frequency_per_week"] = 4
            elif level == "高级":
                defaults["frequency_per_week"] = 5
        
        # 根据健身目标推断默认计划类型
        if "fitness_goal" in user_info:
            goal = user_info["fitness_goal"]
            if goal in ["增肌", "力量"]:
                defaults["plan_type"] = "周计划"
            else:
                defaults["plan_type"] = "单日"
        
        # 根据用户消息推断更多默认值
        try:
            prompt = f"""
            根据用户信息和消息，推断可能的默认训练参数。如果无法确定，请返回null。

            用户信息:
            {json.dumps(user_info, ensure_ascii=False)}

            用户消息:
            "{message}"

            请推断以下参数（JSON格式）:
            - training_scene: 训练场景，如健身房、家庭等
            - duration_minutes: 训练时长（分钟）

            只返回JSON格式，不要有任何其他文字。
            """
            
            response = await llm_service.aget_chat_response(
                messages=[
                    {"role": "system", "content": "你是一个参数推断助手，负责根据用户信息推断默认训练参数。"},
                    {"role": "user", "content": prompt}
                ],
                model="agent-app",
                temperature=0.1
            )
            
            try:
                inferred = json.loads(response)
                # 合并推断的参数，但不覆盖已有默认值
                for key, value in inferred.items():
                    if value is not None and key not in defaults:
                        defaults[key] = value
            except json.JSONDecodeError:
                logger.warning(f"无法解析推断参数响应: {response}")
        except Exception as e:
            logger.error(f"推断默认参数失败: {str(e)}")
        
        return defaults
