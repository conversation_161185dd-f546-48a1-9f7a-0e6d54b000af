from typing import Optional, List
from sqlalchemy.orm import Session
from decimal import Decimal

from app.crud.crud_nutrient import (
    get_vitamin_rni, get_mineral_rni, get_pregnancy_increment,
    get_water_rni, get_water_pregnancy_inc, get_other_dietary_spl_ul,
    get_nutrition_main, get_nutrition_pregnancy_inc
)
from app.schemas.nutrient import (
    NutrientResponse, VitaminValue, MineralValue, 
    MacroNutrientValue, WaterValue, OtherDietaryValue, NutrientBase
)

async def get_nutrient_target(
    db: Session, 
    age: Decimal, 
    sex: str, 
    pregnancy_stage: Optional[str] = None,
    dietary_names: Optional[List[str]] = None
) -> NutrientResponse:
    """
    根据用户年龄、性别和孕期阶段计算营养素目标值
    
    Args:
        db: 数据库会话
        age: 用户年龄(岁)
        sex: 性别: 'male' 或 'female'
        pregnancy_stage: 孕期阶段: 'early', 'mid', 'late', 'lactation' 或 None
        dietary_names: 营养素字段名称列表，如'vitamin_a'、'phosphor'等
    """
    # 1. 获取各类营养素基础推荐值
    vitamin_data = await get_vitamin_rni(db, age, sex)
    mineral_data = await get_mineral_rni(db, age, sex)
    water_data = await get_water_rni(db, age, sex)
    nutrition_data = await get_nutrition_main(db, age, sex)
    
    # 2. 如果是女性且处于孕期/哺乳期，获取增量
    vitamin_preg_data = None
    water_preg_data = None
    nutrition_preg_data = None
    if sex == 'female' and pregnancy_stage:
        vitamin_preg_data = await get_pregnancy_increment(db, pregnancy_stage)
        water_preg_data = await get_water_pregnancy_inc(db, pregnancy_stage)
        nutrition_preg_data = await get_nutrition_pregnancy_inc(db, pregnancy_stage)
    
    # 3. 组装维生素数据
    vitamins = VitaminValue(
        vitamin_a=NutrientBase(value=vitamin_data.vitamin_a, unit=vitamin_data.vitamin_a_unit),
        vitamin_d=NutrientBase(
            value=vitamin_data.vitamin_d + (vitamin_preg_data.vitamin_d_inc if vitamin_preg_data else 0), 
            unit=vitamin_data.vitamin_d_unit
        ),
        vitamin_e=NutrientBase(value=vitamin_data.vitamin_e, unit=vitamin_data.vitamin_e_unit),
        thiamine=NutrientBase(
            value=vitamin_data.thiamine + (vitamin_preg_data.thiamine_inc if vitamin_preg_data else 0), 
            unit=vitamin_data.thiamine_unit
        ),
        lactoflavin=NutrientBase(
            value=vitamin_data.lactoflavin + (vitamin_preg_data.lactoflavin_inc if vitamin_preg_data else 0), 
            unit=vitamin_data.lactoflavin_unit
        ),
        vitamin_b6=NutrientBase(value=vitamin_data.vitamin_b6, unit=vitamin_data.vitamin_b6_unit),
        vitamin_b12=NutrientBase(value=vitamin_data.vitamin_b12, unit=vitamin_data.vitamin_b12_unit),
        vitamin_c=NutrientBase(value=vitamin_data.vitamin_c, unit=vitamin_data.vitamin_c_unit),
        niacin=NutrientBase(value=vitamin_data.niacin, unit=vitamin_data.niacin_unit),
        folacin=NutrientBase(value=vitamin_data.folacin, unit=vitamin_data.folacin_unit),
        pantothenic=NutrientBase(value=vitamin_data.pantothenic, unit=vitamin_data.pantothenic_unit),
        biotin=NutrientBase(value=vitamin_data.biotin, unit=vitamin_data.biotin_unit),
        choline=NutrientBase(value=vitamin_data.choline, unit=vitamin_data.choline_unit),
        # 数据库中不存在的字段返回None
        vitamin_k=None,
        carotene=None
    )
    
    # 4. 组装矿物质数据
    minerals = MineralValue(
        calcium=NutrientBase(value=mineral_data.calcium, unit=mineral_data.calcium_unit),
        phosphor=NutrientBase(value=mineral_data.phosphor, unit=mineral_data.phosphor_unit),
        kalium=NutrientBase(value=mineral_data.kalium, unit=mineral_data.kalium_unit),
        natrium=NutrientBase(value=mineral_data.natrium, unit=mineral_data.natrium_unit),
        magnesium=NutrientBase(value=mineral_data.magnesium, unit=mineral_data.magnesium_unit),
        chlorine=NutrientBase(value=mineral_data.chlorine, unit=mineral_data.chlorine_unit),
        iron=NutrientBase(value=mineral_data.iron, unit=mineral_data.iron_unit),
        iodine=NutrientBase(value=mineral_data.iodine, unit=mineral_data.iodine_unit),
        zinc=NutrientBase(value=mineral_data.zinc, unit=mineral_data.zinc_unit),
        selenium=NutrientBase(value=mineral_data.selenium, unit=mineral_data.selenium_unit),
        copper=NutrientBase(value=mineral_data.copper, unit=mineral_data.copper_unit),
        fluorine=NutrientBase(value=mineral_data.fluorine, unit=mineral_data.fluorine_unit),
        # 数据库中不存在的字段返回None
        manganese=None
    )
    
    # 5. 组装水分数据
    water = WaterValue(
        drinking_water=NutrientBase(
            value=water_data.drinking_ml + (water_preg_data.drinking_inc_ml if water_preg_data else 0), 
            unit="mL"
        ),
        total_water=NutrientBase(
            value=water_data.total_water_ml + (water_preg_data.total_water_inc_ml if water_preg_data else 0), 
            unit="mL"
        )
    )
    
    # 6. 组装宏量营养素数据
    protein_rni = nutrition_data.protein_rni
    if nutrition_preg_data and nutrition_preg_data.protein_rni:
        protein_rni += nutrition_preg_data.protein_rni
        
    macros = MacroNutrientValue(
        protein=NutrientBase(value=nutrition_data.protein, unit="g/%E"),
        protein_rni=NutrientBase(value=protein_rni, unit="g/d"),
        fat=NutrientBase(value=nutrition_data.fat, unit="g/%E"),
        saturated_fat=NutrientBase(value=nutrition_data.saturated_fat, unit="g/%E"),
        pufa=NutrientBase(value=nutrition_data.pufa, unit="g/%E"),
        n3fa=NutrientBase(value=nutrition_data.n3fa, unit="g/%E"),
        carbohydrate=NutrientBase(value=nutrition_data.carbohydrate, unit="g/%E"),
        fiber_dietary=NutrientBase(
            value=nutrition_data.fiber_dietary + (nutrition_preg_data.fiber_dietary if nutrition_preg_data else 0), 
            unit="g/d"
        ),
        fructose=NutrientBase(value=nutrition_data.fructose, unit="g/%E")
    )
    
    # 7. 获取其他膳食成分SPL和UL值
    other_dietary = []
    if dietary_names:
        other_dietary_data = await get_other_dietary_spl_ul(db, dietary_names)
        for item in other_dietary_data:
            other_dietary.append(
                OtherDietaryValue(
                    name_cn=item.name_cn,
                    spl=NutrientBase(value=item.spl, unit=item.spl_unit) if item.spl else None,
                    ul=NutrientBase(value=item.ul, unit=item.ul_unit) if item.ul else None
                )
            )
    
    # 8. 返回最终结果
    notes = vitamin_preg_data.notes if vitamin_preg_data else None
    
    return NutrientResponse(
        vitamins=vitamins,
        minerals=minerals,
        macros=macros,
        water=water,
        other_dietary=other_dietary if other_dietary else None,
        recommendation_type=vitamin_data.rec_type,
        notes=notes
    )
