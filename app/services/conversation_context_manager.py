"""
会话上下文管理器 - 负责管理会话上下文，维护对话连贯性
"""
from typing import Dict, Any, List, Optional, Tuple
import logging
import json
import time
from datetime import datetime
from app.services.llm_proxy_service import LLMProxyService
from app.core.chat_config import MODELS

# 配置日志
logger = logging.getLogger(__name__)

# 全局LLM实例
llm_service = LLMProxyService()

class ConversationContextManager:
    """会话上下文管理器类，负责管理会话上下文，维护对话连贯性"""
    
    # 上下文压缩配置
    CONTEXT_COMPRESSION = {
        "max_messages": 20,        # 最大消息数量
        "max_tokens": 4000,        # 最大token数量
        "summary_interval": 10,    # 摘要生成间隔（消息数）
        "keep_recent": 5           # 保留最近的消息数量
    }
    
    @classmethod
    def save_conversation_state(cls, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        保存会话状态
        
        Args:
            state: 会话状态
            
        Returns:
            可序列化的会话状态
        """
        # 创建可序列化的状态副本
        serializable_state = {}
        
        # 处理消息历史
        if "messages" in state:
            serializable_state["messages"] = cls._serialize_messages(state["messages"])
        
        # 处理流程状态
        if "flow_state" in state:
            serializable_state["flow_state"] = state["flow_state"]
        
        # 处理元信息
        if "meta_info" in state:
            serializable_state["meta_info"] = state["meta_info"]
        
        # 处理用户信息
        if "user_info" in state:
            serializable_state["user_info"] = state["user_info"]
        
        # 处理训练参数
        if "training_params" in state:
            serializable_state["training_params"] = state["training_params"]
        
        # 添加时间戳
        serializable_state["timestamp"] = datetime.now().isoformat()
        
        return serializable_state
    
    @classmethod
    def restore_conversation_state(cls, serialized_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        恢复会话状态
        
        Args:
            serialized_state: 序列化的会话状态
            
        Returns:
            恢复的会话状态
        """
        # 创建恢复的状态
        restored_state = {}
        
        # 恢复消息历史
        if "messages" in serialized_state:
            restored_state["messages"] = cls._deserialize_messages(serialized_state["messages"])
        
        # 恢复流程状态
        if "flow_state" in serialized_state:
            restored_state["flow_state"] = serialized_state["flow_state"]
        
        # 恢复元信息
        if "meta_info" in serialized_state:
            restored_state["meta_info"] = serialized_state["meta_info"]
        
        # 恢复用户信息
        if "user_info" in serialized_state:
            restored_state["user_info"] = serialized_state["user_info"]
        
        # 恢复训练参数
        if "training_params" in serialized_state:
            restored_state["training_params"] = serialized_state["training_params"]
        
        return restored_state
    
    @classmethod
    def _serialize_messages(cls, messages: List[Any]) -> List[Dict[str, Any]]:
        """序列化消息列表"""
        serialized_messages = []
        
        for msg in messages:
            # 处理不同类型的消息对象
            if hasattr(msg, "to_dict"):
                # 如果消息对象有to_dict方法
                serialized_messages.append(msg.to_dict())
            elif hasattr(msg, "__dict__"):
                # 如果消息对象有__dict__属性
                serialized_messages.append(vars(msg))
            elif isinstance(msg, dict):
                # 如果消息已经是字典
                serialized_messages.append(msg)
            else:
                # 尝试提取常见属性
                serialized_msg = {}
                for attr in ["role", "content", "type", "timestamp"]:
                    if hasattr(msg, attr):
                        serialized_msg[attr] = getattr(msg, attr)
                
                if "role" in serialized_msg and "content" in serialized_msg:
                    serialized_messages.append(serialized_msg)
        
        return serialized_messages
    
    @classmethod
    def _deserialize_messages(cls, serialized_messages: List[Dict[str, Any]]) -> List[Any]:
        """反序列化消息列表"""
        # 导入消息类
        from langgraph.graph.message import AnyMessage
        
        deserialized_messages = []
        
        for msg in serialized_messages:
            # 创建AnyMessage对象
            if "role" in msg and "content" in msg:
                deserialized_messages.append(
                    AnyMessage(role=msg["role"], content=msg["content"])
                )
        
        return deserialized_messages
    
    @classmethod
    async def compress_message_history(cls, messages: List[Any], max_tokens: int = None) -> List[Any]:
        """
        压缩消息历史
        
        Args:
            messages: 消息列表
            max_tokens: 最大token数量，默认使用配置值
            
        Returns:
            压缩后的消息列表
        """
        if not messages:
            return []
        
        # 使用默认配置
        if max_tokens is None:
            max_tokens = cls.CONTEXT_COMPRESSION["max_tokens"]
        
        # 如果消息数量小于阈值，不进行压缩
        if len(messages) <= cls.CONTEXT_COMPRESSION["max_messages"]:
            return messages
        
        # 估算当前token数量
        estimated_tokens = cls._estimate_token_count(messages)
        
        # 如果token数量小于阈值，不进行压缩
        if estimated_tokens <= max_tokens:
            return messages
        
        logger.info(f"消息历史需要压缩: {len(messages)}条消息, 估计{estimated_tokens}个token")
        
        # 保留最近的消息
        recent_messages = messages[-cls.CONTEXT_COMPRESSION["keep_recent"]:]
        
        # 需要压缩的消息
        messages_to_compress = messages[:-cls.CONTEXT_COMPRESSION["keep_recent"]]
        
        # 生成摘要
        summary = await cls._generate_conversation_summary(messages_to_compress)
        
        # 创建摘要消息
        from langgraph.graph.message import AnyMessage
        summary_message = AnyMessage(
            role="system",
            content=f"以下是之前对话的摘要: {summary}"
        )
        
        # 返回压缩后的消息列表
        return [summary_message] + recent_messages
    
    @classmethod
    def _estimate_token_count(cls, messages: List[Any]) -> int:
        """估算消息列表的token数量"""
        total_tokens = 0
        
        for msg in messages:
            content = ""
            if hasattr(msg, "content"):
                content = msg.content
            elif isinstance(msg, dict) and "content" in msg:
                content = msg["content"]
            
            # 简单估算: 每个单词约1.3个token
            words = content.split()
            total_tokens += len(words) * 1.3
            
            # 每条消息的基本开销
            total_tokens += 4
        
        return int(total_tokens)
    
    @classmethod
    async def _generate_conversation_summary(cls, messages: List[Any]) -> str:
        """生成对话摘要"""
        # 提取消息内容
        conversation_text = ""
        for msg in messages:
            role = ""
            content = ""
            
            if hasattr(msg, "role") and hasattr(msg, "content"):
                role = msg.role
                content = msg.content
            elif isinstance(msg, dict) and "role" in msg and "content" in msg:
                role = msg["role"]
                content = msg["content"]
            
            if role and content:
                conversation_text += f"{role.capitalize()}: {content}\n\n"
        
        # 如果没有有效的对话内容，返回空摘要
        if not conversation_text:
            return "没有之前的对话记录"
        
        # 构建提示词
        prompt = f"""
        请为以下对话生成一个简洁的摘要，捕捉关键信息和讨论要点。

        对话内容:
        {conversation_text}

        摘要:
        """
        
        try:
            # 调用LLM生成摘要
            response = await llm_service.aget_chat_response(
                messages=[
                    {"role": "system", "content": "你是一个对话摘要助手，负责为对话生成简洁的摘要。"},
                    {"role": "user", "content": prompt}
                ],
                model=MODELS["conversation"],
                temperature=0.3
            )
            
            return response.strip()
        except Exception as e:
            logger.error(f"生成对话摘要失败: {str(e)}")
            return "之前的对话包含了用户的健身需求和相关信息"
    
    @classmethod
    def update_active_flow(cls, state: Dict[str, Any], flow_name: str = None) -> Dict[str, Any]:
        """
        更新活跃流程
        
        Args:
            state: 会话状态
            flow_name: 流程名称，如果为None则清除活跃流程
            
        Returns:
            更新后的会话状态
        """
        # 确保meta_info存在
        if "meta_info" not in state:
            state["meta_info"] = {}
        
        # 更新活跃流程
        if flow_name:
            state["meta_info"]["active_flow"] = flow_name
        else:
            state["meta_info"].pop("active_flow", None)
        
        return state
    
    @classmethod
    def update_related_plan_id(cls, state: Dict[str, Any], plan_id: str = None) -> Dict[str, Any]:
        """
        更新关联计划ID
        
        Args:
            state: 会话状态
            plan_id: 计划ID，如果为None则清除关联计划ID
            
        Returns:
            更新后的会话状态
        """
        # 确保meta_info存在
        if "meta_info" not in state:
            state["meta_info"] = {}
        
        # 更新关联计划ID
        if plan_id:
            state["meta_info"]["related_plan_id"] = plan_id
        else:
            state["meta_info"].pop("related_plan_id", None)
        
        return state
    
    @classmethod
    async def check_context_relevance(cls, state: Dict[str, Any], message: str) -> bool:
        """
        检查消息与当前上下文的相关性
        
        Args:
            state: 会话状态
            message: 用户消息
            
        Returns:
            是否相关
        """
        # 获取上下文信息
        active_flow = state.get("meta_info", {}).get("active_flow")
        related_plan_id = state.get("meta_info", {}).get("related_plan_id")
        
        # 如果没有上下文，默认相关
        if not active_flow and not related_plan_id:
            return True
        
        # 构建上下文描述
        context = ""
        if active_flow:
            context += f"当前对话流程: {active_flow}. "
        if related_plan_id:
            context += f"正在讨论的训练计划ID: {related_plan_id}. "
        
        # 构建提示词
        prompt = f"""
        请分析用户的消息是否与当前对话上下文相关。

        当前对话上下文:
        {context}

        用户消息:
        "{message}"

        这条消息是否与当前对话上下文相关？请只回答"相关"或"不相关"。
        """
        
        try:
            # 调用LLM判断相关性
            response = await llm_service.aget_chat_response(
                messages=[
                    {"role": "system", "content": "你是一个对话分析助手，负责判断用户消息是否与当前对话上下文相关。"},
                    {"role": "user", "content": prompt}
                ],
                model=MODELS["intent_recognition"],
                temperature=0.1
            )
            
            # 解析响应
            is_relevant = "相关" in response.lower()
            logger.info(f"上下文相关性检查结果: {'相关' if is_relevant else '不相关'}, 原始响应: {response}")
            
            return is_relevant
        except Exception as e:
            logger.error(f"检查上下文相关性失败: {str(e)}")
            # 出错时默认相关
            return True
