"""
增强参数提取器 - 提供更智能的参数提取功能
"""
from typing import Dict, Any, List, Optional, Tuple
import logging
import re
import json
from app.services.llm_proxy_service import LLMProxyService
from app.core.chat_config import MODELS
from app.services.parameter_extractor import ParameterExtractor

# 配置日志
logger = logging.getLogger(__name__)

# 全局LLM实例
llm_service = LLMProxyService()

class EnhancedParameterExtractor(ParameterExtractor):
    """增强参数提取器类，提供更智能的参数提取功能"""
    
    # 训练器材关键词映射
    EQUIPMENT_KEYWORDS = {
        "哑铃": ["哑铃", "dumbbell"],
        "杠铃": ["杠铃", "barbell"],
        "壶铃": ["壶铃", "kettlebell"],
        "健身球": ["健身球", "瑜伽球", "stability ball", "exercise ball"],
        "弹力带": ["弹力带", "resistance band"],
        "引体向上器": ["引体向上", "单杠", "pull-up bar"],
        "卧推架": ["卧推架", "bench press"],
        "深蹲架": ["深蹲架", "squat rack"],
        "跑步机": ["跑步机", "treadmill"],
        "椭圆机": ["椭圆机", "elliptical"],
        "划船机": ["划船机", "rowing machine"],
        "自重": ["自重", "bodyweight", "徒手"]
    }
    
    # 训练强度关键词映射
    INTENSITY_KEYWORDS = {
        "低强度": ["低强度", "轻松", "恢复", "low intensity", "light"],
        "中强度": ["中强度", "适中", "moderate intensity", "medium"],
        "高强度": ["高强度", "剧烈", "high intensity", "intense", "HIIT"]
    }
    
    # 训练风格关键词映射
    TRAINING_STYLE_KEYWORDS = {
        "力量训练": ["力量", "strength", "重量", "举重"],
        "有氧训练": ["有氧", "cardio", "心肺", "耐力"],
        "HIIT": ["HIIT", "高强度间歇", "间歇训练"],
        "超级组": ["超级组", "superset"],
        "循环训练": ["循环", "circuit"],
        "分化训练": ["分化", "split"],
        "全身训练": ["全身", "full body"]
    }
    
    @classmethod
    async def extract_parameters(cls, message: str, context: Dict[str, Any] = None, message_history: List[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        从用户消息中提取训练相关参数，增强版
        
        Args:
            message: 用户消息
            context: 上下文信息，包含已知参数等
            message_history: 消息历史
            
        Returns:
            提取的参数字典
        """
        # 调用基类方法提取基本参数
        parameters = await super().extract_parameters(message, context)
        
        # 提取增强参数
        enhanced_params = await cls._extract_enhanced_parameters(message)
        
        # 合并增强参数，但不覆盖已有参数
        for key, value in enhanced_params.items():
            if key not in parameters or not parameters[key]:
                parameters[key] = value
        
        # 如果提供了消息历史，尝试从上下文中提取隐式参数
        if message_history:
            implicit_params = await cls._extract_implicit_parameters(message, message_history, parameters)
            # 合并隐式参数，但不覆盖已有参数
            for key, value in implicit_params.items():
                if key not in parameters or not parameters[key]:
                    parameters[key] = value
        
        # 参数冲突解决
        if context and "training_params" in context:
            parameters = cls._resolve_parameter_conflicts(parameters, context["training_params"])
        
        return parameters
    
    @classmethod
    async def _extract_enhanced_parameters(cls, message: str) -> Dict[str, Any]:
        """提取增强参数"""
        parameters = {}
        
        # 提取训练器材
        equipment = cls._extract_keyword_match(message, cls.EQUIPMENT_KEYWORDS)
        if equipment:
            parameters["equipment"] = equipment
        
        # 提取训练强度
        intensity = cls._extract_keyword_match(message, cls.INTENSITY_KEYWORDS)
        if intensity:
            parameters["intensity"] = intensity
        
        # 提取训练风格
        training_style = cls._extract_keyword_match(message, cls.TRAINING_STYLE_KEYWORDS)
        if training_style:
            parameters["training_style"] = training_style
        
        # 提取训练时间（小时）
        hours_match = re.search(r'(\d+)\s*(小时|hour)', message)
        if hours_match:
            hours = int(hours_match.group(1))
            # 转换为分钟
            parameters["duration_minutes"] = hours * 60
        
        # 提取组数和次数
        sets_match = re.search(r'(\d+)\s*组', message)
        if sets_match:
            parameters["sets"] = int(sets_match.group(1))
        
        reps_match = re.search(r'(\d+)\s*次', message)
        if reps_match:
            parameters["reps"] = int(reps_match.group(1))
        
        # 使用实体识别提取更多参数
        try:
            entity_params = await cls._extract_entities(message)
            # 合并实体参数，但不覆盖已有参数
            for key, value in entity_params.items():
                if key not in parameters or not parameters[key]:
                    parameters[key] = value
        except Exception as e:
            logger.error(f"提取实体失败: {str(e)}")
        
        return parameters
    
    @classmethod
    async def _extract_entities(cls, message: str) -> Dict[str, Any]:
        """使用实体识别提取更多参数"""
        prompt = f"""
        请从用户消息中提取以下训练相关实体。如果无法确定某个实体，请返回null。

        用户消息: "{message}"

        请提取以下实体（JSON格式）:
        - exercise_names: 训练动作名称列表
        - muscle_groups: 肌肉群列表
        - equipment: 训练器材列表
        - training_style: 训练风格
        - rest_time: 休息时间（秒）
        - special_techniques: 特殊训练技巧（如超级组、递减组等）

        只返回JSON格式，不要有任何其他文字。
        """
        
        try:
            response = await llm_service.aget_chat_response(
                messages=[
                    {"role": "system", "content": "你是一个实体识别助手，负责从用户消息中提取训练相关实体。"},
                    {"role": "user", "content": prompt}
                ],
                model="agent-app",
                temperature=0.1
            )
            
            # 解析JSON响应
            try:
                entities = json.loads(response)
                # 过滤掉None值和空列表
                return {k: v for k, v in entities.items() if v is not None and (not isinstance(v, list) or len(v) > 0)}
            except json.JSONDecodeError:
                # 尝试从文本中提取JSON
                try:
                    start_idx = response.find('{')
                    end_idx = response.rfind('}') + 1
                    if start_idx >= 0 and end_idx > start_idx:
                        json_str = response[start_idx:end_idx]
                        entities = json.loads(json_str)
                        # 过滤掉None值和空列表
                        return {k: v for k, v in entities.items() if v is not None and (not isinstance(v, list) or len(v) > 0)}
                    else:
                        logger.warning(f"无法从响应中提取JSON: {response}")
                        return {}
                except Exception as e:
                    logger.error(f"解析实体JSON失败: {str(e)}")
                    return {}
        except Exception as e:
            logger.error(f"提取实体失败: {str(e)}")
            return {}
    
    @classmethod
    async def _extract_implicit_parameters(cls, message: str, message_history: List[Dict[str, str]], current_params: Dict[str, Any]) -> Dict[str, Any]:
        """从对话上下文中提取隐式参数"""
        # 构建对话历史文本
        history_text = ""
        for msg in message_history[-5:]:  # 只使用最近的5条消息
            if "role" in msg and "content" in msg:
                history_text += f"{msg['role'].capitalize()}: {msg['content']}\n\n"
        
        # 构建当前参数文本
        current_params_text = ", ".join([f"{k}: {v}" for k, v in current_params.items()])
        
        prompt = f"""
        请从对话历史和当前消息中提取可能的隐式训练参数。这些是用户没有明确提到但可能从上下文推断的参数。

        对话历史:
        {history_text}

        当前消息:
        "{message}"

        当前已知参数:
        {current_params_text}

        请提取以下可能的隐式参数（JSON格式）:
        - body_part: 训练部位
        - training_scene: 训练场景
        - plan_type: 计划类型
        - training_goal: 训练目标
        - fitness_level: 健身水平
        - equipment: 训练器材

        只返回JSON格式，不要有任何其他文字。如果无法确定某个参数，请返回null。
        """
        
        try:
            response = await llm_service.aget_chat_response(
                messages=[
                    {"role": "system", "content": "你是一个上下文分析助手，负责从对话上下文中提取隐式参数。"},
                    {"role": "user", "content": prompt}
                ],
                model="agent-app",
                temperature=0.2
            )
            
            # 解析JSON响应
            try:
                implicit_params = json.loads(response)
                # 过滤掉None值
                return {k: v for k, v in implicit_params.items() if v is not None}
            except json.JSONDecodeError:
                # 尝试从文本中提取JSON
                try:
                    start_idx = response.find('{')
                    end_idx = response.rfind('}') + 1
                    if start_idx >= 0 and end_idx > start_idx:
                        json_str = response[start_idx:end_idx]
                        implicit_params = json.loads(json_str)
                        # 过滤掉None值
                        return {k: v for k, v in implicit_params.items() if v is not None}
                    else:
                        logger.warning(f"无法从响应中提取JSON: {response}")
                        return {}
                except Exception as e:
                    logger.error(f"解析隐式参数JSON失败: {str(e)}")
                    return {}
        except Exception as e:
            logger.error(f"提取隐式参数失败: {str(e)}")
            return {}
    
    @classmethod
    def _resolve_parameter_conflicts(cls, new_params: Dict[str, Any], old_params: Dict[str, Any]) -> Dict[str, Any]:
        """解决参数冲突"""
        resolved_params = {}
        
        # 合并所有参数键
        all_keys = set(new_params.keys()) | set(old_params.keys())
        
        for key in all_keys:
            # 如果新参数中没有该键，使用旧参数
            if key not in new_params:
                resolved_params[key] = old_params[key]
                continue
            
            # 如果旧参数中没有该键，使用新参数
            if key not in old_params:
                resolved_params[key] = new_params[key]
                continue
            
            # 如果都有该键，需要解决冲突
            new_value = new_params[key]
            old_value = old_params[key]
            
            # 如果值相同，直接使用
            if new_value == old_value:
                resolved_params[key] = new_value
                continue
            
            # 特殊处理某些参数
            if key == "plan_type":
                # 计划类型冲突，优先使用新值
                resolved_params[key] = new_value
            elif key == "body_part":
                # 身体部位冲突，如果新值是"全身"，使用新值，否则保留旧值
                if new_value == "全身":
                    resolved_params[key] = new_value
                else:
                    resolved_params[key] = old_value
            elif key == "training_scene":
                # 训练场景冲突，优先使用新值
                resolved_params[key] = new_value
            elif key == "duration_minutes":
                # 训练时长冲突，使用新值
                resolved_params[key] = new_value
            elif key == "equipment":
                # 训练器材冲突，如果是列表则合并，否则使用新值
                if isinstance(new_value, list) and isinstance(old_value, list):
                    resolved_params[key] = list(set(new_value + old_value))
                else:
                    resolved_params[key] = new_value
            else:
                # 其他参数冲突，默认使用新值
                resolved_params[key] = new_value
        
        return resolved_params
    
    @classmethod
    async def extract_exercise_preferences(cls, message: str, user_info: Dict[str, Any]) -> Dict[str, Any]:
        """提取训练偏好"""
        # 构建用户信息文本
        user_info_text = ", ".join([f"{k}: {v}" for k, v in user_info.items()])
        
        prompt = f"""
        请从用户消息和用户信息中提取训练偏好。

        用户信息:
        {user_info_text}

        用户消息:
        "{message}"

        请提取以下训练偏好（JSON格式）:
        - preferred_exercises: 偏好的训练动作列表
        - avoided_exercises: 避免的训练动作列表
        - preferred_equipment: 偏好的训练器材列表
        - avoided_equipment: 避免的训练器材列表
        - preferred_training_style: 偏好的训练风格
        - preferred_intensity: 偏好的训练强度
        - special_requirements: 特殊要求或限制

        只返回JSON格式，不要有任何其他文字。如果无法确定某个偏好，请返回null。
        """
        
        try:
            response = await llm_service.aget_chat_response(
                messages=[
                    {"role": "system", "content": "你是一个训练偏好分析助手，负责从用户消息中提取训练偏好。"},
                    {"role": "user", "content": prompt}
                ],
                model="agent-app",
                temperature=0.2
            )
            
            # 解析JSON响应
            try:
                preferences = json.loads(response)
                # 过滤掉None值和空列表
                return {k: v for k, v in preferences.items() if v is not None and (not isinstance(v, list) or len(v) > 0)}
            except json.JSONDecodeError:
                # 尝试从文本中提取JSON
                try:
                    start_idx = response.find('{')
                    end_idx = response.rfind('}') + 1
                    if start_idx >= 0 and end_idx > start_idx:
                        json_str = response[start_idx:end_idx]
                        preferences = json.loads(json_str)
                        # 过滤掉None值和空列表
                        return {k: v for k, v in preferences.items() if v is not None and (not isinstance(v, list) or len(v) > 0)}
                    else:
                        logger.warning(f"无法从响应中提取JSON: {response}")
                        return {}
                except Exception as e:
                    logger.error(f"解析训练偏好JSON失败: {str(e)}")
                    return {}
        except Exception as e:
            logger.error(f"提取训练偏好失败: {str(e)}")
            return {}
