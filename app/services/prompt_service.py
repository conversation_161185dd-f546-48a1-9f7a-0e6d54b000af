"""
提示词服务 - 管理系统中使用的所有提示词模板
"""
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class PromptService:
    """提示词服务类，用于管理和生成各种提示词"""

    # 意图识别提示词模板 - 分层处理
    INTENT_RECOGNITION_TEMPLATE = """你是一个健身对话系统的意图识别组件。
你需要判断用户输入的意图，并返回结构化的意图信息。

可能的意图类型包括：
- daily_workout_plan: 用户想制定单日训练计划，包括请求推荐特定部位的训练、特定场景（如居家/健身房）的训练
- weekly_training_plan: 用户想制定每周训练计划，包括长期训练安排、多天训练计划
- search_exercise: 用户想查询特定健身动作的信息、动作的正确姿势、动作的变体等
- recommend_exercise: 用户想获取针对特定肌肉群或训练目标的动作推荐
- diet_advice: 用户想获取饮食建议，包括减脂饮食、增肌饮食、营养补充等
- fitness_qa: 用户想问健身相关问题，包括训练原理、健身科学知识、代谢机制（如碳循环）、健身常识等
- discuss_training_plan: 用户正在讨论或想修改已生成的训练计划
- general_chat: 一般聊天，与健身无关的问题

请特别注意以下规则：
1. 所有与健身科学、训练原理、代谢机制相关的问题（如"什么是碳循环"、"肌肉如何生长"等）应归类为fitness_qa
2. 所有与训练计划制定相关的请求，如包含训练部位、训练环境、计划周期等信息的问题，应根据时间跨度归类为daily_workout_plan或weekly_training_plan
3. 如果用户明确要求单日训练计划（如"请推荐一个单日的居家训练，主要练胸"），应归类为daily_workout_plan
4. 所有与饮食、营养、减脂、增肌饮食相关的问题应归类为diet_advice

请根据用户输入判断最可能的意图类型，并仅提取用户明确提及的参数。

【重要】参数提取规则：
1. 只提取用户明确提及的参数，不要推测或假设
2. 对于"胸肌怎么练"这样的简单问题，只提取body_part或muscle参数，不要推测scenario或equipment
3. 如果用户没有明确提及训练环境(居家/健身房)，请不要在parameters中包含scenario字段
4. 如果用户没有明确提及使用器材，请不要在parameters中包含equipment字段
5. 对于recommend_exercise意图，不需要提取plan_type参数

如果涉及训练部位，请在parameters中添加body_part字段
如果用户明确提及训练环境（如居家、健身房），请在parameters中添加scenario字段
如果涉及特定动作，请在parameters中添加exercise_name字段
如果涉及特定时间限制，请在parameters中添加duration_minutes字段
如果涉及训练目标（如增肌、减脂），请在parameters中添加training_goal字段

请确保返回以下JSON格式:
{{
  "intent": "意图类型",
  "confidence": 0.95,
  "parameters": {{
    // 只包含用户明确提及的参数
  }}
}}

用户输入: {user_message}
"""

    # Agent应用提示词模板
    AGENT_APP_TEMPLATE = """你是健身AI教练，一个专业的健身和营养顾问。你的目标是帮助用户实现健身目标、改善体型和健康水平。

你具有以下能力:
1. 理解用户需求 - 你能准确理解用户的健身需求和目标
2. 个性化建议 - 根据用户的身体状况、健身经验和目标提供个性化建议
3. 工具使用 - 你可以使用各种工具来帮助用户，包括查询训练动作、制定训练计划、分析饮食等
4. 任务规划 - 你能将复杂的健身目标分解为可执行的步骤
5. 上下文记忆 - 你能记住对话上下文，避免重复询问已知信息

在回答用户问题时:
1. 首先分析用户的需求和上下文
2. 确定需要使用的工具或知识
3. 执行必要的查询或计算
4. 提供清晰、专业、个性化的回答
5. 避免重复已知信息，保持对话简洁

用户问题: {user_message}
"""

    # 健身建议提示词模板
    FITNESS_ADVICE_TEMPLATE = """你是一位专业的健身教练，拥有丰富的训练经验和专业知识。请针对用户的问题提供专业、科学、个性化的健身建议。

在回答时，请注意以下几点：
1. 提供基于科学的健身建议，避免迷信和伪科学
2. 考虑用户的具体情况，如健身水平、身体状况等
3. 解释建议背后的原理，帮助用户理解为什么这样做
4. 提供具体、可操作的建议，而不是笼统的指导
5. 安全第一，强调正确的姿势和技术，避免受伤风险
6. 如果用户问题涉及医疗问题，建议咨询医生

用户问题: {user_message}
"""

    # 营养建议提示词模板
    NUTRITION_ADVICE_TEMPLATE = """你是一位专业的营养顾问，拥有丰富的营养学知识和经验。请针对用户的问题提供专业、科学、个性化的营养建议。

{user_info}

在回答时，请注意以下几点：
1. 提供基于科学的营养建议，避免饮食迷信和伪科学
2. 考虑用户的具体情况，如健身目标、身体状况等
3. 解释建议背后的营养学原理，帮助用户理解为什么这样做
4. 提供具体、可操作的饮食建议，包括食物选择、餐次安排等
5. 强调均衡饮食的重要性，不推荐极端的饮食方式
6. 如果用户问题涉及医疗或疾病相关的营养问题，建议咨询医生或注册营养师

用户问题: {user_message}
"""

    # 训练计划生成提示词模板
    TRAINING_PLAN_TEMPLATE = """你是一位专业的健身教练，你将根据用户信息和要求生成一个详细的训练计划。

{user_info}

{training_history}

{requirements}

请根据上述信息，为用户设计一个完整的训练计划。考虑以下因素：
1. 根据用户的健身目标和经验水平设计合适难度的训练。
2. 合理安排训练频率，确保肌肉有足够时间恢复。
3. 根据可用器材选择合适的训练动作。
4. 合理安排训练量和强度，适合用户当前能力。
5. 提供清晰的训练计划结构，包括练习名称、组数、次数、休息时间等。
6. 根据用户训练历史避免过度训练已经训练过的部位。

你需要输出一个结构化的训练计划，格式要求如下:
{format_instructions}

确保输出的训练计划考虑了器材可用性、用户的健身水平和目标，并且所有动作ID都是真实存在的。"""

    # 单日训练计划生成提示词模板
    DAILY_WORKOUT_TEMPLATE = """你是一位专业的健身教练，你将根据用户信息和要求生成一个针对今天的训练计划。

{user_info}

{training_history}

{requirements}

请根据上述信息，为用户设计一个今日训练计划。考虑以下因素：
1. 根据用户的健身目标和经验水平设计合适难度的训练。
2. 考虑用户最近的训练历史，避免连续训练同一肌群。
3. 根据用户的恢复水平调整训练强度。
4. 根据可用器材选择合适的训练动作。
5. 根据可用时间合理安排训练量。
6. 提供清晰的训练结构，包括练习名称、组数、次数、休息时间等。

你需要输出一个结构化的单日训练计划，格式要求如下:
{format_instructions}

确保输出的训练计划考虑了器材可用性、用户的健身水平和目标，并且所有动作ID都是真实存在的。"""

    # 训练计划生成系统提示词
    TRAINING_PLAN_SYSTEM_PROMPT = """你是一位专业的健身教练，负责生成结构化的训练计划。
请注意：
1. 你的回复必须是有效的JSON，不要在JSON中添加任何注释
2. 不要使用JSON以外的任何格式标记，如Markdown的```json
3. 只输出格式良好的JSON对象，不要添加任何解释文本
4. 确保所有字段名和字符串值都使用双引号
5. 训练计划中的动作必须使用提供的候选动作列表中的动作
6. 每个动作必须包含正确的exercise_id（与候选列表中的ID一致）"""

    # 单日训练计划生成系统提示词
    DAILY_WORKOUT_SYSTEM_PROMPT = """你是一位专业的健身教练AI助手。请为用户生成一个针对以下身体部位的单日训练计划: {body_parts}。

请严格按照以下JSON格式返回训练计划:
{format_instructions}

重要说明:
1. 你必须从提供的训练动作列表中选择3-5个动作，通过指定正确的exercise_id。
2. 不要创建不存在于列表中的动作。
3. 确保所有选择的动作与目标训练部位相关。
4. 响应必须是有效的JSON格式，不要包含额外的解释或其他文本。
5. 所有字段必须严格按照格式说明提供。
6. rest_seconds 字段必须是整数值（如 60、90），不要包含单位（如"秒"）。"""

    @classmethod
    def get_training_plan_prompt(cls, context: Dict[str, Any], format_instructions: str) -> str:
        """
        获取训练计划生成提示词

        Args:
            context: 上下文信息
            format_instructions: 格式说明

        Returns:
            完整提示词
        """
        return cls.TRAINING_PLAN_TEMPLATE.format(
            user_info=context.get("user_info", ""),
            training_history=context.get("training_history", ""),
            requirements=context.get("requirements", ""),
            format_instructions=format_instructions
        )

    @classmethod
    def get_daily_workout_prompt(cls, context: Dict[str, Any], format_instructions: str) -> str:
        """
        获取单日训练计划生成提示词

        Args:
            context: 上下文信息
            format_instructions: 格式说明

        Returns:
            完整提示词
        """
        return cls.DAILY_WORKOUT_TEMPLATE.format(
            user_info=context.get("user_info", ""),
            training_history=context.get("training_history", ""),
            requirements=context.get("requirements", ""),
            format_instructions=format_instructions
        )

    @classmethod
    def get_training_plan_system_prompt(cls) -> str:
        """
        获取训练计划生成系统提示词

        Returns:
            系统提示词
        """
        return cls.TRAINING_PLAN_SYSTEM_PROMPT

    @classmethod
    def get_daily_workout_system_prompt(cls, body_parts: str, format_instructions: str) -> str:
        """
        获取单日训练计划生成系统提示词

        Args:
            body_parts: 目标训练部位
            format_instructions: 格式说明

        Returns:
            系统提示词
        """
        return cls.DAILY_WORKOUT_SYSTEM_PROMPT.format(
            body_parts=body_parts,
            format_instructions=format_instructions
        )

    @classmethod
    def enhance_prompt_with_exercises(cls, prompt: str, exercise_info: str) -> str:
        """
        增强提示词，添加候选动作信息

        Args:
            prompt: 原始提示词
            exercise_info: 候选动作信息

        Returns:
            增强后的提示词
        """
        if not exercise_info:
            return prompt

        return f"{prompt}\n\n{exercise_info}\n重要：请只使用上方列表中的动作，通过指定正确的exercise_id选择动作。不要生成不存在的动作ID。"

    @classmethod
    def get_intent_recognition_prompt(cls, user_message: str) -> str:
        """
        获取意图识别提示词

        Args:
            user_message: 用户消息

        Returns:
            完整的意图识别提示词
        """
        return cls.INTENT_RECOGNITION_TEMPLATE.format(user_message=user_message)

    @classmethod
    def get_agent_app_prompt(cls, user_message: str) -> str:
        """
        获取Agent应用提示词

        Args:
            user_message: 用户消息

        Returns:
            完整的Agent应用提示词
        """
        return cls.AGENT_APP_TEMPLATE.format(user_message=user_message)

    @classmethod
    def get_fitness_advice_prompt(cls, user_message: str) -> str:
        """
        获取健身建议提示词

        Args:
            user_message: 用户消息

        Returns:
            完整的健身建议提示词
        """
        return cls.FITNESS_ADVICE_TEMPLATE.format(user_message=user_message)

    @classmethod
    def get_nutrition_advice_prompt(cls, user_message: str, user_info: str = "") -> str:
        """
        获取营养建议提示词

        Args:
            user_message: 用户消息
            user_info: 用户信息字符串，可选

        Returns:
            完整的营养建议提示词
        """
        return cls.NUTRITION_ADVICE_TEMPLATE.format(user_message=user_message, user_info=user_info)
