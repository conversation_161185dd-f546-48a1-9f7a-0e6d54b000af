"""
服务工厂模块

这个模块提供了一个服务工厂类，用于管理和提供对所有服务的访问。
它确保了服务的单例模式，并处理服务间的依赖关系。
"""
import logging
from typing import Dict, Type, Any, Optional

logger = logging.getLogger(__name__)

class ServiceFactory:
    """
    服务工厂类，用于管理和提供对所有服务的访问
    
    该工厂确保了服务的单例模式，并处理服务间的依赖关系。
    它提供了一个统一的入口点来获取系统中的任何服务。
    """
    
    _instances: Dict[str, Any] = {}
    _service_map: Dict[str, Type[Any]] = {}
    
    @classmethod
    def register(cls, name: str, service_class: Type[Any]) -> None:
        """
        注册服务
        
        Args:
            name: 服务名称
            service_class: 服务类
        """
        cls._service_map[name] = service_class
        logger.info(f"Registered service: {name}")
    
    @classmethod
    def get(cls, name: str) -> Any:
        """
        获取服务实例
        
        Args:
            name: 服务名称
            
        Returns:
            服务实例
            
        Raises:
            ValueError: 如果服务不存在
        """
        if name not in cls._instances:
            if name not in cls._service_map:
                raise ValueError(f"Service '{name}' not registered")
            
            try:
                cls._instances[name] = cls._service_map[name]()
                logger.debug(f"Created service instance: {name}")
            except Exception as e:
                logger.error(f"Failed to create service '{name}': {str(e)}")
                raise
        
        return cls._instances[name]
    
    @classmethod
    def register_instance(cls, name: str, instance: Any) -> None:
        """
        注册服务实例
        
        Args:
            name: 服务名称
            instance: 服务实例
        """
        cls._instances[name] = instance
        logger.info(f"Registered service instance: {name}")
    
    @classmethod
    def initialize_services(cls) -> None:
        """
        初始化所有服务
        
        该方法加载所有需要在应用启动时初始化的服务。
        """
        # 加载LLM服务
        try:
            from app.services.ai_assistant.llm.service import llm_service
            cls.register_instance("llm", llm_service)
        except ImportError:
            logger.warning("LLM service not available")
        
        # 加载用户服务
        try:
            from app.services.domain.user.service import user_service
            cls.register_instance("user", user_service)
        except ImportError:
            logger.warning("User service not available")
        
        # 加载训练计划服务
        try:
            from app.services.domain.training.service import training_plan_service
            cls.register_instance("training_plan", training_plan_service)
        except ImportError:
            logger.warning("Training plan service not available")
        
        # 加载会话服务
        try:
            from app.services.ai_assistant.conversation.service import conversation_service
            cls.register_instance("conversation", conversation_service)
        except ImportError:
            logger.warning("Conversation service not available")

# 导出工厂实例供其他模块使用
service_factory = ServiceFactory() 