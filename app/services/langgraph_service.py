from typing import Dict, List, Any, Optional, AsyncGenerator, <PERSON>ple
import json
import uuid
import time
import logging
from datetime import datetime

from fastapi import WebSocket, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import text
from langgraph.graph import StateGraph, END
from langgraph.graph.message import AnyMessage
from langgraph.checkpoint.memory import InMemorySaver

from app.core.config import settings
from app.db.session import SessionLocal
from app.api import deps
from app.crud import crud_conversation, crud_message, crud_user
from app.services.llm_proxy_service import LLMProxyService
from app.services.memory_cache_service import MemoryCacheService
from app.services.db_checkpointer import PostgreSQLCheckpointer
from app.services.parameter_extractor import ParameterExtractor
from app.services.enhanced_parameter_extractor import EnhancedParameterExtractor
from app.services.user_profile_manager import UserProfileManager
from app.services.conversation_context_manager import ConversationContextManager
from app.services.dialog_flow_optimizer import Dialog<PERSON>lowOptimizer
from app.services.graph_nodes import (
    router_node,
    param_collector_node,
    user_info_collector_node,
    training_plan_expert_node,
    fitness_qa_expert_node,
    general_chat_expert_node,
    interruption_handler_node,
    state_monitor_node
)
from app.services.graph_nodes.image_analysis_expert_node import image_analysis_expert_node
from app.services.graph_nodes.enhanced_exercise_recommendation_expert import exercise_recommendation_expert_node
from app.services.graph_nodes.training_progress_expert_node import training_progress_expert_node
from app.services.state_definitions import ConversationState

logger = logging.getLogger(__name__)

class LangGraphService:
    """LangGraph服务，负责管理健身AI助手的图状工作流"""

    def __init__(self, db: Session, llm_service: LLMProxyService = None):
        """初始化LangGraph服务"""
        self.db = db
        self.llm_service = llm_service or LLMProxyService()
        self.checkpoint_dir = settings.LANGGRAPH_CHECKPOINT_DIR
        self.cache_service = MemoryCacheService()

        # 初始化数据库检查点存储
        try:
            self.checkpointer = PostgreSQLCheckpointer(db)
            logger.info("使用PostgreSQL检查点存储")
        except Exception as e:
            logger.warning(f"PostgreSQL检查点存储初始化失败，使用内存存储: {str(e)}")
            self.checkpointer = InMemorySaver()

        # 构建图
        self._build_graph()

        # 启动定期清理任务
        self._schedule_cleanup()

    def _schedule_cleanup(self):
        """计划定期清理任务"""
        # 这里可以使用后台任务调度器，但为简单起见，我们只记录一条日志
        logger.info("已计划缓存清理任务")

    def _build_graph(self):
        """构建图状工作流"""
        # 创建图
        workflow = StateGraph(ConversationState)

        # 添加节点
        workflow.add_node("state_monitor", state_monitor_node)  # 添加状态监控节点
        workflow.add_node("interruption_handler", interruption_handler_node)
        workflow.add_node("router", router_node)
        workflow.add_node("param_collector", param_collector_node)
        workflow.add_node("user_info_collector", user_info_collector_node)
        workflow.add_node("training_plan_expert", training_plan_expert_node)
        workflow.add_node("fitness_qa_expert", fitness_qa_expert_node)
        workflow.add_node("general_chat_expert", general_chat_expert_node)

        # 添加新节点
        workflow.add_node("image_analysis_expert", image_analysis_expert_node)
        workflow.add_node("exercise_recommendation_expert", exercise_recommendation_expert_node)
        workflow.add_node("training_progress_expert", training_progress_expert_node)

        # 设置入口节点
        workflow.set_entry_point("state_monitor")  # 从状态监控节点开始

        # 配置状态监控节点的下一步去向
        workflow.add_conditional_edges(
            "state_monitor",
            self._state_monitor_next,
            {
                "interruption_handler": "interruption_handler",
                END: END
            }
        )

        # 配置中断处理节点的下一步去向
        workflow.add_conditional_edges(
            "interruption_handler",
            self._interruption_handler_next,
            {
                "router": "router",
                END: END
            }
        )

        # 配置路由规则
        workflow.add_conditional_edges(
            "router",
            self._route_message,
            {
                "param_collector": "param_collector",
                "user_info_collector": "user_info_collector",
                "training_plan_expert": "training_plan_expert",
                "fitness_qa_expert": "fitness_qa_expert",
                "general_chat_expert": "general_chat_expert",
                "image_analysis_expert": "image_analysis_expert",
                "exercise_recommendation_expert": "exercise_recommendation_expert",
                "training_progress_expert": "training_progress_expert",
                END: END
            }
        )

        # 配置参数收集器的下一步去向
        workflow.add_conditional_edges(
            "param_collector",
            self._param_collector_next,
            {
                "router": "router",
                "training_plan_expert": "training_plan_expert",
                END: END
            }
        )

        # 配置用户信息收集器的下一步去向
        workflow.add_conditional_edges(
            "user_info_collector",
            self._user_info_collector_next,
            {
                "router": "router",
                END: END
            }
        )

        # 配置专家节点的结束条件
        workflow.add_edge("training_plan_expert", END)
        workflow.add_edge("fitness_qa_expert", END)
        workflow.add_edge("general_chat_expert", END)
        workflow.add_edge("image_analysis_expert", END)
        workflow.add_edge("exercise_recommendation_expert", END)
        workflow.add_edge("training_progress_expert", END)

        # 编译图
        self.graph = workflow.compile()

        # 注意：在当前版本的LangGraph中，recursion_limit需要在运行时配置
        # 我们将在状态监控节点中实现循环检测和限制

        # 设置检查点
        try:
            self.graph.set_checkpointer(self.checkpointer)
            logger.info("图状工作流检查点设置成功")
        except Exception as e:
            logger.warning(f"设置检查点失败: {str(e)}")

    def _route_message(self, state: ConversationState) -> str:
        """根据状态确定下一个节点"""
        # 检查是否需要强制退出
        if state.flow_state.get("force_exit", False):
            return END

        # 检查是否有图像分析结果
        if state.meta_info.get("image_analysis_result"):
            # 如果图像分析结果是新的（在最近5分钟内添加）
            analysis_timestamp = state.meta_info.get("image_analysis_timestamp")
            if analysis_timestamp and (time.time() - analysis_timestamp < 300):
                # 记录当前节点
                state.flow_state["current_node"] = "image_analysis_expert"
                return "image_analysis_expert"

        # 如果需要收集参数
        if state.flow_state.get("needs_param_collection", False):
            # 记录当前节点
            state.flow_state["current_node"] = "param_collector"
            return "param_collector"

        # 如果需要收集用户信息
        if state.flow_state.get("needs_user_info", False):
            # 记录当前节点
            state.flow_state["current_node"] = "user_info_collector"
            return "user_info_collector"

        # 根据意图路由到相应专家
        intent = state.flow_state.get("intent", "")
        if intent == "training_plan":
            # 记录当前节点
            state.flow_state["current_node"] = "training_plan_expert"
            return "training_plan_expert"
        elif intent == "fitness_qa":
            # 记录当前节点
            state.flow_state["current_node"] = "fitness_qa_expert"
            return "fitness_qa_expert"
        elif intent == "exercise_recommendation":
            # 记录当前节点
            state.flow_state["current_node"] = "exercise_recommendation_expert"
            return "exercise_recommendation_expert"
        elif intent == "training_progress":
            # 记录当前节点
            state.flow_state["current_node"] = "training_progress_expert"
            return "training_progress_expert"
        else:
            # 记录当前节点
            state.flow_state["current_node"] = "general_chat_expert"
            return "general_chat_expert"

    def _param_collector_next(self, state: ConversationState) -> str:
        """参数收集器完成后的下一步"""
        # 检查是否需要强制退出
        if state.flow_state.get("force_exit", False):
            return END

        if state.flow_state.get("params_complete", False):
            intent = state.flow_state.get("intent", "")
            if intent == "training_plan":
                # 记录当前节点
                state.flow_state["current_node"] = "training_plan_expert"
                return "training_plan_expert"
            # 记录当前节点
            state.flow_state["current_node"] = "router"
            return "router"
        # 记录当前节点
        state.flow_state["current_node"] = "router"
        return "router"

    def _user_info_collector_next(self, state: ConversationState) -> str:
        """用户信息收集器完成后的下一步"""
        # 检查是否需要强制退出
        if state.flow_state.get("force_exit", False):
            return END

        if state.flow_state.get("user_info_complete", False):
            # 记录当前节点
            state.flow_state["current_node"] = "router"
            return "router"
        # 记录当前节点
        state.flow_state["current_node"] = "router"
        return "router"

    def _state_monitor_next(self, state: ConversationState) -> str:
        """状态监控节点完成后的下一步"""
        # 检查是否需要强制退出
        if state.flow_state.get("force_exit", False):
            return END

        # 记录当前节点
        state.flow_state["current_node"] = "interruption_handler"

        # 继续到中断处理节点
        return "interruption_handler"

    def _interruption_handler_next(self, state: ConversationState) -> str:
        """中断处理节点完成后的下一步"""
        # 检查是否需要强制退出
        if state.flow_state.get("force_exit", False):
            return END

        # 如果有确认中断状态，不进入路由节点
        if state.meta_info.get("confirming_interruption", False):
            return END

        # 如果有消息回复，不进入路由节点
        if state.messages and state.messages[-1].role == "assistant":
            return END

        # 记录当前节点
        state.flow_state["current_node"] = "router"

        # 默认进入路由节点
        return "router"

    async def process_message(self,
                             message: str,
                             session_id: Optional[str] = None,
                             user_id: Optional[int] = None,
                             meta_info: Optional[Dict] = None) -> Dict[str, Any]:
        """处理用户消息并返回响应"""
        start_time = time.time()

        # 创建或获取会话ID
        session_id = session_id or str(uuid.uuid4())
        logger.debug(f"处理会话 {session_id} 的消息")

        # 获取用户信息（使用缓存）
        user = None
        if user_id:
            # 尝试从缓存获取用户信息
            cached_user_info = self.cache_service.get_user_info(user_id)
            if cached_user_info:
                user = type('User', (), cached_user_info)  # 创建一个类似用户对象的结构
                logger.debug(f"从缓存获取用户信息: {user_id}")
            else:
                # 从数据库获取并缓存
                user = crud_user.get(self.db, id=user_id)
                if not user:
                    raise HTTPException(status_code=404, detail="User not found")

                # 缓存用户信息
                user_dict = {
                    "id": user.id,
                    "name": user.nickname,
                    "gender": user.gender,
                    "age": user.age,
                    "height": user.height,
                    "weight": user.weight,
                    "fitness_goal": user.fitness_goal,
                    "fitness_level": user.experience_level
                }
                self.cache_service.set_user_info(user_id, user_dict)

        # 使用事务处理数据库操作
        try:
            # 获取或创建会话
            conversation = self._get_or_create_conversation(session_id, user_id)

            # 保存用户消息
            user_message_id = self._save_message(conversation.id, message, "user", meta_info)

            # 准备初始状态
            initial_state = await self._prepare_initial_state(message, user, meta_info)

            # 设置会话ID
            initial_state.session_id = session_id

            # 检查是否有缓存的会话状态
            cached_state = self.cache_service.get_conversation_state(session_id)
            if cached_state and hasattr(cached_state, 'messages'):
                # 合并新消息到缓存状态
                cached_state.messages.append(AnyMessage(role="user", content=message))
                # 更新元信息
                if meta_info:
                    cached_state.meta_info.update(meta_info)
                # 使用缓存状态
                initial_state = cached_state
                logger.debug(f"使用缓存的会话状态: {session_id}")
            elif cached_state and isinstance(cached_state, dict) and 'messages' in cached_state:
                # 如果缓存状态是字典格式，转换为ConversationState对象
                from app.services.state_definitions import AnyMessage as StateAnyMessage

                # 提取消息
                messages = cached_state.get('messages', [])
                if isinstance(messages, list):
                    # 添加新消息
                    messages.append({"role": "user", "content": message})

                    # 创建新的ConversationState
                    initial_state = ConversationState(
                        messages=[StateAnyMessage(role=m["role"], content=m["content"])
                                 if isinstance(m, dict) else m for m in messages],
                        user_info=cached_state.get('user_info', {}),
                        training_params=cached_state.get('training_params', {}),
                        meta_info=meta_info or cached_state.get('meta_info', {}),
                        flow_state=cached_state.get('flow_state', {}),
                        session_id=session_id
                    )
                    logger.debug(f"从字典格式转换缓存状态: {session_id}")

            # 压缩消息历史，减少token使用
            if len(initial_state.messages) > 15:  # 如果消息超过15条
                initial_state.messages = await ConversationContextManager.compress_message_history(initial_state.messages)
                logger.debug(f"压缩消息历史: {len(initial_state.messages)}条消息")

            # 执行图状工作流
            config = {
                "configurable": {"session_id": session_id},
                "recursion_limit": 50  # 设置递归限制
            }
            result = await self.graph.ainvoke(initial_state, config=config)

            # 提取响应
            # 检查result的类型和结构
            if hasattr(result, 'messages') and result.messages:
                if hasattr(result.messages[-1], 'content'):
                    response = result.messages[-1].content
                elif isinstance(result.messages[-1], dict) and 'content' in result.messages[-1]:
                    response = result.messages[-1]['content']
                else:
                    logger.warning(f"无法从消息中提取内容: {type(result.messages[-1])}")
                    response = "抱歉，我无法处理您的请求。"
            elif isinstance(result, dict) and 'messages' in result and result['messages']:
                if isinstance(result['messages'][-1], dict) and 'content' in result['messages'][-1]:
                    response = result['messages'][-1]['content']
                elif hasattr(result['messages'][-1], 'content'):
                    response = result['messages'][-1].content
                else:
                    logger.warning(f"无法从消息中提取内容: {type(result['messages'][-1])}")
                    response = "抱歉，我无法处理您的请求。"
            else:
                logger.warning(f"无法从结果中提取消息: {type(result)}")
                response = "抱歉，我无法处理您的请求。"

            # 获取元信息
            meta_info_to_save = result.meta_info if hasattr(result, 'meta_info') else (result.get('meta_info', {}) if isinstance(result, dict) else {})

            # 保存AI响应
            ai_message_id = self._save_message(conversation.id, response, "assistant", meta_info_to_save)

            # 更新元信息
            if hasattr(result, 'meta_info'):
                result.meta_info["last_message_id"] = ai_message_id
            elif isinstance(result, dict) and 'meta_info' in result:
                result['meta_info']["last_message_id"] = ai_message_id

            # 缓存会话状态
            self.cache_service.set_conversation_state(session_id, result)

            # 记录处理时间
            process_time = time.time() - start_time
            logger.debug(f"消息处理完成，耗时: {process_time:.2f}秒")

            # 获取元信息
            meta_info_to_return = {}
            if hasattr(result, 'meta_info'):
                meta_info_to_return = result.meta_info
            elif isinstance(result, dict) and 'meta_info' in result:
                meta_info_to_return = result['meta_info']

            return {
                "response": response,
                "session_id": session_id,
                "meta_info": meta_info_to_return
            }
        except Exception as e:
            logger.error(f"处理消息时出错: {str(e)}")
            self.db.rollback()
            raise

    async def process_message_stream(self,
                                    message: str,
                                    session_id: Optional[str] = None,
                                    user_id: Optional[int] = None,
                                    meta_info: Optional[Dict] = None) -> AsyncGenerator[Dict[str, Any], None]:
        """处理用户消息并以流的形式返回响应"""
        start_time = time.time()

        # 创建或获取会话ID
        session_id = session_id or str(uuid.uuid4())
        logger.debug(f"流式处理会话 {session_id} 的消息")

        # 获取用户信息（使用缓存）
        user = None
        if user_id:
            # 尝试从缓存获取用户信息
            cached_user_info = self.cache_service.get_user_info(user_id)
            if cached_user_info:
                user = type('User', (), cached_user_info)  # 创建一个类似用户对象的结构
                logger.debug(f"从缓存获取用户信息: {user_id}")
            else:
                # 从数据库获取并缓存
                user = crud_user.get(self.db, id=user_id)
                if not user:
                    raise HTTPException(status_code=404, detail="User not found")

                # 缓存用户信息
                user_dict = {
                    "id": user.id,
                    "name": user.nickname,
                    "gender": user.gender,
                    "age": user.age,
                    "height": user.height,
                    "weight": user.weight,
                    "fitness_goal": user.fitness_goal,
                    "fitness_level": user.experience_level
                }
                self.cache_service.set_user_info(user_id, user_dict)

        # 使用事务处理数据库操作
        try:
            # 获取或创建会话
            conversation = self._get_or_create_conversation(session_id, user_id)

            # 保存用户消息
            user_message_id = self._save_message(conversation.id, message, "user", meta_info)

            # 准备初始状态
            initial_state = await self._prepare_initial_state(message, user, meta_info)

            # 设置会话ID
            initial_state.session_id = session_id

            # 检查是否有缓存的会话状态
            cached_state = self.cache_service.get_conversation_state(session_id)
            if cached_state and hasattr(cached_state, 'messages'):
                # 合并新消息到缓存状态
                cached_state.messages.append(AnyMessage(role="user", content=message))
                # 更新元信息
                if meta_info:
                    cached_state.meta_info.update(meta_info)
                # 使用缓存状态
                initial_state = cached_state
                logger.debug(f"使用缓存的会话状态: {session_id}")
            elif cached_state and isinstance(cached_state, dict) and 'messages' in cached_state:
                # 如果缓存状态是字典格式，转换为ConversationState对象
                from app.services.state_definitions import AnyMessage as StateAnyMessage

                # 提取消息
                messages = cached_state.get('messages', [])
                if isinstance(messages, list):
                    # 添加新消息
                    messages.append({"role": "user", "content": message})

                    # 创建新的ConversationState
                    initial_state = ConversationState(
                        messages=[StateAnyMessage(role=m["role"], content=m["content"])
                                 if isinstance(m, dict) else m for m in messages],
                        user_info=cached_state.get('user_info', {}),
                        training_params=cached_state.get('training_params', {}),
                        meta_info=meta_info or cached_state.get('meta_info', {}),
                        flow_state=cached_state.get('flow_state', {}),
                        session_id=session_id
                    )
                    logger.debug(f"从字典格式转换缓存状态: {session_id}")

            # 压缩消息历史，减少token使用
            if len(initial_state.messages) > 15:  # 如果消息超过15条
                initial_state.messages = await ConversationContextManager.compress_message_history(initial_state.messages)
                logger.debug(f"压缩消息历史: {len(initial_state.messages)}条消息")

            # 执行图状工作流(流式)
            config = {
                "configurable": {"session_id": session_id},
                "recursion_limit": 50  # 设置递归限制
            }

            # 收集完整响应用于保存
            full_response = ""
            last_meta_info = meta_info.copy() if meta_info else {}

            # 使用流式API
            stream_gen = self.graph.astream(initial_state, config=config)

            # 批量处理变量，减少数据库写入次数
            response_chunks = []
            batch_size = 5  # 每5个块处理一次
            last_state = None

            async for chunk in stream_gen:
                if isinstance(chunk, dict) and "event" in chunk:
                    # 特殊事件处理
                    yield chunk
                    if chunk["event"] == "meta_info_update":
                        last_meta_info.update(chunk.get("data", {}))
                elif isinstance(chunk, ConversationState):
                    # 保存最后的状态
                    last_state = chunk

                    # 状态更新，提取最新消息
                    if chunk.messages and chunk.messages[-1].role == "assistant":
                        content = chunk.messages[-1].content
                        if content and content not in response_chunks:
                            # 添加到响应块列表
                            response_chunks.append(content)

                            # 更新完整响应
                            full_response += content

                            # 发送响应块
                            yield content

                            # 如果累积了足够的块，更新元信息
                            if len(response_chunks) >= batch_size:
                                # 提取元信息更新
                                if chunk.meta_info != last_meta_info:
                                    last_meta_info.update(chunk.meta_info)
                                    yield {"event": "meta_info_update", "data": last_meta_info}

                                # 重置块列表
                                response_chunks = []

                    # 提取元信息更新
                    if chunk.meta_info and chunk.meta_info != last_meta_info:
                        last_meta_info.update(chunk.meta_info)
                        yield {"event": "meta_info_update", "data": last_meta_info}

            # 保存完整响应
            if full_response:
                ai_message_id = self._save_message(conversation.id, full_response, "assistant", last_meta_info)

                # 更新元信息
                if last_state:
                    last_state.meta_info["last_message_id"] = ai_message_id

                    # 缓存最终状态
                    self.cache_service.set_conversation_state(session_id, last_state)

            # 记录处理时间
            process_time = time.time() - start_time
            logger.debug(f"流式消息处理完成，耗时: {process_time:.2f}秒")

        except Exception as e:
            logger.error(f"流式处理消息时出错: {str(e)}")
            self.db.rollback()
            # 发送错误事件
            yield {"event": "error", "data": {"message": "处理消息时出错"}}
            raise

    async def _prepare_initial_state(self, message: str, user: Any, meta_info: Dict = None) -> ConversationState:
        """准备初始状态"""
        messages = [{"role": "user", "content": message}]

        # 提取已有会话历史(如果有)
        session_id = meta_info.get("session_id") if meta_info else None
        if session_id:
            # 尝试从缓存获取会话历史
            cached_state = self.cache_service.get_session_state(f"history:{session_id}")
            if cached_state and "messages" in cached_state:
                logger.debug(f"从缓存获取会话历史: {session_id}")
                messages = cached_state["messages"] + messages
            else:
                # 从数据库获取
                prev_messages = self._get_conversation_messages(session_id)
                if prev_messages:
                    messages = prev_messages + messages
                    # 缓存会话历史
                    self.cache_service.set_session_state(f"history:{session_id}", {"messages": prev_messages})

        # 构建用户信息
        user_info = {}
        if user:
            # 如果user是字典，直接使用
            if isinstance(user, dict):
                user_info = user
            # 如果user是对象，提取属性
            elif hasattr(user, "id"):
                user_info = {
                    "user_id": getattr(user, "id", None),
                    "name": getattr(user, "nickname", ""),
                    "gender": getattr(user, "gender", ""),
                    "age": getattr(user, "age", 0),
                    "height": getattr(user, "height", 0),
                    "weight": getattr(user, "weight", 0),
                    "fitness_goal": getattr(user, "fitness_goal", ""),
                    "fitness_level": getattr(user, "experience_level", "")
                }

        # 尝试从消息中推断缺失的用户信息
        if user_info:
            inferred_user_info = await UserProfileManager.infer_missing_fields(user_info, message)
            user_info.update(inferred_user_info)

        # 使用增强参数提取器提取训练参数
        message_history = [{"role": m["role"], "content": m["content"]} for m in messages[-5:]]  # 只使用最近5条消息
        training_params = await EnhancedParameterExtractor.extract_parameters(
            message,
            context={"training_params": meta_info.get("training_params", {}) if meta_info else {}},
            message_history=message_history
        )

        # 构造初始状态
        # 使用正确的AnyMessage类型
        from app.services.state_definitions import AnyMessage as StateAnyMessage

        initial_state = ConversationState(
            messages=[StateAnyMessage(role=m["role"], content=m["content"]) for m in messages],
            user_info=user_info,
            training_params=training_params,
            meta_info=meta_info or {},
            flow_state={"intent": meta_info.get("quick_intent") if meta_info else None},
            session_id=session_id or f"session_{int(time.time())}"
        )

        # 如果有意图，使用对话流程优化器优化流程
        if initial_state.flow_state.get("intent"):
            intent = initial_state.flow_state["intent"]
            optimized_state = await DialogFlowOptimizer.optimize_dialog_flow(
                intent,
                message,
                {
                    "user_info": user_info,
                    "training_params": training_params,
                    "flow_state": initial_state.flow_state,
                    "meta_info": initial_state.meta_info
                }
            )

            # 更新状态
            if "training_params" in optimized_state:
                initial_state.training_params.update(optimized_state["training_params"])
            if "user_info" in optimized_state:
                initial_state.user_info.update(optimized_state["user_info"])
            if "flow_state" in optimized_state:
                initial_state.flow_state.update(optimized_state["flow_state"])
            if "meta_info" in optimized_state:
                initial_state.meta_info.update(optimized_state["meta_info"])

        return initial_state

    def _get_or_create_conversation(self, session_id: str, user_id: Optional[int] = None):
        """获取或创建会话"""
        # 尝试从缓存获取会话
        cached_conversation = self.cache_service.get_session_state(f"conversation:{session_id}")
        if cached_conversation:
            # 创建一个类似会话对象的结构
            return type('Conversation', (), cached_conversation)

        # 从数据库获取
        conversation = crud_conversation.get_by_session_id(self.db, session_id=session_id)
        if not conversation:
            # 创建新会话
            conversation_in = {
                "session_id": session_id,
                "user_id": user_id,
                "is_active": True,
                "metadata": {},
                "last_active": datetime.now()
            }
            conversation = crud_conversation.create(self.db, obj_in=conversation_in)
        else:
            # 更新最后活动时间
            crud_conversation.update(
                self.db,
                db_obj=conversation,
                obj_in={"last_active": datetime.now()}
            )

        # 缓存会话
        self.cache_service.set_session_state(f"conversation:{session_id}", {
            "id": conversation.id,
            "session_id": conversation.session_id,
            "user_id": conversation.user_id,
            "is_active": conversation.is_active,
            "metadata": conversation.metadata
        })

        return conversation

    def _save_message(self, conversation_id: int, content: str, role: str, meta_info: Dict = None) -> int:
        """保存消息到数据库，返回消息ID"""
        # 获取会话的用户ID
        conversation = crud_conversation.get(self.db, id=conversation_id)
        user_id = conversation.user_id if conversation else None

        message_in = {
            "conversation_id": conversation_id,
            "user_id": user_id,
            "content": content,
            "role": role,
            "metadata": meta_info
        }
        message = crud_message.create(self.db, obj_in=message_in)

        # 返回消息ID
        return message.id

    def _get_conversation_messages(self, session_id: str, limit: int = 20) -> List[Dict[str, str]]:
        """获取会话历史消息"""
        # 尝试使用批量查询优化
        try:
            # 使用原生SQL查询，减少ORM开销
            query = text("""
            SELECT m.role, m.content
            FROM messages m
            JOIN conversations c ON m.conversation_id = c.id
            WHERE c.session_id = :session_id
            ORDER BY m.created_at ASC
            LIMIT :limit
            """)

            result = self.db.execute(query, {"session_id": session_id, "limit": limit})

            # 转换结果
            messages = [{"role": row[0], "content": row[1]} for row in result]

            # 如果找到消息，缓存结果
            if messages:
                self.cache_service.set_session_state(f"history:{session_id}", {"messages": messages})

            return messages

        except Exception as e:
            # 如果原生SQL失败，回退到ORM
            logger.warning(f"原生SQL查询失败，回退到ORM: {str(e)}")

            conversation = crud_conversation.get_by_session_id(self.db, session_id=session_id)
            if not conversation:
                return []

            messages = crud_message.get_multi_by_conversation(
                self.db, conversation_id=conversation.id, limit=limit
            )

            result = [{"role": msg.role, "content": msg.content} for msg in messages]

            # 缓存结果
            if result:
                self.cache_service.set_session_state(f"history:{session_id}", {"messages": result})

            return result

    def _save_messages_batch(self, conversation_id: int, messages: List[Dict[str, Any]]) -> List[int]:
        """批量保存消息，返回消息ID列表"""
        # 使用批量插入优化
        try:
            # 准备批量插入的值
            values = []
            for msg in messages:
                # 获取会话的用户ID
                conversation = crud_conversation.get(self.db, id=conversation_id)
                user_id = conversation.user_id if conversation else None

                values.append({
                    "conversation_id": conversation_id,
                    "user_id": user_id,
                    "content": msg.get("content", ""),
                    "role": msg.get("role", "user"),
                    "metadata": json.dumps(msg.get("metadata", {})) if msg.get("metadata") else None,
                    "created_at": datetime.now(),
                    "updated_at": datetime.now()
                })

            # 执行批量插入
            if values:
                query = text("""
                INSERT INTO messages (conversation_id, user_id, content, role, metadata, created_at, updated_at)
                VALUES (:conversation_id, :user_id, :content, :role, :metadata, :created_at, :updated_at)
                RETURNING id
                """)

                result = self.db.execute(query, values)
                message_ids = [row[0] for row in result]
                self.db.commit()

                return message_ids

        except Exception as e:
            logger.error(f"批量保存消息失败: {str(e)}")
            self.db.rollback()

            # 回退到单条插入
            message_ids = []
            for msg in messages:
                # 获取会话的用户ID
                conversation = crud_conversation.get(self.db, id=conversation_id)
                user_id = conversation.user_id if conversation else None

                message = crud_message.create(self.db, obj_in={
                    "conversation_id": conversation_id,
                    "user_id": user_id,
                    "content": msg.get("content", ""),
                    "role": msg.get("role", "user"),
                    "metadata": msg.get("metadata")
                })
                message_ids.append(message.id)

            return message_ids

        return []