from sqlalchemy.orm import Session
from sqlalchemy.future import select
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from app.models.team import (
    Team, TeamMembership, TeamRole, MembershipStatus, 
    TeamInvitation, InvitationStatus
)
from app.models.user import User
from app.schemas.team import (
    MembershipCreate, MembershipUpdate, MembershipResponse, 
    InvitationCreate, InvitationResponse
)
from app.crud.team import (
    create_membership, get_team_membership, update_membership, 
    get_team_members, delete_membership,
    create_invitation, get_invitation, update_invitation, 
    get_team_invitations, get_user_invitations
)
from app.services.team_service import TeamService, TeamServiceException

class MembershipNotFoundException(TeamServiceException):
    """成员关系不存在异常"""
    pass

class InvitationNotFoundException(TeamServiceException):
    """邀请不存在异常"""
    pass

class TeamMemberService:
    """团队成员服务，处理团队成员相关业务逻辑"""
    
    def __init__(self, db: Session):
        """初始化
        
        Args:
            db (Session): 数据库会话
        """
        self.db = db
        self.team_service = TeamService(db)
    
    async def add_team_member(self, team_id: int, membership_data: MembershipCreate, current_user: User) -> TeamMembership:
        """添加团队成员
        
        Args:
            team_id (int): 团队ID
            membership_data (MembershipCreate): 成员关系创建数据
            current_user (User): 当前用户
            
        Returns:
            TeamMembership: 创建的成员关系
        """
        # 检查权限
        await self.team_service._check_team_permission(team_id, current_user.id, [TeamRole.OWNER, TeamRole.ADMIN])
        
        # 创建成员关系
        membership = await create_membership(self.db, team_id, membership_data)
        
        # 更新团队统计
        await self.team_service.update_team_stats(team_id)
        
        return membership
    
    async def get_membership(self, team_id: int, user_id: int) -> Optional[TeamMembership]:
        """获取成员关系
        
        Args:
            team_id (int): 团队ID
            user_id (int): 用户ID
            
        Returns:
            Optional[TeamMembership]: 成员关系对象，如果不存在则返回None
        """
        return await get_team_membership(self.db, team_id, user_id)
    
    async def update_member_role(self, team_id: int, user_id: int, role_data: MembershipUpdate, current_user: User) -> TeamMembership:
        """更新成员角色
        
        Args:
            team_id (int): 团队ID
            user_id (int): 用户ID
            role_data (MembershipUpdate): 角色更新数据
            current_user (User): 当前用户
            
        Returns:
            TeamMembership: 更新后的成员关系
        """
        # 检查权限
        await self.team_service._check_team_permission(team_id, current_user.id, [TeamRole.OWNER, TeamRole.ADMIN])
        
        # 不允许修改所有者角色
        target_membership = await self.get_membership(team_id, user_id)
        if not target_membership:
            raise MembershipNotFoundException(f"Membership for user {user_id} in team {team_id} not found")
        
        # 检查是否是团队所有者
        team = await self.team_service.get_team(team_id)
        if user_id == team.owner_id and role_data.role != TeamRole.OWNER:
            raise TeamServiceException("Cannot change the role of the team owner")
        
        # 更新角色
        membership = await update_membership(self.db, team_id, user_id, role_data)
        if not membership:
            raise MembershipNotFoundException(f"Membership for user {user_id} in team {team_id} not found")
        
        return membership
    
    async def remove_team_member(self, team_id: int, user_id: int, current_user: User) -> bool:
        """移除团队成员
        
        Args:
            team_id (int): 团队ID
            user_id (int): 用户ID
            current_user (User): 当前用户
            
        Returns:
            bool: 是否成功移除
        """
        # 检查权限
        await self.team_service._check_team_permission(team_id, current_user.id, [TeamRole.OWNER, TeamRole.ADMIN])
        
        # 不允许移除所有者
        team = await self.team_service.get_team(team_id)
        if user_id == team.owner_id:
            raise TeamServiceException("Cannot remove the team owner")
        
        # 移除成员
        result = await delete_membership(self.db, team_id, user_id)
        if not result:
            raise MembershipNotFoundException(f"Membership for user {user_id} in team {team_id} not found")
        
        # 更新团队统计
        await self.team_service.update_team_stats(team_id)
        
        return True
    
    async def get_team_members(self, team_id: int, role: Optional[TeamRole] = None, current_user: User = None) -> List[Dict[str, Any]]:
        """获取团队成员列表
        
        Args:
            team_id (int): 团队ID
            role (Optional[TeamRole]): 角色过滤
            current_user (User): 当前用户
            
        Returns:
            List[Dict[str, Any]]: 成员列表
        """
        # 检查访问权限
        if current_user:
            has_access = await self.team_service.check_team_access(team_id, current_user.id)
            if not has_access:
                raise TeamServiceException("No access to this team")
        
        return await get_team_members(self.db, team_id, role)
    
    async def create_team_invitation(self, team_id: int, invitation_data: InvitationCreate, current_user: User) -> TeamInvitation:
        """创建团队邀请
        
        Args:
            team_id (int): 团队ID
            invitation_data (InvitationCreate): 邀请创建数据
            current_user (User): 当前用户
            
        Returns:
            TeamInvitation: 创建的邀请
        """
        # 检查权限
        await self.team_service._check_team_permission(team_id, current_user.id, [TeamRole.OWNER, TeamRole.ADMIN])
        
        # 创建邀请
        invitation = await create_invitation(self.db, team_id, current_user.id, invitation_data)
        
        return invitation
    
    async def respond_to_invitation(self, invitation_id: int, accept: bool, current_user: User) -> Dict[str, Any]:
        """响应团队邀请
        
        Args:
            invitation_id (int): 邀请ID
            accept (bool): 是否接受
            current_user (User): 当前用户
            
        Returns:
            Dict[str, Any]: 响应结果
        """
        # 获取邀请
        invitation = await get_invitation(self.db, invitation_id)
        if not invitation:
            raise InvitationNotFoundException(f"Invitation with ID {invitation_id} not found")
        
        # 检查邀请是否是给当前用户的
        if invitation.invitee_id != current_user.id:
            raise TeamServiceException("This invitation is not for you")
        
        # 检查邀请状态
        if invitation.status != InvitationStatus.PENDING:
            raise TeamServiceException(f"Invitation is already {invitation.status.name}")
        
        # 检查邀请是否过期
        if invitation.expired_at and invitation.expired_at < datetime.now():
            invitation.status = InvitationStatus.EXPIRED
            self.db.add(invitation)
            await self.db.commit()
            raise TeamServiceException("Invitation has expired")
        
        if accept:
            # 接受邀请
            invitation.status = InvitationStatus.ACCEPTED
            
            # 创建成员关系
            membership_data = MembershipCreate(
                user_id=current_user.id,
                role=invitation.role,
                status=MembershipStatus.ACTIVE
            )
            membership = await create_membership(self.db, invitation.team_id, membership_data)
            
            # 更新团队统计
            await self.team_service.update_team_stats(invitation.team_id)
            
            result = {
                "status": "accepted",
                "team_id": invitation.team_id,
                "role": invitation.role
            }
        else:
            # 拒绝邀请
            invitation.status = InvitationStatus.REJECTED
            result = {
                "status": "rejected",
                "team_id": invitation.team_id
            }
        
        self.db.add(invitation)
        await self.db.commit()
        
        return result
    
    async def get_team_invitations(self, team_id: int, status: Optional[InvitationStatus] = None, current_user: User = None) -> List[Dict[str, Any]]:
        """获取团队邀请列表
        
        Args:
            team_id (int): 团队ID
            status (Optional[InvitationStatus]): 状态过滤
            current_user (User): 当前用户
            
        Returns:
            List[Dict[str, Any]]: 邀请列表
        """
        # 检查访问权限
        if current_user:
            has_access = await self.team_service.check_team_access(team_id, current_user.id)
            if not has_access:
                raise TeamServiceException("No access to this team")
        
        return await get_team_invitations(self.db, team_id, status)
    
    async def get_user_invitations(self, user_id: int, status: Optional[InvitationStatus] = None) -> List[Dict[str, Any]]:
        """获取用户收到的邀请列表
        
        Args:
            user_id (int): 用户ID
            status (Optional[InvitationStatus]): 状态过滤
            
        Returns:
            List[Dict[str, Any]]: 邀请列表
        """
        return await get_user_invitations(self.db, user_id, status)
    
    async def cancel_invitation(self, invitation_id: int, current_user: User) -> bool:
        """取消邀请
        
        Args:
            invitation_id (int): 邀请ID
            current_user (User): 当前用户
            
        Returns:
            bool: 是否成功取消
        """
        # 获取邀请
        invitation = await get_invitation(self.db, invitation_id)
        if not invitation:
            raise InvitationNotFoundException(f"Invitation with ID {invitation_id} not found")
        
        # 检查权限
        await self.team_service._check_team_permission(invitation.team_id, current_user.id, [TeamRole.OWNER, TeamRole.ADMIN])
        
        # 检查邀请状态
        if invitation.status != InvitationStatus.PENDING:
            raise TeamServiceException(f"Cannot cancel invitation with status {invitation.status.name}")
        
        # 取消邀请
        invitation.status = InvitationStatus.REJECTED
        self.db.add(invitation)
        await self.db.commit()
        
        return True
