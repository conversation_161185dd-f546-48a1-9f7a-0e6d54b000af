from sqlalchemy.orm import Session
from typing import Optional, List, Dict, Any
from fastapi import Depends, HTTPException

from app import models, schemas, crud
from app.models.workout import WorkoutStatus
from datetime import datetime
import json

class WorkoutSummaryService:
    """
    训练摘要服务，处理从Workout到DailyWorkout的转换，
    用于社区分享功能。
    """
    
    async def create_daily_workout_from_workout(
        self, 
        db: Session, 
        workout_id: int, 
        user_id: int, 
        title: Optional[str] = None, 
        content: Optional[str] = None, 
        visibility: str = "Everyone"
    ) -> Optional[models.DailyWorkout]:
        """
        从已完成的Workout创建DailyWorkout记录用于社区分享
        
        Args:
            db: 数据库会话
            workout_id: Workout ID
            user_id: 用户ID
            title: 自定义标题
            content: 自定义内容
            visibility: 可见性设置
            
        Returns:
            创建的DailyWorkout实例，如果Workout不存在或未完成则返回None
        """
        # 获取Workout
        workout = db.query(models.Workout).filter(models.Workout.id == workout_id).first()
        
        # 检查Workout是否存在且属于该用户
        if not workout:
            return None
            
        # 检查训练计划的用户身份（通过training_plan_id和training_plan关系）
        # 这里假设有training_plan_id和training_plan关系，需要根据实际模型调整
        if hasattr(workout, 'training_plan') and workout.training_plan:
            if workout.training_plan.user_id != user_id:
                return None
        
        # 检查训练是否已完成
        if workout.status != WorkoutStatus.COMPLETED:
            return None
            
        # 如果已经有DailyWorkout，则返回现有的
        if workout.daily_workout:
            return workout.daily_workout
            
        # 创建新的DailyWorkout
        daily_workout = workout.create_daily_summary(
            user_id=user_id,
            title=title,
            content=content,
            visibility=visibility
        )
        
        db.add(daily_workout)
        db.commit()
        db.refresh(daily_workout)
        
        return daily_workout
        
    async def get_workout_summary(
        self,
        db: Session,
        daily_workout_id: int,
        current_user_id: Optional[int] = None
    ) -> Optional[models.DailyWorkout]:
        """
        获取训练摘要详情，考虑可见性设置
        
        Args:
            db: 数据库会话
            daily_workout_id: DailyWorkout ID
            current_user_id: 当前用户ID，用于检查可见性
            
        Returns:
            DailyWorkout实例，如果不存在或无权限查看则返回None
        """
        daily_workout = db.query(models.DailyWorkout).filter(
            models.DailyWorkout.id == daily_workout_id
        ).first()
        
        if not daily_workout:
            return None
            
        # 检查可见性
        if daily_workout.visibility != "Everyone" and daily_workout.user_id != current_user_id:
            return None
            
        return daily_workout
        
    async def create_or_update_workout_and_summary(
        self,
        db: Session,
        user_id: int,
        workout_id: Optional[int] = None,
        data: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        创建或更新Workout和相关的WorkoutExercise，并创建DailyWorkout用于社区分享
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            workout_id: 可选的Workout ID (如果提供则更新，否则创建新的)
            data: 包含训练数据的字典
            
        Returns:
            包含创建/更新后的Workout和DailyWorkout信息的字典
        """
        # 处理训练数据
        workout_data = data.get("workout_data", {})
        exercises = workout_data.get("exercises", [])
        duration_seconds = workout_data.get("duration_seconds", 0)
        total_sets = workout_data.get("total_sets", 0)
        total_volume = workout_data.get("total_volume", 0)
        
        # 如果提供了workout_id，查找现有的workout
        workout = None
        if workout_id:
            workout = db.query(models.Workout).filter(models.Workout.id == workout_id).first()
        
        # 如果找不到现有的workout，则创建新的
        if not workout:
            # 创建新的Workout记录
            workout = models.Workout(
                name=data.get("title", "训练记录"),
                training_plan_id=1,  # 临时使用ID为1的训练计划，如果不存在可能需要创建
                day_number=1,  # 设置一个默认值
                status=WorkoutStatus.COMPLETED,
                actual_duration=duration_seconds // 60,  # 转换为分钟
                start_time=datetime.utcnow(),
                end_time=datetime.utcnow()
            )
            db.add(workout)
            db.flush()  # 获取ID但不提交事务
        else:
            # 更新现有Workout
            workout.name = data.get("title", workout.name)
            workout.status = WorkoutStatus.COMPLETED
            workout.actual_duration = duration_seconds // 60
            workout.end_time = datetime.utcnow()
        
        # 记录现有的workout_exercise IDs以便后续比较
        existing_exercise_ids = set()
        if workout.workout_exercises:
            existing_exercise_ids = {ex.id for ex in workout.workout_exercises}
        
        # 处理的exercise IDs，用于后续删除不再需要的records
        processed_exercise_ids = set()
        
        # 处理每个训练动作
        for idx, exercise_data in enumerate(exercises):
            exercise_id = exercise_data.get("id")
            exercise_name = exercise_data.get("name", "")
            image_url = exercise_data.get("imageUrl", "")
            sets_data = exercise_data.get("sets", [])
            
            # 查找或创建exercise记录
            exercise = None
            if exercise_id:
                exercise = db.query(models.WorkoutExercise).filter(
                    models.WorkoutExercise.id == exercise_id,
                    models.WorkoutExercise.workout_id == workout.id
                ).first()
            
            # 如果找不到现有记录，创建新的
            if not exercise:
                exercise = models.WorkoutExercise(
                    workout_id=workout.id,
                    exercise_id=exercise_id,  # 假设这是关联到exercises表的ID
                    sets=len(sets_data),
                    reps=",".join(str(s.get("reps", "")) for s in sets_data if s),
                    order=idx + 1
                )
                db.add(exercise)
                db.flush()
            else:
                # 更新现有记录
                exercise.sets = len(sets_data)
                exercise.reps = ",".join(str(s.get("reps", "")) for s in sets_data if s)
                exercise.order = idx + 1
                processed_exercise_ids.add(exercise.id)
            
            # 处理每组数据
            existing_set_records = []
            if exercise_id:
                existing_set_records = db.query(models.SetRecord).filter(
                    models.SetRecord.workout_exercise_id == exercise.id
                ).all()
                
            existing_set_ids = {sr.id for sr in existing_set_records}
            processed_set_ids = set()
            
            for set_idx, set_data in enumerate(sets_data):
                set_id = set_data.get("id")
                set_record = None
                
                if set_id:
                    set_record = db.query(models.SetRecord).filter(
                        models.SetRecord.id == set_id
                    ).first()
                
                if not set_record:
                    # 创建新的SetRecord
                    set_record = models.SetRecord(
                        workout_exercise_id=exercise.id,
                        set_number=set_idx + 1,
                        weight=float(set_data.get("weight", 0)),
                        reps=int(set_data.get("reps", 0)),
                        completed=set_data.get("completed", False),
                        notes=set_data.get("notes", "")
                    )
                    db.add(set_record)
                else:
                    # 更新现有SetRecord
                    set_record.set_number = set_idx + 1
                    set_record.weight = float(set_data.get("weight", set_record.weight))
                    set_record.reps = int(set_data.get("reps", set_record.reps))
                    set_record.completed = set_data.get("completed", set_record.completed)
                    set_record.notes = set_data.get("notes", set_record.notes)
                    processed_set_ids.add(set_record.id)
            
            # 删除不再需要的SetRecord
            for set_id in existing_set_ids - processed_set_ids:
                set_to_delete = db.query(models.SetRecord).filter(models.SetRecord.id == set_id).first()
                if set_to_delete:
                    db.delete(set_to_delete)
        
        # 删除不再需要的WorkoutExercise
        for ex_id in existing_exercise_ids - processed_exercise_ids:
            ex_to_delete = db.query(models.WorkoutExercise).filter(models.WorkoutExercise.id == ex_id).first()
            if ex_to_delete:
                # 首先删除关联的SetRecords
                db.query(models.SetRecord).filter(models.SetRecord.workout_exercise_id == ex_id).delete()
                db.delete(ex_to_delete)
        
        # 创建DailyWorkout
        daily_workout = models.DailyWorkout(
            user_id=user_id,
            workout_id=workout.id,
            name=data.get("name", data.get("title", "训练记录")),  # 首先使用name，如果没有则使用title，如果都没有则使用默认值
            title=data.get("title", "训练记录"),
            content=data.get("content", ""),
            training_duration=duration_seconds // 60,
            training_sets=total_sets,
            training_volume=str(total_volume) if total_volume else "",
            visibility=data.get("visibility", "Everyone").lower(),
            training_date=datetime.utcnow()
        )
        
        db.add(daily_workout)
        db.commit()
        db.refresh(workout)
        db.refresh(daily_workout)
        
        # 返回结果
        return {
            "workout": workout,
            "daily_workout": daily_workout
        } 