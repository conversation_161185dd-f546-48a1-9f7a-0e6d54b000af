from typing import List, Dict, Optional, Any, Union
from datetime import datetime
import logging
from langchain.output_parsers import PydanticOutputParser
from app import crud, models
from app.schemas.training_plan import TrainingPlanSchema, DailyWorkoutSchema
from app.schemas.exercise import ExerciseSchema
from app.services.llm_proxy_service import LLMProxyService
from app.services.base_service import BaseService
from sqlalchemy.orm import Session
import uuid
import json

# 导入工具类和转换器
from app.utils.training_plan_utils import (
    extract_json_from_response,
    get_body_part_name,
    get_fitness_goal_name,
    normalize_body_part_id,
    safe_get
)
from app.utils.exercise_utils import (
    determine_training_scenario,
    format_candidate_exercises,
    remove_duplicate_exercises
)
from app.helpers.json_helper import JsonHelper
from app.helpers.error_helper import <PERSON>rror<PERSON>elper
from app.transformers.training_plan_transformer import TrainingPlanTransformer
from app.services.prompt_service import PromptService
from app.services.llm_service import LLMService
from app.services.sql_tool_service import BODY_PART_CATEGORIES, EQUIPMENT_CATEGORIES
from app.core.config import settings

logger = logging.getLogger(__name__)

class TrainingPlanService(BaseService):
    """训练计划生成服务"""

    def __init__(self, db_session: Session, llm_proxy_service: LLMProxyService, sql_tool_service=None):
        """初始化服务

        Args:
            db_session: 数据库会话
            llm_proxy_service: LLM代理服务
            sql_tool_service: SQL工具服务，用于查询数据库中的训练动作
        """
        super().__init__(db_session)
        self.db = db_session
        self.llm_proxy_service = llm_proxy_service
        self.sql_tool_service = sql_tool_service

        # 创建LLM服务
        self.llm_service = LLMService(llm_proxy_service)

        # 计划类型映射
        self.plan_types = {
            "single_day": "单日计划",
            "weekly": "周计划",
            "beginner": "新手计划",
            "intermediate": "中级计划",
            "advanced": "高级计划"
        }

    @ErrorHelper.handle_exceptions(error_message="生成训练计划失败", log_error=True)
    async def generate_training_plan(
        self,
        user_id: int,
        duration_weeks: int = 4,
        days_per_week: int = 3,
        fitness_goal: Optional[int] = None,
        available_equipment: Optional[List[int]] = None,
        focus_body_parts: Optional[List[int]] = None,
        time_per_workout: Optional[int] = 60,
        additional_notes: Optional[str] = None
    ) -> Dict[str, Any]:
        """生成完整训练计划

        Args:
            user_id: 用户ID
            duration_weeks: 计划持续周数
            days_per_week: 每周训练天数
            fitness_goal: 健身目标
            available_equipment: 可用器材ID列表
            focus_body_parts: 重点训练部位ID列表
            time_per_workout: 每次训练时长（分钟）
            additional_notes: 额外备注

        Returns:
            生成的训练计划
        """
        # 获取用户信息
        user = crud.user.get(self.db, id=user_id)
        if not user:
            raise ValueError(f"用户 ID {user_id} 不存在")

        # 获取用户训练历史
        training_history = crud.crud_user_training_record.get_user_training_history(
            self.db, user_id=user_id, days=30
        )

        # 构建上下文
        context = TrainingPlanTransformer.build_plan_context(
            user=user,
            training_history=training_history,
            duration_weeks=duration_weeks,
            days_per_week=days_per_week,
            fitness_goal=fitness_goal,
            available_equipment=available_equipment,
            focus_body_parts=focus_body_parts,
            time_per_workout=time_per_workout,
            additional_notes=additional_notes
        )

        # 创建提示
        prompt = self._create_training_plan_prompt(context)

        # 生成结构化训练计划
        plan_data = await self._generate_structured_plan(prompt, context)

        # 保存到数据库
        return TrainingPlanTransformer.transform_training_plan_to_db(user_id, plan_data, self.db)

    @ErrorHelper.handle_exceptions(error_message="生成单日训练计划失败", log_error=True)
    async def generate_daily_workout(
        self,
        user_id: int,
        available_time: int = 60,
        target_body_parts: Optional[List[int]] = None,
        available_equipment: Optional[List[int]] = None,
        recovery_level: Optional[int] = 5,
        additional_notes: Optional[str] = None
    ) -> Dict[str, Any]:
        """生成单日训练计划

        Args:
            user_id: 用户ID
            available_time: 可用时间（分钟）
            target_body_parts: 目标训练部位ID列表
            available_equipment: 可用器材ID列表
            recovery_level: 恢复程度（1-10）
            additional_notes: 额外备注

        Returns:
            生成的单日训练计划
        """
        # 记录函数调用开始
        logger.info(f"进入generate_daily_workout函数，参数: user_id={user_id}, target_body_parts={target_body_parts}, available_equipment={available_equipment}")

        # 记录调用开始时间
        import time
        start_time = time.time()

        try:
            # 获取用户信息
            user = crud.user.get(self.db, id=user_id)
            if not user:
                logger.error(f"用户 ID {user_id} 不存在")
                raise ValueError(f"用户 ID {user_id} 不存在")

            logger.info(f"成功获取用户信息: id={user.id}, 性别={user.gender}, 年龄={user.age}, 健身目标={user.fitness_goal}")
        except Exception as e:
            logger.error(f"获取用户信息时出错: {str(e)}", exc_info=True)
            raise

        # 获取用户最近训练记录
        training_history = crud.crud_user_training_record.get_user_training_history(
            self.db, user_id=user_id, days=7
        )

        # 构建上下文
        context = TrainingPlanTransformer.build_daily_workout_context(
            user=user,
            training_history=training_history,
            available_time=available_time,
            target_body_parts=target_body_parts,
            available_equipment=available_equipment,
            recovery_level=recovery_level,
            additional_notes=additional_notes
        )

        # 创建提示
        prompt = self._create_daily_workout_prompt(context)

        # 生成结构化训练计划
        workout_data = await self._generate_structured_daily_workout(prompt, context)

        # 确定训练场景
        training_scenario = determine_training_scenario(available_equipment)

        try:
            # 保存到数据库
            result = TrainingPlanTransformer.transform_daily_workout_to_db(
                user_id=user_id,
                workout_data=workout_data,
                db=self.db,
                fitness_goal=user.fitness_goal,
                experience_level=user.experience_level,
                target_body_parts=target_body_parts,
                training_scenario=training_scenario
            )

            # 记录调用结束时间和耗时
            end_time = time.time()
            logger.info(f"generate_daily_workout函数执行完成，耗时: {end_time - start_time:.2f}秒")

            # 记录结果信息
            logger.info(f"生成的单日训练计划: ID={result.get('id')}, 名称={result.get('name')}")
            if 'workout_exercises' in result:
                logger.info(f"包含 {len(result['workout_exercises'])} 个训练动作")
                # 记录部分动作信息，便于调试
                sample_exercises = result['workout_exercises'][:3]  # 只记录前3个
                for i, ex in enumerate(sample_exercises):
                    logger.info(f"示例动作 {i+1}: ID={ex.get('id')}, 名称={ex.get('name')}, 部位ID={ex.get('body_part_id')}")
            else:
                logger.warning("生成的训练计划没有包含workout_exercises字段")

            return result
        except Exception as e:
            logger.error(f"保存单日训练计划到数据库时出错: {str(e)}", exc_info=True)
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            raise

    def _create_training_plan_prompt(self, context: Dict[str, Any]) -> str:
        """创建训练计划生成提示

        Args:
            context: 上下文字典

        Returns:
            完整提示文本
        """
        # 创建输出解析器
        parser = PydanticOutputParser(pydantic_object=TrainingPlanSchema)
        format_instructions = parser.get_format_instructions()

        # 使用提示词服务获取提示词
        return PromptService.get_training_plan_prompt(context, format_instructions)

    def _create_daily_workout_prompt(self, context: Dict[str, Any]) -> str:
        """创建单日训练计划生成提示

        Args:
            context: 上下文字典

        Returns:
            完整提示文本
        """
        # 创建输出解析器
        parser = PydanticOutputParser(pydantic_object=DailyWorkoutSchema)
        format_instructions = parser.get_format_instructions()

        # 使用提示词服务获取提示词
        return PromptService.get_daily_workout_prompt(context, format_instructions)

    async def _generate_structured_plan(self, prompt: str, context: Dict[str, Any] = None) -> TrainingPlanSchema:
        """生成结构化训练计划

        Args:
            prompt: 完整提示文本
            context: 上下文信息，包含用户信息和计划需求

        Returns:
            结构化训练计划对象
        """
        # 获取用户和重点部位信息
        user_id = context.get("user_id") if context else None
        focus_body_parts = context.get("focus_body_parts", []) if context else []
        available_equipment = context.get("available_equipment", []) if context else []

        # 获取用户信息
        user = crud.user.get(self.db, id=user_id) if user_id else None
        if not user:
            logger.warning(f"无法获取用户ID={user_id}的信息")
            raise ValueError(f"用户ID {user_id} 不存在")

        # 获取候选动作
        candidate_exercises = await TrainingPlanTransformer.get_candidate_exercises_for_plan(
            self.sql_tool_service,
            user,
            focus_body_parts,
            available_equipment
        )

        if not candidate_exercises:
            logger.warning(f"没有找到针对部位 {focus_body_parts} 的候选动作")
            raise ValueError(f"未能找到针对指定部位的训练动作")

        # 创建候选动作信息提示
        exercise_info = self.llm_service.create_exercise_info_prompt(candidate_exercises)

        # 获取系统提示词
        system_prompt = PromptService.get_training_plan_system_prompt()

        # 增强用户提示词，添加候选动作信息
        enhanced_prompt = PromptService.enhance_prompt_with_exercises(prompt, exercise_info)

        # 生成结构化数据
        try:
            result = await self.llm_service.generate_structured_data(
                system_prompt=system_prompt,
                user_prompt=enhanced_prompt,
                schema_model=TrainingPlanSchema,
                temperature=0.2
            )

            # 验证并修复训练计划中的动作ID
            self._validate_and_fix_plan_exercise_ids(result, candidate_exercises)

            return result
        except Exception as e:
            logger.error(f"生成训练计划失败: {str(e)}")
            raise ValueError(f"生成训练计划失败: {str(e)}")

    @ErrorHelper.handle_exceptions(error_message="验证并修复训练计划动作ID失败", log_error=True)
    def _validate_and_fix_plan_exercise_ids(self, plan_schema: TrainingPlanSchema, candidate_exercises: List[Dict[str, Any]]) -> None:
        """验证并修复训练计划中的exercise_id

        Args:
            plan_schema: 训练计划结构
            candidate_exercises: 候选动作列表
        """
        # 创建候选动作ID到完整信息的映射
        candidate_map = {ex["id"]: ex for ex in candidate_exercises} if candidate_exercises else {}

        # 创建名称到ID的映射（用于名称匹配）
        name_to_id = {}
        for ex in candidate_exercises:
            name = ex.get("name", "").lower()
            if name:
                name_to_id[name] = ex["id"]

        # 获取数据库中所有有效的动作ID
        valid_ids = set()
        try:
            exercises = crud.crud_exercise.get_multi(self.db, limit=1000)
            valid_ids = {ex.id for ex in exercises}
        except Exception as e:
            logger.error(f"获取有效动作ID失败: {str(e)}")

        # 验证并修复每个训练日中的每个动作ID
        for workout in plan_schema.workouts:
            valid_exercises = []

            for i, exercise in enumerate(workout.get("exercises", [])):
                # 获取当前exercise_id
                ex_id = exercise.get("exercise_id")
                ex_body_part_id = exercise.get("body_part_id", None)

                # 规范化body_part_id
                if ex_body_part_id is not None:
                    normalized_body_part_id = normalize_body_part_id(ex_body_part_id)
                    if normalized_body_part_id is not None:
                        exercise["body_part_id"] = normalized_body_part_id

                # 检查ID是否有效
                if ex_id in candidate_map:
                    # ID在候选列表中，直接使用
                    # 如果候选列表中有更详细的部位ID信息，使用候选列表中的信息
                    if "body_part_id" in candidate_map[ex_id]:
                        exercise["body_part_id"] = candidate_map[ex_id]["body_part_id"]
                    valid_exercises.append(exercise)
                elif ex_id in valid_ids:
                    # ID在数据库中有效，但不在候选列表中
                    valid_exercises.append(exercise)
                else:
                    # ID无效，尝试通过名称匹配
                    ex_name = exercise.get("exercise_name", "").lower()
                    if ex_name and ex_name in name_to_id:
                        # 找到匹配的名称，使用对应的ID
                        exercise["exercise_id"] = name_to_id[ex_name]
                        # 同时更新body_part_id，使用候选动作的信息
                        new_id = name_to_id[ex_name]
                        if new_id in candidate_map and "body_part_id" in candidate_map[new_id]:
                            exercise["body_part_id"] = candidate_map[new_id]["body_part_id"]
                        valid_exercises.append(exercise)
                    elif candidate_exercises and i < len(candidate_exercises):
                        # 使用候选列表中的动作替代
                        replacement = candidate_exercises[i % len(candidate_exercises)]
                        exercise["exercise_id"] = replacement["id"]
                        exercise["exercise_name"] = replacement["name"]
                        # 同时更新body_part_id，确保它与替代动作一致
                        if "body_part_id" in replacement:
                            exercise["body_part_id"] = replacement["body_part_id"]
                        valid_exercises.append(exercise)

            # 更新训练日中的动作列表
            workout["exercises"] = valid_exercises

    async def _generate_structured_daily_workout(self, prompt: str, context: Dict[str, Any] = None) -> DailyWorkoutSchema:
        """生成结构化单日训练计划

        Args:
            prompt: 完整提示文本
            context: 上下文信息，包含用户信息和计划需求

        Returns:
            结构化单日训练计划对象
        """
        # 创建输出解析器
        parser = PydanticOutputParser(pydantic_object=DailyWorkoutSchema)

        # 预筛选有效动作
        candidate_exercises = []
        exercise_info = ""
        target_body_parts = context.get("target_body_parts", []) if context else []
        available_equipment = context.get("available_equipment", []) if context else []
        user_id = context.get("user_id") if context else None

        # 记录目标部位和用户信息
        logger.info(f"开始生成单日训练计划: user_id={user_id}, 目标部位IDs={target_body_parts}, 可用器材={available_equipment}")

        # 检查用户ID是否有效
        if not user_id:
            logger.warning("未提供用户ID，将使用默认参数")

        body_part_dict = {bp["id"]: bp["name"] for bp in BODY_PART_CATEGORIES}
        equipment_dict = {eq["id"]: eq["name"] for eq in EQUIPMENT_CATEGORIES}
        body_part_names = [body_part_dict.get(bp_id, f"未知部位({bp_id})") for bp_id in target_body_parts]
        equipment_names = [equipment_dict.get(eq_id, f"未知器材({eq_id})") for eq_id in available_equipment]

        logger.info(f"目标训练部位名称: {body_part_names}, 可用器材: {equipment_names}")

        # 如果未指定目标部位，使用默认部位
        if not target_body_parts:
            logger.warning("未指定目标部位，将使用默认部位(胸部)")
            default_body_part_id = 1  # 假设ID=1为胸部
            target_body_parts = [default_body_part_id]
            body_part_names = [body_part_dict.get(default_body_part_id, f"默认部位({default_body_part_id})")]
            logger.info(f"设置默认目标部位: ID={default_body_part_id}, 名称={body_part_names[0]}")
            # 更新上下文中的目标部位
            if context is not None:
                context["target_body_parts"] = target_body_parts

        if target_body_parts:
            from app.services.conversation.exercise_helper import get_candidate_exercises, personalized_filtering
            from app.services.conversation.profile_helper import _get_user_profile, _get_recommended_difficulty

            # 构建用户资料
            user = crud.user.get(self.db, id=user_id) if user_id else None
            if user:
                logger.info(f"用户信息: ID={user.id}, 性别={user.gender}, 年龄={user.age}, 健身目标={user.fitness_goal}")
                user_profile = _get_user_profile(None, user)  # 第一个参数在函数内部不使用
                difficulty_range = _get_recommended_difficulty(None, user_profile)
                logger.info(f"用户健身资料: 经验级别={user_profile.get('experience_level')}, 活动水平={user_profile.get('activity_level')}, 难度范围={difficulty_range}")

                # 获取用户健身目标的名称
                goal_name = self._get_fitness_goal_name(user.fitness_goal) if user.fitness_goal else "未设置"
                logger.info(f"用户健身目标: ID={user.fitness_goal}, 名称={goal_name}")

                # 为每个目标部位获取候选动作
                all_candidates = []
                for body_part_id in target_body_parts:
                    # 根据ID获取身体部位名称
                    body_part_name = self._get_body_part_name(body_part_id)
                    if not body_part_name:
                        logger.error(f"无法找到部位ID {body_part_id} 对应的名称，可能是无效的部位ID")
                        body_part_name = f"未知部位({body_part_id})"

                    # 获取候选动作
                    logger.info(f"为部位 {body_part_name} (ID={body_part_id}) 获取候选动作")
                    try:
                        # 确定当前环境对应的scenario值
                        current_scenario = None
                        if available_equipment:
                            # 基于可用设备判断场景
                            # 如果只有自重器材(ID=2)，或者是自重+哑铃(ID=1,2)，视为居家场景
                            if set(available_equipment).issubset({4, 2}):
                                current_scenario = "home"
                                logger.info(f"基于可用器材 {available_equipment} 判断为'居家'场景")
                            # 如果包含更多器材，视为健身房场景
                            elif len(available_equipment) > 2 or any(eq_id > 2 for eq_id in available_equipment):
                                current_scenario = "gym"
                                logger.info(f"基于可用器材 {available_equipment} 判断为'健身房'场景")

                        # 记录可用器材和场景
                        logger.info(f"获取候选动作的参数: 部位={body_part_name}, 场景={current_scenario}, 可用器材={available_equipment}")

                        candidates = await get_candidate_exercises(
                            self.sql_tool_service,  # 传递SQL工具服务而不是None
                            body_part=body_part_name,
                            scenario=current_scenario,  # 传入判断的场景参数
                            equipment_ids=available_equipment if available_equipment else None,  # 传入可用器材
                            difficulty_range=difficulty_range,
                            limit=10  # 每个部位10个候选
                        )
                        logger.info(f"部位 {body_part_name} 获取到 {len(candidates)} 个候选动作")

                        if len(candidates) == 0:
                            logger.warning(f"警告：部位 {body_part_name} 没有找到任何候选动作")
                            # 尝试调试body_part_name可能的问题
                            logger.warning(f"调试信息 - 部位名称: {body_part_name}, 类型: {type(body_part_name)}")

                            # 尝试使用部位ID作为后备方案
                            logger.info(f"尝试直接使用部位ID {body_part_id} 获取候选动作")
                            candidates = await get_candidate_exercises(
                                self.sql_tool_service,  # 传递SQL工具服务而不是None
                                body_part=body_part_id,  # 直接使用ID
                                scenario=current_scenario,  # 传入判断的场景参数
                                equipment_ids=available_equipment if available_equipment else None,  # 传入可用器材
                                difficulty_range=difficulty_range,
                                limit=10
                            )
                            logger.info(f"使用部位ID直接查询获取到 {len(candidates)} 个候选动作")

                        # 记录部分候选动作详情
                        if candidates:
                            sample_candidates = candidates[:3]  # 只记录前3个，避免日志过长
                            sample_details = []
                            for ex in sample_candidates:
                                ex_details = {
                                    "id": ex.get("id"),
                                    "name": ex.get("name"),
                                    "body_parts": ex.get("body_parts", []),
                                    "difficulty": ex.get("level", 2)
                                }
                                sample_details.append(ex_details)
                                logger.info(f"候选动作示例: ID={ex.get('id')}, 名称={ex.get('name')}, 部位={ex.get('body_parts')}")
                            logger.info(f"部位 {body_part_name} 候选动作示例: {sample_details}")

                        all_candidates.extend(candidates)
                    except Exception as e:
                        logger.error(f"获取部位 {body_part_name} 的候选动作时出错: {str(e)}", exc_info=True)

                logger.info(f"所有部位总共获取到 {len(all_candidates)} 个候选动作")

                # 去除重复项
                seen_ids = set()
                unique_candidates = []
                for ex in all_candidates:
                    if ex["id"] not in seen_ids:
                        seen_ids.add(ex["id"])
                        unique_candidates.append(ex)

                logger.info(f"去重后剩余 {len(unique_candidates)} 个候选动作")

                # 个性化筛选
                training_params = {}
                if user.fitness_goal:
                    goal_name = self._get_fitness_goal_name(user.fitness_goal)
                    from app.services.conversation.parameter_extractor import DEFAULT_TRAINING_PARAMS
                    training_params = DEFAULT_TRAINING_PARAMS.get(goal_name, {})
                    logger.info(f"使用健身目标 '{goal_name}' 的训练参数: {training_params}")

                # 筛选出最合适的动作
                logger.info(f"开始个性化筛选")
                try:
                    filtered_exercises = await personalized_filtering(
                        self.sql_tool_service,  # 传递SQL工具服务而不是None
                        unique_candidates,
                        user_profile,
                        training_params,
                        limit=min(15, len(unique_candidates))  # 最多15个候选，确保有足够的选择
                    )

                    logger.info(f"个性化筛选后剩余 {len(filtered_exercises)} 个候选动作")

                    # 记录筛选后的候选动作详情
                    if filtered_exercises:
                        logger.info("筛选后的候选动作：")
                        filtered_details = []
                        for ex in filtered_exercises[:5]:  # 只记录前5个
                            ex_details = {
                                "id": ex.get("id"),
                                "name": ex.get("name"),
                                "body_parts": ex.get("body_parts", []),
                                "body_part_id": ex.get("body_part_id"),
                                "difficulty": ex.get("level", 2)
                            }
                            filtered_details.append(ex_details)
                            logger.info(f"- ID={ex.get('id')}, 名称={ex.get('name')}, 部位={ex.get('body_parts')}, 部位ID={ex.get('body_part_id')}")
                        logger.info(f"筛选后的动作示例: {filtered_details}")

                        # 检查筛选后的动作是否与目标部位匹配
                        matching_count = 0
                        non_matching_count = 0

                        for ex in filtered_exercises:
                            ex_body_part_id = ex.get("body_part_id")

                            # 改进的匹配逻辑 - 处理不同类型的body_part_id
                            matches = False
                            if ex_body_part_id is not None:
                                if isinstance(ex_body_part_id, list):
                                    # 如果是列表，检查是否有任意一个元素在目标部位列表中
                                    # 使用集合交集操作提高效率
                                    common_parts = set(ex_body_part_id) & set(target_body_parts)
                                    matches = len(common_parts) > 0
                                    if matches:
                                        logger.info(f"动作ID={ex.get('id')}, 名称={ex.get('name')} 的部位ID列表 {ex_body_part_id} 与目标部位 {target_body_parts} 有交集: {common_parts}")
                                    else:
                                        logger.warning(f"动作ID={ex.get('id')}, 名称={ex.get('name')} 的部位ID列表 {ex_body_part_id} 与目标部位 {target_body_parts} 无交集")
                                elif isinstance(ex_body_part_id, (int, str)):
                                    # 处理单个ID的情况，直接在列表中查找
                                    # 尝试将字符串转为整数进行比较
                                    if isinstance(ex_body_part_id, str):
                                        try:
                                            ex_body_part_id = int(ex_body_part_id)
                                        except ValueError:
                                            logger.warning(f"部位ID '{ex_body_part_id}'无法转换为整数")

                                    matches = ex_body_part_id in target_body_parts
                                    if matches:
                                        logger.info(f"动作ID={ex.get('id')}, 名称={ex.get('name')} 的部位ID {ex_body_part_id} 在目标部位列表 {target_body_parts} 中")
                                    else:
                                        logger.warning(f"动作ID={ex.get('id')}, 名称={ex.get('name')} 的部位ID {ex_body_part_id} 不在目标部位列表 {target_body_parts} 中")
                                else:
                                    # 处理其他意外类型
                                    logger.warning(f"动作ID={ex.get('id')}, 名称={ex.get('name')} 的部位ID类型 {type(ex_body_part_id)} 不可识别")
                            else:
                                logger.warning(f"动作ID={ex.get('id')}, 名称={ex.get('name')} 没有部位ID信息")

                            if matches:
                                matching_count += 1
                            else:
                                non_matching_count += 1

                        logger.info(f"筛选后的动作中有 {matching_count} 个匹配目标部位，{non_matching_count} 个不匹配")

                        # 如果匹配数量太少，记录详细信息以供调试
                        if matching_count < 3 and len(filtered_exercises) > 0:
                            logger.warning("匹配目标部位的动作数量过少，这可能导致生成的训练计划不符合用户期望")
                            # 验证body_part_id是否正确设置
                            for ex in filtered_exercises[:10]:
                                ex_id = ex.get("id")
                                ex_name = ex.get("name")
                                ex_body_part_id = ex.get("body_part_id")
                                ex_body_parts = ex.get("body_parts", [])
                                logger.warning(f"动作ID={ex_id}, 名称={ex_name}, 部位ID={ex_body_part_id}, 部位名称={ex_body_parts}")

                                # 如果部位ID不匹配但部位名称包含目标部位名称，记录这种不一致
                                for bp_id in target_body_parts:
                                    bp_name = body_part_dict.get(bp_id, "")
                                    if bp_name and any(bp_name.lower() in bp.lower() for bp in ex_body_parts):
                                        # 修改部位ID匹配逻辑，处理列表类型
                                        is_match = False
                                        if isinstance(ex_body_part_id, list):
                                            is_match = bp_id in ex_body_part_id
                                        else:
                                            is_match = ex_body_part_id == bp_id

                                        if not is_match:
                                            logger.warning(f"动作 {ex_name} 的部位名称包含 '{bp_name}'，但部位ID={ex_body_part_id}，不包含目标部位ID={bp_id}")

                                            # 尝试修复：将目标部位ID添加到动作的部位ID中
                                            try:
                                                if isinstance(ex_body_part_id, list):
                                                    # 如果已经是列表，添加新部位ID
                                                    if bp_id not in ex_body_part_id:
                                                        ex["body_part_id"] = ex_body_part_id + [bp_id]
                                                        logger.info(f"修复: 将部位ID {bp_id} 添加到动作 {ex_name} 的部位ID列表中")
                                                else:
                                                    # 如果不是列表，创建包含原ID和新ID的列表
                                                    ex["body_part_id"] = [ex_body_part_id, bp_id] if ex_body_part_id else [bp_id]
                                                    logger.info(f"修复: 将动作 {ex_name} 的部位ID从 {ex_body_part_id} 更新为 {ex['body_part_id']}")
                                            except Exception as e:
                                                logger.error(f"修复动作 {ex_name} 的部位ID时出错: {str(e)}")
                    else:
                        logger.warning("筛选后没有候选动作，请检查筛选条件和动作数据")

                    candidate_exercises = filtered_exercises
                except Exception as e:
                    logger.error(f"个性化筛选过程中出错: {str(e)}", exc_info=True)

                # 如果没有找到任何候选动作，记录一个错误
                if not candidate_exercises:
                    logger.error(f"没有找到任何针对部位 {body_part_names} 的有效训练动作")
                    error_msg = f"未能找到任何针对部位 {', '.join(body_part_names)} 的训练动作"
                    raise ValueError(error_msg)

                # 构建动作信息提示
                exercise_info = "可选的训练动作（LLM必须从这些动作中选择）:\n"
                for i, ex in enumerate(candidate_exercises, 1):
                    muscles_str = ", ".join(ex.get("muscles", []))
                    equip_str = ", ".join(ex.get("equipment", []))
                    body_parts_str = ", ".join(ex.get("body_parts", []))
                    exercise_info += (
                        f"{i}. ID: {ex['id']}, 名称: {ex['name']}\n"
                        f"   部位: {body_parts_str}\n"
                        f"   部位ID: {ex.get('body_part_id')}\n"
                        f"   肌肉: {muscles_str}\n"
                        f"   器材: {equip_str}\n"
                        f"   难度: {ex.get('level', 2)}\n\n"
                    )

                logger.info(f"构建了包含 {len(candidate_exercises)} 个训练动作的提示信息")
            else:
                logger.warning(f"无法获取用户ID={user_id}的信息，将使用空的候选动作列表")

        # 创建增强的系统提示，强调生成JSON格式和选择有效动作
        format_instructions = parser.get_format_instructions()
        enhanced_system_prompt = (
            f"你是一位专业的健身教练AI助手。请为用户生成一个针对以下身体部位的单日训练计划: {', '.join(body_part_names)}。\n\n"
            f"请严格按照以下JSON格式返回训练计划:\n{format_instructions}\n\n"
            "重要说明:\n"
            "1. 你必须从提供的训练动作列表中选择3-5个动作，通过指定正确的exercise_id。\n"
            "2. 不要创建不存在于列表中的动作。\n"
            "3. 确保所有选择的动作与目标训练部位相关。\n"
            "4. 响应必须是有效的JSON格式，不要包含额外的解释或其他文本。\n"
            "5. 所有字段必须严格按照格式说明提供。\n"
            "6. rest_seconds 字段必须是整数值（如 60、90），不要包含单位（如\"秒\"）。\n"
        )

        # 更新用户提示，添加候选动作信息
        if exercise_info:
            user_prompt = f"{prompt}\n\n{exercise_info}\n重要：请只使用上方列表中的动作，通过指定正确的exercise_id选择动作。不要生成不存在的动作ID。"
        else:
            user_prompt = prompt
            logger.warning("没有候选动作信息提供给LLM，这可能导致生成无效的训练计划")

        logger.info(f"准备向LLM发送系统和用户提示，请求生成针对{', '.join(body_part_names)}的训练计划")

        # 记录提示长度
        logger.debug(f"系统提示长度: {len(enhanced_system_prompt)}")
        logger.debug(f"用户提示长度: {len(user_prompt)}")

        try:
            # 调用LLM
            # 从model_config.py中获取训练计划生成模型
            from app.core.model_config import MODEL_MAPPING
            training_plan_model = MODEL_MAPPING["training_plan"]["default"]
            logger.info(f"使用训练计划生成模型: {training_plan_model}")

            response = await self.llm_proxy_service.aget_chat_response(
                messages=[
                    {"role": "system", "content": enhanced_system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                model=training_plan_model,
                temperature=0.2
            )

            logger.info(f"LLM返回了训练计划响应，长度: {len(response)}")

            # 预处理响应，尝试提取有效的JSON部分
            processed_response = self._extract_json_from_response(response)
            logger.debug(f"处理后的JSON响应: {processed_response}")

            # 解析结构化输出
            try:
                result = parser.parse(processed_response)
                logger.info(f"成功解析训练计划JSON: {result}")
            except Exception as e:
                logger.error(f"解析训练计划JSON失败: {str(e)}")
                logger.error(f"无效的JSON响应: {processed_response}")
                raise ValueError(f"生成的训练计划不是有效的JSON: {str(e)}")

            # 验证并修复训练计划中的动作ID
            self._validate_and_fix_exercise_ids(result, candidate_exercises)

            # 记录最终生成的训练计划
            exercise_count = len(result.exercises)
            logger.info(f"最终训练计划包含 {exercise_count} 个训练动作")

            # 详细记录每个训练动作
            for i, ex in enumerate(result.exercises):
                try:
                    # 使用字典访问方式而非对象属性访问
                    if isinstance(ex, dict):
                        ex_id = ex["exercise_id"]
                    else:
                        # 如果是对象，则使用属性访问
                        ex_id = ex.exercise_id

                    # 查找动作名称
                    ex_name = next((c_ex.get("name") for c_ex in candidate_exercises if c_ex.get("id") == ex_id), f"未知动作ID:{ex_id}")
                    # 查找动作对应的部位ID
                    ex_bp_id = next((c_ex.get("body_part_id") for c_ex in candidate_exercises if c_ex.get("id") == ex_id), None)

                    # 获取部位名称 - 处理 ex_bp_id 可能是列表的情况
                    if isinstance(ex_bp_id, list):
                        # 如果是列表，获取第一个部位的名称
                        first_bp_id = ex_bp_id[0] if ex_bp_id else None
                        ex_bp_name = body_part_dict.get(first_bp_id, f"未知部位ID:{first_bp_id}") if first_bp_id else "未知部位"
                        # 记录所有部位
                        all_bp_names = []
                        for bp_id in ex_bp_id:
                            bp_name = body_part_dict.get(bp_id, f"未知部位ID:{bp_id}")
                            all_bp_names.append(bp_name)
                        if all_bp_names:
                            ex_bp_name = ", ".join(all_bp_names)
                    else:
                        # 如果不是列表，直接获取部位名称
                        ex_bp_name = body_part_dict.get(ex_bp_id, f"未知部位ID:{ex_bp_id}") if ex_bp_id else "未知部位"

                    # 同样安全地获取其他字段
                    if isinstance(ex, dict):
                        sets = ex.get("sets", 0)
                        reps = ex.get("reps", 0)
                        rest_time = ex.get("rest_time", 0)
                    else:
                        sets = getattr(ex, "sets", 0)
                        reps = getattr(ex, "reps", 0)
                        rest_time = getattr(ex, "rest_time", 0)

                    logger.info(f"训练动作 {i+1}: ID={ex_id}, 名称={ex_name}, 部位={ex_bp_name}, 组数={sets}, 次数={reps}, 休息={rest_time}秒")

                    # 检查训练动作是否属于目标部位
                    if ex_bp_id:
                        # 处理 ex_bp_id 可能是列表的情况
                        if isinstance(ex_bp_id, list):
                            # 检查是否有交集
                            common_parts = set(ex_bp_id) & set(target_body_parts)
                            if not common_parts:
                                logger.warning(f"训练动作 {ex_name} (ID={ex_id}) 属于部位 {ex_bp_name} (ID={ex_bp_id})，不在目标部位列表中 {target_body_parts}")
                        else:
                            # 单个值的情况
                            if ex_bp_id not in target_body_parts:
                                logger.warning(f"训练动作 {ex_name} (ID={ex_id}) 属于部位 {ex_bp_name} (ID={ex_bp_id})，不在目标部位列表中 {target_body_parts}")
                except Exception as ex_error:
                    logger.error(f"处理训练动作 {i+1} 时出错: {str(ex_error)}")
                    continue

            return result
        except Exception as e:
            logger.error(f"生成训练计划失败: {str(e)}", exc_info=True)
            raise ValueError(f"生成训练计划失败: {str(e)}")

    @ErrorHelper.handle_exceptions(error_message="验证并修复训练动作ID失败", log_error=True)
    def _validate_and_fix_exercise_ids(self, workout_schema: DailyWorkoutSchema, candidate_exercises: List[Dict[str, Any]]) -> None:
        """验证并修复训练计划中的exercise_id

        Args:
            workout_schema: 训练计划结构
            candidate_exercises: 候选动作列表
        """
        # 创建候选动作ID到完整信息的映射
        candidate_map = {ex["id"]: ex for ex in candidate_exercises} if candidate_exercises else {}

        # 记录候选动作
        logger.info(f"验证ID: 候选动作数量={len(candidate_map)}")
        if candidate_map:
            logger.info(f"候选动作ID列表: {list(candidate_map.keys())[:10]}")  # 只记录前10个

        # 创建名称到ID的映射（用于名称匹配）
        name_to_id = {}
        for ex in candidate_exercises:
            name = ex.get("name", "").lower()
            if name:
                name_to_id[name] = ex["id"]

        # 获取数据库中所有有效的动作ID
        valid_ids = set()
        try:
            logger.info("从数据库获取所有有效动作ID")
            exercises = crud.crud_exercise.get_multi(self.db, limit=1000)
            valid_ids = {ex.id for ex in exercises}
            logger.info(f"数据库中有效动作ID数量: {len(valid_ids)}")
        except Exception as e:
            logger.error(f"获取有效动作ID失败: {str(e)}")

        # 验证并修复每个动作的ID
        valid_exercises = []
        for i, exercise in enumerate(workout_schema.exercises):
            # 获取当前exercise_id
            ex_id = exercise.get("exercise_id")
            ex_name = exercise.get("exercise_name", "")
            ex_body_part_id = exercise.get("body_part_id", None)

            # 规范化body_part_id
            if ex_body_part_id is not None:
                normalized_body_part_id = normalize_body_part_id(ex_body_part_id)
                if normalized_body_part_id is not None:
                    exercise["body_part_id"] = normalized_body_part_id

            # 检查ID是否有效
            if ex_id in candidate_map:
                # ID在候选列表中，直接使用
                # 如果候选列表中有更详细的部位ID信息，使用候选列表中的信息
                if "body_part_id" in candidate_map[ex_id]:
                    exercise["body_part_id"] = candidate_map[ex_id]["body_part_id"]
                valid_exercises.append(exercise)
            elif ex_id in valid_ids:
                # ID在数据库中有效，但不在候选列表中
                valid_exercises.append(exercise)
            else:
                # ID无效，尝试通过名称匹配
                ex_name_lower = ex_name.lower()
                if ex_name_lower and ex_name_lower in name_to_id:
                    # 找到匹配的名称，使用对应的ID
                    new_id = name_to_id[ex_name_lower]
                    exercise["exercise_id"] = new_id

                    # 同时更新body_part_id，使用候选动作的信息
                    if new_id in candidate_map and "body_part_id" in candidate_map[new_id]:
                        exercise["body_part_id"] = candidate_map[new_id]["body_part_id"]

                    valid_exercises.append(exercise)
                elif candidate_exercises and i < len(candidate_exercises):
                    # 使用候选列表中的动作替代
                    replacement = candidate_exercises[i % len(candidate_exercises)]
                    exercise["exercise_id"] = replacement["id"]
                    exercise["exercise_name"] = replacement["name"]

                    # 同时更新body_part_id，确保它与替代动作一致
                    if "body_part_id" in replacement:
                        exercise["body_part_id"] = replacement["body_part_id"]

                    valid_exercises.append(exercise)

        # 更新训练计划中的动作列表
        logger.info(f"验证和修复后的有效动作数量: {len(valid_exercises)}")
        workout_schema.exercises = valid_exercises

    def _get_body_part_name(self, body_part_id: int) -> Optional[str]:
        """根据ID获取身体部位名称

        Args:
            body_part_id: 身体部位ID

        Returns:
            身体部位名称，如果未找到则返回None
        """
        return get_body_part_name(body_part_id)

    def _get_fitness_goal_name(self, fitness_goal: Optional[int]) -> str:
        """根据ID获取健身目标名称

        Args:
            fitness_goal: 健身目标ID

        Returns:
            健身目标名称，如果未找到则返回"未设置"
        """
        goal_name = get_fitness_goal_name(fitness_goal)
        return goal_name if goal_name else "未设置"

    def _get_experience_level_name(self, experience_level: Optional[int]) -> str:
        """获取经验水平名称

        Args:
            experience_level: 经验水平ID

        Returns:
            经验水平名称
        """
        levels = {
            1: "初学者",
            2: "中级",
            3: "高级"
        }
        return levels.get(experience_level, "未设置") if experience_level else "未设置"

    def _get_activity_level_name(self, activity_level: Optional[int]) -> str:
        """获取活动水平名称

        Args:
            activity_level: 活动水平ID

        Returns:
            活动水平名称
        """
        levels = {
            1: "久坐不动",
            2: "轻度活动",
            3: "中度活动",
            4: "高度活动",
            5: "极高活动"
        }
        return levels.get(activity_level, "未设置") if activity_level else "未设置"

    @ErrorHelper.handle_exceptions(error_message="获取增强的训练计划数据失败", log_error=True)
    def get_enhanced_plan(self, plan_id: int) -> Dict[str, Any]:
        """获取增强的训练计划数据，包含完整的动作信息

        Args:
            plan_id: 训练计划ID

        Returns:
            包含完整动作信息的训练计划数据
        """
        logger.info(f"获取增强的训练计划数据: plan_id={plan_id}")

        # 获取训练计划基本信息
        plan = crud.crud_training_plan.get(self.db, id=plan_id)
        if not plan:
            logger.error(f"训练计划不存在: plan_id={plan_id}")
            raise ValueError(f"训练计划不存在: plan_id={plan_id}")

        # 检查是否是单日训练计划
        workouts = crud.crud_workout.get_by_plan(self.db, training_plan_id=plan_id)

        if len(workouts) == 1 and workouts[0].day_number == 1:
            # 单日训练计划
            workout = workouts[0]
            logger.info(f"获取单日训练计划: plan_id={plan_id}, workout_id={workout.id}")

            # 使用转换器丰富响应
            return TrainingPlanTransformer.enrich_workout_response(workout.id, self.db)
        else:
            # 完整训练计划
            logger.info(f"获取完整训练计划: plan_id={plan_id}, workouts_count={len(workouts)}")

            # 构建增强的响应
            enhanced_response = {
                "id": plan.id,
                "plan_name": plan.plan_name,
                "description": plan.description,
                "duration_weeks": plan.duration_weeks,
                "workouts": []
            }

            # 添加训练日详情
            for workout in workouts:
                try:
                    # 使用转换器丰富每个训练日
                    workout_data = TrainingPlanTransformer.enrich_workout_response(workout.id, self.db)
                    enhanced_response["workouts"].append(workout_data)
                except Exception as e:
                    logger.error(f"构建训练日详情时出错: {str(e)}")
                    # 继续处理下一条记录

            # 计算总动作数
            total_exercises = sum(len(workout.get("exercises", [])) for workout in enhanced_response["workouts"])
            logger.info(f"成功构建完整训练计划响应，包含 {len(enhanced_response['workouts'])} 个训练日，共 {total_exercises} 个动作")
            return enhanced_response

    def _extract_json_from_response(self, text: str) -> str:
        """从响应中提取JSON部分，尝试修复常见的JSON格式问题

        Args:
            text: 原始响应文本

        Returns:
            处理后的JSON文本
        """
        # 使用工具函数提取JSON
        return extract_json_from_response(text)

    def generate_cycle_training_plan(self,
                                     user_info: Dict[str, Any],
                                     body_part: Optional[str] = None,
                                     training_scene: Optional[str] = None,
                                     days: int = 7) -> Dict[str, Any]:
        """生成周期训练计划

        Args:
            user_info: 用户信息
            body_part: 训练部位
            training_scene: 训练场景
            days: 天数

        Returns:
            训练计划数据
        """
        logger.info(f"开始生成周期训练计划: 部位={body_part}, 场景={training_scene}, 天数={days}")

        # 构建提示
        system_prompt = """你是一名专业的健身教练，擅长根据用户的特点和需求制定个性化训练计划。
请根据用户的身体特征、健身水平和目标，为用户制定一个完整的周训练计划。
训练计划应包含每天的训练内容，合理安排训练部位和强度，设计适当的休息日。
每个训练日应包含适当数量的动作，每个动作包含组数和次数建议。
请考虑用户的训练场景和可用器材。
回答应简明专业，提供足够的细节指导用户完成整周训练。"""

        # 构建用户提示
        user_details = self._format_user_info(user_info)

        if body_part and body_part != "全身":
            training_request = f"请为我制定一个为期{days}天的训练计划，重点关注{body_part}部位的训练。训练场景是{training_scene or '健身房'}。"
        else:
            training_request = f"请为我制定一个为期{days}天的全身训练计划。训练场景是{training_scene or '健身房'}。"

        user_prompt = f"{user_details}\n\n{training_request}\n\n请以JSON格式返回结果，包含以下字段：\n1. plan_name: 计划名称\n2. description: 计划描述\n3. schedule: 训练安排，包含每天的训练部位\n4. workouts: 按天组织的训练内容数组，每天包含day_number, focus, exercises字段，其中exercises是动作列表，每个动作包含name, sets, reps, rest, description字段"

        # 生成计划
        try:
            response = self.llm_service.chat_completion(
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                model_type=settings.TRAINING_PLAN_MODEL,
                temperature=0.7,
                max_tokens=4000
            )

            content = response.get("content", "")

            # 尝试提取JSON
            plan_data = self._extract_json(content)
            if not plan_data:
                logger.warning("无法从LLM响应中提取JSON数据")
                plan_data = {
                    "plan_name": f"{days}天{body_part or '全身'}训练计划",
                    "description": "无法生成详细计划，请稍后再试",
                    "schedule": {"周一": "休息", "周二": "休息", "周三": "休息", "周四": "休息", "周五": "休息", "周六": "休息", "周日": "休息"},
                    "workouts": []
                }

            # 创建计划ID
            plan_id = f"cycle_{uuid.uuid4().hex[:8]}"

            # 补充计划信息
            plan_data["plan_id"] = plan_id
            plan_data["created_at"] = datetime.now().isoformat()
            plan_data["training_scene"] = training_scene or "健身房"
            plan_data["plan_type"] = "weekly"
            plan_data["days"] = days

            # 存储计划到数据库
            self._save_plan_to_db(
                plan_id=plan_id,
                user_id=user_info.get("id"),
                plan_data=plan_data,
                plan_type="weekly"
            )

            return plan_data

        except Exception as e:
            logger.error(f"生成周期训练计划时出错: {str(e)}")
            return {
                "plan_id": f"error_{uuid.uuid4().hex[:8]}",
                "plan_name": "生成计划失败",
                "description": f"生成周期计划时出错: {str(e)}",
                "error": True
            }

    def get_plan(self, plan_id: str) -> Dict[str, Any]:
        """获取计划数据

        Args:
            plan_id: 计划ID

        Returns:
            计划数据
        """
        try:
            # 查询数据库中的计划
            plan = self.db.query(TrainingPlan).filter(TrainingPlan.external_id == plan_id).first()
            if not plan:
                logger.warning(f"未找到计划: {plan_id}")
                return {}

            # 解析计划数据
            plan_data = {}
            if plan.data:
                try:
                    if isinstance(plan.data, str):
                        plan_data = json.loads(plan.data)
                    elif isinstance(plan.data, dict):
                        plan_data = plan.data
                except Exception as e:
                    logger.error(f"解析计划数据错误: {str(e)}")

            return plan_data

        except Exception as e:
            logger.error(f"获取计划数据错误: {str(e)}")
            return {}

    def _format_user_info(self, user_info: Dict[str, Any]) -> str:
        """格式化用户信息

        Args:
            user_info: 用户信息字典

        Returns:
            格式化后的字符串
        """
        if not user_info:
            return "用户信息: 没有可用的用户信息。"

        lines = ["用户信息:"]
        mapping = {
            "gender": "性别",
            "age": "年龄",
            "height": "身高(cm)",
            "weight": "体重(kg)",
            "fitness_level": "健身水平",
            "fitness_goal": "健身目标",
            "activity_level": "日常活动水平",
            "health_condition": "健康状况"
        }

        for key, label in mapping.items():
            if key in user_info and user_info[key]:
                lines.append(f"- {label}: {user_info[key]}")

        return "\n".join(lines)

    def _extract_json(self, text: str) -> Dict[str, Any]:
        """从文本中提取JSON数据

        Args:
            text: 包含JSON的文本

        Returns:
            提取的JSON数据
        """
        try:
            # 尝试直接解析整个文本
            return json.loads(text)
        except json.JSONDecodeError:
            # 尝试查找JSON块
            start_markers = ["{", "["]
            end_markers = ["}", "]"]

            for start_marker in start_markers:
                start_idx = text.find(start_marker)
                if start_idx != -1:
                    # 根据开始标记找对应的结束标记
                    end_marker = end_markers[start_markers.index(start_marker)]
                    end_idx = text.rfind(end_marker)

                    if end_idx > start_idx:
                        json_text = text[start_idx:end_idx+1]
                        try:
                            return json.loads(json_text)
                        except json.JSONDecodeError:
                            continue

            # 没有找到有效的JSON
            return {}

    def _save_plan_to_db(self, plan_id: str, user_id: Optional[int], plan_data: Dict[str, Any], plan_type: str) -> bool:
        """保存计划到数据库

        Args:
            plan_id: 计划ID
            user_id: 用户ID
            plan_data: 计划数据
            plan_type: 计划类型

        Returns:
            是否保存成功
        """
        try:
            # 准备计划数据
            plan = TrainingPlan(
                external_id=plan_id,
                user_id=user_id,
                name=plan_data.get("plan_name", f"{plan_type}训练计划"),
                description=plan_data.get("description", ""),
                type=plan_type,
                status=TrainingPlanStatus.ACTIVE,
                data=plan_data
            )

            # 保存到数据库
            self.db.add(plan)
            self.db.commit()
            self.db.refresh(plan)

            logger.info(f"训练计划已保存到数据库: {plan_id}")
            return True

        except Exception as e:
            logger.error(f"保存计划到数据库时出错: {str(e)}")
            self.db.rollback()
            return False

    @ErrorHelper.handle_exceptions(error_message="保存单个训练为workout失败", log_error=True)
    async def save_workout_only(
        self,
        user_id: int,
        workout_data: Dict[str, Any],
        target_body_parts: Optional[List[int]] = None,
        training_scenario: Optional[str] = None
    ) -> Dict[str, Any]:
        """保存单个训练为workout，不创建组记录

        Args:
            user_id: 用户ID
            workout_data: 训练数据
            target_body_parts: 目标训练部位ID列表
            training_scenario: 训练场景

        Returns:
            保存的workout数据
        """
        logger.info(f"保存单个训练为workout: user_id={user_id}, 目标部位={target_body_parts}, 场景={training_scenario}")

        try:
            # 获取用户信息
            user = crud.user.get(self.db, id=user_id)
            if not user:
                logger.error(f"用户 ID {user_id} 不存在")
                raise ValueError(f"用户 ID {user_id} 不存在")

            # 确定训练场景
            if not training_scenario:
                training_scenario = determine_training_scenario(workout_data.get("available_equipment", []))

            # 保存workout到数据库，但不创建组记录
            result = TrainingPlanTransformer.transform_daily_workout_to_db(
                user_id=user_id,
                workout_data=workout_data,
                db=self.db,
                fitness_goal=user.fitness_goal,
                experience_level=user.experience_level,
                target_body_parts=target_body_parts,
                training_scenario=training_scenario,
                create_group=False  # 不创建组记录
            )

            logger.info(f"成功保存单个训练为workout: ID={result.get('id')}, 名称={result.get('name')}")
            return result

        except Exception as e:
            logger.error(f"保存单个训练为workout失败: {str(e)}", exc_info=True)
            raise