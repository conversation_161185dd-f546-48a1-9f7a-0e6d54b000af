import os
import logging
import base64
from typing import Dict, Any, Optional
from pathlib import Path
from datetime import date, datetime
import time
import random
from io import BytesIO
from PIL import Image
import uuid
import re
import io
import hashlib

from app.core.config import settings
from app.utils.secure_path import generate_user_secure_path

logger = logging.getLogger(__name__)

class StorageService:
    """存储服务"""
    
    BASE_STORAGE_PATH = "/data/users"  # 所有用户图片的存储根目录
    BASE_URL = "/food/images"  # 图片访问的基础URL，与FastAPI中的挂载路径保持一致
    
    @staticmethod
    async def upload_image(
        image_data: bytes, 
        user_id: str,
        meal_date: date = None,
        meal_type: str = None,
        filename: str = None,
        content_type: str = "image/jpeg",
        is_original: bool = False,
        optimize: bool = True,
        file_prefix: str = None
    ) -> Dict[str, Any]:
        """
        上传图片到文件系统
        
        参数:
            image_data: 图片二进制数据
            user_id: 用户ID
            meal_date: 餐食日期
            meal_type: 餐食类型
            filename: 文件名，如果未提供则生成随机文件名
            content_type: 内容类型
            is_original: 是否为原图
            optimize: 是否优化图片
            file_prefix: 文件名前缀，用于区分不同类型的图像
        
        返回:
            Dict包含图片URL和路径信息
        """
        try:
            # 安全路径构建
            secure_path = StorageService._generate_secure_path(user_id)
            
            # 确定目标目录
            date_str = meal_date.strftime("%Y-%m-%d") if meal_date else datetime.now().strftime("%Y-%m-%d")
            
            if is_original:
                # 如果是原图，存储在单独的目录
                storage_dir = f"{StorageService.BASE_STORAGE_PATH}/{secure_path}/food_recognition_original/{date_str}"
            else:
                # 常规路径
                storage_dir = f"{StorageService.BASE_STORAGE_PATH}/{secure_path}/food_recognition/{date_str}"
            
            # 确保目录存在
            os.makedirs(storage_dir, exist_ok=True)
            
            # 生成文件名
            if not filename:
                file_ext = "jpg"
                if content_type == "image/png":
                    file_ext = "png"
                elif content_type == "image/gif":
                    file_ext = "gif"
                
                # 构建文件名，加入前缀
                prefix = ""
                if file_prefix:
                    prefix = file_prefix
                elif meal_type:
                    # 使用餐食类型作为文件名前缀
                    prefix = f"{meal_type}_"
                
                filename = f"{prefix}{uuid.uuid4().hex}.{file_ext}"
            elif file_prefix and not filename.startswith(file_prefix):
                # 如果提供了文件名和前缀，但文件名不以前缀开头，则添加前缀
                filename = f"{file_prefix}{filename}"
            
            # 构建完整文件路径
            file_path = os.path.join(storage_dir, filename)
            
            # 如果需要优化，处理图片
            if optimize and not is_original:
                try:
                    # 优化图片
                    optimized_data = await StorageService.optimize_image_for_web(image_data)
                    image_data = optimized_data
                except Exception as e:
                    # 优化失败，使用原始图片数据
                    logger.error(f"图片优化失败: {str(e)}")
                    # 继续使用原始图片数据
            
            # 保存文件
            with open(file_path, "wb") as f:
                f.write(image_data)
            
            # 构建返回的URL路径（相对路径）
            if is_original:
                relative_path = f"{secure_path}/food_recognition_original/{date_str}/{filename}"
            else:
                relative_path = f"{secure_path}/food_recognition/{date_str}/{filename}"
            
            # 创建原始完整URL用于保存（可通过静态文件服务访问）
            full_url = f"{StorageService.BASE_URL}/users/{relative_path}"
            
            # 创建简化版URL（只包含文件名）
            simplified_url = filename
            
            logger.info(f"图片上传成功，原始URL: {full_url}，简化URL: {simplified_url}")
            
            return {
                "url": simplified_url,  # 只返回文件名作为URL
                "full_url": full_url,   # 保留完整URL作为参考
                "filename": filename,
                "original_filename": filename,
                "secure_path": secure_path,
                "content_type": content_type,
                "size": len(image_data)
            }
            
        except Exception as e:
            logger.error(f"图片上传失败: {str(e)}")
            raise e
    
    @staticmethod
    async def upload_base64_image(
        base64_image: str,
        user_id: str,
        meal_date: date = None,
        meal_type: str = None,
        filename: str = None,
        is_original: bool = False,
        optimize: bool = True,
        file_prefix: str = None
    ) -> Dict[str, Any]:
        """
        上传base64编码的图片到文件系统
        
        参数:
            base64_image: base64编码的图片数据
            user_id: 用户ID
            meal_date: 餐食日期
            meal_type: 餐食类型
            filename: 文件名，如果未提供则生成随机文件名
            is_original: 是否为原图
            optimize: 是否优化图片
            file_prefix: 文件名前缀，用于区分不同类型的图像
            
        返回:
            Dict包含图片URL和路径信息
        """
        try:
            # 分析并处理base64数据
            content_type = "image/jpeg"  # 默认类型
            image_data = None
            
            # 检查是否包含data URL前缀
            data_url_pattern = r'^data:image\/([a-zA-Z+]+);base64,'
            match = re.match(data_url_pattern, base64_image)
            
            if match:
                # 数据URL格式: data:image/jpeg;base64,/9j/...
                image_type = match.group(1).lower()
                if image_type == "jpeg" or image_type == "jpg":
                    content_type = "image/jpeg"
                elif image_type == "png":
                    content_type = "image/png"
                elif image_type == "gif":
                    content_type = "image/gif"
                
                # 分离base64数据部分
                base64_data = base64_image.split(';base64,', 1)[1]
                image_data = base64.b64decode(base64_data)
            else:
                # 没有前缀，假设是纯base64数据
                try:
                    image_data = base64.b64decode(base64_image)
                except Exception as e:
                    logger.error(f"Base64解码错误: {str(e)}")
                    raise ValueError("提供的base64数据格式错误")
            
            # 调用现有的上传方法
            return await StorageService.upload_image(
                image_data=image_data,
                user_id=user_id,
                meal_date=meal_date,
                meal_type=meal_type,
                filename=filename,
                content_type=content_type,
                is_original=is_original,
                optimize=optimize,
                file_prefix=file_prefix
            )
            
        except Exception as e:
            logger.error(f"Base64图片上传失败: {str(e)}")
            raise e
    
    @staticmethod
    async def create_thumbnail(
        image_data: bytes,
        user_id: str,
        meal_date: date = None,
        meal_type: str = None,
        original_filename: str = None
    ) -> Dict[str, Any]:
        """
        从图片创建缩略图
        
        参数:
            image_data: 图片二进制数据
            user_id: 用户ID
            meal_date: 餐食日期
            meal_type: 餐食类型
            original_filename: 原始文件名
            
        返回:
            Dict包含缩略图URL和路径信息
        """
        try:
            # 安全路径构建
            secure_path = StorageService._generate_secure_path(user_id)
            
            # 确定缩略图目录
            date_str = meal_date.strftime("%Y-%m-%d") if meal_date else datetime.now().strftime("%Y-%m-%d")
            thumbnail_dir = f"{StorageService.BASE_STORAGE_PATH}/{secure_path}/thumbnails/{date_str}"
            
            # 确保目录存在
            os.makedirs(thumbnail_dir, exist_ok=True)
            
            # 生成缩略图文件名
            thumbnail_filename = f"thumb_{uuid.uuid4().hex}.jpg"
            if original_filename:
                # 使用原始文件名的前缀
                name_parts = os.path.splitext(original_filename)
                thumbnail_filename = f"thumb_{name_parts[0]}.jpg"
            
            # 构建完整缩略图路径
            thumbnail_path = os.path.join(thumbnail_dir, thumbnail_filename)
            
            # 创建缩略图
            img = Image.open(BytesIO(image_data))
            img.thumbnail((300, 300))  # 缩略图尺寸
            
            # 保存缩略图
            img.save(thumbnail_path, "JPEG", quality=80)
            
            # 缩略图相对路径
            relative_path = f"{secure_path}/thumbnails/{date_str}/{thumbnail_filename}"
            
            # 缩略图访问URL
            full_url = f"{StorageService.BASE_URL}/users/{relative_path}"
            
            # 创建简化版URL（只包含文件名）
            simplified_url = thumbnail_filename
            
            logger.info(f"缩略图创建成功，原始URL: {full_url}，简化URL: {simplified_url}")
            
            return {
                "url": simplified_url,  # 只返回文件名作为URL
                "full_url": full_url,   # 保留完整URL作为参考
                "filename": thumbnail_filename,
                "secure_path": secure_path,
                "content_type": "image/jpeg",
            }
            
        except Exception as e:
            logger.error(f"创建缩略图失败: {str(e)}")
            raise e
    
    @staticmethod
    async def create_thumbnail_from_base64(
        base64_image: str,
        user_id: str,
        meal_date: date = None,
        meal_type: str = None,
        original_filename: str = None
    ) -> Dict[str, Any]:
        """
        从base64编码的图片创建缩略图
        
        参数:
            base64_image: base64编码的图片数据
            user_id: 用户ID
            meal_date: 餐食日期
            meal_type: 餐食类型
            original_filename: 原始文件名
            
        返回:
            Dict包含缩略图URL和路径信息
        """
        try:
            # 解析base64数据
            image_data = None
            
            # 检查是否包含data URL前缀
            if ';base64,' in base64_image:
                # 分离base64数据部分
                base64_data = base64_image.split(';base64,', 1)[1]
                image_data = base64.b64decode(base64_data)
            else:
                # 纯base64数据
                try:
                    image_data = base64.b64decode(base64_image)
                except Exception as e:
                    logger.error(f"Base64解码错误: {str(e)}")
                    raise ValueError("提供的base64数据格式错误")
            
            # 调用现有的缩略图创建方法
            return await StorageService.create_thumbnail(
                image_data=image_data,
                user_id=user_id,
                meal_date=meal_date,
                meal_type=meal_type,
                original_filename=original_filename
            )
            
        except Exception as e:
            logger.error(f"Base64缩略图创建失败: {str(e)}")
            raise e
    
    @staticmethod
    async def optimize_image_for_web(image_data: bytes, max_size: int = 1200, quality: int = 85) -> bytes:
        """
        优化图片用于Web显示，保持细节的同时减小文件大小
        
        参数:
            image_data: 原始图片二进制数据
            max_size: 图片的最大尺寸（宽度或高度）
            quality: JPEG压缩质量
            
        返回:
            优化后的图片二进制数据
        """
        try:
            # 打开图片
            with Image.open(io.BytesIO(image_data)) as img:
                # 检查图片是否需要调整大小
                original_width, original_height = img.size
                
                # 如果图片尺寸超过最大值，按比例缩小
                new_width, new_height = original_width, original_height
                if original_width > max_size or original_height > max_size:
                    if original_width >= original_height:
                        ratio = max_size / original_width
                        new_width = max_size
                        new_height = int(original_height * ratio)
                    else:
                        ratio = max_size / original_height
                        new_height = max_size
                        new_width = int(original_width * ratio)
                    
                    # 创建调整后的图片
                    resized_img = img.resize((new_width, new_height), Image.LANCZOS)
                else:
                    # 不需要调整大小，使用原图
                    resized_img = img
                
                # 尝试WebP格式(如果PIL支持)
                webp_supported = hasattr(Image, 'WEBP')
                
                # 确定保存格式
                save_format = 'JPEG'
                if img.format == 'PNG' and 'transparency' in img.info:
                    save_format = 'PNG'
                
                # 转换到RGB模式（如果需要保存为JPEG）
                if save_format == 'JPEG' and resized_img.mode != 'RGB':
                    resized_img = resized_img.convert('RGB')
                
                # 先尝试WebP格式，如果支持的话
                output = io.BytesIO()
                optimized_data = None
                
                if webp_supported and resized_img.mode in ['RGB', 'RGBA']:
                    try:
                        # 尝试使用WebP格式，有损压缩，质量稍高以保证视觉效果
                        webp_output = io.BytesIO()
                        resized_img.save(webp_output, 'WEBP', quality=quality+5)
                        webp_data = webp_output.getvalue()
                        
                        # 检查WebP大小是否明显更小
                        if len(webp_data) < 0.9 * len(image_data):
                            optimized_data = webp_data
                            logger.info(f"WebP优化: 从 {len(image_data)/1024:.1f}KB 到 {len(webp_data)/1024:.1f}KB")
                    except Exception as webp_error:
                        logger.warning(f"WebP转换失败: {str(webp_error)}")
                
                # 如果WebP不可用或转换失败，使用传统格式
                if optimized_data is None:
                    if save_format == 'JPEG':
                        resized_img.save(output, save_format, quality=quality, optimize=True)
                    else:
                        resized_img.save(output, save_format, optimize=True)
                    
                    # 获取优化后的图片数据
                    optimized_data = output.getvalue()
                
                # 检查是否优化成功（大小是否减小）
                if len(optimized_data) >= len(image_data):
                    # 优化后图片反而变大，返回原图
                    logger.info("优化后图片大小增加，使用原始图片")
                    return image_data
                
                logger.info(f"图片优化: 从 {len(image_data)/1024:.1f}KB 到 {len(optimized_data)/1024:.1f}KB")
                return optimized_data
        
        except Exception as e:
            logger.error(f"图片优化失败: {str(e)}")
            # 返回原始图片数据
            return image_data
    
    @staticmethod
    async def convert_to_webp(image_data: bytes, quality: int = 80) -> bytes:
        """
        将图片转换为WebP格式，获得更好的压缩效果
        
        参数:
            image_data: 原始图片二进制数据
            quality: WebP压缩质量
            
        返回:
            WebP格式的图片二进制数据，如果转换失败则返回原数据
        """
        try:
            # 检查PIL是否支持WebP
            if not hasattr(Image, 'WEBP'):
                logger.warning("当前PIL版本不支持WebP格式")
                return image_data
                
            # 打开图片
            with Image.open(io.BytesIO(image_data)) as img:
                # 确保图片模式兼容WebP
                if img.mode not in ['RGB', 'RGBA']:
                    img = img.convert('RGB')
                
                # 保存为WebP格式
                output = io.BytesIO()
                img.save(output, 'WEBP', quality=quality)
                webp_data = output.getvalue()
                
                # 检查WebP转换是否成功减小了文件大小
                if len(webp_data) >= len(image_data):
                    logger.info("WebP转换后文件大小未减小，使用原始图片")
                    return image_data
                
                logger.info(f"WebP转换: 从 {len(image_data)/1024:.1f}KB 到 {len(webp_data)/1024:.1f}KB")
                return webp_data
        except Exception as e:
            logger.error(f"WebP转换失败: {str(e)}")
            return image_data
    
    @staticmethod
    async def delete_image(file_path: str) -> Dict[str, Any]:
        """
        删除图片。
        在实际项目中，这里应该调用云存储服务API。
        此实现为本地文件系统存储的简化版本。
        """
        try:
            # 确定存储根目录
            storage_dir = settings.UPLOADS_DIR
            
            # 构建完整路径
            full_path = os.path.join(storage_dir, file_path)
            
            # 检查文件是否存在
            if not os.path.exists(full_path):
                return {
                    "success": False,
                    "message": f"文件不存在: {file_path}"
                }
            
            # 删除文件
            os.remove(full_path)
            
            return {
                "success": True,
                "message": f"文件已成功删除: {file_path}"
            }
            
        except Exception as e:
            logger.error(f"删除图片失败: {str(e)}")
            return {
                "success": False,
                "message": f"删除失败: {str(e)}"
            }
    
    @staticmethod
    def _generate_secure_path(user_id: str) -> str:
        """
        为用户生成安全的存储路径
        
        参数:
            user_id: 用户ID
            
        返回:
            安全的存储路径
        """
        # 使用工具函数生成安全路径
        return generate_user_secure_path(user_id) 