"""
对话流程优化器 - 提供更智能的对话流程管理
"""
from typing import Dict, Any, List, Optional, Tuple, Set
import logging
import json
from app.services.llm_proxy_service import LLMProxyService
from app.core.chat_config import MODELS
from app.services.parameter_extractor import ParameterExtractor
from app.services.user_profile_manager import UserProfileManager

# 配置日志
logger = logging.getLogger(__name__)

# 全局LLM实例
llm_service = LLMProxyService()

class DialogFlowOptimizer:
    """对话流程优化器类，提供更智能的对话流程管理"""
    
    # 对话流程定义
    DIALOG_FLOWS = {
        "training_plan": {
            "required_params": ["body_part", "training_scene", "plan_type"],
            "optional_params": ["training_goal", "fitness_level", "duration_minutes", "equipment"],
            "required_user_info": ["gender", "age", "fitness_level"],
            "next_steps": ["generate_plan", "discuss_plan", "modify_plan"]
        },
        "fitness_qa": {
            "required_params": [],
            "optional_params": ["body_part", "training_goal"],
            "required_user_info": ["gender", "age"],
            "next_steps": ["answer_question", "follow_up"]
        },
        "diet_advice": {
            "required_params": [],
            "optional_params": ["training_goal"],
            "required_user_info": ["gender", "age", "weight", "height"],
            "next_steps": ["answer_question", "follow_up"]
        },
        "exercise_info": {
            "required_params": [],
            "optional_params": ["body_part", "equipment"],
            "required_user_info": ["fitness_level"],
            "next_steps": ["provide_exercise_info", "follow_up"]
        }
    }
    
    @classmethod
    async def optimize_dialog_flow(cls, intent: str, message: str, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        优化对话流程
        
        Args:
            intent: 用户意图
            message: 用户消息
            state: 当前状态
            
        Returns:
            优化后的状态
        """
        # 获取当前流程定义
        flow_def = cls.DIALOG_FLOWS.get(intent, {})
        
        # 如果没有流程定义，返回原始状态
        if not flow_def:
            return state
        
        # 获取当前参数和用户信息
        current_params = state.get("training_params", {})
        user_info = state.get("user_info", {})
        
        # 检查是否可以批量收集参数
        can_batch_collect = await cls._can_batch_collect_params(intent, message, current_params, user_info)
        
        if can_batch_collect:
            # 批量收集参数
            logger.info(f"尝试批量收集{intent}流程的参数")
            batch_params = await cls._batch_collect_params(intent, message, current_params, user_info)
            
            # 更新参数
            if "training_params" not in state:
                state["training_params"] = {}
            state["training_params"].update(batch_params)
            
            # 检查是否可以自动流程跨越
            can_auto_proceed = await cls._can_auto_proceed(intent, state["training_params"], user_info)
            
            if can_auto_proceed:
                # 自动流程跨越
                logger.info(f"自动流程跨越: {intent}")
                state["flow_state"]["auto_proceed"] = True
                state["flow_state"]["next_step"] = flow_def["next_steps"][0]
        
        # 检查是否需要收集用户信息
        missing_user_info = cls._get_missing_required_user_info(intent, user_info)
        
        if missing_user_info:
            # 尝试从消息中提取用户信息
            extracted_user_info = await cls._extract_user_info_from_message(message, missing_user_info)
            
            # 更新用户信息
            if extracted_user_info:
                if "user_info" not in state:
                    state["user_info"] = {}
                state["user_info"].update(extracted_user_info)
        
        return state
    
    @classmethod
    async def _can_batch_collect_params(cls, intent: str, message: str, current_params: Dict[str, Any], user_info: Dict[str, Any]) -> bool:
        """判断是否可以批量收集参数"""
        # 获取流程定义
        flow_def = cls.DIALOG_FLOWS.get(intent, {})
        
        # 如果没有必要参数，不需要批量收集
        if not flow_def.get("required_params"):
            return False
        
        # 检查消息长度，太短的消息可能不包含足够信息
        if len(message) < 15:
            return False
        
        # 使用LLM判断消息是否包含足够的参数信息
        prompt = f"""
        请分析用户消息，判断是否包含足够的信息来提取以下参数:
        {', '.join(flow_def.get("required_params", []))}

        用户消息:
        "{message}"

        这条消息是否包含足够的参数信息？请只回答"是"或"否"。
        """
        
        try:
            response = await llm_service.aget_chat_response(
                messages=[
                    {"role": "system", "content": "你是一个对话分析助手，负责判断用户消息是否包含足够的参数信息。"},
                    {"role": "user", "content": prompt}
                ],
                model=MODELS["intent_recognition"],
                temperature=0.1
            )
            
            # 解析响应
            contains_enough_info = "是" in response.lower()
            logger.info(f"批量参数收集判断结果: {'可以' if contains_enough_info else '不可以'}, 原始响应: {response}")
            
            return contains_enough_info
        except Exception as e:
            logger.error(f"判断批量参数收集失败: {str(e)}")
            # 出错时默认不批量收集
            return False
    
    @classmethod
    async def _batch_collect_params(cls, intent: str, message: str, current_params: Dict[str, Any], user_info: Dict[str, Any]) -> Dict[str, Any]:
        """批量收集参数"""
        # 获取流程定义
        flow_def = cls.DIALOG_FLOWS.get(intent, {})
        
        # 获取需要收集的参数
        required_params = flow_def.get("required_params", [])
        optional_params = flow_def.get("optional_params", [])
        all_params = required_params + optional_params
        
        # 构建提示词
        params_str = ", ".join(all_params)
        current_params_str = ", ".join([f"{k}: {v}" for k, v in current_params.items()])
        
        prompt = f"""
        请从用户消息中提取以下训练相关参数。如果无法确定某个参数，请返回null。

        需要提取的参数: {params_str}
        
        当前已知参数: {current_params_str}

        用户消息: "{message}"

        请返回JSON格式，包含提取的参数值。
        """
        
        try:
            response = await llm_service.aget_chat_response(
                messages=[
                    {"role": "system", "content": "你是一个参数提取助手，负责从用户消息中提取训练相关参数。"},
                    {"role": "user", "content": prompt}
                ],
                model="agent-app",
                temperature=0.1
            )
            
            # 解析JSON响应
            try:
                batch_params = json.loads(response)
                # 过滤掉None值
                batch_params = {k: v for k, v in batch_params.items() if v is not None}
                
                # 标准化参数
                normalized_params = ParameterExtractor._normalize_parameters(batch_params)
                
                logger.info(f"批量收集参数结果: {normalized_params}")
                return normalized_params
            except json.JSONDecodeError:
                # 尝试从文本中提取JSON
                try:
                    start_idx = response.find('{')
                    end_idx = response.rfind('}') + 1
                    if start_idx >= 0 and end_idx > start_idx:
                        json_str = response[start_idx:end_idx]
                        batch_params = json.loads(json_str)
                        # 过滤掉None值
                        batch_params = {k: v for k, v in batch_params.items() if v is not None}
                        
                        # 标准化参数
                        normalized_params = ParameterExtractor._normalize_parameters(batch_params)
                        
                        logger.info(f"批量收集参数结果: {normalized_params}")
                        return normalized_params
                    else:
                        logger.warning(f"无法从响应中提取JSON: {response}")
                        return {}
                except Exception as e:
                    logger.error(f"解析批量参数JSON失败: {str(e)}")
                    return {}
        except Exception as e:
            logger.error(f"批量收集参数失败: {str(e)}")
            return {}
    
    @classmethod
    async def _can_auto_proceed(cls, intent: str, params: Dict[str, Any], user_info: Dict[str, Any]) -> bool:
        """判断是否可以自动流程跨越"""
        # 获取流程定义
        flow_def = cls.DIALOG_FLOWS.get(intent, {})
        
        # 检查必要参数是否齐全
        required_params = flow_def.get("required_params", [])
        for param in required_params:
            if param not in params or not params[param]:
                return False
        
        # 检查必要用户信息是否齐全
        required_user_info = flow_def.get("required_user_info", [])
        for field in required_user_info:
            if field not in user_info or not user_info[field]:
                return False
        
        # 使用LLM判断是否可以自动流程跨越
        params_str = ", ".join([f"{k}: {v}" for k, v in params.items()])
        user_info_str = ", ".join([f"{k}: {v}" for k, v in user_info.items()])
        
        prompt = f"""
        请分析当前参数和用户信息，判断是否可以自动进行下一步流程。

        当前参数:
        {params_str}

        用户信息:
        {user_info_str}

        流程: {intent}

        是否可以自动进行下一步流程？请只回答"是"或"否"。
        """
        
        try:
            response = await llm_service.aget_chat_response(
                messages=[
                    {"role": "system", "content": "你是一个流程分析助手，负责判断是否可以自动进行下一步流程。"},
                    {"role": "user", "content": prompt}
                ],
                model=MODELS["intent_recognition"],
                temperature=0.1
            )
            
            # 解析响应
            can_auto_proceed = "是" in response.lower()
            logger.info(f"自动流程跨越判断结果: {'可以' if can_auto_proceed else '不可以'}, 原始响应: {response}")
            
            return can_auto_proceed
        except Exception as e:
            logger.error(f"判断自动流程跨越失败: {str(e)}")
            # 出错时默认不自动跨越
            return False
    
    @classmethod
    def _get_missing_required_user_info(cls, intent: str, user_info: Dict[str, Any]) -> List[str]:
        """获取缺失的必要用户信息"""
        # 获取流程定义
        flow_def = cls.DIALOG_FLOWS.get(intent, {})
        
        # 获取必要用户信息
        required_user_info = flow_def.get("required_user_info", [])
        
        # 检查缺失的用户信息
        missing_fields = []
        for field in required_user_info:
            if field not in user_info or not user_info[field]:
                missing_fields.append(field)
        
        return missing_fields
    
    @classmethod
    async def _extract_user_info_from_message(cls, message: str, fields: List[str]) -> Dict[str, Any]:
        """从消息中提取用户信息"""
        # 如果没有需要提取的字段，返回空字典
        if not fields:
            return {}
        
        # 构建提示词
        fields_str = ", ".join(fields)
        prompt = f"""
        请从用户消息中提取以下用户信息字段: {fields_str}

        用户消息: "{message}"

        如果无法确定某个字段，请返回null。请返回JSON格式，包含提取的字段值。
        """
        
        try:
            response = await llm_service.aget_chat_response(
                messages=[
                    {"role": "system", "content": "你是一个用户信息提取助手，负责从用户消息中提取用户信息。"},
                    {"role": "user", "content": prompt}
                ],
                model="agent-app",
                temperature=0.1
            )
            
            # 解析JSON响应
            try:
                extracted_fields = json.loads(response)
                # 过滤掉None值
                extracted_fields = {k: v for k, v in extracted_fields.items() if v is not None}
                
                # 验证提取的字段
                validated_fields = {}
                for field, value in extracted_fields.items():
                    is_valid, _, normalized_value = UserProfileManager.validate_field(field, value)
                    if is_valid:
                        validated_fields[field] = normalized_value
                
                logger.info(f"从消息中提取用户信息结果: {validated_fields}")
                return validated_fields
            except json.JSONDecodeError:
                # 尝试从文本中提取JSON
                try:
                    start_idx = response.find('{')
                    end_idx = response.rfind('}') + 1
                    if start_idx >= 0 and end_idx > start_idx:
                        json_str = response[start_idx:end_idx]
                        extracted_fields = json.loads(json_str)
                        # 过滤掉None值
                        extracted_fields = {k: v for k, v in extracted_fields.items() if v is not None}
                        
                        # 验证提取的字段
                        validated_fields = {}
                        for field, value in extracted_fields.items():
                            is_valid, _, normalized_value = UserProfileManager.validate_field(field, value)
                            if is_valid:
                                validated_fields[field] = normalized_value
                        
                        logger.info(f"从消息中提取用户信息结果: {validated_fields}")
                        return validated_fields
                    else:
                        logger.warning(f"无法从响应中提取JSON: {response}")
                        return {}
                except Exception as e:
                    logger.error(f"解析用户信息JSON失败: {str(e)}")
                    return {}
        except Exception as e:
            logger.error(f"从消息中提取用户信息失败: {str(e)}")
            return {}
    
    @classmethod
    async def generate_multi_path_response(cls, intent: str, message: str, state: Dict[str, Any]) -> Dict[str, Any]:
        """生成多路径响应"""
        # 获取当前参数和用户信息
        current_params = state.get("training_params", {})
        user_info = state.get("user_info", {})
        
        # 构建提示词
        params_str = ", ".join([f"{k}: {v}" for k, v in current_params.items()])
        user_info_str = ", ".join([f"{k}: {v}" for k, v in user_info.items()])
        
        prompt = f"""
        请分析用户消息，生成多路径响应选项。

        用户消息: "{message}"

        当前参数:
        {params_str}

        用户信息:
        {user_info_str}

        意图: {intent}

        请生成3个可能的响应路径（JSON格式）:
        - path_1: 第一个响应路径
        - path_2: 第二个响应路径
        - path_3: 第三个响应路径

        每个路径包含:
        - response: 响应内容
        - next_step: 下一步操作
        - required_params: 需要收集的参数列表

        请返回JSON格式。
        """
        
        try:
            response = await llm_service.aget_chat_response(
                messages=[
                    {"role": "system", "content": "你是一个多路径响应生成助手，负责生成多个可能的响应路径。"},
                    {"role": "user", "content": prompt}
                ],
                model=MODELS["conversation"],
                temperature=0.7
            )
            
            # 解析JSON响应
            try:
                paths = json.loads(response)
                logger.info(f"生成多路径响应结果: {paths}")
                return paths
            except json.JSONDecodeError:
                # 尝试从文本中提取JSON
                try:
                    start_idx = response.find('{')
                    end_idx = response.rfind('}') + 1
                    if start_idx >= 0 and end_idx > start_idx:
                        json_str = response[start_idx:end_idx]
                        paths = json.loads(json_str)
                        logger.info(f"生成多路径响应结果: {paths}")
                        return paths
                    else:
                        logger.warning(f"无法从响应中提取JSON: {response}")
                        return {}
                except Exception as e:
                    logger.error(f"解析多路径响应JSON失败: {str(e)}")
                    return {}
        except Exception as e:
            logger.error(f"生成多路径响应失败: {str(e)}")
            return {}
