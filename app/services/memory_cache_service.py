"""
内存缓存服务 - 提供高效的内存缓存机制，减少数据库访问
作为Redis缓存的补充，用于频繁访问的数据
"""
from typing import Dict, Any, Optional, List, TypeVar, Generic, Callable
import time
import logging
import json
from collections import OrderedDict
from datetime import datetime, timedelta
from threading import Lock
from app.core.config import settings
from app.services.state_definitions import ConversationState

logger = logging.getLogger(__name__)

T = TypeVar('T')

class CacheItem(Generic[T]):
    """缓存项"""
    def __init__(self, value: T, ttl: int = 3600):
        self.value = value
        self.created_at = time.time()
        self.last_accessed = time.time()
        self.ttl = ttl  # 生存时间（秒）
        self.access_count = 0  # 访问计数
    
    def is_expired(self) -> bool:
        """检查缓存项是否过期"""
        return (time.time() - self.created_at) > self.ttl
    
    def access(self) -> T:
        """访问缓存项，更新访问时间和计数"""
        self.last_accessed = time.time()
        self.access_count += 1
        return self.value


class BaseCache(Generic[T]):
    """基础缓存类"""
    def __init__(self, name: str, ttl: int = 3600, max_size: int = 1000):
        self.name = name
        self.ttl = ttl
        self.max_size = max_size
        self.cache: Dict[str, CacheItem[T]] = {}
        self.lock = Lock()
        self.hit_count = 0
        self.miss_count = 0
        logger.info(f"初始化内存缓存: {name}, TTL={ttl}秒, 最大容量={max_size}")
    
    def get(self, key: str) -> Optional[T]:
        """获取缓存项"""
        with self.lock:
            if key in self.cache:
                item = self.cache[key]
                if item.is_expired():
                    # 缓存项已过期，删除并返回None
                    del self.cache[key]
                    self.miss_count += 1
                    return None
                # 缓存命中
                self.hit_count += 1
                return item.access()
            # 缓存未命中
            self.miss_count += 1
            return None
    
    def set(self, key: str, value: T, ttl: Optional[int] = None) -> None:
        """设置缓存项"""
        if value is None:
            return
        
        with self.lock:
            # 如果缓存已满，删除最早访问的项
            if len(self.cache) >= self.max_size and key not in self.cache:
                self._evict()
            
            # 设置缓存项
            self.cache[key] = CacheItem(value, ttl or self.ttl)
    
    def delete(self, key: str) -> None:
        """删除缓存项"""
        with self.lock:
            if key in self.cache:
                del self.cache[key]
    
    def clear(self) -> None:
        """清空缓存"""
        with self.lock:
            self.cache.clear()
    
    def _evict(self) -> None:
        """驱逐策略：删除最早访问的项"""
        if not self.cache:
            return
        
        # 找到最早访问的项
        oldest_key = min(self.cache.keys(), key=lambda k: self.cache[k].last_accessed)
        del self.cache[oldest_key]
    
    def cleanup(self) -> int:
        """清理过期项，返回清理的数量"""
        with self.lock:
            expired_keys = [k for k, v in self.cache.items() if v.is_expired()]
            for key in expired_keys:
                del self.cache[key]
            return len(expired_keys)
    
    def stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.lock:
            total = self.hit_count + self.miss_count
            hit_rate = self.hit_count / total if total > 0 else 0
            return {
                "name": self.name,
                "size": len(self.cache),
                "max_size": self.max_size,
                "ttl": self.ttl,
                "hit_count": self.hit_count,
                "miss_count": self.miss_count,
                "hit_rate": hit_rate,
                "memory_usage_estimate": len(self.cache) * 1024  # 粗略估计
            }


class UserInfoCache(BaseCache[Dict[str, Any]]):
    """用户信息缓存"""
    def __init__(self):
        super().__init__(
            name="user_info_cache",
            ttl=3600,  # 1小时
            max_size=10000  # 最多缓存10000个用户
        )


class SessionStateCache(BaseCache[Dict[str, Any]]):
    """会话状态缓存"""
    def __init__(self):
        super().__init__(
            name="session_state_cache",
            ttl=1800,  # 30分钟
            max_size=5000  # 最多缓存5000个会话
        )


class ConversationStateCache(BaseCache[ConversationState]):
    """会话状态对象缓存"""
    def __init__(self):
        super().__init__(
            name="conversation_state_cache",
            ttl=1800,  # 30分钟
            max_size=1000  # 最多缓存1000个会话状态对象
        )


class QueryResultCache(BaseCache[List[Dict[str, Any]]]):
    """查询结果缓存"""
    def __init__(self):
        super().__init__(
            name="query_result_cache",
            ttl=300,  # 5分钟
            max_size=1000  # 最多缓存1000个查询结果
        )
    
    def get_with_key(self, query: str, params: Optional[Dict[str, Any]] = None) -> Optional[List[Dict[str, Any]]]:
        """使用查询和参数作为键获取缓存结果"""
        # 生成缓存键
        key = self._generate_key(query, params)
        return self.get(key)
    
    def set_with_key(self, query: str, params: Optional[Dict[str, Any]], result: List[Dict[str, Any]]) -> None:
        """使用查询和参数作为键设置缓存结果"""
        # 生成缓存键
        key = self._generate_key(query, params)
        self.set(key, result)
    
    def _generate_key(self, query: str, params: Optional[Dict[str, Any]]) -> str:
        """生成缓存键"""
        # 规范化查询（移除多余空格）
        normalized_query = " ".join(query.split())
        
        # 如果有参数，将其包含在键中
        if params:
            # 将参数转换为排序后的字符串
            params_str = json.dumps(params, sort_keys=True)
            return f"{normalized_query}:{params_str}"
        
        return normalized_query


# 全局缓存实例
user_info_cache = UserInfoCache()
session_state_cache = SessionStateCache()
conversation_state_cache = ConversationStateCache()
query_result_cache = QueryResultCache()


class MemoryCacheService:
    """内存缓存服务，提供统一的缓存管理接口"""
    
    @staticmethod
    def get_user_info(user_id: int) -> Optional[Dict[str, Any]]:
        """获取缓存的用户信息"""
        return user_info_cache.get(str(user_id))
    
    @staticmethod
    def set_user_info(user_id: int, user_info: Dict[str, Any]) -> None:
        """缓存用户信息"""
        user_info_cache.set(str(user_id), user_info)
    
    @staticmethod
    def get_session_state(session_id: str) -> Optional[Dict[str, Any]]:
        """获取缓存的会话状态"""
        return session_state_cache.get(session_id)
    
    @staticmethod
    def set_session_state(session_id: str, state: Dict[str, Any]) -> None:
        """缓存会话状态"""
        session_state_cache.set(session_id, state)
    
    @staticmethod
    def get_conversation_state(session_id: str) -> Optional[ConversationState]:
        """获取缓存的会话状态对象"""
        return conversation_state_cache.get(session_id)
    
    @staticmethod
    def set_conversation_state(session_id: str, state: ConversationState) -> None:
        """缓存会话状态对象"""
        conversation_state_cache.set(session_id, state)
    
    @staticmethod
    def get_query_result(query: str, params: Optional[Dict[str, Any]] = None) -> Optional[List[Dict[str, Any]]]:
        """获取缓存的查询结果"""
        return query_result_cache.get_with_key(query, params)
    
    @staticmethod
    def set_query_result(query: str, params: Optional[Dict[str, Any]], result: List[Dict[str, Any]]) -> None:
        """缓存查询结果"""
        query_result_cache.set_with_key(query, params, result)
    
    @staticmethod
    def cleanup() -> Dict[str, int]:
        """清理所有缓存的过期项"""
        return {
            "user_info": user_info_cache.cleanup(),
            "session_state": session_state_cache.cleanup(),
            "conversation_state": conversation_state_cache.cleanup(),
            "query_result": query_result_cache.cleanup()
        }
    
    @staticmethod
    def stats() -> Dict[str, Dict[str, Any]]:
        """获取所有缓存的统计信息"""
        return {
            "user_info": user_info_cache.stats(),
            "session_state": session_state_cache.stats(),
            "conversation_state": conversation_state_cache.stats(),
            "query_result": query_result_cache.stats()
        }
