from fastapi import FastAPI, Request, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from app.core.config import settings
from app.api.v1.api import api_router
from app.api.admin.admin_router import router as admin_router
import logging
import os
import re
from pathlib import Path
import time
import traceback
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse, RedirectResponse, Response, FileResponse
import json
from app.api.endpoints.exercise import fix_path_middleware
import logging
from contextlib import asynccontextmanager


# 配置日志
log_file = "logs.txt"
# 配置日志，同时输出到文件和控制台
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()  # 添加控制台输出
    ]
)

# 设置 SQLAlchemy Engine 的日志级别，移到这里确保尽早生效
sql_engine_logger = logging.getLogger("sqlalchemy.engine")
sql_engine_logger.setLevel(logging.ERROR)  # 只显示ERROR级别的日志，完全禁止INFO和WARNING
sql_engine_logger.propagate = False  # 阻止消息向上传播

# 同时设置sqlalchemy.pool的日志级别
sql_pool_logger = logging.getLogger("sqlalchemy.pool")
sql_pool_logger.setLevel(logging.ERROR)
sql_pool_logger.propagate = False

# 设置sqlalchemy.orm的日志级别
sql_orm_logger = logging.getLogger("sqlalchemy.orm")
sql_orm_logger.setLevel(logging.ERROR)
sql_orm_logger.propagate = False

logger = logging.getLogger("fitness-coach-api")
# 记录启动信息
logger.info("应用启动")
logger.info(f"开发环境: {settings.IS_DEV}")
logger.info(f"日志级别: {settings.LOG_LEVEL}")

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("应用启动中...")
    
    # 加载LLM提供商
    try:
        from app.services.ai_assistant.llm.factory import LLMProxyFactory
        LLMProxyFactory.load_providers()
        logger.info("LLM提供商加载完成")
        
        # 检查可用的提供商
        available_providers = LLMProxyFactory.list_available_providers()
        logger.info(f"可用的LLM提供商: {list(available_providers.keys())}")
        
        # 尝试获取默认提供商
        try:
            default_provider = LLMProxyFactory.get_provider()
            logger.info(f"默认LLM提供商: {default_provider.__class__.__name__}")
        except Exception as e:
            logger.warning(f"无法获取默认LLM提供商: {str(e)}")
            
    except Exception as e:
        logger.error(f"加载LLM提供商失败: {str(e)}")
    
    logger.info("应用启动完成")
    
    yield
    
    # 关闭时执行
    logger.info("应用关闭中...")

app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.VERSION,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url=None,  # 禁用默认的Swagger UI路由
    redoc_url=None,  # 禁用默认的ReDoc路由
    lifespan=lifespan
)
# 只显示 WARNING 及以上级别的日志，屏蔽 INFO 级别
# logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING) # 这行将被移动到上面
# 配置FastAPI响应验证，使其更宽松
from fastapi.responses import Response
from fastapi.exceptions import ResponseValidationError, RequestValidationError
from pydantic import BaseModel, ConfigDict
from typing import Any

# 自定义全局模型配置，使验证更宽松
class BaseSchema(BaseModel):
    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
        extra="ignore"  # 忽略额外字段
    )

# 配置CORS
origins = []
# 设置开发环境下允许所有源访问
if settings.IS_DEV:
    origins = ["*"]
else:
    origins = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "https://localhost:3000",
        "https://127.0.0.1:3000",
        "http://localhost",
        "http://127.0.0.1",
        "https://fitapi.edgeless.top",
        "https://coach.fit",
        "https://www.coach.fit",
    ]
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
# 添加路径修复中间件，用于处理URL路径问题
@app.middleware("http")
async def fix_url_paths(request: Request, call_next):
    """处理URL路径中的双斜杠和嵌套问题"""
    path = request.url.path
    original_path = path

    # 修复双斜杠问题
    while '//' in path:
        path = path.replace('//', '/')

    # 修复路径嵌套问题
    path = re.sub(r'/exercises/images/data/exercises/images/', '/exercises/images/', path)
    path = re.sub(r'/exercises/videos/data/exercises/videos/', '/exercises/videos/', path)
    path = re.sub(r'/exercises/gifs/data/exercises/gifs/', '/exercises/gifs/', path)

    # 处理/data前缀问题
    if '/data/exercises/' in path:
        path = path.replace('/data/exercises/', '/exercises/')

    # 如果路径有变化，重定向到正确路径
    if path != original_path:
        logger.warning(f"检测到路径问题，正在修复: {original_path} -> {path}")
        return RedirectResponse(url=path)

    # 继续处理请求
    return await call_next(request)

# 简化中间件，仅记录请求基础信息，不捕获响应详情
@app.middleware("http")
async def log_request(request: Request, call_next):
    # 记录请求开始时间
    start_time = time.time()
    client_host = request.client.host if request.client else "unknown"

    # 构建完整URL，包括查询参数
    full_url = str(request.url)

    # 记录请求详细信息
    logger.info(f"接收请求: {request.method} {request.url.path} - 来自IP: {client_host}")
    logger.info(f"完整URL: {full_url}")
    logger.info(f"请求头: {dict(request.headers)}")

    # 执行请求处理，不捕获和记录响应体
    try:
        response = await call_next(request)

        # 记录处理时间和响应状态
        process_time = time.time() - start_time
        status_code = response.status_code

        # 对于404错误，记录更详细的信息
        if status_code == 404:
            logger.warning(f"404错误: {request.method} {request.url.path} - 可能的路由不匹配")
            logger.warning(f"检查连字符与下划线: 尝试将'-'替换为'_'或将'_'替换为'-'")

            # 提供可能的替代路径（将连字符替换为下划线，反之亦然）
            alt_path = request.url.path.replace('-', '_')
            if alt_path == request.url.path:  # 如果没有变化，说明可能是下划线需要变成连字符
                alt_path = request.url.path.replace('_', '-')
            logger.warning(f"可能的替代路径: {alt_path}")

        logger.info(f"响应完成: {request.method} {request.url.path} - 状态码: {status_code} - 耗时: {process_time:.4f}秒")

        return response
    except Exception as e:
        # 记录处理过程中的异常
        process_time = time.time() - start_time
        logger.error(f"请求处理异常: {request.method} {request.url.path} - 错误: {str(e)} - 耗时: {process_time:.4f}秒")
        logger.error(f"异常堆栈: {traceback.format_exc()}")
        raise
# 直接挂载动作库图片目录
app.mount("/exercises/images", StaticFiles(directory="/data/exercises/images"), name="exercises_images")
# 同时支持单数形式的路径访问
app.mount("/exercises/image", StaticFiles(directory="/data/exercises/images"), name="exercises_image")

# 挂载GIF文件目录
app.mount("/exercises/gifs", StaticFiles(directory="/data/exercises/gifs"), name="exercises_gifs")
app.mount("/exercises/gif", StaticFiles(directory="/data/exercises/gifs"), name="exercises_gif")

# 不再直接挂载视频目录，使用动态路由处理视频流
# app.mount("/exercises/videos", StaticFiles(directory="/data/exercises/videos"), name="exercises_videos")
# app.mount("/exercises/video", StaticFiles(directory="/data/exercises/videos"), name="exercises_video")

# 挂载肌肉图片目录
app.mount("/muscles/images", StaticFiles(directory="/data/muscles"), name="muscles_images")
app.mount("/muscles/image", StaticFiles(directory="/data/muscles"), name="muscles_image")
app.mount("/muscles/body_front", StaticFiles(directory="/data/muscles/body_front"), name="muscles_body_front")
app.mount("/muscles/body_back", StaticFiles(directory="/data/muscles/body_back"), name="muscles_body_back")

# 修改食物图像目录挂载方式，重命名以避免混淆
# 旧的挂载点
# app.mount("/food/images", StaticFiles(directory="/data/users"), name="food_images")
# 新的挂载点
app.mount("/user-food-data", StaticFiles(directory="/data/users"), name="user_food_data")

# 兼容旧版URL格式
app.mount("/api/files/users", StaticFiles(directory="/data/users"), name="legacy_food_images")

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")
# 挂载数据根目录
app.mount("/data", StaticFiles(directory="/data"), name="data_root")
# 挂载食物图片目录 - 更新为简化的方式
app.mount("/food", StaticFiles(directory="/data/food"), name="food_dir")
# 挂载处理后的视频目录
app.mount("/videos", StaticFiles(directory="/data/videos/processed"), name="processed_videos")
# 删除重复的挂载点
# app.mount("/data/exercises/gifs", StaticFiles(directory="/data/exercises/gifs"), name="data_gifs")
# app.mount("/data/exercises/images", StaticFiles(directory="/data/exercises/images"), name="data_images")
# app.mount("/data/exercises/videos", StaticFiles(directory="/data/exercises/videos"), name="data_videos")

# 启动时打印所有路由
@app.on_event("startup")
async def print_routes():
    """打印所有注册的路由，帮助调试404错误"""
    logger.info("路由调试：注册的API路由列表")
    all_routes = []
    for route in api_router.routes:
        route_info = f"{route.path} [{','.join(getattr(route, 'methods', ['']))}]"
        all_routes.append(route_info)
        # logger.info(f"路由: {route_info}")
    logger.info(f"总计 {len(all_routes)} 个API路由")

app.include_router(api_router, prefix=settings.API_V1_STR)
# 添加v2版API路由
from app.api.v2.api import api_router as api_router_v2
app.include_router(api_router_v2, prefix="/api/v2")
# 注册管理后台路由
app.include_router(admin_router)

# 添加对workout/workouts的特殊处理，确保两种形式都能正常工作
@app.middleware("http")
async def workout_path_compatibility(request: Request, call_next):
    """处理workout和workouts路径的兼容性问题"""
    path = request.url.path

    # 如果是旧的workouts路径但服务器找不到对应路由，尝试重定向到workout路径
    if path.startswith("/api/v1/workouts/") and "workout_id" not in request.path_params:
        # 记录重定向信息
        logger.info(f"将请求从复数形式 {path} 重定向到单数形式 {path.replace('/workouts/', '/workout/')}")
        # 返回重定向响应
        return RedirectResponse(url=path.replace("/workouts/", "/workout/"))

    # 正常处理请求
    response = await call_next(request)

    # 如果是404错误，并且路径包含workouts，尝试重定向到workout
    if response.status_code == 404 and "/workouts/" in path:
        logger.info(f"404错误，尝试从 {path} 重定向到 {path.replace('/workouts/', '/workout/')}")
        return RedirectResponse(url=path.replace("/workouts/", "/workout/"))

    return response

# 添加WebSocket路径兼容性处理中间件
@app.middleware("http")
async def websocket_path_compatibility(request: Request, call_next):
    """处理WebSocket路径的兼容性问题，特别是会话ID格式和/connect后缀"""
    path = request.url.path
    original_path = path
    
    # 检查是否为WebSocket路径且包含会话ID
    if "/api/v1/chat/stream/" in path:
        # 处理session_id格式问题（下划线和连字符）
        parts = path.split("/api/v1/chat/stream/")
        if len(parts) == 2:
            prefix = parts[0]
            rest = parts[1]
            
            # 处理/connect后缀
            if rest.endswith("/connect"):
                rest = rest[:-8]  # 移除"/connect"
                
            # 统一转换为连字符格式
            normalized_rest = rest.replace("_", "-")
            
            # 重建路径
            new_path = f"{prefix}/api/v1/chat/stream/{normalized_rest}"
            
            # 如果路径有变化，重定向到新路径
            if new_path != original_path:
                logger.info(f"WebSocket路径规范化: {original_path} -> {new_path}")
                return RedirectResponse(url=new_path)
    
    # 正常处理请求
    response = await call_next(request)
    
    # 对于404错误，如果是WebSocket路径，尝试提供具体的建议
    if response.status_code == 404 and "/api/v1/chat/stream/" in path:
        logger.warning(f"WebSocket 404错误: {path}")
        logger.warning("请确保WebSocket客户端使用正确的URL格式: /api/v1/chat/stream/SESSIONID")
        logger.warning("会话ID应使用连字符(-)而非下划线(_)，且不要添加/connect后缀")
    
    return response

# 添加对food_recognition的特殊处理（将下划线版本转发到连字符版本）
@app.api_route("/api/v1/food_recognition{path:path}", methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"])
async def redirect_food_recognition(request: Request, path: str):
    """
    特殊处理：将/api/v1/food_recognition的请求转发到/api/v1/food-recognition
    """
    logger.info(f"将请求从 /api/v1/food_recognition{path} 重定向到 /api/v1/food-recognition{path}")

    # 处理OPTIONS请求的特殊情况
    if request.method == "OPTIONS":
        headers = {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
            "Access-Control-Allow-Headers": "Authorization, Content-Type",
            "Access-Control-Max-Age": "86400",  # 24小时
        }
        return Response(headers=headers)

    # 其他请求类型使用307临时重定向
    return RedirectResponse(url=f"/api/v1/food-recognition{path}", status_code=307)

# 添加一个直接的路径别名，以防重定向失败
@app.get("/api/v1/food-recognition")
@app.post("/api/v1/food-recognition")
@app.options("/api/v1/food-recognition")
async def food_recognition_root(request: Request):
    """直接处理根路径请求"""
    logger.info(f"处理根路径请求: {request.method} /api/v1/food-recognition")
    # 重定向到实际的路由
    return RedirectResponse(url=f"/api/v1/food_recognition/", status_code=307)

@app.get("/health")
def health_check():
    """健康检查端点，供Docker healthcheck使用"""
    return {"status": "ok"}

@app.get("/")
def read_root():
    logger.info("访问了首页")
    return {"message": "Welcome to Fitness Coach API", "docs": f"{settings.API_V1_STR}/docs"}
# 在所有路由注册完毕后再设置Swagger文档
# 防止循环导入问题
from app.api.openapi_docs import setup_swagger_docs
setup_swagger_docs(app)
# 异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logger.error(f"全局异常: {str(exc)}")
    logger.error(f"异常堆栈: {traceback.format_exc()}")
    return JSONResponse(
        status_code=500,
        content={"detail": "服务器内部错误"}
    )
# 添加HTTP异常处理器
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    # 记录详细的异常信息
    logger.error(f"HTTP异常: 状态码={exc.status_code}, 详情={exc.detail}, 路径={request.url.path}")

    # 如果是400错误，在开发环境下返回更详细的错误信息
    if exc.status_code == 400 and settings.IS_DEV:
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "detail": exc.detail,
                "path": request.url.path,
                "method": request.method,
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
                "is_dev": True
            }
        )

    # 正常返回异常
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail}
    )
# app.mount("/test", StaticFiles(directory="test_static"), name="test_static")
# 注册API路由

# 创建一个新的路由，通过文件名直接访问食物图片
@app.get("/meal-images/{filename}")
async def get_food_image_by_filename(filename: str):
    """
    通过文件名直接访问食物图片，无需暴露完整路径
    """
    # 在系统中搜索此文件
    base_dirs = ["/data/users", "/data/food"]

    # 日志记录访问请求
    logger.info(f"尝试通过简化路径访问食物图片: {filename}")

    # 查找文件的逻辑
    for base_dir in base_dirs:
        for root, _, files in os.walk(base_dir):
            if filename in files:
                file_path = os.path.join(root, filename)
                logger.info(f"找到文件: {file_path}")
                return FileResponse(file_path)

    # 如果找不到文件，返回404错误
    logger.warning(f"找不到文件: {filename}")
    raise HTTPException(status_code=404, detail="图片文件未找到")