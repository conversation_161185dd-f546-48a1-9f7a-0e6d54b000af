import logging
import os
from pathlib import Path

# 确保日志目录存在
logs_dir = Path("logs")
if not logs_dir.exists():
    logs_dir.mkdir(parents=True, exist_ok=True)

# 日志文件路径
log_file = logs_dir / "app.log"

# 配置日志格式
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

def setup_logger(name: str, level: str = "INFO", log_to_file: bool = True):
    """
    设置并返回一个命名的日志记录器
    
    参数:
        name: 日志记录器名称
        level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_to_file: 是否同时记录到文件
    
    返回:
        配置好的日志记录器实例
    """
    # 将字符串日志级别转换为实际级别
    numeric_level = getattr(logging, level.upper(), logging.INFO)
    
    # 创建日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(numeric_level)
    
    # 如果日志记录器已经有处理器，则不重复添加
    if logger.handlers:
        return logger
    
    # 创建处理器集合
    handlers = []
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(numeric_level)
    handlers.append(console_handler)
    
    # 文件处理器 (可选)
    if log_to_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(numeric_level)
        handlers.append(file_handler)
    
    # 创建格式化器
    formatter = logging.Formatter(LOG_FORMAT)
    
    # 将格式化器添加到所有处理器
    for handler in handlers:
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    return logger

# 默认应用日志记录器
app_logger = setup_logger("fitness-coach-api")

# 导出常用函数，使其可以直接从logger模块导入
def get_logger(name: str, level: str = "INFO"):
    """获取指定名称的日志记录器"""
    return setup_logger(name, level) 