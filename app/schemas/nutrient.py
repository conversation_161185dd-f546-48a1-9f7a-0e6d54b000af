from pydantic import BaseModel, Field
from typing import Optional, Dict, List
from decimal import Decimal

class NutrientBase(BaseModel):
    value: Optional[Decimal] = None
    unit: Optional[str] = None

class VitaminValue(BaseModel):
    vitamin_a: Optional[NutrientBase] = None
    vitamin_d: Optional[NutrientBase] = None
    vitamin_e: Optional[NutrientBase] = None
    vitamin_k: Optional[NutrientBase] = None  # 从CSV中添加
    thiamine: Optional[NutrientBase] = None  # vitamin_b1
    lactoflavin: Optional[NutrientBase] = None  # vitamin_b2
    vitamin_b6: Optional[NutrientBase] = None
    vitamin_b12: Optional[NutrientBase] = None
    vitamin_c: Optional[NutrientBase] = None
    niacin: Optional[NutrientBase] = None
    folacin: Optional[NutrientBase] = None
    pantothenic: Optional[NutrientBase] = None
    biotin: Optional[NutrientBase] = None
    choline: Optional[NutrientBase] = None
    carotene: Optional[NutrientBase] = None  # 从CSV中添加

class MineralValue(BaseModel):
    calcium: Optional[NutrientBase] = None
    phosphor: Optional[NutrientBase] = None
    kalium: Optional[NutrientBase] = None
    natrium: Optional[NutrientBase] = None
    magnesium: Optional[NutrientBase] = None
    chlorine: Optional[NutrientBase] = None
    iron: Optional[NutrientBase] = None
    iodine: Optional[NutrientBase] = None
    zinc: Optional[NutrientBase] = None
    selenium: Optional[NutrientBase] = None
    copper: Optional[NutrientBase] = None
    fluorine: Optional[NutrientBase] = None
    manganese: Optional[NutrientBase] = None  # 从CSV中添加

class MacroNutrientValue(BaseModel):
    calory: Optional[NutrientBase] = None
    protein: Optional[NutrientBase] = None
    protein_rni: Optional[NutrientBase] = None  # RNI值
    fat: Optional[NutrientBase] = None
    saturated_fat: Optional[NutrientBase] = None
    ufa: Optional[NutrientBase] = None  # 不饱和脂肪酸
    mufa: Optional[NutrientBase] = None  # 单不饱和脂肪酸
    pufa: Optional[NutrientBase] = None  # 多不饱和脂肪酸
    n3fa: Optional[NutrientBase] = None  # n-3脂肪酸
    carbohydrate: Optional[NutrientBase] = None
    fiber_dietary: Optional[NutrientBase] = None
    fructose: Optional[NutrientBase] = None  # 添加糖

class WaterValue(BaseModel):
    drinking_water: Optional[NutrientBase] = None  # 饮水量
    total_water: Optional[NutrientBase] = None  # 总水摄入量

class OtherDietaryValue(BaseModel):
    name_cn: str
    spl: Optional[NutrientBase] = None  # 推荐最大摄入量
    ul: Optional[NutrientBase] = None  # 可耐受最高摄入量

class NutrientRequest(BaseModel):
    age: Decimal = Field(..., description="用户年龄(岁)")
    sex: str = Field(..., description="性别: 'male' 或 'female'")
    pregnancy_stage: Optional[str] = Field(None, description="孕期阶段: 'early', 'mid', 'late', 'lactation' 或 None")
    dietary_names: Optional[List[str]] = Field(None, description="营养素字段名称列表，如'vitamin_a'、'phosphor'等")

class NutrientResponse(BaseModel):
    vitamins: VitaminValue
    minerals: MineralValue
    macros: MacroNutrientValue
    water: WaterValue
    other_dietary: Optional[List[OtherDietaryValue]] = None
    recommendation_type: str  # AI 或 RNI
    notes: Optional[str] = None

    class Config:
        schema_extra = {
            "example": {
                "vitamins": {
                    "vitamin_a": {"value": 770, "unit": "μgRAE"},
                    "vitamin_d": {"value": 10, "unit": "μg"},
                    # 其他维生素...
                },
                "minerals": {
                    "calcium": {"value": 800, "unit": "mg"},
                    "iron": {"value": 12, "unit": "mg"},
                    # 其他矿物质...
                },
                "macros": {
                    "protein": {"value": 65, "unit": "g"},
                    "fat": {"value": 60, "unit": "g"},
                    # 其他宏量营养素...
                },
                "water": {
                    "drinking_water": {"value": 1700, "unit": "mL"},
                    "total_water": {"value": 3000, "unit": "mL"}
                },
                "other_dietary": [
                    {
                        "name_cn": "大豆异黄酮",
                        "spl": {"value": 55, "unit": "mg"},
                        "ul": {"value": 120, "unit": "mg"}
                    }
                ],
                "recommendation_type": "RNI",
                "notes": "基于中国居民膳食营养素参考摄入量"
            }
        }
