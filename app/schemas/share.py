from typing import Optional
from pydantic import BaseModel
from datetime import datetime


class ShareTrackBase(BaseModel):
    share_type: str  # 'menu', 'button', 'qrcode'
    page: str
    scene: Optional[str] = None  # 场景参数，用于精确匹配QR码
    qrcode_url: Optional[str] = None  # 小程序码URL


class ShareTrackCreate(ShareTrackBase):
    shared_by: Optional[int] = None
    scanned_by: Optional[int] = None


class ShareTrackInDBBase(ShareTrackBase):
    id: int
    shared_by: Optional[int] = None
    scanned_by: Optional[int] = None
    created_at: datetime

    class Config:
        from_attributes = True


class ShareTrack(ShareTrackInDBBase):
    pass


class ShareTrackInDB(ShareTrackInDBBase):
    pass


class QRCodeCreate(BaseModel):
    page: str
    scene: str
    width: int = 430


class QRCodeResponse(BaseModel):
    url: str  # 小程序码访问URL
    path: Optional[str] = None  # 小程序码在本地服务器的路径 