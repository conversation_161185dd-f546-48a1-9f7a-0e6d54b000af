from datetime import datetime
from typing import List, Optional, Dict, Any, Union

from pydantic import BaseModel, Field


class FoodUnitBase(BaseModel):
    unit_name: str
    weight: float
    eat_weight: float
    is_default: bool = False


class FoodUnitCreate(FoodUnitBase):
    pass


class FoodUnit(FoodUnitBase):
    id: int
    food_id: int

    model_config = {
        "from_attributes": True
    }


class FoodNutrientValueBase(BaseModel):
    name_en: str
    name_cn: str
    value: Optional[float] = None
    unit: Optional[str] = None
    unit_name: Optional[str] = None
    precision: Optional[int] = None
    nrv: Optional[float] = None
    category: str


class FoodNutrientValueCreate(FoodNutrientValueBase):
    pass


class FoodNutrientValue(FoodNutrientValueBase):
    id: int
    food_id: int

    model_config = {
        "from_attributes": True
    }


# 添加警告场景对象模型
class WarningScene(BaseModel):
    name: str
    tags: List[str]
    scene: str
    suitable: bool


class NutritionalProfileBase(BaseModel):
    health_light: Optional[int] = None
    lights: Optional[List[str]] = []
    warnings: Optional[List[str]] = []
    warning_scenes: Optional[List[WarningScene]] = []  # 修改为WarningScene对象列表
    calory: Optional[float] = None
    protein_fraction: Optional[float] = None
    fat_fraction: Optional[float] = None
    carb_fraction: Optional[float] = None
    food_rank: Optional[int] = None


class NutritionalProfileCreate(NutritionalProfileBase):
    pass


class NutritionalProfile(NutritionalProfileBase):
    id: int
    food_id: int
    updated_at: datetime

    model_config = {
        "from_attributes": True
    }


class FoodBase(BaseModel):
    name: str
    code: str
    category: Optional[str] = None
    food_type: Optional[str] = None
    goods_id: Optional[int] = None
    thumb_image_url: Optional[str] = None
    large_image_url: Optional[str] = None
    is_liquid: bool = False
    can_revise: bool = False
    hot: Optional[int] = 0  # 热度字段，默认为0


class FoodCreate(FoodBase):
    nutritional_profile: Optional[NutritionalProfileCreate] = None
    nutrients: Optional[List[FoodNutrientValueCreate]] = []
    units: Optional[List[FoodUnitCreate]] = []


class FoodUpdate(FoodBase):
    name: Optional[str] = None
    code: Optional[str] = None
    nutritional_profile: Optional[NutritionalProfileCreate] = None
    nutrients: Optional[List[FoodNutrientValueCreate]] = None
    units: Optional[List[FoodUnitCreate]] = None


class Food(FoodBase):
    id: int
    updated_at: datetime
    nutritional_profile: Optional[NutritionalProfile] = None
    nutrients: List[FoodNutrientValue] = []
    units: List[FoodUnit] = []

    model_config = {
        "from_attributes": True
    }


class FoodSearch(BaseModel):
    id: int
    name: str
    code: str
    category: Optional[str] = None
    food_type: Optional[str] = None
    thumb_image_url: Optional[str] = None
    calory: Optional[float] = None
    protein_fraction: Optional[float] = None
    fat_fraction: Optional[float] = None
    carb_fraction: Optional[float] = None
    hot: Optional[int] = 0  # 热度字段
    
    model_config = {
        "from_attributes": True
    }

# 获取特定营养素的请求模型
class NutrientRequest(BaseModel):
    food_id: int
    nutrient_names: List[str]  # 营养素的中文名称列表，如 ["膳食纤维", "脂肪酸", "叶酸"]

# 营养素数据响应模型
class NutrientResponse(BaseModel):
    name_cn: str
    value: Optional[float] = None
    unit: Optional[str] = None
    unit_name: Optional[str] = None
    precision: Optional[int] = None
    nrv: Optional[float] = None
    category: str
    
    model_config = {
        "from_attributes": True
    }

# 多个营养素数据的响应模型
class NutrientsResponse(BaseModel):
    food_id: int
    nutrients: List[NutrientResponse]
    
    model_config = {
        "from_attributes": True
    } 