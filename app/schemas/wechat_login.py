from pydantic import BaseModel
from typing import Optional, Dict, Any, Union
from app.models.user import Gender
from datetime import datetime

class Token(BaseModel):
    """登录成功返回的Token模型"""
    access_token: str
    token_type: str = "bearer"


class TokenData(BaseModel):
    """令牌数据模型"""
    sub: Optional[str] = None


class UserInfo(BaseModel):
    """用户信息模型"""
    id: int
    nickname: Optional[str] = None
    avatar_url: Optional[str] = None
    phone: Optional[str] = None
    gender: Optional[Gender] = None
    country: Optional[str] = None
    province: Optional[str] = None
    city: Optional[str] = None
    created_at: Optional[datetime] = None
    notification_enabled: bool = True
    
    class Config:
        from_attributes = True


class WechatLoginRequest(BaseModel):
    """微信小程序登录请求模型"""
    code: str
    userInfo: Optional[Dict[str, Any]] = None  # 包含昵称和头像的用户信息
    encryptedData: Optional[str] = None  # 加密的用户数据
    iv: Optional[str] = None  # 加密向量
    nickname: Optional[str] = None  # 向后兼容
    avatar_url: Optional[str] = None  # 向后兼容
    openid: Optional[str] = None  # 用户openid
    unionid: Optional[str] = None  # 用户unionid

    class Config:
        # 允许额外字段，微信可能会传入未定义的字段
        extra = "allow"
        from_attributes = True


class WechatLoginResponse(BaseModel):
    """微信登录响应模型"""
    success: bool = True
    token: str
    expires_at: str  # 新增字段：令牌过期时间，ISO 8601格式
    user: Dict[str, Any]  # 用户信息必须是字典类型
    is_new_user: bool

    class Config:
        from_attributes = True


class WechatPhoneRequest(BaseModel):
    """微信小程序获取手机号请求模型"""
    code: str  # 临时登录凭证
    encrypted_data: str  # 包括敏感数据在内的完整用户信息的加密数据
    iv: str  # 加密算法的初始向量 