from typing import Optional
from pydantic import BaseModel
from datetime import datetime


class UserSettingBase(BaseModel):
    notification_enabled: bool = True


class UserSettingCreate(UserSettingBase):
    user_id: int


class UserSettingUpdate(UserSettingBase):
    pass


class UserSettingInDBBase(UserSettingBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class UserSetting(UserSettingInDBBase):
    pass


class UserSettingInDB(UserSettingInDBBase):
    pass 