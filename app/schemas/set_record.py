from typing import Optional
from pydantic import BaseModel
from datetime import datetime


class SetRecordBase(BaseModel):
    """组记录基础模型"""
    set_number: int
    set_type: str = "normal"  # 组类型：warmup, normal, drop, superset, rest
    weight: Optional[float] = None
    reps: Optional[int] = None
    completed: bool = False
    notes: Optional[str] = None


class SetRecordCreate(SetRecordBase):
    """用于创建组记录的模型"""
    workout_exercise_id: int


class SetRecordUpdate(BaseModel):
    """用于更新组记录的模型"""
    set_number: Optional[int] = None
    set_type: Optional[str] = None
    weight: Optional[float] = None
    reps: Optional[int] = None
    completed: Optional[bool] = None
    notes: Optional[str] = None


class SetRecordInDB(SetRecordBase):
    """数据库中的组记录模型"""
    id: int
    workout_exercise_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
