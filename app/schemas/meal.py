from datetime import datetime, date
from typing import List, Optional, Dict, Any, Union
from enum import Enum

from pydantic import BaseModel, Field


class MealType(str, Enum):
    BREAKFAST = "breakfast"
    LUNCH = "lunch"
    DINNER = "dinner"
    SNACK = "snack"


class FoodItemNutrientIntakeBase(BaseModel):
    name_en: str
    name_cn: str
    value: Optional[float] = None
    unit: Optional[str] = None
    unit_name: Optional[str] = None
    nrv_percentage: Optional[float] = None
    category: str


class FoodItemNutrientIntakeCreate(FoodItemNutrientIntakeBase):
    pass


class FoodItemNutrientIntakeUpdate(BaseModel):
    name_en: Optional[str] = None
    name_cn: Optional[str] = None
    value: Optional[float] = None
    unit: Optional[str] = None
    unit_name: Optional[str] = None
    nrv_percentage: Optional[float] = None
    category: Optional[str] = None


class FoodItemNutrientIntake(FoodItemNutrientIntakeBase):
    id: int
    food_item_id: int

    model_config = {
        "from_attributes": True
    }


class FoodItemBase(BaseModel):
    name: str
    quantity: float = 1.0
    unit_name: str = "份"
    weight: float
    category: Optional[str] = None
    cuisine_type: Optional[str] = None
    cuisine_type_detail: Optional[str] = None
    image_url: Optional[str] = None


class FoodItemCreate(FoodItemBase):
    food_id: Optional[int] = None
    health_light: Optional[int] = None
    lights: Optional[List[str]] = []
    warnings: Optional[List[str]] = []
    warning_scenes: Optional[List[str]] = []
    calory: Optional[float] = None
    protein: Optional[float] = None
    fat: Optional[float] = None
    carbohydrate: Optional[float] = None
    protein_fraction: Optional[float] = None
    fat_fraction: Optional[float] = None
    carb_fraction: Optional[float] = None
    is_custom: bool = False
    nutrient_intakes: Optional[List[FoodItemNutrientIntakeCreate]] = []


class FoodItemUpdate(BaseModel):
    name: Optional[str] = None
    quantity: Optional[float] = None
    unit_name: Optional[str] = None
    weight: Optional[float] = None
    category: Optional[str] = None
    cuisine_type: Optional[str] = None
    cuisine_type_detail: Optional[str] = None
    image_url: Optional[str] = None
    food_id: Optional[int] = None
    calory: Optional[float] = None
    protein: Optional[float] = None
    fat: Optional[float] = None
    carbohydrate: Optional[float] = None
    is_custom: Optional[bool] = None


class FoodItem(FoodItemBase):
    id: int
    meal_record_id: int
    food_id: Optional[int] = None
    health_light: Optional[int] = None
    lights: List[str] = []
    warnings: List[str] = []
    warning_scenes: List[str] = []
    calory: Optional[float] = None
    protein: Optional[float] = None
    fat: Optional[float] = None
    carbohydrate: Optional[float] = None
    protein_fraction: Optional[float] = None
    fat_fraction: Optional[float] = None
    carb_fraction: Optional[float] = None
    is_custom: bool
    created_at: datetime
    updated_at: datetime
    nutrient_intakes: List[FoodItemNutrientIntake] = []

    model_config = {
        "from_attributes": True
    }


class HealthRecommendationBase(BaseModel):
    recommendation_text: str
    recommendation_type: Optional[str] = None
    priority: int = 0


class HealthRecommendationCreate(HealthRecommendationBase):
    pass


class HealthRecommendationUpdate(BaseModel):
    recommendation_text: Optional[str] = None
    recommendation_type: Optional[str] = None
    priority: Optional[int] = None


class HealthRecommendation(HealthRecommendationBase):
    id: int
    meal_record_id: int
    created_at: datetime

    model_config = {
        "from_attributes": True
    }


class MealRecordBase(BaseModel):
    date: date
    meal_type: MealType
    image_url: Optional[str] = None
    file_id: Optional[str] = None
    thumb_image_url: Optional[str] = None
    is_ai_recognized: bool = False


class MealRecordCreate(MealRecordBase):
    pass


class MealRecordUpdate(BaseModel):
    image_url: Optional[str] = None
    file_id: Optional[str] = None
    thumb_image_url: Optional[str] = None
    is_ai_recognized: Optional[bool] = None


class MealRecord(MealRecordBase):
    id: int
    user_id: int
    total_calory: float = 0
    total_protein: float = 0
    total_fat: float = 0
    total_carbohydrate: float = 0
    created_at: datetime
    updated_at: datetime
    food_items: List[FoodItem] = []
    health_recommendations: List[HealthRecommendation] = []

    model_config = {
        "from_attributes": True
    }


class MealRecordSummary(BaseModel):
    id: int
    date: date
    meal_type: MealType
    image_url: Optional[str] = None
    thumb_image_url: Optional[str] = None
    total_calory: float
    total_protein: float
    total_fat: float
    total_carbohydrate: float

    model_config = {
        "from_attributes": True
    }


class DailyNutritionSummary(BaseModel):
    date: date
    total_calory: float = 0
    total_protein: float = 0
    total_fat: float = 0
    total_carbohydrate: float = 0
    meals: List[MealRecordSummary] = []


class FoodRecognitionResponse(BaseModel):
    success: bool
    recognition_id: int
    food_items: List[FoodItemCreate]
    health_recommendation: Optional[str] = None
    image_url: Optional[str] = None


class FoodRecognitionConfirmation(BaseModel):
    food_items: List[FoodItemCreate]
    health_recommendation: Optional[str] = None 