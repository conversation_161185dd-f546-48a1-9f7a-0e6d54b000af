from typing import Optional, List, Dict, Any
from pydantic import BaseModel, EmailStr, validator, Field
from datetime import datetime
from app.models.user import Gender, ExperienceLevel, FitnessGoal

class TokenData(BaseModel):
    phone: str

class UserBase(BaseModel):
    nickname: Optional[str] = None
    avatar_url: Optional[str] = None
    gender: Optional[int] = None  # 改为直接使用整数类型，而不是枚举
    age: Optional[int] = None
    weight: Optional[float] = None
    height: Optional[float] = None
    activity_level: Optional[int] = Field(default=3, ge=1, le=5)  # 1-5
    health_conditions: Optional[List[str]] = None  # 健康状况记录
    allergies: Optional[List[str]] = None  # 过敏源记录

class UserCreate(UserBase):
    openid: str
    unionid: Optional[str] = None
    session_key: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None

class UserUpdate(UserBase):
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    country: Optional[str] = None
    province: Optional[str] = None
    city: Optional[str] = None
    birthday: Optional[datetime] = None
    experience_level: Optional[ExperienceLevel] = None
    fitness_goal: Optional[FitnessGoal] = None
    
    # 添加一个model_config用于替代旧版的Config类
    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "nickname": "用户昵称",
                    "gender": 1,  # 1-男, 2-女
                    "age": 30,
                    "weight": 70.5,
                    "height": 175.0,
                    "activity_level": 3,
                    "country": "中国",
                    "province": "广东",
                    "city": "深圳",
                    "health_conditions": ["轻度高血压"],
                    "allergies": ["乳制品"]
                }
            ]
        }
    }

class UserInDBBase(UserBase):
    id: int
    openid: str
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    country: Optional[str] = None
    province: Optional[str] = None
    city: Optional[str] = None
    bmi: Optional[float] = None
    tedd: Optional[int] = None
    completed: bool = False
    created_at: datetime
    updated_at: datetime

    model_config = {
        "from_attributes": True
    }

class User(UserInDBBase):
    pass

class UserInDB(UserInDBBase):
    hashed_password: Optional[str] = None
    unionid: Optional[str] = None
    session_key: Optional[str] = None

class Token(BaseModel):
    access_token: str
    token_type: str = "bearer"

class TokenPayload(BaseModel):
    sub: int  # 用户ID
    exp: datetime  # 过期时间

class UserLogin(BaseModel):
    code: str  # 微信授权后返回的临时code
    userInfo: Optional[Dict[str, Any]] = None  # 包含昵称和头像的用户信息
    encryptedData: Optional[str] = None  # 加密的用户数据
    iv: Optional[str] = None  # 加密向量
    nickname: Optional[str] = None
    avatar_url: Optional[str] = None
    openid: Optional[str] = None  # 用户openid
    unionid: Optional[str] = None  # 用户unionid
    
    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "code": "wx_temporary_code"
                }
            ]
        },
        "extra": "allow"  # 允许额外字段，微信可能会传入未定义的字段
    }

class AvatarResponse(BaseModel):
    success: bool
    avatar_url: str

class UserProfile(BaseModel):
    id: int
    nickname: Optional[str] = None
    avatar_url: Optional[str] = None
    gender: Optional[int] = None  # 改为直接使用整数类型，而不是枚举
    age: Optional[int] = None
    weight: Optional[float] = None
    height: Optional[float] = None
    activity_level: int = 3
    bmi: Optional[float] = None
    tedd: Optional[int] = None
    completed: bool = False
    country: Optional[str] = None
    province: Optional[str] = None
    city: Optional[str] = None
    experience_level: Optional[ExperienceLevel] = None
    fitness_goal: Optional[FitnessGoal] = None
    health_conditions: Optional[List[str]] = None  # 健康状况记录
    allergies: Optional[List[str]] = None  # 过敏源记录
    created_at: Optional[datetime] = None
    birthday: Optional[datetime] = None

    model_config = {
        "from_attributes": True
    }
    
    # 添加一个方法，用于从对象属性中获取数据
    @classmethod
    def model_validate(cls, obj, *args, **kwargs):
        # 如果obj有gender_int属性，保存该值以便使用
        gender_int_value = None
        if hasattr(obj, 'gender_int'):
            gender_int_value = getattr(obj, 'gender_int')
            
        # 使用标准验证创建模型实例
        instance = super().model_validate(obj, *args, **kwargs)
        
        # 如果有gender_int值，直接使用它而不是转换为枚举
        if gender_int_value is not None:
            instance.gender = gender_int_value
        # 如果没有gender_int但有gender，尝试将枚举或字符串转换为整数
        elif instance.gender is not None:
            gender_value = instance.gender
            if isinstance(gender_value, Gender):
                # 枚举对象转为整数
                instance.gender = gender_value.value
            elif isinstance(gender_value, str):
                # 字符串枚举值转为整数
                gender_map = {'UNKNOWN': 0, 'MALE': 1, 'FEMALE': 2}
                if gender_value in gender_map:
                    instance.gender = gender_map[gender_value]
                else:
                    instance.gender = 0  # 默认为UNKNOWN
            elif not isinstance(gender_value, int):
                # 其他类型设为默认值
                instance.gender = 0
                
        return instance 

class UserUpdateProfile(UserBase):
    country: Optional[str] = None
    province: Optional[str] = None
    city: Optional[str] = None
    birthday: Optional[datetime] = None
    experience_level: Optional[ExperienceLevel] = None
    fitness_goal: Optional[FitnessGoal] = None
    
    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "nickname": "新昵称",
                    "gender": 1,
                    "age": 30,
                    "weight": 70.5,
                    "height": 175.0,
                    "activity_level": 3,
                    "country": "中国",
                    "province": "广东",
                    "city": "深圳",
                    "health_conditions": ["轻度高血压"],
                    "allergies": ["乳制品"]
                }
            ]
        }
    }

class UserListResponse(BaseModel):
    total: int
    items: List[User]
    
    model_config = {
        "from_attributes": True
    }
    
class UserPasswordUpdate(BaseModel):
    phone: str  # 手机号用于验证身份
    verify_code: str  # 验证码
    new_password: Optional[str] = None  # 可选的新密码，微信登录可能不需要
    code: Optional[str] = None  # 临时登录凭证，用于微信获取手机号
    encrypted_data: Optional[str] = None  # 包括敏感数据在内的完整用户信息的加密数据
    iv: Optional[str] = None  # 加密算法的初始向量
    
    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "phone": "13800138000",
                    "verify_code": "123456",
                    "new_password": "new_password123"
                }
            ]
        }
    }

class FoodImageResponse(BaseModel):
    """食物图像上传响应模型"""
    success: bool
    image_url: str
    filename: str
    food_name: str 