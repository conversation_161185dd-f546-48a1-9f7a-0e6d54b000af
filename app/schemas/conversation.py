from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
from datetime import datetime

# 共享属性
class ConversationBase(BaseModel):
    session_id: Optional[str] = None
    meta_info: Optional[Dict[str, Any]] = None
    user_id: Optional[int] = None
    is_active: Optional[bool] = True

# 创建时使用
class ConversationCreate(ConversationBase):
    pass

# 更新时使用
class ConversationUpdate(BaseModel):
    is_active: Optional[bool] = None
    meta_info: Optional[Dict[str, Any]] = None

# 数据库中的完整对象
class ConversationInDBBase(ConversationBase):
    id: int
    user_id: int
    session_id: str
    start_time: datetime
    last_active: datetime
    is_active: bool

    class Config:
        from_attributes = True  # 新版Pydantic用from_attributes代替orm_mode

# API返回对象
class Conversation(ConversationInDBBase):
    pass

# 对话列表
class ConversationList(BaseModel):
    conversations: List[Conversation]
    total: int