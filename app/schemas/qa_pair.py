from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
from datetime import datetime

# 共享属性
class QAPairBase(BaseModel):
    question: str
    answer: str
    intent: Optional[str] = None
    session_id: Optional[str] = None
    retrieved_docs: Optional[Dict[str, Any]] = None
    tool_used: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

# 创建时使用
class QAPairCreate(QAPairBase):
    user_id: int

# 更新时使用
class QAPairUpdate(BaseModel):
    feedback_score: Optional[int] = Field(None, ge=1, le=5)

# 数据库中的完整对象
class QAPairInDBBase(QAPairBase):
    id: int
    user_id: int
    created_at: datetime
    feedback_score: Optional[int] = None

    class Config:
        from_attributes = True

# API返回对象
class QAPair(QAPairInDBBase):
    pass

# QA对列表
class QAPairList(BaseModel):
    qa_pairs: List[QAPair]
    total: int 