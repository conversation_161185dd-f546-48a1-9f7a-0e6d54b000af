from typing import List, Optional, Dict, Any, ClassVar, Type, TYPE_CHECKING
from pydantic import BaseModel, Field
from datetime import datetime

if TYPE_CHECKING:
    from app.schemas.user import User

# 共享属性基类
class UserTrainingRecordBase(BaseModel):
    date: Optional[datetime] = Field(None, description="训练日期")
    body_parts: Optional[List[str]] = Field(None, description="训练的身体部位")
    duration_minutes: Optional[int] = Field(None, description="训练时长(分钟)")
    exercises_data: Optional[Dict[str, Any]] = Field(None, description="训练的动作详情JSON")
    notes: Optional[str] = Field(None, description="训练笔记")
    
    # 基础训练信息
    training_age: Optional[int] = Field(None, description="训练年龄（月）")
    preferred_training_days: Optional[int] = Field(None, description="每周首选训练天数")
    
    # 体能和力量指标
    squat_max: Optional[float] = Field(None, description="深蹲最大重量")
    bench_press_max: Optional[float] = Field(None, description="卧推最大重量")
    deadlift_max: Optional[float] = Field(None, description="硬拉最大重量")
    
    # 训练偏好
    preferred_exercise_types: Optional[Dict[str, Any]] = Field(None, description="首选运动类型")
    avoided_exercise_types: Optional[Dict[str, Any]] = Field(None, description="避免的运动类型")
    
    # 目标数据
    fitness_goals: Optional[Dict[str, Any]] = Field(None, description="健身目标")
    target_muscle_groups: Optional[Dict[str, Any]] = Field(None, description="目标肌肉群")
    
    # 健康信息
    injuries: Optional[Dict[str, Any]] = Field(None, description="伤病记录")
    medical_conditions: Optional[Dict[str, Any]] = Field(None, description="医疗状况")


# 创建请求模型
class UserTrainingRecordCreate(UserTrainingRecordBase):
    pass


# 更新请求模型
class UserTrainingRecordUpdate(UserTrainingRecordBase):
    pass


# 数据库模型响应
class UserTrainingRecordInDBBase(UserTrainingRecordBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True  # 替换了 orm_mode = True


# API响应模型
class UserTrainingRecord(UserTrainingRecordInDBBase):
    pass


# 带有用户详情的响应模型
class UserTrainingRecordWithUser(UserTrainingRecordInDBBase):
    # 正确的导入方式，避免循环导入
    user: "User"  # 作为字符串引用，避免循环导入问题 