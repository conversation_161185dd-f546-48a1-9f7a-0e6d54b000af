from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime


# 用户货币相关模式
class CurrencyBase(BaseModel):
    """用户货币基础模式"""
    amount: int = Field(0, ge=0, description="当前余额")
    lifetime_earned: int = Field(0, ge=0, description="历史总收入")
    daily_earned_today: int = Field(0, ge=0, description="今日已获取")


class CurrencyCreate(CurrencyBase):
    """创建用户货币的输入模式"""
    user_id: int


class CurrencyUpdate(BaseModel):
    """更新用户货币的输入模式"""
    amount: Optional[int] = Field(None, ge=0, description="当前余额")
    daily_earned_today: Optional[int] = Field(None, ge=0, description="今日已获取")
    last_reset_date: Optional[datetime] = Field(None, description="上次重置每日计数的日期")


class CurrencyInDB(CurrencyBase):
    """用户货币数据库模式"""
    id: int
    user_id: int
    last_reset_date: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# 货币交易相关模式
class CurrencyTransactionBase(BaseModel):
    """货币交易基础模式"""
    amount: int = Field(..., description="交易金额，正数为获得，负数为消费")
    description: str = Field(..., description="交易描述")
    transaction_type: str = Field(..., description="交易类型：task_reward, achievement, level_up, purchase, etc.")
    related_entity_type: Optional[str] = Field(None, description="关联实体类型：task, achievement, shop_item, etc.")
    related_entity_id: Optional[int] = Field(None, description="关联实体ID")


class CurrencyTransactionCreate(CurrencyTransactionBase):
    """创建货币交易的输入模式"""
    user_id: int
    currency_id: int
    balance_after: int = Field(..., ge=0, description="交易后余额")


class CurrencyTransactionInDB(CurrencyTransactionBase):
    """货币交易数据库模式"""
    id: int
    user_id: int
    currency_id: int
    balance_after: int
    created_at: datetime

    class Config:
        from_attributes = True


# 商店物品相关模式
class ShopItemBase(BaseModel):
    """商店物品基础模式"""
    name: str = Field(..., description="商品名称")
    description: Optional[str] = Field(None, description="商品描述")
    image_url: Optional[str] = Field(None, description="商品图片URL")
    category: str = Field(..., description="商品类别：virtual, physical")
    subcategory: Optional[str] = Field(None, description="子类别：appearance, equipment, nutrition, etc.")
    price: int = Field(..., gt=0, description="价格（像素杠铃）")
    stock: Optional[int] = Field(None, ge=0, description="库存数量，null表示无限库存")
    is_limited: bool = Field(False, description="是否限定商品")
    is_active: bool = Field(True, description="是否在售")
    
    # 折扣相关
    discount_level_type: Optional[str] = Field(None, description="折扣等级类型：exercise, diet")
    discount_level_required: Optional[int] = Field(None, ge=1, le=10, description="折扣所需等级")
    discount_percentage: Optional[int] = Field(None, ge=0, le=100, description="折扣百分比，如20表示8折")
    
    # 时间相关
    available_from: Optional[datetime] = Field(None, description="开始销售时间")
    available_until: Optional[datetime] = Field(None, description="结束销售时间")
    
    # 关联卡片
    card_id: Optional[int] = Field(None, description="关联卡片ID（如果购买的是卡片）")


class ShopItemCreate(ShopItemBase):
    """创建商店物品的输入模式"""
    pass


class ShopItemUpdate(BaseModel):
    """更新商店物品的输入模式"""
    name: Optional[str] = Field(None, description="商品名称")
    description: Optional[str] = Field(None, description="商品描述")
    image_url: Optional[str] = Field(None, description="商品图片URL")
    price: Optional[int] = Field(None, gt=0, description="价格（像素杠铃）")
    stock: Optional[int] = Field(None, ge=0, description="库存数量")
    is_limited: Optional[bool] = Field(None, description="是否限定商品")
    is_active: Optional[bool] = Field(None, description="是否在售")
    
    # 折扣相关
    discount_level_type: Optional[str] = Field(None, description="折扣等级类型：exercise, diet")
    discount_level_required: Optional[int] = Field(None, ge=1, le=10, description="折扣所需等级")
    discount_percentage: Optional[int] = Field(None, ge=0, le=100, description="折扣百分比")
    
    # 时间相关
    available_from: Optional[datetime] = Field(None, description="开始销售时间")
    available_until: Optional[datetime] = Field(None, description="结束销售时间")


class ShopItemInDB(ShopItemBase):
    """商店物品数据库模式"""
    id: int
    created_at: datetime

    class Config:
        from_attributes = True


# 用户购买相关模式
class UserPurchaseBase(BaseModel):
    """用户购买基础模式"""
    item_id: int = Field(..., description="商品ID")
    quantity: int = Field(1, ge=1, description="购买数量")
    price_paid: int = Field(..., ge=0, description="支付价格")
    total_paid: int = Field(..., ge=0, description="总支付金额")
    discount_applied: float = Field(0, ge=0, le=1, description="应用的折扣比例")
    status: str = Field("completed", description="订单状态：completed, pending, shipped, etc.")
    shipping_address: Optional[str] = Field(None, description="物流地址（实物商品）")
    tracking_number: Optional[str] = Field(None, description="物流单号（实物商品）")


class UserPurchaseCreate(UserPurchaseBase):
    """创建用户购买的输入模式"""
    user_id: int
    transaction_id: Optional[int] = Field(None, description="关联的交易记录ID")


class UserPurchaseUpdate(BaseModel):
    """更新用户购买的输入模式"""
    status: Optional[str] = Field(None, description="订单状态")
    shipping_address: Optional[str] = Field(None, description="物流地址")
    tracking_number: Optional[str] = Field(None, description="物流单号")


class UserPurchaseInDB(UserPurchaseBase):
    """用户购买数据库模式"""
    id: int
    user_id: int
    transaction_id: Optional[int] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# 响应模式 - 添加这些新模式用于API响应
class CurrencyResponse(CurrencyInDB):
    """虚拟货币响应模式"""
    pass


class CurrencyTransactionResponse(CurrencyTransactionInDB):
    """交易记录响应模式"""
    pass


class ShopItemResponse(ShopItemInDB):
    """商店物品响应模式"""
    discounted_price: Optional[int] = None
    
    class Config:
        from_attributes = True


class UserPurchaseResponse(UserPurchaseInDB):
    """用户购买记录响应模式"""
    shop_item: Optional[ShopItemResponse] = None
    
    class Config:
        from_attributes = True
