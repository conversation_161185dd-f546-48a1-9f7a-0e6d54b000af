from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class CardTypeEnum(str, Enum):
    """卡片类型枚举"""
    FOOD = "food"
    EQUIPMENT = "equipment"
    SPECIAL = "special"


# 卡片基础模式
class CardBase(BaseModel):
    """卡片基础模式"""
    name: str = Field(..., description="卡片名称")
    description: Optional[str] = Field(None, description="卡片描述")
    image_url: Optional[str] = Field(None, description="卡片图片URL")
    card_type: CardTypeEnum = Field(..., description="卡片类型")
    rarity: int = Field(1, ge=1, le=5, description="稀有度，1-5星级")
    
    # 属性加成
    strength_bonus: int = Field(0, ge=0, description="力量属性加成")
    endurance_bonus: int = Field(0, ge=0, description="耐力属性加成")
    flexibility_bonus: int = Field(0, ge=0, description="灵活性属性加成")
    nutrition_knowledge_bonus: int = Field(0, ge=0, description="营养知识属性加成")
    cooking_skill_bonus: int = Field(0, ge=0, description="烹饪技巧属性加成")
    diet_planning_bonus: int = Field(0, ge=0, description="饮食规划属性加成")
    
    # 其他属性
    duration_hours: Optional[int] = Field(None, ge=1, description="效果持续时间（小时），不填表示永久")
    is_active: bool = Field(True, description="卡片是否可获取")


class CardCreate(CardBase):
    """创建卡片的输入模式"""
    pass


class CardUpdate(BaseModel):
    """更新卡片的输入模式"""
    name: Optional[str] = Field(None, description="卡片名称")
    description: Optional[str] = Field(None, description="卡片描述")
    image_url: Optional[str] = Field(None, description="卡片图片URL")
    rarity: Optional[int] = Field(None, ge=1, le=5, description="稀有度，1-5星级")
    
    # 属性加成
    strength_bonus: Optional[int] = Field(None, ge=0, description="力量属性加成")
    endurance_bonus: Optional[int] = Field(None, ge=0, description="耐力属性加成")
    flexibility_bonus: Optional[int] = Field(None, ge=0, description="灵活性属性加成")
    nutrition_knowledge_bonus: Optional[int] = Field(None, ge=0, description="营养知识属性加成")
    cooking_skill_bonus: Optional[int] = Field(None, ge=0, description="烹饪技巧属性加成")
    diet_planning_bonus: Optional[int] = Field(None, ge=0, description="饮食规划属性加成")
    
    # 其他属性
    duration_hours: Optional[int] = Field(None, ge=1, description="效果持续时间（小时）")
    is_active: Optional[bool] = Field(None, description="卡片是否可获取")


class CardInDB(CardBase):
    """卡片数据库模式"""
    id: int
    created_at: datetime

    class Config:
        from_attributes = True


# 用户卡片相关模式
class UserCardBase(BaseModel):
    """用户卡片基础模式"""
    card_id: int = Field(..., description="卡片ID")
    quantity: int = Field(1, ge=1, description="拥有数量")
    is_equipped: bool = Field(False, description="是否已装备")


class UserCardCreate(UserCardBase):
    """创建用户卡片的输入模式"""
    user_id: int
    expires_at: Optional[datetime] = Field(None, description="过期时间")


class UserCardUpdate(BaseModel):
    """更新用户卡片的输入模式"""
    quantity: Optional[int] = Field(None, ge=0, description="拥有数量")
    is_equipped: Optional[bool] = Field(None, description="是否已装备")
    expires_at: Optional[datetime] = Field(None, description="过期时间")


class UserCardInDB(UserCardBase):
    """用户卡片数据库模式"""
    id: int
    user_id: int
    obtained_at: datetime
    expires_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# 卡片合成相关模式
class CardSynthesisIngredientBase(BaseModel):
    """卡片合成材料基础模式"""
    card_id: int = Field(..., description="材料卡片ID")
    quantity: int = Field(1, ge=1, description="需要数量")


class CardSynthesisIngredientCreate(CardSynthesisIngredientBase):
    """创建卡片合成材料的输入模式"""
    recipe_id: int


class CardSynthesisIngredientUpdate(BaseModel):
    """更新卡片合成材料的输入模式"""
    quantity: Optional[int] = Field(None, ge=1, description="需要数量")


class CardSynthesisIngredientInDB(CardSynthesisIngredientBase):
    """卡片合成材料数据库模式"""
    id: int
    recipe_id: int

    class Config:
        from_attributes = True


class CardSynthesisRecipeBase(BaseModel):
    """卡片合成配方基础模式"""
    result_card_id: int = Field(..., description="结果卡片ID")
    description: Optional[str] = Field(None, description="配方描述")
    is_active: bool = Field(True, description="配方是否可用")


class CardSynthesisRecipeCreate(CardSynthesisRecipeBase):
    """创建卡片合成配方的输入模式"""
    ingredients: List[CardSynthesisIngredientCreate] = Field(..., description="材料列表")


class CardSynthesisRecipeUpdate(BaseModel):
    """更新卡片合成配方的输入模式"""
    description: Optional[str] = Field(None, description="配方描述")
    is_active: Optional[bool] = Field(None, description="配方是否可用")


class CardSynthesisRecipeInDB(CardSynthesisRecipeBase):
    """卡片合成配方数据库模式"""
    id: int
    created_at: datetime
    ingredients: List[CardSynthesisIngredientInDB] = []

    class Config:
        from_attributes = True


# 卡片详细信息，用于前端展示
class CardDetail(CardInDB):
    """卡片详细信息，包含合成配方"""
    synthesis_recipe: Optional[CardSynthesisRecipeInDB] = None


# 用户卡片详细信息，包含卡片信息
class UserCardDetail(UserCardInDB):
    """用户卡片详细信息，包含卡片信息"""
    card: CardInDB

    class Config:
        from_attributes = True


# 卡片合成请求
class SynthesisRequest(BaseModel):
    """卡片合成请求"""
    recipe_id: int = Field(..., description="合成配方ID") 


# 响应模式 - 添加这些新模式用于API响应
class CardResponse(CardInDB):
    """卡片响应模式"""
    pass


class UserCardResponse(UserCardInDB):
    """用户卡片响应模式"""
    card: Optional[CardResponse] = None
    
    class Config:
        from_attributes = True


class CardSynthesisIngredientResponse(CardSynthesisIngredientInDB):
    """卡片合成材料响应模式"""
    pass


class CardSynthesisRecipeResponse(CardSynthesisRecipeInDB):
    """卡片合成配方响应模式"""
    pass


class CardEffectsResponse(BaseModel):
    """卡片效果响应模式"""
    strength_bonus: float = 0.0
    endurance_bonus: float = 0.0
    flexibility_bonus: float = 0.0
    nutrition_bonus: float = 0.0
    cooking_bonus: float = 0.0
    planning_bonus: float = 0.0
    exp_gain_bonus: float = 0.0
    currency_gain_bonus: float = 0.0 