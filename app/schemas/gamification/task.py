from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum


class TaskTypeEnum(str, Enum):
    """任务类型枚举"""
    DAILY = "daily"
    WEEKLY = "weekly"
    CHALLENGE = "challenge"


class TaskCategoryEnum(str, Enum):
    """任务类别枚举"""
    WORKOUT = "workout"
    DIET = "diet"
    SOCIAL = "social"
    SYSTEM = "system"


# 任务相关模式
class TaskBase(BaseModel):
    """任务基础模式"""
    name: str = Field(..., description="任务名称")
    description: str = Field(..., description="任务描述")
    task_type: TaskTypeEnum = Field(..., description="任务类型")
    category: TaskCategoryEnum = Field(..., description="任务类别")
    difficulty: int = Field(1, ge=1, le=5, description="任务难度，1-5星")
    
    # 任务目标
    target_action: str = Field(..., description="目标动作")
    target_value: int = Field(1, ge=1, description="目标值")
    
    # 任务奖励
    currency_reward: int = Field(0, ge=0, description="虚拟货币奖励")
    experience_reward_type: Optional[str] = Field("both", description="经验值类型：exercise, diet, both")
    experience_reward_value: int = Field(0, ge=0, description="经验值奖励")
    
    # 其他属性
    is_active: bool = Field(True, description="是否激活")
    icon_url: Optional[str] = Field(None, description="任务图标URL")


class TaskCreate(TaskBase):
    """创建任务的输入模式"""
    pass


class TaskUpdate(BaseModel):
    """更新任务的输入模式"""
    name: Optional[str] = Field(None, description="任务名称")
    description: Optional[str] = Field(None, description="任务描述")
    task_type: Optional[TaskTypeEnum] = Field(None, description="任务类型")
    category: Optional[TaskCategoryEnum] = Field(None, description="任务类别")
    difficulty: Optional[int] = Field(None, ge=1, le=5, description="任务难度，1-5星")
    
    # 任务目标
    target_action: Optional[str] = Field(None, description="目标动作")
    target_value: Optional[int] = Field(None, ge=1, description="目标值")
    
    # 任务奖励
    currency_reward: Optional[int] = Field(None, ge=0, description="虚拟货币奖励")
    experience_reward_type: Optional[str] = Field(None, description="经验值类型")
    experience_reward_value: Optional[int] = Field(None, ge=0, description="经验值奖励")
    
    # 其他属性
    is_active: Optional[bool] = Field(None, description="是否激活")
    icon_url: Optional[str] = Field(None, description="任务图标URL")


class TaskInDB(TaskBase):
    """任务数据库模式"""
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# 用户任务相关模式
class UserTaskBase(BaseModel):
    """用户任务基础模式"""
    task_id: int = Field(..., description="任务ID")
    progress: int = Field(0, ge=0, description="当前进度")
    completed: bool = Field(False, description="是否完成")
    reward_claimed: bool = Field(False, description="是否已领取奖励")
    expires_at: Optional[datetime] = Field(None, description="过期时间")


class UserTaskCreate(UserTaskBase):
    """创建用户任务的输入模式"""
    user_id: int


class UserTaskUpdate(BaseModel):
    """更新用户任务的输入模式"""
    progress: Optional[int] = Field(None, ge=0, description="当前进度")
    completed: Optional[bool] = Field(None, description="是否完成")
    reward_claimed: Optional[bool] = Field(None, description="是否已领取奖励")


class UserTaskInDB(UserTaskBase):
    """用户任务数据库模式"""
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime
    completed_at: Optional[datetime] = None
    claimed_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# 每日签到相关模式
class DailyCheckInBase(BaseModel):
    """每日签到基础模式"""
    checkin_date: datetime = Field(..., description="签到日期")
    streak_count: int = Field(1, ge=1, description="连续签到天数")
    currency_reward: int = Field(0, ge=0, description="虚拟货币奖励")
    experience_reward_type: Optional[str] = Field("both", description="经验值类型")
    experience_reward_value: int = Field(0, ge=0, description="经验值奖励")


class DailyCheckInCreate(DailyCheckInBase):
    """创建每日签到的输入模式"""
    user_id: int


class DailyCheckInUpdate(BaseModel):
    """更新每日签到的输入模式"""
    streak_count: Optional[int] = Field(None, ge=1, description="连续签到天数")
    currency_reward: Optional[int] = Field(None, ge=0, description="虚拟货币奖励")
    experience_reward_value: Optional[int] = Field(None, ge=0, description="经验值奖励")


class DailyCheckInInDB(DailyCheckInBase):
    """每日签到数据库模式"""
    id: int
    user_id: int
    created_at: datetime

    class Config:
        from_attributes = True


# 任务详细信息，用于前端展示
class TaskDetail(TaskInDB):
    """任务详细信息，包含用户进度"""
    user_progress: Optional[UserTaskInDB] = None


# 生成用户任务请求
class GenerateUserTasksRequest(BaseModel):
    """生成用户任务请求"""
    task_type: TaskTypeEnum = Field(..., description="任务类型")
    count: int = Field(5, ge=1, le=10, description="生成数量")


# 响应模式 - 添加这些新模式用于API响应
class TaskResponse(TaskInDB):
    """任务响应模式"""
    pass


class UserTaskResponse(UserTaskInDB):
    """用户任务响应模式"""
    task: Optional[TaskResponse] = None
    
    class Config:
        from_attributes = True


class DailyCheckInResponse(DailyCheckInInDB):
    """每日签到响应模式"""
    pass


class TaskUpdateRequest(BaseModel):
    """任务更新请求"""
    task_id: int = Field(..., description="任务ID")
    progress_increment: int = Field(1, ge=1, description="进度增量") 