# 导入所有游戏化系统验证模式
from app.schemas.gamification.level import (
    UserLevelBase, UserLevelCreate, UserLevelUpdate, UserLevelInDB,
    UserAttributeBase, UserAttributeCreate, UserAttributeUpdate, UserAttributeInDB,
    UserTitleBase, UserTitleCreate, UserTitleUpdate, UserTitleInDB,
    UserGameProfile, ExperienceGain,
    # 响应模式
    UserLevelResponse, UserAttributeResponse, UserTitleResponse, 
    UserLevelDetailResponse, ExperienceGainRequest
)

from app.schemas.gamification.card import (
    CardTypeEnum, CardBase, CardCreate, CardUpdate, CardInDB,
    UserCardBase, UserCardCreate, UserCardUpdate, UserCardInDB,
    CardSynthesisIngredientBase, CardSynthesisIngredientCreate, CardSynthesisIngredientUpdate, CardSynthesisIngredientInDB,
    CardSynthesisRecipeBase, CardSynthesisRecipeCreate, CardSynthesisRecipeUpdate, CardSynthesisRecipeInDB,
    CardDetail, UserCardDetail, SynthesisRequest,
    # 响应模式
    CardResponse, UserCardResponse, CardSynthesisIngredientResponse,
    CardSynthesisRecipeResponse, CardEffectsResponse
)

from app.schemas.gamification.currency import (
    CurrencyBase, CurrencyCreate, CurrencyUpdate, CurrencyInDB,
    CurrencyTransactionBase, CurrencyTransactionCreate, CurrencyTransactionInDB,
    ShopItemBase, ShopItemCreate, ShopItemUpdate, ShopItemInDB,
    UserPurchaseBase, UserPurchaseCreate, UserPurchaseUpdate, UserPurchaseInDB,
    # 响应模式
    CurrencyResponse, CurrencyTransactionResponse, ShopItemResponse, UserPurchaseResponse
)

from app.schemas.gamification.currency_extra import (
    PurchaseRequest, CurrencyGain
)

from app.schemas.gamification.achievement import (
    AchievementBase, AchievementCreate, AchievementUpdate, AchievementInDB,
    UserAchievementBase, UserAchievementCreate, UserAchievementUpdate, UserAchievementInDB,
    MilestoneBase, MilestoneCreate, MilestoneUpdate, MilestoneInDB,
    UserMilestoneBase, UserMilestoneCreate, UserMilestoneUpdate, UserMilestoneInDB,
    AchievementDetail, MilestoneDetail,
    # 响应模式
    AchievementResponse, UserAchievementResponse, MilestoneResponse, UserMilestoneResponse
)

from app.schemas.gamification.task import (
    TaskTypeEnum, TaskCategoryEnum, TaskBase, TaskCreate, TaskUpdate, TaskInDB,
    UserTaskBase, UserTaskCreate, UserTaskUpdate, UserTaskInDB,
    DailyCheckInBase, DailyCheckInCreate, DailyCheckInUpdate, DailyCheckInInDB,
    TaskDetail, GenerateUserTasksRequest,
    # 响应模式
    TaskResponse, UserTaskResponse, DailyCheckInResponse, TaskUpdateRequest
)
