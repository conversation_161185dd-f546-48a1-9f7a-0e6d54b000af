from pydantic import BaseModel, Field
from typing import Optional

# 购买请求模式
class PurchaseRequest(BaseModel):
    """购买请求模式"""
    item_id: int = Field(..., description="商品ID")
    quantity: int = Field(1, ge=1, description="购买数量")
    shipping_address: Optional[str] = Field(None, description="物流地址（实物商品）")


# 货币获取模式
class CurrencyGain(BaseModel):
    """货币获取模式"""
    amount: int = Field(..., gt=0, description="获取的货币数量")
    source: str = Field(..., description="货币来源：task, achievement, check_in, etc.")
    source_id: Optional[int] = Field(None, description="来源ID")
    description: str = Field(..., description="获取描述") 