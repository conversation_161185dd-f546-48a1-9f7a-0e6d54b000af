from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime


# 属性相关模式
class UserAttributeBase(BaseModel):
    """用户属性基础模式"""
    strength: int = Field(10, ge=1, description="力量属性")
    endurance: int = Field(10, ge=1, description="耐力属性")
    flexibility: int = Field(10, ge=1, description="灵活性属性")
    nutrition_knowledge: int = Field(10, ge=1, description="营养知识属性")
    cooking_skill: int = Field(10, ge=1, description="烹饪技巧属性")
    diet_planning: int = Field(10, ge=1, description="饮食规划属性")


class UserAttributeCreate(UserAttributeBase):
    """创建用户属性的输入模式"""
    user_id: int


class UserAttributeUpdate(BaseModel):
    """更新用户属性的输入模式"""
    strength: Optional[int] = Field(None, ge=1, description="力量属性")
    endurance: Optional[int] = Field(None, ge=1, description="耐力属性")
    flexibility: Optional[int] = Field(None, ge=1, description="灵活性属性")
    nutrition_knowledge: Optional[int] = Field(None, ge=1, description="营养知识属性")
    cooking_skill: Optional[int] = Field(None, ge=1, description="烹饪技巧属性")
    diet_planning: Optional[int] = Field(None, ge=1, description="饮食规划属性")


class UserAttributeInDB(UserAttributeBase):
    """用户属性数据库模式"""
    id: int
    user_id: int
    updated_at: datetime

    class Config:
        from_attributes = True


# 等级相关模式
class UserLevelBase(BaseModel):
    """用户等级基础模式"""
    exercise_level: int = Field(1, ge=1, le=10, description="运动等级")
    exercise_experience: int = Field(0, ge=0, description="运动经验值")
    diet_level: int = Field(1, ge=1, le=10, description="饮食等级")
    diet_experience: int = Field(0, ge=0, description="饮食经验值")


class UserLevelCreate(UserLevelBase):
    """创建用户等级的输入模式"""
    user_id: int


class UserLevelUpdate(BaseModel):
    """更新用户等级的输入模式"""
    exercise_level: Optional[int] = Field(None, ge=1, le=10, description="运动等级")
    exercise_experience: Optional[int] = Field(None, ge=0, description="运动经验值")
    diet_level: Optional[int] = Field(None, ge=1, le=10, description="饮食等级")
    diet_experience: Optional[int] = Field(None, ge=0, description="饮食经验值")


class UserLevelInDB(UserLevelBase):
    """用户等级数据库模式"""
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# 称号相关模式
class UserTitleBase(BaseModel):
    """用户称号基础模式"""
    title_name: str = Field(..., description="称号名称")
    title_type: str = Field(..., description="称号类型: exercise, diet, combined")
    is_active: bool = Field(False, description="是否激活显示")


class UserTitleCreate(UserTitleBase):
    """创建用户称号的输入模式"""
    user_id: int
    level_id: int


class UserTitleUpdate(BaseModel):
    """更新用户称号的输入模式"""
    is_active: Optional[bool] = Field(None, description="是否激活显示")


class UserTitleInDB(UserTitleBase):
    """用户称号数据库模式"""
    id: int
    user_id: int
    level_id: int
    obtained_at: datetime

    class Config:
        from_attributes = True


# 复合响应模式
class UserGameProfile(BaseModel):
    """用户游戏化资料模式，包含等级和属性信息"""
    level: UserLevelInDB
    attributes: UserAttributeInDB
    active_title: Optional[UserTitleInDB] = None
    available_titles: List[UserTitleInDB] = []

    class Config:
        from_attributes = True


# 经验值相关模式
class ExperienceGain(BaseModel):
    """经验值获取模式"""
    experience_type: str = Field(..., description="经验值类型: exercise, diet")
    amount: int = Field(..., gt=0, description="获取的经验值数量")
    source: str = Field(..., description="经验值来源: workout, meal, task, achievement, etc.")
    source_id: Optional[int] = Field(None, description="来源ID")


# 响应模式 - 添加这些新模式用于API响应
class UserLevelResponse(UserLevelInDB):
    """用户等级响应模式"""
    pass


class UserAttributeResponse(UserAttributeInDB):
    """用户属性响应模式"""
    pass


class UserTitleResponse(UserTitleInDB):
    """用户称号响应模式"""
    pass


class UserLevelDetailResponse(UserLevelInDB):
    """用户等级详细响应模式，包含进度信息"""
    exercise_required_exp: int = Field(..., description="升级所需运动经验值")
    exercise_progress: float = Field(..., description="运动等级进度百分比")
    diet_required_exp: int = Field(..., description="升级所需饮食经验值")
    diet_progress: float = Field(..., description="饮食等级进度百分比")


class ExperienceGainRequest(BaseModel):
    """经验值获取请求模式"""
    amount: int = Field(..., gt=0, description="获取的经验值数量") 