from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime


# 成就相关模式
class AchievementBase(BaseModel):
    """成就基础模式"""
    name: str = Field(..., description="成就名称")
    description: str = Field(..., description="成就描述")
    icon_url: Optional[str] = Field(None, description="成就图标URL")
    category: str = Field(..., description="成就类别")
    condition_type: Optional[str] = Field(None, description="条件类型")
    condition_value: Optional[str] = Field(None, description="条件值")
    requirement_value: int = Field(1, ge=1, description="完成所需值")
    is_hidden: bool = Field(False, description="是否隐藏")
    is_active: bool = Field(True, description="是否激活")
    
    # 奖励相关
    currency_reward: Optional[int] = Field(0, ge=0, description="虚拟货币奖励")
    experience_reward: Optional[int] = Field(0, ge=0, description="经验值奖励")
    card_reward_id: Optional[int] = Field(None, description="卡片奖励ID")


class AchievementCreate(AchievementBase):
    """创建成就的输入模式"""
    pass


class AchievementUpdate(BaseModel):
    """更新成就的输入模式"""
    name: Optional[str] = Field(None, description="成就名称")
    description: Optional[str] = Field(None, description="成就描述")
    icon_url: Optional[str] = Field(None, description="成就图标URL")
    category: Optional[str] = Field(None, description="成就类别")
    condition_type: Optional[str] = Field(None, description="条件类型")
    condition_value: Optional[str] = Field(None, description="条件值")
    requirement_value: Optional[int] = Field(None, ge=1, description="完成所需值")
    is_hidden: Optional[bool] = Field(None, description="是否隐藏")
    is_active: Optional[bool] = Field(None, description="是否激活")
    
    # 奖励相关
    currency_reward: Optional[int] = Field(None, ge=0, description="虚拟货币奖励")
    experience_reward: Optional[int] = Field(None, ge=0, description="经验值奖励")
    card_reward_id: Optional[int] = Field(None, description="卡片奖励ID")


class AchievementInDB(AchievementBase):
    """成就数据库模式"""
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# 用户成就相关模式
class UserAchievementBase(BaseModel):
    """用户成就基础模式"""
    achievement_id: int = Field(..., description="成就ID")
    progress: int = Field(0, ge=0, description="进度")
    completed: bool = Field(False, description="是否完成")
    reward_claimed: bool = Field(False, description="是否已领取奖励")


class UserAchievementCreate(UserAchievementBase):
    """创建用户成就的输入模式"""
    user_id: int


class UserAchievementUpdate(BaseModel):
    """更新用户成就的输入模式"""
    progress: Optional[int] = Field(None, ge=0, description="进度")
    completed: Optional[bool] = Field(None, description="是否完成")
    reward_claimed: Optional[bool] = Field(None, description="是否已领取奖励")


class UserAchievementInDB(UserAchievementBase):
    """用户成就数据库模式"""
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime
    completed_at: Optional[datetime] = None
    claimed_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# 里程碑相关模式
class MilestoneBase(BaseModel):
    """里程碑基础模式"""
    name: str = Field(..., description="里程碑名称")
    description: str = Field(..., description="里程碑描述")
    icon_url: Optional[str] = Field(None, description="里程碑图标URL")
    milestone_type: str = Field(..., description="里程碑类型")
    days_required: int = Field(1, ge=1, description="完成所需天数")
    is_active: bool = Field(True, description="是否激活")
    
    # 奖励相关
    currency_reward: Optional[int] = Field(0, ge=0, description="虚拟货币奖励")
    experience_reward: Optional[int] = Field(0, ge=0, description="经验值奖励")
    title_reward: Optional[str] = Field(None, description="称号奖励")


class MilestoneCreate(MilestoneBase):
    """创建里程碑的输入模式"""
    pass


class MilestoneUpdate(BaseModel):
    """更新里程碑的输入模式"""
    name: Optional[str] = Field(None, description="里程碑名称")
    description: Optional[str] = Field(None, description="里程碑描述")
    icon_url: Optional[str] = Field(None, description="里程碑图标URL")
    milestone_type: Optional[str] = Field(None, description="里程碑类型")
    days_required: Optional[int] = Field(None, ge=1, description="完成所需天数")
    is_active: Optional[bool] = Field(None, description="是否激活")
    
    # 奖励相关
    currency_reward: Optional[int] = Field(None, ge=0, description="虚拟货币奖励")
    experience_reward: Optional[int] = Field(None, ge=0, description="经验值奖励")
    title_reward: Optional[str] = Field(None, description="称号奖励")


class MilestoneInDB(MilestoneBase):
    """里程碑数据库模式"""
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# 用户里程碑相关模式
class UserMilestoneBase(BaseModel):
    """用户里程碑基础模式"""
    milestone_id: int = Field(..., description="里程碑ID")
    active_days: int = Field(0, ge=0, description="活跃天数")
    completed: bool = Field(False, description="是否完成")
    reward_claimed: bool = Field(False, description="是否已领取奖励")


class UserMilestoneCreate(UserMilestoneBase):
    """创建用户里程碑的输入模式"""
    user_id: int


class UserMilestoneUpdate(BaseModel):
    """更新用户里程碑的输入模式"""
    active_days: Optional[int] = Field(None, ge=0, description="活跃天数")
    completed: Optional[bool] = Field(None, description="是否完成")
    reward_claimed: Optional[bool] = Field(None, description="是否已领取奖励")


class UserMilestoneInDB(UserMilestoneBase):
    """用户里程碑数据库模式"""
    id: int
    user_id: int
    created_at: datetime
    updated_at: datetime
    last_active_date: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    claimed_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# 详细信息模式
class AchievementDetail(AchievementInDB):
    """成就详细信息"""
    user_progress: Optional[UserAchievementInDB] = None


class MilestoneDetail(MilestoneInDB):
    """里程碑详细信息"""
    user_progress: Optional[UserMilestoneInDB] = None


# 响应模式 - 添加这些新模式用于API响应
class AchievementResponse(AchievementInDB):
    """成就响应模式"""
    pass


class UserAchievementResponse(UserAchievementInDB):
    """用户成就响应模式"""
    achievement: Optional[AchievementResponse] = None
    
    class Config:
        from_attributes = True


class MilestoneResponse(MilestoneInDB):
    """里程碑响应模式"""
    pass


class UserMilestoneResponse(UserMilestoneInDB):
    """用户里程碑响应模式"""
    milestone: Optional[MilestoneResponse] = None
    
    class Config:
        from_attributes = True 