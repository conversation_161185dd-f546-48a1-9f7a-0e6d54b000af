from typing import Optional, Dict, Any, List, Union
from pydantic import BaseModel, Field, ConfigDict
from datetime import datetime
from app.models.message import MessageRole

# 共享属性
class MessageBase(BaseModel):
    content: str
    role: Union[MessageRole, str]  # 允许字符串或枚举
    metadata: Optional[Dict[str, Any]] = Field(default=None, alias="meta_info")

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,  # 允许使用别名
        extra="ignore"          # 忽略额外字段
    )

# 创建时使用
class MessageCreate(MessageBase):
    conversation_id: int
    user_id: int

# 更新时使用
class MessageUpdate(MessageBase):
    pass

# 数据库中的完整对象
class MessageInDBBase(MessageBase):
    id: int
    conversation_id: Optional[int] = None
    user_id: Optional[int] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,  # 允许使用别名
        extra="ignore"          # 忽略额外字段
    )

# API返回对象
class Message(MessageInDBBase):
    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,  # 允许使用别名
        extra="ignore"          # 忽略额外字段
    )

# 消息列表
class MessageList(BaseModel):
    messages: List[Message]
    total: int
    from_cache: Optional[bool] = None
    
    model_config = ConfigDict(from_attributes=True, extra="ignore")

# 会话简要信息
class ConversationBrief(BaseModel):
    """会话简要信息"""
    id: int
    title: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    model_config = ConfigDict(from_attributes=True)

# 包含会话信息的最近消息列表
class RecentMessageList(BaseModel):
    """用于返回最近消息列表及关联会话信息"""
    messages: List[Message]
    conversations: Dict[Union[int, str], Union[ConversationBrief, Dict[str, Any]]]  # 会话ID -> 会话信息
    total: int
    
    model_config = ConfigDict(
        from_attributes=True,
        populate_by_name=True,
        extra="ignore"
    ) 