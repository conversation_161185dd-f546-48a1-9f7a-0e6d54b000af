from typing import List, Optional, Dict, Any, Union
from datetime import datetime, date
from pydantic import BaseModel

from app.schemas.food import FoodBase

# 扩展食物项模型，增加匹配信息和详细数据
class FoodItemCreate(BaseModel):
    """创建食物项的基础模型"""
    name: str
    food_id: Optional[int] = None
    quantity: float = 1
    unit_name: str = "份"
    weight: float = 100
    category: Optional[str] = None
    cuisine_type: Optional[str] = None
    cuisine_type_detail: Optional[str] = None
    calory: float
    protein: float
    fat: float
    carbohydrate: float
    protein_fraction: Optional[float] = None
    fat_fraction: Optional[float] = None
    carb_fraction: Optional[float] = None
    health_light: Optional[int] = None
    lights: Optional[List[str]] = None
    warnings: Optional[List[str]] = None
    warning_scenes: Optional[List[str]] = None
    is_takeout: bool = False
    is_custom: bool = False
    code: Optional[str] = None
    image_url: Optional[str] = None  # 食物项图像URL
    # 新增字段
    matching_info: Optional[Dict[str, Any]] = None
    food_details: Optional[Dict[str, Any]] = None
    nutritional_profile: Optional[Dict[str, Any]] = None

class FoodRecognitionCreate(BaseModel):
    user_id: Union[str, int]
    meal_date: date
    meal_type: str
    image_url: Optional[str] = None
    thumb_image_url: Optional[str] = None
    secure_path: Optional[str] = None
    status: str = "processing"
    recognition_result: Optional[Dict] = None
    matched_foods: Optional[List[Dict[str, Any]]] = None
    nutrition_totals: Optional[Dict] = None

class FoodRecognitionUpdate(BaseModel):
    status: Optional[str] = None
    recognition_result: Optional[Dict[str, Any]] = None
    matched_foods: Optional[List[Dict[str, Any]]] = None
    nutrition_totals: Optional[Dict[str, float]] = None
    meal_record_id: Optional[int] = None
    user_modified: Optional[bool] = None

class FoodRecognition(BaseModel):
    """食物识别记录模型"""
    id: int
    user_id: Union[str, int]
    meal_date: date
    meal_type: str
    image_url: Optional[str] = None
    thumb_image_url: Optional[str] = None
    secure_path: Optional[str] = None
    status: str
    recognition_result: Optional[Dict[str, Any]] = None
    matched_foods: Optional[List[Dict[str, Any]]] = None
    nutrition_totals: Optional[Dict[str, float]] = None
    meal_record_id: Optional[int] = None
    user_modified: bool = False
    created_at: datetime
    updated_at: datetime
    
    model_config = {
        "from_attributes": True
    }

class FoodRecognitionResponse(BaseModel):
    success: bool
    recognition_id: Optional[int] = None
    food_items: Optional[List[FoodItemCreate]] = None
    meal_name: Optional[str] = None
    health_recommendation: Optional[str] = None
    image_url: Optional[str] = None
    thumb_image_url: Optional[str] = None
    nutrition_totals: Optional[Dict] = None
    matched_foods: Optional[List[Dict[str, Any]]] = None
    status: Optional[str] = None
    created_at: Optional[datetime] = None
    meal_date: Optional[date] = None
    meal_type: Optional[str] = None

class NutritionTotals(BaseModel):
    """营养总值模型"""
    total_calory: float
    total_protein: float
    total_fat: float
    total_carbohydrate: float

class MatchedFoodAlternative(BaseModel):
    """匹配食物的替代选项"""
    id: int
    name: str
    calory: float

class MatchedFood(BaseModel):
    """食物匹配结果"""
    name: str
    is_matched: bool
    food_id: Optional[int]
    alternatives: List[MatchedFoodAlternative]

class FoodRecognitionDetailResponse(BaseModel):
    """食物识别详细响应模型"""
    success: bool
    recognition_id: int
    food_items: List[FoodItemCreate]
    meal_name: str
    health_recommendation: Optional[str]
    image_url: str
    nutrition_totals: NutritionTotals
    status: str
    created_at: datetime
    meal_date: date
    meal_type: str

class FoodRecognitionConfirmation(BaseModel):
    """用户确认食物识别结果的请求模型"""
    food_items: List[FoodItemCreate]  # 确认后的食物项列表，包含名称、数量、营养信息等
    meal_name: Optional[str] = None  # 餐食名称，如"早餐鸡蛋组合"
    health_recommendation: Optional[str] = None  # 针对此餐食的健康建议
    nutrition_totals: Optional[Dict[str, float]] = None  # 总营养值，包括总热量、总蛋白质、总脂肪、总碳水化合物
    matched_foods: Optional[List[Dict[str, Any]]] = None  # 匹配到的食物列表

class FoodRecognitionConfirmResponse(BaseModel):
    """确认食物识别结果的响应模型"""
    success: bool  # 操作是否成功
    message: str  # 操作结果消息
    meal_record: Optional[Dict[str, Any]] = None  # 创建的餐食记录，包含ID、日期、餐食类型、图片URL、食物项列表等
    recognition_id: Optional[int] = None  # 原识别记录ID

class FoodRecognitionRejection(BaseModel):
    """拒绝食物识别的请求模型"""
    reason: Optional[str] = None

class FoodRecognitionStats(BaseModel):
    """食物识别统计信息"""
    processing: int = 0
    completed: int = 0
    confirmed: int = 0
    rejected: int = 0
    error: int = 0
    total: int = 0

class FoodRecognitionRejectResponse(BaseModel):
    success: bool
    recognition_id: str
    message: str
    status: str

class Base64ImageRequest(BaseModel):
    """
    Base64编码图片请求模型
    """
    base64_image: str  # Base64编码的图片数据，不包含前缀 (data:image/jpeg;base64,)
    optimize: bool = True 