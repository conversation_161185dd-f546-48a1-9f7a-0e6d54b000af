from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from app.models.team.enums import TeamR<PERSON>, InvitationStatus

# 创建邀请请求
class InvitationCreate(BaseModel):
    invitee_id: int
    role: TeamRole
    expired_at: Optional[datetime] = None

# 邀请响应
class InvitationResponse(BaseModel):
    id: int
    team_id: int
    inviter_id: int
    invitee_id: int
    role: TeamRole
    status: InvitationStatus
    created_at: datetime
    expired_at: Optional[datetime] = None
    team_name: str
    inviter_name: str
    invitee_name: str
    
    class Config:
        orm_mode = True

# 邀请列表响应
class InvitationListResponse(BaseModel):
    id: int
    team_id: int
    inviter_id: int
    invitee_id: int
    role: TeamRole
    status: InvitationStatus
    created_at: datetime
    expired_at: Optional[datetime] = None
    team_name: str
    inviter_name: str
    
    class Config:
        orm_mode = True
