from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from app.models.team.enums import TeamRole, MembershipStatus

# 创建成员关系请求
class MembershipCreate(BaseModel):
    user_id: int
    role: TeamRole
    status: MembershipStatus = MembershipStatus.ACTIVE

# 更新成员关系请求
class MembershipUpdate(BaseModel):
    role: Optional[TeamRole] = None
    status: Optional[MembershipStatus] = None

# 角色更新请求
class RoleUpdate(BaseModel):
    role: TeamRole

# 成员关系响应
class MembershipResponse(BaseModel):
    id: int
    team_id: int
    user_id: int
    role: TeamRole
    status: MembershipStatus
    joined_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True

# 团队成员响应（包含用户信息）
class TeamMemberResponse(BaseModel):
    id: int
    user_id: int
    role: TeamRole
    status: MembershipStatus
    joined_at: datetime
    nickname: str
    avatar_url: Optional[str] = None
    
    class Config:
        orm_mode = True
