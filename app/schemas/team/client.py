from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from app.models.team.enums import ClientStatus

# 分配会员请求
class ClientAssignment(BaseModel):
    client_id: int
    coach_id: int
    status: ClientStatus = ClientStatus.ACTIVE

# 转移会员请求
class ClientTransfer(BaseModel):
    new_coach_id: int
    reason: Optional[str] = None

# 会员关系响应
class ClientRelationResponse(BaseModel):
    id: int
    team_id: int
    client_id: int
    coach_id: int
    status: ClientStatus
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        orm_mode = True

# 会员列表响应
class ClientListResponse(BaseModel):
    id: int
    client_id: int
    coach_id: int
    status: ClientStatus
    client_name: str
    client_avatar: Optional[str] = None
    coach_name: str
    active_plan_count: int
    last_training_date: Optional[datetime] = None
    
    class Config:
        orm_mode = True

# 会员详细信息响应
class ClientDetailResponse(BaseModel):
    id: int
    client_id: int
    coach_id: int
    status: ClientStatus
    created_at: datetime
    client_name: str
    client_avatar: Optional[str] = None
    coach_name: str
    coach_avatar: Optional[str] = None
    stats: Dict[str, Any]
    transfer_history: List[Dict[str, Any]]
    
    class Config:
        orm_mode = True

# 转移记录响应
class TransferResponse(BaseModel):
    id: int
    client_relation_id: int
    from_coach_id: int
    to_coach_id: int
    reason: Optional[str] = None
    transferred_at: datetime
    from_coach_name: str
    to_coach_name: str
    
    class Config:
        orm_mode = True
