from enum import Enum
from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel

class WorkoutStatusEnum(str, Enum):
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    PAUSED = "paused"
    COMPLETED = "completed"

class PauseRecord(BaseModel):
    pause_start: datetime
    pause_end: Optional[datetime] = None
    duration: Optional[int] = None  # 分钟

class StatusRecord(BaseModel):
    status: WorkoutStatusEnum
    timestamp: datetime
    duration: Optional[int] = None  # 该状态持续的分钟数

class WorkoutBase(BaseModel):
    """训练日基础模型"""
    name: str
    day_of_week: Optional[int] = None
    day_number: int
    description: Optional[str] = None
    estimated_duration: Optional[int] = None  # 预计时长（分钟）
    scheduled_date: Optional[datetime] = None  # 计划执行日期
    status: WorkoutStatusEnum = WorkoutStatusEnum.NOT_STARTED

class WorkoutCreate(WorkoutBase):
    """用于创建训练日的模型"""
    training_plan_id: int

class WorkoutUpdate(BaseModel):
    """用于更新训练日的模型"""
    name: Optional[str] = None
    day_of_week: Optional[int] = None
    description: Optional[str] = None
    estimated_duration: Optional[int] = None
    scheduled_date: Optional[datetime] = None
    status: Optional[WorkoutStatusEnum] = None

class WorkoutStatusUpdate(BaseModel):
    """训练状态更新模型"""
    status: WorkoutStatusEnum

class WorkoutInDB(WorkoutBase):
    """数据库中的训练日模型"""
    id: int
    training_plan_id: int
    created_at: datetime
    updated_at: datetime
    actual_duration: Optional[int] = None
    net_duration: Optional[int] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    last_status_change: Optional[datetime] = None
    pause_records: List[Dict[str, Any]] = []
    status_history: List[Dict[str, Any]] = []
    training_session_id: Optional[int] = None

    class Config:
        from_attributes = True

class WorkoutWithExercises(WorkoutInDB):
    """包含训练动作的训练日模型"""
    exercises: List = []  # 这里应引用 WorkoutExerciseWithDetail，但避免循环导入

class WorkoutStatistics(BaseModel):
    """训练统计数据模型"""
    workout_id: int
    status: str
    estimated_duration: Optional[int] = None
    actual_duration: Optional[int] = None
    net_duration: Optional[int] = None
    total_pause_time: int = 0
    pause_count: int = 0
    time_efficiency: float = 0  # 净训练时间/总训练时间的百分比

# 新增的Schema类
class WorkoutExerciseUpdateOrder(BaseModel):
    """用于更新训练动作顺序的模型"""
    id: int
    order: int

class SetRecordUpdateItem(BaseModel):
    """用于更新组记录的模型"""
    id: Optional[int] = None  # 若有ID则更新，无则创建
    set_number: int
    set_type: str = "normal"
    weight: Optional[float] = None
    reps: Optional[int] = None
    completed: Optional[bool] = False
    notes: Optional[str] = None

class WorkoutExerciseFullUpdate(BaseModel):
    """用于全面更新训练动作的模型"""
    id: Optional[int] = None  # 若有ID则更新，无则创建
    exercise_id: Optional[int] = None
    sets: Optional[int] = None
    reps: Optional[str] = None
    rest_seconds: Optional[int] = None
    order: Optional[int] = None
    notes: Optional[str] = None
    exercise_type: Optional[str] = None
    superset_group: Optional[int] = None
    weight: Optional[str] = None
    set_records: Optional[List[SetRecordUpdateItem]] = None
    delete: Optional[bool] = False  # 若为True则删除此训练动作

class WorkoutFullUpdate(BaseModel):
    """用于全面更新训练日的模型，包括基本信息、训练动作和组记录"""
    # 基本信息更新
    name: Optional[str] = None
    day_of_week: Optional[int] = None
    description: Optional[str] = None
    estimated_duration: Optional[int] = None
    scheduled_date: Optional[datetime] = None
    status: Optional[WorkoutStatusEnum] = None
    
    # 训练动作更新
    exercises: Optional[List[WorkoutExerciseFullUpdate]] = None