from app.schemas.user import (
    User, UserCreate, UserUpdate, UserProfile,
    UserBase, AvatarResponse, FoodImageResponse
)

from app.schemas.setting import (
    UserSetting, UserSettingCreate, UserSettingUpdate, UserSettingInDB
)

from app.schemas.wechat_login import (
    WechatLoginResponse, WechatLoginRequest, WechatPhoneRequest,
    Token, TokenData, UserInfo
)

# 基础响应模型
from app.schemas.base import BasicResponse

# 共享和分享记录相关Schema
from app.schemas.share import (
    ShareTrack, QRCodeCreate, QRCodeResponse
)

from app.schemas.share_track import (
    ShareTrackBase, ShareTrackCreate, ShareTrackUpdate, ShareTrackInDB
)

# LangChain相关Schema
from app.schemas.conversation import (
    Conversation, ConversationCreate, ConversationUpdate, ConversationList
)

from app.schemas.message import (
    Message, MessageCreate, MessageUpdate, MessageList,
    MessageBase, MessageInDBBase, RecentMessageList, ConversationBrief
)

from app.schemas.qa_pair import (
    QAPair, QAPairCreate, QAPairUpdate, QAPairList
)

from app.schemas.chat import (
    ChatMessage, ChatRequest, ChatResponse, UserInfoUpdateRequest
)

from app.schemas.food import (
    FoodUnit, FoodUnitCreate, FoodUnitBase,
    FoodNutrientValue, FoodNutrientValueCreate, FoodNutrientValueBase,
    NutritionalProfile, NutritionalProfileCreate, NutritionalProfileBase,
    Food, FoodCreate, FoodUpdate, FoodBase, FoodSearch,
    NutrientRequest, NutrientResponse, NutrientsResponse
)

from app.schemas.meal import (
    MealType,
    FoodItemNutrientIntake, FoodItemNutrientIntakeCreate, FoodItemNutrientIntakeBase,
    FoodItem, FoodItemCreate, FoodItemUpdate, FoodItemBase,
    HealthRecommendation, HealthRecommendationCreate, HealthRecommendationBase,
    MealRecord, MealRecordCreate, MealRecordUpdate, MealRecordBase, MealRecordSummary,
    DailyNutritionSummary
)

from app.schemas.food_recognition import (
    FoodRecognition,
    FoodRecognitionResponse,
    FoodRecognitionConfirmation,
    FoodRecognitionConfirmResponse,
    FoodRecognitionDetailResponse,
    FoodRecognitionStats,
    FoodRecognitionRejection,
    FoodRecognitionRejectResponse,
    Base64ImageRequest,
    FoodRecognitionCreate,
    FoodRecognitionUpdate
)

# 训练记录相关Schema
from app.schemas.training_record import (
    UserTrainingRecord, UserTrainingRecordCreate, UserTrainingRecordUpdate,
    UserTrainingRecordInDBBase, UserTrainingRecordWithUser
)

# 社区相关Schema
from app.schemas.community import (
    # DailyWorkout相关
    DailyWorkoutBase, DailyWorkoutCreate, DailyWorkoutUpdate, DailyWorkoutInDB, DailyWorkoutWithExercises,
    DailyWorkoutExerciseBase, DailyWorkoutExerciseCreate, DailyWorkoutExerciseInDB, DailyWorkoutExerciseWithDetail,

    # Post相关
    PostBase, PostCreate, PostUpdate, PostInDB, PostRead, PostListResponse,

    # Comment相关
    CommentBase, CommentCreate, CommentUpdate, CommentInDB, CommentRead, CommentListResponse,

    # Like相关
    LikeBase, PostLikeCreate, CommentLikeCreate, LikeInDB, PostLikeInDB, CommentLikeInDB,

    # Report相关
    ReportBase, ReportCreate, ReportInDB, ReportRead,

    # Notification相关
    NotificationBase, NotificationCreate, NotificationInDB, NotificationRead, NotificationListResponse,

    # Moderation相关
    ModerationAction
)

# 训练计划相关Schema
from app.schemas.workout import (
    WorkoutBase, WorkoutCreate, WorkoutUpdate, WorkoutInDB, WorkoutWithExercises,
    WorkoutStatusEnum, WorkoutStatusUpdate, WorkoutStatistics
)

# 组记录相关Schema
from app.schemas.set_record import (
    SetRecordBase, SetRecordCreate, SetRecordUpdate, SetRecordInDB
)

# 可以根据需要添加其他 schema 文件的导入