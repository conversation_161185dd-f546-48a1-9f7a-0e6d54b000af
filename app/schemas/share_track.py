from typing import Optional
from datetime import datetime
from pydantic import BaseModel, Field

class ShareTrackBase(BaseModel):
    """分享记录基础模型"""
    share_type: str = Field(..., description="分享类型")
    page: str = Field(..., description="分享页面")
    scene: str = Field(..., description="场景参数")
    shared_by: int = Field(..., description="分享者ID")
    qrcode_url: Optional[str] = Field(None, description="二维码URL")
    is_active: bool = Field(True, description="是否有效")

class ShareTrackCreate(ShareTrackBase):
    """创建分享记录模型"""
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")

class ShareTrackUpdate(BaseModel):
    """更新分享记录模型"""
    is_active: Optional[bool] = None
    qrcode_url: Optional[str] = None

class ShareTrackInDB(ShareTrackBase):
    """数据库中的分享记录模型"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True 