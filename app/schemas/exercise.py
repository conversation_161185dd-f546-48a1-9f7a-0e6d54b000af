from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

class ExerciseBase(BaseModel):
    """健身动作基础模型"""
    name: str
    en_name: Optional[str] = None
    body_part_id: List[int]
    equipment_id: List[int]
    image_name: Optional[str] = None
    gif_url: Optional[str] = None
    description: Optional[str] = None
    level: Optional[int] = None
    sort_priority: Optional[int] = None
    user_id: Optional[str] = None
    exercise_type: Optional[str] = None
    hit_time: Optional[int] = 0  # 浏览次数

class ExerciseCreate(ExerciseBase):
    """创建健身动作的请求模型"""
    pass

class ExerciseUpdate(ExerciseBase):
    """更新健身动作的请求模型"""
    pass

class Exercise(ExerciseBase):
    """健身动作响应模型"""
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class ExerciseDetailBase(BaseModel):
    """健身动作详情基础模型"""
    exercise_id: int
    target_muscles_id: List[int]
    synergist_muscles_id: Optional[List[int]] = None
    ex_instructions: Optional[List[str]] = None
    exercise_tips: Optional[List[str]] = None
    video_file: Optional[str] = None
    is_public: bool = True  # 是否公开，默认为公开

class ExerciseDetailCreate(ExerciseDetailBase):
    """创建健身动作详情的请求模型"""
    pass

class ExerciseDetailUpdate(ExerciseDetailBase):
    """更新健身动作详情的请求模型"""
    pass

class ExerciseDetail(ExerciseDetailBase):
    """健身动作详情响应模型"""
    id: int
    created_at: datetime
    updated_at: datetime
    hit_time: Optional[int] = 0  # 浏览次数，从exercises表获取

    class Config:
        from_attributes = True

class MuscleBase(BaseModel):
    """肌肉基础模型"""
    name: str
    en_name: str

class MuscleCreate(MuscleBase):
    """创建肌肉的请求模型"""
    pass

class Muscle(MuscleBase):
    """肌肉响应模型"""
    id: int

    class Config:
        from_attributes = True

class BodyPartBase(BaseModel):
    """身体部位基础模型"""
    name: str

class BodyPartCreate(BodyPartBase):
    """创建身体部位的请求模型"""
    pass

class BodyPart(BodyPartBase):
    """身体部位响应模型"""
    id: int

    class Config:
        from_attributes = True

class EquipmentBase(BaseModel):
    """器材基础模型"""
    name: str

class EquipmentCreate(EquipmentBase):
    """创建器材的请求模型"""
    pass

class Equipment(EquipmentBase):
    """器材响应模型"""
    id: int

    class Config:
        from_attributes = True

# 添加ExerciseSchema用于LLM输出解析
class ExerciseSchema(BaseModel):
    """用于LLM生成的运动结构模式"""
    id: int
    name: str
    description: Optional[str] = None
    body_parts: List[str] = []
    equipment: List[str] = []
    target_muscles: List[str] = []
    level: Optional[int] = None
    
    class Config:
        schema_extra = {
            "example": {
                "id": 123,
                "name": "卧推",
                "description": "平卧在卧推凳上，将杠铃推离胸部，然后控制下降",
                "body_parts": ["胸部", "肩部", "手臂"],
                "equipment": ["杠铃", "卧推凳"],
                "target_muscles": ["胸大肌", "三角肌前束", "肱三头肌"],
                "level": 3
            }
        } 