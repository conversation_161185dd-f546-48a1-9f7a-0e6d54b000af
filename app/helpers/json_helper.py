"""
JSON处理工具类
"""
import json
import logging
from typing import Any, Dict, List, Optional, Type, Union
from pydantic import BaseModel, ValidationError

logger = logging.getLogger(__name__)

class JsonHelper:
    """JSON处理工具类"""
    
    @staticmethod
    def parse_json(json_str: str) -> Dict[str, Any]:
        """
        解析JSON字符串为字典
        
        Args:
            json_str: JSON字符串
            
        Returns:
            解析后的字典
            
        Raises:
            ValueError: 如果JSON解析失败
        """
        try:
            return json.loads(json_str)
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {str(e)}")
            raise ValueError(f"无效的JSON格式: {str(e)}")
    
    @staticmethod
    def to_json(obj: Any, ensure_ascii: bool = False, indent: Optional[int] = None) -> str:
        """
        将对象转换为JSON字符串
        
        Args:
            obj: 要转换的对象
            ensure_ascii: 是否确保ASCII编码
            indent: 缩进空格数
            
        Returns:
            JSON字符串
        """
        return json.dumps(obj, ensure_ascii=ensure_ascii, indent=indent)
    
    @staticmethod
    def validate_json_schema(json_data: Dict[str, Any], schema_model: Type[BaseModel]) -> Dict[str, Any]:
        """
        验证JSON数据是否符合指定的Pydantic模型
        
        Args:
            json_data: JSON数据
            schema_model: Pydantic模型类
            
        Returns:
            验证后的数据
            
        Raises:
            ValueError: 如果验证失败
        """
        try:
            # 使用Pydantic模型验证数据
            validated_data = schema_model(**json_data)
            # 返回模型的字典表示
            return validated_data.dict()
        except ValidationError as e:
            logger.error(f"JSON验证失败: {str(e)}")
            raise ValueError(f"数据验证失败: {str(e)}")
    
    @staticmethod
    def parse_and_validate(json_str: str, schema_model: Type[BaseModel]) -> Dict[str, Any]:
        """
        解析并验证JSON字符串
        
        Args:
            json_str: JSON字符串
            schema_model: Pydantic模型类
            
        Returns:
            验证后的数据
            
        Raises:
            ValueError: 如果解析或验证失败
        """
        # 先解析JSON
        json_data = JsonHelper.parse_json(json_str)
        # 再验证数据
        return JsonHelper.validate_json_schema(json_data, schema_model)
    
    @staticmethod
    def merge_json_objects(obj1: Dict[str, Any], obj2: Dict[str, Any]) -> Dict[str, Any]:
        """
        合并两个JSON对象
        
        Args:
            obj1: 第一个JSON对象
            obj2: 第二个JSON对象
            
        Returns:
            合并后的JSON对象
        """
        result = obj1.copy()
        
        for key, value in obj2.items():
            # 如果两个对象都有相同的键，且值都是字典，则递归合并
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = JsonHelper.merge_json_objects(result[key], value)
            else:
                # 否则，使用obj2的值覆盖obj1的值
                result[key] = value
                
        return result
