"""
错误处理工具类
"""
import logging
import functools
import traceback
from typing import Any, Callable, Dict, Optional, Type, TypeVar, cast

logger = logging.getLogger(__name__)

# 定义泛型类型变量
T = TypeVar('T')

class ErrorHelper:
    """错误处理工具类"""
    
    @staticmethod
    def handle_exceptions(
        error_message: str = "操作执行失败",
        log_error: bool = True,
        return_none: bool = False,
        reraise: bool = False,
        expected_exceptions: Optional[tuple] = None
    ) -> Callable:
        """
        异常处理装饰器
        
        Args:
            error_message: 错误消息前缀
            log_error: 是否记录错误日志
            return_none: 发生异常时是否返回None
            reraise: 是否重新抛出异常
            expected_exceptions: 预期的异常类型元组
            
        Returns:
            装饰器函数
        """
        def decorator(func: Callable[..., T]) -> Callable[..., Optional[T]]:
            @functools.wraps(func)
            def wrapper(*args: Any, **kwargs: Any) -> Optional[T]:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    # 如果指定了预期异常类型，且当前异常不是预期类型，则重新抛出
                    if expected_exceptions and not isinstance(e, expected_exceptions):
                        raise
                    
                    # 构建详细错误消息
                    detailed_message = f"{error_message}: {str(e)}"
                    
                    # 记录错误日志
                    if log_error:
                        logger.error(detailed_message)
                        logger.error(traceback.format_exc())
                    
                    # 根据配置决定是重新抛出异常还是返回None
                    if reraise:
                        raise ValueError(detailed_message) from e
                    elif return_none:
                        return None
                    else:
                        # 默认抛出ValueError
                        raise ValueError(detailed_message) from e
                        
            return wrapper
        return decorator
    
    @staticmethod
    def safe_execute(
        func: Callable[..., T],
        *args: Any,
        error_message: str = "执行失败",
        default_value: Optional[T] = None,
        **kwargs: Any
    ) -> Optional[T]:
        """
        安全执行函数，捕获异常并返回默认值
        
        Args:
            func: 要执行的函数
            *args: 函数参数
            error_message: 错误消息前缀
            default_value: 发生异常时返回的默认值
            **kwargs: 函数关键字参数
            
        Returns:
            函数执行结果或默认值
        """
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"{error_message}: {str(e)}")
            logger.error(traceback.format_exc())
            return default_value
