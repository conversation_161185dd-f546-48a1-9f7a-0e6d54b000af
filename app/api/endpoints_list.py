#!/usr/bin/env python3
"""
This script lists all API endpoints available in the application.
Run this script to generate a comprehensive list of all endpoints.
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from fastapi import FastAPI
from fastapi.routing import APIRoute
from app.main import app

def get_all_routes(app: FastAPI):
    """获取应用程序中的所有路由"""
    routes = []
    
    for route in app.routes:
        if isinstance(route, APIRoute):
            routes.append({
                "path": route.path,
                "name": route.name,
                "methods": route.methods,
                "tags": getattr(route, "tags", []),
                "summary": route.summary,
                "description": route.description,
            })
    
    return routes

def print_routes(routes):
    """以格式化方式输出路由信息"""
    routes.sort(key=lambda x: x["path"])
    
    print("\n科学健身 API 路由列表")
    print("=" * 80)
    print(f"{'HTTP Method':<10} {'Path':<40} {'Name':<30}")
    print("-" * 80)
    
    for route in routes:
        methods = ", ".join(route["methods"])
        print(f"{methods:<10} {route['path']:<40} {route['name']:<30}")
    
    print("\n\n按标签分组的端点")
    print("=" * 80)
    
    # 按标签分组
    tag_groups = {}
    for route in routes:
        tags = route["tags"] or ["未分类"]
        for tag in tags:
            if tag not in tag_groups:
                tag_groups[tag] = []
            tag_groups[tag].append(route)
    
    # 输出每个标签组
    for tag, group_routes in sorted(tag_groups.items()):
        print(f"\n{tag}:")
        print("-" * 80)
        print(f"{'HTTP Method':<10} {'Path':<40} {'Summary':<30}")
        print("-" * 80)
        
        for route in sorted(group_routes, key=lambda x: x["path"]):
            methods = ", ".join(route["methods"])
            summary = route["summary"] or ""
            print(f"{methods:<10} {route['path']:<40} {summary[:30]:<30}")

def main():
    """主函数"""
    print("\n获取所有API端点...\n")
    routes = get_all_routes(app)
    print_routes(routes)
    print(f"\n总计: {len(routes)} 个端点\n")
    
    # 输出到文件
    output_file = Path(project_root) / "api_endpoints.txt"
    with open(output_file, "w") as f:
        sys.stdout = f
        print_routes(routes)
        print(f"\n总计: {len(routes)} 个端点\n")
    
    sys.stdout = sys.__stdout__
    print(f"端点列表已保存到: {output_file}")

if __name__ == "__main__":
    main() 