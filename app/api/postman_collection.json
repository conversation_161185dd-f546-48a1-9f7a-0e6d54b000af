{"info": {"_postman_id": "b46f5b8a-5c91-4d90-9d56-e7f5e7ea05dd", "name": "科学健身 API", "description": "科学健身小程序后端 API 接口集合", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "认证模块", "description": "用户认证相关接口", "item": [{"name": "微信小程序登录", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"code\": \"{{wx_code}}\",\n  \"userInfo\": {\n    \"nickname\": \"微信用户\",\n    \"avatar_url\": \"https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132\"\n  },\n  \"encryptedData\": \"{{encrypted_data}}\",\n  \"iv\": \"{{iv}}\",\n  \"openid\": \"{{openid}}\",\n  \"unionid\": \"{{unionid}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/login/wechat", "host": ["{{base_url}}"], "path": ["auth", "login", "wechat"]}, "description": "微信小程序登录接口，通过code换取用户身份和token"}, "response": []}, {"name": "绑定微信手机号", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"code\": \"{{wx_code}}\",\n  \"encrypted_data\": \"{{encrypted_data}}\",\n  \"iv\": \"{{iv}}\"\n}"}, "url": {"raw": "{{base_url}}/auth/wechat/phone", "host": ["{{base_url}}"], "path": ["auth", "wechat", "phone"]}, "description": "绑定微信用户的手机号码"}, "response": []}]}, {"name": "用户模块", "description": "用户信息管理相关接口", "item": [{"name": "获取当前用户信息", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/user/me", "host": ["{{base_url}}"], "path": ["user", "me"]}, "description": "获取当前登录用户的详细信息"}, "response": []}, {"name": "更新用户信息", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"nickname\": \"更新昵称\",\n  \"gender\": \"male\",\n  \"age\": 30,\n  \"weight\": 70.5,\n  \"height\": 175.0,\n  \"activity_level\": 3,\n  \"body_type\": \"中等体型\",\n  \"experience_level\": \"beginner\",\n  \"fitness_goal\": \"weight_loss\"\n}"}, "url": {"raw": "{{base_url}}/user/me", "host": ["{{base_url}}"], "path": ["user", "me"]}, "description": "更新当前用户的个人信息"}, "response": []}, {"name": "更新用户设置", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"notification_enabled\": true\n}"}, "url": {"raw": "{{base_url}}/user/settings", "host": ["{{base_url}}"], "path": ["user", "settings"]}, "description": "更新用户应用设置"}, "response": []}]}, {"name": "二维码模块", "description": "二维码和小程序码相关接口", "item": [{"name": "生成小程序码", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"page\": \"pages/index/index\",\n  \"scene\": \"id=1\",\n  \"width\": 280,\n  \"auto_color\": true,\n  \"line_color\": {\"r\":0,\"g\":0,\"b\":0},\n  \"is_hyaline\": false\n}"}, "url": {"raw": "{{base_url}}/qrcode/generate", "host": ["{{base_url}}"], "path": ["qrcode", "generate"]}, "description": "生成微信小程序码"}, "response": []}]}, {"name": "分享模块", "description": "内容分享和追踪相关接口", "item": [{"name": "记录分享事件", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"type\": \"program\",\n  \"content_id\": 1,\n  \"channel\": \"wechat_moments\"\n}"}, "url": {"raw": "{{base_url}}/share/event", "host": ["{{base_url}}"], "path": ["share", "event"]}, "description": "记录用户分享事件"}, "response": []}, {"name": "获取分享统计", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/share/stats", "host": ["{{base_url}}"], "path": ["share", "stats"]}, "description": "获取用户分享统计数据"}, "response": []}]}, {"name": "健康检查", "description": "系统健康状态检查", "item": [{"name": "服务状态检查", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/ping", "host": ["{{base_url}}"], "path": ["ping"]}, "description": "检查API服务是否正常运行"}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["if (pm.response.code === 200) {", "    if (pm.response.json().token) {", "        pm.environment.set(\"access_token\", pm.response.json().token);", "        console.log(\"已自动保存访问令牌到环境变量\");", "    }", "}"]}}], "variable": [{"key": "base_url", "value": "http://localhost:8000/api/v1", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}, {"key": "wx_code", "value": "", "type": "string"}, {"key": "encrypted_data", "value": "", "type": "string"}, {"key": "iv", "value": "", "type": "string"}, {"key": "openid", "value": "", "type": "string"}, {"key": "unionid", "value": "", "type": "string"}]}