from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from datetime import datetime
from app.api.deps import get_db, get_current_user
from app.models.user import User
from app.models.team import <PERSON><PERSON><PERSON>, MembershipStatus, InvitationStatus, ClientStatus
from app.schemas.team import (
    TeamCreate, TeamUpdate, TeamResponse, TeamDetail, TeamListResponse,
    MembershipCreate, MembershipUpdate, MembershipResponse, TeamMemberResponse,
    ClientAssignment, ClientTransfer, ClientRelationResponse, ClientListResponse, ClientDetailResponse,
    InvitationCreate, InvitationResponse, InvitationListResponse,
    ClientPlanCreate, ClientPlanUpdate, ClientTrainingPlanResponse, SessionResponse, SetRecordCreate, SessionFeedback,
    TemplateCreate, TemplateResponse, TemplateListResponse
)
from app.services.team_service import TeamService, TeamServiceException, TeamNotFoundException, InsufficientPermissionException
from app.services.team_member_service import TeamMemberService, MembershipNotFoundException, InvitationNotFoundException
from app.services.team_client_service import TeamClientService, ClientRelationNotFoundException
from app.services.team_training_service import TeamTrainingService, TrainingPlanNotFoundException, SessionNotFoundException, TemplateNotFoundException, ScheduleConflictException
from app.services.team_stats_service import TeamStatsService

router = APIRouter()

# 团队管理接口
@router.post("/teams/", response_model=TeamResponse)
async def create_new_team(
    team_data: TeamCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建新团队"""
    team_service = TeamService(db)
    try:
        return await team_service.create_team(current_user, team_data)
    except TeamServiceException as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/teams/{team_id}", response_model=TeamDetail)
async def get_team_detail(
    team_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取团队详情"""
    team_service = TeamService(db)
    try:
        return await team_service.get_team_detail(team_id, current_user.id)
    except TeamNotFoundException:
        raise HTTPException(status_code=404, detail="Team not found")
    except InsufficientPermissionException:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.put("/teams/{team_id}", response_model=TeamResponse)
async def update_team_info(
    team_id: int,
    team_data: TeamUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新团队信息"""
    team_service = TeamService(db)
    try:
        return await team_service.update_team(team_id, team_data, current_user)
    except TeamNotFoundException:
        raise HTTPException(status_code=404, detail="Team not found")
    except InsufficientPermissionException:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/teams/", response_model=List[TeamListResponse])
async def list_user_teams(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户相关的团队列表"""
    team_service = TeamService(db)
    try:
        return await team_service.get_user_teams(current_user.id)
    except TeamServiceException as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/teams/{team_id}", status_code=204)
async def delete_team(
    team_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除团队（标记为已删除状态）"""
    team_service = TeamService(db)
    try:
        await team_service.delete_team(team_id, current_user)
    except TeamNotFoundException:
        raise HTTPException(status_code=404, detail="Team not found")
    except InsufficientPermissionException:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        raise HTTPException(status_code=400, detail=str(e))

# 成员管理接口
@router.post("/teams/{team_id}/members/", response_model=MembershipResponse)
async def add_team_member(
    team_id: int,
    member_data: MembershipCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """添加团队成员"""
    member_service = TeamMemberService(db)
    try:
        return await member_service.add_team_member(team_id, member_data, current_user)
    except InsufficientPermissionException:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.put("/teams/{team_id}/members/{user_id}", response_model=MembershipResponse)
async def update_member_role(
    team_id: int,
    user_id: int,
    role_data: MembershipUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新成员角色"""
    member_service = TeamMemberService(db)
    try:
        return await member_service.update_member_role(team_id, user_id, role_data, current_user)
    except MembershipNotFoundException:
        raise HTTPException(status_code=404, detail="Membership not found")
    except InsufficientPermissionException:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/teams/{team_id}/members/{user_id}", status_code=204)
async def remove_team_member(
    team_id: int,
    user_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """移除团队成员"""
    member_service = TeamMemberService(db)
    try:
        await member_service.remove_team_member(team_id, user_id, current_user)
    except MembershipNotFoundException:
        raise HTTPException(status_code=404, detail="Membership not found")
    except InsufficientPermissionException:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/teams/{team_id}/members", response_model=List[TeamMemberResponse])
async def list_team_members(
    team_id: int,
    role: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取团队成员列表"""
    member_service = TeamMemberService(db)
    try:
        return await member_service.get_team_members(team_id, role, current_user)
    except TeamServiceException as e:
        raise HTTPException(status_code=403, detail=str(e))

# 会员管理接口
@router.post("/teams/{team_id}/clients/", response_model=ClientRelationResponse)
async def assign_team_client(
    team_id: int,
    client_data: ClientAssignment,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """分配会员"""
    client_service = TeamClientService(db)
    try:
        return await client_service.assign_client(team_id, client_data, current_user)
    except InsufficientPermissionException:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/teams/{team_id}/clients/{client_relation_id}/transfer", response_model=ClientRelationResponse)
async def transfer_team_client(
    team_id: int,
    client_relation_id: int,
    transfer_data: ClientTransfer,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """转移会员"""
    client_service = TeamClientService(db)
    try:
        return await client_service.transfer_client(client_relation_id, transfer_data, current_user)
    except ClientRelationNotFoundException:
        raise HTTPException(status_code=404, detail="Client relation not found")
    except InsufficientPermissionException:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/teams/{team_id}/clients", response_model=List[ClientListResponse])
async def list_team_clients(
    team_id: int,
    status: Optional[str] = None,
    coach_id: Optional[int] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取团队会员列表"""
    client_service = TeamClientService(db)
    try:
        return await client_service.get_team_clients(team_id, status, coach_id, current_user)
    except TeamServiceException as e:
        raise HTTPException(status_code=403, detail=str(e))

@router.get("/teams/{team_id}/clients/{client_relation_id}", response_model=ClientDetailResponse)
async def get_client_detail(
    team_id: int,
    client_relation_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取会员详细信息"""
    client_service = TeamClientService(db)
    try:
        return await client_service.get_client_detail(client_relation_id, current_user)
    except ClientRelationNotFoundException:
        raise HTTPException(status_code=404, detail="Client relation not found")
    except TeamServiceException as e:
        raise HTTPException(status_code=403, detail=str(e))

@router.put("/teams/{team_id}/clients/{client_relation_id}/deactivate", response_model=ClientRelationResponse)
async def deactivate_client(
    team_id: int,
    client_relation_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """停用会员"""
    client_service = TeamClientService(db)
    try:
        return await client_service.deactivate_client(client_relation_id, current_user)
    except ClientRelationNotFoundException:
        raise HTTPException(status_code=404, detail="Client relation not found")
    except InsufficientPermissionException:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.put("/teams/{team_id}/clients/{client_relation_id}/reactivate", response_model=ClientRelationResponse)
async def reactivate_client(
    team_id: int,
    client_relation_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """重新激活会员"""
    client_service = TeamClientService(db)
    try:
        return await client_service.reactivate_client(client_relation_id, current_user)
    except ClientRelationNotFoundException:
        raise HTTPException(status_code=404, detail="Client relation not found")
    except InsufficientPermissionException:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        raise HTTPException(status_code=400, detail=str(e))

# 邀请管理接口
@router.post("/teams/{team_id}/invitations/", response_model=InvitationResponse)
async def create_team_invitation(
    team_id: int,
    invitation_data: InvitationCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建团队邀请"""
    member_service = TeamMemberService(db)
    try:
        return await member_service.create_team_invitation(team_id, invitation_data, current_user)
    except InsufficientPermissionException:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.put("/teams/invitations/{invitation_id}/accept", response_model=Dict[str, Any])
async def accept_team_invitation(
    invitation_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """接受团队邀请"""
    member_service = TeamMemberService(db)
    try:
        result = await member_service.respond_to_invitation(invitation_id, True, current_user)
        return result
    except InvitationNotFoundException:
        raise HTTPException(status_code=404, detail="Invitation not found")
    except TeamServiceException as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.put("/teams/invitations/{invitation_id}/reject", response_model=Dict[str, Any])
async def reject_team_invitation(
    invitation_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """拒绝团队邀请"""
    member_service = TeamMemberService(db)
    try:
        result = await member_service.respond_to_invitation(invitation_id, False, current_user)
        return result
    except InvitationNotFoundException:
        raise HTTPException(status_code=404, detail="Invitation not found")
    except TeamServiceException as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/teams/{team_id}/invitations", response_model=List[InvitationListResponse])
async def list_team_invitations(
    team_id: int,
    status: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取团队发出的邀请列表"""
    member_service = TeamMemberService(db)
    try:
        invitation_status = None
        if status:
            try:
                invitation_status = InvitationStatus[status.upper()]
            except (KeyError, AttributeError):
                raise HTTPException(status_code=400, detail=f"Invalid status: {status}")

        return await member_service.get_team_invitations(team_id, invitation_status, current_user)
    except InsufficientPermissionException:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/user/invitations", response_model=List[InvitationResponse])
async def list_user_invitations(
    status: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户收到的邀请列表"""
    member_service = TeamMemberService(db)
    try:
        invitation_status = None
        if status:
            try:
                invitation_status = InvitationStatus[status.upper()]
            except (KeyError, AttributeError):
                raise HTTPException(status_code=400, detail=f"Invalid status: {status}")

        return await member_service.get_user_invitations(current_user.id, invitation_status)
    except TeamServiceException as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/teams/invitations/{invitation_id}", status_code=204)
async def cancel_invitation(
    invitation_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """取消邀请"""
    member_service = TeamMemberService(db)
    try:
        await member_service.cancel_invitation(invitation_id, current_user)
    except InvitationNotFoundException:
        raise HTTPException(status_code=404, detail="Invitation not found")
    except InsufficientPermissionException:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        raise HTTPException(status_code=400, detail=str(e))

# 训练计划模板接口
@router.post("/teams/{team_id}/templates/", response_model=TemplateResponse)
async def create_plan_template(
    team_id: int,
    template_data: TemplateCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建训练计划模板"""
    training_service = TeamTrainingService(db)
    try:
        return await training_service.create_training_template(team_id, template_data, current_user)
    except InsufficientPermissionException:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/teams/{team_id}/templates/", response_model=List[TemplateListResponse])
async def list_plan_templates(
    team_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取团队训练计划模板列表"""
    training_service = TeamTrainingService(db)
    try:
        return await training_service.get_team_templates(team_id, current_user)
    except TeamServiceException as e:
        raise HTTPException(status_code=403, detail=str(e))

# 训练计划接口
@router.post("/clients/{client_relation_id}/training-plans/", response_model=ClientTrainingPlanResponse)
async def create_client_training_plan(
    client_relation_id: int,
    plan_data: ClientPlanCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建会员训练计划"""
    training_service = TeamTrainingService(db)
    try:
        return await training_service.assign_training_plan(client_relation_id, plan_data, current_user)
    except ClientRelationNotFoundException:
        raise HTTPException(status_code=404, detail="Client relation not found")
    except InsufficientPermissionException:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except ScheduleConflictException as e:
        raise HTTPException(status_code=409, detail=str(e))
    except TeamServiceException as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/clients/{client_relation_id}/training-plans/", response_model=List[Dict[str, Any]])
async def list_client_training_plans(
    client_relation_id: int,
    status: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取会员训练计划列表"""
    training_service = TeamTrainingService(db)
    try:
        return await training_service.get_client_training_plans(client_relation_id, status, current_user)
    except ClientRelationNotFoundException:
        raise HTTPException(status_code=404, detail="Client relation not found")
    except TeamServiceException as e:
        raise HTTPException(status_code=403, detail=str(e))

@router.put("/clients/training-plans/{plan_id}", response_model=ClientTrainingPlanResponse)
async def update_client_training_plan(
    plan_id: int,
    update_data: ClientPlanUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新会员训练计划"""
    training_service = TeamTrainingService(db)
    try:
        return await training_service.update_training_plan(plan_id, update_data, current_user)
    except TrainingPlanNotFoundException:
        raise HTTPException(status_code=404, detail="Training plan not found")
    except InsufficientPermissionException:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        raise HTTPException(status_code=400, detail=str(e))

# 训练课程接口
@router.get("/training-plans/{plan_id}/sessions", response_model=List[Dict[str, Any]])
async def list_training_sessions(
    plan_id: int,
    status: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取训练课程列表"""
    training_service = TeamTrainingService(db)
    try:
        return await training_service.get_training_sessions(plan_id, status)
    except TrainingPlanNotFoundException:
        raise HTTPException(status_code=404, detail="Training plan not found")
    except TeamServiceException as e:
        raise HTTPException(status_code=403, detail=str(e))

@router.post("/sessions/{session_id}/start", response_model=SessionResponse)
async def start_training_session(
    session_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """开始训练课程"""
    training_service = TeamTrainingService(db)
    try:
        return await training_service.start_session(session_id, current_user)
    except SessionNotFoundException:
        raise HTTPException(status_code=404, detail="Training session not found")
    except InsufficientPermissionException:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/sessions/{session_id}/exercises/{exercise_id}/records", response_model=Dict[str, Any])
async def record_exercise_set(
    session_id: int,
    exercise_id: int,
    set_data: SetRecordCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """记录训练组数据"""
    training_service = TeamTrainingService(db)
    try:
        record = await training_service.record_exercise_set(session_id, exercise_id, set_data, current_user)
        return {
            "id": record.id,
            "session_id": record.session_id,
            "exercise_id": record.exercise_id,
            "sets_planned": record.sets_planned,
            "sets_completed": record.sets_completed,
            "status": record.status,
            "set_records": record.set_records
        }
    except SessionNotFoundException:
        raise HTTPException(status_code=404, detail="Training session not found")
    except InsufficientPermissionException:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.put("/sessions/{session_id}/complete", response_model=SessionResponse)
async def complete_training_session(
    session_id: int,
    feedback_data: SessionFeedback,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """完成训练课程并提交反馈"""
    training_service = TeamTrainingService(db)
    try:
        return await training_service.complete_session(session_id, feedback_data, current_user)
    except SessionNotFoundException:
        raise HTTPException(status_code=404, detail="Training session not found")
    except InsufficientPermissionException:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        raise HTTPException(status_code=400, detail=str(e))

# 团队统计接口
@router.get("/teams/{team_id}/stats", response_model=Dict[str, Any])
async def get_team_statistics(
    team_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取团队统计数据"""
    stats_service = TeamStatsService(db)
    try:
        return await stats_service.get_team_stats(team_id, current_user)
    except TeamNotFoundException:
        raise HTTPException(status_code=404, detail="Team not found")
    except InsufficientPermissionException:
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/clients/{client_relation_id}/stats", response_model=Dict[str, Any])
async def get_client_statistics(
    client_relation_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取客户统计数据"""
    stats_service = TeamStatsService(db)
    try:
        return await stats_service.get_client_stats(client_relation_id, current_user)
    except ClientRelationNotFoundException:
        raise HTTPException(status_code=404, detail="Client relation not found")
    except TeamServiceException as e:
        raise HTTPException(status_code=403, detail=str(e))
