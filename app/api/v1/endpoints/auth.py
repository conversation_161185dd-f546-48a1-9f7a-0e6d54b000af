from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.orm import Session
import logging

from app.api import deps
from app.services.wechat import WechatService
from app.schemas.wechat_login import WechatLoginRequest, WechatLoginResponse, UserInfo
from app.core.config import settings

router = APIRouter()
wechat_service = WechatService()
logger = logging.getLogger("fitness-coach-api")

@router.post("/login/wechat", response_model=WechatLoginResponse)
def wechat_login(
    request: WechatLoginRequest,
    request_obj: Request,
    db: Session = Depends(deps.get_db)
):
    """
    微信小程序登录接口
    - **code**: 小程序登录code
    - **encrypted_data**: 加密的用户数据（可选）
    - **iv**: 加密向量（可选）
    """
    try:
        # 记录请求信息
        logger.info(f"微信登录请求: code={request.code}, IP={request_obj.client.host}")
        
        # 测试环境处理 - 如果有数据库连接问题，使用模拟数据
        if settings.IS_DEV:
            # 模拟用户数据
            user_info = UserInfo(
                id=1,
                nickname="测试用户",
                avatar_url="https://example.com/avatar.jpg",
                phone="13800138000",
                gender="male",
                country="China",
                province="Zhejiang",
                city="Hangzhou"
            )
            
            # 创建模拟token
            token = wechat_service.create_access_token(1)
            
            logger.info(f"返回测试用户数据: {user_info}")
            
            return WechatLoginResponse(
                success=True,
                token=token,
                is_new_user=False,
                user=user_info
            )
        
        # 正式处理流程
        try:
            token, is_new_user, user_info = wechat_service.process_login(
                db=db,
                code=request.code,
                encrypted_data=request.encrypted_data,
                iv=request.iv
            )
            
            return WechatLoginResponse(
                success=True,
                token=token,
                is_new_user=is_new_user,
                user=user_info
            )
        except Exception as db_error:
            # 如果处理过程中出错，记录错误并尝试使用模拟数据
            logger.error(f"微信登录处理错误: {str(db_error)}")
            raise
            
    except ValueError as e:
        logger.error(f"微信登录参数错误: {str(e)}")
        raise HTTPException(
            status_code=400,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"微信登录异常: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error"
        ) 