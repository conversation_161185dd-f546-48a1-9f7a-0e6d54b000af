from typing import Generator, Optional, Dict
from fastapi import Depends, HTTPException, status, WebSocket, Header, Request
from sqlalchemy.orm import Session
from jose import JWTError
import time
import logging
from app.db.session import SessionLocal
from app.core.config import settings
from app.core.security import verify_access_token
from app import crud, models
from app.services.llm_proxy_service import LLMProxyService
from app.services.community_service import CommunityService

logger = logging.getLogger(__name__)

# 简单的内存速率限制器
class RateLimiter:
    def __init__(self, requests_per_minute: int = 60):
        self.requests_per_minute = requests_per_minute
        self.window_size = 60  # 1分钟窗口
        self.user_requests: Dict[int, Dict[float, int]] = {}

    def is_allowed(self, user_id: int) -> bool:
        """检查用户是否允许发送请求"""
        current_time = time.time()

        # 初始化用户记录
        if user_id not in self.user_requests:
            self.user_requests[user_id] = {}

        # 清理过期的请求记录
        self._cleanup_old_requests(user_id, current_time)

        # 计算当前窗口内的请求数
        request_count = sum(self.user_requests[user_id].values())

        # 检查是否超过限制
        if request_count >= self.requests_per_minute:
            return False

        # 记录新请求
        self.user_requests[user_id][current_time] = 1
        return True

    def _cleanup_old_requests(self, user_id: int, current_time: float) -> None:
        """清理过期的请求记录"""
        cutoff_time = current_time - self.window_size
        self.user_requests[user_id] = {
            ts: count for ts, count in self.user_requests[user_id].items()
            if ts > cutoff_time
        }

# 创建速率限制器实例
chat_rate_limiter = RateLimiter(requests_per_minute=settings.CHAT_RATE_LIMIT if hasattr(settings, 'CHAT_RATE_LIMIT') else 60)

async def get_db_websocket() -> Generator:
    """WebSocket连接的数据库会话依赖项"""
    try:
        db = SessionLocal()
        yield db
    finally:
        db.close()

def get_db() -> Generator:
    """常规HTTP请求的数据库会话依赖项"""
    try:
        db = SessionLocal()
        yield db
    finally:
        db.close()

async def get_current_user_websocket(
    websocket: WebSocket,
    db: Session = Depends(get_db_websocket)
) -> models.User:
    """WebSocket连接的当前用户依赖项"""
    try:
        # 从查询参数或头部获取token
        token = websocket.query_params.get("token") or websocket.headers.get("authorization")

        if not token:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="未提供认证凭据"
            )

        # 验证token
        token_data = verify_access_token(token.replace("Bearer ", ""))
        user = crud.crud_user.get(db, id=token_data.sub)

        if not user:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            raise HTTPException(
                status_code=404,
                detail="用户未找到"
            )

        return user

    except Exception as e:
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )

def get_current_user(
    db: Session = Depends(get_db),
    authorization: Optional[str] = Header(None)
) -> models.User:
    """获取当前用户依赖项"""
    if not authorization:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="未提供认证凭据",
            headers={"WWW-Authenticate": "Bearer"},
        )

    try:
        # 验证token
        token = authorization.replace("Bearer ", "") if authorization.startswith("Bearer ") else authorization
        token_data = verify_access_token(token)
        user = crud.crud_user.get(db, id=token_data.sub)

        if not user:
            raise HTTPException(
                status_code=404,
                detail="用户未找到"
            )

        return user
    except JWTError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"无效的访问令牌: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"},
        )

def get_current_active_user(
    current_user: models.User = Depends(get_current_user),
) -> models.User:
    """获取当前活跃用户依赖项"""
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="用户已禁用")
    return current_user

def get_current_active_superuser(
    current_user: models.User = Depends(get_current_user),
) -> models.User:
    """获取当前活跃超级用户依赖项（管理员）"""
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="用户已禁用")
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足，需要管理员权限")
    return current_user

def check_chat_rate_limit(
    request: Request,
    current_user: models.User = Depends(get_current_active_user)
) -> int:
    """
    检查聊天请求的速率限制

    Args:
        request: 当前请求
        current_user: 当前用户

    Returns:
        int: 用户ID，如果通过速率限制检查

    Raises:
        HTTPException: 如果用户超过速率限制
    """
    # 管理员不受速率限制
    if current_user.is_superuser:
        return current_user.id

    # 检查用户是否允许发送请求
    if not chat_rate_limiter.is_allowed(current_user.id):
        logger.warning(f"用户 {current_user.id} 超过聊天速率限制")
        raise HTTPException(
            status_code=429,
            detail="请求过于频繁，请稍后再试"
        )

    return current_user.id

# 创建LLM代理服务单例
_llm_proxy_service = None

def get_llm_proxy_service() -> LLMProxyService:
    """
    获取LLM代理服务实例（单例模式）

    Returns:
        LLMProxyService: LLM代理服务实例
    """
    global _llm_proxy_service
    if _llm_proxy_service is None:
        _llm_proxy_service = LLMProxyService()
    return _llm_proxy_service

# 创建社区服务单例
_community_service = None

def get_community_service(db: Session = Depends(get_db)) -> CommunityService:
    """
    获取社区服务实例（单例模式）

    Args:
        db: 数据库会话

    Returns:
        CommunityService: 社区服务实例
    """
    global _community_service
    if _community_service is None:
        _community_service = CommunityService(db)
    return _community_service

def get_current_user_optional(
    db: Session = Depends(get_db),
    authorization: Optional[str] = Header(None)
) -> Optional[models.User]:
    """
    获取当前用户依赖项（可选）

    与get_current_user不同，此函数在未提供认证凭据时不会抛出异常，而是返回None

    Args:
        db: 数据库会话
        authorization: 认证头

    Returns:
        Optional[models.User]: 当前用户，如果未认证则为None
    """
    if not authorization:
        return None

    try:
        # 验证token
        token = authorization.replace("Bearer ", "") if authorization.startswith("Bearer ") else authorization
        token_data = verify_access_token(token)
        user = crud.crud_user.get(db, id=token_data.sub)

        if not user or not user.is_active:
            return None

        return user
    except Exception:
        return None
