from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.openapi.utils import get_openapi
from fastapi import FastAPI, Request, APIRouter
from starlette.responses import HTMLResponse, JSONResponse
import importlib

router = APIRouter()

@router.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html(request: Request):
    """自定义 Swagger UI 路由，允许前端开发人员通过浏览器查看API文档"""
    return get_swagger_ui_html(
        openapi_url="/api/v1/openapi.json",
        title="科学健身 API - 开发文档",
        swagger_js_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui-bundle.js",
        swagger_css_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css",
    )

@router.get("/openapi.json", include_in_schema=False, response_model=None)
async def get_openapi_endpoint(request: Request):
    """返回OpenAPI规范以生成Swagger UI文档"""
    # 避免循环导入，动态导入app
    main_module = importlib.import_module("app.main")
    app = getattr(main_module, "app")
    
    openapi_schema = get_openapi(
        title="科学健身 API",
        version="1.0.0",
        description="科学健身小程序后端API接口文档",
        routes=app.routes,
    )
    
    return JSONResponse(content=openapi_schema)

def setup_swagger_docs(app: FastAPI):
    """配置Swagger文档，在主应用启动时调用"""
    app.include_router(router, prefix="/api/v1")
    
    # 自定义文档描述
    app.description = """
    # 科学健身小程序 API
    
    这是科学健身小程序的后端API接口说明文档。
    
    ## 认证
    
    大部分API需要认证，请通过微信小程序登录获取token，并在后续请求中通过Authorization头部携带。
    
    ## 环境说明
    
    - 开发环境：http://localhost:8000/api/v1
    - 测试环境：https://test-api.example.com/api/v1
    - 生产环境：https://api.example.com/api/v1
    
    ## 联系方式
    
    如有API相关问题，请联系后端开发团队。
    """
    
    # 自定义API标签说明
    app.openapi_tags = [
        {
            "name": "auth",
            "description": "认证相关接口，包括微信登录、获取手机号等",
        },
        {
            "name": "users",
            "description": "用户相关接口，包括获取用户信息、更新用户资料等",
        },
        {
            "name": "qrcode",
            "description": "二维码相关接口，用于生成和管理小程序码",
        },
        {
            "name": "share",
            "description": "分享追踪相关接口，用于记录和统计分享数据",
        },
        {
            "name": "health",
            "description": "健康检查接口，用于监控服务状态",
        },
        {
            "name": "exercise",
            "description": "健身动作库接口，包括动作查询、搜索、详情等功能",
        },
    ] 