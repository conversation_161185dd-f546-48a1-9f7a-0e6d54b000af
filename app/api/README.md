# 科学健身 API 文档

本文档详细说明了科学健身小程序的后端 API，供前端开发人员参考使用。

## 基础信息

- 基础URL: `/api/v1`
- 所有请求返回的数据格式均为 JSON
- 认证使用 Bearer Token 方式，在请求头中添加 `Authorization: Bearer {token}`

## 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 资源创建成功 |
| 400 | 请求参数错误 |
| 401 | 未认证或认证失败 |
| 403 | 无权限访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 认证相关 API

### 微信小程序登录

```
POST /auth/login/wechat
```

**请求参数**

```json
{
  "code": "string",                     // 必填，微信临时登录凭证
  "userInfo": {                         // 可选，用户信息对象
    "nickname": "微信用户",
    "avatar_url": "https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132"
  },
  "encryptedData": "string",            // 可选，微信加密数据
  "iv": "string",                       // 可选，加密算法的初始向量
  "openid": "string",                   // 可选，用户openid
  "unionid": "string"                   // 可选，用户unionid
}
```

**响应内容**

```json
{
  "success": true,
  "token": "string",
  "user": {
    "id": 1,
    "nickname": "string",
    "avatarUrl": "string",
    "phone": "string",
    "gender": "male",                   // male, female, other
    "country": "string",
    "province": "string",
    "city": "string",
    "created_at": "2024-04-05T12:00:00",
    "notification_enabled": true
  },
  "is_new_user": false
}
```

### 绑定微信手机号

```
POST /auth/wechat/phone
```

**请求参数**

```json
{
  "code": "string",                    // 必填，微信临时登录凭证
  "encrypted_data": "string",          // 必填，微信加密数据
  "iv": "string"                       // 必填，加密算法的初始向量
}
```

**响应内容**

```json
{
  "success": true,
  "message": "手机号绑定成功",
  "phone": "13800138000"
}
```

## 用户相关 API

### 获取用户信息

```
GET /user/me
```

**请求头**
- Authorization: Bearer {token}

**响应内容**

```json
{
  "id": 1,
  "nickname": "string",
  "avatar_url": "string",
  "phone": "string",
  "gender": "male",
  "country": "string",
  "province": "string",
  "city": "string",
  "age": 30,
  "weight": 70.5,
  "height": 175.0,
  "activity_level": 3,
  "body_type": "string",
  "experience_level": "beginner",
  "fitness_goal": "weight_loss",
  "health_conditions": ["轻度高血压"],
  "allergies": ["乳制品"],
  "bmi": 23.0,
  "tedd": 2500,
  "completed": true,
  "created_at": "2024-04-05T12:00:00",
  "notification_enabled": true
}
```

### 更新用户信息

```
PUT /user/me
```

**请求头**
- Authorization: Bearer {token}

**请求参数**

```json
{
  "nickname": "string",
  "avatar_url": "string",
  "gender": "male",
  "age": 30,
  "weight": 70.5,
  "height": 175.0,
  "activity_level": 3,
  "body_type": "string",
  "experience_level": "beginner",
  "fitness_goal": "weight_loss",
  "health_conditions": ["轻度高血压"],
  "allergies": ["乳制品"]
}
```

**响应内容**

```json
{
  "success": true,
  "message": "用户信息更新成功",
  "user": {
    "id": 1,
    "nickname": "string",
    "avatar_url": "string",
    "gender": "male",
    "age": 30,
    "weight": 70.5,
    "height": 175.0,
    "activity_level": 3,
    "body_type": "string",
    "experience_level": "beginner",
    "fitness_goal": "weight_loss",
    "health_conditions": ["轻度高血压"],
    "allergies": ["乳制品"],
    "bmi": 23.0,
    "tedd": 2500,
    "completed": true
  }
}
```

### 更新用户设置

```
PUT /user/settings
```

**请求头**
- Authorization: Bearer {token}

**请求参数**

```json
{
  "notification_enabled": true
}
```

**响应内容**

```json
{
  "success": true,
  "message": "设置更新成功",
  "settings": {
    "notification_enabled": true
  }
}
```

## 二维码相关 API

### 生成小程序码

```
POST /qrcode/generate
```

**请求头**
- Authorization: Bearer {token}

**请求参数**

```json
{
  "page": "pages/index/index",
  "scene": "id=1",
  "width": 280,
  "auto_color": true,
  "line_color": {"r":0,"g":0,"b":0},
  "is_hyaline": false
}
```

**响应内容**

```json
{
  "success": true,
  "qrcode_url": "https://example.com/qrcode/1234.jpg"
}
```

## 分享追踪 API

### 记录分享事件

```
POST /share/event
```

**请求头**
- Authorization: Bearer {token}

**请求参数**

```json
{
  "type": "program",
  "content_id": 1,
  "channel": "wechat_moments"
}
```

**响应内容**

```json
{
  "success": true,
  "message": "分享事件记录成功",
  "share_id": "1234567890"
}
```

### 获取分享统计

```
GET /share/stats
```

**请求头**
- Authorization: Bearer {token}

**响应内容**

```json
{
  "total_shares": 10,
  "program_shares": 5,
  "article_shares": 3,
  "other_shares": 2,
  "channels": {
    "wechat_moments": 6,
    "wechat_friends": 4
  }
}
```

## 健康检查 API

### 服务状态检查

```
GET /ping
```

**响应内容**

```json
{
  "status": "success",
  "message": "pong"
}
```

## 错误响应示例

当API调用出错时，会返回如下格式的错误信息：

```json
{
  "detail": "错误详细信息"
}
```

## 开发环境信息

- API基础URL: `http://localhost:8000/api/v1`
- 测试账号: 请联系后端开发人员获取

## 注意事项

1. 所有需要认证的接口必须在请求头中携带有效的 token
2. 用户首次登录后，建议完善用户资料以获得更好的体验
3. 部分敏感操作可能需要额外的校验，如手机号验证码
4. API 可能会不断更新和优化，请定期查看最新文档

## 重要功能更新

### 用户健康信息功能

我们增加了对用户健康信息的支持，通过两个新字段来记录用户的健康状况和过敏源信息：

- **health_conditions**: 用户的健康状况记录，如"轻度高血压"、"糖尿病"等
- **allergies**: 用户的食物或药物过敏源记录，如"乳制品"、"花生"等

这些信息将用于：

1. 生成个性化的健身计划，避免不适合用户健康状况的动作
2. 提供膳食建议时，避免包含用户过敏源的食物
3. 在运动指导中给予适当的健康提醒

用户可以通过更新个人资料API来添加或修改这些信息：

```
PUT /user/me
```

示例请求：
```json
{
  "nickname": "用户昵称",
  "health_conditions": ["轻度高血压", "腰椎间盘突出"],
  "allergies": ["乳制品", "花生"]
}
```

这些信息也会在获取用户资料时返回：

```
GET /user/me
```

示例响应：
```json
{
  "id": 1,
  "nickname": "用户昵称",
  "health_conditions": ["轻度高血压", "腰椎间盘突出"],
  "allergies": ["乳制品", "花生"],
  ...其他用户信息
}
```

### 枚举字段统一使用整数类型

为了提高系统一致性和性能，所有枚举类型字段现在统一使用整数值存储和传输：

#### gender（性别）
- 0: 未知（UNKNOWN）
- 1: 男性（MALE）
- 2: 女性（FEMALE）

#### experience_level（健身经验级别）
- 1: 初学者（BEGINNER）
- 2: 中级（INTERMEDIATE）
- 3: 高级（ADVANCED）

#### fitness_goal（健身目标）
- 1: 减肥（LOSE_WEIGHT）
- 2: 保持（MAINTAIN）
- 3: 增肌（GAIN_MUSCLE）
- 4: 耐力（ENDURANCE）

#### activity_level（活动级别）
- 1: 久坐不动
- 2: 轻度活动
- 3: 中度活动（默认）
- 4: 高度活动
- 5: 极高活动

**前端开发者注意事项**：
- 所有API请求中，这些枚举字段必须使用整数值
- 响应中也将返回整数值，前端需要负责根据上述映射进行显示转换
- 建议在前端使用常量定义这些映射关系，确保一致性

示例请求：
```json
{
  "nickname": "用户昵称",
  "gender": 1,
  "experience_level": 2,
  "fitness_goal": 3
}
``` 