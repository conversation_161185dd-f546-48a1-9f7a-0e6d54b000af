from fastapi import Request, status
from fastapi.responses import JSONResponse
from app.core.logger import logger
from typing import Callable
import traceback

async def error_handler_middleware(request: Request, call_next: Callable):
    try:
        return await call_next(request)
    except Exception as e:
        logger.error(f"请求处理错误: {str(e)}\n{traceback.format_exc()}")
        
        if request.url.path.startswith("/ws"):
            # WebSocket错误处理
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={
                    "detail": "WebSocket连接错误",
                    "error": str(e)
                }
            )
        else:
            # 常规HTTP请求错误处理
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={
                    "detail": "请求处理失败",
                    "error": str(e)
                }
            )