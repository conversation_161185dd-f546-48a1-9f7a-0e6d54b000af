from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app import crud, models, schemas
from app.api import deps

router = APIRouter()


@router.get("/{meal_id}/recommendations", response_model=List[schemas.HealthRecommendation])
def read_recommendations(
    *,
    db: Session = Depends(deps.get_db),
    meal_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取餐食的健康建议列表。
    """
    meal = crud.meal.get(db=db, id=meal_id)
    if not meal:
        raise HTTPException(status_code=404, detail="餐食记录不存在")
    if meal.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限查看此餐食的健康建议")
    
    return meal.health_recommendations


@router.get("/recommendations/{recommendation_id}", response_model=schemas.HealthRecommendation)
def read_recommendation(
    *,
    db: Session = Depends(deps.get_db),
    recommendation_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取单个健康建议详情。
    """
    recommendation = crud.health_recommendation.get(db=db, id=recommendation_id)
    if not recommendation:
        raise HTTPException(status_code=404, detail="健康建议不存在")
    
    meal = crud.meal.get(db=db, id=recommendation.meal_record_id)
    if meal.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限查看此健康建议")
    
    return recommendation


@router.post("/{meal_id}/recommendations", response_model=schemas.HealthRecommendation)
def create_recommendation(
    *,
    db: Session = Depends(deps.get_db),
    meal_id: int,
    recommendation_in: schemas.HealthRecommendationCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    为餐食添加健康建议。
    """
    meal = crud.meal.get(db=db, id=meal_id)
    if not meal:
        raise HTTPException(status_code=404, detail="餐食记录不存在")
    if meal.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限为此餐食添加健康建议")
    
    return crud.health_recommendation.create_with_meal_id(
        db=db, 
        obj_in=recommendation_in, 
        meal_id=meal_id
    )


@router.put("/recommendations/{recommendation_id}", response_model=schemas.HealthRecommendation)
def update_recommendation(
    *,
    db: Session = Depends(deps.get_db),
    recommendation_id: int,
    recommendation_in: schemas.HealthRecommendationUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    更新健康建议。
    """
    recommendation = crud.health_recommendation.get(db=db, id=recommendation_id)
    if not recommendation:
        raise HTTPException(status_code=404, detail="健康建议不存在")
    
    meal = crud.meal.get(db=db, id=recommendation.meal_record_id)
    if meal.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限更新此健康建议")
    
    return crud.health_recommendation.update(
        db=db, 
        db_obj=recommendation, 
        obj_in=recommendation_in
    )


@router.delete("/recommendations/{recommendation_id}", response_model=schemas.HealthRecommendation)
def delete_recommendation(
    *,
    db: Session = Depends(deps.get_db),
    recommendation_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    删除健康建议。
    """
    recommendation = crud.health_recommendation.get(db=db, id=recommendation_id)
    if not recommendation:
        raise HTTPException(status_code=404, detail="健康建议不存在")
    
    meal = crud.meal.get(db=db, id=recommendation.meal_record_id)
    if meal.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限删除此健康建议")
    
    return crud.health_recommendation.remove(db=db, id=recommendation_id) 