from typing import Any, List, Optional, Dict
from fastapi import APIRouter, Depends, HTTPException, Body, WebSocket, WebSocketDisconnect, status
from sqlalchemy.orm import Session
from langchain_core.messages import HumanMessage, AIMessage
import logging
import uuid
import json
import asyncio
import datetime
from pydantic import BaseModel, Field

from app import schemas, models, crud
from app.api import deps
from app.db.session import get_db
from app.services.llm_proxy_service import LLMProxyService
from app.services.langgraph_service import LangGraphService
from app.services.ai_assistant.conversation.orchestrator import conversation_orchestrator

logger = logging.getLogger(__name__)

router = APIRouter()

class ChatMessage(BaseModel):
    """聊天消息模型"""
    message: str = Field(..., description="用户消息内容")
    conversation_id: Optional[str] = Field(None, description="对话ID，如果为空则创建新对话")
    user_id: Optional[str] = Field(None, description="用户ID")

class ChatResponse(BaseModel):
    """聊天响应模型"""
    conversation_id: str = Field(..., description="对话ID")
    response: str = Field(..., description="AI助手响应内容")
    success: bool = Field(..., description="处理是否成功")
    intent_type: Optional[str] = Field(None, description="识别的意图类型")
    confidence: Optional[float] = Field(None, description="意图识别置信度")
    end_conversation: bool = Field(False, description="是否结束对话")
    next_intent: Optional[str] = Field(None, description="下一个需要处理的意图")
    missing_parameters: Optional[list] = Field(None, description="缺失的参数列表")
    error: Optional[str] = Field(None, description="错误信息")

@router.post("/message", response_model=schemas.ChatResponse)
async def create_message(
    *,
    db: Session = Depends(deps.get_db),
    message_in: schemas.ChatRequest,
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """
    处理用户消息并返回AI响应
    """
    try:
        # 初始化LangGraph服务
        langgraph_service = LangGraphService(db)

        # 处理消息
        meta_info = {"quick_intent": message_in.quick_intent} if message_in.quick_intent else {}
        result = await langgraph_service.process_message(
            message=message_in.message,
            session_id=message_in.session_id,
            user_id=current_user.id,
            meta_info=meta_info
        )

        # 返回结果
        return {
            "response": result["response"],
            "session_id": result["session_id"],
            "meta_info": result["meta_info"]
        }
    except Exception as e:
        logger.error(f"处理消息时出错: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"处理消息时出错: {str(e)}")

@router.get("/conversations", response_model=List[schemas.Conversation])
def get_user_conversations(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    skip: int = 0,
    limit: int = 10
) -> Any:
    """
    获取用户的会话列表
    """
    conversations = crud.crud_conversation.get_multi_by_user(
        db, user_id=current_user.id, skip=skip, limit=limit
    )
    return conversations

@router.get("/conversations/{session_id}/messages", response_model=List[schemas.Message])
def get_conversation_messages(
    *,
    db: Session = Depends(deps.get_db),
    session_id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
    skip: int = 0,
    limit: int = 50
) -> Any:
    """
    获取会话的消息列表
    """
    conversation = crud.crud_conversation.get_by_session_id(db, session_id=session_id)
    if not conversation or conversation.user_id != current_user.id:
        raise HTTPException(status_code=404, detail="会话不存在或无权访问")

    messages = crud.crud_message.get_multi_by_conversation(
        db, conversation_id=conversation.id, skip=skip, limit=limit
    )
    return messages

@router.delete("/conversations/{session_id}", response_model=schemas.BasicResponse)
def delete_conversation(
    *,
    db: Session = Depends(deps.get_db),
    session_id: str,
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """
    删除会话
    """
    conversation = crud.crud_conversation.get_by_session_id(db, session_id=session_id)
    if not conversation or conversation.user_id != current_user.id:
        raise HTTPException(status_code=404, detail="会话不存在或无权访问")

    crud.crud_conversation.remove(db, id=conversation.id)
    return {"status": "success", "message": "会话已删除"}

@router.websocket("/stream/{session_id}")
async def websocket_stream(
    websocket: WebSocket,
    session_id: str,
    db: Session = Depends(deps.get_db)
):
    """
    WebSocket流式响应
    """
    await websocket.accept()

    try:
        # 验证身份
        # 注意：WebSocket中无法使用标准的OAuth2依赖，需要手动验证token
        # 这里简化处理，实际应用中应验证token

        # 初始化LangGraph服务
        langgraph_service = LangGraphService(db)

        while True:
            # 接收消息
            data = await websocket.receive_text()
            message_data = json.loads(data)

            user_message = message_data.get("message", "")
            meta_info = message_data.get("meta_info", {})
            meta_info["session_id"] = session_id

            if not user_message:
                await websocket.send_json({"event": "error", "message": "消息不能为空"})
                continue

            # 获取用户ID（实际应用中应从验证的token中获取）
            user_id = meta_info.get("user_id")

            # 处理消息流
            try:
                async for chunk in langgraph_service.process_message_stream(
                    message=user_message,
                    session_id=session_id,
                    user_id=user_id,
                    meta_info=meta_info
                ):
                    if isinstance(chunk, dict):
                        # 事件消息
                        await websocket.send_json(chunk)
                    else:
                        # 文本片段
                        await websocket.send_text(chunk)
            except Exception as e:
                logger.error(f"流处理消息时出错: {str(e)}", exc_info=True)
                error_msg = {"event": "error", "message": f"处理消息时出错: {str(e)}"}
                await websocket.send_json(error_msg)

    except WebSocketDisconnect:
        # 客户端断开连接
        logger.info(f"WebSocket连接断开: {session_id}")
        pass
    except Exception as e:
        # 尝试发送错误消息
        logger.error(f"WebSocket处理错误: {str(e)}", exc_info=True)
        try:
            error_msg = {"event": "error", "message": f"服务器错误: {str(e)}"}
            await websocket.send_json(error_msg)
        except:
            # 如果发送失败，忽略
            pass

@router.post("/ai_chat", response_model=ChatResponse, status_code=status.HTTP_200_OK)
async def chat(message: ChatMessage) -> Dict[str, Any]:
    """
    与重构后的AI助手对话
    
    接收用户消息，返回AI助手的响应。
    """
    try:
        # 记录API调用
        logger.info(f"AI Chat API called with message: {message.message[:50]}...")
        
        # 处理消息
        response = await conversation_orchestrator.process_message(
            user_input=message.message,
            conversation_id=message.conversation_id,
            user_id=message.user_id
        )
        
        # 记录API调用完成
        logger.info(f"AI Chat API completed for conversation: {response.get('conversation_id')}")
        
        return response
        
    except Exception as e:
        # 记录错误
        logger.error(f"Error in AI chat API: {str(e)}")
        
        # 返回错误响应
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"处理消息时发生错误: {str(e)}"
        )

@router.get("/ai_conversations/{conversation_id}", status_code=status.HTTP_200_OK)
async def get_conversation(conversation_id: str) -> Dict[str, Any]:
    """
    获取重构后的对话历史
    
    根据对话ID获取对话历史记录。
    """
    try:
        # 记录API调用
        logger.info(f"Get AI conversation API called for conversation: {conversation_id}")
        
        # 检查对话是否存在
        if conversation_id not in conversation_orchestrator.conversations:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"对话 {conversation_id} 不存在"
            )
        
        # 获取对话历史
        conversation = conversation_orchestrator.conversations[conversation_id]
        
        # 记录API调用完成
        logger.info(f"Get AI conversation API completed for conversation: {conversation_id}")
        
        return {
            "conversation_id": conversation_id,
            "messages": conversation.get("messages", []),
            "state": conversation.get("state", "unknown"),
            "created_at": conversation.get("created_at"),
            "updated_at": conversation.get("updated_at")
        }
        
    except HTTPException:
        # 重新抛出HTTP异常
        raise
        
    except Exception as e:
        # 记录错误
        logger.error(f"Error in get AI conversation API: {str(e)}")
        
        # 返回错误响应
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取对话历史时发生错误: {str(e)}"
        )