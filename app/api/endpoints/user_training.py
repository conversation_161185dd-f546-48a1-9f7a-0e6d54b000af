from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Body
from sqlalchemy.orm import Session
from datetime import datetime, date

from app.db.session import get_db
from app.models.user import User
from app.models.workout_exercise import WorkoutExercise
from app.models.set_record import SetRecord
from app.api.deps import get_current_user
from app.models.exercise import Exercise

router = APIRouter()

@router.get("/", response_model=List[dict])
def get_user_training_records(
    date: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取用户的训练记录（基于 WorkoutExercise + SetRecord）

    参数:
    - date: 可选，指定日期（格式：YYYY-MM-DD）
    """
    try:
        # 查询用户的 SetRecord，通过 WorkoutExercise 关联
        query = db.query(SetRecord).join(WorkoutExercise).filter(
            # 只查询独立的训练记录（不属于任何 workout 或 template）
            WorkoutExercise.workout_id.is_(None),
            WorkoutExercise.template_id.is_(None),
            WorkoutExercise.daily_workout_id.is_(None)
        )

        # 如果指定了日期，添加日期过滤
        if date:
            try:
                target_date = datetime.strptime(date, "%Y-%m-%d").date()
                query = query.filter(
                    db.func.date(SetRecord.created_at) == target_date
                )
            except ValueError:
                raise HTTPException(status_code=400, detail="日期格式无效，请使用YYYY-MM-DD格式")

        # 按创建时间降序排序
        set_records = query.order_by(SetRecord.created_at.desc()).all()

        # 按 WorkoutExercise 分组
        exercise_groups = {}
        for set_record in set_records:
            workout_exercise = set_record.workout_exercise
            if workout_exercise.id not in exercise_groups:
                exercise_groups[workout_exercise.id] = {
                    "workout_exercise": workout_exercise,
                    "set_records": []
                }
            exercise_groups[workout_exercise.id]["set_records"].append(set_record)

        # 转换为返回格式
        result = []
        for group in exercise_groups.values():
            workout_exercise = group["workout_exercise"]
            set_records = group["set_records"]

            exercise_dict = {
                "id": workout_exercise.id,
                "exercise_id": workout_exercise.exercise_id,
                "exercise_name": workout_exercise.exercise.name if workout_exercise.exercise else None,
                "muscle_group": workout_exercise.exercise.muscle_group if workout_exercise.exercise else None,
                "sets": workout_exercise.sets,
                "reps": workout_exercise.reps,
                "weight": workout_exercise.weight,
                "rest_seconds": workout_exercise.rest_seconds,
                "notes": workout_exercise.notes,
                "exercise_type": workout_exercise.exercise_type,
                "set_records": [
                    {
                        "id": sr.id,
                        "set_number": sr.set_number,
                        "set_type": sr.set_type,
                        "weight": sr.weight,
                        "reps": sr.reps,
                        "completed": sr.completed,
                        "notes": sr.notes,
                        "created_at": sr.created_at.isoformat() if sr.created_at else None,
                        "updated_at": sr.updated_at.isoformat() if sr.updated_at else None
                    }
                    for sr in sorted(set_records, key=lambda x: x.set_number)
                ],
                "total_sets_completed": sum(1 for sr in set_records if sr.completed),
                "training_date": set_records[0].created_at.date().isoformat() if set_records else None
            }
            result.append(exercise_dict)

        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取训练记录失败: {str(e)}")

@router.post("/", response_model=dict)
def create_training_record(
    record_data: dict = Body(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    创建新的训练记录（WorkoutExercise + SetRecord）

    参数:
    - exercise_id: 动作ID
    - sets: 计划组数
    - reps: 计划次数
    - weight: 计划重量
    - rest_seconds: 休息时间
    - notes: 备注
    - set_records: 实际完成的组数据数组
    - training_date: 训练日期(可选，默认今天)
    """
    try:
        exercise_id = record_data.get("exercise_id")
        if not exercise_id:
            raise HTTPException(status_code=400, detail="exercise_id 不能为空")

        # 检查动作是否存在
        exercise = db.query(Exercise).filter(Exercise.id == exercise_id).first()
        if not exercise:
            raise HTTPException(status_code=400, detail="指定的练习不存在")

        # 解析训练日期
        training_date = record_data.get("training_date")
        if training_date:
            try:
                target_date = datetime.strptime(training_date, "%Y-%m-%d").date()
            except ValueError:
                raise HTTPException(status_code=400, detail="日期格式无效，请使用YYYY-MM-DD格式")
        else:
            target_date = date.today()

        # 创建 WorkoutExercise（独立的，不关联任何 workout）
        workout_exercise = WorkoutExercise(
            exercise_id=exercise_id,
            sets=record_data.get("sets", 3),
            reps=str(record_data.get("reps", "10")),
            weight=record_data.get("weight"),
            rest_seconds=record_data.get("rest_seconds", 60),
            order=1,
            notes=record_data.get("notes"),
            exercise_type=record_data.get("exercise_type", "weight_reps")
        )

        db.add(workout_exercise)
        db.flush()  # 获取ID

        # 创建 SetRecord
        set_records_data = record_data.get("set_records", [])
        created_set_records = []

        if set_records_data:
            # 如果提供了具体的组数据
            for set_data in set_records_data:
                set_record = SetRecord(
                    workout_exercise_id=workout_exercise.id,
                    set_number=set_data.get("set_number", 1),
                    set_type=set_data.get("set_type", "normal"),
                    weight=float(set_data.get("weight")) if set_data.get("weight") else None,
                    reps=int(set_data.get("reps")) if set_data.get("reps") else None,
                    completed=set_data.get("completed", False),
                    notes=set_data.get("notes"),
                    created_at=datetime.combine(target_date, datetime.min.time())
                )
                db.add(set_record)
                created_set_records.append(set_record)
        else:
            # 如果没有提供具体组数据，创建默认的空组
            for set_num in range(1, workout_exercise.sets + 1):
                set_record = SetRecord(
                    workout_exercise_id=workout_exercise.id,
                    set_number=set_num,
                    set_type="normal",
                    completed=False,
                    created_at=datetime.combine(target_date, datetime.min.time())
                )
                db.add(set_record)
                created_set_records.append(set_record)

        db.commit()
        db.refresh(workout_exercise)

        # 返回创建的记录
        return {
            "id": workout_exercise.id,
            "exercise_id": workout_exercise.exercise_id,
            "exercise_name": exercise.name,
            "sets": workout_exercise.sets,
            "reps": workout_exercise.reps,
            "weight": workout_exercise.weight,
            "rest_seconds": workout_exercise.rest_seconds,
            "notes": workout_exercise.notes,
            "exercise_type": workout_exercise.exercise_type,
            "set_records": [
                {
                    "id": sr.id,
                    "set_number": sr.set_number,
                    "set_type": sr.set_type,
                    "weight": sr.weight,
                    "reps": sr.reps,
                    "completed": sr.completed,
                    "notes": sr.notes
                }
                for sr in created_set_records
            ],
            "training_date": target_date.isoformat(),
            "message": "训练记录创建成功"
        }

    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"创建训练记录失败: {str(e)}")

@router.delete("/{workout_exercise_id}")
def delete_training_record(
    workout_exercise_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    删除训练记录（WorkoutExercise 及其关联的 SetRecord）
    """
    try:
        # 查找 WorkoutExercise（只能删除独立的训练记录）
        workout_exercise = db.query(WorkoutExercise).filter(
            WorkoutExercise.id == workout_exercise_id,
            WorkoutExercise.workout_id.is_(None),
            WorkoutExercise.template_id.is_(None),
            WorkoutExercise.daily_workout_id.is_(None)
        ).first()

        if not workout_exercise:
            raise HTTPException(status_code=404, detail="训练记录不存在或无权删除")

        # 删除 WorkoutExercise（级联删除会自动删除关联的 SetRecord）
        db.delete(workout_exercise)
        db.commit()

        return {"message": "训练记录已删除"}

    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"删除训练记录失败: {str(e)}")

@router.post("/batch-delete", response_model=dict)
def batch_delete_training_records(
    workout_exercise_ids: List[int] = Body(..., alias="record_ids"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    批量删除训练记录

    参数:
    - record_ids: 要删除的 WorkoutExercise ID列表
    """
    try:
        if not workout_exercise_ids:
            raise HTTPException(status_code=400, detail="记录ID列表不能为空")

        # 找出所有独立的训练记录
        workout_exercises = db.query(WorkoutExercise).filter(
            WorkoutExercise.id.in_(workout_exercise_ids),
            WorkoutExercise.workout_id.is_(None),
            WorkoutExercise.template_id.is_(None),
            WorkoutExercise.daily_workout_id.is_(None)
        ).all()

        # 检查是否找到所有记录
        found_ids = {record.id for record in workout_exercises}
        not_found_ids = set(workout_exercise_ids) - found_ids

        if not_found_ids:
            raise HTTPException(status_code=404, detail=f"未找到以下记录: {list(not_found_ids)}")

        # 删除找到的所有记录（级联删除会自动删除关联的 SetRecord）
        for record in workout_exercises:
            db.delete(record)

        db.commit()

        return {"message": f"成功删除 {len(workout_exercises)} 条训练记录"}

    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"批量删除训练记录失败: {str(e)}")

# 添加更新 SetRecord 的端点
@router.put("/set-record/{set_record_id}", response_model=dict)
def update_set_record(
    set_record_id: int,
    set_data: dict = Body(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    更新单个组记录

    参数:
    - weight: 重量
    - reps: 次数
    - completed: 是否完成
    - notes: 备注
    """
    try:
        # 查找 SetRecord（通过 WorkoutExercise 确保权限）
        set_record = db.query(SetRecord).join(WorkoutExercise).filter(
            SetRecord.id == set_record_id,
            WorkoutExercise.workout_id.is_(None),
            WorkoutExercise.template_id.is_(None),
            WorkoutExercise.daily_workout_id.is_(None)
        ).first()

        if not set_record:
            raise HTTPException(status_code=404, detail="组记录不存在或无权修改")

        # 更新字段
        if "weight" in set_data:
            set_record.weight = float(set_data["weight"]) if set_data["weight"] else None
        if "reps" in set_data:
            set_record.reps = int(set_data["reps"]) if set_data["reps"] else None
        if "completed" in set_data:
            set_record.completed = bool(set_data["completed"])
        if "notes" in set_data:
            set_record.notes = set_data["notes"]

        db.commit()
        db.refresh(set_record)

        return {
            "id": set_record.id,
            "set_number": set_record.set_number,
            "weight": set_record.weight,
            "reps": set_record.reps,
            "completed": set_record.completed,
            "notes": set_record.notes,
            "message": "组记录更新成功"
        }

    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"更新组记录失败: {str(e)}")