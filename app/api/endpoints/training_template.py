from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Body, Query
from sqlalchemy.orm import Session
from datetime import datetime

from app.db.session import get_db
from app.models.user import User
from app.models.training_template import WorkoutTemplate  # 更新导入
from app.models.workout_exercise import WorkoutExercise
from app.models.set_record import SetRecord
from app.models.exercise import Exercise
from app.api.deps import get_current_user

router = APIRouter()

@router.get("/", response_model=List[dict])
def get_workout_templates(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取用户的所有训练模板
    """
    try:
        templates = db.query(WorkoutTemplate).filter(
            WorkoutTemplate.user_id == current_user.id
        ).order_by(WorkoutTemplate.created_at.desc()).all()

        return [template.to_dict() for template in templates]

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取训练模板失败: {str(e)}")

@router.get("/{template_id}", response_model=dict)
def get_workout_template(
    template_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    获取特定训练模板的详情
    """
    try:
        template = db.query(WorkoutTemplate).filter(
            WorkoutTemplate.id == template_id,
            WorkoutTemplate.user_id == current_user.id
        ).first()

        if not template:
            raise HTTPException(status_code=404, detail="训练模板不存在或无权访问")

        return template.to_dict()

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取训练模板详情失败: {str(e)}")

@router.post("/", response_model=dict)
def create_workout_template(
    template_data: dict = Body(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    创建新的训练模板

    参数:
    - name: 模板名称
    - description: 描述(可选)
    - estimated_duration: 预计时长(可选)
    - target_body_parts: 目标训练部位(可选)
    - training_scenario: 训练场景(可选)
    - exercises: 训练动作信息数组
    - notes: 备注(可选)
    """
    try:
        name = template_data.get("name")
        exercises = template_data.get("exercises")

        if not name:
            raise HTTPException(status_code=400, detail="模板名称不能为空")

        if not exercises or not isinstance(exercises, list) or len(exercises) == 0:
            raise HTTPException(status_code=400, detail="训练动作不能为空")

        # 创建新的训练模板
        new_template = WorkoutTemplate(
            user_id=current_user.id,
            name=name,
            description=template_data.get("description"),
            estimated_duration=template_data.get("estimated_duration"),
            target_body_parts=template_data.get("target_body_parts"),
            training_scenario=template_data.get("training_scenario"),
            notes=template_data.get("notes")
        )

        db.add(new_template)
        db.flush()  # 获取模板ID

        # 创建关联的训练动作
        for idx, exercise_data in enumerate(exercises):
            workout_exercise = WorkoutExercise(
                template_id=new_template.id,
                exercise_id=exercise_data.get("exercise_id"),
                sets=exercise_data.get("sets", 3),
                reps=str(exercise_data.get("reps", "10")),
                weight=exercise_data.get("weight"),
                rest_seconds=exercise_data.get("rest_seconds", 60),
                order=idx + 1,
                notes=exercise_data.get("notes"),
                exercise_type=exercise_data.get("exercise_type", "weight_reps"),
                superset_group=exercise_data.get("superset_group")
            )
            db.add(workout_exercise)

        db.commit()
        db.refresh(new_template)

        return new_template.to_dict()

    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"创建训练模板失败: {str(e)}")

@router.delete("/{template_id}")
def delete_workout_template(
    template_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    删除训练模板
    """
    try:
        template = db.query(WorkoutTemplate).filter(
            WorkoutTemplate.id == template_id,
            WorkoutTemplate.user_id == current_user.id
        ).first()

        if not template:
            raise HTTPException(status_code=404, detail="训练模板不存在或无权删除")

        db.delete(template)  # 级联删除会自动删除关联的 WorkoutExercise
        db.commit()

        return {"message": "训练模板已删除"}

    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"删除训练模板失败: {str(e)}")

@router.post("/{template_id}/apply", response_model=List[dict])
def apply_workout_template(
    template_id: int,
    date: Optional[str] = Query(None),
    body: Optional[dict] = Body(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    应用训练模板到指定日期，创建 WorkoutExercise 和 SetRecord

    参数:
    - template_id: 模板ID
    - date: 应用日期(YYYY-MM-DD)，可以作为查询参数或请求体参数提供
    """
    try:
        # 从查询参数或请求体中获取日期
        date_value = date
        if not date_value and body and "date" in body:
            date_value = body.get("date")

        if not date_value:
            raise HTTPException(status_code=400, detail="缺少必要参数: date")

        # 验证日期格式
        try:
            target_date = datetime.strptime(date_value, "%Y-%m-%d").date()
        except ValueError:
            raise HTTPException(status_code=400, detail="日期格式无效，请使用YYYY-MM-DD格式")

        # 获取模板
        template = db.query(WorkoutTemplate).filter(
            WorkoutTemplate.id == template_id,
            WorkoutTemplate.user_id == current_user.id
        ).first()

        if not template:
            raise HTTPException(status_code=404, detail="训练模板不存在或无权访问")

        # 获取模板中的训练动作
        if not template.template_exercises or len(template.template_exercises) == 0:
            raise HTTPException(status_code=400, detail="该训练模板中没有训练动作")

        # 创建新的 WorkoutExercise 记录（独立的，不关联任何 workout）
        created_exercises = []
        for template_exercise in template.template_exercises:
            # 创建新的 WorkoutExercise（复制模板配置）
            new_workout_exercise = WorkoutExercise(
                exercise_id=template_exercise.exercise_id,
                sets=template_exercise.sets,
                reps=template_exercise.reps,
                weight=template_exercise.weight,
                rest_seconds=template_exercise.rest_seconds,
                order=template_exercise.order,
                notes=template_exercise.notes,
                exercise_type=template_exercise.exercise_type,
                superset_group=template_exercise.superset_group
            )
            db.add(new_workout_exercise)
            db.flush()  # 获取ID

            # 为每个计划的组创建 SetRecord（初始状态为未完成）
            for set_num in range(1, template_exercise.sets + 1):
                set_record = SetRecord(
                    workout_exercise_id=new_workout_exercise.id,
                    set_number=set_num,
                    set_type="normal",
                    weight=float(template_exercise.weight) if template_exercise.weight and template_exercise.weight.replace('.', '').isdigit() else None,
                    reps=int(template_exercise.reps) if template_exercise.reps.isdigit() else None,
                    completed=False,
                    created_at=datetime.combine(target_date, datetime.min.time())
                )
                db.add(set_record)

            created_exercises.append(new_workout_exercise)

        db.commit()

        # 返回创建的训练动作信息
        result = []
        for exercise in created_exercises:
            db.refresh(exercise)  # 刷新以获取关联数据
            exercise_dict = {
                "id": exercise.id,
                "exercise_id": exercise.exercise_id,
                "exercise_name": exercise.exercise.name if exercise.exercise else None,
                "sets": exercise.sets,
                "reps": exercise.reps,
                "weight": exercise.weight,
                "rest_seconds": exercise.rest_seconds,
                "order": exercise.order,
                "notes": exercise.notes,
                "exercise_type": exercise.exercise_type,
                "set_records": [
                    {
                        "id": sr.id,
                        "set_number": sr.set_number,
                        "weight": sr.weight,
                        "reps": sr.reps,
                        "completed": sr.completed,
                        "notes": sr.notes
                    }
                    for sr in exercise.set_records
                ],
                "applied_date": target_date.isoformat()
            }
            result.append(exercise_dict)

        return result

    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"应用训练模板失败: {str(e)}")