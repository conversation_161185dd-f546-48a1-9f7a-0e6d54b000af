from typing import Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app import crud, models, schemas
from app.api import deps
from app.services.meal_service import MealService

router = APIRouter()


@router.get("/food-items/{food_item_id}/nutrient-intakes", response_model=List[schemas.FoodItemNutrientIntake])
def read_nutrient_intakes(
    *,
    db: Session = Depends(deps.get_db),
    food_item_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取食物项的营养素摄入列表。
    """
    food_item = crud.food_item.get(db=db, id=food_item_id)
    if not food_item:
        raise HTTPException(status_code=404, detail="食物项不存在")
    
    meal = crud.meal.get(db=db, id=food_item.meal_record_id)
    if meal.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限查看此食物项的营养素数据")
    
    return food_item.nutrient_intakes


@router.get("/nutrient-intakes/{nutrient_intake_id}", response_model=schemas.FoodItemNutrientIntake)
def read_nutrient_intake(
    *,
    db: Session = Depends(deps.get_db),
    nutrient_intake_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取单个营养素摄入详情。
    """
    nutrient_intake = crud.food_item_nutrient_intake.get(db=db, id=nutrient_intake_id)
    if not nutrient_intake:
        raise HTTPException(status_code=404, detail="营养素摄入数据不存在")
    
    food_item = crud.food_item.get(db=db, id=nutrient_intake.food_item_id)
    meal = crud.meal.get(db=db, id=food_item.meal_record_id)
    if meal.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限查看此营养素数据")
    
    return nutrient_intake


@router.post("/food-items/{food_item_id}/nutrient-intakes", response_model=schemas.FoodItemNutrientIntake)
def create_nutrient_intake(
    *,
    db: Session = Depends(deps.get_db),
    food_item_id: int,
    nutrient_intake_in: schemas.FoodItemNutrientIntakeCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    为食物项添加营养素摄入数据。
    """
    food_item = crud.food_item.get(db=db, id=food_item_id)
    if not food_item:
        raise HTTPException(status_code=404, detail="食物项不存在")
    
    meal = crud.meal.get(db=db, id=food_item.meal_record_id)
    if meal.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限修改此食物项的营养素数据")
    
    return crud.food_item_nutrient_intake.create_with_food_item_id(
        db=db, 
        obj_in=nutrient_intake_in, 
        food_item_id=food_item_id
    )


@router.put("/nutrient-intakes/{nutrient_intake_id}", response_model=schemas.FoodItemNutrientIntake)
def update_nutrient_intake(
    *,
    db: Session = Depends(deps.get_db),
    nutrient_intake_id: int,
    nutrient_intake_in: schemas.FoodItemNutrientIntakeUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    更新营养素摄入数据。
    """
    nutrient_intake = crud.food_item_nutrient_intake.get(db=db, id=nutrient_intake_id)
    if not nutrient_intake:
        raise HTTPException(status_code=404, detail="营养素摄入数据不存在")
    
    food_item = crud.food_item.get(db=db, id=nutrient_intake.food_item_id)
    meal = crud.meal.get(db=db, id=food_item.meal_record_id)
    if meal.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限修改此营养素数据")
    
    return crud.food_item_nutrient_intake.update(
        db=db, 
        db_obj=nutrient_intake, 
        obj_in=nutrient_intake_in
    )


@router.delete("/nutrient-intakes/{nutrient_intake_id}", response_model=schemas.FoodItemNutrientIntake)
def delete_nutrient_intake(
    *,
    db: Session = Depends(deps.get_db),
    nutrient_intake_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    删除营养素摄入数据。
    """
    nutrient_intake = crud.food_item_nutrient_intake.get(db=db, id=nutrient_intake_id)
    if not nutrient_intake:
        raise HTTPException(status_code=404, detail="营养素摄入数据不存在")
    
    food_item = crud.food_item.get(db=db, id=nutrient_intake.food_item_id)
    meal = crud.meal.get(db=db, id=food_item.meal_record_id)
    if meal.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限删除此营养素数据")
    
    return crud.food_item_nutrient_intake.remove(db=db, id=nutrient_intake_id) 