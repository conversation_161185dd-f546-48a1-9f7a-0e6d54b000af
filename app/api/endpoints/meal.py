from typing import Any, List, Optional
from datetime import date, timedelta
import os.path

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app import crud, models, schemas
from app.schemas.meal import FoodItemCreate, FoodItemUpdate
from app.api import deps
from app.services.meal_service import MealService
import re

router = APIRouter()

# 添加URL转换函数
def convert_to_simplified_image_url(original_url: str) -> str:
    """
    将完整的食物图片URL转换为简化版本
    原始格式: /food/images/users/.../food_recognition/.../filename.jpg
    简化格式: /meal-images/filename.jpg
    
    注意：
    - 对于存储在/data/users下的餐食识别图片，使用简化格式
    - 对于存储在/data/food下的食物项图片，保留完整路径
    """
    if not original_url:
        return original_url
    
    # 检查是否是食物项图片路径(存储在/data/food中)
    if '/data/food/' in original_url or original_url.startswith('/food/'):
        # 食物项图片保留原路径
        return original_url
        
    # 提取文件名
    match = re.search(r'([^/]+\.(jpg|jpeg|png|gif))$', original_url)
    if match:
        filename = match.group(1)
        return f"/meal-images/{filename}"
    
    return original_url

# 添加文件名提取函数
def extract_filename(path: str) -> str:
    """
    从完整路径中提取文件名
    '/path/to/file.jpg' -> 'file.jpg'
    """
    if not path:
        return path
    
    # 提取最后一个斜杠后的部分
    return os.path.basename(path)

@router.get("/", response_model=List[schemas.MealRecordSummary])
def read_user_meals(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """
    获取当前用户的餐食记录列表。
    """
    # 默认为当天
    if not start_date:
        start_date = date.today()
    if not end_date:
        end_date = start_date
        
    return crud.meal.get_user_meals(
        db=db, 
        user_id=current_user.id, 
        start_date=start_date, 
        end_date=end_date,
        skip=skip,
        limit=limit
    )


@router.get("/daily", response_model=schemas.DailyNutritionSummary)
def read_daily_nutrition(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    query_date: date = Query(None)
) -> Any:
    """
    获取用户某一天的营养摄入汇总。
    """
    if not query_date:
        query_date = date.today()
        
    return MealService.get_daily_nutrition(
        db=db,
        user_id=current_user.id,
        query_date=query_date
    )


@router.get("/{meal_id}", response_model=schemas.MealRecord)
def read_meal(
    *,
    db: Session = Depends(deps.get_db),
    meal_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取餐食详情。
    """
    meal = crud.meal.get(db=db, id=meal_id)
    if not meal:
        raise HTTPException(status_code=404, detail="餐食记录不存在")
    if meal.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限查看此餐食记录")
    
    # 确保餐食营养总和是所有food_item的总和
    meal = MealService.update_meal_nutrition_totals(db=db, meal_id=meal_id)
    
    # 如果餐食没有food_items，返回404错误
    if not meal.food_items:
        raise HTTPException(status_code=404, detail="餐食记录不存在食物项")
    
    # 转换所有food_item的图片URL为简化格式
    for food_item in meal.food_items:
        if food_item.image_url:
            food_item.image_url = convert_to_simplified_image_url(food_item.image_url)
    
    # 转换餐食本身的图片URL (如果有)
    if meal.image_url:
        meal.image_url = convert_to_simplified_image_url(meal.image_url)
    
    # 转换餐食缩略图URL (如果有)
    if hasattr(meal, 'thumb_image_url') and meal.thumb_image_url:
        meal.thumb_image_url = convert_to_simplified_image_url(meal.thumb_image_url)
        
    return meal


@router.post("/", response_model=schemas.MealRecord)
def create_meal(
    *,
    db: Session = Depends(deps.get_db),
    meal_in: schemas.MealRecordCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    创建新的餐食记录。
    """
    # 修改image_url，只保留文件名
    if meal_in.image_url:
        meal_in.image_url = extract_filename(meal_in.image_url)
        
    # 修改thumb_image_url，只保留文件名
    if meal_in.thumb_image_url:
        meal_in.thumb_image_url = extract_filename(meal_in.thumb_image_url)
        
    return crud.meal.create_with_user_id(
        db=db, 
        obj_in=meal_in, 
        user_id=current_user.id
    )


@router.put("/{meal_id}", response_model=schemas.MealRecord)
def update_meal(
    *,
    db: Session = Depends(deps.get_db),
    meal_id: int,
    meal_in: schemas.MealRecordUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    更新餐食记录。
    """
    meal = crud.meal.get(db=db, id=meal_id)
    if not meal:
        raise HTTPException(status_code=404, detail="餐食记录不存在")
    if meal.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限更新此餐食记录")
    
    # 如果更新包含图片URL，只保留文件名
    if hasattr(meal_in, "image_url") and meal_in.image_url:
        meal_in.image_url = extract_filename(meal_in.image_url)
        
    if hasattr(meal_in, "thumb_image_url") and meal_in.thumb_image_url:
        meal_in.thumb_image_url = extract_filename(meal_in.thumb_image_url)
        
    return crud.meal.update(db=db, db_obj=meal, obj_in=meal_in)


@router.delete("/{meal_id}", response_model=schemas.MealRecord)
def delete_meal(
    *,
    db: Session = Depends(deps.get_db),
    meal_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    删除餐食记录。
    """
    meal = crud.meal.get(db=db, id=meal_id)
    if not meal:
        raise HTTPException(status_code=404, detail="餐食记录不存在")
    if meal.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限删除此餐食记录")
    return crud.meal.remove(db=db, id=meal_id)


@router.post("/{meal_id}/food-items", response_model=schemas.FoodItem)
def add_food_item(
    *,
    db: Session = Depends(deps.get_db),
    meal_id: int,
    food_item_in: FoodItemCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    向餐食记录添加食物项。
    """
    meal = crud.meal.get(db=db, id=meal_id)
    if not meal:
        raise HTTPException(status_code=404, detail="餐食记录不存在")
    if meal.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限修改此餐食记录")
    
    # 转换图片URL为简化格式
    if food_item_in.image_url:
        food_item_in.image_url = convert_to_simplified_image_url(food_item_in.image_url)
    
    # 创建食物项
    food_item = MealService.add_food_item_to_meal(
        db=db,
        meal_id=meal_id,
        food_item_in=food_item_in
    )
    
    # 更新餐食营养总和
    MealService.update_meal_nutrition_totals(db=db, meal_id=meal_id)
    
    return food_item


@router.delete("/food-items/{food_item_id}", response_model=schemas.FoodItem)
def remove_food_item(
    *,
    db: Session = Depends(deps.get_db),
    food_item_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    从餐食记录移除食物项。
    """
    food_item = crud.food_item.get(db=db, id=food_item_id)
    if not food_item:
        raise HTTPException(status_code=404, detail="食物项不存在")
    
    meal = crud.meal.get(db=db, id=food_item.meal_record_id)
    if meal.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限修改此餐食记录")
    
    food_item = crud.food_item.remove(db=db, id=food_item_id)
    
    # 更新餐食营养总和
    MealService.update_meal_nutrition_totals(db=db, meal_id=food_item.meal_record_id)
    
    return food_item


@router.get("/food-items/{food_item_id}", response_model=schemas.FoodItem)
def read_food_item(
    *,
    db: Session = Depends(deps.get_db),
    food_item_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取食物项详情。
    """
    food_item = crud.food_item.get(db=db, id=food_item_id)
    if not food_item:
        raise HTTPException(status_code=404, detail="食物项不存在")
    
    meal = crud.meal.get(db=db, id=food_item.meal_record_id)
    if meal.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限查看此食物项")
    
    return food_item


@router.put("/food-items/{food_item_id}", response_model=schemas.FoodItem)
def update_food_item(
    *,
    db: Session = Depends(deps.get_db),
    food_item_id: int,
    food_item_in: schemas.FoodItemUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    更新食物项。
    """
    food_item = crud.food_item.get(db=db, id=food_item_id)
    if not food_item:
        raise HTTPException(status_code=404, detail="食物项不存在")
    
    meal = crud.meal.get(db=db, id=food_item.meal_record_id)
    if meal.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限更新此食物项")
    
    food_item = crud.food_item.update(db=db, db_obj=food_item, obj_in=food_item_in)
    
    # 更新餐食营养总和
    MealService.update_meal_nutrition_totals(db=db, meal_id=food_item.meal_record_id)
    
    return food_item 