from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from jose import jwt, JWTError
from datetime import timedelta, datetime
from typing import Dict, Any, Optional, List
import logging
import json
import traceback
import uuid

from app.db.session import get_db
from app.core.config import Settings
from app.core.security import create_access_token
from app.services.user import UserService
from app.services.wechat import WechatService, WechatAPIError
from app.api import deps  # 添加缺少的deps模块导入
# from app.utils.response_logger import log_response_data
from app.schemas.wechat_login import (
    WechatLoginRequest, 
    WechatLoginResponse, 
    WechatPhoneRequest, 
    Token
)
from app.models.user_setting import UserSetting
from app.models.user import User

settings = Settings()
router = APIRouter()
logger = logging.getLogger("fitness-coach-api")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# 定义TokenResponse模型
from pydantic import BaseModel

class TokenResponse(BaseModel):
    """访问令牌响应模型"""
    token: str
    expires_at: str

class WechatCode2SessionRequest(BaseModel):
    """微信code2session请求模型"""
    code: str

class WechatCode2SessionResponse(BaseModel):
    """微信code2session响应模型"""
    openid: str
    unionid: Optional[str] = None

class SafeLogging:
    """敏感信息处理与日志辅助类"""
    
    @staticmethod
    def mask_sensitive_data(data: Dict[str, Any], sensitive_fields: List[str] = None) -> Dict[str, Any]:
        """掩盖敏感字段的信息，例如 token, password, openid 等"""
        if sensitive_fields is None:
            sensitive_fields = ["token", "password", "openid", "unionid", "code", "session_key"]
            
        result = data.copy()
        for field in sensitive_fields:
            if field in result and result[field]:
                if isinstance(result[field], str):
                    # 只显示前5个字符和后2个字符
                    if len(result[field]) > 7:
                        result[field] = result[field][:5] + "***" + result[field][-2:]
                    else:
                        result[field] = "***"
        
        return result
    
    @staticmethod
    def log_request(request_data: Dict[str, Any], client_info: str = None, request_id: str = None) -> None:
        """记录请求数据，自动处理敏感信息"""
        try:
            # 掩盖敏感信息
            safe_data = SafeLogging.mask_sensitive_data(request_data)
            
            # 构建日志消息
            log_parts = ["收到请求"]
            if client_info:
                log_parts.append(f"客户端={client_info}")
            if request_id:
                log_parts.append(f"请求ID={request_id}")
                
            log_message = ", ".join(log_parts) + f": {json.dumps(safe_data, ensure_ascii=False)}"
            logger.info(log_message)
        except Exception as e:
            logger.error(f"记录请求数据失败: {str(e)}")
    
    @staticmethod
    def log_response(response_data: Dict[str, Any], processing_time: Optional[float] = None) -> None:
        """记录响应数据，自动处理敏感信息"""
        try:
            # 掩盖敏感信息
            safe_data = SafeLogging.mask_sensitive_data(response_data)
            
            # 构建日志消息
            log_parts = ["发送响应"]
            if processing_time is not None:
                log_parts.append(f"处理时间={processing_time:.2f}秒")
                
            log_message = ", ".join(log_parts) + f": {json.dumps(safe_data, ensure_ascii=False)}"
            logger.info(log_message)
        except Exception as e:
            logger.error(f"记录响应数据失败: {str(e)}")

def truncate_string(value: str, max_length: int = 20) -> str:
    """截断字符串，使其不超过指定长度"""
    if not value:
        return value
    if len(value) <= max_length:
        return value
    return value[:max_length] + "..."

def truncate_dict(data: Dict[str, Any], max_length: int = 20) -> Dict[str, Any]:
    """截断字典中的字符串值，使其不超过指定长度"""
    result = {}
    for key, value in data.items():
        if isinstance(value, str):
            result[key] = truncate_string(value, max_length)
        elif isinstance(value, dict):
            result[key] = truncate_dict(value, max_length)
        else:
            result[key] = value
    return result

@router.post("/login/wechat", response_model=WechatLoginResponse)
async def wechat_login(
    request: WechatLoginRequest, 
    req: Request,
    db: Session = Depends(get_db)
):
    """微信小程序登录接口"""
    # 开始计时并记录请求信息
    start_time = datetime.now()
    client_host = req.client.host if req.client else "unknown"
    request_id = id(req)
    
    # 使用优化后的日志记录方法
    try:
        request_dict = request.dict()
        SafeLogging.log_request(request_dict, client_host, str(request_id))
    except Exception as e:
        logger.error(f"处理请求数据失败: {str(e)}")
    
    try:
        # 检查必要的参数
        if not request.code and not request.openid:
            error_msg = "缺少必要参数: code或openid"
            logger.error(error_msg)
            raise HTTPException(status_code=400, detail=error_msg)
        
        # 优先使用openid路径 - 如果请求中已提供openid
        if request.openid:
            logger.info(f"使用openid登录流程")
            
            # 直接检查用户是否存在
            try:
                existing_user = UserService.get_by_openid(db, openid=request.openid)
                
                # 如果用户存在，直接创建token并返回
                if existing_user:
                    logger.info(f"找到用户: ID={existing_user.id}")
                    
                    # 创建访问令牌
                    access_token_expires = timedelta(days=settings.ACCESS_TOKEN_EXPIRE_DAYS)
                    access_token = create_access_token(
                        data={"sub": str(existing_user.id)}, 
                        expires_delta=access_token_expires
                    )
                    
                    # 计算过期时间
                    expires_at = datetime.utcnow() + access_token_expires
                    
                    # 构建简化的用户响应 - 只包含必要字段
                    user_data = {
                        "id": existing_user.id,
                        "nickname": existing_user.nickname or "用户",
                        "avatarUrl": existing_user.avatar_url,
                        "completed": getattr(existing_user, 'completed', False)
                    }
                    
                    # 添加可选字段(如果存在)
                    if hasattr(existing_user, 'created_at') and existing_user.created_at:
                        user_data["created_at"] = existing_user.created_at.isoformat()
                    
                    # 构建响应
                    response_data = WechatLoginResponse(
                        success=True,
                        token=access_token,
                        expires_at=expires_at.isoformat(),  # 添加过期时间
                        user=user_data,
                        is_new_user=False  # 已存在用户
                    )
                    
                    # 记录处理时间和响应
                    total_time = (datetime.now() - start_time).total_seconds()
                    SafeLogging.log_response(response_data.dict(), total_time)
                    
                    return response_data
                
                # 用户不存在，但有openid，创建新用户
                else:
                    logger.info(f"创建新用户: openid={request.openid[:5] + '***'}")
                    
                    # 解析用户信息
                    nickname = None
                    avatar_url = None
                    
                    # 尝试从userInfo获取用户信息
                    if request.userInfo:
                        # 尝试nickName字段(微信原始格式)
                        nickname = request.userInfo.get("nickName") 
                        if not nickname:
                            # 尝试nickname字段(标准化格式)
                            nickname = request.userInfo.get("nickname")
                        
                        # 尝试avatarUrl字段(微信原始格式)
                        avatar_url = request.userInfo.get("avatarUrl")
                        if not avatar_url:
                            # 尝试avatar_url字段(标准化格式)
                            avatar_url = request.userInfo.get("avatar_url")
                    
                    # 使用请求中的avatar_url和nickname(如果提供)
                    if hasattr(request, 'avatar_url') and request.avatar_url:
                        avatar_url = request.avatar_url
                    
                    if hasattr(request, 'nickname') and request.nickname:
                        nickname = request.nickname
                    
                    # 如果没有从请求中获取到，则使用默认值
                    if not nickname:
                        nickname = "微信用户"
                    
                    try:
                        # 创建新用户
                        new_user = User(
                            openid=request.openid,
                            unionid=request.unionid,
                            nickname=nickname,
                            avatar_url=avatar_url,
                            completed=False
                        )
                        
                        db.add(new_user)
                        db.commit()
                        db.refresh(new_user)
                        
                        logger.info(f"用户创建成功: ID={new_user.id}")
                        
                        # 创建访问令牌
                        access_token_expires = timedelta(days=settings.ACCESS_TOKEN_EXPIRE_DAYS)
                        access_token = create_access_token(
                            data={"sub": str(new_user.id)}, 
                            expires_delta=access_token_expires
                        )
                        
                        # 计算过期时间
                        expires_at = datetime.utcnow() + access_token_expires
                        
                        # 构建简化的用户响应
                        user_data = {
                            "id": new_user.id,
                            "nickname": new_user.nickname,
                            "avatarUrl": new_user.avatar_url,
                            "created_at": new_user.created_at.isoformat() if hasattr(new_user, 'created_at') and new_user.created_at else datetime.now().isoformat(),
                            "completed": False
                        }
                        
                        # 构建响应
                        response_data = WechatLoginResponse(
                            success=True,
                            token=access_token,
                            expires_at=expires_at.isoformat(),  # 添加过期时间
                            user=user_data,
                            is_new_user=True  # 新用户
                        )
                        
                        # 记录处理时间和响应
                        total_time = (datetime.now() - start_time).total_seconds()
                        SafeLogging.log_response(response_data.dict(), total_time)
                        
                        return response_data
                        
                    except Exception as e:
                        logger.error(f"创建新用户失败: {str(e)}")
                        raise HTTPException(
                            status_code=400,
                            detail="创建用户失败，请稍后重试"
                        )
                
            except Exception as e:
                logger.error(f"处理openid登录异常: {str(e)}")
                # 继续尝试使用code登录
        
        # 没有openid或openid处理失败，使用code流程
        if request.code:
            logger.info(f"使用code登录流程")
            
            try:
                # 创建或更新用户
                user = await UserService.wechat_login(
                    db=db,
                    code=request.code,
                    nickname=getattr(request, 'nickname', None),
                    avatar_url=getattr(request, 'avatar_url', None),
                    phone=None,
                    openid=request.openid,
                    unionid=request.unionid
                )
                
                # 创建访问令牌
                access_token_expires = timedelta(days=settings.ACCESS_TOKEN_EXPIRE_DAYS)
                access_token = create_access_token(
                    data={"sub": str(user.id)}, 
                    expires_delta=access_token_expires
                )
                
                # 计算过期时间
                expires_at = datetime.utcnow() + access_token_expires
                
                # 构建简化的用户响应
                user_data = {
                    "id": user.id,
                    "nickname": getattr(user, 'nickname', '用户') or '用户',
                    "avatarUrl": getattr(user, 'avatar_url', None)
                }
                
                # 添加用户创建时间(如果存在)
                if hasattr(user, 'created_at') and user.created_at:
                    user_data["created_at"] = user.created_at.isoformat()
                    
                # 添加用户完成状态
                user_data["completed"] = getattr(user, 'completed', False)
                
                # 判断是否为新用户 - 简化判断逻辑
                is_new_user = False
                if hasattr(user, 'created_at') and user.created_at:
                    # 10秒内创建的视为新用户
                    is_new_user = (datetime.now() - user.created_at).total_seconds() < 10
                
                # 构建响应
                response_data = WechatLoginResponse(
                    success=True,
                    token=access_token,
                    expires_at=expires_at.isoformat(),  # 添加过期时间
                    user=user_data,
                    is_new_user=is_new_user
                )
                
                # 记录处理时间和响应
                total_time = (datetime.now() - start_time).total_seconds()
                SafeLogging.log_response(response_data.dict(), total_time)
                
                return response_data
                
            except Exception as e:
                error_msg = f"使用code登录失败: {str(e)}"
                logger.error(error_msg)
                logger.error(f"异常堆栈: {traceback.format_exc()}")
                raise HTTPException(
                    status_code=400,
                    detail=f"微信登录失败，请稍后重试: {str(e)}"
                )
                
        # 如果没有code和openid，返回错误
        error_msg = "缺少必要参数: code和openid"
        logger.error(error_msg)
        raise HTTPException(
            status_code=400,
            detail=error_msg
        )
    
    except HTTPException:
        # 直接重新抛出HTTP异常
        raise
    except Exception as e:
        # 处理其他异常
        error_msg = f"微信登录未处理异常: {str(e)}"
        logger.error(error_msg)
        logger.error(f"异常堆栈: {traceback.format_exc()}")
        raise HTTPException(
            status_code=500,
            detail=f"服务器内部错误，请稍后重试: {str(e)}"
        )

@router.post("/wechat/phone", response_model=dict)
async def bind_wechat_phone(
    request: WechatPhoneRequest,
    req: Request,
    db: Session = Depends(get_db)
):
    """微信小程序绑定手机号接口"""
    
    try:
        # 开始计时
        start_time = datetime.now()
        
        # 记录请求信息（安全处理 - 不记录敏感信息）
        safe_request = {
            "code": f"{request.code[:3]}***" if request.code else None,
            "encrypted_data": "***MASKED***",
            "iv": "***MASKED***"
        }
        logger.info(f"收到绑定手机号请求: {json.dumps(safe_request, ensure_ascii=False)}")
        
        # 获取session_key - 由服务器安全保存，不发送到客户端
        wx_info = await WechatService.code2session(request.code, request=req)
        session_key = wx_info["session_key"]
        openid = wx_info["openid"]
        
        # 解密手机号 - 在服务器端完成
        try:
            phone_info = WechatService.decrypt_phone_number(
                session_key=session_key,
                encrypted_data=request.encrypted_data,
                iv=request.iv
            )
            
            if not phone_info or "phoneNumber" not in phone_info:
                raise HTTPException(
                    status_code=400,
                    detail="手机号解密失败，请重试"
                )
                
            phone_number = phone_info["phoneNumber"]
            logger.info(f"手机号解密成功: {phone_number[:3]}***{phone_number[-2:]}")
            
        except Exception as e:
            logger.error(f"手机号解密失败: {str(e)}")
            raise HTTPException(
                status_code=400,
                detail="手机号解密失败，请重试"
            )
        
        # 更新用户手机号
        try:
            user = UserService.get_by_openid(db, openid)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="用户不存在，请先登录"
                )
            
            user.phone = phone_info["phoneNumber"]
            db.commit()
            
            # 记录处理时间
            total_time = (datetime.now() - start_time).total_seconds()
            logger.info(f"手机号绑定成功: 用户ID={user.id}, 耗时={total_time:.2f}秒")
            
        except HTTPException:
            # 直接重新抛出HTTP异常
            raise
        except Exception as e:
            logger.error(f"更新用户手机号失败: {str(e)}")
            db.rollback()  # 回滚事务
            raise HTTPException(
                status_code=500,
                detail="更新用户信息失败，请稍后再试"
            )
        
        # 构建安全的响应 - 只返回必要信息
        response_data = {
            "success": True, 
            "message": "手机号绑定成功", 
            "phone": phone_info["phoneNumber"]
        }
        
        # 记录响应数据（掩盖敏感信息）
        masked_data = {
            "success": True, 
            "message": "手机号绑定成功", 
            "phone": f"{phone_number[:3]}***{phone_number[-2:]}"
        }
        logger.info(f"手机号绑定成功，响应详情: {json.dumps(masked_data, ensure_ascii=False)}")
        
        return response_data
        
    except HTTPException:
        # 直接重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"绑定手机号未处理异常: {str(e)}")
        logger.error(f"异常堆栈: {traceback.format_exc()}")
        raise HTTPException(
            status_code=500,
            detail="服务器内部错误，请稍后再试"
        )

@router.post("/wechat/code2session", response_model=WechatCode2SessionResponse)
async def wechat_code2session(
    request: WechatCode2SessionRequest,
    req: Request,
):
    """
    微信小程序code2session接口
    
    接收小程序临时登录凭证code，返回用户的openid
    """
    try:
        # 开始计时
        start_time = datetime.now()
        
        # 记录请求信息（安全处理）
        safe_request = SafeLogging.mask_sensitive_data(request.dict())
        client_host = req.client.host if req.client else "unknown"
        request_id = id(req)
        logger.info(f"收到code2session请求: 客户端={client_host}, 请求ID={request_id}, 数据={json.dumps(safe_request, ensure_ascii=False)}")
        
        # 开发环境下的测试模式 - 使用特定的测试代码返回固定响应
        if settings.IS_DEV and request.code in ["test_code", "the_test_code"]:
            logger.info("使用测试模式返回code2session数据")
            test_response = WechatCode2SessionResponse(
                openid=f"test_openid_{str(uuid.uuid4())[:8]}",
                unionid=f"test_unionid_{str(uuid.uuid4())[:8]}"
            )
            return test_response
        
        # 调用微信服务获取会话信息
        try:
            wx_info = await WechatService.code2session(request.code, request=req)
            
            # 构建响应 - 不包含session_key
            response = WechatCode2SessionResponse(
                openid=wx_info["openid"],
                unionid=wx_info.get("unionid")
            )
            
            # 记录处理时间和响应
            total_time = (datetime.now() - start_time).total_seconds()
            safe_response = SafeLogging.mask_sensitive_data(response.dict())
            logger.info(f"code2session处理完成: 耗时={total_time:.2f}秒, 响应={json.dumps(safe_response, ensure_ascii=False)}")
            
            return response
        except WechatAPIError as we:
            # 捕获微信API错误并提供详细日志
            logger.error(f"微信API错误: {str(we)}")
            raise HTTPException(
                status_code=500,
                detail=f"微信服务暂时不可用: {str(we)}"
            )
        
    except HTTPException:
        # 直接重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"code2session处理失败: {str(e)}")
        logger.error(f"异常堆栈: {traceback.format_exc()}")
        raise HTTPException(
            status_code=500,
            detail=f"微信code2session处理失败: {str(e)}"
        )

@router.post("/refresh-token", response_model=TokenResponse)
async def refresh_token(
    current_token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    """
    刷新访问令牌接口
    
    允许客户端在访问令牌即将过期时获取新的令牌，
    无需用户重新登录。要求提供当前有效的令牌。
    """
    try:
        # 开始计时
        start_time = datetime.now()
        
        # 解析当前token
        try:
            payload = jwt.decode(current_token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        except JWTError as e:
            logger.warning(f"解析令牌失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail={"detail": "无效的访问令牌", "code": "invalid_token"},
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # 提取用户ID
        user_id: str = payload.get("sub")
        if user_id is None:
            logger.warning("令牌中缺少用户ID")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail={"detail": "无效的访问令牌", "code": "invalid_token"},
                headers={"WWW-Authenticate": "Bearer"},
            )
            
        # 验证用户是否存在
        user = db.query(User).filter(User.id == int(user_id)).first()
        if not user:
            logger.warning(f"找不到用户ID: {user_id}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail={"detail": "用户不存在", "code": "user_not_found"},
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # 检查用户状态
        if not user.is_active:
            logger.warning(f"用户已禁用: {user_id}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail={"detail": "用户已禁用", "code": "user_inactive"},
                headers={"WWW-Authenticate": "Bearer"},
            )
            
        # 创建新token
        access_token_expires = timedelta(days=settings.ACCESS_TOKEN_EXPIRE_DAYS)
        new_token = create_access_token(
            data={"sub": user_id}, 
            expires_delta=access_token_expires
        )
        
        # 计算过期时间
        expires_at = datetime.utcnow() + access_token_expires
        
        # 构建响应
        response = {
            "token": new_token,
            "expires_at": expires_at.isoformat()
        }
        
        # 记录处理时间
        total_time = (datetime.now() - start_time).total_seconds()
        logger.info(f"令牌刷新成功: 用户ID={user_id}, 耗时={total_time:.2f}秒")
        
        # 发送响应
        return response
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        # 记录错误并返回通用错误
        logger.error(f"刷新令牌时发生错误: {str(e)}")
        logger.debug(f"错误详情: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"detail": "服务器内部错误", "code": "server_error"}
        )

@router.post("/logout")
async def logout(
    current_user: User = Depends(deps.get_current_user),
    db: Session = Depends(get_db)
):
    """
    登出接口
    
    用户登出系统，使当前令牌失效。
    注意：由于JWT的无状态特性，实际上令牌在有效期内依然可用，
    建议客户端在登出时删除本地存储的令牌。
    """
    try:
        # 记录用户登出
        logger.info(f"用户登出: ID={current_user.id}")
        
        # 这里可以记录登出事件，或者将来实现令牌黑名单功能
        
        return {"success": True, "message": "登出成功"}
    except Exception as e:
        logger.error(f"用户登出失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登出处理失败"
        ) 