from typing import Any, Dict, Optional
from fastapi import APIRouter, Depends, HTTPException, Response, BackgroundTasks
from sqlalchemy.orm import Session
import logging
import os
import time
import re
import datetime
from functools import lru_cache
from fastapi.responses import FileResponse

from app import models, schemas, crud
from app.api import deps
from app.utils.qrcode import generate_qrcode

router = APIRouter()
logger = logging.getLogger("fitness-coach-api")

# 内存缓存
QR_CACHE = {}
MAX_CACHE_SIZE = 100
QRCODE_MAX_AGE_DAYS = 30  # 二维码有效期，默认30天

def sanitize_scene_param(scene: str) -> str:
    """清理和验证场景参数，防止注入攻击"""
    # 移除可能的恶意字符，仅允许字母、数字、=、&和部分标点符号
    sanitized = re.sub(r'[^\w=&\-_]', '', scene)
    return sanitized[:32]  # 限制长度

def get_cached_qrcode(user_id: int, page: str, scene: str) -> Optional[Dict]:
    """从缓存获取二维码信息"""
    cache_key = f"{user_id}_{page}_{scene}"
    
    # 检查缓存
    if cache_key in QR_CACHE:
        cached_data = QR_CACHE[cache_key]
        # 检查是否过期
        if time.time() - cached_data["timestamp"] < 3600:  # 1小时缓存
            logger.debug(f"从缓存获取二维码: {cache_key}")
            return {
                "url": cached_data["url"],
                "path": cached_data["path"]
            }
        else:
            # 移除过期缓存
            del QR_CACHE[cache_key]
            
    return None

def update_cache(user_id: int, page: str, scene: str, url: str, path: str) -> None:
    """更新缓存"""
    cache_key = f"{user_id}_{page}_{scene}"
    
    # 如果缓存过大，清理最早的项
    if len(QR_CACHE) >= MAX_CACHE_SIZE:
        # 找到最早添加的项并删除
        oldest_key = min(QR_CACHE.items(), key=lambda x: x[1]["timestamp"])[0]
        del QR_CACHE[oldest_key]
    
    # 添加到缓存
    QR_CACHE[cache_key] = {
        "url": url,
        "path": path,
        "timestamp": time.time()
    }
    logger.debug(f"更新二维码缓存: {cache_key}")

def is_qrcode_expired(file_path: str) -> bool:
    """检查二维码是否已过期（30天）"""
    if not os.path.exists(file_path):
        return True
        
    file_age = time.time() - os.path.getmtime(file_path)
    max_age = QRCODE_MAX_AGE_DAYS * 24 * 60 * 60  # 转换为秒
    return file_age > max_age

def cleanup_expired_qrcode(db: Session, qrcode_url: str) -> None:
    """清理过期的二维码文件和更新记录"""
    try:
        # 从URL中提取路径
        parts = qrcode_url.split('/')
        if len(parts) >= 2:
            secure_path = parts[-2]
            filename = parts[-1]
            file_path = f"/data/users/{secure_path}/qrcode/{filename}"
            
            # 检查文件是否存在且过期
            if os.path.exists(file_path) and is_qrcode_expired(file_path):
                # 删除文件
                os.remove(file_path)
                logger.info(f"删除过期二维码: {file_path}")
                
                # 更新数据库记录
                track = crud.share_track.get_by_qrcode_url(db, qrcode_url)
                if track:
                    track.is_active = False
                    db.commit()
                    logger.info(f"更新二维码记录状态: {qrcode_url}")
    except Exception as e:
        logger.error(f"清理二维码失败: {str(e)}")

class QRCodeService:
    """二维码服务类，集中处理二维码业务逻辑"""
    
    @staticmethod
    def get_existing_qrcode(db: Session, user_id: int, page: str, scene: str) -> Optional[Dict]:
        """获取已存在的二维码记录"""
        # 先检查缓存
        cached = get_cached_qrcode(user_id, page, scene)
        if cached:
            return cached
            
        # 查询数据库
        existing_record = crud.share_track.get_existing_qrcode(
            db=db,
            user_id=user_id,
            page=page,
            scene=scene
        )
        
        # 如果找到记录
        if existing_record and existing_record.qrcode_url:
            # 提取文件路径
            parts = existing_record.qrcode_url.split('/')
            if len(parts) >= 2:
                secure_path = parts[-2]
                filename = parts[-1]
                file_path = f"/data/users/{secure_path}/qrcode/{filename}"
                
                # 检查文件是否存在且未过期
                if os.path.exists(file_path) and not is_qrcode_expired(file_path):
                    result = {
                        "url": existing_record.qrcode_url,
                        "path": file_path
                    }
                    # 更新缓存
                    update_cache(user_id, page, scene, result["url"], result["path"])
                    return result
        
        return None
    
    @staticmethod
    def generate_new_qrcode(page: str, scene: str, width: int, user_id: int) -> Dict:
        """生成新的二维码"""
        return generate_qrcode(
            page=page,
            scene=scene,
            width=width,
            user_id=user_id
        )
    
    @staticmethod
    def create_share_record(db: Session, track_data: Dict) -> Optional[models.ShareTrack]:
        """创建分享记录"""
        try:
            track_obj = schemas.ShareTrackCreate(**track_data)
            return crud.share_track.create(db, obj_in=track_obj)
        except Exception as e:
            logger.error(f"创建分享记录失败: {str(e)}")
            return None


@router.post("/generate", response_model=schemas.QRCodeResponse)
def create_qrcode(
    qrcode_data: schemas.QRCodeCreate,
    background_tasks: BackgroundTasks,
    current_user: models.User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
) -> Any:
    """
    生成小程序码
    """
    # 记录处理开始时间
    start_time = time.time()
    
    try:
        # 参数验证
        if not qrcode_data.page:
            raise HTTPException(status_code=400, detail="缺少必要参数：page")
            
        # 构建和清理用户场景参数
        user_scene = sanitize_scene_param(f"uid={current_user.id}")
        if qrcode_data.scene:
            sanitized_scene = sanitize_scene_param(qrcode_data.scene)
            user_scene = f"{sanitized_scene}&uid={current_user.id}"
            
        logger.info(f"生成带用户ID的场景参数: {user_scene}")
        
        # 使用服务类获取已存在的二维码
        existing_result = QRCodeService.get_existing_qrcode(db, current_user.id, qrcode_data.page, user_scene)
        if existing_result:
            logger.info(f"找到已存在的QR码记录: {existing_result['url']}")
            
            # 记录处理时间
            elapsed_time = time.time() - start_time
            logger.info(f"二维码获取成功, 耗时: {elapsed_time:.4f}秒, 类型: 缓存/已存在")
            
            return existing_result
            
        # 生成新二维码
        qrcode_result = QRCodeService.generate_new_qrcode(
            qrcode_data.page,
            user_scene,
            qrcode_data.width,
            current_user.id
        )
        
        # 创建分享记录
        track_data = {
            "share_type": "qrcode",
            "page": qrcode_data.page,
            "scene": user_scene,  # 保存完整的场景参数，包括用户ID
            "shared_by": current_user.id,
            "qrcode_url": qrcode_result["url"],
            "created_at": datetime.datetime.now(),
            "is_active": True
        }
        
        # 创建分享记录（后台任务）
        background_tasks.add_task(
            QRCodeService.create_share_record,
            db=db,
            track_data=track_data
        )
        
        # 更新缓存
        update_cache(
            current_user.id, 
            qrcode_data.page, 
            user_scene, 
            qrcode_result["url"], 
            qrcode_result["path"]
        )
        
        # 记录处理时间
        elapsed_time = time.time() - start_time
        logger.info(f"二维码生成成功, 耗时: {elapsed_time:.4f}秒, 类型: 新生成")
        
        return {
            "url": qrcode_result["url"],
            "path": qrcode_result["path"]
        }
    except HTTPException:
        # 直接重新抛出HTTP异常
        raise
    except Exception as e:
        # 记录详细错误信息
        logger.error(f"生成小程序码失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500, 
            detail={"message": "生成小程序码失败", "error": str(e)}
        )


@router.get("/image/{secure_path}/{filename}", response_class=FileResponse)
async def get_qrcode_image(
    secure_path: str,
    filename: str,
    background_tasks: BackgroundTasks,
    db: Session = Depends(deps.get_db)
):
    """
    获取小程序码图片 - 不需要身份验证
    
    通过安全路径和文件名获取图片文件
    """
    # 安全性检查
    if '..' in secure_path or '..' in filename:
        logger.warning(f"检测到路径遍历尝试: {secure_path}/{filename}")
        raise HTTPException(status_code=403, detail="非法访问")
        
    # 验证文件名格式是否符合规定模式
    if not re.match(r'^[a-zA-Z0-9_\-]+\.(png|jpg|jpeg)$', filename):
        logger.warning(f"非法文件名格式: {filename}")
        raise HTTPException(status_code=400, detail="非法文件名")
    
    file_path = f"/data/users/{secure_path}/qrcode/{filename}"
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        logger.error(f"小程序码不存在: {file_path}")
        raise HTTPException(status_code=404, detail="小程序码不存在")
    
    # 检查是否过期 - 不阻止访问，但在后台标记过期
    if is_qrcode_expired(file_path):
        logger.info(f"访问过期的二维码: {file_path}")
        qrcode_url = f"/api/v1/qrcode/image/{secure_path}/{filename}"
        # 添加后台任务处理过期的二维码
        background_tasks.add_task(cleanup_expired_qrcode, db, qrcode_url)
    
    # 返回文件
    return FileResponse(file_path, media_type="image/png")


@router.delete("/cleanup", status_code=204)
def cleanup_expired_qrcodes(
    background_tasks: BackgroundTasks,
    current_user: models.User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
):
    """
    清理过期的二维码文件和记录
    只有管理员可以调用此端点
    """
    # 检查权限
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="需要管理员权限")
    
    try:
        # 获取所有二维码记录
        qrcodes = crud.share_track.get_all_qrcodes(db)
        
        # 添加后台任务处理每个二维码
        for qrcode in qrcodes:
            if qrcode.qrcode_url:
                background_tasks.add_task(cleanup_expired_qrcode, db, qrcode.qrcode_url)
        
        logger.info(f"启动二维码清理任务, 记录总数: {len(qrcodes)}")
        return Response(status_code=204)
    except Exception as e:
        logger.error(f"清理二维码失败: {str(e)}")
        raise HTTPException(status_code=500, detail="清理二维码失败") 