from typing import Any, List, Optional, Dict
from datetime import date
import re
import logging
import time
import json
import os.path

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Response, Form, Request, Query
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session

from app import crud, models, schemas
from app.schemas.food_recognition import (
    FoodItemCreate, FoodRecognition, FoodRecognitionCreate, FoodRecognitionUpdate,
    FoodRecognitionResponse, FoodRecognitionConfirmResponse, FoodRecognitionConfirmation,
    FoodRecognitionStats, FoodRecognitionRejection, Base64ImageRequest
)
from app.schemas.meal import MealType, MealRecordCreate, HealthRecommendationCreate
from app.api import deps
from app.services.food_recognition_service import FoodRecognitionService
from app.services.storage import StorageService

router = APIRouter()

# 创建日志记录器
logger = logging.getLogger(__name__)


# 添加一个辅助函数，用于记录请求数据并检查错误
async def get_request_body(request: Request) -> dict:
    """
    获取并记录请求体内容
    """
    try:
        body = await request.json()
        logger.info(f"请求体内容: {json.dumps(body, ensure_ascii=False)}")
        return body
    except Exception as e:
        logger.error(f"解析请求体失败: {str(e)}")
        return {}


# 添加一个临时接口来诊断请求问题
@router.post("/{recognition_id}/debug-confirm")
async def debug_confirm_recognition(
    request: Request,
    recognition_id: int,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    调试确认接口，记录请求数据但不实际处理
    """
    try:
        # 记录请求内容
        body = await get_request_body(request)
        
        # 检查关键字段是否存在
        food_items = body.get('food_items', [])
        logger.info(f"请求包含 {len(food_items)} 个食物项")
        
        # 尝试验证FoodRecognitionConfirmation模型
        try:
            confirmation = FoodRecognitionConfirmation.parse_obj(body)
            logger.info("请求数据符合FoodRecognitionConfirmation模型")
            
            # 检查每个食物项
            for idx, item in enumerate(confirmation.food_items):
                logger.info(f"食物项 {idx+1}: name={item.name}, weight={item.weight}")
                
                # 记录所有字段
                item_dict = item.dict()
                logger.info(f"食物项 {idx+1} 所有字段: {json.dumps(item_dict, ensure_ascii=False)}")
                
                # 检查必要字段
                required_fields = ['name', 'weight', 'calory', 'protein', 'fat', 'carbohydrate']
                missing_fields = [field for field in required_fields if field not in item_dict or item_dict[field] is None]
                if missing_fields:
                    logger.error(f"食物项 {idx+1} 缺少必要字段: {', '.join(missing_fields)}")
                
        except Exception as e:
            logger.error(f"验证FoodRecognitionConfirmation模型失败: {str(e)}")
            
        return {"success": True, "message": "调试请求已记录", "request_data": body}
    except Exception as e:
        logger.error(f"调试确认接口出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"调试确认接口出错: {str(e)}")


def ensure_food_item_fields(item, source="API"):
    """确保食物项包含所有必要字段"""
    if not hasattr(item, 'dict'):
        logger.error(f"{source}: 食物项不是Pydantic模型: {type(item)}")
        return item
        
    try:    
        item_dict = item.dict()
        modified = False
        
        # 确保code字段存在
        if not item_dict.get('code'):
            item_dict['code'] = f"auto_{int(time.time())}_{item_dict.get('name', 'unknown').replace(' ', '_')}"
            modified = True
            logger.info(f"{source}: 为食物项 '{item_dict.get('name')}' 添加code字段")
        
        # 确保matching_info字段存在
        if not item_dict.get('matching_info'):
            item_dict['matching_info'] = {
                "recognition_name": item_dict.get('name', 'unknown'),
                "similarity": 0.0
            }
            modified = True
            logger.info(f"{source}: 为食物项 '{item_dict.get('name')}' 添加matching_info字段")
        
        # 确保food_details字段存在
        if not item_dict.get('food_details'):
            # 如果有food_id，添加基本food_details信息
            if item_dict.get('food_id'):
                item_dict['food_details'] = {
                    "id": item_dict.get('food_id'),
                    "name": item_dict.get('name'),
                    "category": item_dict.get('category', ''),
                }
            else:
                item_dict['food_details'] = None
            modified = True
            logger.info(f"{source}: 为食物项 '{item_dict.get('name')}' 添加food_details字段")
        
        # 确保nutritional_profile字段存在
        if not item_dict.get('nutritional_profile'):
            item_dict['nutritional_profile'] = {
                "health_light": item_dict.get('health_light', 5),
                "lights": item_dict.get('lights', []),
                "warnings": item_dict.get('warnings', []),
                "warning_scenes": item_dict.get('warning_scenes', []),
                "calory": item_dict.get('calory', 0),
                "protein_fraction": item_dict.get('protein_fraction', 0.0),
                "fat_fraction": item_dict.get('fat_fraction', 0.0),
                "carb_fraction": item_dict.get('carb_fraction', 0.0),
                "food_rank": "C"
            }
            modified = True
            logger.info(f"{source}: 为食物项 '{item_dict.get('name')}' 添加nutritional_profile字段")
        
        # 确保warning_scenes是字符串列表
        warning_scenes = item_dict.get('warning_scenes', [])
        if warning_scenes and isinstance(warning_scenes, list):
            processed_scenes = []
            scenes_modified = False
            for scene in warning_scenes:
                if isinstance(scene, dict) and 'name' in scene:
                    processed_scenes.append(scene['name'])
                    scenes_modified = True
                elif isinstance(scene, str):
                    processed_scenes.append(scene)
                else:
                    # 跳过无效项
                    scenes_modified = True
                    continue
            
            if scenes_modified:
                item_dict['warning_scenes'] = processed_scenes
                modified = True
                logger.info(f"{source}: 为食物项 '{item_dict.get('name')}' 处理warning_scenes格式")
        
        # 如果有修改，创建新的对象
        return FoodItemCreate(**item_dict) if modified else item
    except Exception as e:
        logger.error(f"{source}: 处理食物项字段时出错: {str(e)}")
        return item


# 添加文件名提取函数
def extract_filename(path: str) -> str:
    """
    从完整路径中提取文件名
    '/path/to/file.jpg' -> 'file.jpg'
    
    注意：
    - 对于存储在/data/users下的餐食识别图片，使用简化格式
    - 对于存储在/data/food下的食物项图片，保留完整路径
    """
    if not path:
        return path
    
    # 检查是否是食物项图片路径(存储在/data/food中)
    if '/data/food/' in path or path.startswith('/food/'):
        # 食物项图片保留原路径
        return path
    
    # 提取最后一个斜杠后的部分
    return os.path.basename(path)


@router.post("/analyze", response_model=FoodRecognitionResponse)
async def analyze_food_image(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    image: UploadFile = File(...),
    meal_type: str = Form(...),
    meal_date: Optional[str] = Form(None),
    optimize: bool = Form(True)
) -> Any:
    """
    分析食物图片，识别食物项。
    
    - **image**: 食物图片文件（必须）
    - **meal_type**: 餐食类型，必须是：breakfast、lunch、dinner或snack之一
    - **meal_date**: 可选，餐食日期，格式为YYYY-MM-DD
    - **optimize**: 是否优化图片尺寸和质量，默认为True
    
    注意：此接口必须使用multipart/form-data格式提交请求。
    """
    logger.info(f"收到食物识别请求：meal_type={meal_type}, meal_date={meal_date}, optimize={optimize}")
    
    try:
        # 验证meal_type是否为有效枚举值
        try:
            meal_type_enum = MealType(meal_type.lower())
            logger.info(f"餐食类型验证通过: {meal_type_enum}")
        except ValueError as e:
            valid_types = ", ".join([t.value for t in MealType])
            logger.error(f"餐食类型错误: {meal_type} 不是有效值({valid_types})")
            raise HTTPException(
                status_code=422,
                detail=f"无效的餐食类型。必须是以下之一: {valid_types}。当前值: {meal_type}"
            )
        
        # 处理日期参数
        parsed_date = None
        if meal_date:
            try:
                # 尝试直接解析标准格式 YYYY-MM-DD
                parsed_date = date.fromisoformat(meal_date)
                logger.info(f"餐食日期验证通过: {parsed_date}")
            except ValueError as e:
                # 如果标准格式解析失败，尝试处理其他格式
                logger.warning(f"标准日期格式解析失败: {meal_date}, {str(e)}")
                try:
                    # 尝试处理各种日期格式 (年/月/日, 年-月-日, 年.月.日)
                    parts = re.split(r'[-/.]', meal_date)
                    if len(parts) == 3:
                        year, month, day = parts
                        # 确保年、月、日都是数字
                        if year.isdigit() and month.isdigit() and day.isdigit():
                            # 处理两位数年份
                            if len(year) == 2:
                                year = f"20{year}"
                            # 确保月和日是合法的
                            month_int = int(month)
                            day_int = int(day)
                            if 1 <= month_int <= 12 and 1 <= day_int <= 31:
                                # 重新组合成YYYY-MM-DD格式
                                formatted_date = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                                parsed_date = date.fromisoformat(formatted_date)
                                logger.info(f"餐食日期格式转换成功: {meal_date} -> {parsed_date}")
                            else:
                                raise ValueError(f"月份或日期超出范围: 月={month_int}, 日={day_int}")
                        else:
                            raise ValueError("日期部分含有非数字字符")
                    else:
                        raise ValueError("日期格式无法识别")
                except Exception as inner_e:
                    logger.error(f"所有日期格式解析尝试均失败: {meal_date}, {str(inner_e)}")
                    raise HTTPException(
                        status_code=422,
                        detail=f"无效的日期格式。请使用YYYY-MM-DD格式。当前值: {meal_date}"
                    )
        else:
            parsed_date = date.today()
            logger.info(f"未提供日期，使用今天: {parsed_date}")
        
        # 验证图片文件
        if not image.content_type or not image.content_type.startswith("image/"):
            logger.error(f"上传的文件不是图像: {image.content_type}")
            raise HTTPException(
                status_code=422,
                detail="上传的文件必须是图像(image/jpeg, image/png等)"
            )
            
        # 读取图片内容
        try:
            contents = await image.read()
            if not contents or len(contents) == 0:
                raise HTTPException(
                    status_code=422,
                    detail="上传的图像文件为空"
                )
                
            logger.info(f"成功读取图像，大小: {len(contents)}字节")
        except Exception as e:
            logger.error(f"读取图像失败: {str(e)}")
            raise HTTPException(
                status_code=422,
                detail=f"读取图像失败: {str(e)}"
            )
        
        # 调用食物识别服务
        recognition_result = await FoodRecognitionService.analyze_image(
            db=db,
            user_id=current_user.id,
            image_data=contents,
            meal_type=meal_type_enum,
            meal_date=parsed_date,
            optimize=optimize
        )
        
        # 修改image_url和thumb_image_url，只保留文件名
        if "image_url" in recognition_result and recognition_result["image_url"]:
            recognition_result["image_url"] = extract_filename(recognition_result["image_url"])
            
        if "thumb_image_url" in recognition_result and recognition_result["thumb_image_url"]:
            recognition_result["thumb_image_url"] = extract_filename(recognition_result["thumb_image_url"])
        
        # 检查结果是否为空，如果是，提供一个默认响应
        if not recognition_result.get("food_items") or len(recognition_result.get("food_items", [])) == 0:
            logger.warning("食物识别服务返回空结果，提供默认响应")
            # 创建一个默认的食物项
            default_item = FoodItemCreate(
                name="未识别的食物",
                code=f"unknown_{int(time.time())}",
                weight=100,
                unit_name="份",
                quantity=1,
                calory=0,
                protein=0,
                fat=0,
                carbohydrate=0,
                matching_info={
                    "recognition_name": "未能识别",
                    "similarity": 0.0
                },
                nutritional_profile={
                    "health_light": 5,
                    "lights": [],
                    "warnings": ["识别失败，请重新拍照或手动输入"],
                    "warning_scenes": [],
                    "calory": 0,
                    "protein_fraction": 0.0,
                    "fat_fraction": 0.0,
                    "carb_fraction": 0.0,
                    "food_rank": "C"
                },
                food_details=None
            )
            
            # 更新返回结果
            recognition_result["food_items"] = [default_item]
            recognition_result["health_recommendation"] = "抱歉，未能成功识别食物。请尝试重新拍摄更清晰的照片，或手动输入食物信息。"
            recognition_result["error_info"] = "AI服务返回的JSON数据解析失败，可能是因为格式错误。"
        
        # 记录返回结果用于调试
        try:
            food_items = recognition_result.get("food_items", [])
            food_items_summary = []
            for idx, item in enumerate(food_items):
                food_items_summary.append({
                    "idx": idx,
                    "name": item.name if hasattr(item, 'name') else "unknown",
                    "has_code": bool(item.code if hasattr(item, 'code') else False),
                    "has_matching_info": bool(item.matching_info if hasattr(item, 'matching_info') else False),
                    "has_nutritional_profile": bool(item.nutritional_profile if hasattr(item, 'nutritional_profile') else False),
                    "food_id": item.food_id if hasattr(item, 'food_id') else None
                })
            logger.info(f"食物识别返回 {len(food_items)} 个食物项，详情: {json.dumps(food_items_summary, ensure_ascii=False)}")
        except Exception as e:
            logger.error(f"记录食物识别结果详情时出错: {str(e)}")
        
        return recognition_result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"食物识别失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"食物识别失败: {str(e)}"
        )


@router.post("/analyze-base64", response_model=FoodRecognitionResponse)
async def analyze_base64_food_image(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    image_data: Base64ImageRequest,
    meal_type: MealType,
    meal_date: date = None,
    optimize: bool = True
) -> Any:
    """
    使用base64编码的图片分析食物，识别食物项。
    
    - **image_data**: 包含base64编码图像的请求体
    - **meal_type**: 餐食类型，必须是：breakfast、lunch、dinner或snack之一
    - **meal_date**: 可选，餐食日期，格式为YYYY-MM-DD
    - **optimize**: 是否优化图片尺寸和质量，默认为True
    
    注意：此接口使用application/json格式，图像数据通过base64编码在请求体中传递。
    """
    logger.info(f"收到Base64食物识别请求：meal_type={meal_type}, meal_date={meal_date}, optimize={optimize}")
    
    if not meal_date:
        meal_date = date.today()
        logger.info(f"未提供日期，使用今天: {meal_date}")
    
    try:
        # 验证base64图像数据
        if not image_data.base64_image:
            logger.error("缺少base64图像数据")
            raise HTTPException(
                status_code=422,
                detail="请求必须包含base64_image字段"
            )
            
        if not image_data.base64_image.strip().startswith(("data:image/", "iVBORw0K", "/9j/")):
            logger.error("提供的base64数据不是有效的图像格式")
            raise HTTPException(
                status_code=422,
                detail="提供的base64数据不是有效的图像格式"
            )
        
        # 调用食物识别服务处理base64图像
        recognition_result = await FoodRecognitionService.analyze_base64_image(
            db=db,
            user_id=current_user.id,
            base64_image=image_data.base64_image,
            meal_type=meal_type,
            meal_date=meal_date,
            optimize=image_data.optimize
        )
        
        # 修改image_url和thumb_image_url，只保留文件名
        if "image_url" in recognition_result and recognition_result["image_url"]:
            recognition_result["image_url"] = extract_filename(recognition_result["image_url"])
            
        if "thumb_image_url" in recognition_result and recognition_result["thumb_image_url"]:
            recognition_result["thumb_image_url"] = extract_filename(recognition_result["thumb_image_url"])
        
        # 检查结果是否为空，如果是，提供一个默认响应
        if not recognition_result.get("food_items") or len(recognition_result.get("food_items", [])) == 0:
            logger.warning("Base64食物识别服务返回空结果，提供默认响应")
            # 创建一个默认的食物项
            default_item = FoodItemCreate(
                name="未识别的食物",
                code=f"unknown_{int(time.time())}",
                weight=100,
                unit_name="份",
                quantity=1,
                calory=0,
                protein=0,
                fat=0,
                carbohydrate=0,
                matching_info={
                    "recognition_name": "未能识别",
                    "similarity": 0.0
                },
                nutritional_profile={
                    "health_light": 5,
                    "lights": [],
                    "warnings": ["识别失败，请重新拍照或手动输入"],
                    "warning_scenes": [],
                    "calory": 0,
                    "protein_fraction": 0.0,
                    "fat_fraction": 0.0,
                    "carb_fraction": 0.0,
                    "food_rank": "C"
                },
                food_details=None
            )
            
            # 更新返回结果
            recognition_result["food_items"] = [default_item]
            recognition_result["health_recommendation"] = "抱歉，未能成功识别食物。请尝试重新拍摄更清晰的照片，或手动输入食物信息。"
            recognition_result["error_info"] = "AI服务返回的JSON数据解析失败，可能是因为格式错误。"
        
        # 记录返回结果用于调试
        try:
            food_items = recognition_result.get("food_items", [])
            food_items_summary = []
            for idx, item in enumerate(food_items):
                food_items_summary.append({
                    "idx": idx,
                    "name": item.name if hasattr(item, 'name') else "unknown",
                    "has_code": bool(item.code if hasattr(item, 'code') else False),
                    "has_matching_info": bool(item.matching_info if hasattr(item, 'matching_info') else False),
                    "has_nutritional_profile": bool(item.nutritional_profile if hasattr(item, 'nutritional_profile') else False),
                    "food_id": item.food_id if hasattr(item, 'food_id') else None
                })
            logger.info(f"Base64食物识别返回 {len(food_items)} 个食物项，详情: {json.dumps(food_items_summary, ensure_ascii=False)}")
        except Exception as e:
            logger.error(f"记录Base64食物识别结果详情时出错: {str(e)}")
        
        return recognition_result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Base64食物识别失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Base64食物识别失败: {str(e)}"
        )


@router.post("/{recognition_id}/confirm", response_model=FoodRecognitionConfirmResponse)
async def confirm_recognition(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    recognition_id: int,
    confirmation: FoodRecognitionConfirmation,
) -> Any:
    """
    确认食物识别结果并创建餐食记录。
    
    接收确认数据，包含食物项列表、营养总量、健康建议等。
    创建正式餐食记录，包含食物项和健康建议。
    返回完整的餐食记录。
    """
    try:
        logger.info(f"收到确认请求，recognition_id={recognition_id}")
        logger.info(f"确认数据: {confirmation}")
        
        # 检查识别记录是否存在
        recognition = crud.food_recognition.get(db=db, id=recognition_id)
        if not recognition:
            logger.error(f"识别记录不存在: id={recognition_id}")
            raise HTTPException(status_code=404, detail="识别记录不存在")
        
        # 检查权限
        if str(recognition.user_id) != str(current_user.id) and not current_user.is_superuser:
            logger.error(f"权限验证失败: recognition.user_id={recognition.user_id}, current_user.id={current_user.id}, is_superuser={current_user.is_superuser}")
            raise HTTPException(status_code=403, detail="没有权限确认此识别结果")
        
        # 检查状态
        if recognition.status == "confirmed":
            logger.error(f"识别记录已被确认: id={recognition_id}")
            raise HTTPException(status_code=400, detail="此识别记录已经被确认")
        
        # 验证食物项
        if not confirmation.food_items or len(confirmation.food_items) == 0:
            logger.error("确认数据必须包含至少一个食物项")
            raise HTTPException(status_code=422, detail="确认数据必须包含至少一个食物项")
        
        # 检查每个食物项必填字段
        for idx, item in enumerate(confirmation.food_items):
            if not item.name:
                logger.error(f"第{idx+1}个食物项缺少名称")
                raise HTTPException(status_code=422, detail=f"第{idx+1}个食物项缺少名称")
            
            if not hasattr(item, 'weight') or not item.weight:
                logger.error(f"第{idx+1}个食物项缺少重量")
                raise HTTPException(status_code=422, detail=f"第{idx+1}个食物项缺少重量")
        
        # 创建餐食记录
        meal_data = MealRecordCreate(
            date=recognition.meal_date,
            meal_type=recognition.meal_type,
            image_url=recognition.image_url,
            thumb_image_url=recognition.thumb_image_url,
            is_ai_recognized=True
        )
        
        # 创建餐食
        meal = crud.meal.create_with_user_id(
            db=db, 
            obj_in=meal_data,
            user_id=current_user.id
        )
        logger.info(f"创建餐食记录: id={meal.id}")
        
        # 添加食物项
        for food_item_data in confirmation.food_items:
            try:
                # 获取食物项字典
                food_item_dict = food_item_data.dict()
                
                # 检查image_url是否为空，如果为空则尝试从food_details中获取thumb_image_url
                if not food_item_dict.get("image_url") and food_item_dict.get("food_details") and food_item_dict["food_details"].get("thumb_image_url"):
                    food_item_dict["image_url"] = food_item_dict["food_details"]["thumb_image_url"]
                    logger.info(f"食物项 {food_item_dict.get('name')} 使用food_details中的thumb_image_url作为image_url")
                
                # 过滤掉数据库模型不支持的字段
                # 只保留FoodItem数据库模型中定义的字段
                valid_fields = [
                    "name", "food_id", "quantity", "unit_name", "weight", 
                    "category", "cuisine_type", "cuisine_type_detail", "image_url",
                    "health_light", "lights", "warnings", "warning_scenes",
                    "calory", "protein", "fat", "carbohydrate",
                    "protein_fraction", "fat_fraction", "carb_fraction",
                    "is_custom", "is_takeout"
                ]
                
                filtered_dict = {k: v for k, v in food_item_dict.items() if k in valid_fields}
                # 添加meal_record_id
                filtered_dict["meal_record_id"] = meal.id
                
                # 创建食物项
                food_item = crud.food_item.create(
                    db=db, 
                    obj_in=filtered_dict
                )
                logger.info(f"添加食物项: id={food_item.id}, name={food_item.name}")
            except Exception as e:
                logger.error(f"添加食物项失败: {str(e)}")
                raise HTTPException(
                    status_code=422,
                    detail=f"添加食物项失败: {str(e)}"
                )
        
        # 添加健康建议（如果有）
        if confirmation.health_recommendation:
            health_rec = crud.health_recommendation.create_with_meal_id(
                db=db,
                obj_in=HealthRecommendationCreate(recommendation_text=confirmation.health_recommendation),
                meal_id=meal.id
            )
            logger.info(f"添加健康建议: id={health_rec.id}")
        
        # 更新餐食营养总和
        from app.services.meal_service import MealService
        MealService.update_meal_nutrition_totals(db=db, meal_id=meal.id)
        logger.info(f"更新餐食营养总和完成")
        
        # 更新识别记录状态
        crud.food_recognition.update(
            db=db, 
            db_obj=recognition, 
            obj_in={"status": "confirmed", "meal_record_id": meal.id}
        )
        logger.info(f"更新识别记录状态为confirmed")
        
        # 获取完整的餐食记录并返回
        meal_record = crud.meal.get(db=db, id=meal.id)
        logger.info(f"完成确认流程，返回餐食记录 id={meal.id}")

        # 将meal_record转换为字典
        from sqlalchemy.orm import class_mapper
        def object_as_dict(obj):
            """将SQLAlchemy ORM对象转换为字典"""
            if obj is None:
                return None
            columns = [column.key for column in class_mapper(obj.__class__).columns]
            result = {column: getattr(obj, column) for column in columns}
            # 处理food_items属性，确保它也被转换为字典列表
            if hasattr(obj, 'food_items') and obj.food_items:
                result['food_items'] = [object_as_dict(item) for item in obj.food_items]
            # 处理health_recommendations属性
            if hasattr(obj, 'health_recommendations') and obj.health_recommendations:
                result['health_recommendations'] = [object_as_dict(rec) for rec in obj.health_recommendations]
            return result

        meal_record_dict = object_as_dict(meal_record)
        logger.info(f"转换餐食记录为字典格式")

        return {
            "success": True,
            "message": "识别结果已确认并创建餐食记录",
            "meal_record": meal_record_dict,
            "recognition_id": recognition_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"确认识别结果失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"确认识别结果失败: {str(e)}"
        )


@router.get("/pending", response_model=List[FoodRecognitionResponse])
def get_pending_recognitions(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取用户待确认的食物识别记录。
    """
    # 获取所有待确认的识别记录
    pending_recognitions = crud.food_recognition.get_pending(
        db=db,
        user_id=current_user.id
    )
    
    # 构建响应
    result = []
    for recognition in pending_recognitions:
        if recognition.recognition_result:
            try:
                # 创建食物项并确保字段完整
                food_items = []
                for item_data in recognition.recognition_result.get("food_items", []):
                    try:
                        food_item = FoodItemCreate(**item_data)
                        # 使用我们的辅助函数确保所有字段都存在
                        food_item = ensure_food_item_fields(food_item, source="get_pending_recognitions")
                        food_items.append(food_item)
                    except Exception as e:
                        logger.error(f"处理食物项时出错: {str(e)}")
                
                # 记录每个识别的匹配食物数据用于调试
                logger.info(f"识别ID {recognition.id} 包含 {len(food_items)} 个食物项")
                    
                # 构建详细响应
                item_response = {
                    "success": True,
                    "recognition_id": recognition.id,
                    "food_items": food_items,
                    "meal_name": recognition.recognition_result.get("meal_name", ""),
                    "health_recommendation": recognition.recognition_result.get("health_recommendation", ""),
                    "image_url": recognition.image_url,
                    "thumb_image_url": getattr(recognition, 'thumb_image_url', None),
                    "nutrition_totals": recognition.nutrition_totals or {},
                    "matched_foods": recognition.matched_foods or [],
                    "status": recognition.status,
                    "created_at": recognition.created_at,
                    "meal_date": recognition.meal_date,
                    "meal_type": recognition.meal_type
                }
                
                result.append(item_response)
            except Exception as e:
                logger.error(f"处理识别记录 {recognition.id} 时出错: {str(e)}")
    
    return result


@router.get("/image/{secure_path}/{date}/{filename}", response_class=FileResponse)
async def get_food_image(
    secure_path: str,
    date: str,
    filename: str
):
    """
    获取食物识别图片 - 不需要身份验证 (回退方案)
    
    通过安全路径、日期和文件名获取图片文件
    推荐使用静态文件路径访问: /food/images/{secure_path}/food_recognition/{date}/{filename}
    """
    # 记录使用了API端点访问，而不是静态文件挂载
    logger.info(f"通过API端点访问图片，建议使用静态文件路径: /food/images/{secure_path}/food_recognition/{date}/{filename}")
    
    # 安全性检查
    if '..' in secure_path or '..' in date or '..' in filename:
        raise HTTPException(status_code=403, detail="非法访问")
        
    # 验证安全路径格式
    if not re.match(r'^user_[a-zA-Z0-9]{24}$', secure_path) and not secure_path.startswith("user_fallback_"):
        raise HTTPException(status_code=400, detail="非法路径格式")
    
    # 验证文件名格式 - 增加WebP支持
    if not re.match(r'^[a-zA-Z0-9_\-]+\.(png|jpg|jpeg|webp)$', filename):
        raise HTTPException(status_code=400, detail="非法文件名")
    
    # 验证日期格式
    if not re.match(r'^\d{4}-\d{2}-\d{2}$', date):
        raise HTTPException(status_code=400, detail="非法日期格式")
    
    file_path = f"/data/users/{secure_path}/food_recognition/{date}/{filename}"
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="图片不存在")
    
    # 识别媒体类型
    content_type = "image/jpeg"  # 默认
    if filename.endswith(".png"):
        content_type = "image/png"
    elif filename.endswith(".webp"):
        content_type = "image/webp"
    
    # 返回文件
    return FileResponse(file_path, media_type=content_type)


@router.post("/analyze-smart", response_model=FoodRecognitionResponse)
async def analyze_food_smart(
    request: Request,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    image: Optional[UploadFile] = File(None),
    meal_type: Optional[str] = Form(None),
    meal_date: Optional[str] = Form(None),
    optimize: bool = Form(True)
) -> Any:
    """
    智能食物识别接口，支持多种图像输入方式。
    
    支持以下两种方式之一提供图像与数据：
    1. 使用multipart/form-data上传图像文件与参数
    2. 使用application/json提交请求体，包含base64_image与参数
    
    系统会自动检测使用的方法并相应处理。
    
    参数说明：
    - **image**: 当使用multipart/form-data时，上传的图像文件
    - **meal_type**: 餐食类型，必须是：breakfast、lunch、dinner或snack之一
    - **meal_date**: 可选，餐食日期，格式为YYYY-MM-DD
    - **optimize**: 是否优化图片尺寸和质量，默认为True
    """
    logger.info(f"收到智能食物识别请求")
    
    try:
        # 处理日期参数
        parsed_date = None
        if meal_date:
            try:
                # 尝试直接解析标准格式 YYYY-MM-DD
                parsed_date = date.fromisoformat(meal_date)
                logger.info(f"餐食日期验证通过: {parsed_date}")
            except ValueError as e:
                # 如果标准格式解析失败，尝试处理其他格式
                logger.warning(f"标准日期格式解析失败: {meal_date}, {str(e)}")
                try:
                    # 尝试处理各种日期格式 (年/月/日, 年-月-日, 年.月.日)
                    parts = re.split(r'[-/.]', meal_date)
                    if len(parts) == 3:
                        year, month, day = parts
                        # 确保年、月、日都是数字
                        if year.isdigit() and month.isdigit() and day.isdigit():
                            # 处理两位数年份
                            if len(year) == 2:
                                year = f"20{year}"
                            # 确保月和日是合法的
                            month_int = int(month)
                            day_int = int(day)
                            if 1 <= month_int <= 12 and 1 <= day_int <= 31:
                                # 重新组合成YYYY-MM-DD格式
                                formatted_date = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                                parsed_date = date.fromisoformat(formatted_date)
                                logger.info(f"餐食日期格式转换成功: {meal_date} -> {parsed_date}")
                            else:
                                raise ValueError(f"月份或日期超出范围: 月={month_int}, 日={day_int}")
                        else:
                            raise ValueError("日期部分含有非数字字符")
                    else:
                        raise ValueError("日期格式无法识别")
                except Exception as inner_e:
                    logger.error(f"所有日期格式解析尝试均失败: {meal_date}, {str(inner_e)}")
                    raise HTTPException(
                        status_code=422,
                        detail=f"无效的日期格式。请使用YYYY-MM-DD格式。当前值: {meal_date}"
                    )
        else:
            parsed_date = date.today()
            logger.info(f"未提供日期，使用今天: {parsed_date}")
            
        # 如果没有提供meal_type参数，检查是否在请求体中
        if not meal_type:
            try:
                # 尝试从JSON正文获取
                if request.headers.get("content-type", "").startswith("application/json"):
                    body_data = await request.json()
                    meal_type = body_data.get("meal_type")
                    # 如果JSON中也没有提供，则报错
                    if not meal_type:
                        raise HTTPException(
                            status_code=422,
                            detail="缺少必需参数：meal_type"
                        )
            except Exception as e:
                logger.error(f"无法解析请求体: {str(e)}")
                raise HTTPException(
                    status_code=422,
                    detail="缺少必需参数：meal_type"
                )
                
        # 验证meal_type是否为有效枚举值
        try:
            meal_type_enum = MealType(meal_type.lower())
            logger.info(f"餐食类型验证通过: {meal_type_enum}")
        except ValueError as e:
            valid_types = ", ".join([t.value for t in MealType])
            logger.error(f"餐食类型错误: {meal_type} 不是有效值({valid_types})")
            raise HTTPException(
                status_code=422,
                detail=f"无效的餐食类型。必须是以下之一: {valid_types}。当前值: {meal_type}"
            )
                
        # 检查是否是文件上传
        if image:
            logger.info(f"检测到文件上传方式，处理上传的图像文件")
            try:
                # 验证图片文件
                if not image.content_type or not image.content_type.startswith("image/"):
                    logger.error(f"上传的文件不是图像: {image.content_type}")
                    raise HTTPException(
                        status_code=422,
                        detail="上传的文件必须是图像(image/jpeg, image/png等)"
                    )
                
                # 读取图片内容
                contents = await image.read()
                if not contents or len(contents) == 0:
                    raise HTTPException(
                        status_code=422,
                        detail="上传的图像文件为空"
                    )
                
                logger.info(f"成功读取图像，大小: {len(contents)}字节")
                
                # 使用文件上传处理逻辑
                result = await FoodRecognitionService.analyze_image(
                    db=db,
                    user_id=current_user.id,
                    image_data=contents,
                    meal_type=meal_type_enum,
                    meal_date=parsed_date,
                    optimize=optimize
                )
                
                # 修改image_url和thumb_image_url，只保留文件名
                if "image_url" in result and result["image_url"]:
                    result["image_url"] = extract_filename(result["image_url"])
                    
                if "thumb_image_url" in result and result["thumb_image_url"]:
                    result["thumb_image_url"] = extract_filename(result["thumb_image_url"])
                
                # 检查结果是否为空，如果是，提供一个默认响应
                if not result.get("food_items") or len(result.get("food_items", [])) == 0:
                    logger.warning("食物识别服务返回空结果，提供默认响应")
                    # 创建一个默认的食物项
                    default_item = FoodItemCreate(
                        name="未识别的食物",
                        code=f"unknown_{int(time.time())}",
                        weight=100,
                        unit_name="份",
                        quantity=1,
                        calory=0,
                        protein=0,
                        fat=0,
                        carbohydrate=0,
                        matching_info={
                            "recognition_name": "未能识别",
                            "similarity": 0.0
                        },
                        nutritional_profile={
                            "health_light": 5,
                            "lights": [],
                            "warnings": ["识别失败，请重新拍照或手动输入"],
                            "warning_scenes": [],
                            "calory": 0,
                            "protein_fraction": 0.0,
                            "fat_fraction": 0.0,
                            "carb_fraction": 0.0,
                            "food_rank": "C"
                        },
                        food_details=None
                    )
                    
                    # 更新返回结果
                    result["food_items"] = [default_item]
                    result["health_recommendation"] = "抱歉，未能成功识别食物。请尝试重新拍摄更清晰的照片，或手动输入食物信息。"
                    result["error_info"] = "AI服务返回的JSON数据解析失败，可能是因为格式错误。"
                
                # 记录返回结果用于调试
                try:
                    food_items = result.get("food_items", [])
                    food_items_summary = []
                    for idx, item in enumerate(food_items):
                        food_items_summary.append({
                            "idx": idx,
                            "name": item.name if hasattr(item, 'name') else "unknown",
                            "has_code": bool(item.code if hasattr(item, 'code') else False),
                            "has_matching_info": bool(item.matching_info if hasattr(item, 'matching_info') else False),
                            "has_nutritional_profile": bool(item.nutritional_profile if hasattr(item, 'nutritional_profile') else False),
                            "food_id": item.food_id if hasattr(item, 'food_id') else None
                        })
                    logger.info(f"智能食物识别(文件上传)返回 {len(food_items)} 个食物项，详情: {json.dumps(food_items_summary, ensure_ascii=False)}")
                except Exception as e:
                    logger.error(f"记录智能食物识别(文件上传)结果详情时出错: {str(e)}")
                
                return result
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"处理上传图像失败: {str(e)}", exc_info=True)
                raise HTTPException(
                    status_code=500,
                    detail=f"处理上传图像失败: {str(e)}"
                )
                
        # 检查是否是JSON请求
        if request.headers.get("content-type", "").startswith("application/json"):
            logger.info(f"检测到JSON请求方式，处理base64图像")
            try:
                body = await request.json()
                base64_image = body.get("base64_image")
                body_optimize = body.get("optimize", optimize)
                
                if not base64_image:
                    logger.error("JSON请求中缺少base64_image字段")
                    raise HTTPException(
                        status_code=422,
                        detail="JSON请求必须包含base64_image字段"
                    )
                    
                # 验证base64图像格式
                if not base64_image.strip().startswith(("data:image/", "iVBORw0K", "/9j/")):
                    logger.error("提供的base64数据不是有效的图像格式")
                    raise HTTPException(
                        status_code=422,
                        detail="提供的base64数据不是有效的图像格式"
                    )
                
                # 使用base64处理逻辑
                result = await FoodRecognitionService.analyze_base64_image(
                    db=db,
                    user_id=current_user.id,
                    base64_image=base64_image,
                    meal_type=meal_type_enum,
                    meal_date=parsed_date,
                    optimize=body_optimize
                )
                
                # 修改image_url和thumb_image_url，只保留文件名
                if "image_url" in result and result["image_url"]:
                    result["image_url"] = extract_filename(result["image_url"])
                    
                if "thumb_image_url" in result and result["thumb_image_url"]:
                    result["thumb_image_url"] = extract_filename(result["thumb_image_url"])
                
                # 检查结果是否为空，如果是，提供一个默认响应
                if not result.get("food_items") or len(result.get("food_items", [])) == 0:
                    logger.warning("Base64食物识别服务返回空结果，提供默认响应")
                    # 创建一个默认的食物项
                    default_item = FoodItemCreate(
                        name="未识别的食物",
                        code=f"unknown_{int(time.time())}",
                        weight=100,
                        unit_name="份",
                        quantity=1,
                        calory=0,
                        protein=0,
                        fat=0,
                        carbohydrate=0,
                        matching_info={
                            "recognition_name": "未能识别",
                            "similarity": 0.0
                        },
                        nutritional_profile={
                            "health_light": 5,
                            "lights": [],
                            "warnings": ["识别失败，请重新拍照或手动输入"],
                            "warning_scenes": [],
                            "calory": 0,
                            "protein_fraction": 0.0,
                            "fat_fraction": 0.0,
                            "carb_fraction": 0.0,
                            "food_rank": "C"
                        },
                        food_details=None
                    )
                    
                    # 更新返回结果
                    result["food_items"] = [default_item]
                    result["health_recommendation"] = "抱歉，未能成功识别食物。请尝试重新拍摄更清晰的照片，或手动输入食物信息。"
                    result["error_info"] = "AI服务返回的JSON数据解析失败，可能是因为格式错误。"
                
                # 记录返回结果用于调试
                try:
                    food_items = result.get("food_items", [])
                    food_items_summary = []
                    for idx, item in enumerate(food_items):
                        food_items_summary.append({
                            "idx": idx,
                            "name": item.name if hasattr(item, 'name') else "unknown",
                            "has_code": bool(item.code if hasattr(item, 'code') else False),
                            "has_matching_info": bool(item.matching_info if hasattr(item, 'matching_info') else False),
                            "has_nutritional_profile": bool(item.nutritional_profile if hasattr(item, 'nutritional_profile') else False),
                            "food_id": item.food_id if hasattr(item, 'food_id') else None
                        })
                    logger.info(f"智能食物识别(Base64)返回 {len(food_items)} 个食物项，详情: {json.dumps(food_items_summary, ensure_ascii=False)}")
                except Exception as e:
                    logger.error(f"记录智能食物识别(Base64)结果详情时出错: {str(e)}")
                
                return result
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"处理base64图像失败: {str(e)}", exc_info=True)
                raise HTTPException(
                    status_code=500,
                    detail=f"处理base64图像失败: {str(e)}"
                )
        
        # 如果没有检测到有效的图像数据
        logger.error("请求中未提供有效的图像数据")
        raise HTTPException(
            status_code=422,
            detail="无效的请求格式。请使用multipart/form-data上传图像文件或在JSON中提供base64_image字段"
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"智能食物识别失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"智能食物识别失败: {str(e)}"
        )


@router.get("/status/{status}", response_model=List[FoodRecognitionResponse])
def get_recognitions_by_status(
    status: str,
    skip: int = 0,
    limit: int = 20,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取特定状态的食物识别记录
    
    状态可以是：processing, completed, confirmed, rejected, error, all
    """
    # 验证状态值
    valid_statuses = ["processing", "completed", "confirmed", "rejected", "error"]
    if status not in valid_statuses and status != "all":
        raise HTTPException(
            status_code=400,
            detail=f"无效的状态值，可选值: {', '.join(valid_statuses)} 或 'all'"
        )
    
    # 获取指定状态的识别记录
    if status == "all":
        recognitions = crud.food_recognition.get_multi(
            db=db,
            user_id=current_user.id,
            skip=skip,
            limit=limit
        )
    else:
        recognitions = crud.food_recognition.get_by_status(
            db=db,
            user_id=current_user.id,
            status=status,
            skip=skip,
            limit=limit
        )
    
    # 构建响应
    result = []
    for recognition in recognitions:
        try:
            # 创建食物项并确保字段完整
            food_items = []
            if recognition.recognition_result and "food_items" in recognition.recognition_result:
                for item_data in recognition.recognition_result["food_items"]:
                    try:
                        food_item = FoodItemCreate(**item_data)
                        # 使用我们的辅助函数确保所有字段都存在
                        food_item = ensure_food_item_fields(food_item, source="get_recognitions_by_status")
                        food_items.append(food_item)
                    except Exception as e:
                        logger.error(f"处理食物项时出错: {str(e)}")
            
            # 记录每个识别的匹配食物数据用于调试
            logger.info(f"识别ID {recognition.id} 包含 {len(food_items)} 个食物项, 状态: {recognition.status}")
                
            # 构建详细响应
            item_response = {
                "success": True,
                "recognition_id": recognition.id,
                "food_items": food_items,
                "meal_name": recognition.recognition_result.get("meal_name", "") if recognition.recognition_result else "",
                "health_recommendation": recognition.recognition_result.get("health_recommendation", "") if recognition.recognition_result else "",
                "image_url": recognition.image_url,
                "thumb_image_url": getattr(recognition, 'thumb_image_url', None),
                "nutrition_totals": recognition.nutrition_totals or {},
                "matched_foods": recognition.matched_foods or [],
                "status": recognition.status,
                "created_at": recognition.created_at,
                "meal_date": recognition.meal_date,
                "meal_type": recognition.meal_type
            }
            
            # 添加日志用于调试数据格式问题
            try:
                # 验证food_items字段
                for idx, item in enumerate(food_items):
                    if not item.code:
                        logger.warning(f"识别ID {recognition.id} 的食物项 {idx} 缺少code字段")
                    if not item.matching_info:
                        logger.warning(f"识别ID {recognition.id} 的食物项 {idx} 缺少matching_info字段")
                    if not item.nutritional_profile:
                        logger.warning(f"识别ID {recognition.id} 的食物项 {idx} 缺少nutritional_profile字段")
            except Exception as e:
                logger.error(f"验证食物项字段时出错: {str(e)}")
            
            result.append(item_response)
        except Exception as e:
            logger.error(f"处理识别记录 {recognition.id} 时出错: {str(e)}")
    
    return result


@router.get("/date-range", response_model=List[FoodRecognitionResponse])
def get_recognitions_by_date_range(
    start_date: date,
    end_date: date,
    status: Optional[str] = None,
    skip: int = 0,
    limit: int = 20,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取指定日期范围内的食物识别记录
    
    可选择性地按状态筛选
    """
    # 验证日期范围
    if start_date > end_date:
        raise HTTPException(
            status_code=400,
            detail="起始日期不能晚于结束日期"
        )
    
    # 验证状态值（如果提供）
    statuses = None
    if status:
        valid_statuses = ["processing", "completed", "confirmed", "rejected", "error"]
        if status == "all":
            statuses = valid_statuses
        elif status in valid_statuses:
            statuses = [status]
        else:
            raise HTTPException(
                status_code=400,
                detail=f"无效的状态值，可选值: {', '.join(valid_statuses)} 或 'all'"
            )
    
    # 获取指定日期范围内的识别记录
    recognitions = crud.food_recognition.get_by_date_range(
        db=db,
        user_id=current_user.id,
        start_date=start_date,
        end_date=end_date,
        status=statuses,
        skip=skip,
        limit=limit
    )
    
    # 构建响应
    result = []
    for recognition in recognitions:
        try:
            # 创建食物项并确保字段完整
            food_items = []
            if recognition.recognition_result and "food_items" in recognition.recognition_result:
                for item_data in recognition.recognition_result["food_items"]:
                    try:
                        food_item = FoodItemCreate(**item_data)
                        # 使用我们的辅助函数确保所有字段都存在
                        food_item = ensure_food_item_fields(food_item, source="get_recognitions_by_date_range")
                        food_items.append(food_item)
                    except Exception as e:
                        logger.error(f"处理食物项时出错: {str(e)}")
            
            # 记录每个识别的匹配食物数据用于调试
            logger.debug(f"识别ID {recognition.id} 包含 {len(food_items)} 个食物项, 状态: {recognition.status}")
                
            # 构建详细响应
            item_response = {
                "success": True,
                "recognition_id": recognition.id,
                "food_items": food_items,
                "meal_name": recognition.recognition_result.get("meal_name", "") if recognition.recognition_result else "",
                "health_recommendation": recognition.recognition_result.get("health_recommendation", "") if recognition.recognition_result else "",
                "image_url": recognition.image_url,
                "thumb_image_url": getattr(recognition, 'thumb_image_url', None),
                "nutrition_totals": recognition.nutrition_totals or {},
                "matched_foods": recognition.matched_foods or [],
                "status": recognition.status,
                "created_at": recognition.created_at,
                "meal_date": recognition.meal_date,
                "meal_type": recognition.meal_type
            }
            
            result.append(item_response)
        except Exception as e:
            logger.error(f"处理识别记录 {recognition.id} 时出错: {str(e)}")
    
    return result


@router.get("/stats", response_model=FoodRecognitionStats)
def get_recognition_stats(
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取用户的食物识别统计信息
    """
    stats = crud.food_recognition.get_stats_by_user(
        db=db,
        user_id=current_user.id
    )
    
    return {
        "processing": stats.get("processing", 0),
        "completed": stats.get("completed", 0),
        "confirmed": stats.get("confirmed", 0),
        "rejected": stats.get("rejected", 0),
        "error": stats.get("error", 0),
        "total": stats.get("total", 0)
    }


@router.post("/{recognition_id}/reject")
async def reject_recognition(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    recognition_id: int,
    rejection: FoodRecognitionRejection,
) -> Any:
    """
    拒绝食物识别结果
    """
    try:
        # 获取临时识别记录
        recognition = crud.food_recognition.get(db=db, id=recognition_id)
        if not recognition:
            raise HTTPException(status_code=404, detail="识别记录不存在")
        
        if str(recognition.user_id) != str(current_user.id) and not current_user.is_superuser:
            raise HTTPException(status_code=403, detail="没有权限操作此识别结果")
        
        # 拒绝识别结果
        recognition.status = "rejected"
        if rejection.reason:
            # 存储拒绝原因
            if not recognition.recognition_result:
                recognition.recognition_result = {}
            recognition.recognition_result["reject_reason"] = rejection.reason
            
        db.add(recognition)
        db.commit()
        
        return {
            "success": True,
            "message": "识别结果已拒绝",
            "recognition_id": recognition_id
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"拒绝识别结果失败: {str(e)}"
        )


@router.get("/", response_model=List[FoodRecognition])
def read_food_recognitions(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    status: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """
    获取用户的食物识别记录列表。
    """
    return crud.food_recognition.get_multi_by_user(
        db=db, 
        user_id=current_user.id,
        start_date=start_date,
        end_date=end_date,
        status=status,
        skip=skip,
        limit=limit
    )


@router.get("/{recognition_id}", response_model=FoodRecognitionResponse)
def read_food_recognition(
    *,
    db: Session = Depends(deps.get_db),
    recognition_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取单个食物识别记录详情。
    """
    recognition = crud.food_recognition.get(db=db, id=recognition_id)
    if not recognition:
        raise HTTPException(status_code=404, detail="食物识别记录不存在")
    
    # 记录类型和值用于调试
    user_id_type = type(recognition.user_id).__name__
    recognition_user_id = recognition.user_id
    current_user_id = current_user.id
    
    # 将user_id转换为字符串进行比较
    # FoodRecognition模型中user_id是字符串类型
    if str(current_user.id) != str(recognition.user_id) and not current_user.is_superuser:
        logger.error(f"权限验证失败: recognition.user_id={recognition.user_id}({user_id_type}), current_user.id={current_user.id}, is_superuser={current_user.is_superuser}")
        raise HTTPException(status_code=403, detail="没有权限查看此食物识别记录")
    
    # 添加详细日志记录
    try:
        # 记录基本信息
        logger.info(f"获取食物识别记录，ID: {recognition_id}, 用户ID: {recognition.user_id}({user_id_type}), 当前用户ID: {current_user.id}, 管理员: {current_user.is_superuser}, 状态: {recognition.status}")
        
        # 记录详细数据
        log_data = {
            "id": recognition.id,
            "user_id": recognition.user_id,
            "meal_date": str(recognition.meal_date),
            "meal_type": recognition.meal_type,
            "status": recognition.status,
            "meal_record_id": recognition.meal_record_id,
            "image_url": recognition.image_url,
            "has_recognition_result": recognition.recognition_result,
            "has_matched_foods": bool(recognition.matched_foods),
            "has_nutrition_totals": bool(recognition.nutrition_totals),
            "created_at": str(recognition.created_at),
            "updated_at": str(recognition.updated_at)
        }
        logger.info(f"食物识别记录详情: {json.dumps(log_data, ensure_ascii=False)}")
        
        # 检查user_id类型
        logger.info(f"user_id类型: {user_id_type}, 值: {recognition.user_id}")
    except Exception as e:
        logger.error(f"记录食物识别数据时出错: {str(e)}")
    
    # 构建前端期望的响应格式
    food_items = []
    meal_name = None
    health_recommendation = None
    
    # 从recognition_result中提取数据
    if recognition.recognition_result:
        if "food_items" in recognition.recognition_result:
            try:
                for item_data in recognition.recognition_result["food_items"]:
                    food_item = FoodItemCreate(**item_data)
                    # 使用我们的辅助函数确保所有字段都存在
                    food_item = ensure_food_item_fields(food_item, source="read_food_recognition")
                    food_items.append(food_item)
            except Exception as e:
                logger.error(f"处理食物项时出错: {str(e)}")
                
        meal_name = recognition.recognition_result.get("meal_name", "")
        health_recommendation = recognition.recognition_result.get("health_recommendation", "")
        
    # 构建响应
    response = {
        "success": True,
        "recognition_id": recognition.id,
        "food_items": food_items,
        "meal_name": meal_name,
        "health_recommendation": health_recommendation,
        "image_url": recognition.image_url,
        "thumb_image_url": getattr(recognition, 'thumb_image_url', None),
        "nutrition_totals": recognition.nutrition_totals or {},
        "matched_foods": recognition.matched_foods or [],
        "status": recognition.status,
        "created_at": recognition.created_at,
        "meal_date": recognition.meal_date,
        "meal_type": recognition.meal_type
    }
    
    # 记录最终响应
    logger.info(f"最终返回的响应格式: meal_name={meal_name}, food_items数量={len(food_items)}")
    
    return response


@router.post("/", response_model=FoodRecognition)
def create_food_recognition(
    *,
    db: Session = Depends(deps.get_db),
    recognition_in: FoodRecognitionCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    创建新的食物识别记录。
    """
    recognition_in_data = recognition_in.dict()
    recognition_in_data["user_id"] = current_user.id
    
    return crud.food_recognition.create(db=db, obj_in=recognition_in_data)


@router.put("/{recognition_id}", response_model=FoodRecognition)
def update_food_recognition(
    *,
    db: Session = Depends(deps.get_db),
    recognition_id: int,
    recognition_in: FoodRecognitionUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    更新食物识别记录。
    """
    recognition = crud.food_recognition.get(db=db, id=recognition_id)
    if not recognition:
        raise HTTPException(status_code=404, detail="食物识别记录不存在")
    if str(recognition.user_id) != str(current_user.id) and not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="没有权限更新此食物识别记录")
    
    return crud.food_recognition.update(db=db, db_obj=recognition, obj_in=recognition_in)


@router.delete("/{recognition_id}", response_model=FoodRecognition)
def delete_food_recognition(
    *,
    db: Session = Depends(deps.get_db),
    recognition_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    删除食物识别记录。
    """
    recognition = crud.food_recognition.get(db=db, id=recognition_id)
    if not recognition:
        raise HTTPException(status_code=404, detail="食物识别记录不存在")
    if str(recognition.user_id) != str(current_user.id) and not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="没有权限删除此食物识别记录")
    
    return crud.food_recognition.remove(db=db, id=recognition_id)
