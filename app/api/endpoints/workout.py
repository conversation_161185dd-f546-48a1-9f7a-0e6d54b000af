from typing import Any, List, Dict
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from app import crud, models, schemas
from app.api import deps
from app.schemas.workout import WorkoutStatusEnum, WorkoutStatusUpdate, WorkoutStatistics, WorkoutWithExercises, WorkoutFullUpdate
from app.services.workout_service import WorkoutService

router = APIRouter()

@router.put("/{workout_id}/status", response_model=schemas.workout.WorkoutInDB)
async def update_workout_status(
    *,
    db: Session = Depends(deps.get_db),
    workout_id: int,
    status_update: WorkoutStatusUpdate,
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """更新训练状态"""
    # 检查训练日是否存在并属于当前用户
    workout = crud.crud_workout.get(db, id=workout_id)
    if not workout:
        raise HTTPException(status_code=404, detail="训练日不存在")

    training_plan = crud.crud_training_plan.get(db, id=workout.training_plan_id)
    if training_plan.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权访问此训练日")

    # 更新状态
    workout_service = WorkoutService(db)
    updated_workout = workout_service.update_workout_status(workout_id, status_update.status)

    return updated_workout

@router.get("/{workout_id}/statistics", response_model=WorkoutStatistics)
async def get_workout_statistics(
    *,
    db: Session = Depends(deps.get_db),
    workout_id: int,
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """获取训练统计数据"""
    # 检查训练日是否存在并属于当前用户
    workout = crud.crud_workout.get(db, id=workout_id)
    if not workout:
        raise HTTPException(status_code=404, detail="训练日不存在")

    training_plan = crud.crud_training_plan.get(db, id=workout.training_plan_id)
    if training_plan.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权访问此训练日")

    # 获取统计数据
    workout_service = WorkoutService(db)
    stats = workout_service.calculate_workout_statistics(workout_id)

    return stats

@router.post("/{workout_id}/link-session/{session_id}", response_model=schemas.workout.WorkoutInDB)
async def link_with_training_session(
    *,
    db: Session = Depends(deps.get_db),
    workout_id: int,
    session_id: int,
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """关联训练日与团队训练会话"""
    # 检查训练日是否存在并属于当前用户
    workout = crud.crud_workout.get(db, id=workout_id)
    if not workout:
        raise HTTPException(status_code=404, detail="训练日不存在")

    training_plan = crud.crud_training_plan.get(db, id=workout.training_plan_id)
    if training_plan.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权访问此训练日")

    # 检查是否有权访问该训练会话
    # TODO: 根据业务逻辑验证用户是否有权关联特定的训练会话
    # 此处简化处理，仅检查基本权限

    # 关联会话
    workout_service = WorkoutService(db)
    updated_workout = workout_service.link_with_training_session(workout_id, session_id)

    return updated_workout

@router.get("/by-date/{date}", response_model=List[Dict[str, Any]])
async def get_workouts_by_date(
    *,
    db: Session = Depends(deps.get_db),
    date: str,
    current_user: models.User = Depends(deps.get_current_active_user),
    brief: bool = Query(False, description="是否只返回简略信息，不包括训练动作和组记录")
) -> Any:
    """根据日期获取训练日及其相关数据

    Args:
        date: 日期，格式为YYYY-MM-DD
        brief: 是否只返回简略信息，不包括训练动作和组记录

    Returns:
        训练日列表，根据brief参数决定是否包含训练动作和组记录
    """
    try:
        # 解析日期字符串
        parsed_date = datetime.strptime(date, "%Y-%m-%d")
    except ValueError:
        raise HTTPException(status_code=400, detail="日期格式无效，请使用YYYY-MM-DD格式")

    # 获取训练日
    workout_service = WorkoutService(db)

    if brief:
        # 获取简略信息
        workouts = workout_service.get_workouts_brief_by_date(parsed_date, current_user.id)
    else:
        # 获取完整信息
        workouts = workout_service.get_workouts_by_date(parsed_date, current_user.id)

    return workouts

@router.get("/{workout_id}", response_model=Dict[str, Any])
async def get_workout_detail(
    *,
    db: Session = Depends(deps.get_db),
    workout_id: int,
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """获取训练日详情

    Args:
        workout_id: 训练日ID

    Returns:
        训练日详情，包括训练动作
    """
    # 获取训练日详情
    workout = crud.crud_workout.get_with_exercises(db, id=workout_id)
    if not workout:
        raise HTTPException(status_code=404, detail="训练日不存在")

    # 验证用户权限（只能查看自己的训练日）
    training_plan = crud.crud_training_plan.get(db, id=workout["training_plan_id"])
    if training_plan.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权访问此训练日")

    return workout

@router.put("/{workout_id}/update-full", response_model=Dict[str, Any])
async def update_workout_full(
    *,
    db: Session = Depends(deps.get_db),
    workout_id: int,
    update_data: WorkoutFullUpdate,
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """全面更新训练日，包括基本信息、训练动作和组记录
    
    Args:
        workout_id: 训练日ID
        update_data: 更新数据，包含基本信息、训练动作和组记录
        
    Returns:
        更新后的训练日详情，包含训练动作和组记录
    """
    # 检查训练日是否存在并属于当前用户
    workout = crud.crud_workout.get(db, id=workout_id)
    if not workout:
        raise HTTPException(status_code=404, detail="训练日不存在")

    training_plan = crud.crud_training_plan.get(db, id=workout.training_plan_id)
    if training_plan.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权访问此训练日")

    # 调用服务方法执行更新
    workout_service = WorkoutService(db)
    updated_workout = workout_service.update_workout_full(workout_id, update_data)

    return updated_workout