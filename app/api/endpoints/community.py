from typing import List, Optional, Dict, Any, Union
from fastapi import APIRouter, Depends, HTTPException, status, File, UploadFile, Form, Query, Path
from sqlalchemy.orm import Session
from fastapi.responses import FileResponse
import os
import enum
from enum import Enum
import json

from app import crud, models, schemas
from app.api import deps
from app.services.community_service import CommunityService
from app.services.workout_summary_service import WorkoutSummaryService
from app.core.config import settings
from app.schemas.community import (
    DailyWorkoutCreate, DailyWorkoutUpdate, DailyWorkoutResponse,
    PostCreate, PostUpdate, PostResponse,
    CommentCreate, CommentResponse,
    NotificationResponse,
    UserRelationResponse,
    ImageCreate, ImageUpdate, ImageResponse,
    WorkoutShareCreate
)
from app.models.community.notification import NotificationType

router = APIRouter()

# Workout Share Endpoints
@router.post("/workout/{workout_id}/share")
async def share_workout(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    workout_id: int = Path(..., description="Workout ID，如果为0则创建新的workout"),
    share_data: WorkoutShareCreate,
    community_service: CommunityService = Depends(deps.get_community_service),
    workout_summary_service: WorkoutSummaryService = Depends()
):
    """从训练记录创建Post并分享到社区，支持更新现有workout或创建新的workout"""
    try:
        # 处理workout_id为0的情况(创建新workout)
        actual_workout_id = None if workout_id == 0 else workout_id

        # 创建或更新Workout和DailyWorkout
        result = await workout_summary_service.create_or_update_workout_and_summary(
            db=db,
            user_id=current_user.id,
            workout_id=actual_workout_id,
            data={
                "title": share_data.title,
                "content": share_data.content,
                "workout_data": share_data.workout_data.dict() if hasattr(share_data, "workout_data") else {},
                "visibility": share_data.visibility,
                "tags": share_data.tags if hasattr(share_data, "tags") else []
            }
        )

        workout = result.get("workout")
        daily_workout = result.get("daily_workout")

        if not daily_workout:
            raise HTTPException(status_code=404, detail="无法创建训练记录")

        # 创建Post并关联DailyWorkout
        post_data = PostCreate(
            title=share_data.title or "训练记录",
            content=share_data.content or "",
            daily_workout_id=daily_workout.id,
            images=share_data.images if hasattr(share_data, "images") else [],
            visibility=share_data.visibility,
            tags=share_data.tags if hasattr(share_data, "tags") else []
        )

        post = await community_service.create_post_with_workout(current_user.id, post_data)

        # 返回基本信息，避免复杂的响应模型
        return {
            "id": post.id,
            "title": post.title,
            "content": post.content,
            "user_id": post.user_id,
            "created_at": post.created_at,
            "updated_at": post.updated_at,
            "tags": post.tags if hasattr(post, "tags") else [],
            "message": "训练分享成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"分享失败: {str(e)}")

# Daily Workout Endpoints
@router.post("/daily-workouts/", response_model=DailyWorkoutResponse)
async def create_daily_workout(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    workout_in: DailyWorkoutCreate
):
    service = CommunityService(db)
    return await service.create_daily_workout(current_user.id, workout_in)

@router.put("/daily-workouts/{workout_id}", response_model=DailyWorkoutResponse)
async def update_daily_workout(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    workout_id: int,
    workout_in: DailyWorkoutUpdate
):
    service = CommunityService(db)
    return await service.update_daily_workout(workout_id, current_user.id, workout_in)

@router.delete("/daily-workouts/{workout_id}")
async def delete_daily_workout(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    workout_id: int
):
    service = CommunityService(db)
    return await service.delete_daily_workout(workout_id, current_user.id)

@router.get("/daily-workouts/{workout_id}", response_model=DailyWorkoutResponse)
async def get_daily_workout(
    *,
    db: Session = Depends(deps.get_db),
    workout_id: int
):
    service = CommunityService(db)
    return await service.get_daily_workout(workout_id)

@router.get("/daily-workouts/", response_model=List[DailyWorkoutResponse])
async def get_daily_workouts(
    *,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100
):
    service = CommunityService(db)
    return await service.get_daily_workouts(skip, limit)

@router.get("/daily-workouts/search/", response_model=List[DailyWorkoutResponse])
async def search_daily_workouts(
    *,
    db: Session = Depends(deps.get_db),
    keyword: str,
    skip: int = 0,
    limit: int = 20
):
    service = CommunityService(db)
    return await service.search_daily_workouts(keyword, skip, limit)

# Post Endpoints
@router.post("/posts/", response_model=PostResponse)
async def create_post(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    post_in: PostCreate
):
    service = CommunityService(db)
    return await service.create_post_with_workout(current_user.id, post_in)

@router.put("/posts/{post_id}", response_model=PostResponse)
async def update_post(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    post_id: int,
    post_in: PostUpdate
):
    service = CommunityService(db)
    return await service.update_post(post_id, current_user.id, post_in)

@router.delete("/posts/{post_id}")
async def delete_post(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    post_id: int
):
    service = CommunityService(db)
    return await service.delete_post(post_id, current_user.id)

@router.get("/posts/", response_model=schemas.community.PostListResponse)
async def get_posts(
    *,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 20,
    current_user: Optional[models.User] = Depends(deps.get_current_user_optional)
):
    try:
        print(f"DEBUG - API get_posts: skip={skip}, limit={limit}, current_user_id={current_user.id if current_user else None}")
        service = CommunityService(db)
        current_user_id = current_user.id if current_user else None
        result = await service.get_posts(skip=skip, limit=limit, current_user_id=current_user_id)

        # 打印日志
        print(f"DEBUG - API get_posts 返回: total={result.get('total')}, items数量={len(result.get('items', []))}")
        if result['items']:
            print(f"DEBUG - 返回的第一个帖子status类型: {type(result['items'][0]['status'])}")
            print(f"DEBUG - 返回的第一个帖子status值: {result['items'][0]['status']}")
            print(f"DEBUG - 第一个帖子的完整数据: {list(result['items'][0].keys())}")

        # 数据清理和转换
        for item in result["items"]:
            # PostStatus枚举转为字符串
            if isinstance(item["status"], Enum):
                item["status"] = str(item["status"].value)

            # 确保所有必需字段存在
            if "view_count" not in item:
                item["view_count"] = 0
            if "related_workout_detail" not in item:
                item["related_workout_detail"] = None
            if "images" not in item:
                item["images"] = []
            if "comments_summary" not in item:
                item["comments_summary"] = []

        print(f"DEBUG - 数据清理后准备返回")
        return result

    except Exception as e:
        print(f"ERROR - get_posts发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"获取帖子列表失败: {str(e)}")

@router.get("/posts/{post_id}", response_model=PostResponse)
async def get_post(
    *,
    db: Session = Depends(deps.get_db),
    post_id: int,
    current_user: Optional[models.User] = Depends(deps.get_current_user_optional)
):
    service = CommunityService(db)
    current_user_id = current_user.id if current_user else None
    result = await service.get_post(id=post_id, current_user_id=current_user_id)
    if not result:
        raise HTTPException(status_code=404, detail="Post not found")

    # 强制转换status为字符串
    if isinstance(result["status"], Enum):
        result["status"] = str(result["status"].value)

    return result

@router.get("/posts/search/", response_model=List[PostResponse])
async def search_posts(
    *,
    db: Session = Depends(deps.get_db),
    keyword: str,
    skip: int = 0,
    limit: int = 20
):
    service = CommunityService(db)
    return await service.search_posts(keyword, skip, limit)

@router.post("/posts/{post_id}/like/")
async def like_post(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    post_id: int
):
    service = CommunityService(db)
    return await service.like_post(post_id, current_user.id)

@router.post("/posts/{post_id}/report/")
async def report_post(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    post_id: int,
    reason: str
):
    service = CommunityService(db)
    return await service.report_post(post_id, current_user.id, reason)

# Comment Endpoints
@router.post("/posts/{post_id}/comments/", response_model=CommentResponse)
async def create_comment(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    post_id: int,
    comment_in: CommentCreate
):
    service = CommunityService(db)
    return await service.create_comment(current_user.id, comment_in)

@router.put("/comments/{comment_id}", response_model=CommentResponse)
async def update_comment(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    comment_id: int,
    content: str
):
    service = CommunityService(db)
    return await service.update_comment(comment_id, current_user.id, content)

@router.delete("/comments/{comment_id}")
async def delete_comment(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    comment_id: int
):
    service = CommunityService(db)
    return await service.delete_comment(comment_id, current_user.id)

@router.post("/comments/{comment_id}/like/")
async def like_comment(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    comment_id: int
):
    service = CommunityService(db)
    return await service.like_comment(comment_id, current_user.id)

@router.post("/comments/{comment_id}/report/")
async def report_comment(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    comment_id: int,
    reason: str
):
    service = CommunityService(db)
    return await service.report_comment(comment_id, current_user.id, reason)

# Notification Endpoints
@router.get("/notifications/", response_model=List[NotificationResponse])
async def get_notifications(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    skip: int = 0,
    limit: int = 20
):
    service = CommunityService(db)
    return await service.get_notifications(current_user.id, skip, limit)

@router.get("/notifications/filter/", response_model=List[NotificationResponse])
async def filter_notifications(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    type: NotificationType,
    skip: int = 0,
    limit: int = 20
):
    service = CommunityService(db)
    return await service.filter_notifications(current_user.id, type, skip, limit)

@router.patch("/notifications/{notification_id}/read/")
async def mark_notification_as_read(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    notification_id: int
):
    service = CommunityService(db)
    return await service.mark_notification_as_read(notification_id, current_user.id)

@router.patch("/notifications/read-all/")
async def mark_all_notifications_as_read(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user)
):
    service = CommunityService(db)
    return await service.mark_all_notifications_as_read(current_user.id)

@router.delete("/notifications/{notification_id}")
async def delete_notification(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    notification_id: int
):
    service = CommunityService(db)
    return await service.delete_notification(notification_id, current_user.id)

# User Relation Endpoints
@router.post("/users/{user_id}/follow/")
async def follow_user(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    user_id: int
):
    service = CommunityService(db)
    return await service.follow_user(current_user.id, user_id)

@router.delete("/users/{user_id}/follow/")
async def unfollow_user(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    user_id: int
):
    service = CommunityService(db)
    return await service.unfollow_user(current_user.id, user_id)

@router.get("/users/{user_id}/following/", response_model=List[UserRelationResponse])
async def get_following(
    *,
    db: Session = Depends(deps.get_db),
    user_id: int,
    skip: int = 0,
    limit: int = 20
):
    service = CommunityService(db)
    return await service.get_following(user_id, skip, limit)

@router.get("/users/{user_id}/followers/", response_model=List[UserRelationResponse])
async def get_followers(
    *,
    db: Session = Depends(deps.get_db),
    user_id: int,
    skip: int = 0,
    limit: int = 20
):
    service = CommunityService(db)
    return await service.get_followers(user_id, skip, limit)

# Image Endpoints
@router.post("/images/", response_model=ImageResponse)
async def create_image(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    image_in: ImageCreate
):
    service = CommunityService(db)
    return await service.create_image(current_user.id, image_in)

@router.put("/images/{image_id}", response_model=ImageResponse)
async def update_image(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    image_id: int,
    image_in: ImageUpdate
):
    service = CommunityService(db)
    return await service.update_image(image_id, current_user.id, image_in)

@router.delete("/images/{image_id}")
async def delete_image(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_user),
    image_id: int
):
    service = CommunityService(db)
    return await service.delete_image(image_id, current_user.id)

@router.get("/posts/debug/")
async def get_posts_debug(
    *,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 20,
    current_user: Optional[models.User] = Depends(deps.get_current_user_optional)
):
    """调试版本的获取帖子列表接口，不使用响应模型校验"""
    print(f"DEBUG - API get_posts_debug: skip={skip}, limit={limit}, current_user_id={current_user.id if current_user else None}")
    try:
        service = CommunityService(db)
        current_user_id = current_user.id if current_user else None
        result = await service.get_posts(skip=skip, limit=limit, current_user_id=current_user_id)

        # 打印完整响应
        print(f"DEBUG - API get_posts_debug 返回: total={result.get('total')}, items数量={len(result.get('items', []))}")
        if result.get('items'):
            first_item = result['items'][0]
            print(f"DEBUG - 返回的第一个帖子status类型: {type(first_item['status'])}")
            print(f"DEBUG - 返回的第一个帖子status值: {first_item['status']}")
            print(f"DEBUG - 第一个帖子的所有字段: {list(first_item.keys())}")

            # 检查用户字段
            if 'user' in first_item:
                print(f"DEBUG - 用户字段类型: {type(first_item['user'])}")
                if hasattr(first_item['user'], '__dict__'):
                    print(f"DEBUG - 用户字段内容: {first_item['user'].__dict__}")
                else:
                    print(f"DEBUG - 用户字段内容: {first_item['user']}")

        # 数据清理和转换
        for item in result["items"]:
            # PostStatus枚举转为字符串
            if isinstance(item["status"], Enum):
                item["status"] = str(item["status"].value)

            # 转换用户对象为字典
            if hasattr(item.get("user"), '__dict__'):
                user_dict = {}
                user_obj = item["user"]
                for attr in ['id', 'nickname', 'avatar_url', 'gender', 'age', 'weight', 'height',
                           'activity_level', 'bmi', 'tedd', 'completed', 'country', 'province',
                           'city', 'created_at', 'birthday']:
                    if hasattr(user_obj, attr):
                        value = getattr(user_obj, attr)
                        # 处理datetime对象
                        if hasattr(value, 'isoformat'):
                            value = value.isoformat()
                        user_dict[attr] = value
                item["user"] = user_dict

        print(f"DEBUG - 数据清理后准备返回")
        return result
    except Exception as e:
        print(f"ERROR - get_posts_debug发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"获取帖子列表失败: {str(e)}")


@router.get("/posts/simple/")
async def get_posts_simple(
    *,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 20,
    current_user: Optional[models.User] = Depends(deps.get_current_user_optional)
):
    """简化版本的获取帖子列表接口，返回基本JSON数据"""
    try:
        print(f"DEBUG - API get_posts_simple: skip={skip}, limit={limit}")
        service = CommunityService(db)
        current_user_id = current_user.id if current_user else None
        result = await service.get_posts(skip=skip, limit=limit, current_user_id=current_user_id)

        # 简化数据结构
        simplified_result = {
            "total": result.get("total", 0),
            "items": []
        }

        for item in result.get("items", []):
            simplified_item = {
                "id": item.get("id"),
                "title": item.get("title"),
                "content": item.get("content"),
                "user_id": item.get("user_id"),
                "status": str(item.get("status").value) if hasattr(item.get("status"), 'value') else str(item.get("status")),
                "like_count": item.get("like_count", 0),
                "comment_count": item.get("comment_count", 0),
                "view_count": item.get("view_count", 0),
                "created_at": item.get("created_at").isoformat() if item.get("created_at") else None,
                "updated_at": item.get("updated_at").isoformat() if item.get("updated_at") else None,
                "is_liked_by_current_user": item.get("is_liked_by_current_user", False),
                "tags": item.get("tags", [])
            }

            # 简化用户信息
            user = item.get("user")
            if user:
                simplified_item["user"] = {
                    "id": getattr(user, 'id', None),
                    "nickname": getattr(user, 'nickname', None),
                    "avatar_url": getattr(user, 'avatar_url', None)
                }

            simplified_result["items"].append(simplified_item)

        print(f"DEBUG - 简化数据返回: total={simplified_result['total']}, items={len(simplified_result['items'])}")
        return simplified_result

    except Exception as e:
        print(f"ERROR - get_posts_simple发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"获取帖子列表失败: {str(e)}")


@router.get("/posts/test/")
async def get_posts_test(
    *,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 20
):
    """测试版本 - 直接查询数据库"""
    try:
        from app.models.community.post import Post

        # 直接查询数据库，不使用状态过滤
        posts = db.query(Post).order_by(Post.created_at.desc()).offset(skip).limit(limit).all()
        total = db.query(Post).count()

        print(f"DEBUG - 直接查询结果: total={total}, posts={len(posts)}")

        if posts:
            first_post = posts[0]
            print(f"DEBUG - 第一个帖子: id={first_post.id}, title={first_post.title}, status={first_post.status}")
            print(f"DEBUG - 状态类型: {type(first_post.status)}")

        # 构建简单响应
        result = {
            "total": total,
            "items": []
        }

        for post in posts:
            item = {
                "id": post.id,
                "title": post.title,
                "content": post.content,
                "user_id": post.user_id,
                "status": str(post.status),
                "created_at": post.created_at.isoformat() if post.created_at else None,
                "updated_at": post.updated_at.isoformat() if post.updated_at else None
            }
            result["items"].append(item)

        return result

    except Exception as e:
        print(f"ERROR - get_posts_test发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"测试查询失败: {str(e)}")


@router.get("/posts/db-check/")
async def check_database_status(
    *,
    db: Session = Depends(deps.get_db)
):
    """检查数据库中的状态值类型"""
    try:
        from app.models.community.post import Post

        # 直接查询数据库，检查状态值
        result = db.execute("SELECT id, title, status FROM posts LIMIT 5")
        rows = result.fetchall()

        print(f"DEBUG - 数据库查询结果:")
        db_data = []
        for row in rows:
            print(f"  ID: {row[0]}, Title: {row[1]}, Status: {row[2]} (type: {type(row[2])})")
            db_data.append({
                "id": row[0],
                "title": row[1],
                "status": row[2],
                "status_type": str(type(row[2]))
            })

        # 检查枚举类型定义
        from app.models.community.post import PostStatus
        print(f"DEBUG - PostStatus 枚举定义:")
        print(f"  ACTIVE = {PostStatus.ACTIVE} (type: {type(PostStatus.ACTIVE)})")
        print(f"  DELETED = {PostStatus.DELETED} (type: {type(PostStatus.DELETED)})")
        print(f"  REPORTED = {PostStatus.REPORTED} (type: {type(PostStatus.REPORTED)})")

        # 检查数据库枚举类型
        enum_result = db.execute("SELECT enumlabel FROM pg_enum WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'poststatus')")
        enum_values = [row[0] for row in enum_result.fetchall()]
        print(f"DEBUG - 数据库中的枚举值: {enum_values}")

        return {
            "database_data": db_data,
            "python_enum": {
                "ACTIVE": str(PostStatus.ACTIVE),
                "DELETED": str(PostStatus.DELETED),
                "REPORTED": str(PostStatus.REPORTED)
            },
            "database_enum_values": enum_values
        }

    except Exception as e:
        print(f"ERROR - check_database_status发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"数据库检查失败: {str(e)}")
