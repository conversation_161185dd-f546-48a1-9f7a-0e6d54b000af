from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session

from app.api import deps
from app.services.llm_log_service import LLMLogService
from app.core.config import settings
import logging

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/by-date/{date}")
async def get_logs_by_date(
    date: str,
    db: Session = Depends(deps.get_db),
    current_user = Depends(deps.get_current_active_superuser)  # 仅管理员可访问
):
    """
    获取指定日期的所有日志
    """
    logs = LLMLogService.get_logs_by_date(date)
    return {
        "date": date,
        "count": len(logs),
        "logs": logs
    }

@router.get("/by-user/{user_id}")
async def get_logs_by_user(
    user_id: str,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    db: Session = Depends(deps.get_db),
    current_user = Depends(deps.get_current_active_superuser)  # 仅管理员可访问
):
    """
    获取指定用户的所有日志
    """
    logs = LLMLogService.get_logs_by_user(user_id, start_date, end_date)
    return {
        "user_id": user_id,
        "start_date": start_date,
        "end_date": end_date,
        "count": len(logs),
        "logs": logs
    }

@router.get("/details")
async def get_log_details(
    log_directory: str,
    db: Session = Depends(deps.get_db),
    current_user = Depends(deps.get_current_active_superuser)  # 仅管理员可访问
):
    """
    获取指定日志的详细信息
    """
    details = LLMLogService.get_log_details(log_directory)
    return details

@router.get("/image")
async def get_log_image(
    log_directory: str,
    db: Session = Depends(deps.get_db),
    current_user = Depends(deps.get_current_active_superuser)  # 仅管理员可访问
):
    """
    获取日志关联的图片
    """
    import os
    image_path = os.path.join(log_directory, "request_image.jpg")
    
    if not os.path.exists(image_path):
        raise HTTPException(status_code=404, detail="Image not found")
    
    return FileResponse(image_path, media_type="image/jpeg")

@router.post("/clean")
async def clean_old_logs(
    days_to_keep: int = Query(30, ge=1, le=365),
    background_tasks: BackgroundTasks = None,
    db: Session = Depends(deps.get_db),
    current_user = Depends(deps.get_current_active_superuser)  # 仅管理员可访问
):
    """
    清理指定天数之前的日志
    """
    if background_tasks:
        # 在后台任务中执行清理
        background_tasks.add_task(LLMLogService.clean_old_logs, days_to_keep)
        return {"message": f"正在后台清理超过 {days_to_keep} 天的日志"}
    else:
        # 直接执行清理
        removed_count = LLMLogService.clean_old_logs(days_to_keep)
        return {"message": f"清理完成，已移除 {removed_count} 个日期目录"}

@router.get("/qa-pairs/by-user/{user_id}")
async def get_qa_pairs_by_user(
    user_id: str,
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(deps.get_db),
    current_user = Depends(deps.get_current_active_superuser)  # 仅管理员可访问
):
    """
    获取指定用户的所有问答对
    """
    logs = LLMLogService.get_logs_by_user(user_id)
    
    qa_pairs = []
    for log in logs[:limit]:
        log_dir = log.get("log_directory")
        if log_dir:
            try:
                import os
                import json
                qa_path = os.path.join(log_dir, "qa_pairs.json")
                if os.path.exists(qa_path):
                    with open(qa_path, "r", encoding="utf-8") as f:
                        pairs = json.load(f)
                        for pair in pairs:
                            pair["log_directory"] = log_dir
                            pair["metadata"] = log.get("metadata")
                            qa_pairs.append(pair)
            except Exception as e:
                logger.error(f"读取问答对失败: {str(e)}")
    
    return {
        "user_id": user_id,
        "count": len(qa_pairs),
        "qa_pairs": qa_pairs
    } 