"""
聊天API辅助函数模块

此模块包含用于chat.py的辅助函数和工具，实现了聊天API的核心功能。
提取这些函数可以使主路由文件更简洁、更易于维护。
"""

from typing import Any, Dict, List, Optional, Tuple, Union
from sqlalchemy.orm import Session
import logging
import json
import asyncio
from fastapi import HTTPException, WebSocket, WebSocketDisconnect

from app import crud, models, schemas
from app.models.message import MessageRole
from app.services.llm_proxy_service import LLMProxyService
from app.services.chat_log_service import ChatLogService
from app.services.cache_service import cache_service
from app.services.conversation.orchestrator import ConversationService
from app.services.training_plan_service import TrainingPlanService
from app.services.sql_tool_service import SQLToolService
from app.services.meta_info_manager import MetaInfoManager
from app.services.intent_transition_manager import IntentTransitionManager
from app.services.parameter_collection_manager import ParameterCollectionManager

logger = logging.getLogger(__name__)

# ============== 会话管理辅助函数 ==============

def get_or_create_conversation(
    db: Session, 
    session_id: str, 
    user_id: int,
    meta_info: Dict[str, Any] = None,
    system_prompt: str = None
) -> Tuple[models.Conversation, bool]:
    """
    获取或创建会话，并处理系统提示
    
    Args:
        db: 数据库会话
        session_id: 会话ID
        user_id: 用户ID
        meta_info: 元数据信息
        system_prompt: 系统提示消息
        
    Returns:
        元组：(会话对象, 是否新创建)
    """
    # 检查是否有现有会话
    conversation = crud.crud_conversation.get_by_session_id(db, session_id=session_id)
    is_new = False
    
    # 如果会话存在且不属于当前用户，返回错误
    if conversation and conversation.user_id != user_id:
        raise HTTPException(status_code=404, detail="会话未找到")
    
    # 如果会话不存在，创建新会话
    if not conversation:
        logger.info(f"为用户{user_id}创建新会话: {session_id}")
        
        # 尝试使用现有的session_id，如果未提供则生成新的UUID
        try:
            # 尝试create_with_session_id方法
            conversation = crud.crud_conversation.create_with_session_id(
                db,
                obj_in=schemas.ConversationCreate(
                    session_id=session_id,
                    user_id=user_id,
                    is_active=True,
                    meta_info=meta_info
                )
            )
        except Exception as e:
            # 如果方法不存在或者失败，尝试create_with_user方法
            logger.warning(f"使用create_with_session_id失败: {str(e)}，尝试create_with_user")
            conversation = crud.crud_conversation.create_with_user(
                db,
                obj_in=schemas.ConversationCreate(
                    session_id=session_id,
                    is_active=True,
                    meta_info=meta_info
                ),
                user_id=user_id
            )
            
        is_new = True
        
        # 如果提供了系统提示，添加为系统消息
        if system_prompt:
            system_message = crud.crud_message.create_with_conversation(
                db,
                obj_in=schemas.MessageCreate(
                    content=system_prompt,
                    role=MessageRole.SYSTEM,
                    conversation_id=conversation.id
                ),
                conversation_id=conversation.id,
                user_id=user_id
            )
            
            # 缓存系统消息
            cache_service.cache_message(conversation.session_id, {
                "id": system_message.id,
                "content": system_message.content,
                "role": system_message.role,
                "created_at": system_message.created_at.isoformat(),
                "meta_info": system_message.meta_info or {}
            })
    else:
        # 更新会话的最后活跃时间
        try:
            crud.crud_conversation.update_last_active(db, conversation_id=conversation.id)
        except Exception as e:
            logger.warning(f"更新会话活跃时间出错: {str(e)}")
    
    return conversation, is_new

def normalize_session_id(session_id: str) -> str:
    """
    标准化会话ID格式，将下划线替换为连字符
    
    Args:
        session_id: 原始会话ID
        
    Returns:
        标准化后的会话ID
    """
    return session_id.replace('_', '-')


# ============== 消息处理辅助函数 ==============

async def create_user_message(
    db: Session,
    conversation_id: int,
    user_id: int,
    content: str,
    meta_info: Dict[str, Any] = None
) -> models.Message:
    """
    创建用户消息并缓存
    
    Args:
        db: 数据库会话
        conversation_id: 会话ID
        user_id: 用户ID
        content: 消息内容
        meta_info: 元数据
        
    Returns:
        创建的消息对象
    """
    message_create = schemas.MessageCreate(
        content=content,
        role=MessageRole.USER,
        user_id=user_id,
        meta_info=meta_info or {},
        conversation_id=conversation_id
    )
    
    user_message = crud.crud_message.create_with_conversation(
        db,
        obj_in=message_create,
        conversation_id=conversation_id,
        user_id=user_id
    )
    
    # 更新meta_info以包含数据库ID
    if meta_info is None:
        meta_info = {}
    meta_info['db_message_id'] = user_message.id
    
    # 处理clientMessageId替换
    if 'clientMessageId' in meta_info:
        meta_info['clientMessageId'] = user_message.id
    
    # 缓存用户消息
    try:
        conversation = crud.crud_conversation.get(db, id=conversation_id)
        if conversation:
            cache_service.cache_message(conversation.session_id, {
                "id": user_message.id,
                "content": user_message.content,
                "role": user_message.role,
                "created_at": user_message.created_at.isoformat(),
                "meta_info": user_message.meta_info or {}
            })
    except Exception as e:
        logger.warning(f"缓存用户消息失败: {str(e)}")
    
    return user_message

async def create_assistant_message(
    db: Session,
    conversation_id: int,
    user_id: int,
    content: str,
    meta_info: Dict[str, Any] = None,
    related_message_id: Optional[int] = None
) -> models.Message:
    """
    创建AI助手消息并缓存
    
    Args:
        db: 数据库会话
        conversation_id: 会话ID
        user_id: 用户ID
        content: 消息内容
        meta_info: 元数据
        related_message_id: 关联的用户消息ID
        
    Returns:
        创建的消息对象
    """
    message_create = schemas.MessageCreate(
        content=content,
        role=MessageRole.ASSISTANT,
        user_id=user_id,
        meta_info=meta_info or {},
        conversation_id=conversation_id,
        related_message_id=related_message_id
    )
    
    ai_message = crud.crud_message.create_with_conversation(
        db,
        obj_in=message_create,
        conversation_id=conversation_id,
        user_id=user_id
    )
    
    # 缓存AI消息
    try:
        conversation = crud.crud_conversation.get(db, id=conversation_id)
        if conversation:
            cache_service.cache_message(conversation.session_id, {
                "id": ai_message.id,
                "content": ai_message.content,
                "role": ai_message.role,
                "created_at": ai_message.created_at.isoformat(),
                "meta_info": ai_message.meta_info or {}
            })
    except Exception as e:
        logger.warning(f"缓存AI消息失败: {str(e)}")
    
    return ai_message

def get_last_assistant_meta_info(
    messages: List[models.Message]
) -> Tuple[Dict[str, Any], Optional[Any]]:
    """
    从最近的消息中获取AI助手的meta_info和时间
    
    Args:
        messages: 消息列表
        
    Returns:
        元组：(meta_info字典, 消息时间)
    """
    last_assistant_meta_info = {}
    last_assistant_message_time = None
    
    for msg in messages:
        if msg.role == MessageRole.ASSISTANT and msg.meta_info:
            last_assistant_meta_info = msg.meta_info
            last_assistant_message_time = msg.created_at
            logger.info(f"找到最近的AI消息: id={msg.id}, time={last_assistant_message_time}")
            break
            
    return last_assistant_meta_info, last_assistant_message_time

def prepare_meta_info(
    message: str,
    last_meta_info: Dict[str, Any],
    last_message_time: Any,
    current_time: Any,
    db_message_id: Optional[int] = None
) -> Dict[str, Any]:
    """
    为新会话准备元数据
    
    Args:
        message: 用户消息
        last_meta_info: 上一条AI消息的元数据
        last_message_time: 上一条消息的时间
        current_time: 当前时间
        db_message_id: 数据库消息ID
        
    Returns:
        准备好的元数据字典
    """
    return IntentTransitionManager.prepare_meta_info_for_new_conversation(
        message=message,
        last_meta_info=last_meta_info,
        last_message_time=last_message_time,
        current_time=current_time,
        db_message_id=db_message_id
    )


# ============== 训练计划处理辅助函数 ==============

def ensure_exercise_fields(exercise: Dict[str, Any]) -> None:
    """
    确保训练动作包含所有必要字段
    
    Args:
        exercise: 训练动作字典
    """
    if 'image_name' not in exercise:
        exercise['image_name'] = exercise.get('image_url', '')
    if 'gif_url' not in exercise:
        exercise['gif_url'] = ''
    if 'exercise_type' not in exercise:
        exercise['exercise_type'] = 'weight_reps'
    if 'body_part_id' not in exercise:
        # 默认设置为1（腿部）
        exercise['body_part_id'] = 1

def process_training_plan_data(
    db: Session, 
    plan_id: int, 
    llm_proxy_service: LLMProxyService
) -> Dict[str, Any]:
    """
    处理训练计划数据
    
    Args:
        db: 数据库会话
        plan_id: 训练计划ID
        llm_proxy_service: LLM代理服务
        
    Returns:
        处理后的训练计划数据
    """
    # 创建SQL工具服务
    sql_tool_service = SQLToolService(db)
    # 实例化训练计划服务，传递必要的参数
    training_plan_service = TrainingPlanService(db, llm_proxy_service, sql_tool_service)

    try:
        # 获取训练计划数据
        training_plan_data = training_plan_service.get_enhanced_plan(plan_id)

        # 确保训练计划数据中的每个动作包含所有必要字段
        if "workouts" in training_plan_data:
            # 完整训练计划
            for workout in training_plan_data["workouts"]:
                if "exercises" in workout:
                    for exercise in workout["exercises"]:
                        ensure_exercise_fields(exercise)
            logger.info(f"已确保完整训练计划中所有动作包含必要字段")
        elif "workout_exercises" in training_plan_data:
            # 单日训练计划
            for exercise in training_plan_data["workout_exercises"]:
                ensure_exercise_fields(exercise)
            logger.info(f"已确保单日训练计划中所有动作包含必要字段")
        elif "exercises" in training_plan_data:
            # 兼容旧版单日训练计划
            for exercise in training_plan_data["exercises"]:
                ensure_exercise_fields(exercise)
            logger.info(f"已确保旧版单日训练计划中所有动作包含必要字段")

        # 记录训练计划数据大小
        exercises_count = 0
        if "workouts" in training_plan_data:
            for workout in training_plan_data["workouts"]:
                exercises_count += len(workout.get("exercises", []))
        elif "workout_exercises" in training_plan_data:
            exercises_count = len(training_plan_data["workout_exercises"])
        elif "exercises" in training_plan_data:
            exercises_count = len(training_plan_data["exercises"])
        logger.info(f"处理训练计划数据，包含 {exercises_count} 个训练动作")

        return training_plan_data
    except Exception as e:
        logger.error(f"获取训练计划数据时出错: {str(e)}")
        return {}


# ============== WebSocket辅助函数 ==============

async def send_heartbeats(websocket: WebSocket):
    """
    定期发送心跳包以保持WebSocket连接
    
    Args:
        websocket: WebSocket连接
    """
    try:
        while True:
            await asyncio.sleep(25)  # 每25秒发送一次心跳包
            await websocket.send_json({
                "event": "heartbeat",
                "time": time.time()
            })
    except asyncio.CancelledError:
        # 任务被取消
        pass
    except Exception as e:
        logger.error(f"发送心跳包时出错: {str(e)}")

async def process_websocket_message(
    websocket: WebSocket,
    conversation: models.Conversation,
    data: Dict[str, Any],
    db: Session,
    current_user: models.User,
    llm_proxy_service: LLMProxyService
) -> None:
    """
    处理通过WebSocket接收的消息
    
    Args:
        websocket: WebSocket连接
        conversation: 会话对象
        data: 消息数据
        db: 数据库会话
        current_user: 当前用户
        llm_proxy_service: LLM代理服务
    """
    # 解析消息数据
    message_text = data.get("message", "")
    meta_info = data.get("meta_info", {})
    quick_intent = data.get("quick_intent")
    
    # 验证消息内容
    if not message_text:
        await websocket.send_json({
            "event": "error",
            "message": "消息不能为空"
        })
        return
    
    # 通知客户端开始处理
    await websocket.send_json({
        "event": "processing",
        "time": time.time()
    })
    
    # 先记录用户消息到数据库
    user_message = await create_user_message(
        db, 
        conversation.id,
        current_user.id,
        message_text,
        meta_info
    )
    
    # 告知客户端消息已接收
    await websocket.send_json({
        "event": "message_received",
        "message_id": user_message.id
    })
    
    # 使用ConversationService处理消息流
    conversation_service = ConversationService(db, llm_proxy_service)
    
    # 收集完整响应和元数据
    complete_response = ""
    final_meta_info = {}
    
    try:
        # 流式处理消息
        async for chunk in conversation_service.process_message_stream(
            user_id=current_user.id,
            message=message_text,
            conversation_id=conversation.id,
            meta_info=meta_info,
            quick_intent=quick_intent  # 传递快速意图参数
        ):
            if isinstance(chunk, dict) and "meta_info_update" in chunk:
                # 元数据更新
                await websocket.send_json({
                    "event": "meta_info_update",
                    "data": chunk["meta_info_update"]
                })
                
                # 更新最终元数据
                if not final_meta_info:
                    final_meta_info = {}
                final_meta_info.update(chunk["meta_info_update"])
            else:
                # 文本响应
                if isinstance(chunk, str):
                    complete_response += chunk
                    await websocket.send_text(chunk)
                elif isinstance(chunk, dict):
                    # 如果是字典类型但不含meta_info_update，记录但不处理
                    logger.warning(f"收到未处理的字典类型chunk: {chunk}")
                else:
                    # 其他类型转为字符串
                    try:
                        chunk_str = str(chunk)
                        complete_response += chunk_str
                        await websocket.send_text(chunk_str)
                    except Exception as e:
                        logger.error(f"无法将chunk转换为字符串: {str(e)}")
    except Exception as e:
        logger.error(f"流式处理消息时出错: {str(e)}", exc_info=True)
        await websocket.send_json({
            "event": "error",
            "message": f"处理消息时出错: {str(e)}"
        })
        
        # 设置错误响应
        complete_response = f"抱歉，处理您的消息时出现了错误。错误信息: {str(e)}"
        await websocket.send_text(complete_response)
    
    # 合并元数据
    if meta_info and final_meta_info:
        meta_info.update(final_meta_info)
    elif final_meta_info:
        meta_info = final_meta_info
    
    # 保存AI回复到数据库
    ai_message = await create_assistant_message(
        db,
        conversation.id,
        current_user.id,
        complete_response,
        meta_info,
        user_message.id
    )
    
    # 检查元数据中是否包含训练计划ID并处理
    if (meta_info and "training_params" in meta_info and 
            "related_plan_id" in meta_info["training_params"]):
        plan_id = meta_info["training_params"]["related_plan_id"]
        logger.info(f"响应包含训练计划ID: {plan_id}")
        
        # 获取训练计划数据
        training_plan_data = process_training_plan_data(db, plan_id, llm_proxy_service)
        
        if training_plan_data:
            # 记录训练计划数据大小
            exercises_count = 0
            if "workouts" in training_plan_data:
                for workout in training_plan_data["workouts"]:
                    exercises_count += len(workout.get("exercises", []))
            elif "workout_exercises" in training_plan_data:
                exercises_count = len(training_plan_data["workout_exercises"])
            elif "exercises" in training_plan_data:
                exercises_count = len(training_plan_data["exercises"])
            logger.info(f"发送训练计划数据，包含 {exercises_count} 个训练动作")
            
            # 发送完整的训练计划数据
            await websocket.send_json({
                "event": "training_plan",
                "data": training_plan_data
            })
    
    # 告知客户端响应完成
    await websocket.send_json({
        "event": "response_completed",
        "message_id": ai_message.id
    })
    
    # 记录QA对
    chat_log_service = ChatLogService(db)
    chat_log_service.log_interaction(
        user_id=current_user.id,
        session_id=conversation.session_id,
        question=message_text,
        answer=complete_response
    )

async def process_ai_response(
    db: Session,
    session_id: str,
    conversation_id: int,
    user_id: int,
    user_message_id: int,
    message: str,
    meta_info: Dict[str, Any],
    quick_intent: Optional[str] = None,
    llm_proxy_service: Optional[LLMProxyService] = None
) -> None:
    """
    在后台异步处理AI响应
    
    Args:
        db: 数据库会话
        session_id: 会话ID字符串
        conversation_id: 数据库会话ID
        user_id: 用户ID
        user_message_id: 用户消息ID
        message: 用户消息内容
        meta_info: 元数据
        quick_intent: 快速意图参数
        llm_proxy_service: LLM代理服务
    """
    # 创建新的数据库会话
    from app.db.session import SessionLocal
    async_db = SessionLocal()
    
    try:
        logger.info(f"开始处理AI回复: session_id={session_id}")
        
        # 获取最近的消息历史，用于确保meta_info的连续性
        recent_messages = crud.crud_message.get_conversation_messages_desc(
            async_db, conversation_id=conversation_id, skip=0, limit=10
        )
        
        # 获取最近的AI消息和用户消息的meta_info
        last_assistant_meta_info, _ = get_last_assistant_meta_info(recent_messages)
        
        # 从IntentTransitionManager处理原始意图和强制切换
        from app.services.intent_transition_manager import IntentTransitionManager
        from app.services.meta_info_manager import MetaInfoManager
        
        # 初始化元数据
        response_meta_info = meta_info.copy() if meta_info else {}
        
        # 记录原始意图
        original_intent = None
        if last_assistant_meta_info and "intent" in last_assistant_meta_info:
            original_intent = last_assistant_meta_info["intent"]
            logger.info(f"从最近的AI消息中获取原始意图: {original_intent}")
        
        # 检查强制意图切换
        forced_switch = IntentTransitionManager.check_forced_intent_switch(message)
        
        if forced_switch:
            # 处理强制意图切换
            target_intent, confidence = forced_switch
            response_meta_info = IntentTransitionManager.handle_forced_intent_switch(
                response_meta_info, target_intent, confidence
            )
            logger.info(f"强制意图切换: {original_intent} -> {target_intent}，重置所有参数")
        elif last_assistant_meta_info:
            # 正常合并元数据
            response_meta_info = MetaInfoManager.merge_meta_info(
                response_meta_info, last_assistant_meta_info, original_intent
            )
            
            # 记录参数收集状态
            from app.services.parameter_collection_manager import ParameterCollectionManager
            collection_state, current_param = ParameterCollectionManager.get_current_collection_state(response_meta_info)
            if collection_state == "training_params":
                logger.info(f"参数收集状态: 正在收集训练参数 {current_param}")
            elif collection_state == "user_info":
                logger.info(f"参数收集状态: 正在收集用户信息 {current_param}")
        
        # 记录合并后的meta_info
        logger.info(f"合并后的meta_info状态: {response_meta_info}")
        
        # 创建ConversationService实例
        conversation_service = ConversationService(async_db, llm_proxy_service)
        
        # 初始化变量
        current_response = ""
        ai_message_saved = False
        
        try:
            # 流式处理消息
            async for chunk in conversation_service.process_message_stream(
                user_id=user_id,
                message=message,
                conversation_id=conversation_id,
                meta_info=response_meta_info,
                quick_intent=quick_intent
            ):
                # 处理chunk
                if isinstance(chunk, dict):
                    # 处理消息类型的响应
                    if chunk.get("type") == "message":
                        message_content = chunk.get("content", "")
                        current_response = message_content
                        
                        if "meta_info" in chunk:
                            chunk_meta_info = chunk["meta_info"]
                            response_meta_info = MetaInfoManager.merge_meta_info(
                                response_meta_info, chunk_meta_info, original_intent
                            )
                        continue
                    
                    # 处理元数据更新
                    if "meta_info_update" in chunk:
                        update_meta_info = chunk["meta_info_update"]
                        
                        # 特别处理llm_params和training_params
                        if "llm_params" in update_meta_info:
                            if "llm_params" not in response_meta_info:
                                response_meta_info["llm_params"] = {}
                            for key, value in update_meta_info["llm_params"].items():
                                response_meta_info["llm_params"][key] = value
                            del update_meta_info["llm_params"]
                        
                        # 更新其他元数据
                        for key, value in update_meta_info.items():
                            response_meta_info[key] = value
                        
                        continue
                    
                    # 检查是否已在流中保存AI消息
                    if "ai_message_saved" in chunk and chunk["ai_message_saved"]:
                        ai_message_saved = True
                        continue
                
                # 累积文本响应
                if isinstance(chunk, str):
                    current_response += chunk
                elif not isinstance(chunk, dict):
                    # 尝试转换非字符串、非字典类型
                    try:
                        current_response += str(chunk)
                    except Exception as e:
                        logger.error(f"无法将chunk转换为字符串: {str(e)}")
            
        except Exception as e:
            logger.error(f"处理AI回复流时出错: {str(e)}")
            current_response = f"抱歉，处理您的消息时出现了错误。错误信息: {str(e)}"
        
        # 检查是否在参数收集状态
        is_collecting_params = (
            response_meta_info and (
                response_meta_info.get("collecting_training_params", False) or 
                response_meta_info.get("waiting_for_info") is not None
            )
        )
        
        # 处理空响应
        if not current_response and not is_collecting_params:
            current_response = "抱歉，AI助手没有返回有效回复。请稍后再试。"
            logger.warning("未检测到任何响应，设置默认错误消息")
        
        # 检查训练计划ID并处理
        if (response_meta_info and "training_params" in response_meta_info and
            "related_plan_id" in response_meta_info["training_params"]):
            plan_id = response_meta_info["training_params"]["related_plan_id"]
            
            # 获取完整的训练计划数据
            try:
                plan_data = process_training_plan_data(async_db, plan_id, llm_proxy_service)
                if plan_data:
                    response_meta_info["complete_training_plan"] = plan_data
            except Exception as e:
                logger.error(f"获取训练计划数据时出错: {str(e)}")
        
        # 保存AI回复
        if not ai_message_saved:
            await create_assistant_message(
                async_db,
                conversation_id,
                user_id,
                current_response,
                response_meta_info,
                user_message_id
            )
        else:
            # 如果消息已保存，更新元数据
            latest_ai_message = crud.crud_message.get_latest_assistant_message_by_conversation_id(
                async_db, conversation_id
            )
            if latest_ai_message:
                crud.crud_message.update(
                    async_db,
                    db_obj=latest_ai_message,
                    obj_in={"meta_info": response_meta_info}
                )
        
        # 记录QA对
        try:
            chat_log_service = ChatLogService(async_db)
            await chat_log_service.log_interaction(
                user_id=user_id,
                session_id=session_id,
                question=message,
                answer=current_response
            )
        except Exception as e:
            logger.error(f"记录QA对时出错: {str(e)}")
        
        logger.info(f"AI回复已处理完成: session_id={session_id}")
        
    except Exception as e:
        logger.error(f"处理AI回复时出错: {str(e)}")
    finally:
        # 关闭数据库会话
        async_db.close() 