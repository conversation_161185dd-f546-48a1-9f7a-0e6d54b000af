from typing import Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app import crud, models, schemas
from app.api import deps

router = APIRouter()


@router.get("/", response_model=List[schemas.FoodSearch])
def read_foods(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    name: Optional[str] = None,
    category: Optional[str] = None,
    food_type: Optional[str] = None,
) -> Any:
    """
    获取食品列表，支持名称搜索。返回包含基本营养成分数据（热量、蛋白质比例、脂肪比例、碳水比例）和热度信息。
    结果按热度降序排序，优先展示热度高的食品。
    """
    # 获取数据并按照hot字段降序排序
    foods = crud.food.get_multi(
        db, skip=skip, limit=limit, name=name, category=category, food_type=food_type,
        sort_by="hot", sort_desc=True
    )
    
    # 确保营养数据正确映射到响应模型
    result = []
    for food in foods:
        food_data = {
            "id": food.id,
            "name": food.name,
            "code": food.code,
            "category": food.category,
            "food_type": food.food_type,
            "thumb_image_url": food.thumb_image_url,
            "calory": food.nutritional_profile.calory if food.nutritional_profile else None,
            "protein_fraction": food.nutritional_profile.protein_fraction if food.nutritional_profile else None,
            "fat_fraction": food.nutritional_profile.fat_fraction if food.nutritional_profile else None,
            "carb_fraction": food.nutritional_profile.carb_fraction if food.nutritional_profile else None,
            "hot": food.hot if hasattr(food, "hot") else 0
        }
        result.append(schemas.FoodSearch(**food_data))
    
    return result


@router.get("/search", response_model=List[schemas.FoodSearch])
def search_foods_by_name(
    name: str = Query(..., description="食品名称搜索关键词"),
    limit: int = Query(10, description="返回结果数量上限"),
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    基于名称搜索食品，使用高级相似度匹配算法。返回包含营养成分数据的食品对象，包括热度信息。
    结果按热度降序排序，优先展示热度高的食品。
    """
    foods = crud.food.search_by_name(db=db, name=name, limit=limit, sort_by_hot=True)
    
    # 手动构建返回数据，与read_foods保持一致
    result = []
    for food in foods:
        food_data = {
            "id": food.id,
            "name": food.name,
            "code": food.code,
            "category": food.category,
            "food_type": food.food_type,
            "thumb_image_url": food.thumb_image_url,
            "calory": food.nutritional_profile.calory if food.nutritional_profile else None,
            "protein_fraction": food.nutritional_profile.protein_fraction if food.nutritional_profile else None,
            "fat_fraction": food.nutritional_profile.fat_fraction if food.nutritional_profile else None,
            "carb_fraction": food.nutritional_profile.carb_fraction if food.nutritional_profile else None,
            "hot": food.hot if hasattr(food, "hot") else 0
        }
        result.append(schemas.FoodSearch(**food_data))
    
    return result


@router.get("/categories", response_model=List[str])
def read_food_categories(
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    获取所有食品类别列表。
    """
    return crud.food.get_all_categories(db)


@router.get("/types", response_model=List[str])
def read_food_types(
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    获取所有食品类型列表。
    """
    return crud.food.get_all_types(db)


@router.get("/{food_id}", response_model=schemas.Food)
def read_food(
    *,
    db: Session = Depends(deps.get_db),
    food_id: int,
) -> Any:
    """
    通过ID获取食品详情。
    """
    food = crud.food.get(db=db, id=food_id)
    if not food:
        raise HTTPException(status_code=404, detail="食品不存在")
    return food


@router.get("/code/{code}", response_model=schemas.Food)
def read_food_by_code(
    *,
    db: Session = Depends(deps.get_db),
    code: str,
) -> Any:
    """
    通过编码获取食品详情。
    """
    food = crud.food.get_by_code(db=db, code=code)
    if not food:
        raise HTTPException(status_code=404, detail="食品不存在")
    return food


@router.post("/", response_model=schemas.Food)
def create_food(
    *,
    db: Session = Depends(deps.get_db),
    food_in: schemas.FoodCreate,
    current_user: models.User = Depends(deps.get_current_active_superuser),
) -> Any:
    """
    创建新食品（仅管理员）。
    """
    # 检查食品编码是否已存在
    food = crud.food.get_by_code(db=db, code=food_in.code)
    if food:
        raise HTTPException(
            status_code=400,
            detail=f"编码为 {food_in.code} 的食品已存在",
        )
    return crud.food.create(db=db, obj_in=food_in)


@router.put("/{food_id}", response_model=schemas.Food)
def update_food(
    *,
    db: Session = Depends(deps.get_db),
    food_id: int,
    food_in: schemas.FoodUpdate,
    current_user: models.User = Depends(deps.get_current_active_superuser),
) -> Any:
    """
    更新食品信息（仅管理员）。
    """
    food = crud.food.get(db=db, id=food_id)
    if not food:
        raise HTTPException(status_code=404, detail="食品不存在")
    
    # 检查是否可以修改
    if not food.can_revise:
        raise HTTPException(status_code=403, detail="该食品不允许修改")
        
    return crud.food.update(db=db, db_obj=food, obj_in=food_in)


@router.delete("/{food_id}", response_model=schemas.Food)
def delete_food(
    *,
    db: Session = Depends(deps.get_db),
    food_id: int,
    current_user: models.User = Depends(deps.get_current_active_superuser),
) -> Any:
    """
    删除食品（仅管理员）。
    """
    food = crud.food.get(db=db, id=food_id)
    if not food:
        raise HTTPException(status_code=404, detail="食品不存在")
    return crud.food.remove(db=db, id=food_id)


@router.post("/nutrients", response_model=schemas.NutrientsResponse)
def get_nutrients(
    *,
    db: Session = Depends(deps.get_db),
    nutrient_request: schemas.NutrientRequest,
) -> Any:
    """
    获取特定食品的指定营养素数据。
    
    接收食品ID和营养素名称列表，返回对应的营养素详细数据。
    每个营养素数据包含：value（数值）、unit（单位）、unit_name（单位名称）、
    precision（精度）、nrv（营养参考值）和category（分类）。
    """
    # 验证食品是否存在
    food = crud.food.get(db=db, id=nutrient_request.food_id)
    if not food:
        raise HTTPException(status_code=404, detail="食品不存在")
    
    # 获取指定营养素数据
    nutrients = crud.food.get_specific_nutrients(
        db=db, 
        food_id=nutrient_request.food_id,
        nutrient_names=nutrient_request.nutrient_names
    )
    
    # 构建响应
    return {
        "food_id": nutrient_request.food_id,
        "nutrients": nutrients
    } 