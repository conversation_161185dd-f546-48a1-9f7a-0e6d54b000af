from fastapi import APIRouter, Depends, HTTPException, File, UploadFile, Form, BackgroundTasks, Request
from sqlalchemy.orm import Session
from typing import Any, Optional, Dict
from datetime import date
import logging
import time
import json
import re

from app.api import deps
from app.models import User
from app.schemas.food_recognition import FoodRecognitionResponse, Base64ImageRequest
from app.services.food_recognition_service import FoodRecognitionService
from app.services.langgraph_service import LangGraphService
from app.schemas.meal import MealType

router = APIRouter()
logger = logging.getLogger(__name__)

def extract_filename(url: str) -> str:
    """从URL中提取文件名"""
    if not url:
        return ""
    match = re.search(r'/([^/]+)$', url)
    if match:
        return match.group(1)
    return url

@router.post("/analyze-for-chat", response_model=FoodRecognitionResponse)
async def analyze_food_image_for_chat(
    background_tasks: BackgroundTasks,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    image: UploadFile = File(...),
    meal_type: str = Form(...),
    meal_date: Optional[str] = Form(None),
    session_id: Optional[str] = Form(None),
    optimize: bool = Form(True)
) -> Any:
    """
    分析食物图片并将结果集成到AI聊天流程中。

    - **image**: 食物图片文件
    - **meal_type**: 餐食类型
    - **meal_date**: 可选，餐食日期
    - **session_id**: 可选，聊天会话ID
    - **optimize**: 是否优化图片
    """
    logger.info(f"收到食物识别请求(AI聊天集成)：meal_type={meal_type}, session_id={session_id}")

    # 验证餐食类型
    valid_meal_types = ["breakfast", "lunch", "dinner", "snack"]
    if meal_type not in valid_meal_types:
        raise HTTPException(
            status_code=422,
            detail=f"无效的餐食类型。必须是以下之一: {', '.join(valid_meal_types)}"
        )

    # 转换为枚举类型
    meal_type_enum = getattr(MealType, meal_type.upper())

    # 处理日期
    if not meal_date:
        parsed_date = date.today()
    else:
        try:
            parsed_date = date.fromisoformat(meal_date)
        except ValueError:
            raise HTTPException(
                status_code=422,
                detail="无效的日期格式。必须是YYYY-MM-DD"
            )

    # 读取图片内容
    try:
        contents = await image.read()
        if not contents or len(contents) == 0:
            raise HTTPException(
                status_code=422,
                detail="上传的图像文件为空"
            )
    except Exception as e:
        logger.error(f"读取图像失败: {str(e)}")
        raise HTTPException(
            status_code=422,
            detail=f"读取图像失败: {str(e)}"
        )

    # 调用食物识别服务
    recognition_result = await FoodRecognitionService.analyze_image(
        db=db,
        user_id=current_user.id,
        image_data=contents,
        meal_type=meal_type_enum,
        meal_date=parsed_date,
        optimize=optimize
    )

    # 修改image_url和thumb_image_url，只保留文件名
    if "image_url" in recognition_result and recognition_result["image_url"]:
        recognition_result["image_url"] = extract_filename(recognition_result["image_url"])

    if "thumb_image_url" in recognition_result and recognition_result["thumb_image_url"]:
        recognition_result["thumb_image_url"] = extract_filename(recognition_result["thumb_image_url"])

    # 如果提供了会话ID，将结果集成到LangGraph流程中
    if session_id:
        # 准备图像分析结果
        image_analysis_result = {
            "is_food": True,
            "food_items": recognition_result.get("food_items", []),
            "nutrition_info": recognition_result.get("nutrition_totals", {}),
            "health_recommendation": recognition_result.get("recognition_result", {}).get("health_recommendation", ""),
            "meal_type": meal_type,
            "meal_date": str(parsed_date)
        }

        # 创建元信息
        meta_info = {
            "image_analysis_result": image_analysis_result,
            "image_analysis_timestamp": time.time()
        }

        # 初始化LangGraph服务
        langgraph_service = LangGraphService(db)

        # 发送系统消息到LangGraph流程
        system_message = "用户上传了一张食物图片，请分析图片中的食物并提供健康建议。"

        # 异步处理消息
        background_tasks.add_task(
            langgraph_service.process_message,
            message=system_message,
            session_id=session_id,
            user_id=current_user.id,
            meta_info=meta_info
        )

    # 确保返回结果包含success字段
    if "success" not in recognition_result:
        recognition_result["success"] = True

    return recognition_result

@router.post("/analyze-base64-for-chat", response_model=FoodRecognitionResponse)
async def analyze_base64_food_image_for_chat(
    background_tasks: BackgroundTasks,
    request: Request,
    meal_type: MealType,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    session_id: Optional[str] = None,
    meal_date: Optional[date] = None
) -> Any:
    """
    使用base64编码的图片分析食物，并将结果集成到AI聊天流程中。

    - **request**: 包含base64编码图像的请求体
    - **meal_type**: 餐食类型
    - **session_id**: 可选，聊天会话ID
    - **meal_date**: 可选，餐食日期
    """
    logger.info(f"收到Base64食物识别请求(AI聊天集成)：meal_type={meal_type}, session_id={session_id}")

    if not meal_date:
        meal_date = date.today()
        logger.info(f"未提供日期，使用今天: {meal_date}")

    try:
        # 获取请求体
        body = await request.json()

        # 验证base64图像数据
        if "base64_image" not in body:
            logger.error("缺少base64图像数据")
            raise HTTPException(
                status_code=422,
                detail="请求必须包含base64_image字段"
            )

        base64_image = body["base64_image"]
        optimize = body.get("optimize", True)

        if not base64_image.strip().startswith(("data:image/", "iVBORw0K", "/9j/")):
            logger.error("提供的base64数据不是有效的图像格式")
            raise HTTPException(
                status_code=422,
                detail="提供的base64数据不是有效的图像格式"
            )

        # 调用食物识别服务处理base64图像
        recognition_result = await FoodRecognitionService.analyze_base64_image(
            db=db,
            user_id=current_user.id,
            base64_image=base64_image,
            meal_type=meal_type,
            meal_date=meal_date,
            optimize=optimize
        )

        # 修改image_url和thumb_image_url，只保留文件名
        if "image_url" in recognition_result and recognition_result["image_url"]:
            recognition_result["image_url"] = extract_filename(recognition_result["image_url"])

        if "thumb_image_url" in recognition_result and recognition_result["thumb_image_url"]:
            recognition_result["thumb_image_url"] = extract_filename(recognition_result["thumb_image_url"])

        # 如果提供了会话ID，将结果集成到LangGraph流程中
        if session_id:
            # 准备图像分析结果
            image_analysis_result = {
                "is_food": True,
                "food_items": recognition_result.get("food_items", []),
                "nutrition_info": recognition_result.get("nutrition_totals", {}),
                "health_recommendation": recognition_result.get("recognition_result", {}).get("health_recommendation", ""),
                "meal_type": meal_type.value,
                "meal_date": str(meal_date)
            }

            # 创建元信息
            meta_info = {
                "image_analysis_result": image_analysis_result,
                "image_analysis_timestamp": time.time()
            }

            # 初始化LangGraph服务
            langgraph_service = LangGraphService(db)

            # 发送系统消息到LangGraph流程
            system_message = "用户上传了一张食物图片，请分析图片中的食物并提供健康建议。"

            # 异步处理消息
            background_tasks.add_task(
                langgraph_service.process_message,
                message=system_message,
                session_id=session_id,
                user_id=current_user.id,
                meta_info=meta_info
            )

        # 确保返回结果包含success字段
        if "success" not in recognition_result:
            recognition_result["success"] = True

        return recognition_result

    except json.JSONDecodeError:
        logger.error("无效的JSON请求体")
        raise HTTPException(
            status_code=422,
            detail="无效的JSON请求体"
        )
    except Exception as e:
        logger.error(f"处理Base64图像失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"处理Base64图像失败: {str(e)}"
        )
