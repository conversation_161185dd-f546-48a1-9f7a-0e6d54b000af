from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session
from datetime import datetime
import logging

from app.db.session import get_db
from app.api.deps import get_current_user
from app.models.user import User
from app.models.team.enums import <PERSON><PERSON><PERSON>, TeamStatus, MembershipStatus, ClientStatus, InvitationStatus
from app.schemas.team.team import TeamCreate, TeamUpdate, TeamResponse, TeamDetail, TeamListResponse
from app.schemas.team.membership import MembershipCreate, MembershipUpdate, MembershipResponse, TeamMemberResponse
from app.schemas.team.invitation import InvitationCreate, InvitationResponse, InvitationListResponse
from app.schemas.team.client import ClientAssignment, ClientTransfer, ClientRelationResponse, ClientListResponse, ClientDetailResponse
from app.schemas.team.template import TemplateCreate, TemplateResponse, TemplateListResponse
from app.schemas.team.training import (
    <PERSON><PERSON><PERSON>lan<PERSON><PERSON>, ClientPlanUpdate, ClientTrainingPlanResponse,
    SessionResponse, SetRecordCreate, SessionFeedback
)
from app.services.team_service import TeamService, TeamServiceException, TeamNotFoundException, InsufficientPermissionException
from app.services.team_member_service import TeamMemberService, MembershipNotFoundException, InvitationNotFoundException
from app.services.team_client_service import TeamClientService, ClientRelationNotFoundException
from app.services.team_training_service import TeamTrainingService, TrainingPlanNotFoundException, SessionNotFoundException, TemplateNotFoundException, ScheduleConflictException
from app.services.team_stats_service import TeamStatsService

router = APIRouter()
logger = logging.getLogger("fitness-coach-api")

# 团队管理接口
@router.post("/teams/", response_model=TeamResponse)
async def create_team(
    team_data: TeamCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建新团队
    
    用户可以创建一个新的团队，并自动成为该团队的所有者。
    """
    logger.info(f"用户 {current_user.id} 创建团队: {team_data.name}")
    team_service = TeamService(db)
    try:
        return await team_service.create_team(current_user, team_data)
    except TeamServiceException as e:
        logger.error(f"创建团队失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/teams/{team_id}", response_model=TeamDetail)
async def get_team_detail(
    team_id: int = Path(..., description="团队ID"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取团队详情
    
    获取团队的详细信息，包括基本信息、设置和统计数据。
    """
    logger.info(f"用户 {current_user.id} 获取团队 {team_id} 详情")
    team_service = TeamService(db)
    try:
        return await team_service.get_team_detail(team_id, current_user.id)
    except TeamNotFoundException:
        logger.error(f"团队不存在: {team_id}")
        raise HTTPException(status_code=404, detail="Team not found")
    except InsufficientPermissionException:
        logger.error(f"用户 {current_user.id} 没有权限查看团队 {team_id}")
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        logger.error(f"获取团队详情失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.put("/teams/{team_id}", response_model=TeamResponse)
async def update_team_info(
    team_id: int = Path(..., description="团队ID"),
    team_data: TeamUpdate = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新团队信息
    
    团队所有者或管理员可以更新团队的名称、描述、状态和设置。
    """
    logger.info(f"用户 {current_user.id} 更新团队 {team_id}")
    team_service = TeamService(db)
    try:
        return await team_service.update_team(team_id, team_data, current_user)
    except TeamNotFoundException:
        logger.error(f"团队不存在: {team_id}")
        raise HTTPException(status_code=404, detail="Team not found")
    except InsufficientPermissionException:
        logger.error(f"用户 {current_user.id} 没有权限更新团队 {team_id}")
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        logger.error(f"更新团队失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/teams/", response_model=List[TeamListResponse])
async def list_user_teams(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户相关的团队列表
    
    获取当前用户加入的所有团队列表，包括自己创建的和被邀请加入的团队。
    """
    logger.info(f"用户 {current_user.id} 获取团队列表")
    team_service = TeamService(db)
    try:
        return await team_service.get_user_teams(current_user.id)
    except TeamServiceException as e:
        logger.error(f"获取用户团队列表失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/teams/{team_id}", status_code=204)
async def delete_team(
    team_id: int = Path(..., description="团队ID"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除团队
    
    团队所有者可以删除团队，此操作将团队标记为已删除状态，而不是真正删除数据。
    """
    logger.info(f"用户 {current_user.id} 删除团队 {team_id}")
    team_service = TeamService(db)
    try:
        await team_service.delete_team(team_id, current_user)
    except TeamNotFoundException:
        logger.error(f"团队不存在: {team_id}")
        raise HTTPException(status_code=404, detail="Team not found")
    except InsufficientPermissionException:
        logger.error(f"用户 {current_user.id} 没有权限删除团队 {team_id}")
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        logger.error(f"删除团队失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

# 成员管理接口
@router.post("/teams/{team_id}/members/", response_model=MembershipResponse)
async def add_team_member(
    team_id: int = Path(..., description="团队ID"),
    member_data: MembershipCreate = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """添加团队成员
    
    团队所有者或管理员可以直接添加成员到团队。
    """
    logger.info(f"用户 {current_user.id} 添加成员 {member_data.user_id} 到团队 {team_id}")
    member_service = TeamMemberService(db)
    try:
        return await member_service.add_team_member(team_id, member_data, current_user)
    except InsufficientPermissionException:
        logger.error(f"用户 {current_user.id} 没有权限添加成员到团队 {team_id}")
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        logger.error(f"添加团队成员失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.put("/teams/{team_id}/members/{user_id}", response_model=MembershipResponse)
async def update_member_role(
    team_id: int = Path(..., description="团队ID"),
    user_id: int = Path(..., description="用户ID"),
    role_data: MembershipUpdate = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新成员角色
    
    团队所有者或管理员可以更新团队成员的角色和状态。
    """
    logger.info(f"用户 {current_user.id} 更新团队 {team_id} 成员 {user_id} 的角色")
    member_service = TeamMemberService(db)
    try:
        return await member_service.update_member_role(team_id, user_id, role_data, current_user)
    except MembershipNotFoundException:
        logger.error(f"成员关系不存在: 团队 {team_id}, 用户 {user_id}")
        raise HTTPException(status_code=404, detail="Membership not found")
    except InsufficientPermissionException:
        logger.error(f"用户 {current_user.id} 没有权限更新成员角色")
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        logger.error(f"更新成员角色失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/teams/{team_id}/members/{user_id}", status_code=204)
async def remove_team_member(
    team_id: int = Path(..., description="团队ID"),
    user_id: int = Path(..., description="用户ID"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """移除团队成员
    
    团队所有者或管理员可以移除团队成员。所有者不能被移除。
    """
    logger.info(f"用户 {current_user.id} 从团队 {team_id} 移除成员 {user_id}")
    member_service = TeamMemberService(db)
    try:
        await member_service.remove_team_member(team_id, user_id, current_user)
    except MembershipNotFoundException:
        logger.error(f"成员关系不存在: 团队 {team_id}, 用户 {user_id}")
        raise HTTPException(status_code=404, detail="Membership not found")
    except InsufficientPermissionException:
        logger.error(f"用户 {current_user.id} 没有权限移除团队成员")
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        logger.error(f"移除团队成员失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/teams/{team_id}/members", response_model=List[TeamMemberResponse])
async def list_team_members(
    team_id: int = Path(..., description="团队ID"),
    role: Optional[str] = Query(None, description="角色过滤"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取团队成员列表
    
    获取团队的所有成员列表，可以按角色过滤。
    """
    logger.info(f"用户 {current_user.id} 获取团队 {team_id} 成员列表")
    member_service = TeamMemberService(db)
    try:
        return await member_service.get_team_members(team_id, role, current_user)
    except TeamServiceException as e:
        logger.error(f"获取团队成员列表失败: {str(e)}")
        raise HTTPException(status_code=403, detail=str(e))

# 会员管理接口
@router.post("/teams/{team_id}/clients/", response_model=ClientRelationResponse)
async def assign_team_client(
    team_id: int = Path(..., description="团队ID"),
    client_data: ClientAssignment = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """分配会员
    
    团队所有者、管理员或教练可以分配会员到教练。
    """
    logger.info(f"用户 {current_user.id} 分配会员 {client_data.client_id} 到教练 {client_data.coach_id}")
    client_service = TeamClientService(db)
    try:
        return await client_service.assign_client(team_id, client_data, current_user)
    except InsufficientPermissionException:
        logger.error(f"用户 {current_user.id} 没有权限分配会员")
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        logger.error(f"分配会员失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/teams/{team_id}/clients/{client_relation_id}/transfer", response_model=ClientRelationResponse)
async def transfer_team_client(
    team_id: int = Path(..., description="团队ID"),
    client_relation_id: int = Path(..., description="会员关系ID"),
    transfer_data: ClientTransfer = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """转移会员
    
    团队所有者或管理员可以将会员从一个教练转移到另一个教练。
    """
    logger.info(f"用户 {current_user.id} 转移会员关系 {client_relation_id} 到教练 {transfer_data.new_coach_id}")
    client_service = TeamClientService(db)
    try:
        return await client_service.transfer_client(client_relation_id, transfer_data, current_user)
    except ClientRelationNotFoundException:
        logger.error(f"会员关系不存在: {client_relation_id}")
        raise HTTPException(status_code=404, detail="Client relation not found")
    except InsufficientPermissionException:
        logger.error(f"用户 {current_user.id} 没有权限转移会员")
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        logger.error(f"转移会员失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/teams/{team_id}/clients", response_model=List[ClientListResponse])
async def list_team_clients(
    team_id: int = Path(..., description="团队ID"),
    status: Optional[str] = Query(None, description="状态过滤"),
    coach_id: Optional[int] = Query(None, description="教练ID过滤"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取团队会员列表
    
    获取团队的所有会员列表，可以按状态和教练ID过滤。
    """
    logger.info(f"用户 {current_user.id} 获取团队 {team_id} 会员列表")
    client_service = TeamClientService(db)
    try:
        return await client_service.get_team_clients(team_id, status, coach_id, current_user)
    except TeamServiceException as e:
        logger.error(f"获取团队会员列表失败: {str(e)}")
        raise HTTPException(status_code=403, detail=str(e))

@router.get("/teams/{team_id}/clients/{client_relation_id}", response_model=ClientDetailResponse)
async def get_client_detail(
    team_id: int = Path(..., description="团队ID"),
    client_relation_id: int = Path(..., description="会员关系ID"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取会员详细信息
    
    获取会员的详细信息，包含会员基本信息、教练信息、统计数据和转移历史。
    """
    logger.info(f"用户 {current_user.id} 获取会员关系 {client_relation_id} 详情")
    client_service = TeamClientService(db)
    try:
        return await client_service.get_client_detail(client_relation_id, current_user)
    except ClientRelationNotFoundException:
        logger.error(f"会员关系不存在: {client_relation_id}")
        raise HTTPException(status_code=404, detail="Client relation not found")
    except TeamServiceException as e:
        logger.error(f"获取会员详情失败: {str(e)}")
        raise HTTPException(status_code=403, detail=str(e))

@router.put("/teams/{team_id}/clients/{client_relation_id}/deactivate", response_model=ClientRelationResponse)
async def deactivate_client(
    team_id: int = Path(..., description="团队ID"),
    client_relation_id: int = Path(..., description="会员关系ID"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """停用会员
    
    团队所有者、管理员或负责的教练可以停用会员。
    """
    logger.info(f"用户 {current_user.id} 停用会员关系 {client_relation_id}")
    client_service = TeamClientService(db)
    try:
        return await client_service.deactivate_client(client_relation_id, current_user)
    except ClientRelationNotFoundException:
        logger.error(f"会员关系不存在: {client_relation_id}")
        raise HTTPException(status_code=404, detail="Client relation not found")
    except InsufficientPermissionException:
        logger.error(f"用户 {current_user.id} 没有权限停用会员")
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        logger.error(f"停用会员失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.put("/teams/{team_id}/clients/{client_relation_id}/reactivate", response_model=ClientRelationResponse)
async def reactivate_client(
    team_id: int = Path(..., description="团队ID"),
    client_relation_id: int = Path(..., description="会员关系ID"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """重新激活会员
    
    团队所有者、管理员或负责的教练可以重新激活已停用的会员。
    """
    logger.info(f"用户 {current_user.id} 重新激活会员关系 {client_relation_id}")
    client_service = TeamClientService(db)
    try:
        return await client_service.reactivate_client(client_relation_id, current_user)
    except ClientRelationNotFoundException:
        logger.error(f"会员关系不存在: {client_relation_id}")
        raise HTTPException(status_code=404, detail="Client relation not found")
    except InsufficientPermissionException:
        logger.error(f"用户 {current_user.id} 没有权限重新激活会员")
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        logger.error(f"重新激活会员失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

# 邀请管理接口
@router.post("/teams/{team_id}/invitations/", response_model=InvitationResponse)
async def create_team_invitation(
    team_id: int = Path(..., description="团队ID"),
    invitation_data: InvitationCreate = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建团队邀请
    
    团队所有者或管理员可以创建邀请，邀请用户加入团队。
    """
    logger.info(f"用户 {current_user.id} 创建邀请: 邀请 {invitation_data.invitee_id} 加入团队 {team_id}")
    member_service = TeamMemberService(db)
    try:
        return await member_service.create_invitation(team_id, invitation_data, current_user)
    except InsufficientPermissionException:
        logger.error(f"用户 {current_user.id} 没有权限创建邀请")
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        logger.error(f"创建邀请失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.put("/teams/invitations/{invitation_id}/accept", response_model=Dict[str, Any])
async def accept_team_invitation(
    invitation_id: int = Path(..., description="邀请ID"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """接受团队邀请
    
    用户可以接受发送给自己的邀请，接受后成为团队成员。
    """
    logger.info(f"用户 {current_user.id} 接受邀请 {invitation_id}")
    member_service = TeamMemberService(db)
    try:
        result = await member_service.accept_invitation(invitation_id, current_user)
        return {"team_id": result.team_id, "role": result.role, "status": "accepted"}
    except InvitationNotFoundException:
        logger.error(f"邀请不存在: {invitation_id}")
        raise HTTPException(status_code=404, detail="Invitation not found")
    except TeamServiceException as e:
        logger.error(f"接受邀请失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.put("/teams/invitations/{invitation_id}/reject", response_model=Dict[str, Any])
async def reject_team_invitation(
    invitation_id: int = Path(..., description="邀请ID"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """拒绝团队邀请
    
    用户可以拒绝发送给自己的邀请。
    """
    logger.info(f"用户 {current_user.id} 拒绝邀请 {invitation_id}")
    member_service = TeamMemberService(db)
    try:
        await member_service.reject_invitation(invitation_id, current_user)
        return {"status": "rejected"}
    except InvitationNotFoundException:
        logger.error(f"邀请不存在: {invitation_id}")
        raise HTTPException(status_code=404, detail="Invitation not found")
    except TeamServiceException as e:
        logger.error(f"拒绝邀请失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/teams/{team_id}/invitations", response_model=List[InvitationListResponse])
async def list_team_invitations(
    team_id: int = Path(..., description="团队ID"),
    status: Optional[str] = Query(None, description="状态过滤"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取团队发出的邀请列表
    
    获取团队发出的所有邀请列表，可以按状态过滤。
    """
    logger.info(f"用户 {current_user.id} 获取团队 {team_id} 邀请列表")
    member_service = TeamMemberService(db)
    try:
        return await member_service.get_team_invitations(team_id, status, current_user)
    except InsufficientPermissionException:
        logger.error(f"用户 {current_user.id} 没有权限查看团队邀请")
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        logger.error(f"获取团队邀请列表失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/user/invitations", response_model=List[InvitationResponse])
async def list_user_invitations(
    status: Optional[str] = Query(None, description="状态过滤"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户收到的邀请列表
    
    获取当前用户收到的所有邀请列表，可以按状态过滤。
    """
    logger.info(f"用户 {current_user.id} 获取个人邀请列表")
    member_service = TeamMemberService(db)
    try:
        return await member_service.get_user_invitations(current_user.id, status)
    except TeamServiceException as e:
        logger.error(f"获取用户邀请列表失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/teams/invitations/{invitation_id}", status_code=204)
async def cancel_invitation(
    invitation_id: int = Path(..., description="邀请ID"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """取消邀请
    
    团队所有者或管理员可以取消已发出但尚未被接受的邀请。
    """
    logger.info(f"用户 {current_user.id} 取消邀请 {invitation_id}")
    member_service = TeamMemberService(db)
    try:
        await member_service.cancel_invitation(invitation_id, current_user)
    except InvitationNotFoundException:
        logger.error(f"邀请不存在: {invitation_id}")
        raise HTTPException(status_code=404, detail="Invitation not found")
    except InsufficientPermissionException:
        logger.error(f"用户 {current_user.id} 没有权限取消邀请")
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        logger.error(f"取消邀请失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

# 训练计划模板接口
@router.post("/teams/{team_id}/templates/", response_model=TemplateResponse)
async def create_plan_template(
    team_id: int = Path(..., description="团队ID"),
    template_data: TemplateCreate = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建训练计划模板
    
    团队所有者、管理员或教练可以创建训练计划模板，用于快速分配训练计划给会员。
    """
    logger.info(f"用户 {current_user.id} 创建团队 {team_id} 的训练计划模板")
    training_service = TeamTrainingService(db)
    try:
        return await training_service.create_template(team_id, template_data, current_user)
    except InsufficientPermissionException:
        logger.error(f"用户 {current_user.id} 没有权限创建模板")
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        logger.error(f"创建训练计划模板失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/teams/{team_id}/templates/", response_model=List[TemplateListResponse])
async def list_plan_templates(
    team_id: int = Path(..., description="团队ID"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取团队训练计划模板列表
    
    获取团队的所有训练计划模板列表。
    """
    logger.info(f"用户 {current_user.id} 获取团队 {team_id} 的训练计划模板列表")
    training_service = TeamTrainingService(db)
    try:
        return await training_service.get_team_templates(team_id, current_user)
    except TeamServiceException as e:
        logger.error(f"获取训练计划模板列表失败: {str(e)}")
        raise HTTPException(status_code=403, detail=str(e))

# 会员训练计划接口
@router.post("/clients/{client_relation_id}/training-plans/", response_model=ClientTrainingPlanResponse)
async def create_client_training_plan(
    client_relation_id: int = Path(..., description="会员关系ID"),
    plan_data: ClientPlanCreate = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建会员训练计划
    
    教练可以为自己负责的会员创建训练计划。
    """
    logger.info(f"用户 {current_user.id} 为会员关系 {client_relation_id} 创建训练计划")
    training_service = TeamTrainingService(db)
    try:
        return await training_service.create_client_plan(client_relation_id, plan_data, current_user)
    except ClientRelationNotFoundException:
        logger.error(f"会员关系不存在: {client_relation_id}")
        raise HTTPException(status_code=404, detail="Client relation not found")
    except TemplateNotFoundException:
        logger.error(f"训练计划模板不存在: {plan_data.training_plan_id}")
        raise HTTPException(status_code=404, detail="Template not found")
    except InsufficientPermissionException:
        logger.error(f"用户 {current_user.id} 没有权限创建训练计划")
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except ScheduleConflictException:
        logger.error(f"创建训练计划失败: 时间冲突")
        raise HTTPException(status_code=409, detail="Schedule conflict")
    except TeamServiceException as e:
        logger.error(f"创建会员训练计划失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/clients/{client_relation_id}/training-plans/", response_model=List[Dict[str, Any]])
async def list_client_training_plans(
    client_relation_id: int = Path(..., description="会员关系ID"),
    status: Optional[str] = Query(None, description="状态过滤"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取会员训练计划列表
    
    获取会员的所有训练计划列表，可以按状态过滤。
    """
    logger.info(f"用户 {current_user.id} 获取会员关系 {client_relation_id} 的训练计划列表")
    training_service = TeamTrainingService(db)
    try:
        return await training_service.get_client_plans(client_relation_id, status, current_user)
    except ClientRelationNotFoundException:
        logger.error(f"会员关系不存在: {client_relation_id}")
        raise HTTPException(status_code=404, detail="Client relation not found")
    except TeamServiceException as e:
        logger.error(f"获取会员训练计划列表失败: {str(e)}")
        raise HTTPException(status_code=403, detail=str(e))

@router.put("/clients/training-plans/{plan_id}", response_model=ClientTrainingPlanResponse)
async def update_client_training_plan(
    plan_id: int = Path(..., description="计划ID"),
    update_data: ClientPlanUpdate = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新会员训练计划
    
    教练可以更新会员的训练计划。
    """
    logger.info(f"用户 {current_user.id} 更新训练计划 {plan_id}")
    training_service = TeamTrainingService(db)
    try:
        return await training_service.update_client_plan(plan_id, update_data, current_user)
    except TrainingPlanNotFoundException:
        logger.error(f"训练计划不存在: {plan_id}")
        raise HTTPException(status_code=404, detail="Training plan not found")
    except InsufficientPermissionException:
        logger.error(f"用户 {current_user.id} 没有权限更新训练计划")
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except ScheduleConflictException:
        logger.error(f"更新训练计划失败: 时间冲突")
        raise HTTPException(status_code=409, detail="Schedule conflict")
    except TeamServiceException as e:
        logger.error(f"更新会员训练计划失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

# 训练课程接口
@router.get("/training-plans/{plan_id}/sessions", response_model=List[Dict[str, Any]])
async def list_training_sessions(
    plan_id: int = Path(..., description="计划ID"),
    status: Optional[str] = Query(None, description="状态过滤"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取训练课程列表
    
    获取训练计划的所有课程列表，可以按状态过滤。
    """
    logger.info(f"用户 {current_user.id} 获取训练计划 {plan_id} 的课程列表")
    training_service = TeamTrainingService(db)
    try:
        return await training_service.get_plan_sessions(plan_id, status, current_user)
    except TrainingPlanNotFoundException:
        logger.error(f"训练计划不存在: {plan_id}")
        raise HTTPException(status_code=404, detail="Training plan not found")
    except TeamServiceException as e:
        logger.error(f"获取训练课程列表失败: {str(e)}")
        raise HTTPException(status_code=403, detail=str(e))

@router.post("/sessions/{session_id}/start", response_model=SessionResponse)
async def start_training_session(
    session_id: int = Path(..., description="课程ID"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """开始训练课程
    
    会员或教练可以开始训练课程。
    """
    logger.info(f"用户 {current_user.id} 开始训练课程 {session_id}")
    training_service = TeamTrainingService(db)
    try:
        return await training_service.start_session(session_id, current_user)
    except SessionNotFoundException:
        logger.error(f"训练课程不存在: {session_id}")
        raise HTTPException(status_code=404, detail="Session not found")
    except InsufficientPermissionException:
        logger.error(f"用户 {current_user.id} 没有权限开始训练课程")
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        logger.error(f"开始训练课程失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/sessions/{session_id}/exercises/{exercise_id}/records", response_model=Dict[str, Any])
async def record_exercise_set(
    session_id: int = Path(..., description="课程ID"),
    exercise_id: int = Path(..., description="动作ID"),
    set_data: SetRecordCreate = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """记录训练组数据
    
    会员或教练可以记录训练中的动作组数据。
    """
    logger.info(f"用户 {current_user.id} 记录训练课程 {session_id} 的动作 {exercise_id} 数据")
    training_service = TeamTrainingService(db)
    try:
        return await training_service.record_exercise_set(session_id, exercise_id, set_data, current_user)
    except SessionNotFoundException:
        logger.error(f"训练课程不存在: {session_id}")
        raise HTTPException(status_code=404, detail="Session not found")
    except InsufficientPermissionException:
        logger.error(f"用户 {current_user.id} 没有权限记录训练数据")
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        logger.error(f"记录训练组数据失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.put("/sessions/{session_id}/complete", response_model=SessionResponse)
async def complete_training_session(
    session_id: int = Path(..., description="课程ID"),
    feedback_data: SessionFeedback = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """完成训练课程并提交反馈
    
    会员或教练可以完成训练课程并提交反馈。
    """
    logger.info(f"用户 {current_user.id} 完成训练课程 {session_id}")
    training_service = TeamTrainingService(db)
    try:
        return await training_service.complete_session(session_id, feedback_data, current_user)
    except SessionNotFoundException:
        logger.error(f"训练课程不存在: {session_id}")
        raise HTTPException(status_code=404, detail="Session not found")
    except InsufficientPermissionException:
        logger.error(f"用户 {current_user.id} 没有权限完成训练课程")
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        logger.error(f"完成训练课程失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

# 统计接口
@router.get("/teams/{team_id}/stats", response_model=Dict[str, Any])
async def get_team_statistics(
    team_id: int = Path(..., description="团队ID"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取团队统计数据
    
    获取团队的统计数据，包括成员数量、会员数量、活跃会员数量、训练课程数量、完成率等。
    """
    logger.info(f"用户 {current_user.id} 获取团队 {team_id} 统计数据")
    stats_service = TeamStatsService(db)
    try:
        return await stats_service.get_team_stats(team_id, current_user)
    except TeamNotFoundException:
        logger.error(f"团队不存在: {team_id}")
        raise HTTPException(status_code=404, detail="Team not found")
    except InsufficientPermissionException:
        logger.error(f"用户 {current_user.id} 没有权限查看团队统计数据")
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        logger.error(f"获取团队统计数据失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/clients/{client_relation_id}/stats", response_model=Dict[str, Any])
async def get_client_statistics(
    client_relation_id: int = Path(..., description="会员关系ID"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取会员统计数据
    
    获取会员的统计数据，包括训练计划数量、已完成的训练计划数量、训练课程数量、完成率等。
    """
    logger.info(f"用户 {current_user.id} 获取会员关系 {client_relation_id} 统计数据")
    stats_service = TeamStatsService(db)
    try:
        return await stats_service.get_client_stats(client_relation_id, current_user)
    except ClientRelationNotFoundException:
        logger.error(f"会员关系不存在: {client_relation_id}")
        raise HTTPException(status_code=404, detail="Client relation not found")
    except InsufficientPermissionException:
        logger.error(f"用户 {current_user.id} 没有权限查看会员统计数据")
        raise HTTPException(status_code=403, detail="Not enough permissions")
    except TeamServiceException as e:
        logger.error(f"获取会员统计数据失败: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e)) 