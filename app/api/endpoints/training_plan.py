from typing import Any, List, Optional, Dict
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Query, Body, Path
from sqlalchemy.orm import Session

from app import crud, models, schemas
from app.schemas import set_record as set_record_schemas  # 导入set_record模块
from app.api import deps
from app.services.training_plan_service import TrainingPlanService
from app.services.llm_proxy_service import LLMProxyService
from app.services.sql_tool_service import SQLToolService

router = APIRouter()


@router.post("/generate", response_model=schemas.training_plan.TrainingPlanWithWorkouts)
async def generate_training_plan(
    *,
    db: Session = Depends(deps.get_db),
    plan_request: schemas.training_plan.TrainingPlanGenRequest,
    current_user: models.User = Depends(deps.get_current_active_user),
    llm_service: LLMProxyService = Depends(deps.get_llm_proxy_service)
) -> Any:
    """生成训练计划"""
    # 验证用户权限（只能为自己生成计划）
    if plan_request.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="只能为自己生成训练计划")

    # 创建SQL工具服务
    sql_tool_service = SQLToolService(db)

    # 实例化训练计划服务
    training_plan_service = TrainingPlanService(db, llm_service, sql_tool_service)

    try:
        # 生成训练计划
        plan = await training_plan_service.generate_training_plan(
            user_id=plan_request.user_id,
            duration_weeks=plan_request.duration_weeks,
            days_per_week=plan_request.days_per_week,
            fitness_goal=plan_request.fitness_goal,
            available_equipment=plan_request.available_equipment,
            focus_body_parts=plan_request.focus_body_parts,
            time_per_workout=plan_request.time_per_workout,
            additional_notes=plan_request.additional_notes
        )
        return plan
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成训练计划失败: {str(e)}")


@router.post("/daily", response_model=schemas.workout.WorkoutWithExercises)
async def generate_daily_workout(
    *,
    db: Session = Depends(deps.get_db),
    workout_request: schemas.training_plan.DailyWorkoutGenRequest,
    current_user: models.User = Depends(deps.get_current_active_user),
    llm_service: LLMProxyService = Depends(deps.get_llm_proxy_service)
) -> Any:
    """生成单日训练计划"""
    # 验证用户权限（只能为自己生成计划）
    if workout_request.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="只能为自己生成训练计划")

    # 创建SQL工具服务
    sql_tool_service = SQLToolService(db)
    # 实例化训练计划服务
    training_plan_service = TrainingPlanService(db, llm_service, sql_tool_service)

    try:
        # 生成单日训练计划
        workout = await training_plan_service.generate_daily_workout(
            user_id=workout_request.user_id,
            available_time=workout_request.available_time,
            target_body_parts=workout_request.target_body_parts,
            available_equipment=workout_request.available_equipment,
            recovery_level=workout_request.recovery_level,
            additional_notes=workout_request.additional_notes
        )
        return workout
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成单日训练计划失败: {str(e)}")


@router.get("/", response_model=List[schemas.training_plan.TrainingPlanInDB])
def get_user_plans(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    skip: int = 0,
    limit: int = 100,
    active_only: bool = Query(True, description="是否只返回激活状态的计划")
) -> Any:
    """获取用户的所有训练计划"""
    plans = crud.crud_training_plan.get_user_plans(
        db, user_id=current_user.id, skip=skip, limit=limit, active_only=active_only
    )
    return plans


@router.get("/{plan_id}", response_model=schemas.training_plan.TrainingPlanWithWorkouts)
def get_plan_detail(
    *,
    db: Session = Depends(deps.get_db),
    plan_id: int,
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """获取训练计划详情"""
    plan = crud.crud_training_plan.get_with_workouts(db, id=plan_id)


    if not plan:
        raise HTTPException(status_code=404, detail="训练计划不存在")

    # 验证用户权限（只能查看自己的计划）
    if plan["user_id"] != current_user.id:
        # 检查计划的隐私设置 (0: Public, 1: Private)
        if plan["privacy_setting"] != 0:
            raise HTTPException(status_code=403, detail="无权访问此训练计划")

    return plan


@router.post("/{plan_id}/status", response_model=schemas.training_plan.TrainingPlanInDB)
def update_plan_status(
    *,
    db: Session = Depends(deps.get_db),
    plan_id: int,
    status: str = Body(..., embed=True),
    is_active: Optional[bool] = Body(None, embed=True),
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """更新训练计划状态"""
    # 验证状态值
    valid_statuses = ["active", "completed", "paused"]
    if status not in valid_statuses:
        raise HTTPException(status_code=400, detail=f"无效的状态值，可选值: {', '.join(valid_statuses)}")

    # 获取计划
    plan = crud.crud_training_plan.get(db, id=plan_id)

    if not plan:
        raise HTTPException(status_code=404, detail="训练计划不存在")

    # 验证用户权限（只能更新自己的计划）
    if plan.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权修改此训练计划")

    # 更新状态
    updated_plan = crud.crud_training_plan.update_plan_status(
        db, plan_id=plan_id, status=status, is_active=is_active
    )

    return updated_plan


@router.post("/{plan_id}/template", response_model=schemas.training_plan.TrainingPlanInDB)
def create_template(
    *,
    db: Session = Depends(deps.get_db),
    plan_id: int,
    name: Optional[str] = Body(None, embed=True),
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """从现有训练计划创建模板"""
    # 获取计划
    plan = crud.crud_training_plan.get(db, id=plan_id)

    if not plan:
        raise HTTPException(status_code=404, detail="训练计划不存在")

    # 验证用户权限（只能为自己的计划创建模板）
    if plan.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权为此训练计划创建模板")

    # 创建模板
    template = crud.crud_training_plan.create_template_from_plan(
        db, plan_id=plan_id, new_name=name
    )

    return template


@router.post("/templates/{template_id}/create", response_model=schemas.training_plan.TrainingPlanInDB)
def create_from_template(
    *,
    db: Session = Depends(deps.get_db),
    template_id: int,
    name: Optional[str] = Body(None, embed=True),
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """从模板创建训练计划"""
    # 获取模板
    template = crud.crud_training_plan.get(db, id=template_id)

    if not template:
        raise HTTPException(status_code=404, detail="训练计划模板不存在")

    # 验证模板的类型
    if not template.is_template:
        raise HTTPException(status_code=400, detail="指定的训练计划不是模板")

    # 验证用户权限（只能使用自己的模板或公开模板）
    if template.user_id != current_user.id and template.privacy_setting != 0:
        raise HTTPException(status_code=403, detail="无权使用此训练计划模板")

    # 从模板创建计划
    plan = crud.crud_training_plan.create_plan_from_template(
        db, template_id=template_id, user_id=current_user.id, new_name=name
    )

    return plan


@router.delete("/{plan_id}", response_model=dict)
def delete_plan(
    *,
    db: Session = Depends(deps.get_db),
    plan_id: int,
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """删除训练计划"""
    # 获取计划
    plan = crud.crud_training_plan.get(db, id=plan_id)

    if not plan:
        raise HTTPException(status_code=404, detail="训练计划不存在")

    # 验证用户权限（只能删除自己的计划）
    if plan.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权删除此训练计划")

    # 删除计划
    crud.crud_training_plan.remove(db, id=plan_id)

    return {"status": "success", "message": "训练计划已删除"}


@router.get("/workouts/by-date", response_model=List[schemas.workout.WorkoutWithExercises])
def get_workouts_by_date(
    *,
    db: Session = Depends(deps.get_db),
    date: str = Query(..., description="查询日期，格式为YYYY-MM-DD"),
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """通过日期获取训练日"""
    try:
        # 解析日期字符串
        parsed_date = datetime.strptime(date, "%Y-%m-%d")
    except ValueError:
        raise HTTPException(status_code=400, detail="日期格式无效，请使用YYYY-MM-DD格式")

    # 查询指定日期的所有训练日
    workouts = db.query(models.Workout).join(
        models.TrainingPlan,
        models.Workout.training_plan_id == models.TrainingPlan.id
    ).filter(
        models.TrainingPlan.user_id == current_user.id,
        models.Workout.scheduled_date == parsed_date.date(),
        models.TrainingPlan.is_active == True
    ).all()

    # 构建响应
    result = []
    for workout in workouts:
        # 获取训练日详情
        workout_detail = crud.crud_workout.get_with_exercises(db, id=workout.id)
        result.append(workout_detail)

    return result


@router.post("/workouts/{workout_id}/status", response_model=schemas.workout.WorkoutInDB)
def update_workout_status(
    *,
    db: Session = Depends(deps.get_db),
    workout_id: int,
    status_update: schemas.workout.WorkoutStatusUpdate,
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """更新训练日状态"""
    # 获取训练日
    workout = crud.crud_workout.get(db, id=workout_id)

    if not workout:
        raise HTTPException(status_code=404, detail="训练日不存在")

    # 获取关联的训练计划
    plan = crud.crud_training_plan.get(db, id=workout.training_plan_id)

    # 验证用户权限（只能更新自己的训练日）
    if plan.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权更新此训练日")

    # 更新状态
    update_data = {"status": status_update.status}

    # 如果状态变为 "in_progress"，记录开始时间
    if status_update.status == models.workout.WorkoutStatus.IN_PROGRESS and not workout.start_time:
        update_data["start_time"] = datetime.now()
        update_data["last_status_change"] = datetime.now()

        # 添加状态历史记录
        status_history = workout.status_history or []
        status_history.append({
            "status": status_update.status,
            "timestamp": datetime.now().isoformat(),
            "duration": None
        })
        update_data["status_history"] = status_history

    # 如果状态变为 "completed"，记录结束时间和计算持续时间
    elif status_update.status == models.workout.WorkoutStatus.COMPLETED:
        now = datetime.now()
        update_data["end_time"] = now
        update_data["last_status_change"] = now

        # 计算实际持续时间（分钟）
        if workout.start_time:
            actual_duration = int((now - workout.start_time).total_seconds() / 60)
            update_data["actual_duration"] = actual_duration

            # 计算净训练时间（减去暂停时间）
            pause_records = workout.pause_records or []
            total_pause_minutes = sum(record.get("duration", 0) for record in pause_records if record.get("duration"))
            net_duration = max(0, actual_duration - total_pause_minutes)
            update_data["net_duration"] = net_duration

        # 添加状态历史记录
        status_history = workout.status_history or []

        # 计算上一个状态的持续时间
        if status_history:
            last_status = status_history[-1]
            last_timestamp = datetime.fromisoformat(last_status["timestamp"])
            duration_minutes = int((now - last_timestamp).total_seconds() / 60)
            last_status["duration"] = duration_minutes

        status_history.append({
            "status": status_update.status,
            "timestamp": now.isoformat(),
            "duration": None
        })
        update_data["status_history"] = status_history

    # 如果状态变为 "paused"，记录暂停开始时间
    elif status_update.status == models.workout.WorkoutStatus.PAUSED:
        now = datetime.now()
        update_data["last_status_change"] = now

        # 添加暂停记录
        pause_records = workout.pause_records or []
        pause_records.append({
            "pause_start": now.isoformat(),
            "pause_end": None,
            "duration": None
        })
        update_data["pause_records"] = pause_records

        # 添加状态历史记录
        status_history = workout.status_history or []

        # 计算上一个状态的持续时间
        if status_history:
            last_status = status_history[-1]
            last_timestamp = datetime.fromisoformat(last_status["timestamp"])
            duration_minutes = int((now - last_timestamp).total_seconds() / 60)
            last_status["duration"] = duration_minutes

        status_history.append({
            "status": status_update.status,
            "timestamp": now.isoformat(),
            "duration": None
        })
        update_data["status_history"] = status_history

    # 如果从 "paused" 恢复到 "in_progress"，记录暂停结束时间和计算暂停时长
    elif workout.status == models.workout.WorkoutStatus.PAUSED and status_update.status == models.workout.WorkoutStatus.IN_PROGRESS:
        now = datetime.now()
        update_data["last_status_change"] = now

        # 更新最后一条暂停记录
        pause_records = workout.pause_records or []
        if pause_records:
            last_pause = pause_records[-1]
            if last_pause.get("pause_start") and not last_pause.get("pause_end"):
                pause_start = datetime.fromisoformat(last_pause["pause_start"])
                last_pause["pause_end"] = now.isoformat()
                pause_duration = int((now - pause_start).total_seconds() / 60)
                last_pause["duration"] = pause_duration
                update_data["pause_records"] = pause_records

        # 添加状态历史记录
        status_history = workout.status_history or []

        # 计算上一个状态的持续时间
        if status_history:
            last_status = status_history[-1]
            last_timestamp = datetime.fromisoformat(last_status["timestamp"])
            duration_minutes = int((now - last_timestamp).total_seconds() / 60)
            last_status["duration"] = duration_minutes

        status_history.append({
            "status": status_update.status,
            "timestamp": now.isoformat(),
            "duration": None
        })
        update_data["status_history"] = status_history

    return crud.crud_workout.update(db, db_obj=workout, obj_in=update_data)


@router.get("/{plan_id}/workouts", response_model=List[Dict[str, Any]])
def get_plan_workouts(
    *,
    db: Session = Depends(deps.get_db),
    plan_id: int = Path(..., description="训练计划ID"),
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """根据训练计划ID获取所有workout信息及相关exercises信息

    Args:
        db: 数据库会话
        plan_id: 训练计划ID
        current_user: 当前用户

    Returns:
        包含所有workout和exercises信息的列表
    """
    # 验证计划是否存在
    plan = crud.crud_training_plan.get(db, id=plan_id)
    if not plan:
        raise HTTPException(status_code=404, detail="训练计划不存在")

    # 验证用户权限（只能查看自己的计划或公开计划）
    if plan.user_id != current_user.id and plan.privacy_setting != 0:
        raise HTTPException(status_code=403, detail="无权访问此训练计划")

    # 获取计划下的所有workout
    workouts = crud.crud_workout.get_by_plan(db, training_plan_id=plan_id)

    # 构建响应数据
    result = []
    for workout in workouts:
        # 获取workout详情（包含exercises）
        workout_detail = crud.crud_workout.get_with_exercises(db, id=workout.id)
        if workout_detail:
            result.append(workout_detail)

    return result


@router.get("/{plan_id}/workouts/exercises", response_model=Dict[str, Any])
def get_plan_workouts_with_exercises(
    *,
    db: Session = Depends(deps.get_db),
    plan_id: int = Path(..., description="训练计划ID"),
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """根据训练计划ID获取所有workout信息及相关exercises详细信息

    Args:
        db: 数据库会话
        plan_id: 训练计划ID
        current_user: 当前用户

    Returns:
        包含所有workout和exercises详细信息的数据结构
    """
    # 验证计划是否存在
    plan = crud.crud_training_plan.get(db, id=plan_id)
    if not plan:
        raise HTTPException(status_code=404, detail="训练计划不存在")

    # 验证用户权限（只能查看自己的计划或公开计划）
    if plan.user_id != current_user.id and plan.privacy_setting != 0:
        raise HTTPException(status_code=403, detail="无权访问此训练计划")

    # 获取计划下的所有workout
    workouts = crud.crud_workout.get_by_plan(db, training_plan_id=plan_id)

    # 获取所有workout_id
    workout_ids = [workout.id for workout in workouts]

    # 获取所有workout_exercise
    workout_exercises = []
    exercise_ids = set()
    workout_exercise_map = {}  # workout_id -> [workout_exercise]

    for workout_id in workout_ids:
        exercises = crud.crud_workout_exercise.get_by_workout(db, workout_id=workout_id)
        workout_exercises.extend(exercises)

        # 收集exercise_id
        for exercise in exercises:
            exercise_ids.add(exercise.exercise_id)

        # 构建workout_id到workout_exercise的映射
        workout_exercise_map[workout_id] = exercises

    # 获取所有exercise详细信息
    exercises = {}
    for exercise_id in exercise_ids:
        exercise = db.query(models.Exercise).filter(models.Exercise.id == exercise_id).first()
        if exercise:
            exercises[exercise_id] = {
                "id": exercise.id,
                "name": exercise.name,
                "en_name": exercise.en_name,
                "body_part_id": exercise.body_part_id,
                "equipment_id": exercise.equipment_id,
                "image_name": exercise.image_name,
                "gif_url": exercise.gif_url,
                "description": exercise.description,
                "level": exercise.level
            }

    # 构建最终响应
    response = {
        "plan_id": plan_id,
        "plan_name": plan.plan_name,
        "workouts": []
    }

    for workout in workouts:
        workout_data = {
            "id": workout.id,
            "name": workout.name,
            "day_number": workout.day_number,
            "day_of_week": workout.day_of_week,
            "description": workout.description,
            "estimated_duration": workout.estimated_duration,
            "scheduled_date": workout.scheduled_date,
            "status": workout.status,
            "exercises": []
        }

        # 添加workout_exercise信息
        if workout.id in workout_exercise_map:
            for we in workout_exercise_map[workout.id]:
                exercise_data = {
                    "id": we.id,
                    "exercise_id": we.exercise_id,
                    "sets": we.sets,
                    "reps": we.reps,
                    "rest_seconds": we.rest_seconds,
                    "weight": we.weight,
                    "order": we.order,
                    "notes": we.notes,
                    "exercise_type": we.exercise_type,
                    "superset_group": we.superset_group
                }

                # 添加exercise详细信息
                if we.exercise_id in exercises:
                    exercise_data["exercise_detail"] = exercises[we.exercise_id]

                workout_data["exercises"].append(exercise_data)

        response["workouts"].append(workout_data)

    return response


@router.put("/{plan_id}", response_model=schemas.base.BasicResponse)
def update_training_plan(
    *,
    db: Session = Depends(deps.get_db),
    plan_id: int,
    update_data: schemas.training_plan.TrainingPlanUpdate,
    current_user: models.User = Depends(deps.get_current_active_user)
) -> Any:
    """更新训练计划相关数据"""
    # 验证计划是否存在
    plan = crud.crud_training_plan.get(db, id=plan_id)
    if not plan:
        raise HTTPException(status_code=404, detail="训练计划不存在")

    # 验证用户权限
    if plan.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权修改此训练计划")

    try:
        # 打印请求数据，用于调试
        import json
        #print(f"\n\n===== 训练计划更新请求 ID={plan_id} =====")
        #print(f"请求数据: {json.dumps(update_data.dict(), ensure_ascii=False, indent=2)}")

        # 创建一个字典来存储临时ID到真实ID的映射
        temp_id_mapping = {}

        # 更新训练计划基本信息
        if update_data.plan_data:
            crud.crud_training_plan.update(db, db_obj=plan, obj_in=update_data.plan_data)
            #print(f"✅ 更新训练计划基本信息成功")

        # 更新训练日信息
        if update_data.workout_data:
            for workout_update in update_data.workout_data:
                # 验证workout_update包含id字段
                if "id" not in workout_update:
                    raise ValueError("训练日数据缺少id字段")

                workout = crud.crud_workout.get(db, id=workout_update["id"])
                if workout and workout.training_plan_id == plan_id:
                    crud.crud_workout.update(db, db_obj=workout, obj_in=workout_update)

        # 更新训练动作信息
        if update_data.workout_exercise_data:
            for exercise_update in update_data.workout_exercise_data:
                # 验证exercise_update包含id字段
                if "id" not in exercise_update:
                    raise ValueError("训练动作数据缺少id字段")

                # 检查是否为新增的训练动作
                temp_id = exercise_update["id"]
                if isinstance(temp_id, str) and temp_id.startswith('added_exercise'):
                    #print(f"🆕 检测到新增训练动作标记: {temp_id}")

                    # 验证必要字段
                    if "workout_id" not in exercise_update:
                        #print(f"❌ 新增训练动作缺少workout_id字段，跳过")
                        continue

                    workout_id = exercise_update["workout_id"]
                    # 验证workout是否属于当前训练计划
                    workout = crud.crud_workout.get(db, id=workout_id)
                    if not workout or workout.training_plan_id != plan_id:
                        #print(f"❌ 训练日ID={workout_id}不属于当前训练计划，跳过")
                        continue

                    # 创建新的workout_exercise
                    try:
                        # 移除id字段，因为它是临时ID
                        create_data = exercise_update.copy()
                        del create_data["id"]

                        exercise = crud.crud_workout_exercise.create(db, obj_in=create_data)
                        #print(f"✅ 成功创建新训练动作: ID={exercise.id}, 临时ID={temp_id}")

                        # 保存临时ID到真实ID的映射
                        temp_id_mapping[temp_id] = exercise.id
                        #print(f"📝 添加映射: {temp_id} -> {exercise.id}")
                    except Exception as e:
                        #print(f"❌ 创建训练动作失败: {str(e)}")
                        import traceback
                        #print(traceback.format_exc())
                        continue
                else:
                    # 处理现有的训练动作
                    exercise = crud.crud_workout_exercise.get(db, id=exercise_update["id"])
                    if exercise:
                        workout = crud.crud_workout.get(db, id=exercise.workout_id)
                        if workout and workout.training_plan_id == plan_id:
                            crud.crud_workout_exercise.update(db, db_obj=exercise, obj_in=exercise_update)

        # 更新训练记录
        if update_data.training_record_data:
            #print(f"\n----- 处理训练记录数据 -----")
            #print(f"收到 {len(update_data.training_record_data)} 条训练记录数据")

            for record_update in update_data.training_record_data:
                #print(f"处理训练记录: {json.dumps(record_update, ensure_ascii=False)}")

                # 检查是否有id字段，如果没有但有workout_id字段，则使用workout_id作为id
                if "id" not in record_update:
                    if "workout_id" in record_update:
                        #print(f"使用workout_id作为id: {record_update['workout_id']}")
                        record_id = record_update["workout_id"]
                    else:
                        #print(f"❌ 训练记录数据缺少id和workout_id字段，跳过")
                        continue
                else:
                    record_id = record_update["id"]

                # try:
                #     record = crud.crud_user_training_record.get(db, id=record_id)
                #     if record and record.user_id == current_user.id:
                #         #print(f"更新训练记录: ID={record_id}")
                #         crud.crud_user_training_record.update(db, db_obj=record, obj_in=record_update)
                #         #print(f"✅ 更新成功")
                #     else:
                #         #print(f"⚠️ 未找到ID={record_id}的训练记录或无权限访问")

                #         # 如果记录不存在，尝试创建新记录
                #         if "user_id" not in record_update:
                #             record_update["user_id"] = current_user.id

                #         try:
                #             new_record = crud.crud_user_training_record.create(db, obj_in=record_update)
                #             #print(f"✅ 创建新训练记录: ID={new_record.id}")
                #         except Exception as e:
                #             print(f"❌ 创建训练记录失败: {str(e)}")
                # except Exception as e:
                #     #print(f"❌ 处理训练记录时出错: {str(e)}")
                #     import traceback
                #     print(traceback.format_exc())

        # 更新组记录
        if update_data.set_record_data:
            #print(f"\n----- 处理组记录数据 -----")
            #print(f"收到 {len(update_data.set_record_data)} 条组记录数据")

            for idx, set_update in enumerate(update_data.set_record_data):
                #print(f"\n处理第 {idx+1} 条组记录:")
                #print(f"原始数据: {json.dumps(set_update, ensure_ascii=False)}")

                # 验证set_update包含id字段和workout_exercise_id字段
                if "id" not in set_update:
                    error_msg = "组记录数据缺少id字段"
                    #print(f"❌ 错误: {error_msg}")
                    raise ValueError(error_msg)
                if "workout_exercise_id" not in set_update:
                    error_msg = "组记录数据缺少workout_exercise_id字段"
                    #print(f"❌ 错误: {error_msg}")
                    raise ValueError(error_msg)

                # 获取原始ID（可能是字符串格式，如"62_set_1"）
                original_id = set_update["id"]
                #print(f"原始ID: {original_id}")

                # 尝试查找现有记录（如果ID是整数）
                try:
                    # 尝试将ID转换为整数
                    numeric_id = int(original_id)
                    set_record = crud.crud_set_record.get(db, id=numeric_id)
                    #print(f"尝试查找ID={numeric_id}的记录: {'找到' if set_record else '未找到'}")
                except (ValueError, TypeError):
                    # 如果ID不是整数，则设置为None
                    set_record = None
                    #print(f"ID不是整数，将创建新记录")

                # 获取workout_exercise_id
                workout_exercise_id = set_update["workout_exercise_id"]
                #print(f"训练动作ID: {workout_exercise_id}")

                # 检查是否为临时ID（以'added_exercise'开头的字符串）
                if isinstance(workout_exercise_id, str) and workout_exercise_id.startswith('added_exercise'):
                    #print(f"🔍 检测到临时训练动作ID: {workout_exercise_id}")

                    # 检查是否已经在映射表中
                    if workout_exercise_id in temp_id_mapping:
                        real_id = temp_id_mapping[workout_exercise_id]
                        #print(f"✅ 找到映射: {workout_exercise_id} -> {real_id}")
                        # 更新为真实ID
                        workout_exercise_id = real_id
                        set_update["workout_exercise_id"] = real_id
                    else:
                        #print(f"⚠️ 未找到临时ID的映射，尝试创建新的训练动作")

                        # 从set_update中提取workout_id
                        if "workout_id" not in set_update:
                            #print(f"❌ 新增训练动作缺少workout_id字段，跳过")
                            continue

                        workout_id = set_update["workout_id"]
                        # 验证workout是否属于当前训练计划
                        workout = crud.crud_workout.get(db, id=workout_id)
                        if not workout or workout.training_plan_id != plan_id:
                            #print(f"❌ 训练日ID={workout_id}不属于当前训练计划，跳过")
                            continue

                        # 从set_update中提取创建workout_exercise所需的数据
                        exercise_data = {
                            "workout_id": workout_id,
                            "exercise_id": set_update.get("exercise_id"),
                            "sets": set_update.get("sets", 3),
                            "reps": set_update.get("reps", "10"),
                            "rest_seconds": set_update.get("rest_seconds", 60),
                            "order": set_update.get("order", 1),
                            "notes": set_update.get("notes"),
                            "weight": set_update.get("weight"),
                            "exercise_type": set_update.get("exercise_type", "weight_reps"),
                            "superset_group": set_update.get("superset_group")
                        }

                        # 创建新的workout_exercise
                        try:
                            exercise = crud.crud_workout_exercise.create(db, obj_in=exercise_data)
                            #print(f"✅ 成功创建新训练动作: ID={exercise.id}, 临时ID={workout_exercise_id}")
                            # 更新workout_exercise_id为新创建的ID
                            workout_exercise_id = exercise.id
                            set_update["workout_exercise_id"] = exercise.id

                            # 保存临时ID到真实ID的映射
                            temp_id_mapping[workout_exercise_id] = exercise.id
                            #print(f"📝 添加映射: {workout_exercise_id} -> {exercise.id}")
                        except Exception as e:
                            #print(f"❌ 创建训练动作失败: {str(e)}")
                            import traceback
                            #print(traceback.format_exc())
                            continue
                else:
                    # 验证workout_exercise是否属于当前训练计划
                    try:
                        exercise = crud.crud_workout_exercise.get(db, id=workout_exercise_id)
                        if not exercise:
                            #print(f"❌ 训练动作ID={workout_exercise_id}不存在，跳过")
                            continue  # 跳过不存在的训练动作

                        workout = crud.crud_workout.get(db, id=exercise.workout_id)
                        if not workout or workout.training_plan_id != plan_id:
                            #print(f"❌ 训练动作不属于当前训练计划，跳过")
                            continue  # 跳过不属于当前训练计划的训练日
                    except Exception as e:
                        #print(f"❌ 验证训练动作失败: {str(e)}")
                        continue

                #print(f"✅ 验证通过: 训练动作属于训练计划ID={plan_id}")

                # 处理set_number
                if "set_number" not in set_update:
                    # 如果没有提供set_number，则获取当前最大的set_number并加1
                    existing_sets = crud.crud_set_record.get_by_workout_exercise(
                        db, workout_exercise_id=workout_exercise_id
                    )
                    set_update["set_number"] = len(existing_sets) + 1
                    #print(f"自动分配组号: {set_update['set_number']}")

                if set_record:
                    # 如果记录存在，则更新
                    #print(f"更新现有记录ID={set_record.id}")
                    updated_record = crud.crud_set_record.update(db, db_obj=set_record, obj_in=set_update)
                    #print(f"✅ 更新成功: ID={updated_record.id}, 组号={updated_record.set_number}")
                else:
                    # 如果记录不存在，则创建新记录
                    # 创建新记录时，移除id字段
                    create_data = set_update.copy()
                    if "id" in create_data:
                        del create_data["id"]

                    #print(f"创建新记录: {json.dumps(create_data, ensure_ascii=False)}")

                    try:
                        # 创建新记录
                        new_record = crud.crud_set_record.create_with_workout_exercise(
                            db,
                            obj_in=set_record_schemas.SetRecordCreate(**create_data),
                            workout_exercise_id=workout_exercise_id
                        )
                        #print(f"✅ 创建成功: ID={new_record.id}, 组号={new_record.set_number}")
                    except Exception as e:
                        #print(f"❌ 创建失败: {str(e)}")
                        import traceback
                        #print(traceback.format_exc())
                        raise

        db.commit()
        #print(f"\n✅✅✅ 所有更新操作已完成并提交到数据库")

        # 如果有set_record_data，查询并打印最新的记录
        if update_data.set_record_data:
            #print(f"\n----- 验证组记录更新结果 -----")
            for set_update in update_data.set_record_data:
                workout_exercise_id = set_update.get("workout_exercise_id")
                if workout_exercise_id:
                    records = crud.crud_set_record.get_by_workout_exercise(db, workout_exercise_id=workout_exercise_id)
                    print(f"训练动作ID={workout_exercise_id}的组记录数量: {len(records)}")
                    # for record in records:
                        #print(f"  - ID={record.id}, 组号={record.set_number}, 重量={record.weight}, 次数={record.reps}, 完成状态={record.completed}")

        return {"status": "success", "message": "训练计划更新成功"}
    except ValueError as ve:
        # 处理验证错误
        db.rollback()
        raise HTTPException(status_code=400, detail=f"数据验证失败: {str(ve)}")
    except KeyError as ke:
        # 处理键错误
        db.rollback()
        raise HTTPException(status_code=400, detail=f"数据结构错误: 缺少必要字段 {str(ke)}")
    except Exception as e:
        # 处理其他异常
        db.rollback()
        import traceback
        error_detail = f"更新训练计划失败: {str(e)}\n{traceback.format_exc()}"
        #print(error_detail)  # 打印到服务器日志
        raise HTTPException(status_code=500, detail=f"更新训练计划失败: {str(e)}")