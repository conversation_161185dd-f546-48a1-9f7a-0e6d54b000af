# app/api/endpoints/nutrient.py
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Optional, List

from app.schemas.nutrient import NutrientRequest, NutrientResponse
from app.services.nutrient_service import get_nutrient_target
from app.api.deps import get_db

router = APIRouter()

@router.post("/daily-target", response_model=NutrientResponse)
async def get_daily_nutrient_target(
    request: NutrientRequest,
    db: Session = Depends(get_db)
) -> NutrientResponse:
    """
    根据用户年龄、性别和孕期阶段获取每日营养素推荐摄入量

    - dietary_names参数应使用字段名称，如'vitamin_a'、'phosphor'等，而非中文名称
    """
    # 验证性别参数
    if request.sex not in ['male', 'female']:
        raise HTTPException(status_code=400, detail="性别必须为'male'或'female'")
    
    # 验证孕期阶段参数
    if request.pregnancy_stage and request.pregnancy_stage not in ['early', 'mid', 'late', 'lactation']:
        raise HTTPException(status_code=400, detail="孕期阶段必须为'early'、'mid'、'late'、'lactation'或不提供")
    
    # 验证孕期阶段与性别是否匹配
    if request.pregnancy_stage and request.sex != 'female':
        raise HTTPException(status_code=400, detail="孕期阶段只适用于女性")
    
    # 获取营养素目标值
    try:
        result = await get_nutrient_target(
            db=db, 
            age=request.age, 
            sex=request.sex, 
            pregnancy_stage=request.pregnancy_stage,
            dietary_names=request.dietary_names
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取营养素目标值失败: {str(e)}")

# 提供单独的GET端点，便于简单查询
@router.get("/recommendation", response_model=NutrientResponse)
async def get_nutrient_recommendation(
    age: float = Query(..., description="用户年龄(岁)"),
    sex: str = Query(..., description="性别: 'male' 或 'female'"),
    pregnancy_stage: Optional[str] = Query(None, description="孕期阶段: 'early', 'mid', 'late', 'lactation' 或 None"),
    dietary_names: Optional[List[str]] = Query(None, description="营养素字段名称列表，如vitamin_a、phosphor等"),
    db: Session = Depends(get_db)
) -> NutrientResponse:
    """
    根据用户年龄、性别和孕期阶段获取每日营养素推荐摄入量(GET 方法)

    - dietary_names参数应使用字段名称，如'vitamin_a'、'phosphor'等，而非中文名称
    """
    # 验证性别参数
    if sex not in ['male', 'female']:
        raise HTTPException(status_code=400, detail="性别必须为'male'或'female'")
    
    # 验证孕期阶段参数
    if pregnancy_stage and pregnancy_stage not in ['early', 'mid', 'late', 'lactation']:
        raise HTTPException(status_code=400, detail="孕期阶段必须为'early'、'mid'、'late'、'lactation'或不提供")
    
    # 验证孕期阶段与性别是否匹配
    if pregnancy_stage and sex != 'female':
        raise HTTPException(status_code=400, detail="孕期阶段只适用于女性")
    
    # 获取营养素目标值
    try:
        from decimal import Decimal
        result = await get_nutrient_target(
            db=db, 
            age=Decimal(str(age)), 
            sex=sex, 
            pregnancy_stage=pregnancy_stage,
            dietary_names=dietary_names
        )
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取营养素目标值失败: {str(e)}")