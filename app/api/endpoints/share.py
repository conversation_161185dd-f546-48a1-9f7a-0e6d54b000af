from typing import Any
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
import logging

from app import models, schemas, crud
from app.api import deps

router = APIRouter()
logger = logging.getLogger("fitness-coach-api")


@router.post("/track", response_model=schemas.ShareTrack)
def track_share(
    share_data: schemas.ShareTrackCreate,
    current_user: models.User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
) -> Any:
    """
    记录分享数据
    """
    try:
        # 设置分享者为当前用户
        if not share_data.shared_by:
            share_data.shared_by = current_user.id
        
        # 创建分享记录
        share_track = crud.share_track.create(db, obj_in=share_data)
        
        return share_track
    except Exception as e:
        logger.error(f"记录分享数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail="记录分享数据失败")


@router.get("/scan/{share_code}", response_model=schemas.ShareTrack)
def track_scan(
    share_code: str,
    current_user: models.User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
) -> Any:
    """
    记录扫码数据
    """
    try:
        # 解析分享码
        parts = share_code.split('_')
        if len(parts) != 2 or parts[0] != 'share':
            raise HTTPException(status_code=400, detail="无效的分享码")
        
        try:
            shared_by = int(parts[1])
        except ValueError:
            raise HTTPException(status_code=400, detail="无效的分享码")
        
        # 创建分享记录
        share_data = schemas.ShareTrackCreate(
            shared_by=shared_by,
            scanned_by=current_user.id,
            share_type='qrcode',
            page='pages/home/<USER>'
        )
        
        share_track = crud.share_track.create(db, obj_in=share_data)
        
        return share_track
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"记录扫码数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail="记录扫码数据失败") 