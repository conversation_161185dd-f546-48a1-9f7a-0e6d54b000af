"""
聊天API端点模块

提供与AI助手对话的REST和WebSocket接口，处理消息流、用户对话历史和训练计划生成。
"""

from typing import Any, Dict, List, Optional, Tuple, Union
from fastapi import APIRouter, Depends, HTTPException, Body, BackgroundTasks, WebSocket, WebSocketDisconnect, Query, status
from sqlalchemy.orm import Session
import logging
import uuid
import asyncio
import json
import time
from pydantic import BaseModel, Field

from app import schemas, models, crud
from app.api import deps
from app.models.message import MessageRole
from app.services.llm_proxy_service import LLMProxyService
from app.services.chat_log_service import ChatLogService
from app.services.cache_service import cache_service
from app.services.conversation.orchestrator import ConversationService
from app.utils.time_utils import get_utc_now
from app.services.task_manager import TaskManager
from app.services.ai_assistant.conversation.orchestrator import conversation_orchestrator
from app.services.meta_info_manager import MetaInfoManager
from app.services.sql_tool_service import SQLToolService
from app.services.training_plan_service import TrainingPlanService

# 导入辅助模块
from app.api.endpoints.chat_helpers import (
    get_or_create_conversation,
    normalize_session_id,
    create_user_message,
    create_assistant_message,
    get_last_assistant_meta_info,
    prepare_meta_info,
    process_training_plan_data,
    ensure_exercise_fields,
    send_heartbeats,
    process_websocket_message,
    process_ai_response
)

logger = logging.getLogger(__name__)

router = APIRouter()
llm_proxy_service = LLMProxyService()

class ChatMessage(BaseModel):
    """聊天消息模型"""
    message: str = Field(..., description="用户消息内容")
    conversation_id: Optional[str] = Field(None, description="对话ID，如果为空则创建新对话")
    user_id: Optional[str] = Field(None, description="用户ID")

class ChatResponse(BaseModel):
    """聊天响应模型"""
    conversation_id: str = Field(..., description="对话ID")
    response: str = Field(..., description="AI助手响应内容")
    success: bool = Field(..., description="处理是否成功")
    intent_type: Optional[str] = Field(None, description="识别的意图类型")
    confidence: Optional[float] = Field(None, description="意图识别置信度")
    end_conversation: bool = Field(False, description="是否结束对话")
    next_intent: Optional[str] = Field(None, description="下一个需要处理的意图")
    missing_parameters: Optional[list] = Field(None, description="缺失的参数列表")
    error: Optional[str] = Field(None, description="错误信息")

# ============== 普通HTTP聊天接口 ==============

@router.post("/message", response_model=schemas.ChatResponse)
async def send_message(
    *,
    db: Session = Depends(deps.get_db),
    chat_request: schemas.ChatRequest = Body(...),
    current_user: models.User = Depends(deps.get_current_active_user),
    background_tasks: BackgroundTasks,
    _: int = Depends(deps.check_chat_rate_limit),  # 添加限流依赖
) -> Any:
    """
    向AI助手发送消息并获取回复

    支持快速意图参数，直接进入特定对话流程
    """
    # 获取或创建会话
    conversation, is_new = get_or_create_conversation(
        db, 
        chat_request.session_id or str(uuid.uuid4()),
        current_user.id,
        system_prompt=chat_request.system_prompt
    )

    # 获取最近的AI消息，用于元数据处理
    recent_messages = crud.crud_message.get_conversation_messages_desc(
        db, conversation_id=conversation.id, skip=0, limit=5
    )

    # 获取最近的AI助手消息的meta_info和时间
    last_assistant_meta_info, last_assistant_message_time = get_last_assistant_meta_info(recent_messages)

    # 获取当前时间
    current_time = get_utc_now()

    # 保存用户消息
    user_message = await create_user_message(
        db, 
        conversation.id, 
        current_user.id, 
        chat_request.message
    )

    # 准备初始元数据
    initial_meta_info = prepare_meta_info(
        chat_request.message,
        last_assistant_meta_info,
        last_assistant_message_time,
        current_time,
        user_message.id
    )

    logger.info(f"准备的初始元数据: {initial_meta_info}")

    # 使用ConversationService处理消息
    conversation_service = ConversationService(db, llm_proxy_service)
    llm_response, meta_info = await conversation_service.process_message(
        user_id=current_user.id,
        message=chat_request.message,
        conversation_id=conversation.id,
        meta_info=initial_meta_info,  # 传递初始元数据
        quick_intent=chat_request.quick_intent  # 快速意图参数
    )

    # 保存AI回复
    ai_message = await create_assistant_message(
        db, 
        conversation.id, 
        current_user.id, 
        llm_response, 
        meta_info
    )

    # 后台任务：记录QA对
    chat_log_service = ChatLogService(db)
    background_tasks.add_task(
        chat_log_service.log_interaction,
        user_id=current_user.id,
        session_id=conversation.session_id,
        question=chat_request.message,
        answer=llm_response
    )

    return {
        "response": llm_response,
        "session_id": conversation.session_id,
        "meta_info": meta_info  # 返回元数据给前端
    }

@router.get("/conversations", response_model=schemas.ConversationList)
def get_conversations(
    *,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取当前用户的所有活跃会话
    """
    conversations = crud.crud_conversation.get_active_by_user(
        db, user_id=current_user.id, skip=skip, limit=limit
    )

    total = crud.crud_conversation.count_by_user(db, user_id=current_user.id)

    return {
        "conversations": conversations,
        "total": total
    }

@router.get("/conversations/{session_id}/messages", response_model=schemas.MessageList)
def get_conversation_messages(
    *,
    db: Session = Depends(deps.get_db),
    session_id: str,
    skip: int = 0,
    limit: int = 5,  # 默认获取5条
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取指定会话的消息，默认获取最新的5条，如果会话不存在则自动创建
    """
    # 尝试从缓存获取数据
    cached_messages = cache_service.get_conversation_messages(session_id, limit)
    if cached_messages and skip == 0:
        logger.info(f"从缓存获取会话消息: {session_id}, 消息数: {len(cached_messages)}")
        # 这种情况我们仍然需要验证用户是否有权访问该会话
        conversation = crud.crud_conversation.get_by_session_id(db, session_id=session_id)
        if conversation and conversation.user_id == current_user.id:
            # 从缓存获取成功，并且用户有权访问
            return {
                "messages": cached_messages,
                "total": len(cached_messages),  # 这只是缓存中的数量，不是全部
                "from_cache": True
            }

    # 获取或创建会话
    conversation, is_new = get_or_create_conversation(db, session_id, current_user.id)

    # 计算总消息数
    total = crud.crud_message.count_by_conversation(db, conversation_id=conversation.id)

    # 获取最新的消息，使用倒序方法
    messages = crud.crud_message.get_conversation_messages_desc(
        db, conversation_id=conversation.id, skip=skip, limit=limit
    )

    # 确保每个消息对象的元数据字段是字典而不是None
    formatted_messages = []
    for message in messages:
        if message.meta_info is None:
            message.meta_info = {}
        formatted_messages.append(message)

    # 如果是首页数据且没有skip，缓存结果
    if skip == 0 and messages:
        # 缓存消息数据
        for message in messages:
            cache_service.cache_message(session_id, {
                "id": message.id,
                "content": message.content,
                "role": message.role,
                "created_at": message.created_at.isoformat(),
                "meta_info": message.meta_info or {}
            })

    return {
        "messages": formatted_messages,
        "total": total,
        "from_cache": False
    }

@router.get("/conversations/{session_id}/messages/since/{message_id}", response_model=schemas.MessageList)
def get_new_messages_since(
    *,
    db: Session = Depends(deps.get_db),
    session_id: str,
    message_id: int,
    limit: int = 20,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取指定会话中比给定消息ID更新的所有消息
    """
    conversation = crud.crud_conversation.get_by_session_id(db, session_id=session_id)

    # 如果会话不存在或不属于当前用户，返回404
    if not conversation or conversation.user_id != current_user.id:
        raise HTTPException(status_code=404, detail="会话未找到")

    # 获取新消息
    messages = crud.crud_message.get_new_messages(
        db, conversation_id=conversation.id, last_message_id=message_id
    )

    # 如果超过limit数量,进行限制
    if limit > 0 and len(messages) > limit:
        messages = messages[:limit]

    # 确保每个消息对象的元数据字段是字典而不是None
    for message in messages:
        if message.meta_info is None:
            message.meta_info = {}

    return {
        "messages": messages,
        "total": len(messages)
    }

@router.delete("/conversations/{session_id}")
def delete_conversation(
    *,
    db: Session = Depends(deps.get_db),
    session_id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    删除会话（标记为非活跃），如果会话不存在则直接返回成功
    """
    conversation = crud.crud_conversation.get_by_session_id(db, session_id=session_id)

    # 如果会话不存在，直接返回成功
    if not conversation:
        logger.info(f"会话不存在，无需删除: {session_id}")
        return {"status": "success"}

    # 如果会话不属于当前用户，返回404
    if conversation.user_id != current_user.id:
        raise HTTPException(status_code=404, detail="会话未找到")

    crud.crud_conversation.update(
        db, db_obj=conversation, obj_in=schemas.ConversationUpdate(is_active=False)
    )

    return {"status": "success"}

# ============== WebSocket聊天接口 ==============

@router.websocket("/stream/{session_id}")
async def websocket_endpoint(
    websocket: WebSocket,
    session_id: str,
    db: Session = Depends(deps.get_db_websocket),
    current_user: models.User = Depends(deps.get_current_user_websocket),
):
    """
    WebSocket端点，用于流式传输AI助手的响应
    
    处理带有/connect后缀的路径和下划线/连字符格式的会话ID
    """
    # 处理会话ID中可能包含的/connect后缀
    if session_id.endswith("/connect"):
        session_id = session_id.replace("/connect", "")
        logger.info(f"检测到/connect后缀，已移除: {session_id}")
    
    # 标准化会话ID格式 - 将下划线格式转换为连字符格式
    normalized_session_id = normalize_session_id(session_id)
    if normalized_session_id != session_id:
        logger.info(f"已标准化会话ID格式: {session_id} -> {normalized_session_id}")
        session_id = normalized_session_id
    
    # 初始化变量
    task_manager = TaskManager()
    connection_id = f"ws_{session_id}_{int(time.time())}"
    active_task = None
    heartbeat_task = None

    try:
        await websocket.accept()

        # 验证会话
        conversation = crud.crud_conversation.get_by_session_id(db, session_id=session_id)
        
        # 如果会话不存在，尝试使用未标准化的ID
        if not conversation and normalized_session_id != session_id:
            original_session_id = session_id
            session_id = original_session_id.replace('-', '_')  # 试一下下划线格式
            logger.info(f"尝试使用下划线格式: {session_id}")
            conversation = crud.crud_conversation.get_by_session_id(db, session_id=session_id)

        # 如果会话仍然不存在，创建新会话
        if not conversation:
            conversation = crud.crud_conversation.create_with_user(
                db,
                obj_in=schemas.ConversationCreate(
                    session_id=session_id,
                    is_active=True
                ),
                user_id=current_user.id
            )
            logger.info(f"创建新会话: {session_id}")
        # 如果会话不属于当前用户，返回错误
        elif conversation.user_id != current_user.id:
            await websocket.send_json({
                "event": "error",
                "message": "未找到会话"
            })
            await websocket.close()
            return

        # 通知客户端连接成功
        await websocket.send_json({
            "event": "connected",
            "session_id": session_id
        })

        # 设置WebSocket心跳定时器，防止连接过早中断
        heartbeat_task = asyncio.create_task(send_heartbeats(websocket))
        
        # 主消息循环
        while True:
            try:
                # 设置接收超时
                data = await asyncio.wait_for(
                    websocket.receive_json(),
                    timeout=30.0  # 30秒超时
                )
                
                # 取消之前的任务（如果有）
                if active_task and not active_task.done():
                    active_task.cancel()
                    try:
                        await active_task
                    except asyncio.CancelledError:
                        logger.info(f"已取消上一个任务: {session_id}")
                    except Exception as e:
                        logger.error(f"取消任务时出错: {str(e)}")
                
                # 创建新任务来处理消息
                active_task = asyncio.create_task(
                    process_websocket_message(
                        websocket=websocket,
                        conversation=conversation,
                        data=data,
                        db=db,
                        current_user=current_user,
                        llm_proxy_service=llm_proxy_service
                    )
                )
                
                # 注册任务到任务管理器
                task_manager.register_task(
                    conversation_id=session_id,
                    task=active_task,
                    description=f"处理WebSocket消息: {data.get('message', '')[:20]}..."
                )
            
            except asyncio.TimeoutError:
                # 接收超时，发送心跳包以保持连接
                try:
                    await websocket.send_json({
                        "event": "heartbeat",
                        "time": time.time()
                    })
                except Exception as e:
                    logger.error(f"发送心跳包失败，连接可能已关闭: {str(e)}")
                    break
            
            except json.JSONDecodeError:
                # JSON解析错误
                await websocket.send_json({
                    "event": "error",
                    "message": "消息格式错误，无法解析JSON数据"
                })
            
            except Exception as e:
                # 其他错误
                logger.error(f"处理WebSocket消息时出错: {str(e)}", exc_info=True)
                await websocket.send_json({
                    "event": "error",
                    "message": f"处理消息时出错: {str(e)}"
                })
    
    except WebSocketDisconnect:
        # 客户端断开连接
        logger.info(f"WebSocket连接断开: {session_id}")
    
    except Exception as e:
        # 处理其他异常
        logger.error(f"WebSocket处理错误: {str(e)}", exc_info=True)
        try:
            await websocket.send_json({
                "event": "error",
                "message": f"服务器错误: {str(e)}"
            })
        except:
            # 如果发送失败，忽略
            pass
    
    finally:
        # 清理资源
        # 1. 取消心跳任务
        if heartbeat_task and not heartbeat_task.done():
            heartbeat_task.cancel()
            try:
                await heartbeat_task
            except (asyncio.CancelledError, Exception):
                pass
        
        # 2. 取消并等待活动任务完成
        if active_task and not active_task.done():
            active_task.cancel()
            try:
                await active_task
            except (asyncio.CancelledError, Exception):
                pass
        
        # 3. 移除任务管理器中的任务
        task_manager.remove_tasks_by_session_id(session_id)
        
        # 4. 尝试关闭WebSocket连接（如果尚未关闭）
        try:
            await websocket.close()
        except:
            pass

# 添加WebSocket兼容性路由

@router.websocket("/stream/{session_id}/connect")
async def websocket_connect_endpoint(
    websocket: WebSocket,
    session_id: str,
    db: Session = Depends(deps.get_db_websocket),
    current_user: models.User = Depends(deps.get_current_user_websocket),
):
    """
    WebSocket连接端点，处理带有/connect后缀的请求
    
    为兼容性提供的路由，重定向到主WebSocket处理函数
    """
    logger.info(f"接收到带/connect后缀的WebSocket连接请求: {session_id}")
    await websocket_endpoint(websocket, session_id, db, current_user)

@router.websocket("/stream/{full_path:path}")
async def websocket_catchall(
    websocket: WebSocket,
    full_path: str,
    db: Session = Depends(deps.get_db_websocket),
    current_user: models.User = Depends(deps.get_current_user_websocket),
):
    """
    通配WebSocket路由，能捕获所有格式的会话ID和路径
    
    用于处理各种非标准格式的WebSocket请求
    """
    logger.info(f"接收到通配WebSocket连接请求: {full_path}")
    
    # 处理各种格式的会话ID和路径
    # 1. 移除/connect后缀
    if full_path.endswith("/connect"):
        full_path = full_path[:-8]  # 移除"/connect"
    
    # 2. 将会话ID作为session_id传给主处理函数
    session_id = full_path
    
    # 调用主要的WebSocket处理逻辑
    await websocket_endpoint(websocket, session_id, db, current_user)

# ============== WebSocket配套的HTTP接口 ==============

@router.get("/stream/{session_id}/connect", response_model=schemas.BasicResponse)
def wechat_websocket_connect(
    *,
    db: Session = Depends(deps.get_db),
    session_id: str,
    token: Optional[str] = Query(None, description="认证Token"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    微信小程序WebSocket兼容端点，用于接收WebSocket连接请求
    
    由于微信小程序环境对WebSocket的限制，提供这个HTTP端点来处理连接请求
    """
    logger.info(f"微信小程序WebSocket连接请求: {session_id}")

    # 获取或创建会话
    try:
        conversation, is_new = get_or_create_conversation(db, session_id, current_user.id)
        
        # 返回成功响应，告诉前端可以通过普通HTTP请求与后端通信
        return {
            "status": "success",
            "message": "WebSocket连接兼容模式已准备",
            "data": {"session_id": session_id}
        }
    except Exception as e:
        logger.error(f"微信小程序WebSocket连接错误: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/stream/{session_id}", response_model=schemas.BasicResponse)
def websocket_stream_info(
    *,
    db: Session = Depends(deps.get_db),
    session_id: str,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    直接访问WebSocket URL的GET请求处理

    用于处理非WebSocket的HTTP GET请求访问WebSocket URL的情况
    """
    logger.info(f"HTTP GET请求访问WebSocket URL: {session_id}")

    try:
        # 验证会话
        conversation = crud.crud_conversation.get_by_session_id(db, session_id=session_id)

        # 如果会话不存在，则创建新会话
        if not conversation:
            conversation_in = schemas.ConversationCreate(
                session_id=session_id,
                is_active=True,
            )
            conversation = crud.crud_conversation.create_with_user(
                db,
                obj_in=conversation_in,
                user_id=current_user.id
            )
            logger.info(f"为HTTP GET请求创建新会话: {session_id}")
        elif conversation.user_id != current_user.id:
            # 如果会话不属于当前用户
            logger.warning(f"HTTP GET请求: 无权访问会话 {session_id}")
            raise HTTPException(status_code=403, detail="无权访问此会话")

        # 返回成功响应，同时包含WebSocket连接说明
        return {
            "status": "success",
            "message": "这是一个WebSocket端点，请使用WebSocket协议连接",
            "data": {
                "session_id": session_id,
                "info": "要建立实时通信，请使用WebSocket协议连接此URL"
            }
        }

    except Exception as e:
        logger.error(f"HTTP GET请求处理错误: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# 接收微信小程序聊天消息的接口
@router.post("/stream/{session_id}/message", response_model=schemas.Message)
async def send_stream_message(
    *,
    db: Session = Depends(deps.get_db),
    session_id: str,
    request_data: dict = Body(None),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    发送消息到会话 - 流式处理版本

    - 该接口适用于流式处理模式
    - 客户端需要在调用此接口后，通过长轮询或WebSocket接收回复
    - 支持quick_intent参数直接指定意图，跳过意图识别步骤
    """
    logger.info(f"发送消息到会话: session_id={session_id}")

    # 获取消息内容
    if not request_data:
        raise HTTPException(status_code=400, detail="请求数据不能为空")

    message = request_data.get("message", "")
    meta_info = request_data.get("meta_info", {})
    quick_intent = request_data.get("quick_intent")  # 获取快速意图参数

    logger.debug(f"提取的消息内容: {message[:50]}...")
    logger.debug(f"元数据: {meta_info}")
    if quick_intent:
        logger.info(f"使用快速意图: {quick_intent}")

    if not message or not isinstance(message, str):
        raise HTTPException(status_code=400, detail="消息内容不能为空且必须是字符串")

    # 验证会话
    conversation = crud.crud_conversation.get_by_session_id(db, session_id=session_id)

    # 如果会话不存在，创建新会话
    if not conversation:
        try:
            logger.info(f"为用户{current_user.id}创建新会话，session_id={session_id}")
            conversation = crud.crud_conversation.create_with_session_id(
                db,
                obj_in=schemas.ConversationCreate(
                    session_id=session_id,
                    user_id=current_user.id,
                    meta_info=meta_info
                )
            )
        except Exception as e:
            logger.error(f"创建新会话出错: {str(e)}")
            raise HTTPException(status_code=500, detail=f"创建新会话出错: {str(e)}")
    # 如果会话存在但不属于当前用户，返回404
    elif conversation.user_id != current_user.id:
        raise HTTPException(status_code=404, detail="会话未找到")

    # 更新会话的最后活跃时间
    try:
        crud.crud_conversation.update_last_active(db, conversation_id=conversation.id)
    except Exception as e:
        logger.warning(f"更新会话活跃时间出错: {str(e)}")
        # 非致命错误，可以继续处理

    # 保存用户消息
    try:
        # 创建消息对象，包含conversation_id字段以满足模型验证要求
        message_create = schemas.MessageCreate(
            content=message,
            role=MessageRole.USER,
            user_id=current_user.id,
            meta_info=meta_info,  # 添加元数据
            conversation_id=conversation.id
        )
        # create_with_conversation函数内部会处理重复的conversation_id
        user_message = crud.crud_message.create_with_conversation(
            db,
            obj_in=message_create,
            conversation_id=conversation.id,
            user_id=current_user.id
        )

        # 更新meta_info以包含数据库ID
        meta_info['db_message_id'] = user_message.id

        # 处理clientMessageId替换
        if isinstance(meta_info, dict) and 'clientMessageId' in meta_info:
            meta_info['clientMessageId'] = user_message.id
    except Exception as e:
        logger.error(f"保存用户消息出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"保存用户消息出错: {str(e)}")

    # 保存用户信息供异步任务使用
    conversation_id = conversation.id
    user_id = current_user.id

    # 初始化ConversationService
    conversation_service = ConversationService(db, llm_proxy_service)

    # 在后台处理AI回复
    async def process_ai_response():
        # 创建新的数据库会话
        from app.db.session import SessionLocal
        async_db = SessionLocal()
        try:
            logger.info(f"开始处理AI回复: session_id={session_id}")

            # 获取最近的消息历史，用于确保meta_info的连续性
            recent_messages = crud.crud_message.get_conversation_messages_desc(
                async_db, conversation_id=conversation_id, skip=0, limit=10
            )

            # 获取最近的AI助手消息的meta_info，用于保持参数连续性
            last_assistant_meta_info = {}
            last_user_meta_info = {}

            # 记录最近的消息，用于调试
            logger.info(f"获取最近的{len(recent_messages)}条消息用于参数连续性")

            # 分别获取最近的AI消息和用户消息的meta_info
            for msg in recent_messages:
                if msg.role == MessageRole.ASSISTANT and msg.meta_info and not last_assistant_meta_info:
                    last_assistant_meta_info = msg.meta_info
                    logger.info(f"找到最近的AI消息: id={msg.id}, meta_info={last_assistant_meta_info}")
                elif msg.role == MessageRole.USER and msg.meta_info and not last_user_meta_info:
                    last_user_meta_info = msg.meta_info

            # 初始化元数据
            response_meta_info = meta_info.copy() if meta_info else {}

            # 保存原始意图，避免在参数收集过程中被改变
            original_intent = None
            if last_assistant_meta_info and "intent" in last_assistant_meta_info:
                original_intent = last_assistant_meta_info["intent"]
                logger.info(f"从最近的AI消息中获取原始意图: {original_intent}")

            # 使用工具函数合并元数据
            if last_assistant_meta_info:
                response_meta_info = MetaInfoManager.merge_meta_info(response_meta_info, last_assistant_meta_info, original_intent)

                # 记录关键参数
                if "collecting_training_params" in response_meta_info:
                    logger.info(f"参数收集状态: {response_meta_info['collecting_training_params']}")
                if "asking_param" in response_meta_info:
                    logger.info(f"当前询问的参数: {response_meta_info['asking_param']}")
                if "training_params" in response_meta_info:
                    logger.info(f"训练参数: {response_meta_info['training_params']}")

            # 记录合并后的meta_info状态，用于调试
            logger.info(f"合并后的meta_info状态: {response_meta_info}")

            # 标记AI消息是否已被保存
            ai_message_saved = False
            current_response = ""

            # 记录调用process_message_stream前的状态
            logger.info(f"调用process_message_stream - 用户ID: {user_id}, 会话ID: {conversation_id}")
            logger.info(f"传递的meta_info: {response_meta_info}")

            # 保存原始意图，用于后续恢复
            original_intent = response_meta_info.get("intent")

            # 流式处理消息 - 传递db_message_id以避免重复保存用户消息
            async for chunk in conversation_service.process_message_stream(
                user_id=user_id,
                message=message,
                conversation_id=conversation_id,
                meta_info=response_meta_info,  # 使用合并后的meta_info
                quick_intent=quick_intent  # 传递快速意图参数
            ):
                # 如果chunk是字典类型，说明它包含元数据更新或特殊指令
                if isinstance(chunk, dict):
                    # 处理消息类型的响应
                    if chunk.get("type") == "message":
                        # 收集消息内容
                        message_content = chunk.get("content", "")
                        current_response = message_content

                        # 如果消息中包含元数据，更新元数据
                        if "meta_info" in chunk:
                            chunk_meta_info = chunk["meta_info"]
                            logger.info(f"从消息中获取的meta_info: {chunk_meta_info}")

                            # 使用工具函数合并元数据
                            response_meta_info = MetaInfoManager.merge_meta_info(response_meta_info, chunk_meta_info, original_intent)

                        # 记录消息内容，以便在流程结束时保存
                        logger.info(f"收集消息响应: {message_content[:50]}...")
                        continue  # 跳过添加到响应文本

                    # 处理元数据更新
                    if "meta_info_update" in chunk:
                        update_meta_info = chunk["meta_info_update"]
                        logger.info(f"收到meta_info更新: {update_meta_info}")

                        # 保存当前训练参数和llm_params，避免被覆盖
                        current_training_params = response_meta_info.get("training_params", {}).copy()
                        current_llm_params = response_meta_info.get("llm_params", {}).copy()

                        # 特别处理llm_params字段
                        if "llm_params" in update_meta_info:
                            # 确保response_meta_info中有llm_params字段
                            if "llm_params" not in response_meta_info:
                                response_meta_info["llm_params"] = {}

                            # 合并llm_params，而不是覆盖
                            for key, value in update_meta_info["llm_params"].items():
                                response_meta_info["llm_params"][key] = value
                                logger.info(f"更新llm_params: {key}={value}")

                            # 从update_meta_info中移除llm_params，避免下面的代码再次处理
                            del update_meta_info["llm_params"]

                        # 更新元数据
                        for key, value in update_meta_info.items():
                            if key == "training_params" and isinstance(value, dict):
                                # 特殊处理训练参数，确保正确合并而不是覆盖
                                if "training_params" not in response_meta_info:
                                    response_meta_info["training_params"] = {}

                                # 合并训练参数，保留所有非空值
                                for param_key, param_value in value.items():
                                    # 只有当新值非空或当前值为空时才更新
                                    if param_value or not current_training_params.get(param_key):
                                        response_meta_info["training_params"][param_key] = param_value
                                        logger.info(f"更新参数: {param_key}={param_value}")
                            elif key == "intent" and original_intent:
                                # 如果有原始意图，保持不变
                                response_meta_info[key] = original_intent
                                logger.info(f"保持原始意图: {original_intent}，忽略新意图: {value}")
                            else:
                                # 其他元数据直接更新
                                response_meta_info[key] = value

                        # 确保body_part参数不会被清空
                        if ("training_params" in response_meta_info and
                            "body_part" in current_training_params and
                            current_training_params["body_part"] and
                            (not response_meta_info["training_params"].get("body_part"))):
                            response_meta_info["training_params"]["body_part"] = current_training_params["body_part"]
                            logger.info(f"恢复body_part参数: {current_training_params['body_part']}")

                        # 确保llm_params不会丢失
                        if "llm_params" not in response_meta_info:
                            response_meta_info["llm_params"] = current_llm_params
                        elif current_llm_params:
                            # 合并llm_params，确保不会丢失信息
                            for key, value in current_llm_params.items():
                                if key not in response_meta_info["llm_params"]:
                                    response_meta_info["llm_params"][key] = value
                                    logger.info(f"恢复llm_params: {key}={value}")

                    # 检查是否已保存 AI 消息的标记
                    if "ai_message_saved" in chunk and chunk["ai_message_saved"] is True:
                        ai_message_saved = True
                        logger.info("AI 消息已在 process_message_stream 中保存")
                    continue  # 跳过添加到响应文本

                # 累积文本响应
                current_response += chunk

            # 检查是否在参数收集状态，如果是，即使没有响应也不返回错误消息
            is_collecting_params = (response_meta_info and
                                   (response_meta_info.get("collecting_training_params", False) or
                                    response_meta_info.get("waiting_for_info") is not None))

            # 检查是否有任何响应片段被处理过
            any_response_processed = hasattr(conversation_service, 'response_processed') and conversation_service.response_processed

            # 增加对空响应的更严格检查
            if not current_response and not is_collecting_params:
                # 检查是否有任何响应片段被处理
                if not any_response_processed:
                    current_response = "抱歉，AI助手没有返回有效回复。请稍后再试。"
                    logger.warning("未检测到任何响应片段，设置默认错误消息")
                else:
                    # 如果处理了响应片段但最终响应为空，可能是累积出错
                    logger.warning("检测到响应片段但最终响应为空，可能是累积出错")
                    current_response = "AI助手已生成回复，但在处理过程中出现了问题。"
            elif not current_response and is_collecting_params:
                # 在参数收集状态下，如果没有响应，使用一个空字符串而不是错误消息
                current_response = ""
                logger.info("AI助手处于参数收集状态，但没有生成文本响应")

            logger.info(f"AI回复已生成: {current_response[:50]}...")
            logger.debug(f"最终响应元数据: {response_meta_info}")

            # 检查元数据中是否包含训练计划ID并记录
            if (response_meta_info and "training_params" in response_meta_info and
                "related_plan_id" in response_meta_info["training_params"]):
                plan_id = response_meta_info["training_params"]["related_plan_id"]
                logger.info(f"响应包含训练计划ID: {plan_id}")

                # 获取完整的训练计划数据
                sql_tool_service = SQLToolService(async_db)
                training_plan_service = TrainingPlanService(async_db, llm_proxy_service, sql_tool_service)
                try:
                    training_plan_data = training_plan_service.get_enhanced_plan(plan_id)

                    # 确保训练计划数据中的每个动作包含所有必要字段
                    if "workouts" in training_plan_data:
                        # 完整训练计划
                        for workout in training_plan_data["workouts"]:
                            if "exercises" in workout:
                                for exercise in workout["exercises"]:
                                    _ensure_exercise_fields(exercise)
                        logger.info(f"已确保完整训练计划中所有动作包含必要字段")
                    elif "workout_exercises" in training_plan_data:
                        # 单日训练计划
                        for exercise in training_plan_data["workout_exercises"]:
                            _ensure_exercise_fields(exercise)
                        logger.info(f"已确保单日训练计划中所有动作包含必要字段")
                    elif "exercises" in training_plan_data:
                        # 兼容旧版单日训练计划
                        for exercise in training_plan_data["exercises"]:
                            _ensure_exercise_fields(exercise)
                        logger.info(f"已确保旧版单日训练计划中所有动作包含必要字段")

                    # 将完整的训练计划数据添加到元数据中
                    response_meta_info["complete_training_plan"] = training_plan_data
                    logger.info(f"已将完整的训练计划数据添加到元数据中，准备存储到数据库")
                except Exception as e:
                    logger.error(f"获取训练计划数据时出错: {str(e)}")
                    # 继续处理，不中断流程

            # 保存AI回复
            if not ai_message_saved:
                # 创建消息对象，包含conversation_id字段以满足模型验证要求
                message_create = schemas.MessageCreate(
                    content=current_response,
                    role=MessageRole.ASSISTANT,
                    user_id=user_id,
                    meta_info=response_meta_info,  # 使用最终检查后的meta_info
                    conversation_id=conversation_id
                )

                # 保存AI回复
                ai_message = crud.crud_message.create_with_conversation(
                    async_db,
                    obj_in=message_create,
                    conversation_id=conversation_id,
                    user_id=user_id
                )
                logger.info(f"AI回复已保存到数据库: id={ai_message.id}")
            else:
                # 即使消息已经保存，也需要更新元数据
                # 获取最新的AI消息并更新其元数据
                latest_ai_message = crud.crud_message.get_latest_assistant_message_by_conversation_id(
                    async_db, conversation_id=conversation_id
                )
                if latest_ai_message:
                    # 更新元数据
                    crud.crud_message.update(
                        async_db,
                        db_obj=latest_ai_message,
                        obj_in={"meta_info": response_meta_info}
                    )
                    logger.info(f"已更新现有AI消息的元数据: id={latest_ai_message.id}")
                else:
                    logger.info("跳过AI回复保存，因为它已经在 process_message_stream 中保存")

            # 记录QA对
            try:
                chat_log_service = ChatLogService(async_db)
                await chat_log_service.log_interaction(
                    user_id=user_id,
                    session_id=session_id,
                    question=message,
                    answer=current_response
                )
            except Exception as e:
                logger.error(f"记录QA对时出错: {str(e)}")

            logger.info(f"AI回复已处理完成: session_id={session_id}")
        except Exception as e:
            logger.error(f"处理AI回复时出错: {str(e)}")
        finally:
            # 关闭异步任务创建的数据库会话
            async_db.close()

    # 启动后台任务处理AI回复
    asyncio.create_task(process_ai_response())

    # 返回用户消息
    return user_message

@router.get("/stream/{session_id}/poll", response_model=schemas.MessageList)
def poll_new_messages(
    *,
    db: Session = Depends(deps.get_db),
    session_id: str,
    last_message_id: Optional[str] = Query(None, description="上次接收到的最后一条消息的ID"),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    轮询获取会话中的新消息，用于不支持WebSocket的环境

    客户端可以定期调用此端点获取自上次轮询以来的新消息
    """
    # 转换last_message_id为整数，如果为空或无效则默认为0
    try:
        message_id = int(last_message_id) if last_message_id else 0
    except (ValueError, TypeError):
        logger.warning(f"轮询消息: 无效的last_message_id值 '{last_message_id}'，已重置为0")
        message_id = 0

    logger.info(f"轮询消息: session_id={session_id}, last_message_id={message_id}")

    # 验证会话
    conversation = crud.crud_conversation.get_by_session_id(db, session_id=session_id)

    # 如果会话不存在或不属于当前用户，返回404
    if not conversation or conversation.user_id != current_user.id:
        raise HTTPException(status_code=404, detail="会话未找到")

    # 获取新消息
    messages = crud.crud_message.get_new_messages(
        db, conversation_id=conversation.id, last_message_id=message_id
    )

    # 确保每个消息对象的元数据字段是字典而不是None
    # 过滤掉空内容的消息，只保留有内容的消息
    filtered_messages = []
    for message in messages:
        if message.meta_info is None:
            message.meta_info = {}

        # 只保留有内容的消息
        if message.content:
            filtered_messages.append(message)

    # 使用过滤后的消息列表
    messages = filtered_messages

    # 更新会话的最后活跃时间
    if messages:
        crud.crud_conversation.update_last_active(db, conversation_id=conversation.id)

    return {
        "messages": messages,
        "total": len(messages)
    }

# ============== 用户信息与历史管理接口 ==============

@router.post("/update_user_info", response_model=schemas.ChatResponse)
async def update_user_info(
    *,
    db: Session = Depends(deps.get_db),
    update_request: schemas.UserInfoUpdateRequest = Body(...),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    更新用户信息并继续对话
    
    用于处理信息收集过程中用户提供的信息
    """
    # 验证会话
    conversation = crud.crud_conversation.get_by_session_id(db, session_id=update_request.session_id)
    if not conversation or conversation.user_id != current_user.id:
        raise HTTPException(status_code=404, detail="会话未找到")

    # 更新会话的最后活跃时间
    crud.crud_conversation.update_last_active(db, conversation_id=conversation.id)

    # 保存用户消息
    user_message = await create_user_message(
        db,
        conversation.id,
        current_user.id,
        update_request.value_text,  # 用户输入的原始文本
        {"field_update": update_request.field}
    )

    # 使用ConversationService处理用户信息更新
    conversation_service = ConversationService(db, llm_proxy_service)
    response, meta_info = await conversation_service.handle_user_info_update(
        user_id=current_user.id,
        field=update_request.field,
        value=update_request.value,
        conversation_id=conversation.id
    )

    # 保存AI回复
    await create_assistant_message(
        db,
        conversation.id,
        current_user.id,
        response,
        meta_info
    )

    return {
        "response": response,
        "session_id": conversation.session_id,
        "meta_info": meta_info
    }

@router.get("/recent-messages", response_model=schemas.RecentMessageList)
def get_recent_messages(
    *,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 5,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取用户所有会话中最近的消息，按时间倒序排序
    
    用于前端启动时预加载距离当前时间最近的5条历史对话，支持下拉加载更多
    返回消息及其所属的会话信息，便于前端展示和处理
    """
    logger.info(f"获取用户 {current_user.id} 的最近消息: skip={skip}, limit={limit}")

    # 获取用户所有会话中最近的消息
    messages = crud.crud_message.get_recent_messages_by_user(
        db, user_id=current_user.id, skip=skip, limit=limit
    )

    # 确保每个消息对象的元数据字段是字典而不是None
    # 同时收集消息所属的会话ID列表
    formatted_messages = []
    conversation_ids = set()
    for message in messages:
        if message.meta_info is None:
            message.meta_info = {}
        formatted_messages.append(message)
        conversation_ids.add(message.conversation_id)

    # 获取相关会话信息
    conversations = {}
    for conv_id in conversation_ids:
        conversation = crud.crud_conversation.get(db, id=conv_id)
        if conversation:
            conversations[str(conv_id)] = {
                "id": conversation.id,
                "session_id": conversation.session_id,
                "start_time": conversation.start_time,
                "last_active": conversation.last_active,
                "is_active": conversation.is_active,
                "meta_info": conversation.meta_info or {},
                "created_at": conversation.start_time,  # 使用start_time替代created_at
                "updated_at": conversation.last_active  # 使用last_active替代updated_at
            }

    # 计算用户所有消息的总数，这里不缓存结果，而是每次都计算
    total = db.query(models.Message).filter(models.Message.user_id == current_user.id).count()

    return {
        "messages": formatted_messages,
        "conversations": conversations,
        "total": total
    }

@router.get("/recent-conversations", response_model=schemas.RecentMessageList)
def get_recent_conversations(
    *,
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 5,  # 默认获取5条
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取用户所有会话中最近的消息，不区分session_id，按时间降序排序
    
    用于前端启动时预加载对话记录，支持下拉刷新增量加载更多历史对话
    """
    # 获取用户所有会话中最近的消息
    messages, conversations = crud.crud_message.get_recent_messages_with_conversations(
        db, user_id=current_user.id, skip=skip, limit=limit
    )

    # 确保每个消息对象的元数据字段是字典而不是None
    for message in messages:
        if message.meta_info is None:
            message.meta_info = {}

    # 将conversations列表转换为字典，key为id，value为会话简要信息
    conversations_dict = {}
    for conv in conversations:
        conversations_dict[conv.id] = {
            "id": conv.id,
            "title": getattr(conv, 'title', None),
            "created_at": conv.start_time,  # 使用start_time替代created_at
            "updated_at": conv.last_active  # 使用last_active替代updated_at
        }

    return {
        "messages": messages,
        "conversations": conversations_dict,
        "total": len(messages)
    }

@router.post("/sessions/{session_id}/messages", response_model=schemas.Message)
def add_message(
    *,
    db: Session = Depends(deps.get_db),
    session_id: str,
    message_data: schemas.MessageBase = Body(...),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    向指定的会话中添加消息
    
    允许指定消息内容、角色和元数据
    """
    logger.info(f"向会话添加消息: session_id={session_id}, role={message_data.role}")

    # 获取或创建会话
    conversation, is_new = get_or_create_conversation(db, session_id, current_user.id)
    
    # 创建消息对象
    obj_in_data = {
        "content": message_data.content,
        "role": message_data.role,
        "meta_info": message_data.metadata,
        "conversation_id": conversation.id,
        "user_id": current_user.id
    }
    message = crud.crud_message.create(db, obj_in=schemas.MessageCreate(**obj_in_data))

    # 缓存消息
    cache_service.cache_message(conversation.session_id, {
        "id": message.id,
        "content": message.content,
        "role": message.role,
        "created_at": message.created_at.isoformat(),
        "meta_info": message.meta_info or {}
    })

    return message

@router.put("/messages/{message_id}", response_model=schemas.Message)
def update_message(
    *,
    db: Session = Depends(deps.get_db),
    message_id: int,
    message_update: schemas.MessageUpdate = Body(...),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    更新现有消息的内容、角色或元数据
    """
    logger.info(f"更新消息: message_id={message_id}")

    # 获取消息
    message = crud.crud_message.get(db, id=message_id)

    # 如果消息不存在，返回404
    if not message:
        raise HTTPException(status_code=404, detail="消息未找到")

    # 获取消息所属的会话
    conversation = crud.crud_conversation.get(db, id=message.conversation_id)

    # 检查权限: 消息必须属于当前用户
    if message.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="无权更新此消息")

    # 更新消息
    updated_message = crud.crud_message.update(db, db_obj=message, obj_in=message_update)

    # 如果消息已被缓存，更新缓存
    if conversation:
        cache_service.cache_message(conversation.session_id, {
            "id": updated_message.id,
            "content": updated_message.content,
            "role": updated_message.role,
            "created_at": updated_message.created_at.isoformat(),
            "updated_at": updated_message.updated_at.isoformat(),
            "meta_info": updated_message.meta_info or {}
        })

    return updated_message

# ============== 通用WebSocket接口 ==============

@router.websocket("/ws/chat")
async def websocket_chat(
    websocket: WebSocket,
    db: Session = Depends(deps.get_db_websocket)  # 使用正确的WebSocket数据库依赖
):
    """
    通用WebSocket聊天端点，不需要登录和会话ID
    
    用于简单的对话测试和集成场景
    """
    try:
        await websocket.accept()

        while True:
            try:
                # 接收消息数据
                data = await websocket.receive_json()
                
                # 验证消息格式
                if not isinstance(data, dict) or "message" not in data:
                    await websocket.send_json({
                        "event": "error",
                        "data": {"message": "无效的消息格式"}
                    })
                    continue
                
                # 获取消息内容和元数据
                message = data["message"]
                meta_info = data.get("meta_info", {})
                quick_intent = data.get("quick_intent")
                
                # 处理消息
                conversation_service = ConversationService(db, llm_proxy_service)
                
                # 流式处理消息
                complete_response = ""
                final_meta_info = {}
                
                try:
                    # 使用process_message方法而不是流式处理，简化实现
                    response, meta_info = await conversation_service.process_message(
                        message=message,
                        meta_info=meta_info,
                        quick_intent=quick_intent
                    )
                    
                    # 发送文本响应
                    await websocket.send_json({
                        "event": "message",
                        "data": {
                            "message": response,
                            "meta_info": meta_info
                        }
                    })
                    
                    # 检查训练计划ID
                    if (meta_info and "training_params" in meta_info and
                        "related_plan_id" in meta_info["training_params"]):
                        plan_id = meta_info["training_params"]["related_plan_id"]
                        
                        # 处理训练计划数据
                        plan_data = process_training_plan_data(db, plan_id, llm_proxy_service)
                        if plan_data:
                            await websocket.send_json({
                                "event": "training_plan",
                                "data": plan_data
                            })
                            
                except Exception as e:
                    logger.error(f"处理WebSocket消息失败: {str(e)}", exc_info=True)
                    await websocket.send_json({
                        "event": "error",
                        "data": {
                            "message": "处理消息时发生错误",
                            "error": str(e)
                        }
                    })

            except json.JSONDecodeError:
                await websocket.send_json({
                    "event": "error",
                    "data": {"message": "无效的JSON格式"}
                })
            except Exception as e:
                logger.error(f"处理WebSocket消息时出错: {str(e)}", exc_info=True)
                try:
                    await websocket.send_json({
                        "event": "error",
                        "data": {"message": f"处理消息时出错: {str(e)}"}
                    })
                except:
                    # 如果发送失败，可能连接已经断开
                    break

    except WebSocketDisconnect:
        logger.info("WebSocket连接已关闭")
    except Exception as e:
        logger.error(f"WebSocket连接错误: {str(e)}", exc_info=True)

@router.post("/chat", response_model=ChatResponse, status_code=status.HTTP_200_OK)
async def chat(message: ChatMessage) -> Dict[str, Any]:
    """
    与AI助手对话
    
    接收用户消息，返回AI助手的响应。
    """
    try:
        # 记录API调用
        logger.info(f"Chat API called with message: {message.message[:50]}...")
        
        # 处理消息
        response = await conversation_orchestrator.process_message(
            user_input=message.message,
            conversation_id=message.conversation_id,
            user_id=message.user_id
        )
        
        # 记录API调用完成
        logger.info(f"Chat API completed for conversation: {response.get('conversation_id')}")
        
        return response
        
    except Exception as e:
        # 记录错误
        logger.error(f"Error in chat API: {str(e)}")
        
        # 返回错误响应
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"处理消息时发生错误: {str(e)}"
        )

@router.get("/conversations/{conversation_id}", status_code=status.HTTP_200_OK)
async def get_conversation(conversation_id: str) -> Dict[str, Any]:
    """
    获取对话历史
    
    根据对话ID获取对话历史记录。
    """
    try:
        # 记录API调用
        logger.info(f"Get conversation API called for conversation: {conversation_id}")
        
        # 检查对话是否存在
        if conversation_id not in conversation_orchestrator.conversations:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"对话 {conversation_id} 不存在"
            )
        
        # 获取对话历史
        conversation = conversation_orchestrator.conversations[conversation_id]
        
        # 记录API调用完成
        logger.info(f"Get conversation API completed for conversation: {conversation_id}")
        
        return {
            "conversation_id": conversation_id,
            "messages": conversation.get("messages", []),
            "state": conversation.get("state", "unknown"),
            "created_at": conversation.get("created_at"),
            "updated_at": conversation.get("updated_at")
        }
        
    except HTTPException:
        # 重新抛出HTTP异常
        raise
        
    except Exception as e:
        # 记录错误
        logger.error(f"Error in get conversation API: {str(e)}")
        
        # 返回错误响应
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取对话历史时发生错误: {str(e)}"
        )