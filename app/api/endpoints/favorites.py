from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app.api.deps import get_db, get_current_user
from app.services.favorite import FavoriteService
from app.models.user import User

router = APIRouter()

@router.post("/exercises/{exercise_id}/favorite")
async def toggle_favorite_exercise(
    exercise_id: int,
    notes: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    添加或取消收藏健身动作
    """
    result = FavoriteService.toggle_favorite_exercise(
        db=db,
        user_id=current_user.id,
        exercise_id=exercise_id,
        notes=notes
    )
    return result

@router.post("/foods/{food_id}/favorite")
async def toggle_favorite_food(
    food_id: int,
    notes: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    添加或取消收藏食物
    """
    result = FavoriteService.toggle_favorite_food(
        db=db,
        user_id=current_user.id,
        food_id=food_id,
        notes=notes
    )
    return result

@router.get("/exercises/favorites")
async def get_favorite_exercises(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> List[Dict[str, Any]]:
    """
    获取用户收藏的健身动作列表
    """
    favorites = FavoriteService.get_user_favorite_exercises(
        db=db,
        user_id=current_user.id,
        skip=skip,
        limit=limit
    )
    return favorites

@router.get("/foods/favorites")
async def get_favorite_foods(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> List[Dict[str, Any]]:
    """
    获取用户收藏的食物列表
    """
    favorites = FavoriteService.get_user_favorite_foods(
        db=db,
        user_id=current_user.id,
        skip=skip,
        limit=limit
    )
    return favorites

@router.get("/exercises/{exercise_id}/is-favorite")
async def check_exercise_is_favorite(
    exercise_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, bool]:
    """
    检查健身动作是否被收藏
    """
    is_favorite = FavoriteService.is_exercise_favorite(
        db=db,
        user_id=current_user.id,
        exercise_id=exercise_id
    )
    return {"is_favorite": is_favorite}

@router.get("/foods/{food_id}/is-favorite")
async def check_food_is_favorite(
    food_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
) -> Dict[str, bool]:
    """
    检查食物是否被收藏
    """
    is_favorite = FavoriteService.is_food_favorite(
        db=db,
        user_id=current_user.id,
        food_id=food_id
    )
    return {"is_favorite": is_favorite} 