from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.services.gamification import TaskService
from app.schemas.gamification import (
    TaskResponse, UserTaskResponse, DailyCheckInResponse,
    TaskUpdateRequest
)

router = APIRouter()


@router.get("/all", response_model=List[TaskResponse])
async def get_all_tasks(
    skip: int = Query(0, description="Skip first N items"),
    limit: int = Query(100, description="Limit the number of items returned"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取所有任务
    """
    tasks = await TaskService.get_all_tasks(db, skip=skip, limit=limit)
    return tasks


@router.get("/by-type/{task_type}", response_model=List[TaskResponse])
async def get_tasks_by_type(
    task_type: str = Path(..., description="任务类型，例如：DAILY, WEEKLY, CHALLENGE"),
    skip: int = Query(0, description="Skip first N items"),
    limit: int = Query(50, description="Limit the number of items returned"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    根据任务类型获取任务列表
    """
    tasks = await TaskService.get_tasks_by_type(db, task_type=task_type, skip=skip, limit=limit)
    return tasks


@router.get("/by-category/{category}", response_model=List[TaskResponse])
async def get_tasks_by_category(
    category: str = Path(..., description="任务类别，例如：EXERCISE, DIET, SOCIAL"),
    skip: int = Query(0, description="Skip first N items"),
    limit: int = Query(50, description="Limit the number of items returned"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    根据任务类别获取任务列表
    """
    tasks = await TaskService.get_tasks_by_category(db, category=category, skip=skip, limit=limit)
    return tasks


@router.get("/active", response_model=List[UserTaskResponse])
async def get_user_active_tasks(
    skip: int = Query(0, description="Skip first N items"),
    limit: int = Query(50, description="Limit the number of items returned"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取当前用户未完成的任务
    """
    tasks = await TaskService.get_user_active_tasks(db, user_id=current_user.id, skip=skip, limit=limit)
    return tasks


@router.get("/completed", response_model=List[UserTaskResponse])
async def get_user_completed_tasks(
    skip: int = Query(0, description="Skip first N items"),
    limit: int = Query(50, description="Limit the number of items returned"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取当前用户已完成的任务
    """
    tasks = await TaskService.get_user_completed_tasks(db, user_id=current_user.id, skip=skip, limit=limit)
    return tasks


@router.post("/generate-daily", response_model=List[Dict[str, Any]])
async def generate_daily_tasks(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    为当前用户生成每日任务
    """
    # 获取用户等级信息
    from app.services.gamification import LevelService
    user_level = await LevelService.get_user_level(db, user_id=current_user.id)
    
    user_level_type = "EXERCISE"
    user_level_value = 1
    
    if user_level:
        # 使用较高的等级生成任务
        if user_level.exercise_level >= user_level.diet_level:
            user_level_type = "EXERCISE"
            user_level_value = user_level.exercise_level
        else:
            user_level_type = "DIET"
            user_level_value = user_level.diet_level
    
    tasks = await TaskService.generate_daily_tasks(
        db, 
        user_id=current_user.id,
        user_level_type=user_level_type,
        user_level=user_level_value
    )
    
    return tasks


@router.post("/generate-weekly", response_model=List[Dict[str, Any]])
async def generate_weekly_tasks(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    为当前用户生成每周任务
    """
    # 获取用户等级信息
    from app.services.gamification import LevelService
    user_level = await LevelService.get_user_level(db, user_id=current_user.id)
    
    user_level_type = "EXERCISE"
    user_level_value = 1
    
    if user_level:
        # 使用较高的等级生成任务
        if user_level.exercise_level >= user_level.diet_level:
            user_level_type = "EXERCISE"
            user_level_value = user_level.exercise_level
        else:
            user_level_type = "DIET"
            user_level_value = user_level.diet_level
    
    tasks = await TaskService.generate_weekly_tasks(
        db, 
        user_id=current_user.id,
        user_level_type=user_level_type,
        user_level=user_level_value
    )
    
    return tasks


@router.post("/update-progress", response_model=Dict[str, Any])
async def update_task_progress(
    task_data: TaskUpdateRequest,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    更新任务进度
    """
    try:
        user_task, just_completed, task_obj = await TaskService.update_task_progress(
            db, 
            user_id=current_user.id, 
            task_id=task_data.task_id, 
            progress_increment=task_data.progress_increment
        )
        
        return {
            "success": True,
            "just_completed": just_completed,
            "progress": user_task.progress,
            "requirement": task_obj.requirement_value,
            "message": "进度更新成功" + ("，任务已完成！" if just_completed else "")
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/claim/{task_id}", response_model=Dict[str, Any])
async def claim_task_reward(
    task_id: int = Path(..., description="任务ID"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    领取任务奖励
    """
    success, rewards_info = await TaskService.claim_task_reward(
        db, user_id=current_user.id, task_id=task_id
    )
    
    if not success:
        raise HTTPException(status_code=400, detail=rewards_info.get("message", "领取失败"))
    
    return rewards_info


@router.get("/check-daily-checkin", response_model=Dict[str, Any])
async def check_daily_checkin(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    检查今日是否已签到
    """
    is_checked, checkin = await TaskService.check_daily_checkin(db, user_id=current_user.id)
    
    return {
        "has_checked_in": is_checked,
        "checkin": checkin
    }


@router.post("/daily-checkin", response_model=Dict[str, Any])
async def create_daily_checkin(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    创建签到记录并领取奖励
    """
    # 检查是否已签到
    is_checked, existing_checkin = await TaskService.check_daily_checkin(db, user_id=current_user.id)
    if is_checked:
        return {
            "success": False,
            "message": "今日已签到",
            "checkin": existing_checkin
        }
    
    # 签到并获取奖励
    checkin, is_streak, rewards_info = await TaskService.create_daily_checkin(
        db, user_id=current_user.id
    )
    
    return {
        "success": True,
        "checkin": checkin,
        "is_streak": is_streak,
        "streak_count": checkin.streak_count,
        "rewards": rewards_info.get("rewards", {}),
        "message": rewards_info.get("message", "签到成功")
    }


@router.get("/checkin-history", response_model=List[DailyCheckInResponse])
async def get_checkin_history(
    skip: int = Query(0, description="Skip first N items"),
    limit: int = Query(30, description="Limit the number of items returned"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取签到历史记录
    """
    history = await TaskService.get_user_checkin_history(
        db, user_id=current_user.id, skip=skip, limit=limit
    )
    return history


@router.post("/trigger-event/{event_type}", response_model=Dict[str, Any])
async def trigger_task_event(
    event_type: str = Path(..., description="事件类型"),
    event_data: Optional[Dict[str, Any]] = None,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    触发任务进度更新事件
    """
    if event_data is None:
        event_data = {}
    
    task_results = await TaskService.check_and_trigger_tasks(
        db, user_id=current_user.id, event_type=event_type, event_data=event_data
    )
    
    return {
        "success": True,
        "event_type": event_type,
        "tasks_updated": task_results
    } 