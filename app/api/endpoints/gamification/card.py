from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.services.gamification import CardService
from app.schemas.gamification import (
    CardResponse, UserCardResponse, CardSynthesisRecipeResponse, 
    CardSynthesisIngredientResponse, CardEffectsResponse
)

router = APIRouter()


@router.get("/all", response_model=List[CardResponse])
async def get_all_cards(
    skip: int = Query(0, description="Skip first N items"),
    limit: int = Query(100, description="Limit the number of items returned"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取所有卡片的基本信息
    """
    cards = await CardService.get_all_cards(db, skip=skip, limit=limit)
    return cards


@router.get("/by-type/{card_type}", response_model=List[CardResponse])
async def get_cards_by_type(
    card_type: str = Path(..., description="卡片类型，例如：FOOD, EQUIPMENT"),
    skip: int = Query(0, description="Skip first N items"),
    limit: int = Query(100, description="Limit the number of items returned"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    根据卡片类型获取卡片列表
    """
    cards = await CardService.get_cards_by_type(db, card_type=card_type, skip=skip, limit=limit)
    return cards


@router.get("/detail/{card_id}", response_model=CardResponse)
async def get_card_detail(
    card_id: int = Path(..., description="卡片ID"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取特定卡片的详细信息
    """
    card = await CardService.get_card_by_id(db, card_id=card_id)
    if not card:
        raise HTTPException(status_code=404, detail="卡片不存在")
    
    return card


@router.get("/user-cards", response_model=List[UserCardResponse])
async def get_user_cards(
    skip: int = Query(0, description="Skip first N items"),
    limit: int = Query(100, description="Limit the number of items returned"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取当前用户拥有的所有卡片
    """
    user_cards = await CardService.get_user_cards(db, user_id=current_user.id, skip=skip, limit=limit)
    return user_cards


@router.get("/equipped", response_model=List[UserCardResponse])
async def get_equipped_cards(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取当前用户已装备的卡片
    """
    equipped_cards = await CardService.get_equipped_cards(db, user_id=current_user.id)
    return equipped_cards


@router.get("/effects", response_model=CardEffectsResponse)
async def get_card_effects(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取当前用户已装备卡片的综合效果
    """
    effects = await CardService.get_card_effects(db, user_id=current_user.id)
    return effects


@router.post("/toggle-equip/{card_id}", response_model=UserCardResponse)
async def toggle_card_equip_status(
    card_id: int = Path(..., description="卡片ID"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    切换卡片的装备状态（装备或取消装备）
    """
    # 检查卡片是否存在且属于当前用户
    user_cards = await CardService.get_user_cards(db, user_id=current_user.id)
    user_card_ids = [uc.card_id for uc in user_cards]
    
    if card_id not in user_card_ids:
        raise HTTPException(status_code=404, detail="卡片不存在或不属于当前用户")
    
    updated_card = await CardService.toggle_equip_status(db, user_id=current_user.id, card_id=card_id)
    if not updated_card:
        raise HTTPException(status_code=400, detail="切换卡片装备状态失败")
    
    return updated_card


@router.get("/synthesis-recipes", response_model=List[CardSynthesisRecipeResponse])
async def get_synthesis_recipes(
    skip: int = Query(0, description="Skip first N items"),
    limit: int = Query(100, description="Limit the number of items returned"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取所有可用的卡片合成配方
    """
    recipes = await CardService.get_all_synthesis_recipes(db, skip=skip, limit=limit)
    return recipes


@router.get("/synthesis-recipe/{recipe_id}", response_model=Dict[str, Any])
async def get_synthesis_recipe_detail(
    recipe_id: int = Path(..., description="合成配方ID"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取特定合成配方的详细信息，包括配方原料
    """
    recipe, ingredients = await CardService.get_synthesis_recipe_with_ingredients(db, recipe_id=recipe_id)
    if not recipe:
        raise HTTPException(status_code=404, detail="合成配方不存在")
    
    # 获取结果卡片信息
    result_card = await CardService.get_card_by_id(db, card_id=recipe.result_card_id)
    
    # 获取每个原料卡片的详细信息
    ingredient_details = []
    for ingredient in ingredients:
        card = await CardService.get_card_by_id(db, card_id=ingredient.card_id)
        ingredient_details.append({
            "ingredient": ingredient,
            "card": card
        })
    
    return {
        "recipe": recipe,
        "result_card": result_card,
        "ingredients": ingredient_details
    }


@router.post("/synthesize/{recipe_id}", response_model=Dict[str, Any])
async def synthesize_card(
    recipe_id: int = Path(..., description="合成配方ID"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    使用指定配方合成卡片
    """
    success, result_card, message = await CardService.synthesize_card(
        db, user_id=current_user.id, recipe_id=recipe_id
    )
    
    if not success:
        raise HTTPException(status_code=400, detail=message)
    
    return {
        "success": success,
        "message": message,
        "card": result_card
    } 