from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.services.gamification import LevelService
from app.schemas.gamification import (
    UserLevelResponse, UserAttributeResponse, UserTitleResponse,
    UserLevelDetailResponse, ExperienceGainRequest
)

router = APIRouter()


@router.get("/", response_model=UserLevelResponse)
async def get_user_level(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取当前用户的等级信息
    """
    user_level = await LevelService.get_user_level(db, user_id=current_user.id)
    if not user_level:
        # 如果用户没有等级记录，初始化一个
        user_level = await LevelService.initialize_user_level(db, user_id=current_user.id)
    
    return user_level


@router.get("/detail", response_model=UserLevelDetailResponse)
async def get_user_level_detail(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取当前用户的详细等级信息，包括等级、经验值、所需经验值和进度百分比
    """
    user_level = await LevelService.get_user_level(db, user_id=current_user.id)
    if not user_level:
        # 如果用户没有等级记录，初始化一个
        user_level = await LevelService.initialize_user_level(db, user_id=current_user.id)
    
    # 计算下一级所需经验值
    exercise_next_level = user_level.exercise_level + 1
    diet_next_level = user_level.diet_level + 1
    
    exercise_required_exp = await LevelService.calculate_required_exp(exercise_next_level)
    diet_required_exp = await LevelService.calculate_required_exp(diet_next_level)
    
    # 计算当前等级进度百分比
    exercise_current_level_exp = await LevelService.calculate_required_exp(user_level.exercise_level)
    diet_current_level_exp = await LevelService.calculate_required_exp(user_level.diet_level)
    
    exercise_progress = (user_level.exercise_experience - exercise_current_level_exp) / (exercise_required_exp - exercise_current_level_exp) * 100
    diet_progress = (user_level.diet_experience - diet_current_level_exp) / (diet_required_exp - diet_current_level_exp) * 100
    
    # 确保百分比在0-100范围内
    exercise_progress = max(0, min(100, exercise_progress))
    diet_progress = max(0, min(100, diet_progress))
    
    return {
        "id": user_level.id,
        "user_id": user_level.user_id,
        "exercise_level": user_level.exercise_level,
        "exercise_experience": user_level.exercise_experience,
        "exercise_required_exp": exercise_required_exp,
        "exercise_progress": exercise_progress,
        "diet_level": user_level.diet_level,
        "diet_experience": user_level.diet_experience,
        "diet_required_exp": diet_required_exp,
        "diet_progress": diet_progress,
        "created_at": user_level.created_at,
        "updated_at": user_level.updated_at
    }


@router.get("/attributes", response_model=UserAttributeResponse)
async def get_user_attributes(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取当前用户的属性信息
    """
    user_attributes = await LevelService.get_user_attributes(db, user_id=current_user.id)
    if not user_attributes:
        # 如果用户没有属性记录，初始化一个
        await LevelService.initialize_user_level(db, user_id=current_user.id)
        user_attributes = await LevelService.get_user_attributes(db, user_id=current_user.id)
    
    return user_attributes


@router.get("/titles", response_model=List[UserTitleResponse])
async def get_user_titles(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取当前用户的所有称号
    """
    user_titles = await LevelService.get_user_titles(db, user_id=current_user.id)
    return user_titles


@router.get("/active-title", response_model=Optional[UserTitleResponse])
async def get_user_active_title(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取当前用户激活的称号
    """
    active_title = await LevelService.get_active_title(db, user_id=current_user.id)
    return active_title


@router.post("/set-active-title/{title_id}", response_model=UserTitleResponse)
async def set_active_title(
    title_id: int = Path(..., description="要激活的称号ID"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    设置当前用户的激活称号
    """
    # 验证称号是否属于当前用户
    user_titles = await LevelService.get_user_titles(db, user_id=current_user.id)
    title_ids = [title.id for title in user_titles]
    
    if title_id not in title_ids:
        raise HTTPException(status_code=404, detail="称号不存在或不属于当前用户")
    
    updated_title = await LevelService.set_active_title(db, user_id=current_user.id, title_id=title_id)
    if not updated_title:
        raise HTTPException(status_code=400, detail="设置激活称号失败")
    
    return updated_title


@router.post("/add-exercise-experience", response_model=Dict[str, Any])
async def add_exercise_experience(
    experience_data: ExperienceGainRequest,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    为当前用户增加运动经验值（仅供内部API使用，用户不应直接调用）
    """
    updated_level, level_up = await LevelService.add_exercise_experience(
        db, 
        user_id=current_user.id, 
        experience=experience_data.amount,
        update_attributes=True
    )
    
    return {
        "success": True,
        "level": updated_level.exercise_level,
        "experience": updated_level.exercise_experience,
        "level_up": level_up,
        "message": "成功增加运动经验值" + ("，等级提升！" if level_up else "")
    }


@router.post("/add-diet-experience", response_model=Dict[str, Any])
async def add_diet_experience(
    experience_data: ExperienceGainRequest,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    为当前用户增加饮食经验值（仅供内部API使用，用户不应直接调用）
    """
    updated_level, level_up = await LevelService.add_diet_experience(
        db, 
        user_id=current_user.id, 
        experience=experience_data.amount,
        update_attributes=True
    )
    
    return {
        "success": True,
        "level": updated_level.diet_level,
        "experience": updated_level.diet_experience,
        "level_up": level_up,
        "message": "成功增加饮食经验值" + ("，等级提升！" if level_up else "")
    } 