from fastapi import APIRouter
from app.api.endpoints.gamification import level, card, currency, achievement, task

router = APIRouter()

# Level and attributes endpoints
router.include_router(level.router, prefix="/level", tags=["gamification-level"])

# Card system endpoints
router.include_router(card.router, prefix="/card", tags=["gamification-card"])

# Virtual currency endpoints
router.include_router(currency.router, prefix="/currency", tags=["gamification-currency"])

# Achievement endpoints
router.include_router(achievement.router, prefix="/achievement", tags=["gamification-achievement"])

# Task endpoints
router.include_router(task.router, prefix="/task", tags=["gamification-task"])
