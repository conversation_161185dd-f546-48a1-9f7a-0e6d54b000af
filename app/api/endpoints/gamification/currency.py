from typing import List, Dict, Any, Optional
from datetime import date
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.services.gamification import CurrencyService
from app.schemas.gamification import (
    CurrencyResponse, CurrencyTransactionResponse, ShopItemResponse,
    UserPurchaseResponse, PurchaseRequest
)

router = APIRouter()


@router.get("/balance", response_model=CurrencyResponse)
async def get_user_currency(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取当前用户的虚拟货币余额
    """
    currency = await CurrencyService.get_user_currency(db, user_id=current_user.id)
    if not currency:
        # 如果用户没有货币记录，初始化一个
        currency = await CurrencyService.initialize_user_currency(db, user_id=current_user.id)
    
    return currency


@router.get("/transactions", response_model=List[CurrencyTransactionResponse])
async def get_user_transactions(
    skip: int = Query(0, description="Skip first N items"),
    limit: int = Query(20, description="Limit the number of items returned"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取当前用户的交易记录
    """
    transactions = await CurrencyService.get_user_transactions(
        db, user_id=current_user.id, skip=skip, limit=limit
    )
    return transactions


@router.get("/daily-transactions", response_model=List[CurrencyTransactionResponse])
async def get_daily_transactions(
    day: Optional[date] = Query(None, description="指定日期，默认为今天"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取当前用户某一天的交易记录
    """
    transactions = await CurrencyService.get_daily_transactions(
        db, user_id=current_user.id, day=day
    )
    return transactions


@router.get("/shop", response_model=List[ShopItemResponse])
async def get_shop_items(
    category: Optional[str] = Query(None, description="商品类别"),
    skip: int = Query(0, description="Skip first N items"),
    limit: int = Query(50, description="Limit the number of items returned"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取商店物品列表，可以按类别筛选
    """
    # 获取用户等级信息，以显示折扣
    from app.services.gamification import LevelService
    user_level = await LevelService.get_user_level(db, user_id=current_user.id)
    
    # 根据用户等级获取可见的商店物品
    user_level_type = None
    user_level_value = None
    if user_level:
        # 使用较高的等级获取折扣
        if user_level.exercise_level >= user_level.diet_level:
            user_level_type = "EXERCISE"
            user_level_value = user_level.exercise_level
        else:
            user_level_type = "DIET"
            user_level_value = user_level.diet_level
    
    items = await CurrencyService.get_shop_items(
        db, 
        skip=skip, 
        limit=limit, 
        category=category,
        user_level_type=user_level_type, 
        user_level=user_level_value
    )
    
    return items


@router.get("/shop/{item_id}", response_model=Dict[str, Any])
async def get_shop_item_detail(
    item_id: int = Path(..., description="商品ID"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取商店物品详情，包括折扣价格
    """
    # 获取用户等级信息，以显示折扣
    from app.services.gamification import LevelService
    user_level = await LevelService.get_user_level(db, user_id=current_user.id)
    
    # 确定用户等级类型和值
    user_level_type = None
    user_level_value = None
    if user_level:
        # 使用较高的等级获取折扣
        if user_level.exercise_level >= user_level.diet_level:
            user_level_type = "EXERCISE"
            user_level_value = user_level.exercise_level
        else:
            user_level_type = "DIET"
            user_level_value = user_level.diet_level
    
    item, discounted_price = await CurrencyService.get_discounted_price(
        db, 
        item_id=item_id,
        user_level_type=user_level_type, 
        user_level=user_level_value
    )
    
    if not item:
        raise HTTPException(status_code=404, detail="商品不存在")
    
    # 获取关联物品信息
    related_item = None
    if item.item_type == "CARD" and item.related_item_id:
        from app.services.gamification import CardService
        related_item = await CardService.get_card_by_id(db, card_id=item.related_item_id)
    
    return {
        "item": item,
        "discounted_price": discounted_price,
        "original_price": item.price,
        "discount_percentage": item.discount_percentage,
        "related_item": related_item
    }


@router.post("/purchase", response_model=Dict[str, Any])
async def purchase_item(
    purchase_data: PurchaseRequest,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    购买商店物品
    """
    success, purchase, message = await CurrencyService.purchase_item(
        db, 
        user_id=current_user.id, 
        item_id=purchase_data.item_id,
        quantity=purchase_data.quantity
    )
    
    if not success:
        raise HTTPException(status_code=400, detail=message)
    
    # 获取最新余额
    currency = await CurrencyService.get_user_currency(db, user_id=current_user.id)
    
    return {
        "success": success,
        "message": message,
        "purchase": purchase,
        "balance": currency.amount
    }


@router.get("/purchases", response_model=List[UserPurchaseResponse])
async def get_user_purchases(
    skip: int = Query(0, description="Skip first N items"),
    limit: int = Query(20, description="Limit the number of items returned"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取当前用户的购买记录
    """
    purchases = await CurrencyService.get_user_purchases(
        db, user_id=current_user.id, skip=skip, limit=limit
    )
    return purchases


@router.get("/check-daily-limit", response_model=Dict[str, Any])
async def check_daily_earning_limit(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    检查当前用户的当日获取货币是否超过限制
    """
    # 默认每日限制1000个货币
    limit = 1000
    
    is_under_limit, daily_earned = await CurrencyService.check_daily_earning_limit(
        db, user_id=current_user.id, limit=limit
    )
    
    return {
        "is_under_limit": is_under_limit,
        "daily_earned": daily_earned,
        "daily_limit": limit,
        "remaining": max(0, limit - daily_earned)
    } 