from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.services.gamification import AchievementService
from app.schemas.gamification import (
    AchievementResponse, UserAchievementResponse,
    MilestoneResponse, UserMilestoneResponse
)

router = APIRouter()


@router.get("/all", response_model=List[AchievementResponse])
async def get_all_achievements(
    skip: int = Query(0, description="Skip first N items"),
    limit: int = Query(100, description="Limit the number of items returned"),
    visible_only: bool = Query(True, description="只返回可见成就"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取所有成就
    """
    achievements = await AchievementService.get_all_achievements(
        db, skip=skip, limit=limit, visible_only=visible_only
    )
    return achievements


@router.get("/by-category/{category}", response_model=List[AchievementResponse])
async def get_achievements_by_category(
    category: str = Path(..., description="成就类别"),
    skip: int = Query(0, description="Skip first N items"),
    limit: int = Query(100, description="Limit the number of items returned"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    根据类别获取成就列表
    """
    achievements = await AchievementService.get_achievements_by_category(
        db, category=category, skip=skip, limit=limit
    )
    return achievements


@router.get("/progress", response_model=List[UserAchievementResponse])
async def get_user_achievement_progress(
    skip: int = Query(0, description="Skip first N items"),
    limit: int = Query(100, description="Limit the number of items returned"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取当前用户的成就进度
    """
    user_achievements = await AchievementService.get_user_achievement_progress(
        db, user_id=current_user.id, skip=skip, limit=limit
    )
    return user_achievements


@router.get("/completed", response_model=List[UserAchievementResponse])
async def get_user_completed_achievements(
    skip: int = Query(0, description="Skip first N items"),
    limit: int = Query(100, description="Limit the number of items returned"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取当前用户已完成的成就
    """
    completed_achievements = await AchievementService.get_user_completed_achievements(
        db, user_id=current_user.id, skip=skip, limit=limit
    )
    return completed_achievements


@router.post("/claim/{achievement_id}", response_model=Dict[str, Any])
async def claim_achievement_reward(
    achievement_id: int = Path(..., description="成就ID"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    领取成就奖励
    """
    success, rewards_info = await AchievementService.claim_achievement_reward(
        db, user_id=current_user.id, achievement_id=achievement_id
    )
    
    if not success:
        raise HTTPException(status_code=400, detail=rewards_info.get("message", "领取失败"))
    
    return rewards_info


@router.get("/milestones", response_model=List[MilestoneResponse])
async def get_all_milestones(
    skip: int = Query(0, description="Skip first N items"),
    limit: int = Query(100, description="Limit the number of items returned"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取所有里程碑
    """
    milestones = await AchievementService.get_all_milestones(db, skip=skip, limit=limit)
    return milestones


@router.get("/milestone-progress", response_model=List[UserMilestoneResponse])
async def get_user_milestone_progress(
    skip: int = Query(0, description="Skip first N items"),
    limit: int = Query(100, description="Limit the number of items returned"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取当前用户的里程碑进度
    """
    user_milestones = await AchievementService.get_user_milestone_progress(
        db, user_id=current_user.id, skip=skip, limit=limit
    )
    return user_milestones


@router.get("/completed-milestones", response_model=List[UserMilestoneResponse])
async def get_user_completed_milestones(
    skip: int = Query(0, description="Skip first N items"),
    limit: int = Query(100, description="Limit the number of items returned"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    获取当前用户已完成的里程碑
    """
    completed_milestones = await AchievementService.get_user_completed_milestones(
        db, user_id=current_user.id, skip=skip, limit=limit
    )
    return completed_milestones


@router.post("/update-milestone/{milestone_id}", response_model=Dict[str, Any])
async def update_milestone_active_date(
    milestone_id: int = Path(..., description="里程碑ID"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    更新里程碑活跃日期（打卡）
    """
    try:
        user_milestone, just_completed, milestone_obj = await AchievementService.update_milestone_active_date(
            db, user_id=current_user.id, milestone_id=milestone_id
        )
        
        return {
            "success": True,
            "just_completed": just_completed,
            "user_milestone": user_milestone,
            "milestone": milestone_obj,
            "message": "打卡成功" + ("，里程碑已完成！" if just_completed else "")
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))


@router.post("/claim-milestone/{milestone_id}", response_model=Dict[str, Any])
async def claim_milestone_reward(
    milestone_id: int = Path(..., description="里程碑ID"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    领取里程碑奖励
    """
    success, rewards_info = await AchievementService.claim_milestone_reward(
        db, user_id=current_user.id, milestone_id=milestone_id
    )
    
    if not success:
        raise HTTPException(status_code=400, detail=rewards_info.get("message", "领取失败"))
    
    return rewards_info


@router.post("/trigger-event/{event_type}", response_model=Dict[str, Any])
async def trigger_achievement_event(
    event_type: str = Path(..., description="事件类型"),
    event_data: Optional[Dict[str, Any]] = None,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user)
):
    """
    触发成就检查事件
    """
    if event_data is None:
        event_data = {}
    
    achievement_results = await AchievementService.check_and_trigger_achievements(
        db, user_id=current_user.id, event_type=event_type, event_data=event_data
    )
    
    return {
        "success": True,
        "event_type": event_type,
        "achievements_triggered": achievement_results
    } 