from typing import Optional, List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, BackgroundTasks, Request
from fastapi.responses import FileResponse, JSONResponse
from sqlalchemy.orm import Session
import logging
import os
import time
import uuid
from datetime import datetime

from app.api import deps
from app.services.video_processing_service import VideoProcessingService, VideoQuality, VideoOutputFormat
from app import models

router = APIRouter()
logger = logging.getLogger(__name__)

@router.post("/process_video", response_model=Dict[str, Any])
async def process_video(
    video: UploadFile = File(...),
    quality: VideoQuality = Form(...),
    outputFormat: VideoOutputFormat = Form(...),
    title: Optional[str] = Form(None),
    description: Optional[str] = Form(None),
    background_tasks: BackgroundTasks = None,
    current_user: Optional[models.User] = Depends(deps.get_current_user_optional),
    db: Session = Depends(deps.get_db)
):
    """
    处理上传的视频文件

    参数:
        video: 上传的视频文件
        quality: 输出质量 (high/medium/low)
        outputFormat: 输出格式 (livephoto/mp4/gif)
        title: 视频标题（可选）
        description: 视频描述（可选）

    返回:
        处理结果信息
    """
    start_time = time.time()

    try:
        # 初始化视频处理服务
        VideoProcessingService.initialize()

        # 检查文件类型
        content_type = video.content_type or ""
        if not content_type.startswith("video/"):
            raise HTTPException(status_code=400, detail="只接受视频文件")

        # 读取上传的文件内容
        file_content = await video.read()
        if not file_content:
            raise HTTPException(status_code=400, detail="上传的文件为空")

        # 保存上传的视频到临时目录
        temp_file_path = await VideoProcessingService.save_uploaded_video(
            file_content=file_content,
            original_filename=video.filename
        )

        # 处理视频
        result = await VideoProcessingService.process_video(
            video_path=temp_file_path,
            output_format=outputFormat,
            quality=quality,
            title=title,
            description=description
        )

        # 添加处理时间信息
        process_time = time.time() - start_time
        result["process_time"] = f"{process_time:.2f}秒"

        # 记录处理信息
        user_id = current_user.id if current_user else None
        logger.info(f"视频处理完成: 用户={user_id}, 格式={outputFormat}, 质量={quality}, 耗时={process_time:.2f}秒")

        # 添加清理临时文件的后台任务
        if background_tasks:
            background_tasks.add_task(cleanup_temp_file, temp_file_path)

        return result

    except Exception as e:
        logger.error(f"视频处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"视频处理失败: {str(e)}")

@router.get("/download/{filename}")
async def download_file(
    filename: str,
    request: Request,
    db: Session = Depends(deps.get_db)
):
    """
    下载处理后的文件

    参数:
        filename: 文件名

    返回:
        文件内容
    """
    try:
        # 安全检查
        if '..' in filename or '/' in filename:
            raise HTTPException(status_code=400, detail="非法文件名")

        # 构建文件路径
        file_path = os.path.join(VideoProcessingService.PROCESSED_STORAGE_PATH, filename)

        # 检查文件是否存在
        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="文件不存在")

        # 确定内容类型
        content_type = "application/octet-stream"  # 默认
        if filename.lower().endswith(".mp4"):
            content_type = "video/mp4"
        elif filename.lower().endswith(".mov"):
            content_type = "video/quicktime"
        elif filename.lower().endswith(".gif"):
            content_type = "image/gif"
        elif filename.lower().endswith(".heic"):
            content_type = "image/heic"

        # 返回文件
        return FileResponse(
            path=file_path,
            media_type=content_type,
            filename=filename
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件下载失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文件下载失败: {str(e)}")

@router.post("/cleanup")
async def cleanup_old_files(
    days: int = Form(7),
    current_user: models.User = Depends(deps.get_current_active_superuser),
    db: Session = Depends(deps.get_db)
):
    """
    清理旧的临时文件（仅管理员可用）

    参数:
        days: 删除多少天前的文件

    返回:
        清理结果信息
    """
    try:
        result = await VideoProcessingService.cleanup_temp_files(older_than_days=days)
        return result
    except Exception as e:
        logger.error(f"清理临时文件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清理临时文件失败: {str(e)}")

async def cleanup_temp_file(file_path: str):
    """清理临时文件的后台任务"""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            logger.info(f"已清理临时文件: {file_path}")
    except Exception as e:
        logger.error(f"清理临时文件失败: {file_path}, 错误: {str(e)}")
