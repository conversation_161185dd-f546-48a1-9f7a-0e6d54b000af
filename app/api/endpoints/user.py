from typing import Any, Optional, Dict
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Request, Form
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from sqlalchemy import text
import logging
from datetime import datetime
import os
import shutil
import requests
from urllib.parse import urlparse
import uuid
import base64
import time
import hashlib
import json
import traceback

from app import models, schemas, crud
from app.api import deps
from app.utils.avatar import save_avatar, process_avatar
from app.db.session import USE_MOCK_SESSION
from app.core.config import settings
from app.utils.secure_path import generate_user_secure_path
from app.schemas.user import FoodImageResponse  # 直接导入模型

router = APIRouter()
logger = logging.getLogger("fitness-coach-api")


def truncate_dict_values(data: Dict, max_length: int = 20) -> Dict:
    """
    截断字典中字符串值的长度，以便于日志记录和打印
    """
    if not data:
        return data

    result = {}
    for key, value in data.items():
        if isinstance(value, str):
            if len(value) > max_length:
                result[key] = value[:max_length] + "..."
            else:
                result[key] = value
        elif isinstance(value, dict):
            # 递归处理嵌套字典
            result[key] = truncate_dict_values(value, max_length)
        else:
            result[key] = value
    return result


@router.get("/profile", response_model=schemas.UserProfile)
def get_user_profile(
    request: Request,
    current_user: models.User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
) -> Any:
    """
    获取当前用户的个人资料
    """
    # 使用统一函数转换为API响应模型，确保字段类型正确
    return create_user_profile(current_user)


@router.post("/profile", response_model=schemas.UserProfile)
def update_user_profile(
    request: Request,
    user_update: schemas.UserUpdate,
    current_user: models.User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
) -> Any:
    """
    更新当前用户的个人资料
    """
    # 开始计时
    start_time = time.time()

    # 更新用户信息
    try:
        # 记录接收到的用户更新数据
        logger.info(f"接收到用户更新请求: user_id={current_user.id}")

        # 记录原始请求数据
        try:
            body = request.state.body
            if body:
                logger.debug(f"接收到的原始请求数据: {body.decode('utf-8')}")
        except Exception as e:
            logger.error(f"无法读取原始请求数据: {str(e)}")

        # 兼容Pydantic V1和V2
        try:
            # Pydantic V2
            if hasattr(user_update, "model_dump"):
                update_dict = user_update.model_dump(exclude_unset=True)
            # Pydantic V1
            else:
                update_dict = user_update.dict(exclude_unset=True)

            # 使用截断函数处理日志输出
            truncated_dict = truncate_dict_values(update_dict)
            logger.debug(f"处理后的更新数据(字典方法): {truncated_dict}")
        except Exception as e:
            logger.error(f"处理用户更新数据失败: {str(e)}")

            # 尝试直接使用__dict__
            update_dict = {k: v for k, v in user_update.__dict__.items()
                         if not k.startswith('_') and v is not None}
            truncated_dict = truncate_dict_values(update_dict)
            logger.debug(f"处理后的更新数据(__dict__): {truncated_dict}")

        # 记录截断后的数据
        truncated_dict = truncate_dict_values(update_dict)
        logger.info(f"处理后的更新数据: {truncated_dict}")

        # 检查是否提供了用户ID
        user_id = update_dict.get('id')
        target_user = current_user

        # 如果提供了用户ID，并且与当前用户不同，则查找对应用户
        if user_id and user_id != current_user.id:
            logger.info(f"请求更新用户ID: {user_id}，与当前用户ID: {current_user.id} 不同")
            target_user = crud.user.get(db, id=user_id)
            if not target_user:
                logger.error(f"找不到ID为 {user_id} 的用户")
                raise HTTPException(status_code=404, detail=f"找不到ID为 {user_id} 的用户")

        # 处理avatar_url字段 - 如果是base64格式，则保存为文件
        if 'avatar_url' in update_dict and update_dict['avatar_url'] and update_dict['avatar_url'].startswith('data:image/'):
            try:
                avatar_url = update_dict['avatar_url']
                logger.info(f"接收到base64格式的头像数据，准备处理")

                # 确保存储目录存在
                base_dir = "/data/users"

                # 创建安全路径
                secure_path = generate_user_secure_path(target_user.id)
                user_dir = f"{base_dir}/{secure_path}"
                avatar_dir = f"{user_dir}/avatar"

                # 创建必要的目录结构
                for dir_path in [base_dir, user_dir, avatar_dir]:
                    os.makedirs(dir_path, exist_ok=True)
                    logger.debug(f"确保目录存在: {dir_path}")

                # 解析base64数据
                metadata, base64_data = avatar_url.split(',', 1)
                content_type = metadata.split(';')[0].replace('data:', '')

                # 确定文件扩展名
                if content_type == 'image/jpeg':
                    file_ext = '.jpg'
                elif content_type == 'image/png':
                    file_ext = '.png'
                elif content_type == 'image/gif':
                    file_ext = '.gif'
                else:
                    file_ext = '.jpg'

                # 生成带时间戳的文件名
                timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                filename = f"avatar_{timestamp}{file_ext}"
                file_path = os.path.join(avatar_dir, filename)

                # 解码并保存图像数据
                image_data = base64.b64decode(base64_data)
                with open(file_path, "wb") as f:
                    f.write(image_data)

                # 确保文件有正确的权限
                os.chmod(file_path, 0o644)  # rw-r--r--

                # 修改URL格式，使用安全路径代替用户ID
                new_avatar_url = f"/api/v1/user/avatar/image/{secure_path}/{filename}"

                # 在update_dict中更新avatar_url
                update_dict['avatar_url'] = new_avatar_url
                logger.info(f"Base64头像已处理并保存为文件: {new_avatar_url}")
            except Exception as e:
                logger.error(f"处理base64头像失败: {str(e)}")
                raise HTTPException(status_code=400, detail=f"处理头像失败: {str(e)}")

        # 移除id字段，避免尝试更新主键
        if 'id' in update_dict:
            del update_dict['id']

        # 处理枚举类型字段 - 批量处理
        enum_fields = ['gender', 'experience_level', 'fitness_goal', 'activity_level']
        for field in enum_fields:
            if field in update_dict and update_dict[field] is not None:
                # 记录原始值类型和值，便于调试
                logger.debug(f"处理枚举字段 {field}: 原始值={update_dict[field]}, 类型={type(update_dict[field])}")

                if field == 'gender':
                    # gender特殊处理 - 确保最终是整数类型
                    gender_value = update_dict[field]

                    # 将各种格式转换为数字表示
                    gender_int = None
                    if isinstance(gender_value, int):
                        gender_int = gender_value
                    elif hasattr(gender_value, 'value'):
                        gender_int = gender_value.value
                    elif isinstance(gender_value, str):
                        if gender_value.isdigit():
                            gender_int = int(gender_value)
                        elif gender_value.upper() in ['UNKNOWN', 'MALE', 'FEMALE']:
                            gender_map = {'UNKNOWN': 0, 'MALE': 1, 'FEMALE': 2}
                            gender_int = gender_map[gender_value.upper()]

                    # 确保在有效范围内
                    if gender_int is None or gender_int not in [0, 1, 2]:
                        gender_int = 0

                    # 直接使用整数值
                    update_dict[field] = gender_int
                    logger.debug(f"gender字段处理后: 值={update_dict[field]}, 类型={type(update_dict[field])}")
                else:
                    # 处理其他枚举类型字段 - 转为整数
                    if hasattr(update_dict[field], 'value'):
                        update_dict[field] = update_dict[field].value
                    elif not isinstance(update_dict[field], int):
                        try:
                            update_dict[field] = int(update_dict[field])
                        except (ValueError, TypeError):
                            logger.warning(f"无法转换字段 {field} 的值 {update_dict[field]} 为整数")

                # 记录处理后的值
                logger.debug(f"枚举字段 {field} 处理后: 值={update_dict[field]}, 类型={type(update_dict[field])}")

        # 创建一个包含有变化的字段的更新字典
        update_data = {}
        for key, value in update_dict.items():
            # 检查字段是否存在于用户模型
            if not hasattr(target_user, key):
                logger.warning(f"字段 {key} 不存在于用户模型中，已忽略")
                continue

            # 检查值是否有变化
            current_value = getattr(target_user, key, None)
            if value != current_value:
                update_data[key] = value
                # 只记录关键变更，减少日志量
                if key not in ['avatar_url']:
                    # 截断日志中的值显示
                    current_trunc = str(current_value)[:10] + "..." if isinstance(current_value, str) and len(str(current_value)) > 10 else current_value
                    new_trunc = str(value)[:10] + "..." if isinstance(value, str) and len(str(value)) > 10 else value
                    logger.debug(f"字段 {key} 值变化: {current_trunc} -> {new_trunc}")

        # 如果没有变化，直接返回当前用户
        if not update_data:
            logger.info("没有字段需要更新")
            elapsed_time = time.time() - start_time
            logger.info(f"用户资料更新请求处理完成，无更新，耗时: {elapsed_time:.4f}秒")
            # 使用统一转换函数确保返回格式正确
            return create_user_profile(target_user)

        # 有变化则更新 - 使用事务
        truncated_update = truncate_dict_values(update_data)
        logger.info(f"更新用户数据: {truncated_update}")
        try:
            # 设置事务超时
            db.execute(text("SET LOCAL statement_timeout = '10s'"))

            # 构建直接更新的SQL语句，避免使用text模板引擎语法
            set_clauses = []
            params = {}

            for key, value in update_data.items():
                set_clauses.append(f"{key} = :{key}")
                params[key] = value

            # 添加更新时间
            set_clauses.append("updated_at = :updated_at")
            params["updated_at"] = datetime.now()
            params["user_id"] = target_user.id

            if not set_clauses:
                logger.info("没有字段需要更新")
                user = crud.user.get(db, id=target_user.id)
                return create_user_profile(user)

            # 构建完整SQL - 如果没有要设置的字段，避免空的SET子句
            sql = f"UPDATE users SET {', '.join(set_clauses)} WHERE id = :user_id"

            # 记录将要执行的SQL和参数（不含敏感数据）
            safe_params = {k: (v[:20] + '...' if isinstance(v, str) and len(v) > 20 else v)
                          for k, v in params.items() if k != 'avatar_url'}
            logger.debug(f"执行SQL: {sql}")
            logger.debug(f"参数: {safe_params}")

            # 执行SQL
            try:
                db.execute(text(sql), params)
                db.commit()
                logger.info(f"SQL执行成功")
            except Exception as e:
                db.rollback()
                logger.error(f"SQL执行失败: {str(e)}")
                # 尝试更安全的方式更新
                try:
                    logger.info("尝试使用备用更新方法")
                    # 分两步更新：先更新普通字段，再更新gender字段
                    # 1. 更新所有字段
                    for key, value in update_data.items():
                        setattr(target_user, key, value)
                    target_user.updated_at = datetime.now()
                    db.commit()
                    logger.info("备用更新方法成功")
                except Exception as e2:
                    db.rollback()
                    logger.error(f"备用更新方法也失败: {str(e2)}")
                    raise HTTPException(
                        status_code=500,
                        detail="更新用户数据失败，请稍后重试"
                    )

            # 查询更新后的用户数据
            updated_user = crud.user.get(db, id=target_user.id)

            # 使用统一函数转换为API响应模型，确保字段类型正确
            user_profile = create_user_profile(updated_user)

            logger.info(f"用户更新成功: user_id={target_user.id}")

            # 记录总处理时间
            elapsed_time = time.time() - start_time
            logger.info(f"用户资料更新请求处理完成，耗时: {elapsed_time:.4f}秒")

            return user_profile
        except Exception as e:
            logger.error(f"数据库更新操作失败: {str(e)}")
            db.rollback()  # 确保回滚事务
            raise HTTPException(
                status_code=500,
                detail=f"数据库更新操作失败: {str(e)}"
            )
    except HTTPException:
        # 直接重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"更新用户信息失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="更新用户信息失败"
        )


@router.post("/avatar", response_model=schemas.AvatarResponse)
async def upload_avatar(
    request: Request,
    avatar_url: str = Form(None),
    file: UploadFile = File(None),
    current_user: models.User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
) -> Any:
    """
    上传用户头像
    支持三种方式：
    1. 上传文件
    2. 提供avatar_url链接
    3. 提供base64编码的图像数据
    """
    start_time = time.time()
    try:
        # 检查用户是否存在
        if not current_user:
            logger.error(f"用户不存在")
            raise HTTPException(status_code=404, detail="用户不存在")

        # 记录请求信息
        logger.info(f"Avatar请求开始: 用户ID={current_user.id}, 有文件={bool(file and file.filename)}, 有URL={bool(avatar_url)}")

        # 打印接收到的数据
        if avatar_url:
            if avatar_url.startswith('data:image/'):
                logger.info("收到base64格式的图片数据")
                print(f"收到base64格式的图片数据 (前50字符): {avatar_url[:50]}...")
            else:
                # 截断过长的URL显示
                truncated_url = avatar_url[:10] + "..." if len(avatar_url) > 10 else avatar_url
                logger.info(f"收到avatar_url: {truncated_url}")
                print(f"收到avatar_url: {truncated_url}")

        if file and file.filename:
            logger.info(f"收到上传文件: {file.filename}")
            print(f"收到上传文件: {file.filename}")

        # 确保存储目录存在
        base_dir = "/data/users"

        # 创建安全路径
        secure_path = generate_user_secure_path(current_user.id)
        user_dir = f"{base_dir}/{secure_path}"
        avatar_dir = f"{user_dir}/avatar"

        # 创建必要的目录结构
        for dir_path in [base_dir, user_dir, avatar_dir]:
            try:
                os.makedirs(dir_path, exist_ok=True)
                logger.info(f"确保目录存在: {dir_path}")
            except Exception as e:
                logger.error(f"创建目录失败: {dir_path}, 错误: {str(e)}")
                raise HTTPException(status_code=500, detail=f"服务器存储错误: 无法创建目录 {dir_path}")

        # 检查目录写入权限
        if not os.access(avatar_dir, os.W_OK):
            logger.error(f"目录没有写入权限: {avatar_dir}")
            raise HTTPException(status_code=500, detail=f"服务器存储错误: 目录 {avatar_dir} 无写入权限")

        # 生成文件名基础部分
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")

        # 决定使用哪种方式处理头像
        if file and file.filename:
            logger.info(f"处理上传文件: {file.filename}, 类型: {file.content_type}")
            # 验证文件类型
            if not file.content_type.startswith("image/"):
                logger.warning(f"非图片文件类型: {file.content_type}")
                raise HTTPException(status_code=400, detail="只能上传图片文件")

            # 获取文件扩展名
            file_ext = os.path.splitext(file.filename)[1]
            if not file_ext:
                # 根据content_type推断扩展名
                if file.content_type == "image/jpeg":
                    file_ext = ".jpg"
                elif file.content_type == "image/png":
                    file_ext = ".png"
                elif file.content_type == "image/gif":
                    file_ext = ".gif"
                else:
                    file_ext = ".jpg"

            # 生成文件名
            filename = f"avatar_{timestamp}{file_ext}"
            file_path = os.path.join(avatar_dir, filename)

            try:
                # 保存文件
                with open(file_path, "wb") as buffer:
                    shutil.copyfileobj(file.file, buffer)

                logger.info(f"头像文件已保存到: {file_path}")

                # 修改URL格式，使用安全路径代替用户ID
                avatar_url = f"/api/v1/user/avatar/image/{secure_path}/{filename}"
                # 记录日志，截断URL显示
                truncated_url = avatar_url[:20] + "..." if len(avatar_url) > 20 else avatar_url
                logger.info(f"生成的头像URL: {truncated_url}")
            except Exception as e:
                logger.error(f"保存上传文件失败: {str(e)}")
                raise HTTPException(status_code=500, detail=f"保存文件失败: {str(e)}")

        elif avatar_url:
            # 处理base64编码的图像数据
            if avatar_url.startswith('data:image/'):
                logger.info("处理base64编码的图像数据")
                try:
                    # 解析base64数据
                    metadata, base64_data = avatar_url.split(',', 1)
                    content_type = metadata.split(';')[0].replace('data:', '')

                    # 确定文件扩展名
                    if content_type == 'image/jpeg':
                        file_ext = '.jpg'
                    elif content_type == 'image/png':
                        file_ext = '.png'
                    elif content_type == 'image/gif':
                        file_ext = '.gif'
                    else:
                        file_ext = '.jpg'

                    # 解码base64数据
                    try:
                        image_data = base64.b64decode(base64_data)
                    except Exception as e:
                        logger.error(f"Base64解码失败: {str(e)}")
                        raise HTTPException(status_code=400, detail="无效的base64图像数据")

                    # 生成带时间戳的文件名
                    filename = f"avatar_{timestamp}{file_ext}"
                    file_path = os.path.join(avatar_dir, filename)

                    # 保存图像数据到文件
                    with open(file_path, "wb") as f:
                        f.write(image_data)

                    logger.info(f"base64图像已保存到: {file_path}")

                    # 修改URL格式，使用安全路径代替用户ID
                    avatar_url = f"/api/v1/user/avatar/image/{secure_path}/{filename}"
                    # 记录日志，截断URL显示
                    truncated_url = avatar_url[:20] + "..." if len(avatar_url) > 20 else avatar_url
                    logger.info(f"生成的头像URL: {truncated_url}")
                except Exception as e:
                    logger.error(f"处理base64图像失败: {str(e)}")
                    raise HTTPException(status_code=400, detail=f"处理base64图像失败: {str(e)}")
            else:
                logger.info(f"处理外部图片URL: {avatar_url[:10]}...")
                # 从URL下载图片
                try:
                    # 解析URL
                    parsed_url = urlparse(avatar_url)
                    if not parsed_url.scheme or not parsed_url.netloc:
                        logger.warning(f"无效的URL格式: {avatar_url}")
                        raise HTTPException(status_code=400, detail=f"无效的URL格式: {avatar_url}")

                    # 尝试获取文件扩展名，如果没有则根据头信息确定
                    file_ext = os.path.splitext(parsed_url.path)[1] or ".jpg"

                    # 生成文件名和路径
                    filename = f"avatar_{timestamp}{file_ext}"
                    file_path = os.path.join(avatar_dir, filename)

                    # 下载图片，设置较短的超时时间
                    logger.info(f"开始下载图片: {avatar_url}")
                    try:
                        response = requests.get(avatar_url, stream=True, timeout=5)
                        if response.status_code != 200:
                            logger.warning(f"下载图片失败，状态码: {response.status_code}")
                            raise HTTPException(status_code=400, detail=f"无法下载头像图片，状态码: {response.status_code}")

                        # 检查Content-Type是否为图片
                        content_type = response.headers.get('Content-Type', '')
                        if not content_type.startswith('image/'):
                            logger.warning(f"下载的内容不是图片: {content_type}")
                            raise HTTPException(status_code=400, detail="下载的内容不是图片")

                        # 根据Content-Type调整扩展名
                        if content_type == 'image/jpeg':
                            file_ext = '.jpg'
                        elif content_type == 'image/png':
                            file_ext = '.png'
                        elif content_type == 'image/gif':
                            file_ext = '.gif'

                        # 更新文件名和路径
                        filename = f"avatar_{timestamp}{file_ext}"
                        file_path = os.path.join(avatar_dir, filename)
                    except requests.RequestException as e:
                        logger.error(f"请求图片失败: {str(e)}")
                        raise HTTPException(status_code=400, detail=f"请求图片失败: {str(e)}")

                    # 保存图片
                    try:
                        with open(file_path, "wb") as f:
                            for chunk in response.iter_content(1024):
                                f.write(chunk)

                        logger.info(f"头像已从URL下载并保存到: {file_path}")
                    except Exception as e:
                        logger.error(f"保存下载图片失败: {str(e)}")
                        raise HTTPException(status_code=500, detail=f"保存图片失败: {str(e)}")

                    # 验证文件存在
                    if not os.path.exists(file_path):
                        logger.error(f"文件保存失败，路径不存在: {file_path}")
                        raise HTTPException(status_code=500, detail="文件保存失败")

                    # 验证文件大小
                    file_size = os.path.getsize(file_path)
                    if file_size == 0:
                        logger.error(f"文件保存失败，文件大小为0: {file_path}")
                        os.remove(file_path)  # 删除无效文件
                        raise HTTPException(status_code=500, detail="下载的图片为空")

                    # 修改URL格式，使用安全路径代替用户ID
                    avatar_url = f"/api/v1/user/avatar/image/{secure_path}/{filename}"
                    # 记录日志，截断URL显示
                    truncated_url = avatar_url[:20] + "..." if len(avatar_url) > 20 else avatar_url
                    logger.info(f"生成的头像URL: {truncated_url}")
                except HTTPException:
                    # 重新抛出HTTP异常
                    raise
                except Exception as e:
                    logger.error(f"下载头像失败: {str(e)}")
                    raise HTTPException(status_code=400, detail=f"下载头像失败: {str(e)}")
        else:
            logger.warning("请求中既没有文件也没有avatar_url")
            raise HTTPException(status_code=400, detail="必须提供文件或avatar_url")

        # 更新用户头像
        truncated_url = avatar_url[:20] + "..." if len(avatar_url) > 20 else avatar_url
        logger.info(f"更新用户头像: user_id={current_user.id}, avatar_url={truncated_url}")

        try:
            # 将数据库操作放在更短的事务中，避免长时间锁定
            from sqlalchemy import text

            # 创建一个简单的更新语句
            update_sql = "UPDATE users SET avatar_url = :avatar_url, updated_at = :updated_at WHERE id = :user_id"
            update_params = {
                "avatar_url": avatar_url,
                "updated_at": datetime.now(),
                "user_id": current_user.id
            }

            # 执行更新并提交事务
            db.execute(text(update_sql), update_params)
            db.commit()

            logger.info(f"用户头像更新成功: user_id={current_user.id}")
        except Exception as e:
            logger.error(f"更新用户头像数据库记录失败: {str(e)}")
            db.rollback()  # 确保回滚事务
            raise HTTPException(status_code=500, detail="更新用户数据失败")

        response_data = {"success": True, "avatar_url": avatar_url}

        # 记录处理总时间
        elapsed_time = time.time() - start_time
        logger.info(f"头像处理总耗时: {elapsed_time:.4f}秒")

        return response_data
    except HTTPException as e:
        # 记录HTTP异常
        logger.error(f"头像上传HTTP异常: {e.status_code} - {e.detail}")
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"上传头像失败，未处理异常: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"上传头像失败: {str(e)}"
        )


@router.get("/exists", response_model=Optional[schemas.UserProfile])
def check_user_exists(
    request: Request,
    openid: str,
    db: Session = Depends(deps.get_db)
) -> Any:
    """
    检查用户是否存在
    - **openid**: 用户的微信openid

    返回:
    - 如果用户存在: 用户的完整资料
    - 如果用户不存在: null
    """
    try:
        # 使用直接SQL查询获取用户 - 更高效
        from sqlalchemy import text

        # 构建简单的SQL查询
        sql = "SELECT * FROM users WHERE openid = :openid LIMIT 1"
        result = db.execute(text(sql), {"openid": openid}).fetchone()

        if result:
            # 将结果转换为模型
            user = models.User(
                id=result.id,
                openid=result.openid,
                nickname=result.nickname,
                avatar_url=result.avatar_url,
                gender=result.gender,  # 保持原始值，由create_user_profile处理转换
                age=result.age,
                weight=result.weight,
                height=result.height,
                activity_level=result.activity_level,
                experience_level=result.experience_level,
                fitness_goal=result.fitness_goal,
                health_conditions=result.health_conditions,  # 确保包含新字段
                allergies=result.allergies,  # 确保包含新字段
                completed=result.completed
            )
            # 使用统一函数转换为API响应模型
            return create_user_profile(user)
        # 找不到用户，返回None
        return None
    except Exception as e:
        logger.error(f"检查用户存在性失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail="检查用户失败"
        )


@router.get("/check", response_model=Optional[schemas.UserProfile])
def check_user(
    request: Request,
    openid: str,
    db: Session = Depends(deps.get_db)
) -> Any:
    """
    检查用户是否存在（/exists 接口的别名）
    - **openid**: 用户的微信openid

    返回:
    - 如果用户存在: 用户的完整资料
    - 如果用户不存在: null
    """
    # 直接调用exists函数
    return check_user_exists(request=request, openid=openid, db=db)


def process_user_object(user: models.User) -> models.User:
    """
    处理用户对象，确保所有枚举类型字段都是正确格式
    """
    # 如果用户为None，直接返回None
    if user is None:
        return None

    # 处理gender字段 - 确保gender_int为整数
    if hasattr(user, 'gender') and user.gender is not None:
        gender_value = user.gender
        # 将各种类型转换为整数并设置为gender_int属性
        gender_int = None

        if isinstance(gender_value, int):
            gender_int = gender_value
        elif isinstance(gender_value, str):
            if gender_value.isdigit():
                gender_int = int(gender_value)
            elif gender_value.upper() in ['UNKNOWN', 'MALE', 'FEMALE']:
                gender_map = {'UNKNOWN': 0, 'MALE': 1, 'FEMALE': 2}
                gender_int = gender_map[gender_value.upper()]

        # 如果无法转换，设置默认值
        if gender_int is None or gender_int not in [0, 1, 2]:
            gender_int = 0

        # 设置gender_int属性
        setattr(user, 'gender_int', gender_int)
        logger.debug(f"process_user_object: 添加gender_int属性: {gender_value} -> {getattr(user, 'gender_int', 0)}")

    # 处理其他枚举字段 - 统一转换逻辑
    enum_fields = {
        'experience_level': [1, 2, 3],  # 有效值范围
        'fitness_goal': [1, 2, 3, 4],   # 有效值范围
        'activity_level': list(range(1, 6))  # 1-5的有效值范围
    }

    for field, valid_values in enum_fields.items():
        if hasattr(user, field) and getattr(user, field) is not None:
            field_value = getattr(user, field)
            field_int = None

            # 转换为整数
            if isinstance(field_value, int):
                field_int = field_value
            elif isinstance(field_value, str) and field_value.isdigit():
                field_int = int(field_value)

            # 验证有效范围
            if field_int is None or field_int not in valid_values:
                field_int = valid_values[0]  # 使用第一个值作为默认值

            # 设置回对象
            setattr(user, field, field_int)

    return user


@router.get("/settings", response_model=schemas.UserSetting)
def get_user_settings(
    request: Request,
    current_user: models.User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
) -> Any:
    """
    获取用户设置
    """
    # 首先处理当前用户对象的gender字段
    process_user_object(current_user)

    try:
        # 获取用户设置，如果不存在则创建
        user_setting = crud.user_setting.get_by_user_id(db, user_id=current_user.id)
        if not user_setting:
            # 当使用模拟会话时，直接返回默认设置而不尝试创建
            if USE_MOCK_SESSION:
                now = datetime.now()
                user_setting = models.UserSetting(
                    id=1,
                    user_id=current_user.id,
                    notification_enabled=True,
                    created_at=now,
                    updated_at=now
                )
            else:
                # 确保创建用户设置时包含更新时间
                now = datetime.now()
                user_setting_in = schemas.UserSettingCreate(user_id=current_user.id)
                user_setting = crud.user_setting.create(db, obj_in=user_setting_in)

                # 检查updated_at是否为None，如果是则手动设置
                if not hasattr(user_setting, 'updated_at') or user_setting.updated_at is None:
                    user_setting.updated_at = now
                    db.commit()

        # 确保返回的对象有updated_at字段
        if not hasattr(user_setting, 'updated_at') or user_setting.updated_at is None:
            now = datetime.now()
            user_setting.updated_at = now
            if not USE_MOCK_SESSION:
                db.commit()

        # 将ORM对象转换为字典，确保所有字段都有值
        result = {
            "id": user_setting.id,
            "user_id": user_setting.user_id,
            "notification_enabled": user_setting.notification_enabled,
            "created_at": user_setting.created_at or datetime.now(),
            "updated_at": user_setting.updated_at or datetime.now()
        }

        return result
    except Exception as e:
        logger.error(f"获取用户设置失败: {str(e)}")
        # 如果出现错误，返回默认设置
        now = datetime.now()
        default_settings = {
            "id": 0,
            "user_id": current_user.id,
            "notification_enabled": True,
            "created_at": now,
            "updated_at": now
        }
        return default_settings


@router.post("/settings", response_model=schemas.UserSetting)
def update_user_settings(
    request: Request,
    settings_update: schemas.UserSettingUpdate,
    current_user: models.User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
) -> Any:
    """
    更新用户设置
    """
    try:
        # 当使用模拟会话时，直接返回更新后的设置对象
        if USE_MOCK_SESSION:
            now = datetime.now()
            # 创建一个模拟的设置对象
            result = {
                "id": 1,
                "user_id": current_user.id,
                "notification_enabled": settings_update.notification_enabled,
                "created_at": now,
                "updated_at": now
            }
            return result

        # 真实数据库操作
        user_setting = crud.user_setting.get_by_user_id(db, user_id=current_user.id)
        if not user_setting:
            # 创建新设置
            now = datetime.now()
            user_setting_in = schemas.UserSettingCreate(
                user_id=current_user.id,
                **settings_update.dict()
            )
            user_setting = crud.user_setting.create(db, obj_in=user_setting_in)

            # 检查updated_at是否为None，如果是则手动设置
            if not hasattr(user_setting, 'updated_at') or user_setting.updated_at is None:
                user_setting.updated_at = now
                db.commit()
        else:
            # 更新现有设置
            user_setting = crud.user_setting.update(db, db_obj=user_setting, obj_in=settings_update)

            # 确保更新时间被设置
            now = datetime.now()
            if not hasattr(user_setting, 'updated_at') or user_setting.updated_at is None:
                user_setting.updated_at = now
                db.commit()

        # 将ORM对象转换为字典，确保所有字段都有值
        result = {
            "id": user_setting.id,
            "user_id": user_setting.user_id,
            "notification_enabled": user_setting.notification_enabled,
            "created_at": user_setting.created_at or datetime.now(),
            "updated_at": user_setting.updated_at or datetime.now()
        }

        return result
    except Exception as e:
        logger.error(f"更新用户设置失败: {str(e)}")

        # 如果出现错误，在开发环境返回模拟数据
        if settings.IS_DEV:
            now = datetime.now()
            # 创建一个模拟的设置对象
            result = {
                "id": 1,
                "user_id": current_user.id,
                "notification_enabled": settings_update.notification_enabled,
                "created_at": now,
                "updated_at": now
            }
            return result

        raise HTTPException(
            status_code=500,
            detail="更新用户设置失败"
        )


@router.get("/avatar/image/{secure_path}/{filename}", response_class=FileResponse)
async def get_avatar_image(
    secure_path: str,
    filename: str
):
    """
    获取头像图片 - 不需要身份验证

    通过安全路径和文件名获取图片文件
    """
    file_path = f"/data/users/{secure_path}/avatar/{filename}"

    # 检查文件是否存在
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="图片不存在")

    # 返回文件
    return FileResponse(file_path, media_type="image/jpeg")


def create_user_profile(db_user: models.User) -> schemas.UserProfile:
    """
    将ORM用户对象转换为API响应模型，正确处理所有字段类型
    特别是确保gender字段为整数类型
    """
    # 如果用户为None，直接返回None
    if db_user is None:
        return None

    # 确保先处理gender_int
    db_user = process_user_object(db_user)

    # 创建基础字典
    user_dict = {}

    # 尝试获取UserProfile模型的字段
    try:
        # Pydantic V2 方式
        if hasattr(schemas.UserProfile, 'model_fields'):
            profile_fields = schemas.UserProfile.model_fields.keys()
        # Pydantic V1 方式
        elif hasattr(schemas.UserProfile, '__annotations__'):
            profile_fields = schemas.UserProfile.__annotations__.keys()
        # 兜底方案：最常见字段列表
        else:
            profile_fields = [
                'id', 'nickname', 'avatar_url', 'gender', 'age',
                'weight', 'height', 'activity_level', 'bmi', 'tedd',
                'completed', 'country', 'province', 'city',
                'experience_level', 'fitness_goal', 'created_at', 'birthday',
                'health_conditions', 'allergies'
            ]
    except Exception as e:
        logger.warning(f"获取UserProfile字段失败: {str(e)}，使用默认字段列表")
        profile_fields = [
            'id', 'nickname', 'avatar_url', 'gender', 'age',
            'weight', 'height', 'activity_level', 'bmi', 'tedd',
            'completed', 'country', 'province', 'city',
            'experience_level', 'fitness_goal', 'created_at', 'birthday',
            'health_conditions', 'allergies'
        ]

    # 填充所有可用字段
    for field in profile_fields:
        if field == 'gender':
            # 特殊处理gender字段，使用gender_int
            user_dict[field] = getattr(db_user, 'gender_int', 0)
        elif field in ['health_conditions', 'allergies'] and hasattr(db_user, field):
            # 确保健康状况和过敏源是列表类型
            value = getattr(db_user, field)
            if value is None:
                user_dict[field] = []
            elif isinstance(value, list):
                user_dict[field] = value
            else:
                # 如果值不是列表，尝试转换
                try:
                    user_dict[field] = list(value)
                except:
                    user_dict[field] = []
                    logger.warning(f"无法将{field}转换为列表: {value}")
        elif hasattr(db_user, field):
            # 复制其他字段
            user_dict[field] = getattr(db_user, field)

    # 创建并返回UserProfile实例
    try:
        return schemas.UserProfile.model_validate(user_dict)
    except Exception as e:
        logger.error(f"转换为UserProfile失败: {str(e)}")
        # 回退到更基本的转换方法
        return schemas.UserProfile(**user_dict)


@router.post("/upload-food-image", response_model=FoodImageResponse)
async def upload_food_image(
    request: Request,
    food_name: str = Form(""),
    image_url: str = Form(None),
    file: UploadFile = File(None),
    image: UploadFile = File(None),  # 增加常见的替代字段名
    img: UploadFile = File(None),    # 增加常见的替代字段名
    photo: UploadFile = File(None),  # 增加常见的替代字段名
    current_user: models.User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
) -> Any:
    """
    上传食物图像
    支持三种方式：
    1. 上传文件
    2. 提供image_url链接
    3. 提供base64编码的图像数据

    返回图像文件名和完整URL路径
    """
    start_time = time.time()
    try:
        # 检查用户是否存在
        if not current_user:
            logger.error(f"用户不存在")
            raise HTTPException(status_code=404, detail="用户不存在")

        # 调试: 记录收到的所有表单字段
        form_data = await request.form()
        form_fields = dict(form_data)
        logger.info(f"接收到的表单字段: {list(form_fields.keys())}")

        # 尝试从多个可能的字段获取文件
        actual_file = file or image or img or photo or None

        # 记录请求信息
        logger.info(f"食物图像上传请求开始: 用户ID={current_user.id}, 食物名称={food_name}, "
                   f"有文件={bool(actual_file and getattr(actual_file, 'filename', None))}, "
                   f"有URL={bool(image_url)}")

        # 打印接收到的数据
        if image_url:
            if image_url.startswith('data:image/'):
                logger.info("收到base64格式的图片数据")
                print(f"收到base64格式的图片数据 (前50字符): {image_url[:50]}...")
            else:
                # 截断过长的URL显示
                truncated_url = image_url[:10] + "..." if len(image_url) > 10 else image_url
                logger.info(f"收到image_url: {truncated_url}")
                print(f"收到image_url: {truncated_url}")

        if actual_file and getattr(actual_file, 'filename', None):
            logger.info(f"收到上传文件: {actual_file.filename}")
            print(f"收到上传文件: {actual_file.filename}")

        # 确保food_name有值，用于文件命名
        if not food_name:
            food_name = "unknown_food"

        # 处理food_name，移除特殊字符，用于文件名
        safe_food_name = ''.join(c for c in food_name if c.isalnum() or c in '_- ')
        safe_food_name = safe_food_name.replace(' ', '_')
        if not safe_food_name:
            safe_food_name = "food"

        # 确保存储目录存在
        base_dir = "/data/users"

        # 创建安全路径
        secure_path = generate_user_secure_path(current_user.id)
        user_dir = f"{base_dir}/{secure_path}"

        # 创建日期目录
        today = datetime.now().strftime("%Y-%m-%d")
        food_images_dir = f"{user_dir}/uploaded_food_images"
        date_dir = f"{food_images_dir}/{today}"

        # 创建必要的目录结构
        for dir_path in [base_dir, user_dir, food_images_dir, date_dir]:
            try:
                os.makedirs(dir_path, exist_ok=True)
                logger.info(f"确保目录存在: {dir_path}")
            except Exception as e:
                logger.error(f"创建目录失败: {dir_path}, 错误: {str(e)}")
                raise HTTPException(status_code=500, detail=f"服务器存储错误: 无法创建目录 {dir_path}")

        # 检查目录写入权限
        if not os.access(date_dir, os.W_OK):
            logger.error(f"目录没有写入权限: {date_dir}")
            raise HTTPException(status_code=500, detail=f"服务器存储错误: 目录 {date_dir} 无写入权限")

        # 生成文件名的时间戳和随机字符串部分
        timestamp = int(time.time())
        random_str = hashlib.md5(f"{timestamp}_{food_name}_{uuid.uuid4()}".encode()).hexdigest()[:8]

        # 决定使用哪种方式处理图像
        if actual_file and getattr(actual_file, 'filename', None):
            logger.info(f"处理上传文件: {actual_file.filename}, 类型: {actual_file.content_type}")
            # 验证文件类型
            if not actual_file.content_type.startswith("image/"):
                logger.warning(f"非图片文件类型: {actual_file.content_type}")
                raise HTTPException(status_code=400, detail="只能上传图片文件")

            # 获取文件扩展名
            file_ext = os.path.splitext(actual_file.filename)[1]
            if not file_ext:
                # 根据content_type推断扩展名
                if actual_file.content_type == "image/jpeg":
                    file_ext = ".jpg"
                elif actual_file.content_type == "image/png":
                    file_ext = ".png"
                elif actual_file.content_type == "image/gif":
                    file_ext = ".gif"
                else:
                    file_ext = ".jpg"

            # 生成文件名
            filename = f"{safe_food_name}_{timestamp}_{random_str}{file_ext}"
            file_path = os.path.join(date_dir, filename)

            try:
                # 保存文件
                with open(file_path, "wb") as buffer:
                    shutil.copyfileobj(actual_file.file, buffer)

                logger.info(f"食物图像文件已保存到: {file_path}")

                # 构建返回URL
                image_url = f"{filename}"
                logger.info(f"生成的食物图像URL: {image_url}")
            except Exception as e:
                logger.error(f"保存上传文件失败: {str(e)}")
                raise HTTPException(status_code=500, detail=f"保存文件失败: {str(e)}")

        elif image_url:
            # 处理base64编码的图像数据
            if image_url.startswith('data:image/'):
                logger.info("处理base64编码的图像数据")
                try:
                    # 解析base64数据
                    metadata, base64_data = image_url.split(',', 1)
                    content_type = metadata.split(';')[0].replace('data:', '')

                    # 确定文件扩展名
                    if content_type == 'image/jpeg':
                        file_ext = '.jpg'
                    elif content_type == 'image/png':
                        file_ext = '.png'
                    elif content_type == 'image/gif':
                        file_ext = '.gif'
                    else:
                        file_ext = '.jpg'

                    # 解码base64数据
                    try:
                        image_data = base64.b64decode(base64_data)
                    except Exception as e:
                        logger.error(f"Base64解码失败: {str(e)}")
                        raise HTTPException(status_code=400, detail="无效的base64图像数据")

                    # 生成文件名
                    filename = f"{safe_food_name}_{timestamp}_{random_str}{file_ext}"
                    file_path = os.path.join(date_dir, filename)

                    # 保存图像数据到文件
                    with open(file_path, "wb") as f:
                        f.write(image_data)

                    logger.info(f"base64食物图像已保存到: {file_path}")

                    # 构建返回URL
                    image_url = f"{filename}"
                    logger.info(f"生成的食物图像URL: {image_url}")
                except Exception as e:
                    logger.error(f"处理base64图像失败: {str(e)}")
                    raise HTTPException(status_code=400, detail=f"处理base64图像失败: {str(e)}")
            else:
                logger.info(f"处理外部图片URL")
                # 从URL下载图片
                try:
                    # 解析URL
                    parsed_url = urlparse(image_url)
                    if not parsed_url.scheme or not parsed_url.netloc:
                        logger.warning(f"无效的URL格式: {image_url}")
                        raise HTTPException(status_code=400, detail=f"无效的URL格式: {image_url}")

                    # 尝试获取文件扩展名，如果没有则根据头信息确定
                    file_ext = os.path.splitext(parsed_url.path)[1] or ".jpg"

                    # 下载图片，设置较短的超时时间
                    logger.info(f"开始下载图片")
                    try:
                        response = requests.get(image_url, stream=True, timeout=5)
                        if response.status_code != 200:
                            logger.warning(f"下载图片失败，状态码: {response.status_code}")
                            raise HTTPException(status_code=400, detail=f"无法下载图片，状态码: {response.status_code}")

                        # 检查Content-Type是否为图片
                        content_type = response.headers.get('Content-Type', '')
                        if not content_type.startswith('image/'):
                            logger.warning(f"下载的内容不是图片: {content_type}")
                            raise HTTPException(status_code=400, detail="下载的内容不是图片")

                        # 根据Content-Type调整扩展名
                        if content_type == 'image/jpeg':
                            file_ext = '.jpg'
                        elif content_type == 'image/png':
                            file_ext = '.png'
                        elif content_type == 'image/gif':
                            file_ext = '.gif'

                        # 生成文件名
                        filename = f"{safe_food_name}_{timestamp}_{random_str}{file_ext}"
                        file_path = os.path.join(date_dir, filename)
                    except requests.RequestException as e:
                        logger.error(f"请求图片失败: {str(e)}")
                        raise HTTPException(status_code=400, detail=f"请求图片失败: {str(e)}")

                    # 保存图片
                    try:
                        with open(file_path, "wb") as f:
                            for chunk in response.iter_content(1024):
                                f.write(chunk)

                        logger.info(f"食物图像已从URL下载并保存到: {file_path}")
                    except Exception as e:
                        logger.error(f"保存下载图片失败: {str(e)}")
                        raise HTTPException(status_code=500, detail=f"保存图片失败: {str(e)}")

                    # 验证文件存在
                    if not os.path.exists(file_path):
                        logger.error(f"文件保存失败，路径不存在: {file_path}")
                        raise HTTPException(status_code=500, detail="文件保存失败")

                    # 验证文件大小
                    file_size = os.path.getsize(file_path)
                    if file_size == 0:
                        logger.error(f"文件保存失败，文件大小为0: {file_path}")
                        os.remove(file_path)  # 删除无效文件
                        raise HTTPException(status_code=500, detail="下载的图片为空")

                    # 构建返回URL
                    image_url = f"{filename}"
                    logger.info(f"生成的食物图像URL: {image_url}")
                except HTTPException:
                    # 重新抛出HTTP异常
                    raise
                except Exception as e:
                    logger.error(f"下载食物图像失败: {str(e)}")
                    raise HTTPException(status_code=400, detail=f"下载食物图像失败: {str(e)}")
        else:
            # 尝试从表单数据中找到可能的图像字段
            # 检查是否有其他字段包含图像数据
            image_found = False

            for field_name, field_value in form_fields.items():
                # 忽略已处理的字段
                if field_name in ['file', 'image', 'img', 'photo', 'image_url', 'food_name']:
                    continue

                # 检查是否可能是文件字段
                if hasattr(field_value, 'filename') and getattr(field_value, 'filename', None):
                    logger.info(f"发现可能的图像文件字段: {field_name}, 文件名: {field_value.filename}")
                    # 使用这个字段作为文件
                    actual_file = field_value
                    image_found = True

                    # 处理文件上传
                    logger.info(f"使用字段 {field_name} 处理上传文件: {actual_file.filename}")
                    # 验证文件类型
                    if not actual_file.content_type.startswith("image/"):
                        logger.warning(f"非图片文件类型: {actual_file.content_type}")
                        continue  # 尝试其他字段

                    # 获取文件扩展名
                    file_ext = os.path.splitext(actual_file.filename)[1]
                    if not file_ext:
                        # 根据content_type推断扩展名
                        if actual_file.content_type == "image/jpeg":
                            file_ext = ".jpg"
                        elif actual_file.content_type == "image/png":
                            file_ext = ".png"
                        elif actual_file.content_type == "image/gif":
                            file_ext = ".gif"
                        else:
                            file_ext = ".jpg"

                    # 生成文件名
                    filename = f"{safe_food_name}_{timestamp}_{random_str}{file_ext}"
                    file_path = os.path.join(date_dir, filename)

                    try:
                        # 保存文件
                        with open(file_path, "wb") as buffer:
                            shutil.copyfileobj(actual_file.file, buffer)

                        logger.info(f"食物图像文件已保存到: {file_path}")

                        # 构建返回URL
                        image_url = f"{filename}"
                        logger.info(f"生成的食物图像URL: {image_url}")
                        break  # 成功处理，退出循环
                    except Exception as e:
                        logger.error(f"保存上传文件失败: {str(e)}")
                        continue  # 尝试其他字段

                # 检查字符串字段是否包含base64图像
                elif isinstance(field_value, str) and field_value.startswith('data:image/'):
                    logger.info(f"发现可能的base64图像字段: {field_name}")
                    image_url = field_value
                    image_found = True

                    # 处理base64编码的图像数据
                    try:
                        # 解析base64数据
                        metadata, base64_data = image_url.split(',', 1)
                        content_type = metadata.split(';')[0].replace('data:', '')

                        # 确定文件扩展名
                        if content_type == 'image/jpeg':
                            file_ext = '.jpg'
                        elif content_type == 'image/png':
                            file_ext = '.png'
                        elif content_type == 'image/gif':
                            file_ext = '.gif'
                        else:
                            file_ext = '.jpg'

                        # 解码base64数据
                        try:
                            image_data = base64.b64decode(base64_data)
                        except Exception as e:
                            logger.error(f"Base64解码失败: {str(e)}")
                            continue  # 尝试其他字段

                        # 生成文件名
                        filename = f"{safe_food_name}_{timestamp}_{random_str}{file_ext}"
                        file_path = os.path.join(date_dir, filename)

                        # 保存图像数据到文件
                        with open(file_path, "wb") as f:
                            f.write(image_data)

                        logger.info(f"base64食物图像已保存到: {file_path}")

                        # 构建返回URL
                        image_url = f"{filename}"
                        logger.info(f"生成的食物图像URL: {image_url}")
                        break  # 成功处理，退出循环
                    except Exception as e:
                        logger.error(f"处理base64图像失败: {str(e)}")
                        continue  # 尝试其他字段

            # 如果没有找到有效的图像字段
            if not image_found:
                logger.warning("请求中既没有文件也没有image_url")
                raise HTTPException(status_code=400, detail="必须提供文件或image_url")

        # 构造响应数据
        response_data = {
            "success": True,
            "image_url": image_url,
            "filename": filename,
            "food_name": food_name
        }

        # 记录处理总时间
        elapsed_time = time.time() - start_time
        logger.info(f"食物图像处理总耗时: {elapsed_time:.4f}秒")

        return response_data
    except HTTPException as e:
        # 记录HTTP异常
        logger.error(f"食物图像上传HTTP异常: {e.status_code} - {e.detail}")
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"上传食物图像失败，未处理异常: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail=f"上传食物图像失败: {str(e)}"
        )


@router.get("/food-image/{secure_path}/{date}/{filename}", response_class=FileResponse)
async def get_food_image(
    secure_path: str,
    date: str,
    filename: str
):
    """
    获取食物图片 - 不需要身份验证

    通过安全路径、日期和文件名获取图片文件
    """
    file_path = f"/data/users/{secure_path}/uploaded_food_images/{date}/{filename}"

    # 检查文件是否存在
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="图片不存在")

    # 识别媒体类型
    content_type = "image/jpeg"  # 默认
    if filename.endswith(".png"):
        content_type = "image/png"
    elif filename.endswith(".gif"):
        content_type = "image/gif"

    # 返回文件
    return FileResponse(file_path, media_type=content_type)


@router.post("/settings/ai-character", response_model=schemas.UserSetting)
def update_ai_character_type(
    request: Request,
    character_type: str,
    current_user: models.User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
) -> Any:
    """
    更新用户的AI教练角色类型偏好

    Args:
        character_type: AI角色类型，可选值为 "motivational" 或 "strict"
    """
    logger.info(f"更新用户AI角色类型: user_id={current_user.id}, character_type={character_type}")

    # 验证角色类型
    valid_types = ["motivational", "strict"]
    if character_type not in valid_types:
        logger.warning(f"无效的角色类型: {character_type}")
        raise HTTPException(
            status_code=400,
            detail=f"无效的角色类型。有效值为: {', '.join(valid_types)}"
        )

    try:
        # 更新用户设置
        user_setting = crud.crud_user_setting.update_ai_character_type(
            db, user_id=current_user.id, character_type=character_type
        )

        if not user_setting:
            logger.error(f"更新用户设置失败: user_id={current_user.id}")
            raise HTTPException(status_code=500, detail="更新用户设置失败")

        logger.info(f"用户AI角色类型更新成功: user_id={current_user.id}, character_type={character_type}")
        return user_setting
    except Exception as e:
        logger.error(f"更新用户AI角色类型失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新用户AI角色类型失败: {str(e)}")


@router.get("/settings/ai-character", response_model=schemas.UserSetting)
def get_ai_character_type(
    current_user: models.User = Depends(deps.get_current_user),
    db: Session = Depends(deps.get_db)
) -> Any:
    """
    获取用户的AI教练角色类型偏好
    """
    try:
        # 获取用户设置
        user_setting = crud.crud_user_setting.get_by_user_id(db, user_id=current_user.id)

        if not user_setting:
            # 如果不存在，创建默认设置
            user_setting = crud.crud_user_setting.create_or_update(
                db, user_id=current_user.id, obj_in={"ai_character_type": "motivational"}
            )

        return user_setting
    except Exception as e:
        logger.error(f"获取用户AI角色类型失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取用户AI角色类型失败: {str(e)}")