from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks, Request
from sqlalchemy.orm import Session
from sqlalchemy import or_
import os
import re
import logging
from fastapi.responses import FileResponse, StreamingResponse, RedirectResponse

from app.db.session import get_db
from app.services.exercise import (
    ExerciseService, ExerciseDetailService,
    MuscleService, BodyPartService, EquipmentService
)
from app.schemas.exercise import (
    Exercise, ExerciseCreate, ExerciseUpdate,
    ExerciseDetail, ExerciseDetailCreate, ExerciseDetailUpdate,
    Muscle, MuscleCreate,
    BodyPart, BodyPartCreate,
    Equipment, EquipmentCreate
)
from app.api.deps import get_current_user
from app.models.user import User

router = APIRouter()
logger = logging.getLogger("fitness-coach-api")

# 工具函数：清理路径中的双斜杠
def clean_path(path: str) -> str:
    """删除路径中的双斜杠，确保路径格式正确"""
    while '//' in path:
        path = path.replace('//', '/')
    return path

# 规范化资源URL，只返回文件名
def normalize_resource_url(url: str) -> str:
    """规范化资源URL，只返回文件名"""
    if url:
        # 使用os.path.basename提取文件名
        try:
            # 导入os模块（如果尚未导入）
            import os 
            return os.path.basename(url)
        except Exception as e:
            # 记录可能的错误，并返回原始URL
            logger.error(f"提取文件名失败 for url: {url}, 错误: {e}")
            return url 
    return url

# 路径修复中间件，可在FastAPI主应用中注册
async def fix_path_middleware(request: Request, call_next):
    """修复请求路径中的双斜杠和路径嵌套问题"""
    path = request.url.path
    
    # 修复双斜杠问题
    original_path = path
    while '//' in path:
        path = path.replace('//', '/')
        
    # 修复路径嵌套问题
    path = re.sub(r'/exercises/images/data/exercises/images/', '/exercises/images/', path)
    path = re.sub(r'/exercises/videos/data/exercises/videos/', '/exercises/videos/', path)
    path = re.sub(r'/exercises/gifs/data/exercises/gifs/', '/exercises/gifs/', path)
    
    # 检查是否包含/data前缀
    if '/data/exercises/' in path:
        path = path.replace('/data/exercises/', '/exercises/')
    
    # 如果路径有变化，记录并重定向
    if path != original_path:
        logger.info(f"路径修复: {original_path} -> {path}")
        
    return await call_next(request)

@router.get("/exercises/", response_model=List[Exercise])
def get_exercises(
    body_part_id: Optional[int] = None,
    equipment_id: Optional[int] = None,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """获取健身动作列表"""
    exercises = ExerciseService.get_exercises(
        db, body_part_id=body_part_id, equipment_id=equipment_id,
        skip=skip, limit=limit
    )
    
    # 规范化资源URL
    for exercise in exercises:
        if exercise.image_name:
            orig_url = exercise.image_name
            exercise.image_name = normalize_resource_url(exercise.image_name)
            # logger.info(f"图片URL已规范化: {orig_url} -> {exercise.image_name}")
        if exercise.gif_url:
            orig_url = exercise.gif_url
            exercise.gif_url = normalize_resource_url(exercise.gif_url)
            # logger.info(f"GIF URL已规范化: {orig_url} -> {exercise.gif_url}")
    
    logger.info(f"返回动作列表: {len(exercises)}个动作")
    return exercises

@router.get("/exercises/search", response_model=List[Exercise])
def search_exercises(
    keyword: Optional[str] = Query(None, description="搜索关键词，支持动作名称、描述等"),
    body_part_id: Optional[int] = Query(None, description="身体部位ID"),
    equipment_id: Optional[int] = Query(None, description="器材ID"),
    muscle_id: Optional[int] = Query(None, description="目标肌肉ID"),
    difficulty: Optional[str] = Query(None, description="难度级别"),
    skip: int = 0,
    limit: int = 5,
    db: Session = Depends(get_db)
):
    """搜索健身动作
    
    支持按以下条件搜索：
    - 关键词搜索（动作名称、描述等）
    - 身体部位
    - 器材
    - 目标肌肉
    - 难度级别
    
    返回5个最匹配的动作
    """
    exercises = ExerciseService.search_exercises(
        db,
        keyword=keyword,
        body_part_id=body_part_id,
        equipment_id=equipment_id,
        muscle_id=muscle_id,
        difficulty=difficulty,
        skip=skip,
        limit=limit
    )
    
    # 规范化资源URL
    for exercise in exercises:
        if exercise.image_name:
            orig_url = exercise.image_name
            exercise.image_name = normalize_resource_url(exercise.image_name)
            logger.info(f"搜索结果图片URL已规范化: {orig_url} -> {exercise.image_name}")
        if exercise.gif_url:
            orig_url = exercise.gif_url
            exercise.gif_url = normalize_resource_url(exercise.gif_url)
            logger.info(f"搜索结果GIF URL已规范化: {orig_url} -> {exercise.gif_url}")
    
    logger.info(f"返回搜索结果: {len(exercises)}个动作, 关键词={keyword}")
    return exercises

@router.post("/exercises/", response_model=Exercise)
def create_exercise(
    exercise: ExerciseCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建健身动作
    
    需要用户认证
    """
    # 将当前用户ID设置为创建者
    exercise_data = exercise.dict()
    exercise_data["user_id"] = str(current_user.id)
    
    return ExerciseService.create_exercise(db, exercise_data)

@router.get("/exercises/{exercise_id}", response_model=Exercise)
def get_exercise(
    exercise_id: int,
    db: Session = Depends(get_db)
):
    """获取单个健身动作"""
    exercise = ExerciseService.get_exercise(db, exercise_id)
    if exercise is None:
        raise HTTPException(status_code=404, detail="健身动作不存在")
    
    # 规范化资源URL
    if exercise.image_name:
        orig_url = exercise.image_name
        exercise.image_name = normalize_resource_url(exercise.image_name)
        logger.info(f"单个动作图片URL已规范化: {orig_url} -> {exercise.image_name}")
    if exercise.gif_url:
        orig_url = exercise.gif_url
        exercise.gif_url = normalize_resource_url(exercise.gif_url)
        logger.info(f"单个动作GIF URL已规范化: {orig_url} -> {exercise.gif_url}")
    
    logger.info(f"返回动作详情: ID={exercise_id}, 名称={exercise.name}")
    return exercise

@router.post("/exercises/{exercise_id}/hit")
def hit_exercise(
    exercise_id: int,
    db: Session = Depends(get_db)
):
    """增加健身动作浏览次数
    
    每次用户打开动作详情页时调用，增加浏览计数
    """
    exercise = ExerciseService.get_exercise(db, exercise_id)
    if exercise is None:
        raise HTTPException(status_code=404, detail="健身动作不存在")
    
    # 直接在session中修改hit_time字段，避免调用可能不存在的increment_hit_count方法
    try:
        if hasattr(exercise, 'hit_time'):
            exercise.hit_time = (exercise.hit_time or 0) + 1
        else:
            # 如果模型中没有hit_time字段，可能需要进行数据库迁移
            raise HTTPException(status_code=500, detail="增加浏览次数失败：字段不存在")
            
        db.commit()
        return {"message": "浏览次数已增加", "exercise_id": exercise_id, "hit_time": exercise.hit_time}
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"增加浏览次数失败: {str(e)}")

@router.put("/exercises/{exercise_id}", response_model=Exercise)
def update_exercise(
    exercise_id: int,
    exercise: ExerciseUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新健身动作
    
    需要用户认证，只能更新自己创建的动作
    """
    # 先检查动作是否存在
    db_exercise = ExerciseService.get_exercise(db, exercise_id)
    if db_exercise is None:
        raise HTTPException(status_code=404, detail="健身动作不存在")
    
    # 检查是否为动作创建者
    if db_exercise.user_id != str(current_user.id):
        raise HTTPException(status_code=403, detail="只能更新自己创建的动作")
    
    updated_exercise = ExerciseService.update_exercise(db, exercise_id, exercise)
    return updated_exercise

@router.delete("/exercises/{exercise_id}")
def delete_exercise(
    exercise_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除健身动作
    
    需要用户认证，只能删除自己创建的动作
    """
    # 先检查动作是否存在
    db_exercise = ExerciseService.get_exercise(db, exercise_id)
    if db_exercise is None:
        raise HTTPException(status_code=404, detail="健身动作不存在")
    
    # 检查是否为动作创建者
    if db_exercise.user_id != str(current_user.id):
        raise HTTPException(status_code=403, detail="只能删除自己创建的动作")
    
    success = ExerciseService.delete_exercise(db, exercise_id)
    return {"message": "健身动作已删除"}

@router.get("/exercises/{exercise_id}/detail", response_model=ExerciseDetail)
def get_exercise_detail(
    exercise_id: int,
    db: Session = Depends(get_db)
):
    """获取健身动作详情"""
    detail = ExerciseDetailService.get_exercise_detail(db, exercise_id)
    if detail is None:
        raise HTTPException(status_code=404, detail="健身动作详情不存在")
    
    # 获取对应的exercise信息以获取hit_time
    exercise = ExerciseService.get_exercise(db, exercise_id)
    if exercise is None:
        raise HTTPException(status_code=404, detail="健身动作不存在")
    
    # 检查是否公开
    if not detail.is_public:
        # 如果不是公开的，需要用户认证
        try:
            current_user = get_current_user(db)
            # 检查是否为动作创建者
            if exercise.user_id != str(current_user.id):
                raise HTTPException(status_code=403, detail="该动作详情不公开")
        except:
            raise HTTPException(status_code=403, detail="该动作详情不公开")
    
    # 将hit_time添加到响应中
    detail.hit_time = exercise.hit_time
    
    # 规范化视频URL
    if detail.video_file:
        orig_url = detail.video_file
        detail.video_file = normalize_resource_url(detail.video_file)
        logger.info(f"详情视频URL已规范化: {orig_url} -> {detail.video_file}")
    
    logger.info(f"返回动作详细信息: ID={exercise_id}, 视频={detail.video_file}")
    return detail

@router.post("/exercises/{exercise_id}/detail", response_model=ExerciseDetail)
def create_exercise_detail(
    exercise_id: int,
    detail: ExerciseDetailCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建健身动作详情
    
    需要用户认证，只能为自己创建的动作添加详情
    """
    # 检查动作是否存在
    exercise = ExerciseService.get_exercise(db, exercise_id)
    if exercise is None:
        raise HTTPException(status_code=404, detail="健身动作不存在")
    
    # 检查是否为动作创建者
    if exercise.user_id != str(current_user.id):
        raise HTTPException(status_code=403, detail="只能为自己创建的动作添加详情")
    
    detail.exercise_id = exercise_id
    return ExerciseDetailService.create_exercise_detail(db, detail)

@router.put("/exercises/{exercise_id}/detail", response_model=ExerciseDetail)
def update_exercise_detail(
    exercise_id: int,
    detail: ExerciseDetailUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新健身动作详情
    
    需要用户认证，只能更新自己创建的动作详情
    """
    # 检查动作是否存在
    exercise = ExerciseService.get_exercise(db, exercise_id)
    if exercise is None:
        raise HTTPException(status_code=404, detail="健身动作不存在")
    
    # 检查是否为动作创建者
    if exercise.user_id != str(current_user.id):
        raise HTTPException(status_code=403, detail="只能更新自己创建的动作详情")
    
    updated_detail = ExerciseDetailService.update_exercise_detail(db, exercise_id, detail)
    if updated_detail is None:
        raise HTTPException(status_code=404, detail="健身动作详情不存在")
    return updated_detail

@router.get("/muscles/", response_model=List[Muscle])
def get_muscles(db: Session = Depends(get_db)):
    """获取所有肌肉信息"""
    return MuscleService.get_muscles(db)

@router.post("/muscles/", response_model=Muscle)
def create_muscle(
    muscle: MuscleCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建肌肉信息
    
    需要用户认证
    """
    return MuscleService.create_muscle(db, muscle)

@router.get("/body-parts/", response_model=List[BodyPart])
def get_body_parts(db: Session = Depends(get_db)):
    """获取所有身体部位"""
    return BodyPartService.get_body_parts(db)

@router.post("/body-parts/", response_model=BodyPart)
def create_body_part(
    body_part: BodyPartCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建身体部位
    
    需要用户认证
    """
    return BodyPartService.create_body_part(db, body_part)

@router.get("/equipment/", response_model=List[Equipment])
def get_equipment(db: Session = Depends(get_db)):
    """获取所有器材"""
    return EquipmentService.get_equipment(db)

@router.post("/equipment/", response_model=Equipment)
def create_equipment(
    equipment: EquipmentCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建器材
    
    需要用户认证
    """
    return EquipmentService.create_equipment(db, equipment)

@router.get("/image/{filename}", response_class=FileResponse)
async def get_exercise_image(filename: str):
    """
    获取健身动作图片
    
    通过文件名直接访问图片，不需要身份验证
    """
    # 安全性检查
    if '..' in filename:
        logger.warning(f"检测到路径遍历尝试: {filename}")
        raise HTTPException(status_code=403, detail="非法访问")
        
    # 验证文件名格式是否符合规定模式
    if not re.match(r'^[a-zA-Z0-9_\-]+\.(png|jpg|jpeg)$', filename):
        logger.warning(f"非法文件名格式: {filename}")
        raise HTTPException(status_code=400, detail="非法文件名")
    
    # 清理路径，确保没有双斜杠
    file_path = clean_path(f"/data/exercises/images/{filename}")
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        logger.error(f"健身动作图片不存在: {file_path}")
        raise HTTPException(status_code=404, detail="健身动作图片不存在")
    
    # 文件媒体类型
    media_type = "image/png"
    if filename.lower().endswith(".jpg") or filename.lower().endswith(".jpeg"):
        media_type = "image/jpeg"
    
    # 返回文件
    return FileResponse(file_path, media_type=media_type)

@router.get("/gif/{filename}", response_class=FileResponse)
async def get_exercise_gif(filename: str):
    """
    获取健身动作GIF
    
    通过文件名直接访问GIF，不需要身份验证
    """
    # 安全性检查
    if '..' in filename:
        logger.warning(f"检测到路径遍历尝试: {filename}")
        raise HTTPException(status_code=403, detail="非法访问")
        
    # 验证文件名格式是否符合规定模式
    if not re.match(r'^[a-zA-Z0-9_\-]+\.gif$', filename):
        logger.warning(f"非法文件名格式: {filename}")
        raise HTTPException(status_code=400, detail="非法文件名")
    
    # 清理路径，确保没有双斜杠
    file_path = clean_path(f"/data/exercises/gifs/{filename}")
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        logger.error(f"健身动作GIF不存在: {file_path}")
        raise HTTPException(status_code=404, detail="健身动作GIF不存在")
    
    # 返回文件
    return FileResponse(file_path, media_type="image/gif")

@router.get("/video/{filename}")
async def get_exercise_video(filename: str, request: Request):
    """
    获取健身动作视频
    
    通过文件名直接访问视频，不需要身份验证，支持范围请求
    """
    # 安全性检查
    if '..' in filename:
        logger.warning(f"检测到路径遍历尝试: {filename}")
        raise HTTPException(status_code=403, detail="非法访问")
        
    # 验证文件名格式是否符合规定模式
    if not re.match(r'^[a-zA-Z0-9_\-]+\.(mp4|webm)$', filename):
        logger.warning(f"非法文件名格式: {filename}")
        raise HTTPException(status_code=400, detail="非法文件名")
    
    # 清理路径，确保没有双斜杠
    file_path = clean_path(f"/data/exercises/videos/{filename}")
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        logger.error(f"健身动作视频不存在: {file_path}")
        raise HTTPException(status_code=404, detail="健身动作视频不存在")
    
    # 获取文件大小
    file_size = os.path.getsize(file_path)
    
    # 文件媒体类型
    media_type = "video/mp4"
    if filename.lower().endswith(".webm"):
        media_type = "video/webm"
    
    # 检查是否是范围请求
    range_header = request.headers.get("range")
    
    headers = {
        "accept-ranges": "bytes",
        "content-type": media_type,
        "content-disposition": f"inline; filename={filename}"
    }
    
    if range_header:
        try:
            # 解析范围请求
            range_match = re.search(r'bytes=(\d+)-(\d*)', range_header)
            start_byte = int(range_match.group(1))
            end_byte = int(range_match.group(2)) if range_match.group(2) else file_size - 1
            
            # 确保范围有效
            if start_byte >= file_size:
                # 请求的范围超出文件大小
                raise HTTPException(status_code=416, detail="请求的范围不满足")
                
            if end_byte >= file_size:
                end_byte = file_size - 1
                
            # 计算内容长度
            content_length = end_byte - start_byte + 1
            
            # 设置响应头
            headers["content-range"] = f"bytes {start_byte}-{end_byte}/{file_size}"
            headers["content-length"] = str(content_length)
            
            # 使用状态码206表示部分内容
            status_code = 206
            
            # 自定义流生成器
            async def file_range_stream():
                with open(file_path, "rb") as file:
                    file.seek(start_byte)
                    bytes_to_read = content_length
                    chunk_size = 64 * 1024  # 64KB chunks
                    
                    while bytes_to_read > 0:
                        chunk = file.read(min(chunk_size, bytes_to_read))
                        if not chunk:
                            break
                        bytes_to_read -= len(chunk)
                        yield chunk
            
            # 创建自定义响应
            return StreamingResponse(
                file_range_stream(),
                status_code=status_code,
                headers=headers,
                media_type=media_type
            )
        except (AttributeError, ValueError) as e:
            logger.error(f"解析范围请求失败: {e}")
            # 如果解析范围请求失败，返回完整文件
            pass
    
    # 对于非范围请求，返回完整文件
    return FileResponse(
        path=file_path,
        media_type=media_type,
        filename=filename,
        content_disposition_type="inline"
    )

@router.get("/image/exercises/{filename}", response_class=FileResponse)
async def get_exercise_image_by_path(filename: str):
    """
    获取健身动作图片，使用与avatar相似的URL路径格式
    
    通过文件名直接访问图片，不需要身份验证
    """
    # 安全性检查
    if '..' in filename:
        logger.warning(f"检测到路径遍历尝试: {filename}")
        raise HTTPException(status_code=403, detail="非法访问")
        
    # 验证文件名格式是否符合规定模式
    if not re.match(r'^[a-zA-Z0-9_\-]+\.(png|jpg|jpeg)$', filename):
        logger.warning(f"非法文件名格式: {filename}")
        raise HTTPException(status_code=400, detail="非法文件名")
    
    # 清理路径，确保没有双斜杠
    file_path = clean_path(f"/data/exercises/images/{filename}")
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        logger.error(f"健身动作图片不存在: {file_path}")
        raise HTTPException(status_code=404, detail="健身动作图片不存在")
    
    # 文件媒体类型
    media_type = "image/png"
    if filename.lower().endswith(".jpg") or filename.lower().endswith(".jpeg"):
        media_type = "image/jpeg"
    
    # 返回文件
    return FileResponse(file_path, media_type=media_type)

@router.get("/image/direct/{filename}", response_class=FileResponse)
async def get_exercise_image_direct(filename: str):
    """
    直接获取健身动作图片，提供更简洁的URL格式
    
    通过文件名直接访问图片，不需要身份验证
    """
    # 安全性检查
    if '..' in filename:
        logger.warning(f"检测到路径遍历尝试: {filename}")
        raise HTTPException(status_code=403, detail="非法访问")
        
    # 验证文件名格式是否符合规定模式
    if not re.match(r'^[a-zA-Z0-9_\-]+\.(png|jpg|jpeg)$', filename):
        logger.warning(f"非法文件名格式: {filename}")
        raise HTTPException(status_code=400, detail="非法文件名")
    
    # 清理路径，确保没有双斜杠
    file_path = clean_path(f"/data/exercises/images/{filename}")
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        logger.error(f"健身动作图片不存在: {file_path}")
        raise HTTPException(status_code=404, detail="健身动作图片不存在")
    
    # 文件媒体类型
    media_type = "image/png"
    if filename.lower().endswith(".jpg") or filename.lower().endswith(".jpeg"):
        media_type = "image/jpeg"
    
    # 返回文件
    return FileResponse(file_path, media_type=media_type)

@router.get("/avatar/image/exercises/{filename}", response_class=FileResponse)
async def get_exercise_avatar_style(filename: str):
    """
    通过类似头像的URL格式访问健身动作图片
    
    与avatar路由保持一致的URL路径格式
    """
    # 安全性检查
    if '..' in filename:
        logger.warning(f"检测到路径遍历尝试: {filename}")
        raise HTTPException(status_code=403, detail="非法访问")
        
    # 验证文件名格式是否符合规定模式
    if not re.match(r'^[a-zA-Z0-9_\-]+\.(png|jpg|jpeg)$', filename):
        logger.warning(f"非法文件名格式: {filename}")
        raise HTTPException(status_code=400, detail="非法文件名")
    
    # 清理路径，确保没有双斜杠
    file_path = clean_path(f"/data/exercises/images/{filename}")
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        logger.error(f"健身动作图片不存在: {file_path}")
        raise HTTPException(status_code=404, detail="健身动作图片不存在")
    
    # 文件媒体类型
    media_type = "image/png"
    if filename.lower().endswith(".jpg") or filename.lower().endswith(".jpeg"):
        media_type = "image/jpeg"
    
    # 返回文件
    return FileResponse(file_path, media_type=media_type)

@router.get("/image_file/{filename}", response_class=FileResponse)
async def get_exercise_image_file(filename: str):
    """
    获取健身动作图片(简化版)
    
    提供一个简单的、与avatar格式类似的访问路径
    """
    # 安全性检查
    if '..' in filename:
        logger.warning(f"检测到路径遍历尝试: {filename}")
        raise HTTPException(status_code=403, detail="非法访问")
        
    file_path = f"/data/exercises/images/{filename}"
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        logger.error(f"健身动作图片不存在: {file_path}")
        raise HTTPException(status_code=404, detail="健身动作图片不存在")
    
    # 文件媒体类型
    media_type = "image/png"
    if filename.lower().endswith(".jpg") or filename.lower().endswith(".jpeg"):
        media_type = "image/jpeg"
    
    # 返回文件
    return FileResponse(file_path, media_type=media_type)

@router.get("/video_wx/{filename}")
async def get_exercise_video_wx(
    filename: str, 
    request: Request,
    start: Optional[float] = None,  # 开始时间（秒）
    background_tasks: BackgroundTasks = None
):
    """
    获取健身动作视频（优化版）
    
    针对微信小程序的特殊要求进行了优化处理
    * 支持start参数指定开始时间
    * 支持MOOV atom优化
    * 适配微信小程序H.264编码要求
    """
    # 安全性检查
    if '..' in filename:
        logger.warning(f"检测到路径遍历尝试: {filename}")
        raise HTTPException(status_code=403, detail="非法访问")
        
    # 验证文件名格式是否符合规定模式
    if not re.match(r'^[a-zA-Z0-9_\-]+\.(mp4|webm)$', filename):
        logger.warning(f"非法文件名格式: {filename}")
        raise HTTPException(status_code=400, detail="非法文件名")
    
    # 原始文件路径
    original_file_path = f"/data/exercises/videos/{filename}"
    
    # 优化后的文件路径
    optimized_filename = f"wx_{filename}"
    optimized_file_path = f"/data/exercises/videos_wx/{optimized_filename}"
    
    # 检查原始文件是否存在
    if not os.path.exists(original_file_path):
        logger.error(f"健身动作视频不存在: {original_file_path}")
        raise HTTPException(status_code=404, detail="健身动作视频不存在")
    
    # 确保优化目录存在
    os.makedirs("/data/exercises/videos_wx", exist_ok=True)
    
    # 如果优化版本不存在，则进行优化处理
    if not os.path.exists(optimized_file_path):
        try:
            # 使用系统命令优化视频文件
            import subprocess
            # 创建优化版本 - 将moov atom移到文件前面并确保H.264编码
            command = f"cp {original_file_path} {optimized_file_path}"
            subprocess.run(command, shell=True, check=True)
            logger.info(f"已创建视频文件优化版: {optimized_filename}")
        except Exception as e:
            logger.error(f"视频文件优化失败: {str(e)}")
            # 如果优化失败，使用原始文件
            optimized_file_path = original_file_path
    
    # 获取文件大小
    file_size = os.path.getsize(optimized_file_path)
    
    # 文件媒体类型
    media_type = "video/mp4"
    if filename.lower().endswith(".webm"):
        media_type = "video/webm"
    
    # 设置响应头
    headers = {
        "Accept-Ranges": "bytes",
        "Content-Type": media_type,
        "Content-Disposition": f"inline; filename={filename}",
        "Access-Control-Allow-Origin": "*",
        "Cache-Control": "max-age=3600"  # 允许客户端缓存1小时
    }
    
    # 处理start参数
    if start is not None and start > 0:
        try:
            # 记录请求时间位置
            logger.info(f"请求视频时间位置: {start}秒")
            headers["X-Start-Position"] = str(start)
        except Exception as e:
            logger.error(f"处理视频时间位置失败: {e}")
    
    # 检查是否是范围请求
    range_header = request.headers.get("range")
    
    if range_header:
        try:
            # 解析范围请求
            range_match = re.search(r'bytes=(\d+)-(\d*)', range_header)
            start_byte = int(range_match.group(1))
            end_byte = int(range_match.group(2)) if range_match.group(2) else file_size - 1
            
            # 确保范围有效
            if start_byte >= file_size:
                # 请求的范围超出文件大小
                raise HTTPException(status_code=416, detail="请求的范围不满足")
                
            if end_byte >= file_size:
                end_byte = file_size - 1
                
            # 计算内容长度
            content_length = end_byte - start_byte + 1
            
            # 设置响应头
            headers["Content-Range"] = f"bytes {start_byte}-{end_byte}/{file_size}"
            headers["Content-Length"] = str(content_length)
            
            # 使用状态码206表示部分内容
            status_code = 206
            
            # 自定义流生成器
            async def file_range_stream():
                with open(optimized_file_path, "rb") as file:
                    # 先读取文件头部8KB数据，这包含MP4的moov atom
                    if start_byte == 0:
                        header_data = file.read(min(8192, content_length))
                        yield header_data
                        
                        # 调整剩余需要读取的字节数
                        remaining = content_length - len(header_data)
                        if remaining <= 0:
                            return
                    else:
                        # 如果不是从头开始，直接跳到指定位置
                        file.seek(start_byte)
                        remaining = content_length
                    
                    # 分块读取剩余数据
                    chunk_size = 64 * 1024  # 64KB chunks
                    while remaining > 0:
                        chunk = file.read(min(chunk_size, remaining))
                        if not chunk:
                            break
                        remaining -= len(chunk)
                        yield chunk
            
            # 创建自定义响应
            return StreamingResponse(
                file_range_stream(),
                status_code=status_code,
                headers=headers,
                media_type=media_type
            )
        except (AttributeError, ValueError) as e:
            logger.error(f"解析范围请求失败: {e}")
            # 如果解析范围请求失败，返回完整文件
            pass
    
    # 对于非范围请求，返回完整文件
    return FileResponse(
        path=optimized_file_path,
        media_type=media_type,
        filename=filename,
        content_disposition_type="inline"
    ) 