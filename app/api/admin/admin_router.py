from fastapi import APIRouter, Request, Query, HTTPException, status
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from datetime import datetime, date, timedelta
from typing import Optional, List
import os
import logging

from app.api.admin.admin_auth import admin_auth_required
from app.core.config import settings

# 配置日志
logger = logging.getLogger("fitness-coach-api")

# 模板配置
templates = Jinja2Templates(directory="app/templates")

router = APIRouter(prefix="/admin", tags=["admin"])

# 错误处理辅助函数
def handle_error(e, request):
    """统一的错误处理"""
    logger.error(f"管理后台错误: {str(e)}")
    
    # 始终返回错误页面，无论开发环境还是生产环境
    return HTMLResponse(
        f"""
        <html>
            <head>
                <title>系统错误</title>
                <style>
                    body {{ font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif; padding: 20px; }}
                    .error-container {{ max-width: 800px; margin: 0 auto; background-color: #f8f9fa; border-radius: 5px; padding: 20px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }}
                    h1 {{ color: #dc3545; }}
                    .back-link {{ margin-top: 20px; }}
                    .back-link a {{ text-decoration: none; color: #007bff; }}
                    .error-details {{ background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin-top: 20px; overflow-x: auto; }}
                    .error-message {{ font-weight: bold; margin-bottom: 10px; }}
                </style>
            </head>
            <body>
                <div class="error-container">
                    <h1>系统发生错误</h1>
                    <p>抱歉，系统处理您的请求时发生了错误。</p>
                    <div class="error-details">
                        <div class="error-message">错误信息：</div>
                        <pre>{str(e)}</pre>
                    </div>
                    <div class="back-link">
                        <a href="/admin">返回首页</a>
                    </div>
                </div>
            </body>
        </html>
        """,
        status_code=200
    )

@router.get("/", response_class=HTMLResponse)
@admin_auth_required()
async def admin_dashboard(request: Request):
    """管理后台首页 - 简化版，不依赖数据库"""
    try:
        # 使用静态数据代替数据库查询
        user_count = 158
        new_today = 12
        share_count = 35
        
        # 模拟的用户数据
        recent_users = [
            {
                "id": 1,
                "nickname": "测试用户1",
                "phone": "13800138001",
                "gender": "male",
                "created_at": datetime.now(),
            },
            {
                "id": 2,
                "nickname": "测试用户2",
                "phone": "13800138002",
                "gender": "female",
                "created_at": datetime.now() - timedelta(hours=2),
            },
            {
                "id": 3,
                "nickname": "测试用户3",
                "phone": "13800138003",
                "gender": "male",
                "created_at": datetime.now() - timedelta(days=1),
            }
        ]
        
        return templates.TemplateResponse(
            "admin/index.html",
            {
                "request": request,
                "active": "dashboard",
                "user_count": user_count,
                "new_today": new_today,
                "share_count": share_count,
                "recent_users": recent_users
            }
        )
    except Exception as e:
        return handle_error(e, request)

@router.get("/users", response_class=HTMLResponse)
@admin_auth_required()
async def list_users(
    request: Request,
    q: Optional[str] = None,
    page: int = Query(1, ge=1)
):
    """用户列表页面 - 简化版，不依赖数据库"""
    try:
        # 模拟的用户数据
        users = [
            {
                "id": 1,
                "nickname": "测试用户1",
                "avatar_url": "https://example.com/avatar1.jpg",
                "phone": "13800138001",
                "gender": "male",
                "age": 30,
                "weight": 70.0,
                "height": 175.0,
                "bmi": 22.9,
                "created_at": datetime.now(),
            },
            {
                "id": 2,
                "nickname": "测试用户2",
                "avatar_url": "https://example.com/avatar2.jpg",
                "phone": "13800138002",
                "gender": "female",
                "age": 28,
                "weight": 55.0,
                "height": 165.0,
                "bmi": 20.2,
                "created_at": datetime.now() - timedelta(days=1),
            },
            {
                "id": 3,
                "nickname": "测试用户3",
                "avatar_url": "https://example.com/avatar3.jpg",
                "phone": "13800138003",
                "gender": "male",
                "age": 35,
                "weight": 80.0,
                "height": 180.0,
                "bmi": 24.7,
                "created_at": datetime.now() - timedelta(days=2),
            }
        ]
        
        return templates.TemplateResponse(
            "admin/users.html",
            {
                "request": request,
                "active": "users",
                "users": users,
                "query": q,
                "page": page,
                "total_pages": 1
            }
        )
    except Exception as e:
        return handle_error(e, request)

@router.get("/users/{user_id}", response_class=HTMLResponse)
@admin_auth_required()
async def user_detail(
    request: Request,
    user_id: int
):
    """用户详情页面 - 简化版，不依赖数据库"""
    try:
        # 模拟的用户数据
        if user_id == 1:
            user = {
                "id": 1,
                "nickname": "测试用户1",
                "avatar_url": "https://example.com/avatar1.jpg",
                "phone": "13800138001",
                "gender": "male",
                "age": 30,
                "weight": 70.0,
                "height": 175.0,
                "bmi": 22.9,
                "activity_level": 3,
                "tedd": 2500,
                "country": "China",
                "province": "Zhejiang",
                "city": "Hangzhou",
                "openid": "test_openid_1",
                "email": "<EMAIL>",
                "completed": True,
                "created_at": datetime.now() - timedelta(days=10),
                "updated_at": datetime.now() - timedelta(days=2),
            }
            
            user_setting = {
                "id": 1,
                "user_id": 1,
                "notification_enabled": True,
                "created_at": datetime.now() - timedelta(days=10),
                "updated_at": datetime.now() - timedelta(days=2),
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"用户ID {user_id} 不存在"
            )
        
        return templates.TemplateResponse(
            "admin/user_detail.html",
            {
                "request": request,
                "active": "users",
                "user": user,
                "user_setting": user_setting
            }
        )
    except HTTPException as e:
        raise e
    except Exception as e:
        return handle_error(e, request)

@router.get("/settings", response_class=HTMLResponse)
@admin_auth_required()
async def list_settings(
    request: Request,
    q: Optional[str] = None,
    page: int = Query(1, ge=1)
):
    """用户设置列表页面 - 简化版，不依赖数据库"""
    try:
        # 模拟的设置数据
        settings_data = [
            {
                "id": 1,
                "user_id": 1,
                "notification_enabled": True,
                "created_at": datetime.now() - timedelta(days=10),
                "updated_at": datetime.now() - timedelta(days=2),
            },
            {
                "id": 2,
                "user_id": 2,
                "notification_enabled": False,
                "created_at": datetime.now() - timedelta(days=8),
                "updated_at": datetime.now() - timedelta(days=1),
            },
            {
                "id": 3,
                "user_id": 3,
                "notification_enabled": True,
                "created_at": datetime.now() - timedelta(days=5),
                "updated_at": datetime.now(),
            }
        ]
        
        return templates.TemplateResponse(
            "admin/settings.html",
            {
                "request": request,
                "active": "settings",
                "settings": settings_data,
                "query": q,
                "page": page,
                "total_pages": 1
            }
        )
    except Exception as e:
        return handle_error(e, request)

@router.get("/share_tracks", response_class=HTMLResponse)
@admin_auth_required()
async def list_share_tracks(
    request: Request,
    q: Optional[str] = None,
    page: int = Query(1, ge=1)
):
    """分享追踪列表页面 - 简化版，不依赖数据库"""
    try:
        # 模拟的分享追踪数据
        tracks = [
            {
                "id": 1,
                "shared_by": 1,
                "scanned_by": 2,
                "page": "index",
                "share_type": "qrcode",
                "created_at": datetime.now() - timedelta(days=5),
                "scanned_at": datetime.now() - timedelta(days=3),
            },
            {
                "id": 2,
                "shared_by": 2,
                "scanned_by": 3,
                "page": "profile",
                "share_type": "qrcode",
                "created_at": datetime.now() - timedelta(days=4),
                "scanned_at": datetime.now() - timedelta(days=2),
            },
            {
                "id": 3,
                "shared_by": 1,
                "scanned_by": None,
                "page": "settings",
                "share_type": "link",
                "created_at": datetime.now() - timedelta(days=1),
                "scanned_at": None,
            }
        ]
        
        return templates.TemplateResponse(
            "admin/share_tracks.html",
            {
                "request": request,
                "active": "share_tracks",
                "tracks": tracks,
                "query": q,
                "page": page,
                "total_pages": 1
            }
        )
    except Exception as e:
        return handle_error(e, request) 