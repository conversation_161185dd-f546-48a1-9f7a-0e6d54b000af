from fastapi import Request, HTTPException, status
from fastapi.responses import RedirectResponse
from functools import wraps
from app.core.config import settings
import logging

logger = logging.getLogger("fitness-coach-api")

# 基本认证装饰器
def admin_auth_required():
    """
    简单的管理员认证装饰器
    在开发环境下自动通过认证
    在生产环境要求使用HTTP基本认证
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(request: Request, *args, **kwargs):
            logger.info(f"管理后台认证 - 开发环境: {settings.IS_DEV}")
            
            # 开发环境自动通过认证
            if settings.IS_DEV:
                return await func(request, *args, **kwargs)
                
            # 生产环境检查认证
            if request.headers.get("authorization"):
                auth_type, credentials = request.headers.get("authorization").split(" ", 1)
                if auth_type.lower() == "basic":
                    import base64
                    decoded = base64.b64decode(credentials).decode("utf-8")
                    username, password = decoded.split(":", 1)
                    if username == settings.ADMIN_USERNAME and password == settings.ADMIN_PASSWORD:
                        return await func(request, *args, **kwargs)
            
            # 如果认证失败，返回401
            headers = {"WWW-Authenticate": "Basic"}
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的凭证",
                headers=headers
            )
        
        return wrapper
    return decorator 