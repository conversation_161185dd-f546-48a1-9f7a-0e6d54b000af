from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import desc
from decimal import Decimal

from app.models.nutrient import (
    VitaminRNI, MineralRNI, VitaminPregnancyInc, 
    WaterRNI, WaterPregnancyInc, OtherDietarySplUl,
    NutritionReferenceMain, NutritionPregnancyInc
)

async def get_vitamin_rni(db: Session, age: Decimal, sex: str) -> Optional[VitaminRNI]:
    """获取指定年龄和性别的维生素推荐摄入量"""
    query = db.query(VitaminRNI)
    
    # 处理婴幼儿通用记录
    if age < 1 and sex in ['male', 'female']:
        filtered_query = query.filter(
            VitaminRNI.age_start_years <= age,
            VitaminRNI.sex == 'both'
        ).order_by(desc(VitaminRNI.age_start_years))
    else:
        # 对于特定性别，查找适用的年龄段记录
        filtered_query = query.filter(
            VitaminRNI.age_start_years <= age,
            (VitaminRNI.sex == sex) | (VitaminRNI.sex == 'both')
        ).order_by(desc(VitaminRNI.age_start_years))
    
    # 返回最接近但不超过用户年龄的记录
    return filtered_query.first()

async def get_mineral_rni(db: Session, age: Decimal, sex: str) -> Optional[MineralRNI]:
    """获取指定年龄和性别的矿物质推荐摄入量"""
    query = db.query(MineralRNI)
    
    # 处理婴幼儿通用记录
    if age < 1 and sex in ['male', 'female']:
        filtered_query = query.filter(
            MineralRNI.age_start_years <= age,
            MineralRNI.sex == 'both'
        ).order_by(desc(MineralRNI.age_start_years))
    else:
        # 对于特定性别，查找适用的年龄段记录
        filtered_query = query.filter(
            MineralRNI.age_start_years <= age,
            (MineralRNI.sex == sex) | (MineralRNI.sex == 'both')
        ).order_by(desc(MineralRNI.age_start_years))
    
    # 返回最接近但不超过用户年龄的记录
    return filtered_query.first()

async def get_pregnancy_increment(db: Session, stage: str) -> Optional[VitaminPregnancyInc]:
    """获取指定孕期阶段的维生素增量需求"""
    if not stage:
        return None
    return db.query(VitaminPregnancyInc).filter(VitaminPregnancyInc.stage == stage).first()

# 新增函数
async def get_water_rni(db: Session, age: Decimal, sex: str) -> Optional[WaterRNI]:
    """获取指定年龄和性别的水分推荐摄入量"""
    query = db.query(WaterRNI).filter(
        WaterRNI.age_start_years <= age,
        WaterRNI.sex == sex
    ).order_by(desc(WaterRNI.age_start_years))
    
    return query.first()

async def get_water_pregnancy_inc(db: Session, stage: str) -> Optional[WaterPregnancyInc]:
    """获取指定孕期阶段的水分增量需求"""
    if not stage:
        return None
    return db.query(WaterPregnancyInc).filter(WaterPregnancyInc.stage == stage).first()

async def get_other_dietary_spl_ul(db: Session, names: List[str] = None) -> List[OtherDietarySplUl]:
    """
    获取指定膳食成分的SPL和UL值
    
    Args:
        db: 数据库会话
        names: 膳食成分的字段名称列表，如'vitamin_a'、'phosphor'等
    """
    query = db.query(OtherDietarySplUl)
    if names:
        query = query.filter(OtherDietarySplUl.name_cn.in_(names))
    
    return query.all()

async def get_nutrition_main(db: Session, age: Decimal, sex: str) -> Optional[NutritionReferenceMain]:
    """获取指定年龄和性别的宏量营养素参考值"""
    query = db.query(NutritionReferenceMain).filter(
        NutritionReferenceMain.age_start_years <= age,
        NutritionReferenceMain.sex == sex
    ).order_by(desc(NutritionReferenceMain.age_start_years))
    
    return query.first()

async def get_nutrition_pregnancy_inc(db: Session, stage: str) -> Optional[NutritionPregnancyInc]:
    """获取指定孕期阶段的宏量营养素增量需求"""
    if not stage:
        return None
    return db.query(NutritionPregnancyInc).filter(NutritionPregnancyInc.stage == stage).first()
