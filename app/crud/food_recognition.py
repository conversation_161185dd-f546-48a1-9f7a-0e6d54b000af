from typing import List, Optional, Dict, Union
from datetime import date
from sqlalchemy import func, and_, or_
from sqlalchemy.orm import Session

from app.models.meal import FoodRecognition
from app.schemas.food_recognition import FoodRecognitionCreate, FoodRecognitionUpdate
from app.crud.base import CRUDBase

class CRUDFoodRecognition(CRUDBase[FoodRecognition, FoodRecognitionCreate, FoodRecognitionUpdate]):
    def get(self, db: Session, id: int) -> Optional[FoodRecognition]:
        """获取单个识别记录"""
        return db.query(FoodRecognition).filter(FoodRecognition.id == id).first()

    def get_multi(
        self, 
        db: Session, 
        *, 
        user_id: str = None, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[FoodRecognition]:
        """获取多个识别记录"""
        query = db.query(FoodRecognition)
        if user_id:
            query = query.filter(FoodRecognition.user_id == user_id)
        return query.order_by(FoodRecognition.created_at.desc()).offset(skip).limit(limit).all()

    def get_by_status(
        self,
        db: Session,
        *,
        user_id: str,
        status: str,
        skip: int = 0,
        limit: int = 20
    ) -> List[FoodRecognition]:
        """根据状态获取识别记录"""
        return db.query(FoodRecognition).filter(
            FoodRecognition.user_id == user_id,
            FoodRecognition.status == status
        ).order_by(FoodRecognition.created_at.desc()).offset(skip).limit(limit).all()

    def get_pending(
        self,
        db: Session,
        *,
        user_id: str,
        skip: int = 0,
        limit: int = 20
    ) -> List[FoodRecognition]:
        """获取待确认的识别记录"""
        return db.query(FoodRecognition).filter(
            FoodRecognition.user_id == user_id,
            FoodRecognition.status == "completed",
            FoodRecognition.meal_record_id.is_(None)
        ).order_by(FoodRecognition.created_at.desc()).offset(skip).limit(limit).all()

    def get_multi_by_status(
        self,
        db: Session,
        *,
        user_id: str,
        status_list: List[str],
        skip: int = 0,
        limit: int = 20
    ) -> List[FoodRecognition]:
        """获取多个状态的识别记录"""
        return db.query(FoodRecognition).filter(
            FoodRecognition.user_id == user_id,
            FoodRecognition.status.in_(status_list)
        ).order_by(FoodRecognition.created_at.desc()).offset(skip).limit(limit).all()

    def get_by_date_range(
        self,
        db: Session,
        *,
        user_id: str,
        start_date,
        end_date,
        status: List[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[FoodRecognition]:
        """根据日期范围获取识别记录"""
        query = db.query(FoodRecognition).filter(
            FoodRecognition.user_id == user_id,
            FoodRecognition.meal_date >= start_date,
            FoodRecognition.meal_date <= end_date
        )
        
        if status:
            query = query.filter(FoodRecognition.status.in_(status))
            
        return query.order_by(FoodRecognition.meal_date.desc()).offset(skip).limit(limit).all()

    def get_stats_by_user(
        self,
        db: Session,
        *,
        user_id: str
    ) -> Dict[str, int]:
        """获取用户的识别记录统计信息"""
        stats = {}
        
        # 获取所有状态的记录数
        status_counts = db.query(
            FoodRecognition.status,
            func.count(FoodRecognition.id)
        ).filter(
            FoodRecognition.user_id == user_id
        ).group_by(
            FoodRecognition.status
        ).all()
        
        # 转换为字典
        for status, count in status_counts:
            stats[status] = count
        
        # 获取总记录数
        stats["total"] = sum(stats.values())
        
        return stats

    def get_multi_by_user(
        self,
        db: Session,
        *,
        user_id: str,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        status: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[FoodRecognition]:
        """
        获取用户的食物识别记录，支持按日期范围和状态筛选
        """
        query = db.query(FoodRecognition).filter(FoodRecognition.user_id == user_id)
        
        if start_date:
            query = query.filter(FoodRecognition.meal_date >= start_date)
        
        if end_date:
            query = query.filter(FoodRecognition.meal_date <= end_date)
            
        if status:
            query = query.filter(FoodRecognition.status == status)
            
        return query.order_by(FoodRecognition.created_at.desc()).offset(skip).limit(limit).all()

food_recognition = CRUDFoodRecognition(FoodRecognition) 