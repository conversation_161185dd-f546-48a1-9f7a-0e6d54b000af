from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.crud.base import CRUDBase
from app.models.share_track import ShareTrack
from app.schemas.share_track import ShareTrackCreate, ShareTrackUpdate


class CRUDShareTrack(CRUDBase[ShareTrack, ShareTrackCreate, ShareTrackUpdate]):
    def get_by_user(self, db: Session, *, user_id: int) -> List[ShareTrack]:
        return db.query(self.model).filter(self.model.shared_by == user_id).all()
    
    def get_existing_qrcode(
        self, db: Session, *, user_id: int, page: str, scene: str
    ) -> Optional[ShareTrack]:
        """获取已存在的二维码记录"""
        return db.query(self.model).filter(
            and_(
                self.model.shared_by == user_id,
                self.model.page == page,
                self.model.scene == scene,
                self.model.share_type == "qrcode",
                self.model.is_active == True
            )
        ).first()
    
    def get_by_qrcode_url(self, db: Session, *, qrcode_url: str) -> Optional[ShareTrack]:
        """通过二维码URL获取分享记录"""
        return db.query(self.model).filter(
            and_(
                self.model.qrcode_url == qrcode_url,
                self.model.share_type == "qrcode"
            )
        ).first()
    
    def get_all_qrcodes(self, db: Session) -> List[ShareTrack]:
        """获取所有二维码记录"""
        return db.query(self.model).filter(
            self.model.share_type == "qrcode"
        ).all()


share_track = CRUDShareTrack(ShareTrack) 