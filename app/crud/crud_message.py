from typing import List, Optional, Dict, Any, Tuple
from fastapi.encoders import jsonable_encoder
from sqlalchemy.orm import Session, joinedload
from app.crud.base import CRUDBase
from app.models.message import Message, MessageRole
from app.models.conversation import Conversation
from app.schemas.message import MessageCreate, MessageUpdate

class CRUDMessage(CRUDBase[Message, MessageCreate, MessageUpdate]):
    def create_with_conversation(
        self, db: Session, *, obj_in: MessageCreate, conversation_id: int, user_id: Optional[int] = None
    ) -> Message:
        """创建新消息，指定对话ID和用户ID"""
        obj_in_data = jsonable_encoder(obj_in)

        # 移除可能存在的字段，避免重复传入导致错误
        if "conversation_id" in obj_in_data:
            del obj_in_data["conversation_id"]
        if "user_id" in obj_in_data:
            del obj_in_data["user_id"]

        # 创建消息记录，显式传入conversation_id和user_id
        db_obj = Message(**obj_in_data, conversation_id=conversation_id, user_id=user_id)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_conversation_messages(
        self, db: Session, *, conversation_id: int, skip: int = 0, limit: int = 1000
    ) -> List[Message]:
        """获取会话中的所有消息，按时间排序"""
        return (
            db.query(Message)
            .filter(Message.conversation_id == conversation_id)
            .order_by(Message.created_at)
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_conversation_messages_desc(
        self, db: Session, *, conversation_id: int, skip: int = 0, limit: int = 5
    ) -> List[Message]:
        """获取会话中的消息，按时间倒序排序，用于获取最新消息"""
        return (
            db.query(Message)
            .filter(Message.conversation_id == conversation_id)
            .order_by(Message.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_recent_messages_by_user(
        self, db: Session, *, user_id: int, skip: int = 0, limit: int = 5
    ) -> List[Message]:
        """获取用户所有会话中最近的消息，按时间倒序排序"""
        return (
            db.query(Message)
            .filter(Message.user_id == user_id)
            .options(joinedload(Message.conversation))
            .order_by(Message.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_conversation_history(
        self, db: Session, *, conversation_id: int, limit: int = 20
    ) -> List[Dict[str, str]]:
        """获取格式化的会话历史，用于传递给LLM"""
        messages = (
            db.query(Message)
            .filter(Message.conversation_id == conversation_id)
            .order_by(Message.created_at)
            .limit(limit)
            .all()
        )

        formatted_messages = []
        for msg in messages:
            formatted_messages.append({
                "role": msg.role.value,
                "content": msg.content
            })

        return formatted_messages

    def get_conversation_history_raw(
        self, db: Session, *, conversation_id: int, limit: int = 20
    ) -> List[Dict[str, Any]]:
        """获取原始会话历史（包含完整元数据），用于处理消息恢复"""
        messages = (
            db.query(Message)
            .filter(Message.conversation_id == conversation_id)
            .order_by(Message.created_at)
            .limit(limit)
            .all()
        )

        raw_messages = []
        for msg in messages:
            # 将 SQLAlchemy 对象转换为字典
            message_dict = {
                "id": msg.id,
                "conversation_id": msg.conversation_id,
                "role": msg.role.value,
                "content": msg.content,
                "created_at": msg.created_at.isoformat() if msg.created_at else None,
                "meta_info": msg.meta_info
            }
            if hasattr(msg, 'user_id') and msg.user_id:
                message_dict["user_id"] = msg.user_id

            raw_messages.append(message_dict)

        return raw_messages

    def count_by_conversation(self, db: Session, *, conversation_id: int) -> int:
        """计算会话中的消息数量"""
        return db.query(Message).filter(Message.conversation_id == conversation_id).count()

    def get_new_messages(
        self, db: Session, *, conversation_id: int, last_message_id: Optional[int] = None
    ) -> List[Message]:
        """Get all new messages for a conversation after the last message id."""
        query = db.query(self.model).filter(self.model.conversation_id == conversation_id)
        if last_message_id:
            query = query.filter(self.model.id > last_message_id)
        return query.all()

    def get_latest_assistant_message_by_conversation_id(
        self, db: Session, *, conversation_id: int
    ) -> Optional[Message]:
        """获取指定会话ID的最新一条AI助手消息"""
        return (
            db.query(self.model)
            .filter(
                self.model.conversation_id == conversation_id,
                self.model.role == MessageRole.ASSISTANT
            )
            .order_by(self.model.created_at.desc())
            .first()
        )

    def get_recent_user_messages(
        self, db: Session, *, conversation_id: int, limit: int = 3
    ) -> List[Message]:
        """获取指定会话ID的最近几条用户消息，按时间倒序排序"""
        return (
            db.query(self.model)
            .filter(
                self.model.conversation_id == conversation_id,
                self.model.role == MessageRole.USER
            )
            .order_by(self.model.created_at.desc())
            .limit(limit)
            .all()
        )

    def get_recent_messages_with_conversations(
        self, db: Session, *, user_id: int, skip: int = 0, limit: int = 5
    ) -> Tuple[List[Message], List[Conversation]]:
        """
        获取用户所有会话中最近的消息，不区分session_id，按时间降序排序
        返回消息列表和对应的会话列表
        """
        # 1. 获取该用户的所有会话ID
        conversation_ids = (
            db.query(Conversation.id)
            .filter(Conversation.user_id == user_id)
            .subquery()
        )

        # 2. 获取这些会话中最近的消息
        messages = (
            db.query(self.model)
            .filter(self.model.conversation_id.in_(conversation_ids))
            .order_by(self.model.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

        # 3. 获取这些消息关联的会话
        if messages:
            conv_ids = {msg.conversation_id for msg in messages}
            conversations = (
                db.query(Conversation)
                .filter(Conversation.id.in_(conv_ids))
                .all()
            )
        else:
            conversations = []

        return messages, conversations

crud_message = CRUDMessage(Message)