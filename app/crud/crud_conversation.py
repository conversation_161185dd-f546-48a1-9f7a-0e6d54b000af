from typing import List, Optional, Dict, Any, Tuple
from fastapi.encoders import jsonable_encoder
from sqlalchemy.orm import Session
from app.crud.base import CRUDBase
from app.models.conversation import Conversation
from app.schemas.conversation import ConversationCreate, ConversationUpdate
import uuid

class CRUDConversation(CRUDBase[Conversation, ConversationCreate, ConversationUpdate]):
    def create_with_user(
        self, db: Session, *, obj_in: ConversationCreate, user_id: int
    ) -> Conversation:
        """创建新对话，指定用户ID"""
        obj_in_data = jsonable_encoder(obj_in)
        # 如果没有提供session_id，生成一个
        if "session_id" not in obj_in_data or not obj_in_data["session_id"]:
            obj_in_data["session_id"] = str(uuid.uuid4())

        # 确保user_id不会重复
        if "user_id" in obj_in_data:
            # 如果obj_in_data中已经有user_id，使用传入的参数覆盖它
            obj_in_data["user_id"] = user_id
            db_obj = Conversation(**obj_in_data)
        else:
            # 如果obj_in_data中没有user_id，添加它
            db_obj = Conversation(**obj_in_data, user_id=user_id)

        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_by_session_id(self, db: Session, *, session_id: str) -> Optional[Conversation]:
        """通过session_id获取会话"""
        return db.query(Conversation).filter(Conversation.session_id == session_id).first()

    def get_active_by_user(
        self, db: Session, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[Conversation]:
        """获取用户的活跃会话列表"""
        return (
            db.query(Conversation)
            .filter(Conversation.user_id == user_id, Conversation.is_active == True)
            .order_by(Conversation.last_active.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def update_last_active(self, db: Session, *, conversation_id: int) -> Conversation:
        """更新会话的最后活跃时间"""
        db_obj = db.query(Conversation).filter(Conversation.id == conversation_id).first()
        if db_obj:
            # last_active会自动更新，因为我们设置了onupdate=func.now()
            db.add(db_obj)
            db.commit()
            db.refresh(db_obj)
        return db_obj

    def count_by_user(self, db: Session, *, user_id: int) -> int:
        """计算用户的会话总数"""
        return db.query(Conversation).filter(Conversation.user_id == user_id).count()

    def create_with_session_id(self, db: Session, *, obj_in: ConversationCreate) -> Conversation:
        """使用指定的session_id创建新会话"""
        from fastapi.encoders import jsonable_encoder
        obj_in_data = jsonable_encoder(obj_in)

        # 确保user_id存在
        if "user_id" not in obj_in_data or obj_in_data["user_id"] is None:
            raise ValueError("创建会话时必须提供user_id")

        db_obj = Conversation(**obj_in_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_or_create_by_session_id(
        self, db: Session, *, session_id: str, user_id: int
    ) -> Tuple[Conversation, bool]:
        """
        获取或创建会话
        
        Args:
            db: 数据库会话
            session_id: 会话ID
            user_id: 用户ID
            
        Returns:
            元组(会话对象, 是否新创建)
        """
        # 检查是否有现有会话
        conversation = self.get_by_session_id(db, session_id=session_id)
        is_new = False
        
        # 如果会话不存在，创建新会话
        if not conversation:
            conversation = self.create_with_user(
                db,
                obj_in=ConversationCreate(
                    session_id=session_id,
                    is_active=True
                ),
                user_id=user_id
            )
            is_new = True
        else:
            # 更新会话的最后活跃时间
            try:
                self.update_last_active(db, conversation_id=conversation.id)
            except Exception:
                pass
                
        return conversation, is_new

crud_conversation = CRUDConversation(Conversation)