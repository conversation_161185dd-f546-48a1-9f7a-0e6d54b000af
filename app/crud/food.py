from typing import List, Dict, Any, Optional, Union

from sqlalchemy.orm import Session
from sqlalchemy import or_, func
import sqlalchemy.orm

from app.crud.base import CRUDBase
from app.models.food import Food, NutritionalProfile, FoodNutrientValue, FoodUnit
from app.schemas.food import FoodCreate, FoodUpdate
from app.services.food_search_service import FoodSearchService


class CRUDFood(CRUDBase[Food, FoodCreate, FoodUpdate]):
    def get(self, db: Session, id: int) -> Optional[Food]:
        return db.query(Food).filter(Food.id == id).first()
    
    def get_by_code(self, db: Session, *, code: str) -> Optional[Food]:
        return db.query(Food).filter(Food.code == code).first()
    
    def get_by_name(self, db: Session, *, name: str, limit: int = 1) -> List[Food]:
        return db.query(Food).filter(Food.name == name).limit(limit).all()
    
    def get_multi(
        self, db: Session, *, skip: int = 0, limit: int = 100, 
        name: Optional[str] = None, category: Optional[str] = None,
        food_type: Optional[str] = None, sort_by: Optional[str] = None,
        sort_desc: bool = False
    ) -> List[Food]:
        query = db.query(Food).options(
            sqlalchemy.orm.joinedload(Food.nutritional_profile)
        )
        
        # 应用过滤条件
        if name:
            query = query.filter(Food.name.like(f"%{name}%"))
        if category:
            query = query.filter(Food.category == category)
        if food_type:
            query = query.filter(Food.food_type == food_type)
        
        # 应用排序条件
        if sort_by:
            sort_column = getattr(Food, sort_by, None)
            if sort_column is not None:
                if sort_desc:
                    query = query.order_by(sort_column.desc())
                else:
                    query = query.order_by(sort_column)
        
        return query.offset(skip).limit(limit).all()
    
    def count(
        self, db: Session, *, name: Optional[str] = None, 
        category: Optional[str] = None, food_type: Optional[str] = None
    ) -> int:
        query = db.query(Food)
        
        # 应用过滤条件
        if name:
            query = query.filter(Food.name.like(f"%{name}%"))
        if category:
            query = query.filter(Food.category == category)
        if food_type:
            query = query.filter(Food.food_type == food_type)
        
        return query.count()
    
    def get_all_categories(self, db: Session) -> List[str]:
        return [
            cat for cat, in db.query(Food.category)
            .filter(Food.category.isnot(None))
            .distinct()
            .order_by(Food.category)
            .all()
        ]
    
    def get_all_types(self, db: Session) -> List[str]:
        return [
            type_ for type_, in db.query(Food.food_type)
            .filter(Food.food_type.isnot(None))
            .distinct()
            .order_by(Food.food_type)
            .all()
        ]
    
    def create(self, db: Session, *, obj_in: FoodCreate) -> Food:
        # 创建食品基本信息
        db_obj = Food(
            name=obj_in.name,
            code=obj_in.code,
            category=obj_in.category,
            food_type=obj_in.food_type,
            goods_id=obj_in.goods_id,
            thumb_image_url=obj_in.thumb_image_url,
            large_image_url=obj_in.large_image_url,
            is_liquid=obj_in.is_liquid,
            can_revise=obj_in.can_revise
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        
        # 创建营养概况
        if obj_in.nutritional_profile:
            profile_data = obj_in.nutritional_profile.dict()
            profile = NutritionalProfile(food_id=db_obj.id, **profile_data)
            db.add(profile)
        
        # 创建营养素明细
        if obj_in.nutrients:
            for nutrient in obj_in.nutrients:
                nutrient_data = nutrient.dict()
                db_nutrient = FoodNutrientValue(food_id=db_obj.id, **nutrient_data)
                db.add(db_nutrient)
        
        # 创建计量单位
        if obj_in.units:
            for unit in obj_in.units:
                unit_data = unit.dict()
                db_unit = FoodUnit(food_id=db_obj.id, **unit_data)
                db.add(db_unit)
        
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def update(
        self, db: Session, *, db_obj: Food, obj_in: Union[FoodUpdate, Dict[str, Any]]
    ) -> Food:
        # 更新基本信息
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.dict(exclude_unset=True)
        
        # 移除需要特殊处理的字段
        for field in ["nutritional_profile", "nutrients", "units"]:
            if field in update_data:
                del update_data[field]
        
        # 更新基本字段
        for field in update_data:
            setattr(db_obj, field, update_data[field])
        
        # 更新营养概况
        if isinstance(obj_in, dict):
            profile_data = obj_in.get("nutritional_profile")
        else:
            profile_data = obj_in.nutritional_profile
        
        if profile_data:
            # 检查是否存在营养概况
            profile = db.query(NutritionalProfile).filter(
                NutritionalProfile.food_id == db_obj.id
            ).first()
            
            profile_data_dict = profile_data.dict() if hasattr(profile_data, "dict") else profile_data
            
            if profile:
                # 更新现有记录
                for key, value in profile_data_dict.items():
                    setattr(profile, key, value)
            else:
                # 创建新记录
                profile = NutritionalProfile(food_id=db_obj.id, **profile_data_dict)
                db.add(profile)
        
        # 更新营养素明细 (先删除旧的，再添加新的)
        if isinstance(obj_in, dict):
            nutrients_data = obj_in.get("nutrients", [])
        else:
            nutrients_data = obj_in.nutrients if obj_in.nutrients else []
        
        if nutrients_data:
            # 删除旧记录
            db.query(FoodNutrientValue).filter(
                FoodNutrientValue.food_id == db_obj.id
            ).delete()
            
            # 添加新记录
            for nutrient in nutrients_data:
                nutrient_dict = nutrient.dict() if hasattr(nutrient, "dict") else nutrient
                db_nutrient = FoodNutrientValue(
                    food_id=db_obj.id,
                    **nutrient_dict
                )
                db.add(db_nutrient)
        
        # 更新计量单位 (先删除旧的，再添加新的)
        if isinstance(obj_in, dict):
            units_data = obj_in.get("units", [])
        else:
            units_data = obj_in.units if obj_in.units else []
        
        if units_data:
            # 删除旧记录
            db.query(FoodUnit).filter(
                FoodUnit.food_id == db_obj.id
            ).delete()
            
            # 添加新记录
            for unit in units_data:
                unit_dict = unit.dict() if hasattr(unit, "dict") else unit
                db_unit = FoodUnit(
                    food_id=db_obj.id,
                    **unit_dict
                )
                db.add(db_unit)
        
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def remove(self, db: Session, *, id: int) -> Food:
        obj = db.query(Food).get(id)
        db.delete(obj)
        db.commit()
        return obj
    
    def search_by_name(self, db: Session, *, name: str, limit: int = 10, sort_by_hot: bool = False) -> List[Food]:
        """
        基于名称搜索食物，返回最匹配的结果
        步骤：
        1. 初筛 - 获取最多10个候选项
        2. 精确重排 - 计算相似度并返回最匹配的食物
        3. 可选按热度排序 - 如果设置sort_by_hot为True
        4. 如果数据库中没有找到，尝试从外部API获取
        """
        # 第一阶段：初筛 - 获取候选项
        candidates = self._initial_food_search(db, name, limit)
        
        # 第二阶段：精确重排 - 返回最匹配的食物
        if candidates:
            sorted_candidates = self._rerank_food_candidates(candidates, name)
            # 确保加载营养数据和所有必要字段
            food_ids = [food.id for food in sorted_candidates]
            query = db.query(Food).options(
                sqlalchemy.orm.joinedload(Food.nutritional_profile)
            ).filter(Food.id.in_(food_ids))
            
            # 如果需要按热度排序
            if sort_by_hot:
                query = query.order_by(Food.hot.desc())
            
            results = query.all()
            
            # 确保布尔字段有正确的值
            for food in results:
                if food.is_liquid is None:
                    food.is_liquid = False
                if food.can_revise is None:
                    food.can_revise = False
            
            return results
        else:
            # 新增：如果数据库中没有找到，尝试从外部API获取
            try:
                # 创建食物搜索服务
                food_search_service = FoodSearchService()
                
                # 从外部API获取食物数据
                external_foods = food_search_service.fetch_and_process_foods(name, min(limit, 5))
                
                # 如果获取到数据，导入到数据库并返回
                if external_foods:
                    imported_foods = []
                    for food_data in external_foods:
                        # 检查食物是否已存在
                        existing_food = self.get_by_code(db, code=food_data["food"]["code"])
                        if existing_food:
                            imported_foods.append(existing_food)
                            continue
                        
                        # 创建食物记录
                        food_create = FoodCreate(
                            name=food_data["food"]["name"],
                            code=food_data["food"]["code"],
                            category=food_data["food"]["category"],
                            food_type=food_data["food"]["food_type"],
                            goods_id=food_data["food"]["goods_id"],
                            thumb_image_url=food_data["food"]["thumb_image_url"],
                            large_image_url=food_data["food"]["large_image_url"],
                            is_liquid=food_data["food"]["is_liquid"],
                            can_revise=food_data["food"]["can_revise"],
                            
                            # 添加营养概况
                            nutritional_profile=food_data.get("nutritional_profile"),
                            
                            # 添加营养素和计量单位
                            nutrients=food_data.get("nutrients", []),
                            units=food_data.get("units", [])
                        )
                        
                        # 导入到数据库
                        imported_food = self.create(db, obj_in=food_create)
                        imported_foods.append(imported_food)
                    
                    return imported_foods
            except Exception as e:
                import logging
                logging.error(f"从外部API获取食物数据失败: {str(e)}")
        
        return []
    
    def _initial_food_search(self, db: Session, name: str, limit: int = 10) -> List[Food]:
        """初步搜索候选食物项"""
        # 预处理名称
        name = name.lower().strip()
        
        # 关键词提取（简化版，实际项目可能需要更复杂的分词）
        keywords = name.split()
        
        # 设计查询方式，优先级依次降低
        query_priority = [
            # 1. 完全匹配（最高优先级）
            db.query(Food).filter(func.lower(Food.name) == name),
            
            # 2. 名称开头匹配
            db.query(Food).filter(func.lower(Food.name).like(f"{name}%")), 
            
            # 3. 名称包含整个查询词
            db.query(Food).filter(func.lower(Food.name).like(f"%{name}%")),
            
            # 4. 关键词匹配（过滤掉长度为1的关键词）
            db.query(Food).filter(
                or_(*[func.lower(Food.name).like(f"%{kw}%") 
                    for kw in keywords if len(kw) > 1])
            )
        ]
        
        # 逐层执行查询，直到获取足够的候选项
        candidates = []
        for query in query_priority:
            # 如果已有足够候选项，则停止查询
            if len(candidates) >= limit:
                break
            
            # 计算还需要的候选项数量
            remaining = limit - len(candidates)
            
            # 执行查询并添加不重复的结果
            results = query.limit(limit).all()
            for result in results:
                if result not in candidates:
                    candidates.append(result)
                    if len(candidates) >= limit:
                        break
        
        return candidates
    
    def _rerank_food_candidates(self, candidates: List[Food], query_name: str) -> List[Food]:
        """精确重排候选食物项，返回更精确的匹配结果"""
        # 计算各项相似度分数
        scored_candidates = []
        
        # 预处理查询名称
        query_name = query_name.lower()
        
        # 对每个候选项计算相似度分数
        for food in candidates:
            food_name = food.name.lower()
            
            # 计算多种相似度指标
            scores = {
                # 精确匹配
                'exact_match': 100 if food_name == query_name else 0,
                
                # 前缀匹配
                'prefix_match': 80 if food_name.startswith(query_name) else 0,
                
                # 包含关系
                'contains': 60 if query_name in food_name else 0,
                
                # 编辑距离（简化版）
                'edit_distance': self._levenshtein_distance(food_name, query_name),
                
                # 字符串长度接近程度（0-10分）
                'length_ratio': 10 * min(len(food_name), len(query_name)) / max(len(food_name), len(query_name))
                if max(len(food_name), len(query_name)) > 0 else 0
            }
            
            # 计算编辑距离相似度（归一化为0-50分）
            max_len = max(len(food_name), len(query_name))
            normalized_distance = 50 * (1 - scores['edit_distance'] / max_len) if max_len > 0 else 0
            
            # 计算加权总分
            total_score = (
                scores['exact_match'] * 2.0 +     # 精确匹配权重最高
                scores['prefix_match'] * 1.5 +    # 前缀匹配次之
                scores['contains'] * 1.0 +        # 包含关系再次之
                normalized_distance * 0.8 +       # 编辑距离相似度
                scores['length_ratio'] * 0.2      # 长度相似性权重最低
            )
            
            # 如果有营养信息，给予额外加分
            if hasattr(food, 'nutritional_profile') and food.nutritional_profile:
                total_score += 20  # 有营养信息的食物优先
            
            # 存储得分和食物对象
            scored_candidates.append((food, total_score))
        
        # 按分数降序排序
        sorted_results = [item[0] for item in sorted(scored_candidates, key=lambda x: x[1], reverse=True)]
        
        return sorted_results
    
    def _levenshtein_distance(self, s1: str, s2: str) -> int:
        """
        计算两个字符串的Levenshtein距离（编辑距离）
        """
        if len(s1) < len(s2):
            return self._levenshtein_distance(s2, s1)
        
        if len(s2) == 0:
            return len(s1)
        
        previous_row = range(len(s2) + 1)
        for i, c1 in enumerate(s1):
            current_row = [i + 1]
            for j, c2 in enumerate(s2):
                insertions = previous_row[j + 1] + 1
                deletions = current_row[j] + 1
                substitutions = previous_row[j] + (c1 != c2)
                current_row.append(min(insertions, deletions, substitutions))
            previous_row = current_row
        
        return previous_row[-1]
        
    def get_specific_nutrients(self, db: Session, *, food_id: int, nutrient_names: List[str]) -> List[FoodNutrientValue]:
        """
        获取特定食物的特定营养素数据
        
        Args:
            db: 数据库会话
            food_id: 食物ID
            nutrient_names: 营养素中文名称列表，如 ["膳食纤维", "脂肪酸", "叶酸"]
            
        Returns:
            符合条件的营养素数据列表
        """
        query = db.query(FoodNutrientValue).filter(
            FoodNutrientValue.food_id == food_id,
            FoodNutrientValue.name_cn.in_(nutrient_names)
        )
        
        return query.all() 