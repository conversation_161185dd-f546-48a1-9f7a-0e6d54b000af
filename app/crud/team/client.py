from sqlalchemy.orm import Session
from sqlalchemy.future import select
from sqlalchemy import func, desc
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from app.models.team import ClientRelation, ClientTransferHistory, ClientStatus, TrainingSession, TeamMembership, TeamRole, ClientTrainingPlan
from app.models.user import User
from app.schemas.team.client import ClientAssignment, ClientTransfer

async def assign_client(db: Session, team_id: int, client_data: ClientAssignment) -> ClientRelation:
    """分配会员给教练"""
    client_relation = ClientRelation(
        team_id=team_id,
        client_id=client_data.client_id,
        coach_id=client_data.coach_id,
        status=client_data.status
    )
    db.add(client_relation)
    await db.commit()
    await db.refresh(client_relation)
    return client_relation

async def get_client_relation(db: Session, client_relation_id: int) -> Optional[ClientRelation]:
    """获取会员关系"""
    result = await db.execute(select(ClientRelation).filter(ClientRelation.id == client_relation_id))
    return result.scalars().first()

async def transfer_client(db: Session, client_relation_id: int, transfer_data: ClientTransfer) -> Optional[Dict[str, Any]]:
    """转移会员到新教练"""
    client_relation = await get_client_relation(db, client_relation_id)
    if not client_relation:
        return None

    # 记录转移历史
    transfer_history = ClientTransferHistory(
        client_relation_id=client_relation.id,
        from_coach_id=client_relation.coach_id,
        to_coach_id=transfer_data.new_coach_id,
        reason=transfer_data.reason,
        transferred_at=datetime.now()
    )
    db.add(transfer_history)

    # 更新会员关系
    old_coach_id = client_relation.coach_id
    client_relation.coach_id = transfer_data.new_coach_id
    client_relation.status = ClientStatus.TRANSFERRED
    client_relation.updated_at = datetime.now()

    db.add(client_relation)
    await db.commit()
    await db.refresh(transfer_history)

    # 获取教练信息
    from_coach_query = select(User).filter(User.id == old_coach_id)
    to_coach_query = select(User).filter(User.id == transfer_data.new_coach_id)

    from_coach_result = await db.execute(from_coach_query)
    to_coach_result = await db.execute(to_coach_query)

    from_coach = from_coach_result.scalars().first()
    to_coach = to_coach_result.scalars().first()

    return {
        "id": transfer_history.id,
        "client_relation_id": client_relation.id,
        "from_coach_id": old_coach_id,
        "to_coach_id": transfer_data.new_coach_id,
        "reason": transfer_data.reason,
        "transferred_at": transfer_history.transferred_at,
        "from_coach_name": from_coach.nickname if from_coach else "Unknown",
        "to_coach_name": to_coach.nickname if to_coach else "Unknown"
    }

async def get_team_clients(db: Session, team_id: int, status: Optional[str] = None, coach_id: Optional[int] = None) -> List[Dict[str, Any]]:
    """获取团队会员列表"""
    query = select(
        ClientRelation,
        User.nickname.label("client_name"),
        User.avatar_url.label("client_avatar"),
        func.count(ClientTrainingPlan.id).filter(
            ClientTrainingPlan.status.in_(['scheduled', 'in_progress'])
        ).label("active_plan_count"),
        func.max(TrainingSession.actual_end).label("last_training_date")
    ).join(
        User, ClientRelation.client_id == User.id
    ).outerjoin(
        ClientTrainingPlan, ClientRelation.id == ClientTrainingPlan.client_relation_id
    ).outerjoin(
        TrainingSession, ClientTrainingPlan.id == TrainingSession.client_plan_id
    ).filter(
        ClientRelation.team_id == team_id
    ).group_by(
        ClientRelation.id, User.nickname, User.avatar_url
    )

    if status:
        try:
            status_enum = ClientStatus[status.upper()]
            query = query.filter(ClientRelation.status == status_enum)
        except (KeyError, AttributeError):
            pass

    if coach_id:
        query = query.filter(ClientRelation.coach_id == coach_id)

    result = await db.execute(query)
    clients = []

    for client_relation, client_name, client_avatar, active_plan_count, last_training_date in result:
        # 获取教练信息
        coach_query = select(User.nickname).filter(User.id == client_relation.coach_id)
        coach_result = await db.execute(coach_query)
        coach_name = coach_result.scalar_one_or_none() or "Unknown"

        client_dict = {
            "id": client_relation.id,
            "client_id": client_relation.client_id,
            "coach_id": client_relation.coach_id,
            "status": client_relation.status,
            "client_name": client_name,
            "client_avatar": client_avatar,
            "coach_name": coach_name,
            "active_plan_count": active_plan_count or 0,
            "last_training_date": last_training_date
        }
        clients.append(client_dict)

    return clients

async def get_client_with_stats(db: Session, client_relation_id: int, current_user_id: int) -> Optional[Dict[str, Any]]:
    """获取会员详细信息及统计数据"""
    query = select(
        ClientRelation,
        User.nickname.label("client_name"),
        User.avatar_url.label("client_avatar")
    ).join(
        User, ClientRelation.client_id == User.id
    ).filter(
        ClientRelation.id == client_relation_id
    )

    result = await db.execute(query)
    client_data = result.first()

    if not client_data:
        return None

    client_relation, client_name, client_avatar = client_data

    # 检查权限（当前用户必须是教练或团队管理员）
    if client_relation.coach_id != current_user_id:
        # 检查是否是团队管理员
        team_role_query = select(TeamMembership).filter(
            TeamMembership.team_id == client_relation.team_id,
            TeamMembership.user_id == current_user_id,
            TeamMembership.role.in_([TeamRole.OWNER, TeamRole.ADMIN])
        )
        team_role_result = await db.execute(team_role_query)
        if not team_role_result.scalars().first():
            return None

    # 获取教练信息
    coach_query = select(User).filter(User.id == client_relation.coach_id)
    coach_result = await db.execute(coach_query)
    coach = coach_result.scalars().first()

    # 获取转移历史
    transfer_query = select(
        ClientTransferHistory,
        User.nickname.label("from_coach_name")
    ).join(
        User, ClientTransferHistory.from_coach_id == User.id
    ).filter(
        ClientTransferHistory.client_relation_id == client_relation_id
    ).order_by(
        desc(ClientTransferHistory.transferred_at)
    )

    transfer_result = await db.execute(transfer_query)
    transfer_history = []

    for history, from_coach_name in transfer_result:
        # 获取目标教练信息
        to_coach_query = select(User.nickname).filter(User.id == history.to_coach_id)
        to_coach_result = await db.execute(to_coach_query)
        to_coach_name = to_coach_result.scalar_one_or_none() or "Unknown"

        history_dict = {
            "id": history.id,
            "from_coach_id": history.from_coach_id,
            "to_coach_id": history.to_coach_id,
            "reason": history.reason,
            "transferred_at": history.transferred_at,
            "from_coach_name": from_coach_name,
            "to_coach_name": to_coach_name
        }
        transfer_history.append(history_dict)

    # 计算统计数据
    stats_query = select(
        func.count(ClientTrainingPlan.id).label("total_plans"),
        func.count(ClientTrainingPlan.id).filter(
            ClientTrainingPlan.status == 'completed'
        ).label("completed_plans"),
        func.avg(ClientTrainingPlan.completion_rate).label("avg_completion_rate"),
        func.count(TrainingSession.id).label("total_sessions"),
        func.count(TrainingSession.id).filter(
            TrainingSession.status == 'completed'
        ).label("completed_sessions")
    ).outerjoin(
        ClientTrainingPlan, ClientRelation.id == ClientTrainingPlan.client_relation_id
    ).outerjoin(
        TrainingSession, ClientTrainingPlan.id == TrainingSession.client_plan_id
    ).filter(
        ClientRelation.id == client_relation_id
    )

    stats_result = await db.execute(stats_query)
    stats_data = stats_result.first()

    total_plans, completed_plans, avg_completion_rate, total_sessions, completed_sessions = stats_data

    # 计算最近30天活跃情况
    thirty_days_ago = datetime.now() - timedelta(days=30)
    active_query = select(
        func.count(TrainingSession.id).label("active_sessions")
    ).join(
        ClientTrainingPlan, TrainingSession.client_plan_id == ClientTrainingPlan.id
    ).filter(
        ClientTrainingPlan.client_relation_id == client_relation_id,
        TrainingSession.actual_start >= thirty_days_ago
    )

    active_result = await db.execute(active_query)
    active_sessions = active_result.scalar_one_or_none() or 0

    return {
        "id": client_relation.id,
        "client_id": client_relation.client_id,
        "coach_id": client_relation.coach_id,
        "status": client_relation.status,
        "created_at": client_relation.created_at,
        "client_name": client_name,
        "client_avatar": client_avatar,
        "coach_name": coach.nickname if coach else "Unknown",
        "coach_avatar": coach.avatar_url if coach else None,
        "transfer_history": transfer_history,
        "stats": {
            "total_plans": total_plans or 0,
            "completed_plans": completed_plans or 0,
            "avg_completion_rate": float(avg_completion_rate or 0),
            "total_sessions": total_sessions or 0,
            "completed_sessions": completed_sessions or 0,
            "active_sessions_30d": active_sessions,
            "completion_rate": float(completed_sessions / total_sessions if total_sessions else 0)
        }
    }
