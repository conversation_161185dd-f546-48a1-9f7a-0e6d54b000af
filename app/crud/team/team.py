from sqlalchemy.orm import Session
from sqlalchemy.future import select
from sqlalchemy import func
from typing import List, Optional, Dict, Any
from app.models.team import Team, TeamMembership, TeamStats, TeamRole, TeamStatus, MembershipStatus, ClientRelation
from app.models.user import User
from app.schemas.team.team import TeamCreate, TeamUpdate

async def create_team(db: Session, owner: User, team_data: TeamCreate) -> Team:
    """创建新团队"""
    async with db.begin():
        # 创建团队
        team = Team(
            name=team_data.name,
            description=team_data.description,
            owner_id=owner.id,
            status=TeamStatus.ACTIVE,
            settings=team_data.settings or {}
        )
        db.add(team)
        await db.flush()

        # 创建所有者成员关系
        membership = TeamMembership(
            team_id=team.id,
            user_id=owner.id,
            role=TeamRole.OWNER,
            status=MembershipStatus.ACTIVE
        )
        db.add(membership)

        # 初始化团队统计
        stats = TeamStats(team_id=team.id)
        db.add(stats)

    return team

async def get_team_by_id(db: Session, team_id: int) -> Optional[Team]:
    """根据ID获取团队"""
    result = await db.execute(select(Team).filter(Team.id == team_id))
    return result.scalars().first()

async def update_team(db: Session, team_id: int, team_data: TeamUpdate) -> Optional[Team]:
    """更新团队信息"""
    team = await get_team_by_id(db, team_id)
    if not team:
        return None

    update_data = team_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(team, key, value)

    db.add(team)
    await db.commit()
    await db.refresh(team)
    return team

async def get_user_teams(db: Session, user_id: int) -> List[Dict[str, Any]]:
    """获取用户相关的团队列表"""
    # 查询用户所在的团队及其角色
    query = select(
        Team,
        TeamMembership.role,
        func.count(TeamMembership.id).label("member_count"),
        func.count(ClientRelation.id).label("client_count")
    ).join(
        TeamMembership, Team.id == TeamMembership.team_id
    ).outerjoin(
        ClientRelation, Team.id == ClientRelation.team_id
    ).filter(
        TeamMembership.user_id == user_id,
        TeamMembership.status == MembershipStatus.ACTIVE
    ).group_by(
        Team.id, TeamMembership.role
    )

    result = await db.execute(query)
    teams = []

    for team, role, member_count, client_count in result:
        team_dict = {
            "id": team.id,
            "name": team.name,
            "description": team.description,
            "owner_id": team.owner_id,
            "status": team.status,
            "created_at": team.created_at,
            "updated_at": team.updated_at,
            "role": role.name,
            "member_count": member_count,
            "client_count": client_count
        }
        teams.append(team_dict)

    return teams

async def get_team_with_stats(db: Session, team_id: int, user_id: int) -> Optional[Dict[str, Any]]:
    """获取团队详细信息及统计数据"""
    team = await get_team_by_id(db, team_id)
    if not team:
        return None

    # 获取用户在团队中的角色
    membership_query = select(TeamMembership).filter(
        TeamMembership.team_id == team_id,
        TeamMembership.user_id == user_id
    )
    membership_result = await db.execute(membership_query)
    membership = membership_result.scalars().first()

    if not membership:
        return None

    # 获取团队统计数据
    stats_query = select(TeamStats).filter(TeamStats.team_id == team_id)
    stats_result = await db.execute(stats_query)
    stats = stats_result.scalars().first()

    team_dict = {
        "id": team.id,
        "name": team.name,
        "description": team.description,
        "owner_id": team.owner_id,
        "status": team.status,
        "created_at": team.created_at,
        "updated_at": team.updated_at,
        "settings": team.settings,
        "role": membership.role.name,
        "stats": {
            "total_members": stats.total_members if stats else 0,
            "total_clients": stats.total_clients if stats else 0,
            "active_clients_30d": stats.active_clients_30d if stats else 0,
            "total_sessions": stats.total_sessions if stats else 0,
            "completed_sessions": stats.completed_sessions if stats else 0,
            "completion_rate": stats.completion_rate if stats else 0,
            "growth_rate": stats.growth_rate if stats else 0
        }
    }

    return team_dict
