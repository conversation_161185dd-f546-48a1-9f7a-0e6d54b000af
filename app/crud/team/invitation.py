from sqlalchemy.orm import Session
from sqlalchemy.future import select
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from app.models.team import TeamInvitation, InvitationStatus, TeamRole, Team
from app.models.user import User
from app.schemas.team.invitation import InvitationCreate

async def create_invitation(db: Session, team_id: int, inviter_id: int, invitation_data: InvitationCreate) -> TeamInvitation:
    """创建团队邀请"""
    # 设置默认过期时间为7天后
    expired_at = invitation_data.expired_at or (datetime.now() + timedelta(days=7))

    invitation = TeamInvitation(
        team_id=team_id,
        inviter_id=inviter_id,
        invitee_id=invitation_data.invitee_id,
        role=invitation_data.role,
        status=InvitationStatus.PENDING,
        expired_at=expired_at
    )
    db.add(invitation)
    await db.commit()
    await db.refresh(invitation)
    return invitation

async def get_invitation(db: Session, invitation_id: int) -> Optional[TeamInvitation]:
    """获取邀请详情"""
    result = await db.execute(select(TeamInvitation).filter(TeamInvitation.id == invitation_id))
    return result.scalars().first()

async def update_invitation(db: Session, invitation_id: int, status: InvitationStatus) -> Optional[TeamInvitation]:
    """更新邀请状态"""
    invitation = await get_invitation(db, invitation_id)
    if not invitation:
        return None

    invitation.status = status
    db.add(invitation)
    await db.commit()
    await db.refresh(invitation)
    return invitation

async def get_team_invitations(db: Session, team_id: int, status: Optional[InvitationStatus] = None) -> List[Dict[str, Any]]:
    """获取团队发出的邀请列表"""
    query = select(
        TeamInvitation,
        User.nickname.label("invitee_name")
    ).join(
        User, TeamInvitation.invitee_id == User.id
    ).filter(
        TeamInvitation.team_id == team_id
    )

    if status:
        query = query.filter(TeamInvitation.status == status)

    result = await db.execute(query)
    invitations = []

    for invitation, invitee_name in result:
        # 获取邀请人信息
        inviter_query = select(User.nickname).filter(User.id == invitation.inviter_id)
        inviter_result = await db.execute(inviter_query)
        inviter_name = inviter_result.scalar_one_or_none() or "Unknown"

        # 获取团队信息
        team_query = select(Team.name).filter(Team.id == invitation.team_id)
        team_result = await db.execute(team_query)
        team_name = team_result.scalar_one_or_none() or "Unknown Team"

        invitation_dict = {
            "id": invitation.id,
            "team_id": invitation.team_id,
            "inviter_id": invitation.inviter_id,
            "invitee_id": invitation.invitee_id,
            "role": invitation.role,
            "status": invitation.status,
            "created_at": invitation.created_at,
            "expired_at": invitation.expired_at,
            "team_name": team_name,
            "inviter_name": inviter_name,
            "invitee_name": invitee_name
        }
        invitations.append(invitation_dict)

    return invitations

async def get_user_invitations(db: Session, user_id: int, status: Optional[InvitationStatus] = None) -> List[Dict[str, Any]]:
    """获取用户收到的邀请列表"""
    query = select(
        TeamInvitation,
        User.nickname.label("inviter_name")
    ).join(
        User, TeamInvitation.inviter_id == User.id
    ).filter(
        TeamInvitation.invitee_id == user_id
    )

    if status:
        query = query.filter(TeamInvitation.status == status)

    result = await db.execute(query)
    invitations = []

    for invitation, inviter_name in result:
        # 获取团队信息
        team_query = select(Team.name).filter(Team.id == invitation.team_id)
        team_result = await db.execute(team_query)
        team_name = team_result.scalar_one_or_none() or "Unknown Team"

        invitation_dict = {
            "id": invitation.id,
            "team_id": invitation.team_id,
            "inviter_id": invitation.inviter_id,
            "invitee_id": invitation.invitee_id,
            "role": invitation.role,
            "status": invitation.status,
            "created_at": invitation.created_at,
            "expired_at": invitation.expired_at,
            "team_name": team_name,
            "inviter_name": inviter_name
        }
        invitations.append(invitation_dict)

    return invitations
