from sqlalchemy.orm import Session
from sqlalchemy.future import select
from typing import List, Optional, Dict, Any
from app.models.team import TeamMembership, TeamRole, MembershipStatus
from app.models.user import User
from app.schemas.team.membership import MembershipCreate, MembershipUpdate

async def create_membership(db: Session, team_id: int, membership_data: MembershipCreate) -> TeamMembership:
    """创建团队成员关系"""
    membership = TeamMembership(
        team_id=team_id,
        user_id=membership_data.user_id,
        role=membership_data.role,
        status=membership_data.status
    )
    db.add(membership)
    await db.commit()
    await db.refresh(membership)
    return membership

async def get_team_membership(db: Session, team_id: int, user_id: int) -> Optional[TeamMembership]:
    """获取用户在团队中的成员关系"""
    result = await db.execute(
        select(TeamMembership).filter(
            TeamMembership.team_id == team_id,
            TeamMembership.user_id == user_id
        )
    )
    return result.scalars().first()

async def update_membership(db: Session, team_id: int, user_id: int, membership_data: MembershipUpdate) -> Optional[TeamMembership]:
    """更新团队成员关系"""
    membership = await get_team_membership(db, team_id, user_id)
    if not membership:
        return None
    
    update_data = membership_data.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(membership, key, value)
    
    db.add(membership)
    await db.commit()
    await db.refresh(membership)
    return membership

async def delete_membership(db: Session, team_id: int, user_id: int) -> bool:
    """删除团队成员关系"""
    membership = await get_team_membership(db, team_id, user_id)
    if not membership:
        return False
    
    await db.delete(membership)
    await db.commit()
    return True

async def get_team_members(db: Session, team_id: int, role: Optional[str] = None) -> List[Dict[str, Any]]:
    """获取团队成员列表"""
    query = select(
        TeamMembership, User
    ).join(
        User, TeamMembership.user_id == User.id
    ).filter(
        TeamMembership.team_id == team_id
    )
    
    if role:
        try:
            role_enum = TeamRole[role.upper()]
            query = query.filter(TeamMembership.role == role_enum)
        except (KeyError, AttributeError):
            pass
    
    result = await db.execute(query)
    members = []
    
    for membership, user in result:
        member_dict = {
            "id": membership.id,
            "user_id": user.id,
            "role": membership.role,
            "status": membership.status,
            "joined_at": membership.joined_at,
            "nickname": user.nickname,
            "avatar_url": user.avatar_url
        }
        members.append(member_dict)
    
    return members
