from typing import Any, Dict, Optional, Union

from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.meal import HealthRecommendation
from app.schemas.meal import HealthRecommendationCreate, HealthRecommendationUpdate


class CRUDHealthRecommendation(CRUDBase[HealthRecommendation, HealthRecommendationCreate, HealthRecommendationUpdate]):
    """
    健康建议CRUD操作
    """
    
    def create_with_meal_id(
        self, db: Session, *, obj_in: Union[HealthRecommendationCreate, Dict[str, Any]], meal_id: int
    ) -> HealthRecommendation:
        """
        创建健康建议并关联到餐食记录
        """
        obj_in_data = obj_in.dict() if isinstance(obj_in, HealthRecommendationCreate) else obj_in
        db_obj = self.model(**obj_in_data, meal_record_id=meal_id)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def get_multi_by_meal(
        self, db: Session, *, meal_id: int, skip: int = 0, limit: int = 100
    ) -> list[HealthRecommendation]:
        """
        获取餐食的所有健康建议
        """
        return (
            db.query(self.model)
            .filter(self.model.meal_record_id == meal_id)
            .offset(skip)
            .limit(limit)
            .all()
        ) 