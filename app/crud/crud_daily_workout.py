from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import func

from app.crud.base import CRUDBase
from app.models.daily_workout import DailyWorkout
from app.models.workout_exercise import WorkoutExercise
from app.schemas.community import DailyWorkoutCreate, DailyWorkoutUpdate


class CRUDDailyWorkout(CRUDBase[DailyWorkout, DailyWorkoutCreate, DailyWorkoutUpdate]):
    """单日训练的CRUD操作"""

    def create_with_exercises(
        self, db: Session, *, obj_in: DailyWorkoutCreate
    ) -> DailyWorkout:
        """创建单日训练及其训练动作
        
        Args:
            db: 数据库会话
            obj_in: 单日训练数据
            
        Returns:
            创建的单日训练
        """
        # 创建单日训练
        db_obj = DailyWorkout(
            name=obj_in.name,
            description=obj_in.description,
            training_duration=obj_in.training_duration,
            total_capacity=obj_in.total_capacity
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        
        # 创建训练动作
        if obj_in.exercises:
            for exercise_data in obj_in.exercises:
                exercise = WorkoutExercise(
                    daily_workout_id=db_obj.id,
                    exercise_id=exercise_data.exercise_id,
                    sets=exercise_data.sets,
                    reps=exercise_data.reps,
                    rest_seconds=exercise_data.rest_seconds,
                    order=exercise_data.order,
                    notes=exercise_data.notes,
                    exercise_type=exercise_data.exercise_type,
                    superset_group=exercise_data.superset_group,
                    weight=exercise_data.weight
                )
                db.add(exercise)
            
            db.commit()
            db.refresh(db_obj)
        
        return db_obj
    
    def get_with_exercises(self, db: Session, *, id: int) -> Optional[DailyWorkout]:
        """获取单日训练及其训练动作
        
        Args:
            db: 数据库会话
            id: 单日训练ID
            
        Returns:
            单日训练及其训练动作
        """
        return db.query(DailyWorkout).filter(DailyWorkout.id == id).first()
    
    def get_multi_with_exercises(
        self, db: Session, *, skip: int = 0, limit: int = 100
    ) -> List[DailyWorkout]:
        """获取多个单日训练及其训练动作
        
        Args:
            db: 数据库会话
            skip: 跳过的记录数
            limit: 返回的记录数
            
        Returns:
            单日训练列表
        """
        return db.query(DailyWorkout).offset(skip).limit(limit).all()


daily_workout = CRUDDailyWorkout(DailyWorkout)
