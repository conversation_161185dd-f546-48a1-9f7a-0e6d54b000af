from typing import Optional

from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.user_setting import UserSetting
from app.schemas.setting import UserSettingCreate, UserSettingUpdate


class CRUDUserSetting(CRUDBase[UserSetting, UserSettingCreate, UserSettingUpdate]):
    def get_by_user_id(self, db: Session, *, user_id: int) -> Optional[UserSetting]:
        """通过用户ID获取设置"""
        return db.query(UserSetting).filter(UserSetting.user_id == user_id).first()

    def create_with_user(
        self, db: Session, *, user_id: int, notification_enabled: bool = True
    ) -> UserSetting:
        """创建用户设置"""
        db_obj = UserSetting(
            user_id=user_id,
            notification_enabled=notification_enabled
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj


user_setting = CRUDUserSetting(UserSetting) 