from typing import Any, Dict, Optional, Union

from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.meal import FoodItemNutrientIntake
from app.schemas.meal import FoodItemNutrientIntakeCreate, FoodItemNutrientIntakeUpdate


class CRUDFoodItemNutrientIntake(CRUDBase[FoodItemNutrientIntake, FoodItemNutrientIntakeCreate, FoodItemNutrientIntakeUpdate]):
    """
    食物营养素摄入CRUD操作
    """
    
    def create_with_food_item_id(
        self, db: Session, *, obj_in: FoodItemNutrientIntakeCreate, food_item_id: int
    ) -> FoodItemNutrientIntake:
        """
        创建食物营养素摄入并关联到食物项
        """
        obj_in_data = obj_in.dict() if isinstance(obj_in, FoodItemNutrientIntakeCreate) else obj_in
        db_obj = self.model(**obj_in_data, food_item_id=food_item_id)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def get_multi_by_food_item(
        self, db: Session, *, food_item_id: int, skip: int = 0, limit: int = 100
    ) -> list[FoodItemNutrientIntake]:
        """
        获取食物项的所有营养素摄入记录
        """
        return (
            db.query(self.model)
            .filter(self.model.food_item_id == food_item_id)
            .offset(skip)
            .limit(limit)
            .all()
        ) 