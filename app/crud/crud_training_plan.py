from typing import List, Dict, Optional, Union, Any
from sqlalchemy.orm import Session
from sqlalchemy import desc
import logging

from app.crud.base import CRUDBase
from app.models.training_plan import TrainingPlan
from app.models.workout import Workout, WorkoutStatus
from app.models.workout_exercise import WorkoutExercise
from app.schemas.training_plan import TrainingPlanCreate, TrainingPlanUpdate


class CRUDTrainingPlan(CRUDBase[TrainingPlan, TrainingPlanCreate, TrainingPlanUpdate]):
    """训练计划的CRUD操作"""

    logger = logging.getLogger(__name__)

    def get_user_plans(
        self, db: Session, *, user_id: int, skip: int = 0, limit: int = 100, active_only: bool = True
    ) -> List[TrainingPlan]:
        """获取用户的所有训练计划

        Args:
            db: 数据库会话
            user_id: 用户ID
            skip: 跳过记录数
            limit: 最大返回记录数
            active_only: 是否只返回激活状态的计划

        Returns:
            训练计划列表
        """
        query = db.query(self.model).filter(TrainingPlan.user_id == user_id)

        if active_only:
            query = query.filter(TrainingPlan.is_active == True)

        return query.order_by(desc(TrainingPlan.created_at)).offset(skip).limit(limit).all()

    def get_with_workouts(self, db: Session, *, id: int) -> Optional[Dict[str, Any]]:
        """获取训练计划及其所有训练日和训练动作

        Args:
            db: 数据库会话
            id: 训练计划ID

        Returns:
            包含所有训练日和训练动作的训练计划数据
        """
        self.logger.info(f"获取训练计划详情: id={id}")

        # 查询训练计划
        plan = db.query(TrainingPlan).filter(TrainingPlan.id == id).first()
        if not plan:
            self.logger.warning(f"未找到ID为 {id} 的训练计划")
            return None

        self.logger.info(f"找到训练计划: {plan.plan_name}")

        # 将ORM对象转换为字典
        plan_dict = {
            "id": plan.id,
            "user_id": plan.user_id,
            "plan_name": plan.plan_name,
            "description": plan.description,
            "fitness_goal": plan.fitness_goal,
            "experience_level": plan.experience_level,
            "duration_weeks": plan.duration_weeks,
            "start_date": plan.start_date,
            "end_date": plan.end_date,
            "created_at": plan.created_at,
            "updated_at": plan.updated_at,
            "is_active": plan.is_active,
            "is_template": plan.is_template,
            "privacy_setting": plan.privacy_setting,
            "status": plan.status,
            "workouts": []
        }

        # 查询训练日
        workouts = db.query(Workout).filter(Workout.training_plan_id == id)\
            .order_by(Workout.day_number).all()

        for workout in workouts:
            workout_dict = {
                "id": workout.id,
                "name": workout.name,
                "day_number": workout.day_number,
                "day_of_week": workout.day_of_week,
                "description": workout.description,
                "estimated_duration": workout.estimated_duration,
                "scheduled_date": workout.scheduled_date,
                "status": workout.status,
                "created_at": workout.created_at,
                "updated_at": workout.updated_at,
                "training_plan_id": workout.training_plan_id,
                "exercises": []
            }

            # 查询训练动作
            exercises = db.query(WorkoutExercise).filter(WorkoutExercise.workout_id == workout.id)\
                .order_by(WorkoutExercise.order).all()

            # 获取所有训练动作的ID列表，过滤掉非整数ID
            numeric_exercise_ids = []
            for ex in exercises:
                try:
                    # 尝试将ID转换为整数
                    if isinstance(ex.exercise_id, int) or (isinstance(ex.exercise_id, str) and ex.exercise_id.isdigit()):
                        numeric_exercise_ids.append(int(ex.exercise_id))
                    else:
                        self.logger.info(f"跳过非整数ID: {ex.exercise_id}, 类型: {type(ex.exercise_id)}")
                except (ValueError, TypeError) as e:
                    # 如果ID不是整数，则跳过
                    self.logger.warning(f"处理exercise_id时出错: {ex.exercise_id}, 错误: {str(e)}")

            # 批量查询所有训练动作的详细信息
            from app.models.exercise import Exercise
            exercise_details = {}
            if numeric_exercise_ids:
                details = db.query(Exercise).filter(Exercise.id.in_(numeric_exercise_ids)).all()
                exercise_details = {d.id: d for d in details}

            for exercise in exercises:
                # 获取训练动作的详细信息
                detail = None
                exercise_id = exercise.exercise_id

                # 处理不同类型的exercise_id
                if isinstance(exercise_id, int) or (isinstance(exercise_id, str) and exercise_id.isdigit()):
                    # 如果是整数ID，从预先查询的详情中获取
                    numeric_id = int(exercise_id)
                    detail = exercise_details.get(numeric_id)
                    if not detail:
                        self.logger.warning(f"未找到ID为 {numeric_id} 的动作详情")
                else:
                    self.logger.info(f"处理自定义动作ID: {exercise_id}")

                # 为自定义动作ID生成名称
                exercise_name = "自定义动作"
                if detail:
                    exercise_name = detail.name
                    self.logger.info(f"使用动作详情名称: {exercise_name}")
                elif isinstance(exercise_id, str) and exercise_id.startswith('added_exercise'):
                    # 对于自定义动作，提供更友好的名称
                    exercise_name = f"自定义动作 {exercise_id.split('_')[-1]}"
                    self.logger.info(f"生成自定义动作名称: {exercise_name}")
                else:
                    exercise_name = f"动作ID: {exercise_id}"
                    self.logger.info(f"使用默认动作名称: {exercise_name}")

                # 获取该训练动作的组记录
                from app.crud.crud_set_record import set_record as crud_set_record
                set_records = crud_set_record.get_by_workout_exercise(db, workout_exercise_id=exercise.id)

                # 将组记录转换为字典列表
                set_records_list = []
                for record in set_records:
                    set_records_list.append({
                        "id": record.id,
                        "workout_exercise_id": record.workout_exercise_id,
                        "set_number": record.set_number,
                        "set_type": record.set_type,
                        "weight": record.weight,
                        "reps": record.reps,
                        "completed": record.completed,
                        "notes": record.notes,
                        "created_at": record.created_at,
                        "updated_at": record.updated_at
                    })

                exercise_dict = {
                    "id": exercise.id,
                    "exercise_id": exercise_id,
                    "exercise_name": exercise_name,
                    "exercise_image": detail.image_name if detail else None,
                    "exercise_description": detail.description if detail else None,
                    "sets": exercise.sets,
                    "reps": exercise.reps,
                    "rest_seconds": exercise.rest_seconds,
                    "order": exercise.order,
                    "notes": exercise.notes,
                    "exercise_type": exercise.exercise_type,
                    "superset_group": exercise.superset_group,
                    "weight": exercise.weight,
                    "workout_id": exercise.workout_id,
                    "set_records": set_records_list  # 添加组记录信息
                }
                workout_dict["exercises"].append(exercise_dict)

            plan_dict["workouts"].append(workout_dict)

        return plan_dict

    def update_plan_status(
        self, db: Session, *, plan_id: int, status: str, is_active: Optional[bool] = None
    ) -> TrainingPlan:
        """更新训练计划状态

        Args:
            db: 数据库会话
            plan_id: 训练计划ID
            status: 新状态 (active, completed, paused)
            is_active: 是否激活

        Returns:
            更新后的训练计划
        """
        plan = db.query(TrainingPlan).filter(TrainingPlan.id == plan_id).first()
        if not plan:
            return None

        plan.status = status
        if is_active is not None:
            plan.is_active = is_active

        db.add(plan)
        db.commit()
        db.refresh(plan)
        return plan

    def create_template_from_plan(self, db: Session, *, plan_id: int, new_name: Optional[str] = None) -> TrainingPlan:
        """从现有训练计划创建模板

        Args:
            db: 数据库会话
            plan_id: 源训练计划ID
            new_name: 新模板名称

        Returns:
            新创建的训练计划模板
        """
        # 获取源训练计划
        source_plan = db.query(TrainingPlan).filter(TrainingPlan.id == plan_id).first()
        if not source_plan:
            return None

        # 创建新的训练计划模板
        template_name = new_name or f"{source_plan.plan_name} (模板)"
        template = TrainingPlan(
            user_id=source_plan.user_id,
            plan_name=template_name,
            description=source_plan.description,
            fitness_goal=source_plan.fitness_goal,
            experience_level=source_plan.experience_level,
            duration_weeks=source_plan.duration_weeks,
            start_date=source_plan.start_date,
            end_date=source_plan.end_date,
            is_active=True,
            is_template=True,
            privacy_setting=source_plan.privacy_setting,
            status="active"
        )

        db.add(template)
        db.commit()
        db.refresh(template)

        # 获取源训练计划的所有训练日
        source_workouts = db.query(Workout).filter(Workout.training_plan_id == plan_id).all()

        # 复制每个训练日
        for source_workout in source_workouts:
            new_workout = Workout(
                training_plan_id=template.id,
                name=source_workout.name,
                day_of_week=source_workout.day_of_week,
                day_number=source_workout.day_number,
                description=source_workout.description,
                estimated_duration=source_workout.estimated_duration,
                scheduled_date=source_workout.scheduled_date,
                status=WorkoutStatus.NOT_STARTED
            )

            db.add(new_workout)
            db.commit()
            db.refresh(new_workout)

            # 获取源训练日的所有训练动作
            source_exercises = db.query(WorkoutExercise).filter(
                WorkoutExercise.workout_id == source_workout.id
            ).all()

            # 复制每个训练动作
            for source_exercise in source_exercises:
                new_exercise = WorkoutExercise(
                    workout_id=new_workout.id,
                    exercise_id=source_exercise.exercise_id,
                    sets=source_exercise.sets,
                    reps=source_exercise.reps,
                    rest_seconds=source_exercise.rest_seconds,
                    order=source_exercise.order,
                    notes=source_exercise.notes,
                    exercise_type=source_exercise.exercise_type,
                    superset_group=source_exercise.superset_group,
                    weight=source_exercise.weight
                )

                db.add(new_exercise)

            db.commit()

        return template

    def create_plan_from_template(
        self, db: Session, *, template_id: int, user_id: int, new_name: Optional[str] = None
    ) -> TrainingPlan:
        """从模板创建新的训练计划

        Args:
            db: 数据库会话
            template_id: 模板ID
            user_id: 用户ID
            new_name: 新计划名称

        Returns:
            新创建的训练计划
        """
        # 获取模板
        template = db.query(TrainingPlan).filter(TrainingPlan.id == template_id).first()
        if not template:
            return None

        # 创建新的训练计划
        plan_name = new_name or f"{template.plan_name} (复制)"
        plan = TrainingPlan(
            user_id=user_id,
            plan_name=plan_name,
            description=template.description,
            fitness_goal=template.fitness_goal,
            experience_level=template.experience_level,
            duration_weeks=template.duration_weeks,
            start_date=template.start_date,
            end_date=template.end_date,
            is_active=True,
            is_template=False,
            privacy_setting=template.privacy_setting,
            status="active"
        )

        db.add(plan)
        db.commit()
        db.refresh(plan)

        # 获取模板的所有训练日
        template_workouts = db.query(Workout).filter(Workout.training_plan_id == template_id).all()

        # 复制每个训练日
        for template_workout in template_workouts:
            new_workout = Workout(
                training_plan_id=plan.id,
                name=template_workout.name,
                day_of_week=template_workout.day_of_week,
                day_number=template_workout.day_number,
                description=template_workout.description,
                estimated_duration=template_workout.estimated_duration,
                scheduled_date=template_workout.scheduled_date,
                status=WorkoutStatus.NOT_STARTED
            )

            db.add(new_workout)
            db.commit()
            db.refresh(new_workout)

            # 获取模板训练日的所有训练动作
            template_exercises = db.query(WorkoutExercise).filter(
                WorkoutExercise.workout_id == template_workout.id
            ).all()

            # 复制每个训练动作
            for template_exercise in template_exercises:
                new_exercise = WorkoutExercise(
                    workout_id=new_workout.id,
                    exercise_id=template_exercise.exercise_id,
                    sets=template_exercise.sets,
                    reps=template_exercise.reps,
                    rest_seconds=template_exercise.rest_seconds,
                    order=template_exercise.order,
                    notes=template_exercise.notes,
                    exercise_type=template_exercise.exercise_type,
                    superset_group=template_exercise.superset_group,
                    weight=template_exercise.weight
                )

                db.add(new_exercise)

            db.commit()

        return plan


training_plan = CRUDTrainingPlan(TrainingPlan)