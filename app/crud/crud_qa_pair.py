from typing import List, Optional, Dict, Any
from fastapi.encoders import jsonable_encoder
from sqlalchemy.orm import Session
from app.crud.base import CRUDBase
from app.models.qa_pair import QAPair
from app.schemas.qa_pair import QAPairCreate, QAPairUpdate

class CRUDQAPair(CRUDBase[QAPair, QAPairCreate, QAPairUpdate]):
    def create(
        self, db: Session, *, obj_in: Dict[str, Any]
    ) -> QAPair:
        """创建新的QA对"""
        db_obj = QAPair(**obj_in)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def get_by_user(
        self, db: Session, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[QAPair]:
        """获取用户的QA对列表"""
        return (
            db.query(QAPair)
            .filter(QAPair.user_id == user_id)
            .order_by(QAPair.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_by_session(
        self, db: Session, *, session_id: str, skip: int = 0, limit: int = 100
    ) -> List[QAPair]:
        """获取指定会话的QA对列表"""
        return (
            db.query(QAPair)
            .filter(QAPair.session_id == session_id)
            .order_by(QAPair.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def update_feedback(
        self, db: Session, *, qa_pair_id: int, feedback_score: int
    ) -> QAPair:
        """更新QA对的反馈评分"""
        db_obj = db.query(QAPair).filter(QAPair.id == qa_pair_id).first()
        if db_obj:
            db_obj.feedback_score = feedback_score
            db.add(db_obj)
            db.commit()
            db.refresh(db_obj)
        return db_obj
    
    def count_by_user(self, db: Session, *, user_id: int) -> int:
        """计算用户的QA对总数"""
        return db.query(QAPair).filter(QAPair.user_id == user_id).count()

crud_qa_pair = CRUDQAPair(QAPair) 