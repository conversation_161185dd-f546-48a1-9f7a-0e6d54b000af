from typing import List, Dict, Optional, Any
from datetime import datetime
from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.workout import Workout
from app.models.workout_exercise import WorkoutExercise
from app.schemas.training_plan import WorkoutCreate, WorkoutUpdate


class CRUDWorkout(CRUDBase[Workout, WorkoutCreate, WorkoutUpdate]):
    """训练日的CRUD操作"""

    def get_by_plan(self, db: Session, *, training_plan_id: int) -> List[Workout]:
        """获取训练计划的所有训练日

        Args:
            db: 数据库会话
            training_plan_id: 训练计划ID

        Returns:
            训练日列表
        """
        return db.query(self.model).filter(Workout.training_plan_id == training_plan_id)\
            .order_by(Workout.day_number).all()

    def get_by_date(self, db: Session, *, date: datetime, user_id: int) -> List[Workout]:
        """获取指定日期的所有训练日

        Args:
            db: 数据库会话
            date: 日期
            user_id: 用户ID

        Returns:
            训练日列表
        """
        from app.models.training_plan import TrainingPlan

        return db.query(self.model).join(
            TrainingPlan,
            Workout.training_plan_id == TrainingPlan.id
        ).filter(
            TrainingPlan.user_id == user_id,
            Workout.scheduled_date == date.date(),
            TrainingPlan.is_active == True
        ).all()

    def get_with_exercises(self, db: Session, *, id: int) -> Optional[Dict[str, Any]]:
        """获取训练日及其所有训练动作

        Args:
            db: 数据库会话
            id: 训练日ID

        Returns:
            包含所有训练动作的训练日数据
        """
        # 查询训练日
        workout = db.query(Workout).filter(Workout.id == id).first()
        if not workout:
            return None

        # 将ORM对象转换为字典
        workout_dict = {
            "id": workout.id,
            "training_plan_id": workout.training_plan_id,
            "name": workout.name,
            "day_number": workout.day_number,
            "day_of_week": workout.day_of_week,
            "description": workout.description,
            "estimated_duration": workout.estimated_duration,
            "scheduled_date": workout.scheduled_date,
            "status": workout.status,
            "created_at": workout.created_at,
            "updated_at": workout.updated_at,
            "actual_duration": workout.actual_duration,
            "net_duration": workout.net_duration,
            "start_time": workout.start_time,
            "end_time": workout.end_time,
            "exercises": []
        }

        # 查询训练动作
        exercises = db.query(WorkoutExercise).filter(WorkoutExercise.workout_id == id)\
            .order_by(WorkoutExercise.order).all()

        # 获取所有训练动作的ID列表
        exercise_ids = [ex.exercise_id for ex in exercises]

        # 批量查询所有训练动作的详细信息
        from app.models.exercise import Exercise, ExerciseDetail
        exercise_details = {}
        exercise_video_urls = {}
        if exercise_ids:
            details = db.query(Exercise).filter(Exercise.id.in_(exercise_ids)).all()
            exercise_details = {d.id: d for d in details}

            # 获取视频URL
            video_details = db.query(ExerciseDetail).filter(ExerciseDetail.exercise_id.in_(exercise_ids)).all()
            exercise_video_urls = {d.exercise_id: d.video_file for d in video_details}

        for exercise in exercises:
            # 获取训练动作的详细信息
            detail = exercise_details.get(exercise.exercise_id)

            exercise_dict = {
                "id": exercise.id,
                "exercise_id": exercise.exercise_id,
                "exercise_name": detail.name if detail else f"动作ID: {exercise.exercise_id}",
                "exercise_image": detail.image_name if detail else None,
                "video_url": exercise_video_urls.get(exercise.exercise_id),
                "sets": exercise.sets,
                "reps": exercise.reps,
                "rest_seconds": exercise.rest_seconds,
                "order": exercise.order,
                "notes": exercise.notes,
                "exercise_type": exercise.exercise_type,
                "superset_group": exercise.superset_group,
                "weight": exercise.weight
            }
            workout_dict["exercises"].append(exercise_dict)

        return workout_dict

    def create_with_exercises(
        self, db: Session, *, plan_id: int, workout_data: Dict[str, Any]
    ) -> Workout:
        """创建训练日及其训练动作

        Args:
            db: 数据库会话
            plan_id: 训练计划ID
            workout_data: 训练日数据

        Returns:
            创建的训练日
        """
        # 创建训练日
        workout_in = {
            "training_plan_id": plan_id,
            "name": workout_data.get("name", "新训练日"),
            "day_number": workout_data.get("day_number", 1),
            "day_of_week": workout_data.get("day_of_week"),
            "description": workout_data.get("description"),
            "estimated_duration": workout_data.get("estimated_duration"),
            "scheduled_date": workout_data.get("scheduled_date"),
            "status": workout_data.get("status", "not_started")
        }

        workout = self.create(db, obj_in=workout_in)

        # 创建训练动作
        from app.crud.crud_workout_exercise import workout_exercise
        for i, exercise_data in enumerate(workout_data.get("exercises", [])):
            exercise_in = {
                "workout_id": workout.id,
                "exercise_id": exercise_data.get("exercise_id"),
                "sets": exercise_data.get("sets", 3),
                "reps": exercise_data.get("reps", "10"),
                "rest_seconds": exercise_data.get("rest_seconds", 60),
                "order": exercise_data.get("order", i+1),
                "notes": exercise_data.get("notes"),
                "weight": exercise_data.get("weight"),
                "exercise_type": exercise_data.get("exercise_type", "weight_reps"),
                "superset_group": exercise_data.get("superset_group")
            }

            workout_exercise.create(db, obj_in=exercise_in)

        return workout

    def update_with_exercises(
        self, db: Session, *, id: int, workout_data: Dict[str, Any]
    ) -> Workout:
        """更新训练日及其训练动作

        Args:
            db: 数据库会话
            id: 训练日ID
            workout_data: 训练日数据

        Returns:
            更新后的训练日
        """
        # 更新训练日基本信息
        workout_update = {
            "name": workout_data.get("name"),
            "day_of_week": workout_data.get("day_of_week"),
            "description": workout_data.get("description"),
            "estimated_duration": workout_data.get("estimated_duration"),
            "scheduled_date": workout_data.get("scheduled_date"),
            "status": workout_data.get("status")
        }

        # 过滤掉None值
        workout_update = {k: v for k, v in workout_update.items() if v is not None}

        workout = self.update(db, db_obj=self.get(db, id=id), obj_in=workout_update)

        # 处理训练动作
        if "exercises" in workout_data:
            # 获取当前所有训练动作
            from app.crud.crud_workout_exercise import workout_exercise
            current_exercises = db.query(WorkoutExercise).filter(
                WorkoutExercise.workout_id == id
            ).all()

            # 删除所有现有的训练动作
            for exercise in current_exercises:
                db.delete(exercise)
            db.commit()

            # 创建新的训练动作
            for i, exercise_data in enumerate(workout_data.get("exercises", [])):
                exercise_in = {
                    "workout_id": workout.id,
                    "exercise_id": exercise_data.get("exercise_id"),
                    "sets": exercise_data.get("sets", 3),
                    "reps": exercise_data.get("reps", "10"),
                    "rest_seconds": exercise_data.get("rest_seconds", 60),
                    "order": exercise_data.get("order", i+1),
                    "notes": exercise_data.get("notes"),
                    "weight": exercise_data.get("weight"),
                    "exercise_type": exercise_data.get("exercise_type", "weight_reps"),
                    "superset_group": exercise_data.get("superset_group")
                }

                workout_exercise.create(db, obj_in=exercise_in)

        return workout


workout = CRUDWorkout(Workout)