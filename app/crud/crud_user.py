from typing import Any, Dict, Optional, Union
from sqlalchemy.orm import Session
import logging

from app.crud.base import CRUDBase
from app.models.user import User
from app.schemas.user import UserCreate, UserUpdate
from app.crud.user import user  # 导入现有的 user 实例

logger = logging.getLogger(__name__)

def update_user_info(db: Session, *, user_id: int, field: str, value: Any) -> Optional[User]:
    """
    更新用户单个字段的信息
    
    Args:
        db: 数据库会话
        user_id: 用户 ID
        field: 要更新的字段名
        value: 字段的新值
        
    Returns:
        更新后的用户对象，如果用户不存在则返回 None
    """
    # 获取用户
    db_user = db.query(User).filter(User.id == user_id).first()
    if not db_user:
        logger.warning(f"用户不存在: user_id={user_id}")
        return None
    
    # 更新字段
    try:
        # 特殊字段处理
        if field in ['gender', 'experience_level', 'fitness_goal', 'activity_level']:
            try:
                value = int(value)
            except (ValueError, TypeError):
                # 如果无法转换为整数，记录警告但继续使用原值
                logger.warning(f"无法将字段 {field} 的值 '{value}' 转换为整数")
        
        # 设置字段值
        setattr(db_user, field, value)
        logger.info(f"更新用户 {user_id} 的字段 {field}: {value}")
        
        # 提交更改
        db.commit()
        
        # 刷新用户对象
        db.refresh(db_user)
        
        # 计算健康指标
        if field in ['height', 'weight', 'age', 'gender', 'activity_level']:
            user.calculate_health_metrics(db, db_user)
        
        return db_user
    except Exception as e:
        db.rollback()
        logger.error(f"更新用户信息失败: {str(e)}")
        raise 