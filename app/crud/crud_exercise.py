from typing import List, Optional, Dict, Any, Union, TypeVar, Generic, Type
from sqlalchemy.orm import Session
from sqlalchemy import or_, func, desc, asc, case, text, select, literal, bindparam
from app.models.exercise import Exercise, ExerciseDetail, Muscle, BodyPart, Equipment
from app.crud.base import CRUDBase
from app.schemas.exercise import (
    ExerciseCreate, ExerciseUpdate,
    ExerciseDetailCreate, ExerciseDetailUpdate,
    MuscleCreate, BodyPartCreate, EquipmentCreate
)

class CRUDExercise(CRUDBase[Exercise, ExerciseCreate, ExerciseUpdate]):
    """健身动作CRUD操作类"""
    
    def get_multi_filtered(
        self,
        db: Session,
        *,
        body_part_id: Optional[int] = None,
        equipment_id: Optional[int] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Exercise]:
        """获取筛选后的健身动作列表"""
        query = db.query(self.model)
        
        if body_part_id:
            query = query.filter(self.model.body_part_id.any(body_part_id))
        if equipment_id:
            query = query.filter(self.model.equipment_id.any(equipment_id))
            
        # 添加排序逻辑：按照id升序，sort_priority降序排序
        query = query.order_by(
            asc(self.model.id),
            desc(self.model.sort_priority)
        )
            
        return query.offset(skip).limit(limit).all()
    
    def search(
        self,
        db: Session,
        *,
        keyword: Optional[str] = None,
        body_part_id: Optional[int] = None,
        equipment_id: Optional[int] = None,
        muscle_id: Optional[int] = None,
        difficulty: Optional[str] = None,
        skip: int = 0,
        limit: int = 5
    ) -> List[Exercise]:
        """搜索健身动作"""
        # 初始查询
        base_query = db.query(self.model)
        
        # 应用过滤条件
        if difficulty is not None:
            base_query = base_query.filter(self.model.level == int(difficulty))
        
        if body_part_id is not None:
            base_query = base_query.filter(self.model.body_part_id.contains([body_part_id]))
            
        if equipment_id is not None:
            base_query = base_query.filter(self.model.equipment_id.contains([equipment_id]))
        
        if muscle_id is not None:
            base_query = base_query.outerjoin(
                ExerciseDetail, 
                self.model.id == ExerciseDetail.exercise_id
            )
            base_query = base_query.filter(
                or_(
                    ExerciseDetail.target_muscles_id.contains([muscle_id]),
                    ExerciseDetail.synergist_muscles_id.contains([muscle_id])
                )
            )
        
        # 关键词搜索和排序逻辑
        if keyword:
            keyword = keyword.strip().lower()
            
            # 定义不同匹配类型的条件和权重
            conditions = []
            weights = []
            
            # 名称匹配条件 - 中文
            conditions.extend([
                func.lower(self.model.name) == keyword,  # 精确匹配
                func.lower(self.model.name).startswith(keyword),  # 前缀匹配
                func.lower(self.model.name).contains(keyword)  # 包含匹配
            ])
            weights.extend([100, 80, 60])  # 较高权重
            
            # 英文名称匹配条件
            if hasattr(self.model, 'en_name'):
                conditions.extend([
                    func.lower(self.model.en_name) == keyword,  # 精确匹配
                    func.lower(self.model.en_name).startswith(keyword),  # 前缀匹配
                    func.lower(self.model.en_name).contains(keyword)  # 包含匹配
                ])
                weights.extend([50, 40, 30])  # 中等权重
            
            # 描述匹配条件
            if hasattr(self.model, 'description'):
                conditions.append(func.lower(self.model.description).contains(keyword))
                weights.append(10)  # 较低权重
                
            # 构建动态CASE表达式计算相关性得分
            relevance_score = case(
                *[(condition, literal(weight)) for condition, weight in zip(conditions, weights)],
                else_=literal(0)
            ).label('relevance_score')
            
            # 将相关性得分添加到查询中
            base_query = base_query.add_columns(relevance_score)
            
            # 应用关键词过滤 - 任一条件满足即可
            keyword_filter = or_(*conditions)
            base_query = base_query.filter(keyword_filter)
            
            # 按相关性得分排序，然后是其他业务规则
            base_query = base_query.order_by(
                desc('relevance_score'),
                desc(self.model.hit_time) if hasattr(self.model, 'hit_time') else desc(self.model.id),
                desc(self.model.sort_priority),
                asc(self.model.id)
            )
        else:
            # 无关键词时添加虚拟相关性得分0
            relevance_score = literal(0).label('relevance_score')
            base_query = base_query.add_columns(relevance_score)
            
            # 无关键词时的排序逻辑
            base_query = base_query.order_by(
                desc(self.model.hit_time) if hasattr(self.model, 'hit_time') else desc(self.model.id),
                desc(self.model.sort_priority),
                asc(self.model.id)
            )
            
        # 执行分页并获取结果
        results = base_query.offset(skip).limit(limit).all()
        
        # 处理结果集，剥离相关性得分返回Exercise对象
        exercises = [result[0] for result in results] if keyword else results
            
        return exercises
    
    def increment_hit_count(self, db: Session, exercise_id: int) -> bool:
        """增加健身动作浏览次数"""
        db_exercise = db.query(self.model).filter(self.model.id == exercise_id).first()
        if db_exercise:
            if hasattr(db_exercise, 'hit_time'):
                db_exercise.hit_time = (db_exercise.hit_time or 0) + 1
                db.commit()
                return True
        return False


class CRUDExerciseDetail(CRUDBase[ExerciseDetail, ExerciseDetailCreate, ExerciseDetailUpdate]):
    """健身动作详情CRUD操作类"""
    
    def get_by_exercise_id(self, db: Session, exercise_id: int) -> Optional[ExerciseDetail]:
        """根据健身动作ID获取详情"""
        return db.query(self.model).filter(self.model.exercise_id == exercise_id).first()
    
    def create_with_exercise_id(self, db: Session, *, obj_in: Union[ExerciseDetailCreate, Dict[str, Any]], exercise_id: int) -> ExerciseDetail:
        """创建健身动作详情"""
        if isinstance(obj_in, dict):
            create_data = obj_in.copy()
        else:
            create_data = obj_in.dict(exclude_unset=True)
        
        create_data["exercise_id"] = exercise_id
        db_obj = self.model(**create_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def update_by_exercise_id(
        self, 
        db: Session, 
        *, 
        exercise_id: int, 
        obj_in: Union[ExerciseDetailUpdate, Dict[str, Any]]
    ) -> Optional[ExerciseDetail]:
        """根据健身动作ID更新详情"""
        db_obj = db.query(self.model).filter(self.model.exercise_id == exercise_id).first()
        if db_obj:
            if isinstance(obj_in, dict):
                update_data = obj_in
            else:
                update_data = obj_in.dict(exclude_unset=True)
            
            for field in update_data:
                if field in update_data:
                    setattr(db_obj, field, update_data[field])
            db.commit()
            db.refresh(db_obj)
            return db_obj
        return None


class CRUDMuscle(CRUDBase[Muscle, MuscleCreate, Any]):
    """肌肉CRUD操作类"""
    pass


class CRUDBodyPart(CRUDBase[BodyPart, BodyPartCreate, Any]):
    """身体部位CRUD操作类"""
    pass


class CRUDEquipment(CRUDBase[Equipment, EquipmentCreate, Any]):
    """器材CRUD操作类"""
    pass


# 创建实例
exercise = CRUDExercise(Exercise)
exercise_detail = CRUDExerciseDetail(ExerciseDetail)
muscle = CRUDMuscle(Muscle)
body_part = CRUDBodyPart(BodyPart)
equipment = CRUDEquipment(Equipment)