from typing import Any, Dict, Optional, Union, List
from sqlalchemy.orm import Session
from app.crud.base import CRUDBase
from app.models.user_setting import UserSetting
from app.schemas.user_setting import UserSettingCreate, UserSettingUpdate


class CRUDUserSetting(CRUDBase[UserSetting, UserSettingCreate, UserSettingUpdate]):
    """用户设置CRUD操作"""
    
    def get_by_user_id(self, db: Session, *, user_id: int) -> Optional[UserSetting]:
        """根据用户ID获取用户设置
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            
        Returns:
            用户设置对象，如果不存在则返回None
        """
        return db.query(UserSetting).filter(UserSetting.user_id == user_id).first()
    
    def create_or_update(
        self, db: Session, *, user_id: int, obj_in: Union[UserSettingUpdate, Dict[str, Any]]
    ) -> UserSetting:
        """创建或更新用户设置
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            obj_in: 更新数据
            
        Returns:
            更新后的用户设置对象
        """
        # 查找现有设置
        db_obj = self.get_by_user_id(db, user_id=user_id)
        
        if db_obj:
            # 如果存在，则更新
            return self.update(db, db_obj=db_obj, obj_in=obj_in)
        else:
            # 如果不存在，则创建
            if isinstance(obj_in, dict):
                create_data = obj_in.copy()
                create_data["user_id"] = user_id
                return self.create(db, obj_in=UserSettingCreate(**create_data))
            else:
                # 如果是Pydantic模型，转换为字典
                create_data = obj_in.dict()
                create_data["user_id"] = user_id
                return self.create(db, obj_in=UserSettingCreate(**create_data))
    
    def update_ai_character_type(
        self, db: Session, *, user_id: int, character_type: str
    ) -> Optional[UserSetting]:
        """更新用户的AI角色类型偏好
        
        Args:
            db: 数据库会话
            user_id: 用户ID
            character_type: AI角色类型
            
        Returns:
            更新后的用户设置对象，如果用户不存在则返回None
        """
        db_obj = self.get_by_user_id(db, user_id=user_id)
        
        if not db_obj:
            # 如果用户设置不存在，创建新的
            return self.create_or_update(
                db, user_id=user_id, obj_in={"ai_character_type": character_type}
            )
        
        # 更新角色类型
        return self.update(db, db_obj=db_obj, obj_in={"ai_character_type": character_type})


crud_user_setting = CRUDUserSetting(UserSetting)
