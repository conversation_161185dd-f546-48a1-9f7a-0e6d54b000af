from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from app.crud.base import CRUDBase
from app import models, schemas


class CRUDUserTrainingRecord(CRUDBase[
    models.UserTrainingRecord, 
    schemas.UserTrainingRecordCreate, 
    schemas.UserTrainingRecordUpdate
]):
    """用户训练记录CRUD操作"""
    
    def get_by_user_and_date(
        self, db: Session, *, user_id: int, date: datetime
    ) -> Optional[models.UserTrainingRecord]:
        """获取用户某一天的训练记录"""
        return db.query(models.UserTrainingRecord).filter(
            models.UserTrainingRecord.user_id == user_id,
            models.UserTrainingRecord.date == date.date()  # 只比较日期部分
        ).first()
    
    def get_user_training_history(
        self, db: Session, *, user_id: int, days: int = 7
    ) -> List[models.UserTrainingRecord]:
        """获取用户最近N天的训练记录"""
        cutoff_date = datetime.now() - timedelta(days=days)
        return (
            db.query(models.UserTrainingRecord)
            .filter(
                models.UserTrainingRecord.user_id == user_id,
                models.UserTrainingRecord.date >= cutoff_date
            )
            .order_by(models.UserTrainingRecord.date.desc())
            .all()
        )
    
    def get_user_recent_trained_body_parts(
        self, db: Session, *, user_id: int, days: int = 7
    ) -> Dict[str, datetime]:
        """获取用户最近训练过的身体部位及其最近训练日期"""
        records = self.get_user_training_history(db, user_id=user_id, days=days)
        
        # 记录每个部位最近的训练日期
        body_parts_last_trained = {}
        for record in records:
            if record.body_parts:
                for part in record.body_parts:
                    # 只保留最近的训练日期
                    if part not in body_parts_last_trained or record.date > body_parts_last_trained[part]:
                        body_parts_last_trained[part] = record.date
        
        return body_parts_last_trained
    
    def create_with_user(
        self, db: Session, *, obj_in: schemas.UserTrainingRecordCreate, user_id: int
    ) -> models.UserTrainingRecord:
        """创建用户训练记录"""
        db_obj = models.UserTrainingRecord(
            user_id=user_id,
            date=obj_in.date,
            body_parts=obj_in.body_parts,
            duration_minutes=obj_in.duration_minutes,
            exercises_data=obj_in.exercises_data,
            notes=obj_in.notes
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj


crud_user_training_record = CRUDUserTrainingRecord(models.UserTrainingRecord) 