from typing import List, Dict, Optional, Any
from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.workout_exercise import WorkoutExercise
from app.schemas.training_plan import WorkoutExerciseCreate, WorkoutExerciseUpdate


class CRUDWorkoutExercise(CRUDBase[WorkoutExercise, WorkoutExerciseCreate, WorkoutExerciseUpdate]):
    """训练动作的CRUD操作"""

    def get_by_workout(self, db: Session, *, workout_id: int) -> List[WorkoutExercise]:
        """获取训练日的所有训练动作

        Args:
            db: 数据库会话
            workout_id: 训练日ID

        Returns:
            训练动作列表
        """
        return db.query(self.model).filter(WorkoutExercise.workout_id == workout_id)\
            .order_by(WorkoutExercise.order).all()

    def get_by_daily_workout(self, db: Session, *, daily_workout_id: int) -> List[WorkoutExercise]:
        """获取单日训练的所有训练动作

        Args:
            db: 数据库会话
            daily_workout_id: 单日训练ID

        Returns:
            训练动作列表
        """
        return db.query(self.model).filter(WorkoutExercise.daily_workout_id == daily_workout_id)\
            .order_by(WorkoutExercise.order).all()

    def get_with_detail(self, db: Session, *, id: int) -> Optional[Dict[str, Any]]:
        """获取训练动作及其详细信息

        Args:
            db: 数据库会话
            id: 训练动作ID

        Returns:
            包含详细信息的训练动作数据
        """
        # 查询训练动作
        exercise = db.query(WorkoutExercise).filter(WorkoutExercise.id == id).first()
        if not exercise:
            return None

        # 查询动作详细信息
        from app.models.exercise import Exercise
        detail = db.query(Exercise).filter(Exercise.id == exercise.exercise_id).first()

        # 将ORM对象转换为字典
        exercise_dict = {
            "id": exercise.id,
            "workout_id": exercise.workout_id,
            "exercise_id": exercise.exercise_id,
            "exercise_name": detail.name if detail else f"动作ID: {exercise.exercise_id}",
            "exercise_image": detail.image_name if detail else None,
            "exercise_description": detail.description if detail else None,
            "sets": exercise.sets,
            "reps": exercise.reps,
            "rest_seconds": exercise.rest_seconds,
            "order": exercise.order,
            "notes": exercise.notes,
            "exercise_type": exercise.exercise_type,
            "superset_group": exercise.superset_group,
            "weight": exercise.weight
        }

        return exercise_dict

    def update_order(self, db: Session, *, workout_id: int, order_data: List[Dict[str, Any]]) -> List[WorkoutExercise]:
        """更新训练动作顺序

        Args:
            db: 数据库会话
            workout_id: 训练日ID
            order_data: 包含id和新顺序的列表

        Returns:
            更新后的训练动作列表
        """
        # 先获取所有相关的训练动作
        exercises = db.query(WorkoutExercise).filter(WorkoutExercise.workout_id == workout_id).all()
        exercise_dict = {ex.id: ex for ex in exercises}

        # 遍历更新顺序
        for item in order_data:
            exercise_id = item.get("id")
            new_order = item.get("order")

            if exercise_id in exercise_dict and new_order is not None:
                exercise = exercise_dict[exercise_id]
                exercise.order = new_order
                db.add(exercise)

        db.commit()

        # 返回更新后的列表
        return self.get_by_workout(db, workout_id=workout_id)

    def update_superset_groups(self, db: Session, *, workout_id: int, superset_data: List[Dict[str, Any]]) -> List[WorkoutExercise]:
        """更新超级组分组

        Args:
            db: 数据库会话
            workout_id: 训练日ID
            superset_data: 包含id和超级组ID的列表

        Returns:
            更新后的训练动作列表
        """
        # 先获取所有相关的训练动作
        exercises = db.query(WorkoutExercise).filter(WorkoutExercise.workout_id == workout_id).all()
        exercise_dict = {ex.id: ex for ex in exercises}

        # 遍历更新超级组
        for item in superset_data:
            exercise_id = item.get("id")
            superset_group = item.get("superset_group")

            if exercise_id in exercise_dict:
                exercise = exercise_dict[exercise_id]
                exercise.superset_group = superset_group
                db.add(exercise)

        db.commit()

        # 返回更新后的列表
        return self.get_by_workout(db, workout_id=workout_id)

    def bulk_create(self, db: Session, *, workout_id: int, exercises_data: List[Dict[str, Any]]) -> List[WorkoutExercise]:
        """批量创建训练动作

        Args:
            db: 数据库会话
            workout_id: 训练日ID
            exercises_data: 训练动作数据列表

        Returns:
            创建的训练动作列表
        """
        exercises = []

        for i, data in enumerate(exercises_data):
            exercise_in = {
                "workout_id": workout_id,
                "exercise_id": data.get("exercise_id"),
                "sets": data.get("sets", 3),
                "reps": data.get("reps", "10"),
                "rest_seconds": data.get("rest_seconds", 60),
                "order": data.get("order", i+1),
                "notes": data.get("notes"),
                "weight": data.get("weight"),
                "exercise_type": data.get("exercise_type", "weight_reps"),
                "superset_group": data.get("superset_group")
            }

            exercise = self.create(db, obj_in=exercise_in)
            exercises.append(exercise)

        return exercises


workout_exercise = CRUDWorkoutExercise(WorkoutExercise)