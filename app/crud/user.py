from typing import Any, Dict, Optional, Union
import datetime
import time

from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.user import User
from app.schemas.user import UserCreate, UserUpdate


class CRUDUser(CRUDBase[User, UserCreate, UserUpdate]):
    def get_by_email(self, db: Session, *, email: str) -> Optional[User]:
        return db.query(User).filter(User.email == email).first()

    def get_by_phone(self, db: Session, *, phone: str) -> Optional[User]:
        return db.query(User).filter(User.phone == phone).first()

    def get_by_openid(self, db: Session, *, openid: str) -> Optional[User]:
        return db.query(User).filter(User.openid == openid).first()

    def create(self, db: Session, *, obj_in: Union[UserCreate, Dict[str, Any]]) -> User:
        # Handle both UserCreate objects and dictionaries
        if isinstance(obj_in, dict):
            create_data = obj_in
        else:
            # Use model_dump instead of dict (which is deprecated)
            create_data = obj_in.model_dump() if hasattr(obj_in, 'model_dump') else obj_in.dict()

        # Create user object with fields from create_data
        db_obj = User(**{k: v for k, v in create_data.items() if k in User.__table__.columns.keys()})
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update(
        self, db: Session, *, db_obj: User, obj_in: Union[UserUpdate, Dict[str, Any]]
    ) -> User:
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            # Use model_dump instead of dict (which is deprecated)
            update_data = (obj_in.model_dump(exclude_unset=True)
                          if hasattr(obj_in, 'model_dump')
                          else obj_in.dict(exclude_unset=True))

        import logging
        logger = logging.getLogger("fitness-coach-api")
        logger.info(f"CRUD更新用户: 用户ID={db_obj.id}, 更新数据={update_data}")

        # 确保数据类型正确，防止类型错误
        for field in ['gender', 'experience_level', 'fitness_goal', 'activity_level']:
            if field in update_data and update_data[field] is not None:
                try:
                    old_value = update_data[field]
                    update_data[field] = int(update_data[field])
                    logger.info(f"字段 {field} 转换为整数: {old_value} -> {update_data[field]}")
                except (ValueError, TypeError) as e:
                    logger.warning(f"无法转换字段 {field} 为整数: {update_data[field]}, 错误: {str(e)}")
                    pass  # 如果无法转换，保留原值

        # 移除None值，防止将现有值更新为None
        original_count = len(update_data)
        update_data = {k: v for k, v in update_data.items() if v is not None}
        if original_count != len(update_data):
            logger.info(f"已移除 {original_count - len(update_data)} 个None值字段")

        # 添加超时操作到数据库事务
        start_time = time.time()
        TIMEOUT = 5  # 降低超时时间为5秒
        logger.info(f"设置更新超时时间: {TIMEOUT}秒")

        try:
            # 批量更新用户数据，减少循环操作
            if time.time() - start_time > TIMEOUT:
                logger.error("更新用户数据超时 (检查点1)")
                raise TimeoutError("更新用户数据超时")

            logger.info(f"开始更新用户字段: {list(update_data.keys())}")
            update_count = 0
            for key, value in update_data.items():
                current_value = getattr(db_obj, key, None)
                if value != current_value:
                    setattr(db_obj, key, value)
                    logger.info(f"更新字段 {key}: {current_value} -> {value}")
                    update_count += 1
                else:
                    logger.info(f"字段 {key} 值未变化: {current_value}")

            logger.info(f"已更新 {update_count} 个字段")

            # 设置更新时间
            db_obj.updated_at = datetime.datetime.now()
            logger.info(f"设置更新时间: {db_obj.updated_at}")

            # 提交到数据库
            if time.time() - start_time > TIMEOUT:
                logger.error("提交数据库超时 (检查点2)")
                raise TimeoutError("提交数据库超时")

            logger.info("开始提交数据库事务")
            before_commit = time.time()
            db.commit()
            commit_time = time.time() - before_commit
            logger.info(f"数据库提交完成，耗时: {commit_time:.4f}秒")

            # 刷新
            if time.time() - start_time > TIMEOUT:
                logger.error("刷新数据库对象超时 (检查点3)")
                raise TimeoutError("刷新数据库对象超时")

            logger.info("开始刷新数据库对象")
            before_refresh = time.time()
            db.refresh(db_obj)
            refresh_time = time.time() - before_refresh
            logger.info(f"数据库对象刷新完成，耗时: {refresh_time:.4f}秒")

            # 计算健康指标
            logger.info("开始计算健康指标")
            before_metrics = time.time()
            self.calculate_health_metrics(db, db_obj)
            metrics_time = time.time() - before_metrics
            logger.info(f"健康指标计算完成，耗时: {metrics_time:.4f}秒")

            total_time = time.time() - start_time
            logger.info(f"用户更新全部完成，总耗时: {total_time:.4f}秒")

            return db_obj
        except TimeoutError as e:
            # 回滚操作
            logger.error(f"超时错误: {str(e)}")
            db.rollback()
            logger.info("已回滚数据库事务")
            raise e
        except Exception as e:
            # 记录详细错误信息并回滚
            logger.error(f"更新用户数据失败: {str(e)}")
            logger.error(f"错误类型: {e.__class__.__name__}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            db.rollback()
            logger.info("已回滚数据库事务")
            raise e

    def calculate_health_metrics(self, db: Session, user: User) -> None:
        """计算BMI和TEDD等健康指标"""
        import logging
        logger = logging.getLogger("fitness-coach-api")
        logger.info(f"开始计算健康指标: 用户ID={user.id}, 身高={user.height}, 体重={user.weight}")

        try:
            if user.weight and user.height:
                # 计算BMI
                height_m = user.height / 100  # 转换为米
                bmi = user.weight / (height_m * height_m)
                user.bmi = round(bmi, 2)
                logger.info(f"计算BMI: 身高={user.height}cm, 体重={user.weight}kg, BMI={user.bmi}")

                # 计算TEDD (总能量消耗)
                if user.age and user.gender is not None:
                    # 基础代谢率(BMR)
                    gender_value = user.gender
                    logger.info(f"性别值: {gender_value}, 类型: {type(gender_value)}")

                    if gender_value == 1:  # 男性
                        bmr = 10 * user.weight + 6.25 * user.height - 5 * user.age + 5
                        logger.info(f"计算男性BMR: {bmr}")
                    else:  # 女性或其他
                        bmr = 10 * user.weight + 6.25 * user.height - 5 * user.age - 161
                        logger.info(f"计算女性BMR: {bmr}")

                    # 活动系数(PAL)
                    pal_map = {1: 1.2, 2: 1.375, 3: 1.55, 4: 1.725, 5: 1.9}
                    activity_level = user.activity_level
                    logger.info(f"活动水平: {activity_level}, 类型: {type(activity_level)}")

                    pal = pal_map.get(activity_level, 1.55)
                    logger.info(f"活动系数(PAL): {pal}")

                    # TEDD = BMR * PAL
                    user.tedd = int(bmr * pal)
                    logger.info(f"计算TEDD: BMR={bmr} * PAL={pal} = {user.tedd}")
                else:
                    logger.info(f"未计算TEDD: 年龄={user.age}, 性别={user.gender}")

                # 检查资料是否完善
                is_completed = all([
                    user.nickname,
                    user.gender in [1, 2],  # 1-男性, 2-女性
                    user.age is not None,
                    user.weight is not None,
                    user.height is not None,
                    user.activity_level is not None
                ])

                user.completed = is_completed
                logger.info(f"资料完善度检查: nickname={bool(user.nickname)}, gender={user.gender in [1, 2]}, " +
                           f"age={user.age is not None}, weight={user.weight is not None}, " +
                           f"height={user.height is not None}, activity_level={user.activity_level is not None}, " +
                           f"结果: {is_completed}")

                try:
                    logger.info("提交健康指标更新")
                    db.commit()
                    logger.info("健康指标提交成功")
                except Exception as e:
                    logger.error(f"提交健康指标失败: {str(e)}")
                    logger.error(f"错误类型: {e.__class__.__name__}")
                    import traceback
                    logger.error(f"错误堆栈: {traceback.format_exc()}")
                    db.rollback()
            else:
                logger.info(f"未计算健康指标: 身高={user.height}, 体重={user.weight}")
        except Exception as e:
            logger.error(f"计算健康指标时发生错误: {str(e)}")
            logger.error(f"错误类型: {e.__class__.__name__}")
            import traceback
            logger.error(f"错误堆栈: {traceback.format_exc()}")
            # 不抛出异常，保证主流程不受影响


user = CRUDUser(User)