from typing import Optional, List, Dict, Any, Union, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import desc, func
from datetime import datetime, date

from app.crud.base import CRUDBase
from app.models.gamification import Currency, CurrencyTransaction, ShopItem, UserPurchase
from app.schemas.gamification import (
    CurrencyCreate, CurrencyUpdate, CurrencyTransactionCreate,
    ShopItemCreate, ShopItemUpdate, UserPurchaseCreate, UserPurchaseUpdate
)


class CRUDCurrency(CRUDBase[Currency, CurrencyCreate, CurrencyUpdate]):
    def get_by_user_id(self, db: Session, *, user_id: int) -> Optional[Currency]:
        """获取用户货币记录"""
        return db.query(Currency).filter(Currency.user_id == user_id).first()
    
    def create_with_user(self, db: Session, *, user_id: int, initial_amount: int = 0) -> Currency:
        """为用户创建货币记录"""
        db_obj = Currency(
            user_id=user_id,
            amount=initial_amount,
            lifetime_earned=initial_amount,
            daily_earned_today=initial_amount if initial_amount > 0 else 0,
            last_reset_date=datetime.now().date(),
            updated_at=datetime.now()
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def add_currency(
        self, db: Session, *, user_id: int, amount: int, description: str,
        transaction_type: str = "EARN", related_entity_type: str = None, 
        related_entity_id: int = None
    ) -> Tuple[Currency, CurrencyTransaction]:
        """为用户添加货币，并记录交易"""
        # 获取或创建用户的货币记录
        currency = self.get_by_user_id(db, user_id=user_id)
        if not currency:
            currency = self.create_with_user(db, user_id=user_id)
        
        # 更新货币金额
        currency.amount += amount
        currency.lifetime_earned += amount if amount > 0 else 0
        
        # 如果是新的一天，重置daily_earned_today
        today = datetime.now().date()
        if currency.last_reset_date != today:
            currency.daily_earned_today = 0 if amount <= 0 else amount
            currency.last_reset_date = today
        else:
            currency.daily_earned_today += amount if amount > 0 else 0
        
        currency.updated_at = datetime.now()
        db.add(currency)
        
        # 创建交易记录
        transaction = CurrencyTransaction(
            currency_id=currency.id,
            user_id=user_id,
            amount=amount,
            balance_after=currency.amount,
            description=description,
            transaction_type=transaction_type,
            related_entity_type=related_entity_type,
            related_entity_id=related_entity_id,
            created_at=datetime.now()
        )
        db.add(transaction)
        
        db.commit()
        db.refresh(currency)
        db.refresh(transaction)
        
        return currency, transaction
    
    def spend_currency(
        self, db: Session, *, user_id: int, amount: int, description: str,
        transaction_type: str = "SPEND", related_entity_type: str = None,
        related_entity_id: int = None
    ) -> Tuple[bool, Optional[Currency], Optional[CurrencyTransaction]]:
        """用户花费货币，检查余额是否足够"""
        # 获取用户的货币记录
        currency = self.get_by_user_id(db, user_id=user_id)
        if not currency or currency.amount < amount:
            return False, currency, None
        
        # 更新货币金额
        currency.amount -= amount
        currency.updated_at = datetime.now()
        db.add(currency)
        
        # 创建交易记录
        transaction = CurrencyTransaction(
            currency_id=currency.id,
            user_id=user_id,
            amount=-amount,  # 负数表示支出
            balance_after=currency.amount,
            description=description,
            transaction_type=transaction_type,
            related_entity_type=related_entity_type,
            related_entity_id=related_entity_id,
            created_at=datetime.now()
        )
        db.add(transaction)
        
        db.commit()
        db.refresh(currency)
        db.refresh(transaction)
        
        return True, currency, transaction


class CRUDCurrencyTransaction(CRUDBase[CurrencyTransaction, CurrencyTransactionCreate, CurrencyTransactionCreate]):
    def get_by_user_id(
        self, db: Session, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[CurrencyTransaction]:
        """获取用户的所有交易记录"""
        return db.query(CurrencyTransaction).filter(
            CurrencyTransaction.user_id == user_id
        ).order_by(desc(CurrencyTransaction.created_at)).offset(skip).limit(limit).all()
    
    def get_by_user_and_type(
        self, db: Session, *, user_id: int, transaction_type: str, 
        skip: int = 0, limit: int = 100
    ) -> List[CurrencyTransaction]:
        """根据交易类型获取用户的交易记录"""
        return db.query(CurrencyTransaction).filter(
            CurrencyTransaction.user_id == user_id,
            CurrencyTransaction.transaction_type == transaction_type
        ).order_by(desc(CurrencyTransaction.created_at)).offset(skip).limit(limit).all()
    
    def get_daily_transactions(
        self, db: Session, *, user_id: int, day: date = None
    ) -> List[CurrencyTransaction]:
        """获取用户某一天的交易记录"""
        if day is None:
            day = datetime.now().date()
        
        next_day = day + datetime.timedelta(days=1)
        
        return db.query(CurrencyTransaction).filter(
            CurrencyTransaction.user_id == user_id,
            CurrencyTransaction.created_at >= day,
            CurrencyTransaction.created_at < next_day
        ).order_by(desc(CurrencyTransaction.created_at)).all()


class CRUDShopItem(CRUDBase[ShopItem, ShopItemCreate, ShopItemUpdate]):
    def get_by_name(self, db: Session, *, name: str) -> Optional[ShopItem]:
        """通过名称获取商店物品"""
        return db.query(ShopItem).filter(ShopItem.name == name).first()
    
    def get_multi_by_category(
        self, db: Session, *, category: str, skip: int = 0, limit: int = 100
    ) -> List[ShopItem]:
        """根据类别获取商店物品"""
        return db.query(ShopItem).filter(
            ShopItem.category == category,
            ShopItem.is_active == True
        ).offset(skip).limit(limit).all()
    
    def get_available_items(
        self, db: Session, *, user_level_type: str = None, user_level: int = None,
        skip: int = 0, limit: int = 100
    ) -> List[ShopItem]:
        """获取可用的商店物品，考虑等级限制"""
        query = db.query(ShopItem).filter(
            ShopItem.is_active == True,
            or_(
                ShopItem.available_from.is_(None),
                ShopItem.available_from <= datetime.now()
            ),
            or_(
                ShopItem.available_until.is_(None),
                ShopItem.available_until >= datetime.now()
            )
        )
        
        # 如果提供了用户等级信息，过滤出可用的物品
        if user_level_type and user_level:
            query = query.filter(
                or_(
                    ShopItem.discount_level_type.is_(None),
                    and_(
                        ShopItem.discount_level_type == user_level_type,
                        ShopItem.discount_level_required <= user_level
                    )
                )
            )
        
        return query.offset(skip).limit(limit).all()
    
    def get_discounted_price(
        self, db: Session, *, item_id: int, user_level_type: str = None, user_level: int = None
    ) -> Tuple[Optional[ShopItem], int]:
        """获取物品的折扣价格"""
        item = self.get(db, id=item_id)
        
        if not item:
            return None, 0
        
        # 计算折扣价格
        discounted_price = item.price
        
        if (item.discount_level_type == user_level_type and 
                item.discount_level_required and 
                user_level and 
                user_level >= item.discount_level_required and
                item.discount_percentage):
            
            discount = item.discount_percentage / 100.0
            discounted_price = int(item.price * (1 - discount))
        
        return item, discounted_price


class CRUDUserPurchase(CRUDBase[UserPurchase, UserPurchaseCreate, UserPurchaseUpdate]):
    def get_by_user_id(
        self, db: Session, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[UserPurchase]:
        """获取用户的所有购买记录"""
        return db.query(UserPurchase).filter(
            UserPurchase.user_id == user_id
        ).order_by(desc(UserPurchase.created_at)).offset(skip).limit(limit).all()
    
    def get_by_item(
        self, db: Session, *, user_id: int, item_id: int, skip: int = 0, limit: int = 100
    ) -> List[UserPurchase]:
        """获取用户购买特定物品的记录"""
        return db.query(UserPurchase).filter(
            UserPurchase.user_id == user_id,
            UserPurchase.item_id == item_id
        ).order_by(desc(UserPurchase.created_at)).offset(skip).limit(limit).all()
    
    def purchase_item(
        self, db: Session, *, user_id: int, item_id: int, quantity: int = 1,
        price_paid: int = None, currency_crud = None
    ) -> Tuple[bool, Optional[UserPurchase]]:
        """用户购买物品，包括货币扣除"""
        # 获取商品信息
        item = db.query(ShopItem).filter(ShopItem.id == item_id).first()
        if not item or not item.is_active:
            return False, None
        
        # 检查库存是否充足
        if item.is_limited and (item.stock is None or item.stock < quantity):
            return False, None
        
        # 计算总价
        total_paid = price_paid * quantity if price_paid else item.price * quantity
        
        # 扣除货币（如果提供了currency_crud）
        if currency_crud:
            success, _, _ = currency_crud.spend_currency(
                db, 
                user_id=user_id, 
                amount=total_paid,
                description=f"购买 {item.name} x{quantity}",
                transaction_type="PURCHASE",
                related_entity_type="SHOP_ITEM",
                related_entity_id=item.id
            )
            
            if not success:
                return False, None
        
        # 创建购买记录
        purchase = UserPurchase(
            user_id=user_id,
            item_id=item_id,
            quantity=quantity,
            price_paid=price_paid or item.price,
            total_paid=total_paid,
            discount_applied=item.discount_percentage if price_paid and price_paid < item.price else None,
            status="COMPLETED",
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        db.add(purchase)
        
        # 更新商品库存
        if item.is_limited and item.stock is not None:
            item.stock -= quantity
            db.add(item)
        
        db.commit()
        db.refresh(purchase)
        
        return True, purchase


currency = CRUDCurrency(Currency)
currency_transaction = CRUDCurrencyTransaction(CurrencyTransaction)
shop_item = CRUDShopItem(ShopItem)
user_purchase = CRUDUserPurchase(UserPurchase) 