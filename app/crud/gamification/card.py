from typing import Optional, List, Dict, Any, Union, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import func, or_, and_
from datetime import datetime

from app.crud.base import CRUDBase
from app.models.gamification import Card, UserCard, CardSynthesisRecipe, CardSynthesisIngredient
from app.schemas.gamification import (
    CardCreate, CardUpdate, UserCardCreate, UserCardUpdate,
    CardSynthesisRecipeCreate, CardSynthesisRecipeUpdate,
    CardSynthesisIngredientCreate
)


class CRUDCard(CRUDBase[Card, CardCreate, CardUpdate]):
    def get_by_name(self, db: Session, *, name: str) -> Optional[Card]:
        """通过卡片名称获取卡片"""
        return db.query(Card).filter(Card.name == name).first()
    
    def get_multi_by_type(
        self, db: Session, *, card_type: str, skip: int = 0, limit: int = 100
    ) -> List[Card]:
        """根据卡片类型获取多个卡片"""
        return db.query(Card).filter(Card.card_type == card_type).offset(skip).limit(limit).all()
    
    def get_random_by_rarity(self, db: Session, *, rarity: int) -> Optional[Card]:
        """根据稀有度随机获取一张卡片"""
        return db.query(Card).filter(Card.rarity == rarity).order_by(func.random()).first()
    
    def get_all_active(self, db: Session, *, skip: int = 0, limit: int = 100) -> List[Card]:
        """获取所有有效卡片"""
        return db.query(Card).filter(Card.is_active == True).offset(skip).limit(limit).all()


class CRUDUserCard(CRUDBase[UserCard, UserCardCreate, UserCardUpdate]):
    def get_by_user_and_card(
        self, db: Session, *, user_id: int, card_id: int
    ) -> Optional[UserCard]:
        """获取用户特定卡片"""
        return db.query(UserCard).filter(
            UserCard.user_id == user_id,
            UserCard.card_id == card_id
        ).first()
    
    def get_all_by_user(
        self, db: Session, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[UserCard]:
        """获取用户所有卡片"""
        return db.query(UserCard).filter(
            UserCard.user_id == user_id
        ).offset(skip).limit(limit).all()
    
    def get_equipped_cards(self, db: Session, *, user_id: int) -> List[UserCard]:
        """获取用户已装备的卡片"""
        return db.query(UserCard).filter(
            UserCard.user_id == user_id,
            UserCard.is_equipped == True
        ).all()
    
    def add_card_to_user(
        self, db: Session, *, user_id: int, card_id: int, quantity: int = 1
    ) -> UserCard:
        """为用户添加卡片，如果已有则增加数量"""
        user_card = self.get_by_user_and_card(db, user_id=user_id, card_id=card_id)
        
        if user_card:
            user_card.quantity += quantity
        else:
            user_card = UserCard(
                user_id=user_id,
                card_id=card_id,
                quantity=quantity,
                is_equipped=False,
                obtained_at=datetime.now()
            )
        
        db.add(user_card)
        db.commit()
        db.refresh(user_card)
        return user_card
    
    def use_card(
        self, db: Session, *, user_id: int, card_id: int, quantity: int = 1
    ) -> Tuple[bool, Optional[UserCard]]:
        """使用卡片，减少数量"""
        user_card = self.get_by_user_and_card(db, user_id=user_id, card_id=card_id)
        
        if not user_card or user_card.quantity < quantity:
            return False, None
        
        user_card.quantity -= quantity
        
        # 如果数量为0，考虑是否删除记录
        if user_card.quantity <= 0:
            db.delete(user_card)
            db.commit()
            return True, None
        else:
            db.add(user_card)
            db.commit()
            db.refresh(user_card)
            return True, user_card
    
    def toggle_equip_status(
        self, db: Session, *, user_id: int, card_id: int
    ) -> Optional[UserCard]:
        """切换卡片装备状态"""
        user_card = self.get_by_user_and_card(db, user_id=user_id, card_id=card_id)
        
        if not user_card:
            return None
        
        user_card.is_equipped = not user_card.is_equipped
        db.add(user_card)
        db.commit()
        db.refresh(user_card)
        return user_card


class CRUDCardSynthesisRecipe(CRUDBase[CardSynthesisRecipe, CardSynthesisRecipeCreate, CardSynthesisRecipeUpdate]):
    def get_by_result_card(
        self, db: Session, *, result_card_id: int
    ) -> Optional[CardSynthesisRecipe]:
        """通过结果卡片获取合成配方"""
        return db.query(CardSynthesisRecipe).filter(
            CardSynthesisRecipe.result_card_id == result_card_id
        ).first()
    
    def get_all_active(
        self, db: Session, *, skip: int = 0, limit: int = 100
    ) -> List[CardSynthesisRecipe]:
        """获取所有激活的合成配方"""
        return db.query(CardSynthesisRecipe).filter(
            CardSynthesisRecipe.is_active == True
        ).offset(skip).limit(limit).all()
    
    def create_with_ingredients(
        self, db: Session, *, obj_in: CardSynthesisRecipeCreate, 
        ingredients: List[CardSynthesisIngredientCreate]
    ) -> CardSynthesisRecipe:
        """创建合成配方及其原料"""
        db_obj = CardSynthesisRecipe(
            result_card_id=obj_in.result_card_id,
            description=obj_in.description,
            is_active=obj_in.is_active,
            created_at=datetime.now()
        )
        db.add(db_obj)
        db.flush()  # 获取ID但不提交事务
        
        # 添加配方原料
        for ingredient in ingredients:
            db_ingredient = CardSynthesisIngredient(
                recipe_id=db_obj.id,
                card_id=ingredient.card_id,
                quantity=ingredient.quantity
            )
            db.add(db_ingredient)
        
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def get_recipe_with_ingredients(
        self, db: Session, *, recipe_id: int
    ) -> Tuple[Optional[CardSynthesisRecipe], List[CardSynthesisIngredient]]:
        """获取合成配方及其原料"""
        recipe = db.query(CardSynthesisRecipe).filter(CardSynthesisRecipe.id == recipe_id).first()
        
        if not recipe:
            return None, []
        
        ingredients = db.query(CardSynthesisIngredient).filter(
            CardSynthesisIngredient.recipe_id == recipe_id
        ).all()
        
        return recipe, ingredients


card = CRUDCard(Card)
user_card = CRUDUserCard(UserCard)
card_synthesis_recipe = CRUDCardSynthesisRecipe(CardSynthesisRecipe) 