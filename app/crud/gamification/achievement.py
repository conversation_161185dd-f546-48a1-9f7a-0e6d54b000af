from typing import Optional, List, Dict, Any, Union, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import desc, func, and_, or_
from datetime import datetime, date

from app.crud.base import CRUDBase
from app.models.gamification import Achievement, UserAchievement, Milestone, UserMilestone
from app.schemas.gamification import (
    AchievementCreate, AchievementUpdate, UserAchievementCreate, UserAchievementUpdate,
    MilestoneCreate, MilestoneUpdate, UserMilestoneCreate, UserMilestoneUpdate
)


class CRUDAchievement(CRUDBase[Achievement, AchievementCreate, AchievementUpdate]):
    def get_by_name(self, db: Session, *, name: str) -> Optional[Achievement]:
        """通过名称获取成就"""
        return db.query(Achievement).filter(Achievement.name == name).first()
    
    def get_multi_by_category(
        self, db: Session, *, category: str, skip: int = 0, limit: int = 100
    ) -> List[Achievement]:
        """根据类别获取成就列表"""
        return db.query(Achievement).filter(
            Achievement.category == category,
            Achievement.is_active == True
        ).offset(skip).limit(limit).all()
    
    def get_all_active(
        self, db: Session, *, skip: int = 0, limit: int = 100
    ) -> List[Achievement]:
        """获取所有激活的成就"""
        return db.query(Achievement).filter(
            Achievement.is_active == True
        ).offset(skip).limit(limit).all()
    
    def get_visible_achievements(
        self, db: Session, *, skip: int = 0, limit: int = 100
    ) -> List[Achievement]:
        """获取所有非隐藏的激活成就"""
        return db.query(Achievement).filter(
            Achievement.is_active == True,
            Achievement.is_hidden == False
        ).offset(skip).limit(limit).all()


class CRUDUserAchievement(CRUDBase[UserAchievement, UserAchievementCreate, UserAchievementUpdate]):
    def get_by_user_and_achievement(
        self, db: Session, *, user_id: int, achievement_id: int
    ) -> Optional[UserAchievement]:
        """获取用户特定成就的进度记录"""
        return db.query(UserAchievement).filter(
            UserAchievement.user_id == user_id,
            UserAchievement.achievement_id == achievement_id
        ).first()
    
    def get_all_by_user(
        self, db: Session, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[UserAchievement]:
        """获取用户所有成就进度"""
        return db.query(UserAchievement).filter(
            UserAchievement.user_id == user_id
        ).offset(skip).limit(limit).all()
    
    def get_completed_by_user(
        self, db: Session, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[UserAchievement]:
        """获取用户已完成的成就"""
        return db.query(UserAchievement).filter(
            UserAchievement.user_id == user_id,
            UserAchievement.completed == True
        ).offset(skip).limit(limit).all()
    
    def create_with_user(
        self, db: Session, *, user_id: int, achievement_id: int, initial_progress: int = 0
    ) -> UserAchievement:
        """为用户创建成就进度记录"""
        db_obj = UserAchievement(
            user_id=user_id,
            achievement_id=achievement_id,
            progress=initial_progress,
            completed=False,
            reward_claimed=False
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def update_progress(
        self, db: Session, *, user_id: int, achievement_id: int, 
        progress_increment: int = 1
    ) -> Tuple[UserAchievement, bool]:
        """更新用户成就进度，返回（进度记录，是否刚完成）"""
        # 获取成就信息
        achievement = db.query(Achievement).filter(Achievement.id == achievement_id).first()
        if not achievement or not achievement.is_active:
            return None, False
        
        # 获取或创建用户成就进度记录
        user_achievement = self.get_by_user_and_achievement(
            db, user_id=user_id, achievement_id=achievement_id
        )
        if not user_achievement:
            user_achievement = self.create_with_user(
                db, user_id=user_id, achievement_id=achievement_id
            )
        
        # 如果已经完成，不再更新
        if user_achievement.completed:
            return user_achievement, False
        
        # 更新进度
        user_achievement.progress += progress_increment
        
        # 检查是否达成
        just_completed = False
        if user_achievement.progress >= achievement.requirement_value:
            user_achievement.completed = True
            user_achievement.completed_at = datetime.now()
            just_completed = True
        
        db.add(user_achievement)
        db.commit()
        db.refresh(user_achievement)
        
        return user_achievement, just_completed
    
    def claim_reward(
        self, db: Session, *, user_id: int, achievement_id: int
    ) -> Tuple[bool, Optional[UserAchievement]]:
        """领取成就奖励，返回（是否成功，用户成就记录）"""
        user_achievement = self.get_by_user_and_achievement(
            db, user_id=user_id, achievement_id=achievement_id
        )
        
        if not user_achievement or not user_achievement.completed or user_achievement.reward_claimed:
            return False, user_achievement
        
        user_achievement.reward_claimed = True
        db.add(user_achievement)
        db.commit()
        db.refresh(user_achievement)
        
        return True, user_achievement


class CRUDMilestone(CRUDBase[Milestone, MilestoneCreate, MilestoneUpdate]):
    def get_by_name(self, db: Session, *, name: str) -> Optional[Milestone]:
        """通过名称获取里程碑"""
        return db.query(Milestone).filter(Milestone.name == name).first()
    
    def get_all_active(
        self, db: Session, *, skip: int = 0, limit: int = 100
    ) -> List[Milestone]:
        """获取所有激活的里程碑"""
        return db.query(Milestone).filter(
            Milestone.is_active == True
        ).offset(skip).limit(limit).all()


class CRUDUserMilestone(CRUDBase[UserMilestone, UserMilestoneCreate, UserMilestoneUpdate]):
    def get_by_user_and_milestone(
        self, db: Session, *, user_id: int, milestone_id: int
    ) -> Optional[UserMilestone]:
        """获取用户特定里程碑的进度记录"""
        return db.query(UserMilestone).filter(
            UserMilestone.user_id == user_id,
            UserMilestone.milestone_id == milestone_id
        ).first()
    
    def get_all_by_user(
        self, db: Session, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[UserMilestone]:
        """获取用户所有里程碑进度"""
        return db.query(UserMilestone).filter(
            UserMilestone.user_id == user_id
        ).offset(skip).limit(limit).all()
    
    def get_completed_by_user(
        self, db: Session, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[UserMilestone]:
        """获取用户已完成的里程碑"""
        return db.query(UserMilestone).filter(
            UserMilestone.user_id == user_id,
            UserMilestone.completed == True
        ).offset(skip).limit(limit).all()
    
    def create_with_user(
        self, db: Session, *, user_id: int, milestone_id: int, initial_progress: int = 0
    ) -> UserMilestone:
        """为用户创建里程碑进度记录"""
        db_obj = UserMilestone(
            user_id=user_id,
            milestone_id=milestone_id,
            progress=initial_progress,
            last_active_date=datetime.now().date(),
            completed=False,
            reward_claimed=False
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def update_active_date(
        self, db: Session, *, user_id: int, milestone_id: int
    ) -> Tuple[UserMilestone, bool]:
        """更新里程碑活跃日期，检查是否达成，返回（进度记录，是否刚完成）"""
        # 获取里程碑信息
        milestone = db.query(Milestone).filter(Milestone.id == milestone_id).first()
        if not milestone or not milestone.is_active:
            return None, False
        
        # 获取或创建用户里程碑进度记录
        user_milestone = self.get_by_user_and_milestone(
            db, user_id=user_id, milestone_id=milestone_id
        )
        if not user_milestone:
            user_milestone = self.create_with_user(
                db, user_id=user_id, milestone_id=milestone_id
            )
        
        # 如果已经完成，不再更新
        if user_milestone.completed:
            return user_milestone, False
        
        today = datetime.now().date()
        last_active = user_milestone.last_active_date
        
        # 只有当日期不同时才更新
        if today != last_active:
            user_milestone.last_active_date = today
            user_milestone.progress += 1
        
        # 检查是否达成
        just_completed = False
        if user_milestone.progress >= milestone.days_required:
            user_milestone.completed = True
            user_milestone.completed_at = datetime.now()
            just_completed = True
        
        db.add(user_milestone)
        db.commit()
        db.refresh(user_milestone)
        
        return user_milestone, just_completed
    
    def claim_reward(
        self, db: Session, *, user_id: int, milestone_id: int
    ) -> Tuple[bool, Optional[UserMilestone]]:
        """领取里程碑奖励，返回（是否成功，用户里程碑记录）"""
        user_milestone = self.get_by_user_and_milestone(
            db, user_id=user_id, milestone_id=milestone_id
        )
        
        if not user_milestone or not user_milestone.completed or user_milestone.reward_claimed:
            return False, user_milestone
        
        user_milestone.reward_claimed = True
        db.add(user_milestone)
        db.commit()
        db.refresh(user_milestone)
        
        return True, user_milestone


achievement = CRUDAchievement(Achievement)
user_achievement = CRUDUserAchievement(UserAchievement)
milestone = CRUDMilestone(Milestone)
user_milestone = CRUDUserMilestone(UserMilestone) 