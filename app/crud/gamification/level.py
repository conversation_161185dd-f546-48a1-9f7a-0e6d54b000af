from typing import Optional, List, Dict, Any, Union
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_
from datetime import datetime

from app.crud.base import CRUDBase
from app.models.gamification import UserLevel, UserAttribute, UserTitle
from app.schemas.gamification import (
    UserLevelCreate, UserLevelUpdate, UserAttributeCreate, 
    UserAttributeUpdate, UserTitleCreate, UserTitleUpdate
)


class CRUDUserLevel(CRUDBase[UserLevel, UserLevelCreate, UserLevelUpdate]):
    def get_by_user_id(self, db: Session, *, user_id: int) -> Optional[UserLevel]:
        """根据用户ID获取用户等级信息"""
        return db.query(UserLevel).filter(UserLevel.user_id == user_id).first()
    
    def create_with_user(self, db: Session, *, obj_in: UserLevelCreate, user_id: int) -> UserLevel:
        """创建用户等级，关联用户ID"""
        db_obj = UserLevel(
            user_id=user_id,
            exercise_level=obj_in.exercise_level,
            exercise_experience=obj_in.exercise_experience,
            diet_level=obj_in.diet_level,
            diet_experience=obj_in.diet_experience,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def add_exercise_experience(
        self, db: Session, *, user_id: int, experience: int
    ) -> Optional[UserLevel]:
        """增加用户运动经验值"""
        user_level = self.get_by_user_id(db, user_id=user_id)
        if not user_level:
            return None
        
        user_level.exercise_experience += experience
        user_level.updated_at = datetime.now()
        
        # 简单的等级提升逻辑 - 每100经验值提升1级
        new_level = user_level.exercise_experience // 100 + 1
        if new_level > user_level.exercise_level:
            user_level.exercise_level = new_level
        
        db.add(user_level)
        db.commit()
        db.refresh(user_level)
        return user_level
    
    def add_diet_experience(
        self, db: Session, *, user_id: int, experience: int
    ) -> Optional[UserLevel]:
        """增加用户饮食经验值"""
        user_level = self.get_by_user_id(db, user_id=user_id)
        if not user_level:
            return None
        
        user_level.diet_experience += experience
        user_level.updated_at = datetime.now()
        
        # 简单的等级提升逻辑 - 每100经验值提升1级
        new_level = user_level.diet_experience // 100 + 1
        if new_level > user_level.diet_level:
            user_level.diet_level = new_level
        
        db.add(user_level)
        db.commit()
        db.refresh(user_level)
        return user_level


class CRUDUserAttribute(CRUDBase[UserAttribute, UserAttributeCreate, UserAttributeUpdate]):
    def get_by_user_id(self, db: Session, *, user_id: int) -> Optional[UserAttribute]:
        """根据用户ID获取用户属性信息"""
        return db.query(UserAttribute).filter(UserAttribute.user_id == user_id).first()
    
    def create_with_user(
        self, db: Session, *, obj_in: UserAttributeCreate, user_id: int
    ) -> UserAttribute:
        """创建用户属性，关联用户ID"""
        db_obj = UserAttribute(
            user_id=user_id,
            strength=obj_in.strength,
            endurance=obj_in.endurance,
            flexibility=obj_in.flexibility,
            nutrition_knowledge=obj_in.nutrition_knowledge,
            cooking_skill=obj_in.cooking_skill,
            diet_planning=obj_in.diet_planning,
            updated_at=datetime.now()
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def update_attributes(
        self, db: Session, *, user_id: int, attributes: Dict[str, int]
    ) -> Optional[UserAttribute]:
        """更新用户属性值"""
        user_attribute = self.get_by_user_id(db, user_id=user_id)
        if not user_attribute:
            return None
        
        for attr_name, value in attributes.items():
            if hasattr(user_attribute, attr_name):
                current_value = getattr(user_attribute, attr_name)
                setattr(user_attribute, attr_name, current_value + value)
        
        user_attribute.updated_at = datetime.now()
        db.add(user_attribute)
        db.commit()
        db.refresh(user_attribute)
        return user_attribute


class CRUDUserTitle(CRUDBase[UserTitle, UserTitleCreate, UserTitleUpdate]):
    def get_by_user_id(self, db: Session, *, user_id: int) -> List[UserTitle]:
        """获取用户所有称号"""
        return db.query(UserTitle).filter(UserTitle.user_id == user_id).all()
    
    def get_active_title(self, db: Session, *, user_id: int) -> Optional[UserTitle]:
        """获取用户当前激活的称号"""
        return db.query(UserTitle).filter(
            UserTitle.user_id == user_id,
            UserTitle.is_active == True
        ).first()
    
    def create_with_user(
        self, db: Session, *, obj_in: UserTitleCreate, user_id: int, level_id: int
    ) -> UserTitle:
        """创建用户称号"""
        db_obj = UserTitle(
            user_id=user_id,
            level_id=level_id,
            title_name=obj_in.title_name,
            title_type=obj_in.title_type,
            obtained_at=datetime.now(),
            is_active=obj_in.is_active
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def set_active_title(self, db: Session, *, user_id: int, title_id: int) -> Optional[UserTitle]:
        """设置用户当前激活的称号"""
        # 首先将所有称号设为非激活
        db.query(UserTitle).filter(
            UserTitle.user_id == user_id,
            UserTitle.is_active == True
        ).update({"is_active": False})
        
        # 然后激活指定称号
        title = db.query(UserTitle).filter(
            UserTitle.id == title_id,
            UserTitle.user_id == user_id
        ).first()
        
        if title:
            title.is_active = True
            db.add(title)
            db.commit()
            db.refresh(title)
        
        return title


user_level = CRUDUserLevel(UserLevel)
user_attribute = CRUDUserAttribute(UserAttribute)
user_title = CRUDUserTitle(UserTitle) 