from typing import Optional, List, Dict, Any, Union, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import desc, func, and_, or_
from datetime import datetime, date, timedelta

from app.crud.base import CRUDBase
from app.models.gamification import Task, UserTask, DailyCheckIn
from app.schemas.gamification import (
    TaskCreate, TaskUpdate, UserTaskCreate, UserTaskUpdate,
    DailyCheckInCreate, DailyCheckInUpdate
)


class CRUDTask(CRUDBase[Task, TaskCreate, TaskUpdate]):
    def get_by_name(self, db: Session, *, name: str) -> Optional[Task]:
        """通过名称获取任务"""
        return db.query(Task).filter(Task.name == name).first()
    
    def get_multi_by_type(
        self, db: Session, *, task_type: str, skip: int = 0, limit: int = 100
    ) -> List[Task]:
        """根据任务类型获取任务列表"""
        return db.query(Task).filter(
            Task.task_type == task_type,
            Task.is_active == True
        ).offset(skip).limit(limit).all()
    
    def get_multi_by_category(
        self, db: Session, *, category: str, skip: int = 0, limit: int = 100
    ) -> List[Task]:
        """根据任务类别获取任务列表"""
        return db.query(Task).filter(
            Task.category == category,
            Task.is_active == True
        ).offset(skip).limit(limit).all()
    
    def get_available_for_user(
        self, db: Session, *, user_id: int, level_type: str, level: int,
        skip: int = 0, limit: int = 100
    ) -> List[Task]:
        """获取用户可用的任务（基于等级）"""
        return db.query(Task).filter(
            Task.is_active == True,
            or_(
                Task.min_level_required.is_(None),
                and_(
                    Task.level_type == level_type,
                    Task.min_level_required <= level
                )
            )
        ).offset(skip).limit(limit).all()


class CRUDUserTask(CRUDBase[UserTask, UserTaskCreate, UserTaskUpdate]):
    def get_by_user_and_task(
        self, db: Session, *, user_id: int, task_id: int
    ) -> Optional[UserTask]:
        """获取用户特定任务的进度记录"""
        return db.query(UserTask).filter(
            UserTask.user_id == user_id,
            UserTask.task_id == task_id
        ).first()
    
    def get_active_by_user(
        self, db: Session, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[UserTask]:
        """获取用户所有活跃任务（未完成且未过期）"""
        now = datetime.now()
        return db.query(UserTask).filter(
            UserTask.user_id == user_id,
            UserTask.completed == False,
            UserTask.expires_at > now
        ).offset(skip).limit(limit).all()
    
    def get_completed_by_user(
        self, db: Session, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[UserTask]:
        """获取用户已完成的任务"""
        return db.query(UserTask).filter(
            UserTask.user_id == user_id,
            UserTask.completed == True
        ).offset(skip).limit(limit).all()
    
    def create_with_user(
        self, db: Session, *, user_id: int, task_id: int, 
        initial_progress: int = 0, expires_after_days: int = None
    ) -> UserTask:
        """为用户创建任务进度记录"""
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task:
            return None
        
        # 设置过期时间
        expires_at = None
        if task.task_type == "DAILY":
            expires_at = datetime.now() + timedelta(days=1)
        elif task.task_type == "WEEKLY":
            expires_at = datetime.now() + timedelta(days=7)
        elif expires_after_days:
            expires_at = datetime.now() + timedelta(days=expires_after_days)
        
        db_obj = UserTask(
            user_id=user_id,
            task_id=task_id,
            progress=initial_progress,
            completed=False,
            created_at=datetime.now(),
            expires_at=expires_at,
            reward_claimed=False
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def update_progress(
        self, db: Session, *, user_id: int, task_id: int, 
        progress_increment: int = 1
    ) -> Tuple[UserTask, bool]:
        """更新用户任务进度，返回（进度记录，是否刚完成）"""
        # 获取任务信息
        task = db.query(Task).filter(Task.id == task_id).first()
        if not task or not task.is_active:
            return None, False
        
        # 获取或创建用户任务进度记录
        user_task = self.get_by_user_and_task(
            db, user_id=user_id, task_id=task_id
        )
        if not user_task:
            user_task = self.create_with_user(
                db, user_id=user_id, task_id=task_id
            )
        
        # 如果已经完成或过期，不再更新
        now = datetime.now()
        if user_task.completed or (user_task.expires_at and user_task.expires_at < now):
            return user_task, False
        
        # 更新进度
        user_task.progress += progress_increment
        
        # 检查是否达成
        just_completed = False
        if user_task.progress >= task.requirement_value:
            user_task.completed = True
            user_task.completed_at = now
            just_completed = True
        
        db.add(user_task)
        db.commit()
        db.refresh(user_task)
        
        return user_task, just_completed
    
    def claim_reward(
        self, db: Session, *, user_id: int, task_id: int
    ) -> Tuple[bool, Optional[UserTask]]:
        """领取任务奖励，返回（是否成功，用户任务记录）"""
        user_task = self.get_by_user_and_task(
            db, user_id=user_id, task_id=task_id
        )
        
        if not user_task or not user_task.completed or user_task.reward_claimed:
            return False, user_task
        
        user_task.reward_claimed = True
        db.add(user_task)
        db.commit()
        db.refresh(user_task)
        
        return True, user_task
    
    def generate_daily_tasks(
        self, db: Session, *, user_id: int, user_level_type: str, user_level: int
    ) -> List[UserTask]:
        """为用户生成每日任务"""
        # 清理之前未完成的过期任务
        now = datetime.now()
        expired_tasks = db.query(UserTask).filter(
            UserTask.user_id == user_id,
            UserTask.completed == False,
            UserTask.expires_at < now
        ).all()
        
        for task in expired_tasks:
            db.delete(task)
        
        # 获取可用的每日任务
        daily_tasks = db.query(Task).filter(
            Task.task_type == "DAILY",
            Task.is_active == True,
            or_(
                Task.min_level_required.is_(None),
                and_(
                    Task.level_type == user_level_type,
                    Task.min_level_required <= user_level
                )
            )
        ).all()
        
        # 创建每日任务进度记录
        user_tasks = []
        for task in daily_tasks:
            # 检查用户是否已有此任务
            existing = self.get_by_user_and_task(db, user_id=user_id, task_id=task.id)
            if existing and existing.expires_at > now:
                user_tasks.append(existing)
                continue
            
            # 创建新任务
            user_task = UserTask(
                user_id=user_id,
                task_id=task.id,
                progress=0,
                completed=False,
                created_at=now,
                expires_at=now + timedelta(days=1),
                reward_claimed=False
            )
            db.add(user_task)
            user_tasks.append(user_task)
        
        db.commit()
        for task in user_tasks:
            db.refresh(task)
        
        return user_tasks
    
    def generate_weekly_tasks(
        self, db: Session, *, user_id: int, user_level_type: str, user_level: int
    ) -> List[UserTask]:
        """为用户生成每周任务"""
        # 清理之前未完成的过期任务
        now = datetime.now()
        expired_tasks = db.query(UserTask).filter(
            UserTask.user_id == user_id,
            UserTask.completed == False,
            UserTask.expires_at < now
        ).all()
        
        for task in expired_tasks:
            db.delete(task)
        
        # 获取可用的每周任务
        weekly_tasks = db.query(Task).filter(
            Task.task_type == "WEEKLY",
            Task.is_active == True,
            or_(
                Task.min_level_required.is_(None),
                and_(
                    Task.level_type == user_level_type,
                    Task.min_level_required <= user_level
                )
            )
        ).all()
        
        # 创建每周任务进度记录
        user_tasks = []
        for task in weekly_tasks:
            # 检查用户是否已有此任务
            existing = self.get_by_user_and_task(db, user_id=user_id, task_id=task.id)
            if existing and existing.expires_at > now:
                user_tasks.append(existing)
                continue
            
            # 创建新任务
            user_task = UserTask(
                user_id=user_id,
                task_id=task.id,
                progress=0,
                completed=False,
                created_at=now,
                expires_at=now + timedelta(days=7),
                reward_claimed=False
            )
            db.add(user_task)
            user_tasks.append(user_task)
        
        db.commit()
        for task in user_tasks:
            db.refresh(task)
        
        return user_tasks


class CRUDDailyCheckIn(CRUDBase[DailyCheckIn, DailyCheckInCreate, DailyCheckInUpdate]):
    def get_todays_checkin(self, db: Session, *, user_id: int) -> Optional[DailyCheckIn]:
        """获取用户今日的签到记录"""
        today = datetime.now().date()
        return db.query(DailyCheckIn).filter(
            DailyCheckIn.user_id == user_id,
            func.date(DailyCheckIn.checkin_date) == today
        ).first()
    
    def get_user_checkins(
        self, db: Session, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[DailyCheckIn]:
        """获取用户所有签到记录"""
        return db.query(DailyCheckIn).filter(
            DailyCheckIn.user_id == user_id
        ).order_by(desc(DailyCheckIn.checkin_date)).offset(skip).limit(limit).all()
    
    def create_checkin(
        self, db: Session, *, user_id: int,
        currency_reward: int = 10,
        exercise_exp_reward: int = 5,
        diet_exp_reward: int = 5
    ) -> Tuple[DailyCheckIn, bool]:
        """创建签到记录，返回（签到记录，是否连续签到）"""
        # 检查今日是否已签到
        todays_checkin = self.get_todays_checkin(db, user_id=user_id)
        if todays_checkin:
            return todays_checkin, False
        
        # 检查是否有昨日签到记录，计算连续签到天数
        yesterday = datetime.now().date() - timedelta(days=1)
        yesterday_checkin = db.query(DailyCheckIn).filter(
            DailyCheckIn.user_id == user_id,
            func.date(DailyCheckIn.checkin_date) == yesterday
        ).first()
        
        streak_count = 1  # 默认从1开始
        if yesterday_checkin:
            streak_count = yesterday_checkin.streak_count + 1
        
        # 创建今日签到记录
        checkin = DailyCheckIn(
            user_id=user_id,
            checkin_date=datetime.now(),
            streak_count=streak_count,
            currency_reward=currency_reward,
            experience_reward_exercise=exercise_exp_reward,
            experience_reward_diet=diet_exp_reward
        )
        db.add(checkin)
        db.commit()
        db.refresh(checkin)
        
        return checkin, streak_count > 1


task = CRUDTask(Task)
user_task = CRUDUserTask(UserTask)
daily_checkin = CRUDDailyCheckIn(DailyCheckIn) 