from typing import List, Optional, Dict, Any, Union

from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.set_record import SetRecord
from app.schemas.set_record import SetRecordCreate, SetRecordUpdate


class CRUDSetRecord(CRUDBase[SetRecord, SetRecordCreate, SetRecordUpdate]):
    """组记录的CRUD操作"""

    def get_by_workout_exercise(
        self, db: Session, *, workout_exercise_id: int
    ) -> List[SetRecord]:
        """获取训练动作的所有组记录

        Args:
            db: 数据库会话
            workout_exercise_id: 训练动作ID

        Returns:
            组记录列表
        """
        return db.query(self.model).filter(
            SetRecord.workout_exercise_id == workout_exercise_id
        ).order_by(SetRecord.set_number).all()

    def create_with_workout_exercise(
        self, db: Session, *, obj_in: SetRecordCreate, workout_exercise_id: int
    ) -> SetRecord:
        """创建组记录

        Args:
            db: 数据库会话
            obj_in: 组记录创建数据
            workout_exercise_id: 训练动作ID

        Returns:
            创建的组记录
        """
        db_obj = SetRecord(
            workout_exercise_id=workout_exercise_id,
            set_number=obj_in.set_number,
            set_type=obj_in.set_type,
            weight=obj_in.weight,
            reps=obj_in.reps,
            completed=obj_in.completed,
            notes=obj_in.notes
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update_set_completion(
        self, db: Session, *, id: int, completed: bool, weight: Optional[float] = None, reps: Optional[int] = None
    ) -> SetRecord:
        """更新组完成状态

        Args:
            db: 数据库会话
            id: 组记录ID
            completed: 是否完成
            weight: 重量
            reps: 重复次数

        Returns:
            更新后的组记录
        """
        db_obj = db.query(self.model).filter(SetRecord.id == id).first()
        if not db_obj:
            return None
        
        update_data = {"completed": completed}
        if weight is not None:
            update_data["weight"] = weight
        if reps is not None:
            update_data["reps"] = reps
            
        return super().update(db, db_obj=db_obj, obj_in=update_data)


# 创建实例
set_record = CRUDSetRecord(SetRecord)
