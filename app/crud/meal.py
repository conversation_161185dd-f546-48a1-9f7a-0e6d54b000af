from typing import List, Dict, Any, Optional, Union
from datetime import date, timedelta

from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.meal import MealRecord, FoodItem, FoodItemNutrientIntake, HealthRecommendation, FoodRecognition
from app.schemas.meal import MealRecordCreate, MealRecordUpdate


class CRUDMealRecord(CRUDBase[MealRecord, MealRecordCreate, MealRecordUpdate]):
    def create_with_user_id(
        self, db: Session, *, obj_in: MealRecordCreate, user_id: str
    ) -> MealRecord:
        obj_in_data = obj_in.dict()
        db_obj = MealRecord(**obj_in_data, user_id=user_id)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def get_user_meals(
        self, db: Session, *, user_id: str, 
        start_date: date, end_date: date = None,
        skip: int = 0, limit: int = 100
    ) -> List[MealRecord]:
        """获取用户在指定日期范围内的餐食记录"""
        # 如果end_date未指定，默认为start_date
        if not end_date:
            end_date = start_date
            
        return (
            db.query(MealRecord)
            .filter(
                MealRecord.user_id == user_id,
                MealRecord.date >= start_date,
                MealRecord.date <= end_date
            )
            .order_by(MealRecord.date.desc(), MealRecord.meal_type)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_user_meals_by_date(
        self, db: Session, *, user_id: str, date: date
    ) -> List[MealRecord]:
        """获取用户某一天的所有餐食记录"""
        return (
            db.query(MealRecord)
            .filter(
                MealRecord.user_id == user_id,
                MealRecord.date == date
            )
            .order_by(MealRecord.meal_type)
            .all()
        )
    
    def get_recent_meals(
        self, db: Session, *, user_id: str, days: int = 7
    ) -> List[MealRecord]:
        """获取用户最近几天的餐食记录"""
        start_date = date.today() - timedelta(days=days)
        return self.get_user_meals(
            db=db, 
            user_id=user_id, 
            start_date=start_date, 
            end_date=date.today()
        )


class CRUDFoodItem(CRUDBase[FoodItem, Dict[str, Any], Dict[str, Any]]):
    def get_by_meal(
        self, db: Session, *, meal_record_id: int
    ) -> List[FoodItem]:
        """获取餐食记录中的所有食物项"""
        return (
            db.query(FoodItem)
            .filter(FoodItem.meal_record_id == meal_record_id)
            .all()
        )
    
    def create_with_meal(
        self, db: Session, *, obj_in: Dict[str, Any], meal_record_id: int
    ) -> FoodItem:
        """创建餐食记录关联的食物项"""
        obj_in_data = dict(obj_in)
        obj_in_data["meal_record_id"] = meal_record_id
        db_obj = FoodItem(**obj_in_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj


class CRUDFoodItemNutrientIntake(CRUDBase[FoodItemNutrientIntake, Dict[str, Any], Dict[str, Any]]):
    def get_by_food_item(
        self, db: Session, *, food_item_id: int
    ) -> List[FoodItemNutrientIntake]:
        """获取食物项关联的所有营养素摄入记录"""
        return (
            db.query(FoodItemNutrientIntake)
            .filter(FoodItemNutrientIntake.food_item_id == food_item_id)
            .all()
        )
    
    def create_with_food_item(
        self, db: Session, *, obj_in: Dict[str, Any], food_item_id: int
    ) -> FoodItemNutrientIntake:
        """创建食物项关联的营养素摄入记录"""
        obj_in_data = dict(obj_in)
        obj_in_data["food_item_id"] = food_item_id
        db_obj = FoodItemNutrientIntake(**obj_in_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj


class CRUDHealthRecommendation(CRUDBase[HealthRecommendation, Dict[str, Any], Dict[str, Any]]):
    def get_by_meal_record(
        self, db: Session, *, meal_record_id: int
    ) -> List[HealthRecommendation]:
        """获取餐食记录关联的所有健康建议"""
        return (
            db.query(HealthRecommendation)
            .filter(HealthRecommendation.meal_record_id == meal_record_id)
            .order_by(HealthRecommendation.priority.desc())
            .all()
        )
    
    def create_with_meal_record(
        self, db: Session, *, obj_in: Dict[str, Any], meal_record_id: int
    ) -> HealthRecommendation:
        """创建餐食记录关联的健康建议"""
        obj_in_data = dict(obj_in)
        obj_in_data["meal_record_id"] = meal_record_id
        db_obj = HealthRecommendation(**obj_in_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj


class CRUDFoodRecognition(CRUDBase[FoodRecognition, Dict[str, Any], Dict[str, Any]]):
    def get_by_user(
        self, db: Session, *, user_id: str, limit: int = 10
    ) -> List[FoodRecognition]:
        """获取用户的食物识别记录"""
        return (
            db.query(FoodRecognition)
            .filter(FoodRecognition.user_id == user_id)
            .order_by(FoodRecognition.created_at.desc())
            .limit(limit)
            .all()
        )
    
    def get_pending(
        self, db: Session, *, user_id: str
    ) -> List[FoodRecognition]:
        """获取用户待确认的食物识别记录"""
        return (
            db.query(FoodRecognition)
            .filter(
                FoodRecognition.user_id == user_id,
                FoodRecognition.status == "completed",
                FoodRecognition.meal_record_id.is_(None)
            )
            .order_by(FoodRecognition.created_at.desc())
            .all()
        ) 