"""
V2 API适配器

提供新旧API数据格式的转换函数，确保重构后的AI助手系统与原有API完全兼容
"""

from typing import Any, Dict, List, Optional
import logging

from app.models.message import MessageRole

logger = logging.getLogger(__name__)


async def adapt_conversation_response(response: Dict[str, Any]) -> Dict[str, Any]:
    """
    将新系统的响应适配为旧格式
    
    Args:
        response: 新系统的响应数据
        
    Returns:
        适配后的旧格式响应数据
    """
    # 从新格式中提取信息
    meta_info = {
        "intent_type": response.get("intent"),
        "intent": response.get("intent"),  # 兼容性
        "confidence": response.get("confidence"),
        "end_conversation": response.get("end_conversation", False),
        "next_intent": response.get("next_intent"),
        "missing_parameters": response.get("missing_parameters"),
        "current_state": response.get("current_state"),
        "next_state": response.get("next_state"),
        "processing_time_ms": response.get("processing_time_ms"),
        "timestamp": response.get("timestamp")
    }
    
    # 如果有错误信息，添加到meta_info
    if "error" in response and response["error"]:
        meta_info["error"] = response["error"]
    
    # 如果有训练计划数据，添加到meta_info
    if "training_plan_data" in response:
        meta_info["training_plan_data"] = response["training_plan_data"]
    
    # 如果有用户信息更新，添加到meta_info
    if "updated_field" in response:
        meta_info["updated_field"] = response["updated_field"]
        meta_info["value"] = response.get("value")
    
    return {
        "session_id": response.get("conversation_id"),
        "response": response.get("response_content", response.get("response", "")),
        "meta_info": meta_info,
        "success": response.get("success", True)
    }


def adapt_message_format(message: Dict[str, Any]) -> Dict[str, Any]:
    """
    适配消息格式，确保新旧API数据结构兼容
    
    Args:
        message: 原始消息数据
        
    Returns:
        适配后的消息数据
    """
    # 确保meta_info是字典而不是None
    if message.get("meta_info") is None:
        message["meta_info"] = {}
    
    # 处理角色映射，确保兼容性
    role = message.get("role", "")
    if isinstance(role, str) and not role:
        if message.get("is_user", False):
            message["role"] = MessageRole.USER
        else:
            message["role"] = MessageRole.ASSISTANT
    
    # 确保时间戳格式正确
    if "created_at" in message and message["created_at"]:
        # 如果是datetime对象，转换为ISO格式字符串
        created_at = message["created_at"]
        if hasattr(created_at, 'isoformat'):
            message["created_at"] = created_at.isoformat()
    
    # 确保ID字段存在
    if "id" not in message and "message_id" in message:
        message["id"] = message["message_id"]
    
    return message


def adapt_conversation_history(history: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    适配对话历史格式
    
    Args:
        history: 原始对话历史
        
    Returns:
        适配后的对话历史
    """
    return [adapt_message_format(msg) for msg in history]


def adapt_user_info_request(request_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    适配用户信息更新请求格式
    
    Args:
        request_data: 原始请求数据
        
    Returns:
        适配后的请求数据
    """
    adapted = {
        "session_id": request_data.get("session_id"),
        "field": request_data.get("field"),
        "value": request_data.get("value"),
        "value_text": request_data.get("value_text", str(request_data.get("value", "")))
    }
    
    return adapted


def adapt_training_plan_request(request_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    适配训练计划生成请求格式
    
    Args:
        request_data: 原始请求数据
        
    Returns:
        适配后的请求数据
    """
    adapted = {
        "session_id": request_data.get("session_id"),
        "plan_type": request_data.get("plan_type", "single_day"),
        "body_part": request_data.get("body_part"),
        "training_scene": request_data.get("training_scene", "gym"),
        "duration_weeks": request_data.get("duration_weeks", 4),
        "days_per_week": request_data.get("days_per_week", 3),
        "available_time": request_data.get("available_time", 60),
        "additional_notes": request_data.get("additional_notes")
    }
    
    return adapted


def adapt_websocket_message(message: Dict[str, Any]) -> Dict[str, Any]:
    """
    适配WebSocket消息格式
    
    Args:
        message: 原始WebSocket消息
        
    Returns:
        适配后的WebSocket消息
    """
    # 确保事件类型存在
    if "event" not in message:
        if "type" in message:
            message["event"] = message["type"]
        else:
            message["event"] = "message"
    
    # 适配数据格式
    if "data" in message and isinstance(message["data"], dict):
        message["data"] = adapt_message_format(message["data"])
    
    return message


def adapt_error_response(error: Exception, session_id: Optional[str] = None) -> Dict[str, Any]:
    """
    适配错误响应格式
    
    Args:
        error: 异常对象
        session_id: 会话ID
        
    Returns:
        适配后的错误响应
    """
    return {
        "success": False,
        "error": str(error),
        "response": f"抱歉，处理请求时出现错误：{str(error)}",
        "session_id": session_id,
        "conversation_id": session_id,
        "meta_info": {
            "error": str(error),
            "error_type": type(error).__name__
        }
    }


def adapt_stream_chunk(chunk: Any) -> Dict[str, Any]:
    """
    适配流式响应块格式
    
    Args:
        chunk: 原始响应块
        
    Returns:
        适配后的响应块
    """
    if isinstance(chunk, dict):
        # 如果已经是字典格式，直接返回
        return adapt_websocket_message(chunk)
    else:
        # 如果是文本，包装为标准格式
        return {
            "event": "chunk",
            "data": {"text": str(chunk)}
        }


def adapt_conversation_list(conversations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    适配会话列表格式
    
    Args:
        conversations: 原始会话列表
        
    Returns:
        适配后的会话列表
    """
    adapted_conversations = []
    
    for conv in conversations:
        adapted_conv = {
            "id": conv.get("id"),
            "session_id": conv.get("session_id"),
            "user_id": conv.get("user_id"),
            "created_at": conv.get("created_at"),
            "last_active_at": conv.get("last_active_at"),
            "is_active": conv.get("is_active", True),
            "system_prompt": conv.get("system_prompt")
        }
        
        # 处理时间戳格式
        for time_field in ["created_at", "last_active_at"]:
            if time_field in adapted_conv and adapted_conv[time_field]:
                time_value = adapted_conv[time_field]
                if hasattr(time_value, 'isoformat'):
                    adapted_conv[time_field] = time_value.isoformat()
        
        adapted_conversations.append(adapted_conv)
    
    return adapted_conversations


def adapt_intent_result(intent_result: Dict[str, Any]) -> Dict[str, Any]:
    """
    适配意图识别结果格式
    
    Args:
        intent_result: 原始意图识别结果
        
    Returns:
        适配后的意图识别结果
    """
    return {
        "intent_type": intent_result.get("intent"),
        "intent": intent_result.get("intent"),  # 兼容性
        "confidence": intent_result.get("confidence", 0.0),
        "parameters": intent_result.get("parameters", {}),
        "entities": intent_result.get("entities", [])
    }


def adapt_user_profile(user: Any) -> Dict[str, Any]:
    """
    适配用户资料格式
    
    Args:
        user: 用户对象
        
    Returns:
        适配后的用户资料
    """
    if hasattr(user, '__dict__'):
        user_dict = user.__dict__
    else:
        user_dict = user
    
    # 基本字段映射
    adapted_profile = {
        "id": user_dict.get("id"),
        "nickname": user_dict.get("nickname"),
        "avatar_url": user_dict.get("avatar_url"),
        "phone": user_dict.get("phone"),
        "gender": user_dict.get("gender"),
        "age": user_dict.get("age"),
        "height": user_dict.get("height"),
        "weight": user_dict.get("weight"),
        "fitness_goal": user_dict.get("fitness_goal"),
        "experience_level": user_dict.get("experience_level"),
        "activity_level": user_dict.get("activity_level"),
        "bmi": user_dict.get("bmi"),
        "created_at": user_dict.get("created_at"),
        "is_active": user_dict.get("is_active", True)
    }
    
    # 处理时间戳
    if adapted_profile["created_at"] and hasattr(adapted_profile["created_at"], 'isoformat'):
        adapted_profile["created_at"] = adapted_profile["created_at"].isoformat()
    
    return adapted_profile


def validate_session_id(session_id: Optional[str]) -> str:
    """
    验证并标准化会话ID
    
    Args:
        session_id: 原始会话ID
        
    Returns:
        标准化的会话ID
    """
    if not session_id or not isinstance(session_id, str):
        import uuid
        return str(uuid.uuid4())
    
    return session_id.strip()


def extract_message_content(data: Dict[str, Any]) -> str:
    """
    从请求数据中提取消息内容
    
    Args:
        data: 请求数据
        
    Returns:
        消息内容
    """
    # 尝试多种可能的字段名
    for field in ["message", "content", "text", "input"]:
        if field in data and data[field]:
            return str(data[field]).strip()
    
    return ""


def build_meta_info(
    intent: Optional[str] = None,
    confidence: Optional[float] = None,
    **kwargs
) -> Dict[str, Any]:
    """
    构建标准化的元数据信息
    
    Args:
        intent: 意图类型
        confidence: 置信度
        **kwargs: 其他元数据
        
    Returns:
        标准化的元数据
    """
    meta_info = {
        "intent": intent,
        "intent_type": intent,  # 兼容性
        "confidence": confidence
    }
    
    # 添加其他元数据
    meta_info.update(kwargs)
    
    # 移除None值
    return {k: v for k, v in meta_info.items() if v is not None} 