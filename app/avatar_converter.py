import os
import base64
import hashlib
import uuid
import logging
from datetime import datetime
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import sys

# Add the project root to the Python path so we can import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import necessary modules from the app
from app.core.config import settings
from app.db.session import engine, SessionLocal
from app.models.user import User
from app.utils.secure_path import generate_user_secure_path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("avatar-converter")

def convert_base64_to_file(user: User) -> bool:
    """
    将用户头像从base64格式转换为文件
    如果成功转换则返回True，否则返回False
    """
    try:
        # 检查avatar_url是否为base64格式
        if not user.avatar_url or not user.avatar_url.startswith('data:image/'):
            return False

        # 确保目录存在
        base_dir = "/data/users"
        secure_path = generate_user_secure_path(user.id)
        user_dir = f"{base_dir}/{secure_path}"
        avatar_dir = f"{user_dir}/avatar"
        
        # 创建必要的目录结构
        for dir_path in [base_dir, user_dir, avatar_dir]:
            os.makedirs(dir_path, exist_ok=True)
            logger.info(f"确保目录存在: {dir_path}")
            
            # 确保目录有正确的权限
            os.chmod(dir_path, 0o755)  # rwxr-xr-x

        # 解析base64数据
        metadata, base64_data = user.avatar_url.split(',', 1)
        content_type = metadata.split(';')[0].replace('data:', '')
        
        # 确定文件扩展名
        if content_type == 'image/jpeg':
            file_ext = '.jpg'
        elif content_type == 'image/png':
            file_ext = '.png'
        elif content_type == 'image/gif':
            file_ext = '.gif'
        else:
            file_ext = '.jpg'
        
        # 生成带时间戳的文件名
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        filename = f"avatar_{timestamp}{file_ext}"
        file_path = os.path.join(avatar_dir, filename)
        
        # 解码并保存图像数据
        try:
            image_data = base64.b64decode(base64_data)
            with open(file_path, "wb") as f:
                f.write(image_data)
                
            # 确保文件有正确的权限
            os.chmod(file_path, 0o644)  # rw-r--r--
            
            logger.info(f"保存用户 {user.id} 的头像到文件: {file_path}")
        except Exception as e:
            logger.error(f"Base64解码或保存失败: {str(e)}")
            return False
        
        # 生成新的URL
        new_avatar_url = f"/api/v1/user/avatar/image/{secure_path}/{filename}"
        logger.info(f"用户 {user.id} 的新头像URL: {new_avatar_url}")
        
        return new_avatar_url
        
    except Exception as e:
        logger.error(f"处理用户 {user.id} 头像时出错: {str(e)}")
        return False

def update_user_avatar_url(db, user_id: int, new_avatar_url: str) -> bool:
    """使用SQL更新用户头像URL"""
    try:
        update_sql = "UPDATE users SET avatar_url = :avatar_url, updated_at = :updated_at WHERE id = :user_id"
        db.execute(text(update_sql), {
            "avatar_url": new_avatar_url,
            "updated_at": datetime.now(),
            "user_id": user_id
        })
        db.commit()
        logger.info(f"已更新用户 {user_id} 的头像URL")
        return True
    except Exception as e:
        db.rollback()
        logger.error(f"更新用户 {user_id} 的头像URL失败: {str(e)}")
        return False

def main():
    """主函数，处理所有用户的头像"""
    try:
        logger.info("开始处理用户头像...")
        db = SessionLocal()
        
        try:
            # 获取所有用户
            users = db.query(User).all()
            logger.info(f"找到 {len(users)} 个用户")
            
            # 统计数据
            total_users = len(users)
            processed_users = 0
            updated_users = 0
            failed_users = 0
            
            # 处理每个用户的头像
            for user in users:
                processed_users += 1
                
                # 检查是否有头像且是base64格式
                if not user.avatar_url or not user.avatar_url.startswith('data:image/'):
                    logger.info(f"用户 {user.id}: 头像不是base64格式，跳过")
                    continue
                
                logger.info(f"处理用户 {user.id} 的头像 ({processed_users}/{total_users})")
                new_avatar_url = convert_base64_to_file(user)
                
                if not new_avatar_url:
                    failed_users += 1
                    logger.error(f"用户 {user.id}: 转换头像失败")
                    continue
                
                # 更新数据库中的头像URL
                if update_user_avatar_url(db, user.id, new_avatar_url):
                    updated_users += 1
                else:
                    failed_users += 1
            
            # 输出处理结果
            logger.info(f"处理完成! 总用户: {total_users}, 更新: {updated_users}, 失败: {failed_users}")
            
        finally:
            db.close()
    
    except Exception as e:
        logger.error(f"处理过程中发生错误: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 