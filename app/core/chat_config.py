"""
聊天系统配置文件
包含所有与聊天处理相关的超参数
"""
from app.core.config import settings

# 历史记录相关配置
HISTORY_MESSAGE_LIMIT = 5  # 获取历史消息的默认条数
HISTORY_MESSAGE_LIMIT_EXTENDED = 10  # 需要更多上下文时的历史消息条数
MESSAGE_CACHE_TTL = 3600  # 消息缓存时间（秒）

# 大模型相关配置
# 直接从model_config.py导入模型配置，确保一致性
from app.core.model_config import MODEL_MAPPING

# 使用model_config.py中定义的模型配置
MODELS = {
    "default": MODEL_MAPPING["conversation"]["default"],  # 默认对话模型
    "conversation": MODEL_MAPPING["conversation"]["default"],  # 一般对话使用的模型
    "intent_recognition": MODEL_MAPPING["intent_recognition"]["default"],  # 意图识别使用的模型
    "exercise_generation": MODEL_MAPPING["training_plan"]["default"],  # 锻炼计划生成使用的模型
    "fitness_advice": MODEL_MAPPING["fitness_advice"]["default"],  # 健身建议使用的百炼应用模型
    "nutrition_advice": MODEL_MAPPING["nutrition_advice"]["default"],  # 营养建议使用的百炼应用模型
    "agent": MODEL_MAPPING["agent"]["default"]  # Agent使用的模型
}

# 模型参数
MODEL_PARAMS = {
    "default_temperature": settings.LLM_TEMPERATURE,  # 默认温度参数 (0.7)
    "intent_temperature": 0.1,  # 意图识别温度参数（低温度更确定性）
    "creative_temperature": 0.8,  # 创意内容温度参数（高温度更有创意）
    "streaming": True,  # 默认是否使用流式响应
    "use_bailian": True,  # 是否默认使用百炼应用
    "bailian_app_type": "fitness-coach-app",  # 默认使用的百炼应用类型
    "bailian_cache_ttl": 3600,  # 百炼应用缓存时间（秒）
    "vector_similarity_threshold": 0.6,  # 向量相似度阈值
}

# 意图处理相关配置
INTENT_RECOGNITION = {
    "confidence_threshold": 0.5,  # 意图识别的置信度阈值
    "default_intent": "general_chat",  # 默认意图
}

# 会话管理相关配置
CONVERSATION = {
    "session_timeout": 30 * 60,  # 会话超时时间（秒）
    "max_inactive_days": 30,  # 非活跃会话保留天数
}

# 缓存相关配置
CACHE = {
    "enabled": True,  # 是否启用缓存
    "ttl": 3600,  # 缓存过期时间（秒）
}

# API提供商配置
PROVIDERS = {
    "qwen": {
        "api_key": settings.QWEN_API_KEY,
        "api_base": settings.QWEN_API_BASE,
    },
    "deepseek": {
        "api_key": settings.DEEPSEEK_API_KEY,
        "api_base": settings.DEEPSEEK_API_BASE,
    },
    "bailian": {
        "api_key": settings.QWEN_API_KEY,  # 使用相同的API密钥
        "api_base": "https://dashscope.aliyuncs.com/api/v1/",  # 百炼应用API基础URL
        "stream_header": {"X-DashScope-SSE": "enable"},  # 流式输出的请求头
    }
}

# 百炼应用配置
BAILIAN_APPS = {
    "fitness-coach-app": {
        "app_id": "e9ba99afb1084d9ea3bfd2c9913e0dcf",  # 百炼应用ID，来自文档链接
        "description": "健身教练应用，提供专业的健身建议和指导"
    },
    "nutrition-coach-app": {
        "app_id": "b0c4de8a60e54a57b16bf2f3dec81940",  # 百炼应用ID，来自文档链接
        "description": "营养教练应用，提供专业的营养建议和指导"
    },
    "intent-recognition-app": {
        "app_id": "69fdc47bba3146eebd6a745c4c29f827",  # 意图识别应用ID
        "description": "意图识别应用，用于判断用户输入的意图"
    },
    "agent-app": {
        "app_id": "69fdc47bba3146eebd6a745c4c29f827",  # 智能健身AI助手应用ID
        "description": "智能健身AI助手代理应用，提供全面的健身指导和智能对话能力"
    },
}
