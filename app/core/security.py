from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from jose import JWTError, jwt
from passlib.context import CryptContext
from app.core.config import settings
from pydantic import BaseModel

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class TokenData(BaseModel):
    sub: str
    exp: Optional[datetime] = None

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow().replace(tzinfo=None) + expires_delta
    else:
        expire = datetime.utcnow().replace(tzinfo=None) + timedelta(days=settings.ACCESS_TOKEN_EXPIRE_DAYS)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt

def verify_access_token(token: str) -> TokenData:
    """
    验证访问令牌并返回令牌数据

    Args:
        token: JWT令牌字符串

    Returns:
        TokenData: 包含用户ID和过期时间的令牌数据对象

    Raises:
        JWTError: 如果令牌无效或已过期
    """
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        user_id: str = payload.get("sub")
        if user_id is None:
            raise JWTError("令牌中缺少用户ID")

        # 确保过期时间没有时区信息
        exp_time = None
        if "exp" in payload:
            exp_time = datetime.fromtimestamp(payload.get("exp")).replace(tzinfo=None)

        token_data = TokenData(
            sub=user_id,
            exp=exp_time
        )
        return token_data
    except JWTError as e:
        raise JWTError(f"无效的访问令牌: {str(e)}")