from functools import wraps
from typing import Optional
import json
from redis import Redis, RedisError
from app.core.config import settings
from app.core.logger import logger

redis_client = Redis(
    host=settings.REDIS_HOST,
    port=settings.REDIS_PORT,
    db=settings.REDIS_DB,
    decode_responses=True
)

def cache_training_plan(ttl: int = 3600):
    """训练计划缓存装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(self, plan_id: int, *args, **kwargs):
            cache_key = f"training_plan:{plan_id}"
            
            # 尝试从缓存获取
            cached_data = redis_client.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
            
            # 获取新数据
            result = func(self, plan_id, *args, **kwargs)
            
            # 存入缓存
            try:
                redis_client.setex(
                    cache_key,
                    ttl,
                    json.dumps(result)
                )
            except RedisError as e:
                if "read only" in str(e).lower():
                    logger.warning(f"缓存训练计划失败: Redis处于只读模式，跳过缓存操作")
                else:
                    logger.error(f"缓存训练计划失败: {str(e)}")
            except Exception as e:
                logger.error(f"缓存训练计划失败: {str(e)}")
            
            return result
        return wrapper
    return decorator