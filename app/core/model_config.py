"""
模型配置文件
定义所有可用的模型、提供商和配置
"""
from app.core.config import settings

# 模型类型定义
MODEL_TYPES = {
    "intent_recognition": {
        "description": "意图识别模型",
        "default_temperature": 0.1,
        "streaming": False
    },
    "conversation": {
        "description": "对话模型",
        "default_temperature": 0.7,
        "streaming": True
    },
    "fitness_advice": {
        "description": "健身建议模型",
        "default_temperature": 0.7,
        "streaming": True
    },
    "nutrition_advice": {
        "description": "营养建议模型",
        "default_temperature": 0.7,
        "streaming": True
    },
    "agent": {
        "description": "Agent模型",
        "default_temperature": 0.3,
        "streaming": True
    },
    "training_plan": {
        "description": "训练计划生成模型",
        "default_temperature": 0.7,
        "streaming": True,
        "thinking_mode": True
    }
}

# 模型映射表
MODEL_MAPPING = {
    # 意图识别模型
    "intent_recognition": {
        "default": "intent-recognition-app",
        "alternatives": ["intent-recognition-app", "qwen-turbo"],
        "provider": "bailian",
        "bailian_app": "intent-recognition-app",
        "description": "意图识别模型"
    },
    # 对话模型
    "conversation": {
        "default": "fitness-coach-app",
        "alternatives": ["fitness-coach-app", "qwen-max", "qwen-plus", "qwen-turbo"],
        "provider": "bailian",
        "bailian_app": "agent-app",
        "description": "一般对话模型"
    },
    # 健身建议模型
    "fitness_advice": {
        "default": "fitness-coach-app",
        "alternatives": ["agent-app", "qwen-max"],
        "provider": "bailian",
        "bailian_app": "fitness-coach-app",
        "description": "健身建议模型"
    },
    # 营养建议模型
    "nutrition_advice": {
        "default": "fitness-coach-app",
        "alternatives": ["fitness-coach-app", "qwen-max"],
        "provider": "bailian",
        "bailian_app": "fitness-coach-app",
        "description": "营养建议模型"
    },
    # Agent模型
    "agent": {
        "default": settings.AGENT_MODEL,
        "alternatives": ["deepseek-r1-250120", "qwen-max"],
        "provider": "deepseek",
        "bailian_app": "agent-app",
        "description": "Agent模型"
    },
    # 训练计划生成模型
    "training_plan": {
        "default": settings.LLM_EXERCISE_GENERATION_MODEL,
        "alternatives": ["qwen-max", "qwen-plus"],
        "provider": "qwen",
        "bailian_app": "fitness-coach-app",
        "description": "训练计划生成模型"
    }
}

# 意图到模型的映射
INTENT_MODEL_MAPPING = {
    "general_chat": "conversation",
    "daily_workout_plan": "training_plan",
    "weekly_workout_plan": "training_plan",
    "exercise_info": "fitness_advice",
    "diet_advice": "nutrition_advice",
    "fitness_qa": "fitness_advice",
    "body_fat_calculation": "fitness_advice",
    "calorie_calculation": "fitness_advice",
    "search_exercise": "fitness_advice",
    "recommend_exercise": "fitness_advice",
    "discuss_training_plan": "fitness_advice"
}

# 缓存配置
CACHE_CONFIG = {
    "enabled": True,
    "ttl": 3600,  # 缓存有效期（秒）
    "max_size": 1000  # 最大缓存项数
}

# 批处理配置
BATCH_CONFIG = {
    "enabled": True,
    "max_batch_size": 5,  # 最大批处理大小
    "wait_time": 0.1  # 等待时间（秒）
}

# 错误处理配置
ERROR_HANDLING = {
    "max_retries": 3,  # 最大重试次数
    "retry_delay": 1.0,  # 重试延迟（秒）
    "fallback_enabled": True  # 是否启用降级策略
}

# 模型提供商优先级
# 如果第一个提供商失败，会尝试使用下一个
PROVIDER_FALLBACK = {
    "intent_recognition": ["bailian", "qwen"],
    "conversation": ["qwen", "bailian"],
    "fitness_advice": ["bailian", "qwen"],
    "nutrition_advice": ["bailian", "qwen"],
    "agent": ["deepseek", "bailian", "qwen"],
    "training_plan": ["qwen", "bailian"]
}

# 百炼应用配置
BAILIAN_APP_MAPPING = {
    "intent_recognition": "intent-recognition-app",
    "conversation": "agent-app",
    "fitness_advice": "fitness-coach-app",
    "nutrition_advice": "nutrition-coach-app",
    "agent": "agent-app",
    "training_plan": "fitness-coach-app"
}
