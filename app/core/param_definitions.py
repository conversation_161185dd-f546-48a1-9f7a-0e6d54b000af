"""
参数定义中心

统一管理所有与训练、健身相关的参数定义，避免重复定义和不一致问题
"""

from typing import Dict, Any, List, Optional, Set, Union, Tuple

# 身体部位分类常量
BODY_PART_CATEGORIES = [
  { "id": 2, "name": "胸部" },
  { "id": 1, "name": "腿部" },
  { "id": 3, "name": "臀部" },
  { "id": 4, "name": "背部" },
  { "id": 5, "name": "手臂" },
  { "id": 6, "name": "肩部" },
  { "id": 7, "name": "小臂" },
  { "id": 8, "name": "小腿" },
  { "id": 9, "name": "颈部" },
  { "id": 10, "name": "有氧" },
  { "id": 11, "name": "全身" },
  { "id": 12, "name": "腰腹部" },
  { "id": 13, "name": "爆发力" },
  { "id": 14, "name": "力量举" },
  { "id": 15, "name": "瑜伽" },
  { "id": 16, "name": "拉伸" },
  { "id": 17, "name": "二头" },
  { "id": 18, "name": "三头" },
  { "id": 19, "name": "股四头肌" },
  { "id": 20, "name": "腘绳肌" }
]

# 器材分类常量
EQUIPMENT_CATEGORIES = [
  { "id": 1, "name": "杠铃" },
  { "id": 2, "name": "自重" },
  { "id": 3, "name": "绳索" },
  { "id": 4, "name": "哑铃" },
  { "id": 5, "name": "EZ杠铃" },
  { "id": 6, "name": "固定器械" },
  { "id": 7, "name": "悍马器械" },
  { "id": 8, "name": "史密斯" },
  { "id": 9, "name": "负重" },
  { "id": 10, "name": "协助" },
  { "id": 11, "name": "弹力带" },
  { "id": 12, "name": "战绳" },
  { "id": 13, "name": "波速球" },
  { "id": 14, "name": "悍马" },
  { "id": 15, "name": "壶铃" },
  { "id": 16, "name": "药球" },
  { "id": 17, "name": "奥杆" },
  { "id": 18, "name": "雪橇" },
  { "id": 19, "name": "阻力带" },
  { "id": 20, "name": "泡沫轴" },
  { "id": 21, "name": "筋膜球" },
  { "id": 22, "name": "绳类" },
  { "id": 23, "name": "瑜伽球" },
  { "id": 24, "name": "训练棍" },
  { "id": 25, "name": "TRX" },
  { "id": 26, "name": "六角杠铃" },
  { "id": 27, "name": "卷腹轮" }
]

# 肌肉分类常量
MUSCLE_CATEGORIES = [
  { "id": 2, "name": "内收肌", "en_name": "hip_adductors" },
  { "id": 4, "name": "肱二头肌", "en_name": "biceps" },
  { "id": 5, "name": "肱肌", "en_name": "brachialis" },
  { "id": 6, "name": "肱桡肌", "en_name": "brachioradialis" },
  { "id": 8, "name": "三角肌前束", "en_name": "deltoid_anterior" },
  { "id": 9, "name": "三角肌后束", "en_name": "deltoid_lateral" },
  { "id": 10, "name": "三角肌", "en_name": "deltoids" },
  { "id": 12, "name": "腓肠肌", "en_name": "gastrocnemius" },
  { "id": 13, "name": "臀大肌", "en_name": "gluteus_maximus" },
  { "id": 17, "name": "腘绳肌", "en_name": "hamstrings" },
  { "id": 18, "name": "髂腰肌", "en_name": "iliopsoas" },
  { "id": 19, "name": "冈下肌", "en_name": "infraspinatus" },
  { "id": 20, "name": "背阔肌", "en_name": "latissimus_dorsi" },
  { "id": 22, "name": "腹斜肌", "en_name": "obliques" },
  { "id": 24, "name": "胸大肌上束", "en_name": "chest_clavicular_head" },
  { "id": 25, "name": "胸大肌", "en_name": "chest" },
  { "id": 27, "name": "股四头肌", "en_name": "quadriceps" },
  { "id": 28, "name": "腹直肌", "en_name": "rectus_abdominis" },
  { "id": 29, "name": "缝匠肌", "en_name": "sartorius" },
  { "id": 31, "name": "前锯肌", "en_name": "serratus_anterior" },
  { "id": 32, "name": "比目鱼肌", "en_name": "soleus" },
  { "id": 36, "name": "股筋膜张肌", "en_name": "tensor_fasciae_femoris" },
  { "id": 37, "name": "大圆肌", "en_name": "teres_major" },
  { "id": 38, "name": "小圆肌", "en_name": "teres_minor" },
  { "id": 39, "name": "胫骨肌", "en_name": "tibias" },
  { "id": 43, "name": "斜方肌", "en_name": "trapezius" },
  { "id": 44, "name": "肱三头肌", "en_name": "triceps" },
  { "id": 45, "name": "腕伸肌", "en_name": "wrist_extensors" },
  { "id": 46, "name": "腕屈肌", "en_name": "wrist_flexors" },
  { "id": 51, "name": "胸大肌下束", "en_name": "chest_lower" }
]

# 身体部位同义词映射
BODY_PART_SYNONYMS = {
    "胸部": ["胸", "胸肌", "chest", "pecs", "pectoral"],
    "背部": ["背", "背肌", "back", "lats"],
    "肩部": ["肩膀", "肩", "shoulders", "deltoids", "delts"],
    "手臂": ["臂", "arms"],
    "二头肌": ["肱二头肌", "biceps", "二头"],
    "三头肌": ["肱三头肌", "triceps", "三头"],
    "腿部": ["腿", "legs"],
    "股四头肌": ["大腿前侧", "quadriceps", "quads", "股四头"],
    "腘绳肌": ["大腿后侧", "hamstrings", "hams"],
    "臀部": ["臀", "glutes", "gluteus"],
    "小腿": ["calves", "calf"],
    "腰腹部": ["腹部", "abs", "core", "腹肌", "腹"],
    "全身": ["整体", "全部", "复合", "compound", "整个身体"]
}

# 身体部位别名映射表
BODY_PART_ALIASES = {
    "手臂": "手臂",  # 确保手臂能正确映射到自身
    "手部": "手臂",  # 手部映射到手臂
    "上臂": "手臂",  # 上臂映射到手臂
    "下臂": "小臂",  # 下臂映射到小臂
    "腹部": "腰腹部",  # 腹部映射到腰腹部
    "腹肌": "腰腹部",  # 腹肌映射到腰腹部
    "核心": "腰腹部",  # 核心映射到腰腹部
    "臀肌": "臀部",  # 臀肌映射到臀部
    "大腿": "腿部",  # 大腿映射到腿部
    "腿": "腿部",  # 腿映射到腿部
    "二头肌": "二头",  # 二头肌映射到二头
    "三头肌": "三头",  # 三头肌映射到三头
    "肩": "肩部",  # 肩映射到肩部
    "肩膀": "肩部",  # 肩膀映射到肩部
    "胸": "胸部",  # 胸映射到胸部
    "胸肌": "胸部",  # 胸肌映射到胸部
    "背": "背部",  # 背映射到背部
}

# 训练场景定义
TRAINING_SCENARIOS = {
    "健身房": ["器械", "gym", "健身中心", "设备齐全", "器材"],
    "家庭": ["家", "home", "居家", "无器械", "简易器械", "小器材"],
    "户外": ["公园", "outdoor", "室外", "开放空间"],
    "办公室": ["工作场所", "office", "工位", "座椅训练"]
}

# 场景类型映射表
SCENARIO_MAPPING = {
    "home": "居家",
    "gym": "健身房",
    "outdoor": "户外",
    "office": "办公室"
}

# 训练目标定义
TRAINING_GOALS = {
    "增肌": ["肌肉增长", "肌肥大", "长肌肉", "增加肌肉", "块头", "muscle gain", "hypertrophy"],
    "减脂": ["减肥", "减重", "燃脂", "瘦身", "fat loss", "weight loss", "cardio"],
    "力量": ["增强力量", "力量提升", "强壮", "strength", "power"],
    "耐力": ["持久力", "体能", "endurance", "stamina"],
    "塑形": ["紧致", "线条", "定义", "轮廓", "tone", "definition"]
}

# 默认训练参数
DEFAULT_TRAINING_PARAMS = {
    "增肌": {
        "sets": 3,
        "reps": "8-12",
        "rest_seconds": 90
    },
    "减脂": {
        "sets": 3,
        "reps": "15-20",
        "rest_seconds": 45
    },
    "力量": {
        "sets": 4,
        "reps": "4-6",
        "rest_seconds": 120
    },
    "耐力": {
        "sets": 2,
        "reps": "20-30",
        "rest_seconds": 30
    },
    "塑形": {
        "sets": 3,
        "reps": "12-15",
        "rest_seconds": 60
    }
}

# 难度级别映射
DIFFICULTY_LEVELS = {
    1: "初级",
    2: "初中级",
    3: "中级",
    4: "中高级",
    5: "高级"
}

# 参数收集定义
PARAM_COLLECTION_ORDER = {
    # 默认参数收集顺序
    "default": [
        {"param": "body_part", "required": True},
        {"param": "scenario", "required": True},
        {"param": "plan_type", "required": True},
        {"param": "training_goal", "required": False},
        {"param": "difficulty", "required": False}
    ],
    # 单日训练计划参数收集顺序
    "daily_workout_plan": [
        {"param": "body_part", "required": True},
        {"param": "scenario", "required": True},
        {"param": "training_goal", "required": False},
        {"param": "difficulty", "required": False}
    ],
    # 周期训练计划参数收集顺序
    "weekly_training_plan": [
        {"param": "body_part", "required": True},
        {"param": "scenario", "required": True},
        {"param": "training_goal", "required": False},
        {"param": "difficulty", "required": False}
    ],
    # 动作推荐参数收集顺序 - 不需要plan_type
    "recommend_exercise": [
        {"param": "body_part", "required": True},
        {"param": "scenario", "required": True},
        {"param": "training_goal", "required": False}
    ],
    # 动作查询参数收集顺序
    "search_exercise": [
        {"param": "exercise_name", "required": True}
    ],
    # 健身问答参数收集顺序 - 无需特定参数
    "fitness_qa": []
}

# 参数名称映射
PARAM_NAME_MAP = {
    "body_part": "训练部位",
    "scenario": "训练场景",
    "plan_type": "计划类型",
    "training_goal": "训练目标",
    "difficulty": "难度级别",
    "muscle": "目标肌肉",
    "equipment": "使用器材",
    "exercise_name": "动作名称"
}

# 参数枚举值映射
PARAM_ENUM_MAP = {
    "scenario": SCENARIO_MAPPING,
    "plan_type": {"daily": "单日计划", "weekly": "周期计划"},
    "training_goal": {"增肌": "增肌", "减脂": "减脂", "力量": "力量", "耐力": "耐力", "塑形": "塑形"},
    "difficulty": DIFFICULTY_LEVELS
}

# 意图与必要参数映射
INTENT_REQUIRED_PARAMS = {
    "daily_workout_plan": ["body_part", "scenario", "plan_type"],
    "weekly_training_plan": ["body_part", "scenario", "plan_type"],
    "recommend_exercise": ["body_part", "scenario"],  # 显式不包含plan_type
    "search_exercise": ["exercise_name"],
    "diet_advice": ["training_goal"],
    "fitness_qa": []
}

# 参数提问模板
PARAM_QUESTION_TEMPLATES = {
    "body_part": "您想训练哪个身体部位？例如：胸部、背部、肩部、手臂、腿部、腰腹部等。",
    "scenario": "您计划在什么环境下训练？请选择：健身房 或 居家。",
    "plan_type": "您需要的是单日训练计划还是周期训练计划？",
    "training_goal": "您的训练目标是什么？增肌、减脂、力量、耐力还是塑形？",
    "difficulty": "您期望的训练难度是什么级别？初级、中级还是高级？",
    "exercise_name": "您想了解哪个具体的训练动作？请提供动作名称。",
    "default": "请告诉我您的{param_name}。"
}

# 参数确认模板
PARAM_CONFIRMATION_TEMPLATES = {
    "body_part": "好的，您想训练的是{value}。",
    "scenario": "明白了，您选择的是{value}训练环境。",
    "plan_type": "好的，您需要的是{value}。",
    "training_goal": "了解，您的训练目标是{value}。",
    "difficulty": "好的，您选择的训练难度是{value}。",
    "default": "已记录您的{param_name}：{value}。"
}

# 参数提取辅助函数
def normalize_input(text: str) -> str:
    """将输入文本标准化（去除空格，转为小写）"""
    if not text:
        return ""
    return text.strip().lower()

def get_param_collection_order(intent: str) -> List[Dict[str, Any]]:
    """根据意图获取参数收集顺序"""
    if intent and intent in PARAM_COLLECTION_ORDER:
        return PARAM_COLLECTION_ORDER[intent]
    return PARAM_COLLECTION_ORDER["default"]

def get_required_params_for_intent(intent: str) -> List[str]:
    """根据意图获取需要提取的参数列表"""
    if intent and intent in INTENT_REQUIRED_PARAMS:
        return INTENT_REQUIRED_PARAMS[intent]
    return []

def get_param_display_name(param: str) -> str:
    """获取参数的显示名称"""
    return PARAM_NAME_MAP.get(param, param)

def format_param_value(param: str, value: Any) -> str:
    """格式化参数值，处理枚举映射"""
    if param in PARAM_ENUM_MAP and value in PARAM_ENUM_MAP[param]:
        return PARAM_ENUM_MAP[param][value]
    return str(value)

def get_param_question(param: str) -> str:
    """获取参数的提问模板"""
    template = PARAM_QUESTION_TEMPLATES.get(param, PARAM_QUESTION_TEMPLATES["default"])
    if param == "default":
        return template.format(param_name=get_param_display_name(param))
    return template

def get_param_confirmation(param: str, value: Any) -> str:
    """获取参数的确认模板"""
    # 处理value可能是列表的情况
    if isinstance(value, list) and len(value) > 0:
        value = value[0]  # 使用第一个元素
    
    # 格式化显示值
    formatted_value = format_param_value(param, value)
    
    template = PARAM_CONFIRMATION_TEMPLATES.get(param, PARAM_CONFIRMATION_TEMPLATES["default"])
    if param == "default":
        return template.format(param_name=get_param_display_name(param), value=formatted_value)
    return template.format(value=formatted_value) 