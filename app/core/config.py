from pydantic_settings import BaseSettings
from pydantic import field_validator, Field
from typing import Optional, List, Dict, Any, Union
import os
import secrets

class Settings(BaseSettings):
    PROJECT_NAME: str = "Fitness Coach API"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"

    # API地址配置
    API_URL: str = "http://localhost:8000"

    # 是否为开发环境
    IS_DEV: bool = True  # 确保开发环境设置为True

    # 日志配置
    LOG_LEVEL: str = "DEBUG"  # 设置为DEBUG级别以输出所有日志
    CONSOLE_LOG: bool = True  # 启用控制台日志输出

    # 从环境变量读取或使用默认值
    DATABASE_URL: str = os.environ.get(
        "DATABASE_URL",
        "postgresql://postgres:!scienceFit0219@127.0.0.1:5432/fitness_db"
    )

    # 强制设置为正确的端口 - 无论环境变量如何设置
    @property
    def get_database_url(self):
        return self.DATABASE_URL

    REDIS_URL: str = "redis://localhost:6379/0"

    # Redis配置
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None
    REDIS_RATE_LIMIT_DB: int = 1  # 用于速率限制的Redis数据库

    # JWT配置
    SECRET_KEY: str = secrets.token_urlsafe(32)
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8
    ACCESS_TOKEN_EXPIRE_DAYS: int = 30  # 新增：token过期天数

    # 微信小程序配置
    WECHAT_MINI_APPID: str = os.environ.get("WECHAT_MINI_APPID", "wx39e96f46f7ddb61b")
    WECHAT_MINI_SECRET: str = os.environ.get("WECHAT_MINI_SECRET", "2fd45a22d7ef1e6a4e014ad6cfa959ed")

    # 提供别名以兼容新命名
    @property
    def WECHAT_APP_ID(self) -> str:
        return self.WECHAT_MINI_APPID

    @property
    def WECHAT_APP_SECRET(self) -> str:
        return self.WECHAT_MINI_SECRET

    # IP白名单配置 - 完全关闭白名单检查
    CHECK_IP_WHITELIST: bool = False  # 确保IP检查关闭
    IP_WHITELIST: List[str] = [
        "127.0.0.1",
        "localhost",
        "*************",  # 开发服务器IP
        "**************",  # 用户IP，从错误信息中提取
        "***************", # 新的IP地址
        "::1",             # IPv6 localhost
        "0.0.0.0/0",       # 所有IP (仅在开发环境中使用)
    ]

    # API请求超时配置 - 大幅增加超时时间
    API_REQUEST_TIMEOUT: int = 30  # API请求超时时间（秒）- 从10秒增加到30秒
    CONNECTION_TIMEOUT: int = 15   # 连接超时时间（秒）- 从5秒增加到15秒

    # 管理后台认证配置
    ADMIN_USERNAME: str = os.environ.get("ADMIN_USERNAME", "admin")
    ADMIN_PASSWORD: str = os.environ.get("ADMIN_PASSWORD", "sciencefit2025")

    # AI模型接口配置
    AI_API_CONFIGS: Dict[str, Dict[str, str]] = {
        "aliyun_qwen": {
            "api_key": os.environ.get("ALIYUN_API_KEY", "sk-5f104bfa343e42a8be683b05f3d259a9"),
            "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
            "model": "qwen-vl-max-latest",
            "description": "阿里云通义千问视觉大模型"
        },
        "openai": {
            "api_key": os.environ.get("OPENAI_API_KEY", ""),
            "base_url": "https://api.openai.com/v1",
            "model": "gpt-4-vision-preview",
            "description": "OpenAI GPT-4-Vision模型"
        },
        "azure_openai": {
            "api_key": os.environ.get("AZURE_OPENAI_API_KEY", ""),
            "base_url": os.environ.get("AZURE_OPENAI_ENDPOINT", ""),
            "model": "gpt-4-vision",
            "description": "Azure OpenAI Vision模型"
        },
        "mock": {
            "api_key": "mock_key",
            "base_url": "http://localhost:8000",
            "model": "mock_model",
            "description": "本地模拟模型，用于测试"
        }
    }

    # 默认使用的AI接口
    AI_DEFAULT_SERVICE: str = os.environ.get("AI_DEFAULT_SERVICE", "aliyun_qwen")

    # 获取当前配置的AI接口
    @property
    def AI_API_KEY(self) -> str:
        return self.AI_API_CONFIGS.get(self.AI_DEFAULT_SERVICE, {}).get("api_key", "")

    @property
    def AI_BASE_URL(self) -> str:
        return self.AI_API_CONFIGS.get(self.AI_DEFAULT_SERVICE, {}).get("base_url", "")

    @property
    def AI_MODEL(self) -> str:
        return self.AI_API_CONFIGS.get(self.AI_DEFAULT_SERVICE, {}).get("model", "")

    # 新增LangChain和通义千问相关配置
    # 通义千问API配置
    QWEN_API_KEY: str = os.environ.get("QWEN_API_KEY", "sk-5f104bfa343e42a8be683b05f3d259a9")
    QWEN_API_BASE: str = os.environ.get("QWEN_API_BASE", "https://dashscope.aliyuncs.com/compatible-mode/v1")
    LLM_MODEL: str = os.environ.get("LLM_MODEL", "qwen-max")
    LLM_CHARACTER_MODEL: str = os.environ.get("LLM_CHARACTER_MODEL", "qwen-plus-character")
    LLM_INTENT_RECOGNITION_MODEL: str = os.environ.get("LLM_INTENT_RECOGNITION_MODEL", "tongyi-intent-detect-v3")
    LLM_EXERCISE_GENERATION_MODEL: str = os.environ.get("LLM_EXERCISE_GENERATION_MODEL", "qwen-turbo-latest")
    LLM_PROVIDER: str = os.environ.get("LLM_PROVIDER", "qwen")
    LLM_TEMPERATURE: float = float(os.environ.get("LLM_TEMPERATURE", "0.7"))

    # DeepSeek API配置 - 用于Agent
    DEEPSEEK_API_KEY: str = os.environ.get("DEEPSEEK_API_KEY", "91cca13f-0e5f-428e-8f69-f0c7f9ec4218")
    DEEPSEEK_API_BASE: str = os.environ.get("DEEPSEEK_API_BASE", "https://ark.cn-beijing.volces.com/api/v3")
    AGENT_MODEL: str = os.environ.get("AGENT_MODEL", "deepseek-r1-250120")
    USE_AGENT: bool = os.environ.get("USE_AGENT", "True").lower() in ["true", "1", "yes"]

    # LangChain向量存储配置
    VECTOR_STORE_PATH: str = os.environ.get("VECTOR_STORE_PATH", "/home/<USER>/backend/data/vectorstore")

    # 静态文件目录
    STATIC_DIR: str = os.environ.get("STATIC_DIR", "/home/<USER>/backend/static")

    # 配置 LangGraph 设置
    LANGGRAPH_CHECKPOINT_DIR: str = os.environ.get("LANGGRAPH_CHECKPOINT_DIR", "./data/checkpoints")

    # 配置 LLM
    OPENAI_API_KEY: str = os.environ.get("OPENAI_API_KEY", "")
    DEFAULT_LLM_MODEL: str = os.environ.get("DEFAULT_LLM_MODEL", "gpt-3.5-turbo")
    FITNESS_QA_MODEL: str = os.environ.get("FITNESS_QA_MODEL", "gpt-3.5-turbo")
    TRAINING_PLAN_MODEL: str = os.environ.get("TRAINING_PLAN_MODEL", "gpt-4")

    # 数据库配置 - 使用环境变量或默认值，确保这些字段在类初始化时就有值
    POSTGRES_SERVER: str = "localhost"
    POSTGRES_USER: str = "postgres"
    POSTGRES_PASSWORD: str = "!scienceFit0219"
    POSTGRES_DB: str = "fitness_db"
    SQLALCHEMY_DATABASE_URI: Optional[str] = None

    @field_validator("SQLALCHEMY_DATABASE_URI", mode="before")
    def assemble_db_connection(cls, v: Optional[str], info) -> Any:
        values = info.data
        if isinstance(v, str) and v:
            return v
        # 使用字符串格式化，确保所有值都存在
        return f"postgresql://{values.get('POSTGRES_USER')}:{values.get('POSTGRES_PASSWORD')}@{values.get('POSTGRES_SERVER')}/{values.get('POSTGRES_DB') or ''}"

    SENTRY_DSN: Optional[str] = None

    @field_validator("SENTRY_DSN", mode="before")
    def sentry_dsn_can_be_blank(cls, v: Optional[str]) -> Optional[str]:
        if v is None or len(str(v)) == 0:
            return None
        return v

    # 邮件配置
    SMTP_TLS: bool = True
    SMTP_PORT: Optional[int] = None
    SMTP_HOST: Optional[str] = None
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    EMAILS_FROM_EMAIL: Optional[str] = None
    EMAILS_FROM_NAME: Optional[str] = None

    @field_validator("EMAILS_FROM_NAME")
    def get_project_name(cls, v: Optional[str], info) -> str:
        values = info.data
        if not v:
            return values["PROJECT_NAME"]
        return v

    EMAIL_RESET_TOKEN_EXPIRE_HOURS: int = 48
    EMAIL_TEMPLATES_DIR: str = "/app/app/email-templates/build"
    EMAILS_ENABLED: bool = False

    @field_validator("EMAILS_ENABLED", mode="before")
    def get_emails_enabled(cls, v: bool, info) -> bool:
        values = info.data
        return bool(
            values.get("SMTP_HOST")
            and values.get("SMTP_PORT")
            and values.get("EMAILS_FROM_EMAIL")
        )

    # 超级用户配置 - 设置默认值
    EMAIL_TEST_USER: str = "<EMAIL>"
    FIRST_SUPERUSER: str = "<EMAIL>"
    FIRST_SUPERUSER_PASSWORD: str = "admin"
    USERS_OPEN_REGISTRATION: bool = False

    model_config = {
        "case_sensitive": True,
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        # 设置环境变量前缀
        "env_prefix": ""
    }

settings = Settings()