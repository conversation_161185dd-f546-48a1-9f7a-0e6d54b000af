version: '3.8'

services:
  nginx:
    image: nginx:latest
    ports:
      - "8080:80"
      - "8000:8000"
      - "443:443"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d
      - /data/exercises:/data/exercises
      - /data/muscles:/data/muscles
      - /data/users:/data/users
      - /data/food:/data/food
    depends_on:
      backend:
        condition: service_healthy
    restart: always

  backend:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - /data/exercises:/data/exercises
      - /data/muscles:/data/muscles
      - /data/users:/data/users
    environment:
      - DATABASE_URL=**************************************/fitness_db
    depends_on:
      db:
        condition: service_healthy
    restart: always
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  db:
    image: postgres:15
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=!scienceFit0219
      - POSTGRES_DB=fitness_db
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database_backups:/backups
      - ./init-scripts:/docker-entrypoint-initdb.d
    restart: always
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 10s

  redis:
    image: redis:7
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: always
    command: redis-server --appendonly yes
      
  pgadmin:
    image: dpage/pgadmin4
    container_name: pgadmin
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=sciencefit2025
      - PGADMIN_CONFIG_SERVER_MODE=True
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
      - ./pgadmin/servers.json:/pgadmin4/servers.json
    depends_on:
      db:
        condition: service_healthy
    restart: always

volumes:
  postgres_data:
  pgadmin_data:
  redis_data: 