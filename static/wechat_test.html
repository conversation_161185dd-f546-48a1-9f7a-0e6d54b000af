<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信小程序API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
        }
        button {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 10px 2px;
            cursor: pointer;
            border-radius: 4px;
        }
        input, textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
            margin-top: 6px;
            margin-bottom: 16px;
            resize: vertical;
        }
        .result {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
            min-height: 100px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>微信小程序API测试</h1>
    
    <h2>code2session API</h2>
    <div>
        <label for="code">微信临时登录凭证code:</label>
        <input type="text" id="code" value="test_code">
        
        <button onclick="testCode2Session()">测试code2session接口</button>
        
        <div class="result" id="code2session-result">结果将显示在这里...</div>
    </div>
    
    <h2>健康检查</h2>
    <div>
        <button onclick="testHealth()">测试健康检查接口</button>
        <div class="result" id="health-result">结果将显示在这里...</div>
    </div>
    
    <script>
        // 获取当前主机和端口
        const host = window.location.hostname;
        const port = window.location.port;
        const baseUrl = `http://${host}:${port}`;
        
        // code2session测试
        async function testCode2Session() {
            const code = document.getElementById('code').value;
            const resultDiv = document.getElementById('code2session-result');
            
            try {
                resultDiv.innerHTML = '请求中...';
                
                const response = await fetch(`${baseUrl}/api/v1/auth/wechat/code2session`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ code })
                });
                
                const data = await response.json();
                
                resultDiv.innerHTML = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.innerHTML = `错误: ${error.message}`;
                console.error('Code2Session错误:', error);
            }
        }
        
        // 健康检查测试
        async function testHealth() {
            const resultDiv = document.getElementById('health-result');
            
            try {
                resultDiv.innerHTML = '请求中...';
                
                const response = await fetch(`${baseUrl}/health`);
                const data = await response.json();
                
                resultDiv.innerHTML = JSON.stringify(data, null, 2);
            } catch (error) {
                resultDiv.innerHTML = `错误: ${error.message}`;
                console.error('健康检查错误:', error);
            }
        }
    </script>
</body>
</html> 