import requests
import json

# API配置
BASE_URL = "http://localhost:8000"  # 使用localhost
API_PATH = "/api/v1/community/workouts/0/share"  # 更新为完整API路径
USER_ID = 1  # 假设用户ID为1

# 认证函数
def get_auth_token():
    """获取认证令牌"""
    # 使用硬编码的测试令牌
    return "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxIiwiZXhwIjoxNzQ4MzQyNDI1fQ.7xMCcYGAp_CKg8UHJcc3G2XZVdRwUVmzGEaJqBUUDUQ"

# 构建请求数据
def build_workout_data():
    """构建训练数据"""
    return {
        "title": "测试训练分享",
        "content": "这是一条通过API测试创建的训练分享",
        "visibility": "Everyone",
        "tags": ["测试", "API", "训练"],
        "workout_data": {  # 重命名为workout_data以匹配后端期望的字段名
            "exercises": [
                {
                    "name": "俯卧撑",
                    "imageUrl": "/exercises/images/push-up.jpg",  # 使用imageUrl而不是image_url
                    "category": "胸部",
                    "sets": [
                        {"type": "normal", "reps": 10, "weight": 0, "completed": True},
                        {"type": "normal", "reps": 12, "weight": 0, "completed": True},
                        {"type": "normal", "reps": 15, "weight": 0, "completed": True}
                    ],
                    "hasCompletedSets": True,  # 添加hasCompletedSets字段
                    "totalVolume": 0,  # 使用totalVolume而不是total_volume
                    "completedSets": 3  # 添加completedSets字段
                },
                {
                    "name": "深蹲",
                    "imageUrl": "/exercises/images/squat.jpg",  # 使用imageUrl而不是image_url
                    "category": "腿部",
                    "sets": [
                        {"type": "normal", "reps": 8, "weight": 60, "completed": True},
                        {"type": "normal", "reps": 8, "weight": 70, "completed": True},
                        {"type": "normal", "reps": 6, "weight": 80, "completed": True}
                    ],
                    "hasCompletedSets": True,  # 添加hasCompletedSets字段
                    "totalVolume": 1680,  # 使用totalVolume而不是total_volume
                    "completedSets": 3  # 添加completedSets字段
                }
            ],
            "duration_seconds": 1800,  # 添加训练时长
            "total_sets": 6,  # 添加总组数
            "total_volume": 1680  # 添加总重量
        }
    }

# 发送请求
def send_share_request():
    """发送分享请求"""
    token = get_auth_token()
    if not token:
        print("无法获取认证令牌，终止请求")
        return
    
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}"
    }
    
    data = build_workout_data()
    url = f"{BASE_URL}{API_PATH}"
    
    print(f"发送请求到: {url}")
    print(f"请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
    
    try:
        response = requests.post(url, headers=headers, json=data)
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200 or response.status_code == 201:
            print("训练分享成功!")
            return response.json()
        else:
            print(f"训练分享失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"请求异常: {str(e)}")
        return None

if __name__ == "__main__":
    result = send_share_request()
    if result:
        print(f"创建的帖子ID: {result.get('id', 'unknown')}")
        print(f"创建时间: {result.get('created_at', 'unknown')}") 