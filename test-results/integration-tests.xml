<?xml version="1.0" encoding="utf-8"?><testsuites><testsuite name="pytest" errors="0" failures="7" skipped="0" tests="17" time="2.813" timestamp="2025-05-26T12:48:03.909456+08:00" hostname="VM-4-16-ubuntu"><testcase classname="tests.comprehensive.integration.test_api_endpoints.TestChatAPIEndpoints" name="test_send_message_endpoint" time="0.257" /><testcase classname="tests.comprehensive.integration.test_api_endpoints.TestChatAPIEndpoints" name="test_send_message_without_session_id" time="0.017" /><testcase classname="tests.comprehensive.integration.test_api_endpoints.TestChatAPIEndpoints" name="test_send_message_error_handling" time="0.022" /><testcase classname="tests.comprehensive.integration.test_api_endpoints.TestChatAPIEndpoints" name="test_update_user_info_endpoint" time="0.015"><failure message="assert False is True">tests/comprehensive/integration/test_api_endpoints.py:185: in test_update_user_info_endpoint
    assert response_data["success"] is True
E   assert False is True</failure></testcase><testcase classname="tests.comprehensive.integration.test_api_endpoints.TestChatAPIEndpoints" name="test_get_conversations_endpoint" time="0.025"><failure message="sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such table: conversations&#10;[SQL: SELECT conversations.id AS conversations_id, conversations.user_id AS conversations_user_id, conversations.session_id AS conversations_session_id, conversations.start_time AS conversations_start_time, conversations.last_active AS conversations_last_active, conversations.is_active AS conversations_is_active, conversations.meta_info AS conversations_meta_info &#10;FROM conversations &#10;WHERE conversations.user_id = ? AND conversations.is_active = 1 ORDER BY conversations.last_active DESC&#10; LIMIT ? OFFSET ?]&#10;[parameters: (1, 100, 0)]&#10;(Background on this error at: https://sqlalche.me/e/20/e3q8)">.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py:1969: in _exec_single_context
    self.dialect.do_execute(
.venv/lib/python3.12/site-packages/sqlalchemy/engine/default.py:922: in do_execute
    cursor.execute(statement, parameters)
E   sqlite3.OperationalError: no such table: conversations

The above exception was the direct cause of the following exception:
tests/comprehensive/integration/test_api_endpoints.py:191: in test_get_conversations_endpoint
    response = client.get(
.venv/lib/python3.12/site-packages/starlette/testclient.py:499: in get
    return super().get(
.venv/lib/python3.12/site-packages/httpx/_client.py:1041: in get
    return self.request(
.venv/lib/python3.12/site-packages/starlette/testclient.py:465: in request
    return super().request(
.venv/lib/python3.12/site-packages/httpx/_client.py:814: in request
    return self.send(request, auth=auth, follow_redirects=follow_redirects)
.venv/lib/python3.12/site-packages/httpx/_client.py:901: in send
    response = self._send_handling_auth(
.venv/lib/python3.12/site-packages/httpx/_client.py:929: in _send_handling_auth
    response = self._send_handling_redirects(
.venv/lib/python3.12/site-packages/httpx/_client.py:966: in _send_handling_redirects
    response = self._send_single_request(request)
.venv/lib/python3.12/site-packages/httpx/_client.py:1002: in _send_single_request
    response = transport.handle_request(request)
.venv/lib/python3.12/site-packages/starlette/testclient.py:342: in handle_request
    raise exc
.venv/lib/python3.12/site-packages/starlette/testclient.py:339: in handle_request
    portal.call(self.app, scope, receive, send)
.venv/lib/python3.12/site-packages/anyio/from_thread.py:277: in call
    return cast(T_Retval, self.start_task_soon(func, *args).result())
/usr/lib/python3.12/concurrent/futures/_base.py:456: in result
    return self.__get_result()
/usr/lib/python3.12/concurrent/futures/_base.py:401: in __get_result
    raise self._exception
.venv/lib/python3.12/site-packages/anyio/from_thread.py:217: in _call_func
    retval = await retval
.venv/lib/python3.12/site-packages/fastapi/applications.py:1106: in __call__
    await super().__call__(scope, receive, send)
.venv/lib/python3.12/site-packages/starlette/applications.py:122: in __call__
    await self.middleware_stack(scope, receive, send)
.venv/lib/python3.12/site-packages/starlette/middleware/errors.py:184: in __call__
    raise exc
.venv/lib/python3.12/site-packages/starlette/middleware/errors.py:162: in __call__
    await self.app(scope, receive, _send)
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:108: in __call__
    response = await self.dispatch_func(request, call_next)
app/main.py:321: in websocket_path_compatibility
    response = await call_next(request)
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:84: in call_next
    raise app_exc
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:70: in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:108: in __call__
    response = await self.dispatch_func(request, call_next)
app/main.py:281: in workout_path_compatibility
    response = await call_next(request)
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:84: in call_next
    raise app_exc
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:70: in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:108: in __call__
    response = await self.dispatch_func(request, call_next)
app/main.py:181: in log_request
    response = await call_next(request)
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:84: in call_next
    raise app_exc
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:70: in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:108: in __call__
    response = await self.dispatch_func(request, call_next)
app/main.py:162: in fix_url_paths
    return await call_next(request)
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:84: in call_next
    raise app_exc
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:70: in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
.venv/lib/python3.12/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
.venv/lib/python3.12/site-packages/starlette/middleware/exceptions.py:79: in __call__
    raise exc
.venv/lib/python3.12/site-packages/starlette/middleware/exceptions.py:68: in __call__
    await self.app(scope, receive, sender)
.venv/lib/python3.12/site-packages/fastapi/middleware/asyncexitstack.py:20: in __call__
    raise e
.venv/lib/python3.12/site-packages/fastapi/middleware/asyncexitstack.py:17: in __call__
    await self.app(scope, receive, send)
.venv/lib/python3.12/site-packages/starlette/routing.py:718: in __call__
    await route.handle(scope, receive, send)
.venv/lib/python3.12/site-packages/starlette/routing.py:276: in handle
    await self.app(scope, receive, send)
.venv/lib/python3.12/site-packages/starlette/routing.py:66: in app
    response = await func(request)
.venv/lib/python3.12/site-packages/fastapi/routing.py:274: in app
    raw_response = await run_endpoint_function(
.venv/lib/python3.12/site-packages/fastapi/routing.py:193: in run_endpoint_function
    return await run_in_threadpool(dependant.call, **values)
.venv/lib/python3.12/site-packages/starlette/concurrency.py:41: in run_in_threadpool
    return await anyio.to_thread.run_sync(func, *args)
.venv/lib/python3.12/site-packages/anyio/to_thread.py:33: in run_sync
    return await get_asynclib().run_sync_in_worker_thread(
.venv/lib/python3.12/site-packages/anyio/_backends/_asyncio.py:877: in run_sync_in_worker_thread
    return await future
.venv/lib/python3.12/site-packages/anyio/_backends/_asyncio.py:807: in run
    result = context.run(func, *args)
app/api/v2/endpoints/chat.py:329: in get_conversations
    conversations = crud.crud_conversation.get_active_by_user(
app/crud/crud_conversation.py:47: in get_active_by_user
    .all()
.venv/lib/python3.12/site-packages/sqlalchemy/orm/query.py:2693: in all
    return self._iter().all()  # type: ignore
.venv/lib/python3.12/site-packages/sqlalchemy/orm/query.py:2847: in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
.venv/lib/python3.12/site-packages/sqlalchemy/orm/session.py:2308: in execute
    return self._execute_internal(
.venv/lib/python3.12/site-packages/sqlalchemy/orm/session.py:2190: in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
.venv/lib/python3.12/site-packages/sqlalchemy/orm/context.py:293: in orm_execute_statement
    result = conn.execute(
.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py:1416: in execute
    return meth(
.venv/lib/python3.12/site-packages/sqlalchemy/sql/elements.py:516: in _execute_on_connection
    return connection._execute_clauseelement(
.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py:1639: in _execute_clauseelement
    ret = self._execute_context(
.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py:1848: in _execute_context
    return self._exec_single_context(
.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py:1988: in _exec_single_context
    self._handle_dbapi_exception(
.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py:2343: in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py:1969: in _exec_single_context
    self.dialect.do_execute(
.venv/lib/python3.12/site-packages/sqlalchemy/engine/default.py:922: in do_execute
    cursor.execute(statement, parameters)
E   sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such table: conversations
E   [SQL: SELECT conversations.id AS conversations_id, conversations.user_id AS conversations_user_id, conversations.session_id AS conversations_session_id, conversations.start_time AS conversations_start_time, conversations.last_active AS conversations_last_active, conversations.is_active AS conversations_is_active, conversations.meta_info AS conversations_meta_info 
E   FROM conversations 
E   WHERE conversations.user_id = ? AND conversations.is_active = 1 ORDER BY conversations.last_active DESC
E    LIMIT ? OFFSET ?]
E   [parameters: (1, 100, 0)]
E   (Background on this error at: https://sqlalche.me/e/20/e3q8)</failure></testcase><testcase classname="tests.comprehensive.integration.test_api_endpoints.TestChatAPIEndpoints" name="test_get_session_messages_endpoint" time="0.018"><failure message="sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such table: conversations&#10;[SQL: SELECT conversations.id AS conversations_id, conversations.user_id AS conversations_user_id, conversations.session_id AS conversations_session_id, conversations.start_time AS conversations_start_time, conversations.last_active AS conversations_last_active, conversations.is_active AS conversations_is_active, conversations.meta_info AS conversations_meta_info &#10;FROM conversations &#10;WHERE conversations.session_id = ?&#10; LIMIT ? OFFSET ?]&#10;[parameters: ('test_session_003', 1, 0)]&#10;(Background on this error at: https://sqlalche.me/e/20/e3q8)">.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py:1969: in _exec_single_context
    self.dialect.do_execute(
.venv/lib/python3.12/site-packages/sqlalchemy/engine/default.py:922: in do_execute
    cursor.execute(statement, parameters)
E   sqlite3.OperationalError: no such table: conversations

The above exception was the direct cause of the following exception:
tests/comprehensive/integration/test_api_endpoints.py:206: in test_get_session_messages_endpoint
    response = client.get(
.venv/lib/python3.12/site-packages/starlette/testclient.py:499: in get
    return super().get(
.venv/lib/python3.12/site-packages/httpx/_client.py:1041: in get
    return self.request(
.venv/lib/python3.12/site-packages/starlette/testclient.py:465: in request
    return super().request(
.venv/lib/python3.12/site-packages/httpx/_client.py:814: in request
    return self.send(request, auth=auth, follow_redirects=follow_redirects)
.venv/lib/python3.12/site-packages/httpx/_client.py:901: in send
    response = self._send_handling_auth(
.venv/lib/python3.12/site-packages/httpx/_client.py:929: in _send_handling_auth
    response = self._send_handling_redirects(
.venv/lib/python3.12/site-packages/httpx/_client.py:966: in _send_handling_redirects
    response = self._send_single_request(request)
.venv/lib/python3.12/site-packages/httpx/_client.py:1002: in _send_single_request
    response = transport.handle_request(request)
.venv/lib/python3.12/site-packages/starlette/testclient.py:342: in handle_request
    raise exc
.venv/lib/python3.12/site-packages/starlette/testclient.py:339: in handle_request
    portal.call(self.app, scope, receive, send)
.venv/lib/python3.12/site-packages/anyio/from_thread.py:277: in call
    return cast(T_Retval, self.start_task_soon(func, *args).result())
/usr/lib/python3.12/concurrent/futures/_base.py:456: in result
    return self.__get_result()
/usr/lib/python3.12/concurrent/futures/_base.py:401: in __get_result
    raise self._exception
.venv/lib/python3.12/site-packages/anyio/from_thread.py:217: in _call_func
    retval = await retval
.venv/lib/python3.12/site-packages/fastapi/applications.py:1106: in __call__
    await super().__call__(scope, receive, send)
.venv/lib/python3.12/site-packages/starlette/applications.py:122: in __call__
    await self.middleware_stack(scope, receive, send)
.venv/lib/python3.12/site-packages/starlette/middleware/errors.py:184: in __call__
    raise exc
.venv/lib/python3.12/site-packages/starlette/middleware/errors.py:162: in __call__
    await self.app(scope, receive, _send)
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:108: in __call__
    response = await self.dispatch_func(request, call_next)
app/main.py:321: in websocket_path_compatibility
    response = await call_next(request)
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:84: in call_next
    raise app_exc
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:70: in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:108: in __call__
    response = await self.dispatch_func(request, call_next)
app/main.py:281: in workout_path_compatibility
    response = await call_next(request)
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:84: in call_next
    raise app_exc
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:70: in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:108: in __call__
    response = await self.dispatch_func(request, call_next)
app/main.py:181: in log_request
    response = await call_next(request)
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:84: in call_next
    raise app_exc
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:70: in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:108: in __call__
    response = await self.dispatch_func(request, call_next)
app/main.py:162: in fix_url_paths
    return await call_next(request)
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:84: in call_next
    raise app_exc
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:70: in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
.venv/lib/python3.12/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
.venv/lib/python3.12/site-packages/starlette/middleware/exceptions.py:79: in __call__
    raise exc
.venv/lib/python3.12/site-packages/starlette/middleware/exceptions.py:68: in __call__
    await self.app(scope, receive, sender)
.venv/lib/python3.12/site-packages/fastapi/middleware/asyncexitstack.py:20: in __call__
    raise e
.venv/lib/python3.12/site-packages/fastapi/middleware/asyncexitstack.py:17: in __call__
    await self.app(scope, receive, send)
.venv/lib/python3.12/site-packages/starlette/routing.py:718: in __call__
    await route.handle(scope, receive, send)
.venv/lib/python3.12/site-packages/starlette/routing.py:276: in handle
    await self.app(scope, receive, send)
.venv/lib/python3.12/site-packages/starlette/routing.py:66: in app
    response = await func(request)
.venv/lib/python3.12/site-packages/fastapi/routing.py:274: in app
    raw_response = await run_endpoint_function(
.venv/lib/python3.12/site-packages/fastapi/routing.py:193: in run_endpoint_function
    return await run_in_threadpool(dependant.call, **values)
.venv/lib/python3.12/site-packages/starlette/concurrency.py:41: in run_in_threadpool
    return await anyio.to_thread.run_sync(func, *args)
.venv/lib/python3.12/site-packages/anyio/to_thread.py:33: in run_sync
    return await get_asynclib().run_sync_in_worker_thread(
.venv/lib/python3.12/site-packages/anyio/_backends/_asyncio.py:877: in run_sync_in_worker_thread
    return await future
.venv/lib/python3.12/site-packages/anyio/_backends/_asyncio.py:807: in run
    result = context.run(func, *args)
app/api/v2/endpoints/chat.py:355: in get_conversation_messages
    conversation, is_new = crud.crud_conversation.get_or_create_by_session_id(
app/crud/crud_conversation.py:94: in get_or_create_by_session_id
    conversation = self.get_by_session_id(db, session_id=session_id)
app/crud/crud_conversation.py:35: in get_by_session_id
    return db.query(Conversation).filter(Conversation.session_id == session_id).first()
.venv/lib/python3.12/site-packages/sqlalchemy/orm/query.py:2748: in first
    return self.limit(1)._iter().first()  # type: ignore
.venv/lib/python3.12/site-packages/sqlalchemy/orm/query.py:2847: in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
.venv/lib/python3.12/site-packages/sqlalchemy/orm/session.py:2308: in execute
    return self._execute_internal(
.venv/lib/python3.12/site-packages/sqlalchemy/orm/session.py:2190: in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
.venv/lib/python3.12/site-packages/sqlalchemy/orm/context.py:293: in orm_execute_statement
    result = conn.execute(
.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py:1416: in execute
    return meth(
.venv/lib/python3.12/site-packages/sqlalchemy/sql/elements.py:516: in _execute_on_connection
    return connection._execute_clauseelement(
.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py:1639: in _execute_clauseelement
    ret = self._execute_context(
.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py:1848: in _execute_context
    return self._exec_single_context(
.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py:1988: in _exec_single_context
    self._handle_dbapi_exception(
.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py:2343: in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py:1969: in _exec_single_context
    self.dialect.do_execute(
.venv/lib/python3.12/site-packages/sqlalchemy/engine/default.py:922: in do_execute
    cursor.execute(statement, parameters)
E   sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such table: conversations
E   [SQL: SELECT conversations.id AS conversations_id, conversations.user_id AS conversations_user_id, conversations.session_id AS conversations_session_id, conversations.start_time AS conversations_start_time, conversations.last_active AS conversations_last_active, conversations.is_active AS conversations_is_active, conversations.meta_info AS conversations_meta_info 
E   FROM conversations 
E   WHERE conversations.session_id = ?
E    LIMIT ? OFFSET ?]
E   [parameters: ('test_session_003', 1, 0)]
E   (Background on this error at: https://sqlalche.me/e/20/e3q8)</failure></testcase><testcase classname="tests.comprehensive.integration.test_api_endpoints.TestChatAPIEndpoints" name="test_poll_new_messages_endpoint" time="0.017"><failure message="sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such table: conversations&#10;[SQL: SELECT conversations.id AS conversations_id, conversations.user_id AS conversations_user_id, conversations.session_id AS conversations_session_id, conversations.start_time AS conversations_start_time, conversations.last_active AS conversations_last_active, conversations.is_active AS conversations_is_active, conversations.meta_info AS conversations_meta_info &#10;FROM conversations &#10;WHERE conversations.session_id = ?&#10; LIMIT ? OFFSET ?]&#10;[parameters: ('test_session_004', 1, 0)]&#10;(Background on this error at: https://sqlalche.me/e/20/e3q8)">.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py:1969: in _exec_single_context
    self.dialect.do_execute(
.venv/lib/python3.12/site-packages/sqlalchemy/engine/default.py:922: in do_execute
    cursor.execute(statement, parameters)
E   sqlite3.OperationalError: no such table: conversations

The above exception was the direct cause of the following exception:
tests/comprehensive/integration/test_api_endpoints.py:223: in test_poll_new_messages_endpoint
    response = client.get(
.venv/lib/python3.12/site-packages/starlette/testclient.py:499: in get
    return super().get(
.venv/lib/python3.12/site-packages/httpx/_client.py:1041: in get
    return self.request(
.venv/lib/python3.12/site-packages/starlette/testclient.py:465: in request
    return super().request(
.venv/lib/python3.12/site-packages/httpx/_client.py:814: in request
    return self.send(request, auth=auth, follow_redirects=follow_redirects)
.venv/lib/python3.12/site-packages/httpx/_client.py:901: in send
    response = self._send_handling_auth(
.venv/lib/python3.12/site-packages/httpx/_client.py:929: in _send_handling_auth
    response = self._send_handling_redirects(
.venv/lib/python3.12/site-packages/httpx/_client.py:966: in _send_handling_redirects
    response = self._send_single_request(request)
.venv/lib/python3.12/site-packages/httpx/_client.py:1002: in _send_single_request
    response = transport.handle_request(request)
.venv/lib/python3.12/site-packages/starlette/testclient.py:342: in handle_request
    raise exc
.venv/lib/python3.12/site-packages/starlette/testclient.py:339: in handle_request
    portal.call(self.app, scope, receive, send)
.venv/lib/python3.12/site-packages/anyio/from_thread.py:277: in call
    return cast(T_Retval, self.start_task_soon(func, *args).result())
/usr/lib/python3.12/concurrent/futures/_base.py:456: in result
    return self.__get_result()
/usr/lib/python3.12/concurrent/futures/_base.py:401: in __get_result
    raise self._exception
.venv/lib/python3.12/site-packages/anyio/from_thread.py:217: in _call_func
    retval = await retval
.venv/lib/python3.12/site-packages/fastapi/applications.py:1106: in __call__
    await super().__call__(scope, receive, send)
.venv/lib/python3.12/site-packages/starlette/applications.py:122: in __call__
    await self.middleware_stack(scope, receive, send)
.venv/lib/python3.12/site-packages/starlette/middleware/errors.py:184: in __call__
    raise exc
.venv/lib/python3.12/site-packages/starlette/middleware/errors.py:162: in __call__
    await self.app(scope, receive, _send)
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:108: in __call__
    response = await self.dispatch_func(request, call_next)
app/main.py:321: in websocket_path_compatibility
    response = await call_next(request)
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:84: in call_next
    raise app_exc
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:70: in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:108: in __call__
    response = await self.dispatch_func(request, call_next)
app/main.py:281: in workout_path_compatibility
    response = await call_next(request)
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:84: in call_next
    raise app_exc
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:70: in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:108: in __call__
    response = await self.dispatch_func(request, call_next)
app/main.py:181: in log_request
    response = await call_next(request)
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:84: in call_next
    raise app_exc
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:70: in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:108: in __call__
    response = await self.dispatch_func(request, call_next)
app/main.py:162: in fix_url_paths
    return await call_next(request)
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:84: in call_next
    raise app_exc
.venv/lib/python3.12/site-packages/starlette/middleware/base.py:70: in coro
    await self.app(scope, receive_or_disconnect, send_no_error)
.venv/lib/python3.12/site-packages/starlette/middleware/cors.py:83: in __call__
    await self.app(scope, receive, send)
.venv/lib/python3.12/site-packages/starlette/middleware/exceptions.py:79: in __call__
    raise exc
.venv/lib/python3.12/site-packages/starlette/middleware/exceptions.py:68: in __call__
    await self.app(scope, receive, sender)
.venv/lib/python3.12/site-packages/fastapi/middleware/asyncexitstack.py:20: in __call__
    raise e
.venv/lib/python3.12/site-packages/fastapi/middleware/asyncexitstack.py:17: in __call__
    await self.app(scope, receive, send)
.venv/lib/python3.12/site-packages/starlette/routing.py:718: in __call__
    await route.handle(scope, receive, send)
.venv/lib/python3.12/site-packages/starlette/routing.py:276: in handle
    await self.app(scope, receive, send)
.venv/lib/python3.12/site-packages/starlette/routing.py:66: in app
    response = await func(request)
.venv/lib/python3.12/site-packages/fastapi/routing.py:274: in app
    raw_response = await run_endpoint_function(
.venv/lib/python3.12/site-packages/fastapi/routing.py:193: in run_endpoint_function
    return await run_in_threadpool(dependant.call, **values)
.venv/lib/python3.12/site-packages/starlette/concurrency.py:41: in run_in_threadpool
    return await anyio.to_thread.run_sync(func, *args)
.venv/lib/python3.12/site-packages/anyio/to_thread.py:33: in run_sync
    return await get_asynclib().run_sync_in_worker_thread(
.venv/lib/python3.12/site-packages/anyio/_backends/_asyncio.py:877: in run_sync_in_worker_thread
    return await future
.venv/lib/python3.12/site-packages/anyio/_backends/_asyncio.py:807: in run
    result = context.run(func, *args)
app/api/v2/endpoints/chat.py:455: in poll_new_messages
    conversation = crud.crud_conversation.get_by_session_id(db, session_id=session_id)
app/crud/crud_conversation.py:35: in get_by_session_id
    return db.query(Conversation).filter(Conversation.session_id == session_id).first()
.venv/lib/python3.12/site-packages/sqlalchemy/orm/query.py:2748: in first
    return self.limit(1)._iter().first()  # type: ignore
.venv/lib/python3.12/site-packages/sqlalchemy/orm/query.py:2847: in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
.venv/lib/python3.12/site-packages/sqlalchemy/orm/session.py:2308: in execute
    return self._execute_internal(
.venv/lib/python3.12/site-packages/sqlalchemy/orm/session.py:2190: in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
.venv/lib/python3.12/site-packages/sqlalchemy/orm/context.py:293: in orm_execute_statement
    result = conn.execute(
.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py:1416: in execute
    return meth(
.venv/lib/python3.12/site-packages/sqlalchemy/sql/elements.py:516: in _execute_on_connection
    return connection._execute_clauseelement(
.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py:1639: in _execute_clauseelement
    ret = self._execute_context(
.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py:1848: in _execute_context
    return self._exec_single_context(
.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py:1988: in _exec_single_context
    self._handle_dbapi_exception(
.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py:2343: in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
.venv/lib/python3.12/site-packages/sqlalchemy/engine/base.py:1969: in _exec_single_context
    self.dialect.do_execute(
.venv/lib/python3.12/site-packages/sqlalchemy/engine/default.py:922: in do_execute
    cursor.execute(statement, parameters)
E   sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) no such table: conversations
E   [SQL: SELECT conversations.id AS conversations_id, conversations.user_id AS conversations_user_id, conversations.session_id AS conversations_session_id, conversations.start_time AS conversations_start_time, conversations.last_active AS conversations_last_active, conversations.is_active AS conversations_is_active, conversations.meta_info AS conversations_meta_info 
E   FROM conversations 
E   WHERE conversations.session_id = ?
E    LIMIT ? OFFSET ?]
E   [parameters: ('test_session_004', 1, 0)]
E   (Background on this error at: https://sqlalche.me/e/20/e3q8)</failure></testcase><testcase classname="tests.comprehensive.integration.test_api_endpoints.TestChatAPIEndpoints" name="test_generate_training_plan_endpoint" time="0.016"><failure message="assert False is True">tests/comprehensive/integration/test_api_endpoints.py:267: in test_generate_training_plan_endpoint
    assert response_data["success"] is True
E   assert False is True</failure></testcase><testcase classname="tests.comprehensive.integration.test_api_endpoints.TestChatAPIEndpoints" name="test_authentication_required" time="0.013"><failure message="assert 200 == 401&#10; +  where 200 = &lt;Response [200 OK]&gt;.status_code">tests/comprehensive/integration/test_api_endpoints.py:277: in test_authentication_required
    assert response.status_code == 401  # Unauthorized
E   assert 200 == 401
E    +  where 200 = &lt;Response [200 OK]&gt;.status_code</failure></testcase><testcase classname="tests.comprehensive.integration.test_api_endpoints.TestChatAPIEndpoints" name="test_invalid_authentication" time="0.012"><failure message="assert 200 == 401&#10; +  where 200 = &lt;Response [200 OK]&gt;.status_code">tests/comprehensive/integration/test_api_endpoints.py:289: in test_invalid_authentication
    assert response.status_code == 401  # Unauthorized
E   assert 200 == 401
E    +  where 200 = &lt;Response [200 OK]&gt;.status_code</failure></testcase><testcase classname="tests.comprehensive.integration.test_api_endpoints.TestChatAPIEndpoints" name="test_rate_limiting" time="0.108" /><testcase classname="tests.comprehensive.integration.test_api_endpoints.TestChatAPIEndpoints" name="test_request_validation" time="0.041" /><testcase classname="tests.comprehensive.integration.test_api_endpoints.TestChatAPIEndpoints" name="test_response_format_consistency" time="0.015" /><testcase classname="tests.comprehensive.integration.test_api_endpoints.TestWebSocketEndpoints" name="test_websocket_connection" time="0.002" /><testcase classname="tests.comprehensive.integration.test_api_endpoints.TestWebSocketEndpoints" name="test_websocket_message_flow" time="0.002" /><testcase classname="tests.comprehensive.integration.test_api_endpoints.TestWebSocketEndpoints" name="test_websocket_error_handling" time="0.001" /><testcase classname="tests.comprehensive.integration.test_api_endpoints.TestWebSocketEndpoints" name="test_websocket_heartbeat" time="0.008" /></testsuite></testsuites>