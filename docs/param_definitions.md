# 参数定义中心文档

## 概述

为解决健身AI助手对话系统中的参数提取、收集和一致性问题，我们实现了统一的参数定义中心。该中心集中管理所有与训练、健身相关的参数定义，避免重复定义和不一致问题。

## 主要功能

1. **统一常量定义**：集中定义所有与训练和健身相关的常量，如身体部位、器材、肌肉分类等
2. **参数收集规则**：统一定义不同意图所需的参数及其收集顺序
3. **辅助函数集合**：提供参数提取、验证、格式化等辅助函数
4. **模板系统**：提供参数提问和确认的模板化支持

## 使用方法

### 引入参数定义

```python
from app.core.param_definitions import (
    BODY_PART_CATEGORIES, MUSCLE_CATEGORIES, EQUIPMENT_CATEGORIES,
    BODY_PART_SYNONYMS, TRAINING_SCENARIOS, TRAINING_GOALS, DEFAULT_TRAINING_PARAMS,
    normalize_input, get_required_params_for_intent
)
```

### 获取参数收集顺序

```python
from app.core.param_definitions import get_param_collection_order

# 根据意图获取参数收集顺序
param_collection_order = get_param_collection_order("recommend_exercise")
```

### 获取意图所需参数

```python
from app.core.param_definitions import get_required_params_for_intent

# 获取指定意图所需的参数列表
required_params = get_required_params_for_intent("daily_workout_plan")
```

### 参数问题和确认消息

```python
from app.core.param_definitions import get_param_question, get_param_confirmation

# 生成参数问题
question = get_param_question("body_part")

# 生成参数确认
confirmation = get_param_confirmation("body_part", "胸部")
```

## 参数定义内容

### 分类常量

- `BODY_PART_CATEGORIES`：身体部位分类
- `MUSCLE_CATEGORIES`：肌肉分类
- `EQUIPMENT_CATEGORIES`：器材分类

### 同义词和别名

- `BODY_PART_SYNONYMS`：身体部位同义词
- `BODY_PART_ALIASES`：身体部位别名

### 训练场景和目标

- `TRAINING_SCENARIOS`：训练场景定义
- `TRAINING_GOALS`：训练目标定义
- `DEFAULT_TRAINING_PARAMS`：默认训练参数

### 参数收集定义

- `PARAM_COLLECTION_ORDER`：参数收集顺序
- `INTENT_REQUIRED_PARAMS`：意图与必要参数映射
- `PARAM_NAME_MAP`：参数名称映射
- `PARAM_ENUM_MAP`：参数枚举值映射

### 模板

- `PARAM_QUESTION_TEMPLATES`：参数提问模板
- `PARAM_CONFIRMATION_TEMPLATES`：参数确认模板

## 解决的问题

1. **参数提取错误**：通过更严格的词边界匹配和完整词汇检查避免误识别
2. **参数收集状态不一致**：统一意图与参数要求定义，确保一致性
3. **参数收集缺乏连贯性**：增强状态管理，确保参数在状态转换中正确保存
4. **意图处理错误**：统一构造函数参数名称，添加向后兼容处理

## 技术细节

### 文件位置

`app/core/param_definitions.py` - 统一参数定义中心

### 核心组件

- `parameter_extractor.py` - 修改为使用统一的参数定义
- `training_param_manager.py` - 修改为使用统一的参数管理
- `states.py` - 增强TrainingParamCollectionState处理逻辑
- `intent_handler.py` - 添加参数兼容处理

### 扩展

如需添加新的参数类型或意图定义，请在参数定义中心添加，然后更新相应的辅助函数。 