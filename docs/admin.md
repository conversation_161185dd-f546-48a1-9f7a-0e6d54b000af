# 管理后台文档

## 概述

系统提供了一个内置的管理后台，用于数据监控和管理。管理后台基于Jinja2模板引擎构建，提供友好的用户界面进行各项管理操作。

## 访问管理后台

- URL: http://localhost:8000/admin/
- 默认凭据: 
  - 用户名: admin
  - 密码: sciencefit2025

## 环境配置

管理后台的行为受环境配置影响：
- 开发环境（IS_DEV=True）：无需认证即可访问
- 生产环境（IS_DEV=False）：需要 HTTP 基本认证

修改环境配置：
```python
# app/core/config.py
IS_DEV: bool = True  # 开发环境
# 或
IS_DEV: bool = False  # 生产环境
```

## 管理后台功能

### 数据概览

- 用户总数统计
- 新增用户趋势
- 活跃用户数量
- 分享操作统计
- 系统运行状态监控

### 用户管理

- 用户列表查看
- 用户信息搜索
- 用户状态管理（启用/禁用）
- 用户资料详情查看

### 用户设置管理

- 查看用户设置
- 批量更新设置
- 设置模板管理

### 分享追踪管理

- 分享事件列表
- 分享统计报表
- 小程序码管理

## 自定义管理员凭据

可以通过环境变量自定义管理员凭据：
```bash
export ADMIN_USERNAME=your_username
export ADMIN_PASSWORD=your_password
```

或者在配置文件中修改：
```python
# app/core/config.py
ADMIN_USERNAME: str = os.environ.get("ADMIN_USERNAME", "admin")
ADMIN_PASSWORD: str = os.environ.get("ADMIN_PASSWORD", "sciencefit2025")
```

## 管理后台架构

### 目录结构

```
app/
├── api/
│   └── admin/            # 管理后台API
│       ├── admin_auth.py # 管理员认证
│       └── admin_router.py # 管理后台路由
└── templates/            # 管理后台模板
    └── admin/
        ├── base.html     # 基础模板
        ├── dashboard.html # 仪表盘
        ├── login.html    # 登录页面
        ├── user_list.html # 用户列表
        ├── user_detail.html # 用户详情
        ├── settings.html # 设置管理
        └── share_tracking.html # 分享追踪
```

### 认证机制

管理后台使用HTTP基本认证（Basic Authentication）进行认证：

```python
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBasic, HTTPBasicCredentials
from app.core.config import settings

security = HTTPBasic()

def admin_auth(credentials: HTTPBasicCredentials = Depends(security)):
    """管理后台认证依赖"""
    is_username_correct = credentials.username == settings.ADMIN_USERNAME
    is_password_correct = credentials.password == settings.ADMIN_PASSWORD
    
    if not (is_username_correct and is_password_correct):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid credentials",
            headers={"WWW-Authenticate": "Basic"},
        )
    
    return credentials.username
```

### 路由定义

管理后台路由示例：

```python
from fastapi import APIRouter, Depends, Request
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from app.api.admin.admin_auth import admin_auth
from app.core.config import settings

router = APIRouter(prefix="/admin", tags=["admin"])
templates = Jinja2Templates(directory="app/templates")

@router.get("/", response_class=HTMLResponse)
async def admin_dashboard(request: Request, username: str = Depends(admin_auth) if not settings.IS_DEV else None):
    """管理后台仪表盘"""
    # 获取统计数据
    stats = {
        "total_users": 100,
        "active_users": 85,
        "new_users_today": 5,
        "total_shares": 250
    }
    
    return templates.TemplateResponse(
        "admin/dashboard.html", 
        {"request": request, "stats": stats}
    )
```

## 数据可视化

管理后台使用Chart.js实现数据可视化，支持以下图表类型：

- 柱状图：用户增长趋势
- 饼图：用户性别分布
- 线图：活跃用户趋势
- 雷达图：用户活动水平分布

图表示例配置：

```javascript
// 用户增长趋势图
const ctx = document.getElementById('userGrowthChart').getContext('2d');
const userGrowthChart = new Chart(ctx, {
    type: 'bar',
    data: {
        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
        datasets: [{
            label: '新用户数',
            data: [12, 19, 3, 5, 2, 3],
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }]
    },
    options: {
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
```

## 常见操作

### 查看用户列表

1. 登录管理后台
2. 点击左侧导航栏的"用户管理"
3. 可使用顶部搜索框搜索用户
4. 点击用户行查看详情

### 导出用户数据

1. 进入用户列表页面
2. 点击右上角"导出"按钮
3. 选择导出格式（CSV/Excel）
4. 点击确认导出 