# 认证模块文档

## 认证机制

系统使用JWT（JSON Web Token）实现用户认证，支持：
- 微信小程序登录认证
- 基于令牌的验证
- 自动创建新用户
- 令牌刷新

### 登录流程

1. 用户通过微信小程序获取临时code
2. 后端调用微信API获取用户openid
3. 基于openid创建或验证用户身份
4. 生成JWT令牌并返回给前端

### 认证接口优化

系统认证接口已进行以下优化：

#### 敏感信息处理增强
- 统一敏感数据处理机制，屏蔽日志中的敏感信息
- 实现一致的隐私保护策略，如对openid、token等信息进行部分显示
- **安全增强**: 确保敏感信息如session_key不会通过API传输，防止数据泄露

#### Token管理功能
- **Token刷新接口**: 允许客户端在令牌接近过期时请求新令牌，无需重新登录
- **Token有效期配置**: 支持通过环境变量调整令牌有效期
- **Token撤销机制**: 允许用户登出时使当前令牌失效

#### 日志处理优化
- 集中化日志处理逻辑，减少代码重复
- 结构化日志输出，便于分析和监控
- 针对认证过程细化日志级别，提高问题排查效率

#### 异常处理增强
- 细化认证错误类型，如令牌过期、格式无效等
- 向客户端返回更明确的错误消息
- 增加安全保护，防止敏感信息泄露

## 认证相关接口

### 微信小程序登录

```
POST /auth/login/wechat
```

**请求参数**

```json
{
  "code": "string",                     // 必填，微信临时登录凭证
  "userInfo": {                         // 可选，用户信息对象
    "nickname": "微信用户",
    "avatar_url": "https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132"
  },
  "encryptedData": "string",            // 可选，微信加密数据
  "iv": "string",                       // 可选，加密算法的初始向量
  "openid": "string",                   // 可选，用户openid
  "unionid": "string"                   // 可选，用户unionid
}
```

**响应内容**

```json
{
  "success": true,
  "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "expires_at": "2024-05-06T12:00:00",
  "user": {
    "id": 1,
    "nickname": "微信用户",
    "avatarUrl": "https://example.com/avatar.jpg",
    "completed": false,
    "created_at": "2024-04-05T12:00:00"
  },
  "is_new_user": false
}
```

### 获取微信OpenID

```
POST /auth/wechat/code2session
```

**请求参数**

```json
{
  "code": "string"    // 必填，微信临时登录凭证code
}
```

**响应内容**

```json
{
  "openid": "ofjsoFJSOIFJOIsfjiosfjios",    // 用户openid
  "unionid": "uoijsoifjsoFISOJFOISjfoijs"    // 可选，用户unionid
}
```

**说明**

- **安全性增强**：接口不再返回session_key，所有敏感操作在服务端完成
- 返回的openid可用于存储用户标识和后续登录验证
- unionid仅在微信开放平台绑定情况下返回

**错误响应**

请求无效:
```json
{
  "detail": "微信code2session处理失败: 错误信息"
}
```

### 刷新访问令牌

```
POST /auth/refresh-token
```

**请求头**
- Authorization: Bearer {token}

**响应内容**

```json
{
  "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "expires_at": "2024-05-06T12:00:00"
}
```

**错误响应**

令牌无效:
```json
{
  "detail": {
    "detail": "无效的访问令牌", 
    "code": "invalid_token"
  }
}
```

用户不存在:
```json
{
  "detail": {
    "detail": "用户不存在", 
    "code": "user_not_found"
  }
}
```

用户已禁用:
```json
{
  "detail": {
    "detail": "用户已禁用", 
    "code": "user_inactive"
  }
}
```

### 用户登出

```
POST /auth/logout
```

**请求头**
- Authorization: Bearer {token}

**响应内容**

```json
{
  "success": true,
  "message": "登出成功"
}
```

### 绑定微信手机号

```
POST /auth/wechat/phone
```

**请求参数**

```json
{
  "code": "string",                    // 必填，微信临时登录凭证
  "encrypted_data": "string",          // 必填，微信加密数据
  "iv": "string"                       // 必填，加密算法的初始向量
}
```

**响应内容**

```json
{
  "success": true,
  "message": "手机号绑定成功",
  "phone": "13800138000"
}
```

**说明**

- **安全加强**：所有敏感信息(如session_key)仅在服务器端处理，确保不会泄露到客户端
- 手机号加密数据由微信提供，在服务端使用session_key解密
- 该接口实现了完整的异常处理和日志脱敏，保护用户隐私
- 用户手机号解密、存储和关联操作全部在服务端安全完成

**错误响应**

手机号解密失败:
```json
{
  "detail": "手机号解密失败，请重试"
}
```

用户不存在:
```json
{
  "detail": "用户不存在，请先登录"
}
```

服务器错误:
```json
{
  "detail": "服务器内部错误，请稍后再试"
}
```

## 实现示例

### 刷新Token接口示例

```python
@router.post("/refresh-token", response_model=TokenResponse)
def refresh_token(
    current_token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
):
    """刷新访问令牌接口"""
    try:
        # 解析当前token
        payload = jwt.decode(current_token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        user_id = payload.get("sub")
        if not user_id:
            raise HTTPException(status_code=401, detail="无效的令牌")
            
        # 创建新token
        access_token_expires = timedelta(days=settings.ACCESS_TOKEN_EXPIRE_DAYS)
        new_token = create_access_token(data={"sub": user_id}, expires_delta=access_token_expires)
        
        return {"token": new_token, "expires_at": (datetime.utcnow() + access_token_expires).isoformat()}
    except JWTError:
        raise HTTPException(status_code=401, detail="无效的令牌")
```

## 安全配置

### JWT配置
```python
# app/core/config.py
SECRET_KEY: str = os.environ.get("SECRET_KEY", "your_secret_key_here")
ALGORITHM: str = "HS256"
ACCESS_TOKEN_EXPIRE_DAYS: int = 30
```

### 令牌生成

```python
def create_access_token(data: dict, expires_delta: timedelta = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(days=settings.ACCESS_TOKEN_EXPIRE_DAYS)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt
```

### 令牌验证

```python
async def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail={"detail": "无效的访问令牌", "code": "invalid_token"},
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        user_id: str = payload.get("sub")
        if user_id is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    
    user = db.query(User).filter(User.id == user_id).first()
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail={"detail": "用户不存在", "code": "user_not_found"},
        )
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail={"detail": "用户已禁用", "code": "user_inactive"},
        )
    return user
``` 