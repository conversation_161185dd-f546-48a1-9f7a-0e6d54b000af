# 开发指南

## 环境设置

### 开发环境要求

- Python 3.11+
- PostgreSQL 15
- Redis 7
- Git
- 代码编辑器（推荐：VS Code、PyCharm）

### 本地开发环境设置

1. 克隆代码库
   ```bash
   git clone <repository-url>
   cd backend
   ```

2. 创建并激活虚拟环境
   ```bash
   python -m venv venv
   source venv/bin/activate  # Linux/Mac
   # 或
   .\venv\Scripts\activate  # Windows
   ```

3. 安装依赖
   ```bash
   pip install -r requirements.txt
   
   # 安装开发依赖
   pip install -r requirements-dev.txt
   ```

4. 配置环境变量
   ```bash
   cp .env.example .env
   # 编辑.env文件，设置本地环境变量
   ```

5. 启动开发服务器
   ```bash
   uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
   ```

## 代码结构

```
backend/
├── app/                     # 应用主目录
│   ├── api/                 # API相关代码
│   │   ├── admin/           # 管理后台API
│   │   ├── endpoints/       # API端点
│   │   └── v1/              # V1版本API
│   ├── core/                # 核心功能
│   │   ├── config.py        # 应用配置
│   │   ├── security.py      # 安全相关
│   │   └── errors.py        # 错误处理
│   ├── db/                  # 数据库相关
│   │   ├── base.py          # 基础模型
│   │   └── session.py       # 数据库会话
│   ├── models/              # 数据模型
│   │   ├── user.py          # 用户模型
│   │   ├── settings.py      # 设置模型
│   │   └── share.py         # 分享模型
│   ├── schemas/             # 数据验证模式
│   │   ├── user.py          # 用户数据模式
│   │   └── token.py         # 令牌数据模式
│   ├── services/            # 业务逻辑服务
│   │   ├── user.py          # 用户服务
│   │   └── wechat.py        # 微信服务
│   ├── templates/           # HTML模板
│   │   └── admin/           # 管理后台模板
│   ├── utils/               # 工具函数
│   │   ├── file.py          # 文件处理
│   │   ├── image.py         # 图像处理
│   │   └── logger.py        # 日志工具
│   └── main.py              # 应用入口
├── static/                  # 静态文件
├── tests/                   # 测试代码
├── alembic/                 # 数据库迁移
├── .env.example             # 环境变量示例
├── .gitignore               # Git忽略文件
├── alembic.ini              # Alembic配置
├── docker-compose.yml       # Docker配置
├── Dockerfile               # Docker构建文件
├── README.md                # 项目说明
└── requirements.txt         # 依赖包
```

## 开发流程

### 分支管理

项目使用Git Flow工作流：

- `main`: 生产环境分支，保持稳定
- `develop`: 开发主分支，包含最新的开发成果
- `feature/*`: 特性分支，用于开发新功能
- `bugfix/*`: 修复分支，用于修复非生产环境的bug
- `hotfix/*`: 热修复分支，用于修复生产环境的bug
- `release/*`: 发布分支，用于准备新版本发布

### 开发新功能

1. 从`develop`分支创建新的特性分支
   ```bash
   git checkout develop
   git pull
   git checkout -b feature/new-feature-name
   ```

2. 在特性分支上进行开发
3. 提交代码并推送到远程仓库
   ```bash
   git add .
   git commit -m "feat: add new feature"
   git push origin feature/new-feature-name
   ```

4. 创建Pull Request合并到`develop`分支
5. 代码审查通过后合并

### 提交规范

项目使用Angular风格的提交消息格式：

```
<type>(<scope>): <subject>

<body>

<footer>
```

- **type**: 提交类型
  - `feat`: 新功能
  - `fix`: 修复bug
  - `docs`: 文档更新
  - `style`: 代码样式调整，不影响代码逻辑
  - `refactor`: 重构代码
  - `test`: 测试相关
  - `chore`: 构建过程或辅助工具的变动
- **scope**: 变更范围（可选）
- **subject**: 简短描述
- **body**: 详细描述（可选）
- **footer**: 关闭Issue等信息（可选）

示例：
```
feat(user): add user profile update endpoint

Implement the API endpoint for updating user profile information.
The endpoint accepts user details and updates the database.

Closes #123
```

## 测试

### 单元测试

项目使用pytest进行单元测试：

```bash
# 运行所有测试
pytest

# 运行特定模块的测试
pytest tests/test_user.py

# 运行特定测试
pytest tests/test_user.py::test_create_user
```

### 测试覆盖率

使用pytest-cov生成测试覆盖率报告：

```bash
# 生成HTML格式的覆盖率报告
pytest --cov=app --cov-report=html

# 查看覆盖率报告（结果在htmlcov目录）
open htmlcov/index.html
```

## 日志系统

系统使用Python的logging模块记录日志。日志文件默认保存在项目根目录下的logs.txt文件中。

日志配置示例：

```python
# app/utils/logger.py
import logging
import sys
from pathlib import Path

def setup_logger():
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # 创建根日志记录器
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(logging.Formatter(log_format))
    logger.addHandler(console_handler)
    
    # 文件处理器
    log_file = Path("logs.txt")
    file_handler = logging.FileHandler(log_file)
    file_handler.setFormatter(logging.Formatter(log_format))
    logger.addHandler(file_handler)
    
    return logger
```

使用日志记录器：

```python
import logging

logger = logging.getLogger(__name__)

def some_function():
    try:
        # 执行某些操作
        logger.info("操作成功完成")
    except Exception as e:
        logger.error(f"发生错误: {str(e)}", exc_info=True)
```

## 错误处理

系统实现了统一的错误处理机制：

```python
# app/core/errors.py
from fastapi import Request, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError

async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """处理请求验证错误"""
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={"detail": exc.errors()},
    )

async def http_exception_handler(request: Request, exc: HTTPException):
    """处理HTTP异常"""
    headers = getattr(exc, "headers", None)
    if headers:
        return JSONResponse(
            {"detail": exc.detail},
            status_code=exc.status_code,
            headers=headers,
        )
    return JSONResponse(
        {"detail": exc.detail},
        status_code=exc.status_code,
    )

async def general_exception_handler(request: Request, exc: Exception):
    """处理通用异常"""
    logger.error(f"未捕获的异常: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={"detail": "服务器内部错误"},
    )
```

## API文档

系统使用FastAPI的自动生成API文档功能：

- Swagger UI: http://localhost:8000/api/v1/docs
- ReDoc: http://localhost:8000/api/v1/redoc
- OpenAPI JSON: http://localhost:8000/api/v1/openapi.json

自定义API文档配置：

```python
# app/api/openapi_docs.py
from fastapi.openapi.utils import get_openapi

def custom_openapi(app):
    """自定义OpenAPI文档"""
    if app.openapi_schema:
        return app.openapi_schema
    
    openapi_schema = get_openapi(
        title="智能健身教练API",
        version="1.0.0",
        description="智能健身教练小程序后端API文档",
        routes=app.routes,
    )
    
    # 修改架构以添加授权
    openapi_schema["components"]["securitySchemes"] = {
        "Bearer": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT",
        }
    }
    
    app.openapi_schema = openapi_schema
    return app.openapi_schema
```

## 代码质量

### 使用linters和formatters

项目推荐使用以下工具：

- **flake8**: Python代码风格检查
- **black**: Python代码格式化
- **isort**: 导入语句排序

安装开发工具：

```bash
pip install flake8 black isort
```

使用这些工具：

```bash
# 运行flake8检查代码
flake8 app

# 使用black格式化代码
black app

# 排序导入
isort app
```

### 代码质量配置

项目包含常用linter的配置文件：

**.flake8**:
```ini
[flake8]
max-line-length = 100
exclude = .git,__pycache__,build,dist
```

**pyproject.toml**:
```toml
[tool.black]
line-length = 100
target-version = ['py39']
include = '\.pyi?$'
exclude = '''
/(
    \.git
  | \.mypy_cache
  | \.venv
  | venv
  | migrations
)/
'''

[tool.isort]
profile = "black"
line_length = 100
```

## 依赖管理

### 添加新依赖

```bash
# 安装新依赖
pip install new-package

# 更新requirements.txt
pip freeze > requirements.txt
```

### 依赖分组

对于不同环境的依赖，可以创建多个requirements文件：

- `requirements.txt`: 基本依赖
- `requirements-dev.txt`: 开发环境依赖
- `requirements-test.txt`: 测试环境依赖

## 常见开发任务

### 创建新的API端点

1. 在`app/api/endpoints/`目录下创建或编辑相应的路由文件：

```python
# app/api/endpoints/example.py
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api.deps import get_db, get_current_user
from app.models.user import User
from app.schemas.example import ExampleCreate, ExampleResponse

router = APIRouter()

@router.post("/examples/", response_model=ExampleResponse)
def create_example(
    data: ExampleCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建示例资源"""
    # 业务逻辑实现
    return {"id": 1, "name": data.name, "created_by": current_user.id}
```

2. 在`app/api/v1/api.py`中注册路由：

```python
# app/api/v1/api.py
from fastapi import APIRouter

from app.api.endpoints import auth, user, example

api_router = APIRouter()
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(user.router, prefix="/user", tags=["users"])
api_router.include_router(example.router, tags=["examples"])
```

### 创建新的数据模型

1. 在`app/models/`目录下创建模型文件：

```python
# app/models/example.py
from sqlalchemy import Column, Integer, String, ForeignKey
from sqlalchemy.orm import relationship

from app.db.base import Base

class Example(Base):
    __tablename__ = "examples"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    description = Column(String, nullable=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    
    # 关系
    user = relationship("User", back_populates="examples")
```

2. 更新用户模型中的关系：

```python
# app/models/user.py
# 添加关系
examples = relationship("Example", back_populates="user")
```

3. 在`app/schemas/`目录下创建相应的Pydantic模型：

```python
# app/schemas/example.py
from typing import Optional
from pydantic import BaseModel

class ExampleBase(BaseModel):
    name: str
    description: Optional[str] = None

class ExampleCreate(ExampleBase):
    pass

class ExampleUpdate(ExampleBase):
    name: Optional[str] = None

class ExampleInDBBase(ExampleBase):
    id: int
    user_id: int

    class Config:
        orm_mode = True

class Example(ExampleInDBBase):
    pass

class ExampleResponse(ExampleInDBBase):
    pass
```

4. 创建数据库迁移：

```bash
alembic revision --autogenerate -m "Add examples table"
alembic upgrade head
```

## 发布流程

1. 从`develop`分支创建`release`分支
   ```bash
   git checkout develop
   git pull
   git checkout -b release/v1.0.0
   ```

2. 在`release`分支上进行最终测试和修复
3. 合并到`main`分支并打标签
   ```bash
   git checkout main
   git merge release/v1.0.0
   git tag -a v1.0.0 -m "Version 1.0.0"
   git push origin main --tags
   ```

4. 合并回`develop`分支
   ```bash
   git checkout develop
   git merge release/v1.0.0
   git push origin develop
   ``` 