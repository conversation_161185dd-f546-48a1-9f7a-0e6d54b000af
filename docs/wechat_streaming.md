# 微信小程序聊天流式响应实现方案

由于微信小程序环境对WebSocket连接的限制，本文档提供了一种替代方案，使用HTTP轮询实现类似WebSocket的实时通信效果。

## 实现原理

当WebSocket连接不稳定或无法使用时，我们可以使用以下替代方案：

1. **建立会话**: 通过HTTP请求建立初始会话连接
2. **发送消息**: 使用POST请求发送用户消息
3. **异步处理**: 服务器异步处理AI回复并存储
4. **定时轮询**: 客户端定期轮询获取新消息

这种方案虽然不如WebSocket高效，但在微信小程序等特殊环境中是一种有效的替代方案。

## 技术实现

### 后端实现

后端需要提供以下API端点：

1. **初始化会话**: `GET /api/v1/chat/stream/{session_id}/connect`
2. **发送消息**: `POST /api/v1/chat/stream/{session_id}/message`
3. **轮询新消息**: `GET /api/v1/chat/stream/{session_id}/poll`

这些端点工作方式如下：

#### 1. 初始化会话

```http
GET /api/v1/chat/stream/{session_id}/connect
Authorization: Bearer {token}
```

- 创建或验证一个带有`session_id`的会话
- 返回成功响应，确认会话已建立

#### 2. 发送消息

```http
POST /api/v1/chat/stream/{session_id}/message
Authorization: Bearer {token}
Content-Type: application/json

{
  "message": "用户消息内容"
}
```

- 保存用户消息
- 在后台异步处理AI回复
- 返回已保存的用户消息

#### 3. 轮询新消息

```http
GET /api/v1/chat/stream/{session_id}/poll?last_message_id={id}
Authorization: Bearer {token}
```

- 获取指定会话中比`last_message_id`更新的所有消息
- 返回新消息列表

### 前端实现

前端需要实现以下功能：

1. **初始化聊天**:
   - 创建会话或使用已有会话ID
   - 调用`connect`端点确认会话有效
   - 启动消息轮询

2. **轮询机制**:
   - 使用`setInterval`定期调用`poll`端点
   - 跟踪最后收到的消息ID
   - 处理并显示新收到的消息

3. **发送消息**:
   - 使用`message`端点发送用户消息
   - 在UI上立即显示用户消息
   - 等待轮询获取AI回复

## 示例代码

### 前端示例代码

可以参考 `docs/examples/wechat_chat_polling.js` 文件，其中包含了完整的微信小程序聊天组件示例。

#### 关键代码示例：

```javascript
// 初始化聊天并开始轮询
async function initChat() {
  // 连接到会话...
  startMessagePolling();
}

// 轮询获取新消息
async function pollMessages() {
  // 请求新消息...
  // 根据消息角色处理不同类型的消息...
}

// 发送消息
async function sendMessage(messageText) {
  // 显示用户消息...
  // 发送到服务器...
  // 轮询会自动获取AI回复...
}
```

## 实现注意事项

1. **轮询频率**:
   - 建议轮询间隔为1-3秒，平衡实时性和服务器负载
   - 考虑实现自适应轮询间隔，根据消息频率动态调整

2. **断线处理**:
   - 跟踪请求失败，实现重连机制
   - 保存`last_message_id`以恢复会话状态

3. **会话管理**:
   - 在本地存储中保存`session_id`，允许跨页面保持会话
   - 实现会话超时和清理机制

4. **错误处理**:
   - 优雅处理网络错误
   - 提供用户友好的错误提示
   - 实现自动重试机制

5. **性能优化**:
   - 避免在没有活动时频繁轮询
   - 考虑实现指数退避策略

## 与WebSocket对比

| 功能 | HTTP轮询 | WebSocket |
|------|---------|----------|
| 实时性 | 中等（取决于轮询频率） | 高 |
| 资源消耗 | 较高（频繁的HTTP请求） | 较低（保持单一连接） |
| 可靠性 | 高（HTTP重试机制） | 中等（可能断连） |
| 实现复杂度 | 中等 | 中等 |
| 适用环境 | 广泛（包括受限环境） | 需要WebSocket支持 |

## 总结

通过HTTP轮询方式可以有效地模拟WebSocket功能，为微信小程序等特殊环境提供流畅的聊天体验。虽然与真正的WebSocket相比有一定的性能损失，但可以通过优化轮询策略来最小化这种影响。

此外，这种方案还提供了额外的可靠性，因为它基于标准的HTTP请求，不易受到网络环境的影响。 