 

# 饮食分析系统简介

## 系统概述

饮食分析系统是一个专业的营养追踪和分析平台，帮助用户记录、分析和优化其日常饮食习惯。系统集成了食品数据库、AI食物识别、营养分析和健康建议等功能，为用户提供全面的饮食健康管理解决方案。

## 核心功能

1. **食品数据库查询**
   - 支持食品名称、类别、类型的多维度搜索
   - 提供丰富的食品营养数据，包括热量、蛋白质、脂肪、碳水化合物等详细信息
   - 支持按类别和类型浏览食品数据

2. **餐食记录管理**
   - 支持创建、更新和删除餐食记录
   - 按日期查询和管理餐食记录
   - 支持添加和删除餐食中的食物项
   - 自动计算餐食的总营养摄入量

3. **AI食物识别**
   - 通过拍照快速识别食物
   - 支持多食物同时识别
   - 提供识别结果确认和修正功能
   - 自动生成餐食记录并关联营养数据

4. **营养分析统计**
   - 提供每日营养摄入汇总
   - 计算主要营养素(蛋白质、脂肪、碳水化合物)比例
   - 分析营养素摄入是否平衡

## 数据模型

### 食品数据模型
- **Food**: 食品基本信息，包含名称、编码、类别、类型等基础信息
- **NutritionalProfile**: 食品营养概况，包含健康信号灯评级、热量、主要营养素比例等
- **FoodNutrientValue**: 食品营养素明细，记录每种营养素的具体数值
- **FoodUnit**: 食品计量单位，定义食品的不同计量方式及对应重量

### 餐食记录模型
- **MealRecord**: 餐食记录，包含日期、餐食类型、图片、总营养素等信息
- **FoodItem**: 食物项，记录餐食中的每个食物及其数量、重量等信息
- **FoodItemNutrientIntake**: 食物项营养素摄入，详细记录该食物项提供的各营养素摄入量
- **HealthRecommendation**: 健康建议，基于餐食分析生成的健康饮食建议

## API接口

### 食品数据接口
- `GET /food/`: 获取食品列表，支持名称、类别、类型筛选
- `GET /food/categories`: 获取所有食品类别
- `GET /food/types`: 获取所有食品类型
- `GET /food/{food_id}`: 获取特定食品详情
- `GET /food/code/{code}`: 通过编码获取食品详情
- `POST /food/`: 创建新食品(管理员)
- `PUT /food/{food_id}`: 更新食品信息(管理员)
- `DELETE /food/{food_id}`: 删除食品(管理员)

### 餐食记录接口
- `GET /meal/`: 获取用户餐食记录列表，支持日期范围筛选
- `GET /meal/daily`: 获取每日营养摄入汇总
- `GET /meal/{meal_id}`: 获取特定餐食详情
- `POST /meal/`: 创建新餐食记录
- `PUT /meal/{meal_id}`: 更新餐食记录
- `DELETE /meal/{meal_id}`: 删除餐食记录
- `POST /meal/{meal_id}/food-items`: 向餐食添加食物项
- `DELETE /meal/food-items/{food_item_id}`: 从餐食移除食物项

### 食物识别接口
- `POST /food-recognition/analyze`: 分析食物图片，识别食物项
- `POST /food-recognition/{recognition_id}/confirm`: 确认或修正食物识别结果
- `GET /food-recognition/pending`: 获取用户待确认的食物识别记录

## 错误处理

系统实现了全面的错误处理机制：

1. **HTTP请求异常**
   - 404: 资源不存在（如食品不存在、餐食记录不存在等）
   - 403: 权限不足（如非管理员操作、访问他人餐食记录等）
   - 400: 请求参数错误（如创建已存在的食品编码等）
   - 500: 服务器内部错误（如食物识别失败等）

2. **全局异常日志**
   - 系统自动记录所有异常信息，包括详细的堆栈跟踪
   - 在开发环境提供更详细的错误信息，便于调试
   - 生产环境返回友好的错误提示，保护系统安全

3. **业务逻辑验证**
   - 食品数据修改权限控制
   - 用户餐食记录访问权限验证
   - 食物识别结果确认权限验证

## 系统架构

该系统基于FastAPI框架开发，采用现代化的API设计，支持异步处理和依赖注入。系统架构遵循清晰的分层设计：

- **API层**: 处理HTTP请求，参数验证和权限控制
- **服务层**: 实现核心业务逻辑，如食物识别、餐食分析等
- **CRUD层**: 提供数据库操作的抽象接口
- **模型层**: 定义数据模型和关联关系
- **模式层**: 使用Pydantic进行数据验证和序列化

系统支持PostgreSQL数据库，实现了完整的数据模型关联和级联操作。
