# 健身动作模块文档

## 健身动作数据模型

### Exercise（健身动作基本信息）

| 字段 | 类型 | 说明 |
|------|------|------|
| id | Integer | 主键ID |
| name | String(100) | 动作名称（中文） |
| en_name | String(100) | 动作英文名称 |
| body_part_id | ARRAY(Integer) | 身体部位ID数组 |
| equipment_id | ARRAY(Integer) | 器材ID数组 |
| image_name | String(255) | 图片文件名 |
| gif_url | String(255) | GIF动图URL |
| description | Text | 动作描述 |
| level | SmallInteger | 难度级别 |
| sort_priority | Integer | 排序优先级 |
| user_id | String(50) | 创建用户ID |
| exercise_type | String(50) | 动作类型 |
| hit_time | Integer | 点击次数/热度 |
| created_at | DateTime | 创建时间 |
| updated_at | DateTime | 更新时间 |

**索引**：
- idx_exercise_name_en_name：名称和英文名复合索引
- idx_exercise_level_hit_time：难度和热度复合索引
- idx_body_part_id_gin：身体部位GIN索引（针对数组字段优化）
- idx_equipment_id_gin：器材GIN索引（针对数组字段优化）
- idx_exercise_name_lower：名称小写索引（不区分大小写搜索）
- idx_exercise_en_name_lower：英文名小写索引
- idx_popular_exercises：热门动作索引（针对hit_time优化）
- idx_exercise_sort_priority：排序优先级索引

### ExerciseDetail（健身动作详细信息）

| 字段 | 类型 | 说明 |
|------|------|------|
| id | Integer | 主键ID |
| exercise_id | Integer | 关联的动作ID（外键） |
| target_muscles_id | ARRAY(Integer) | 目标肌肉ID数组 |
| synergist_muscles_id | ARRAY(Integer) | 协同肌肉ID数组 |
| ex_instructions | ARRAY(Text) | 动作指导说明数组 |
| exercise_tips | ARRAY(Text) | 动作提示数组 |
| video_file | String(255) | 视频文件名 |
| is_public | Boolean | 是否公开（默认True） |
| created_at | DateTime | 创建时间 |
| updated_at | DateTime | 更新时间 |

**索引**：
- idx_target_muscles_gin：目标肌肉GIN索引
- idx_synergist_muscles_gin：协同肌肉GIN索引
- idx_exercise_detail_public：动作ID和公开状态复合索引

### Muscle（肌肉信息）

| 字段 | 类型 | 说明 |
|------|------|------|
| id | Integer | 主键ID |
| name | String(50) | 肌肉名称（中文） |
| en_name | String(50) | 肌肉英文名称 |

**索引**：
- idx_muscle_name：肌肉名称索引
- idx_muscle_name_lower：肌肉名称小写索引

### BodyPart（身体部位）

| 字段 | 类型 | 说明 |
|------|------|------|
| id | Integer | 主键ID |
| name | String(50) | 身体部位名称 |

**索引**：
- idx_body_part_name_lower：身体部位名称小写索引

### Equipment（器材）

| 字段 | 类型 | 说明 |
|------|------|------|
| id | Integer | 主键ID |
| name | String(50) | 器材名称 |

**索引**：
- idx_equipment_name_lower：器材名称小写索引

## API接口

### 获取动作列表

```
GET /exercises/
```

**请求参数**：
- body_part_id (可选): 身体部位ID过滤
- equipment_id (可选): 器材ID过滤
- skip (默认0): 分页起始位置
- limit (默认100): 分页大小

**响应内容**：
```json
[
  {
    "id": 1,
    "name": "杠铃卧推",
    "en_name": "Barbell Bench Press",
    "body_part_id": [1, 2],
    "equipment_id": [1],
    "image_name": "bench_press.jpg",
    "gif_url": "/api/v1/gif/bench_press.gif",
    "description": "一个针对胸肌的基础复合动作",
    "level": 2,
    "sort_priority": 100,
    "user_id": "admin",
    "exercise_type": "strength",
    "hit_time": 1520,
    "created_at": "2024-04-05T12:00:00",
    "updated_at": "2024-04-05T12:00:00"
  }
]
```

### 搜索动作

```
GET /exercises/search
```

**请求参数**：
- keyword (可选): 搜索关键词（支持动作名称、描述等）
- body_part_id (可选): 身体部位ID
- equipment_id (可选): 器材ID
- muscle_id (可选): 目标肌肉ID
- difficulty (可选): 难度级别
- skip (默认0): 分页起始位置
- limit (默认5): 分页大小

**响应内容**：
与获取动作列表相同格式的动作数组。

### 创建动作

```
POST /exercises/
```

**请求头**：
- Authorization: Bearer {token}

**请求体**：
```json
{
  "name": "杠铃卧推",
  "en_name": "Barbell Bench Press",
  "body_part_id": [1, 2],
  "equipment_id": [1],
  "image_name": "bench_press.jpg",
  "gif_url": "/api/v1/gif/bench_press.gif",
  "description": "一个针对胸肌的基础复合动作",
  "level": 2,
  "sort_priority": 100,
  "exercise_type": "strength"
}
```

**响应内容**：
创建的动作对象，包含ID和时间戳等信息。

### 获取单个动作

```
GET /exercises/{exercise_id}
```

**路径参数**：
- exercise_id: 动作ID

**响应内容**：
单个动作对象的详细信息。

### 动作点击计数

```
POST /exercises/{exercise_id}/hit
```

**路径参数**：
- exercise_id: 动作ID

**响应内容**：
```json
{
  "success": true,
  "message": "Hit count updated",
  "hit_time": 1521
}
```

### 更新动作

```
PUT /exercises/{exercise_id}
```

**请求头**：
- Authorization: Bearer {token}

**路径参数**：
- exercise_id: 动作ID

**请求体**：
与创建动作相同，字段可部分提供。

**响应内容**：
更新后的动作对象。

### 删除动作

```
DELETE /exercises/{exercise_id}
```

**请求头**：
- Authorization: Bearer {token}

**路径参数**：
- exercise_id: 动作ID

**响应内容**：
```json
{
  "success": true,
  "message": "Exercise deleted successfully"
}
```

### 获取动作详情

```
GET /exercises/{exercise_id}/detail
```

**路径参数**：
- exercise_id: 动作ID

**响应内容**：
```json
{
  "id": 1,
  "exercise_id": 1,
  "target_muscles_id": [1, 2],
  "synergist_muscles_id": [3, 4],
  "ex_instructions": ["将杠铃握在肩膀宽度的位置", "躺在平板凳上，脚平放在地面"],
  "exercise_tips": ["保持肩胛骨稳定", "不要过度拱起腰部"],
  "video_file": "bench_press.mp4",
  "is_public": true,
  "created_at": "2024-04-05T12:00:00",
  "updated_at": "2024-04-05T12:00:00"
}
```

### 创建动作详情

```
POST /exercises/{exercise_id}/detail
```

**请求头**：
- Authorization: Bearer {token}

**路径参数**：
- exercise_id: 动作ID

**请求体**：
```json
{
  "target_muscles_id": [1, 2],
  "synergist_muscles_id": [3, 4],
  "ex_instructions": ["将杠铃握在肩膀宽度的位置", "躺在平板凳上，脚平放在地面"],
  "exercise_tips": ["保持肩胛骨稳定", "不要过度拱起腰部"],
  "video_file": "bench_press.mp4",
  "is_public": true
}
```

**响应内容**：
创建的动作详情对象。

### 更新动作详情

```
PUT /exercises/{exercise_id}/detail
```

**请求头**：
- Authorization: Bearer {token}

**路径参数**：
- exercise_id: 动作ID

**请求体**：
与创建动作详情相同，字段可部分提供。

**响应内容**：
更新后的动作详情对象。

### 获取肌肉列表

```
GET /muscles/
```

**响应内容**：
```json
[
  {
    "id": 1,
    "name": "胸大肌",
    "en_name": "Pectoralis Major"
  },
  {
    "id": 2,
    "name": "三角肌",
    "en_name": "Deltoid"
  }
]
```

### 创建肌肉

```
POST /muscles/
```

**请求头**：
- Authorization: Bearer {token}

**请求体**：
```json
{
  "name": "股四头肌",
  "en_name": "Quadriceps"
}
```

**响应内容**：
创建的肌肉对象。

### 获取身体部位列表

```
GET /body-parts/
```

**响应内容**：
```json
[
  {
    "id": 1,
    "name": "胸部"
  },
  {
    "id": 2,
    "name": "背部"
  }
]
```

### 创建身体部位

```
POST /body-parts/
```

**请求头**：
- Authorization: Bearer {token}

**请求体**：
```json
{
  "name": "腿部"
}
```

**响应内容**：
创建的身体部位对象。

### 获取器材列表

```
GET /equipment/
```

**响应内容**：
```json
[
  {
    "id": 1,
    "name": "杠铃"
  },
  {
    "id": 2,
    "name": "哑铃"
  }
]
```

### 创建器材

```
POST /equipment/
```

**请求头**：
- Authorization: Bearer {token}

**请求体**：
```json
{
  "name": "健身球"
}
```

**响应内容**：
创建的器材对象。

### 获取动作相关媒体文件

系统提供多个端点用于访问动作相关的媒体文件：

#### 获取动作图片
```
GET /image/{filename}
```

#### 获取动作GIF图
```
GET /gif/{filename}
```

#### 获取动作视频
```
GET /video/{filename}
```

#### 通过路径获取动作图片
```
GET /image/exercises/{filename}
```

#### 直接获取动作图片
```
GET /image/direct/{filename}
```

#### 获取动作头像样式
```
GET /avatar/image/exercises/{filename}
```

#### 获取动作图片文件
```
GET /image_file/{filename}
```

#### 微信小程序获取视频
```
GET /video_wx/{filename}
```
**请求参数**：
- start (可选): 开始时间（秒）

以上媒体文件端点均返回实际的文件内容，适当的Content-Type和其他HTTP头部信息。

## 健身动作相关接口使用示例

### 示例1: 获取特定身体部位的动作

```python
import requests

# 获取所有针对胸部(body_part_id=1)的动作
response = requests.get("http://localhost:8000/api/v1/exercises/?body_part_id=1")
chest_exercises = response.json()

print(f"找到 {len(chest_exercises)} 个胸部训练动作")
for exercise in chest_exercises:
    print(f"{exercise['name']} - 难度级别: {exercise['level']}")
```

### 示例2: 搜索特定肌肉和器材的动作

```python
import requests

# 搜索针对三角肌(muscle_id=2)的哑铃(equipment_id=2)动作
params = {
    "muscle_id": 2,
    "equipment_id": 2,
    "limit": 5
}
response = requests.get("http://localhost:8000/api/v1/exercises/search", params=params)
exercises = response.json()

print(f"找到 {len(exercises)} 个适合的动作")
for exercise in exercises:
    print(f"{exercise['name']} ({exercise['en_name']})")
```

### 示例3: 创建新动作(需要认证)

```python
import requests

# 假设已获取授权token
token = "your_auth_token"
headers = {"Authorization": f"Bearer {token}"}

# 创建新动作
new_exercise = {
    "name": "哑铃侧平举",
    "en_name": "Dumbbell Lateral Raise",
    "body_part_id": [3],    # 肩部
    "equipment_id": [2],    # 哑铃
    "description": "针对三角肌中束的孤立动作",
    "level": 1,
    "exercise_type": "isolation"
}

response = requests.post(
    "http://localhost:8000/api/v1/exercises/",
    headers=headers,
    json=new_exercise
)

if response.status_code == 200:
    print("动作创建成功!")
    exercise = response.json()
    print(f"ID: {exercise['id']}, 名称: {exercise['name']}")
else:
    print(f"创建失败: {response.text}")
``` 