# AI助手 API v2 接入指南

本文档提供了如何将微信小程序前端接入重构后的AI助手系统的指导。

## 接口变更概览

| 功能 | 旧版API (v1) | 新版API (v2) | 说明 |
|-----|-------------|------------|------|
| WebSocket连接 | `/api/v1/chat/stream/${sessionId}/connect` | `/api/v2/chat/stream/${sessionId}/connect` | 支持流式响应的WebSocket接口 |
| 获取会话消息 | `/api/v1/chat/sessions/${sessionId}/messages` | `/api/v2/chat/sessions/${sessionId}/messages` | 获取历史消息 |
| 轮询新消息 | `/api/v1/chat/stream/${sessionId}/poll` | `/api/v2/chat/poll/${sessionId}` | 轮询获取新消息 |
| 发送消息(HTTP) | `/api/v1/chat/message` | `/api/v2/chat/message` | 通过HTTP发送消息 |

## 平滑迁移策略

为确保平稳过渡，我们建议采用以下迁移策略：

1. **环境配置更新**：添加API版本切换选项
   ```javascript
   // config.js
   export const API_VERSION = 'v2';  // 可切换为'v1'以回退到旧版
   export const BASE_URL = `/api/${API_VERSION}/chat`;
   ```

2. **封装API调用**：创建统一的API调用函数，根据版本选择不同的端点
   ```javascript
   // api.js
   import { API_VERSION, BASE_URL } from './config';
   
   export const chatApi = {
     connectWebSocket: (sessionId) => {
       const wsUrl = `${getWsBaseUrl()}/api/${API_VERSION}/chat/stream/${sessionId}/connect`;
       return new WebSocket(wsUrl);
     },
     
     getMessages: async (sessionId, skip = 0, limit = 20) => {
       if (API_VERSION === 'v1') {
         return request(`/api/v1/chat/sessions/${sessionId}/messages?skip=${skip}&limit=${limit}`);
       } else {
         return request(`/api/v2/chat/sessions/${sessionId}/messages?skip=${skip}&limit=${limit}`);
       }
     },
     
     pollMessages: async (sessionId, lastMessageId) => {
       if (API_VERSION === 'v1') {
         return request(`/api/v1/chat/stream/${sessionId}/poll?last_message_id=${lastMessageId}`);
       } else {
         return request(`/api/v2/chat/poll/${sessionId}?last_message_id=${lastMessageId}`);
       }
     },
     
     sendMessage: async (message, sessionId, metaInfo = {}) => {
       return request(`/api/${API_VERSION}/chat/message`, {
         method: 'POST',
         data: { message, session_id: sessionId, meta_info: metaInfo }
       });
     }
   };
   ```

## 响应格式差异

### WebSocket消息格式

新版API WebSocket响应具有以下特点:

1. 使用事件类型区分不同消息:
   ```javascript
   // 连接成功事件
   {
     "event": "connected",
     "session_id": "abc123"
   }
   
   // 文本块事件
   {
     "event": "chunk",
     "data": {"text": "你好"}
   }
   
   // 元数据更新事件
   {
     "event": "meta_info_update",
     "data": {"intent_type": "fitness_advice"}
   }
   
   // 完成事件
   {
     "event": "done"
   }
   ```

2. 处理示例:
   ```javascript
   ws.onmessage = (event) => {
     const data = JSON.parse(event.data);
     
     switch (data.event) {
       case 'chunk':
         // 处理文本块
         appendText(data.data.text);
         break;
       case 'meta_info_update':
         // 处理元数据更新
         updateMetaInfo(data.data);
         break;
       case 'done':
         // 处理完成事件
         finishResponse();
         break;
     }
   };
   ```

### HTTP响应格式

新版API的HTTP响应保持了大部分向后兼容性，主要有以下优化:

1. `intent_type` 字段更加规范化，从元数据中提升为顶级字段
2. 添加了 `confidence` 字段，表示意图识别的置信度
3. 提供了更丰富的元数据信息

## 典型接入场景

### 场景一：WebSocket实时通讯

```javascript
// 1. 建立WebSocket连接
const ws = chatApi.connectWebSocket(sessionId);

// 2. 处理连接事件
ws.onopen = () => {
  console.log('WebSocket连接已建立');
};

// 3. 处理消息
ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  
  if (data.event === 'chunk') {
    // 添加文本片段
    appendToResponse(data.data.text);
  } else if (data.event === 'done') {
    // 响应完成
    finishResponseAnimation();
  }
};

// 4. 发送消息
function sendMessage(text) {
  ws.send(JSON.stringify({
    message: text,
    meta_info: {
      client_timestamp: Date.now()
    }
  }));
}
```

### 场景二：轮询获取新消息

```javascript
async function pollNewMessages() {
  // 获取最后一条消息的ID
  const lastMessageId = getLastMessageId();
  
  // 轮询获取新消息
  const response = await chatApi.pollMessages(sessionId, lastMessageId);
  
  if (response.messages && response.messages.length > 0) {
    // 处理新消息
    response.messages.forEach(message => {
      displayMessage(message);
      updateLastMessageId(message.id);
    });
  }
  
  // 继续轮询
  setTimeout(pollNewMessages, 2000); // 每2秒轮询一次
}

// 启动轮询
pollNewMessages();
```

## 测试与验证

在完全迁移前，建议进行以下测试:

1. **A/B测试**：随机选择一部分用户使用新版API，比较性能和用户体验
2. **功能覆盖测试**：确保所有功能在新版API上正常工作
3. **回归测试**：验证没有引入新的bug
4. **性能测试**：比较新旧版本的响应时间和资源使用情况

## 常见问题

### Q: 如何在不影响现有用户的情况下逐步迁移?
A: 使用配置开关，先让一小部分测试用户尝试新API，没问题后再扩大范围。

### Q: 新旧版API的消息格式有哪些兼容性问题?
A: 主要差异在于WebSocket的事件格式和元数据字段。可以在客户端添加适配代码处理这些差异。

### Q: 如何处理迁移中可能出现的问题?
A: 添加版本切换功能，如果发现新版API有问题，可以快速回退到旧版API。

## 联系与支持

如有任何问题，请联系技术团队：
- 邮箱：<EMAIL>
- 开发者群：123456789 