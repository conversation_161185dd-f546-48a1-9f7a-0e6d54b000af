# 团队功能实现文档

## 概述

本文档详细描述了团队功能的实现细节，包括服务层、数据模型、API接口和数据流程等方面。团队功能是一个完整的团队管理系统，支持团队创建、成员管理、客户分配、训练计划管理和数据统计等核心功能。

## 系统架构

团队功能采用分层架构设计，主要包括以下几个层次：

1. **数据模型层**：定义数据结构和关系
2. **服务层**：实现业务逻辑
3. **API接口层**：提供外部访问接口

### 目录结构

```
app/
├── models/
│   └── team/
│       ├── __init__.py
│       ├── enums.py
│       ├── team.py
│       ├── membership.py
│       ├── client.py
│       ├── invitation.py
│       ├── stats.py
│       ├── training.py
│       └── template.py
├── schemas/
│   └── team/
│       ├── __init__.py
│       ├── team.py
│       ├── membership.py
│       ├── client.py
│       ├── invitation.py
│       ├── training.py
│       └── template.py
├── services/
│   ├── team_service.py
│   ├── team_member_service.py
│   ├── team_client_service.py
│   ├── team_training_service.py
│   └── team_stats_service.py
└── api/
    └── v1/
        └── endpoints/
            └── team.py
```

## 数据模型实现

### 枚举定义

```python
# app/models/team/enums.py
import enum

class TeamRole(enum.IntEnum):
    """团队角色"""
    OWNER = 1      # 所有者
    ADMIN = 2      # 管理员
    COACH = 3      # 教练
    ASSISTANT = 4  # 助理

class MembershipStatus(enum.IntEnum):
    """成员状态"""
    ACTIVE = 1     # 活跃
    INACTIVE = 2   # 非活跃
    SUSPENDED = 3  # 已暂停

class TeamStatus(enum.IntEnum):
    """团队状态"""
    ACTIVE = 1     # 活跃
    INACTIVE = 2   # 非活跃
    SUSPENDED = 3  # 已暂停

class ClientStatus(enum.IntEnum):
    """客户状态"""
    ACTIVE = 1     # 活跃
    INACTIVE = 2   # 非活跃
    TRANSFERRED = 3 # 已转移

class InvitationStatus(enum.IntEnum):
    """邀请状态"""
    PENDING = 1    # 待处理
    ACCEPTED = 2   # 已接受
    REJECTED = 3   # 已拒绝
    EXPIRED = 4    # 已过期
```

### 主要模型定义

#### 团队模型

```python
# app/models/team/team.py
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.db.base_class import Base

class Team(Base):
    __tablename__ = "teams"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    description = Column(String)
    owner_id = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())
    
    # 状态和配置
    status = Column(String(20))
    settings = Column(JSON, default={})
    
    # 关系
    owner = relationship("User", back_populates="owned_teams")
    memberships = relationship("app.models.team.membership.TeamMembership", back_populates="team", cascade="all, delete-orphan")
    clients = relationship("app.models.team.client.ClientRelation", back_populates="team", cascade="all, delete-orphan")
    invitations = relationship("app.models.team.invitation.TeamInvitation", back_populates="team", cascade="all, delete-orphan")
    stats = relationship("app.models.team.stats.TeamStats", back_populates="team", uselist=False, cascade="all, delete-orphan")
```

#### 成员关系模型

```python
# app/models/team/membership.py
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, UniqueConstraint
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.db.base_class import Base

class TeamMembership(Base):
    __tablename__ = "team_memberships"

    id = Column(Integer, primary_key=True, index=True)
    team_id = Column(Integer, ForeignKey("teams.id"))
    user_id = Column(Integer, ForeignKey("users.id"))
    role = Column(String(20))
    status = Column(String(20))
    joined_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())

    # 关系
    team = relationship("app.models.team.team.Team", back_populates="memberships")
    user = relationship("User", back_populates="team_memberships")
    
    __table_args__ = (
        UniqueConstraint('team_id', 'user_id', name='uq_team_user'),
    )
```

#### 客户关系模型

```python
# app/models/team/client.py
from sqlalchemy import Column, Integer, DateTime, ForeignKey, String
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.db.base_class import Base

class ClientRelation(Base):
    __tablename__ = "client_relations"

    id = Column(Integer, primary_key=True, index=True)
    team_id = Column(Integer, ForeignKey("teams.id"))
    client_id = Column(Integer, ForeignKey("users.id"))
    coach_id = Column(Integer, ForeignKey("users.id"))
    status = Column(String(20))
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())

    # 关系
    team = relationship("app.models.team.team.Team", back_populates="clients")
    client = relationship("User", foreign_keys=[client_id])
    coach = relationship("User", foreign_keys=[coach_id])
    transfer_history = relationship("ClientTransferHistory", back_populates="client_relation", cascade="all, delete-orphan")
    training_plans = relationship("app.models.team.training.ClientTrainingPlan", back_populates="client_relation", cascade="all, delete-orphan")

class ClientTransferHistory(Base):
    __tablename__ = "client_transfer_history"

    id = Column(Integer, primary_key=True, index=True)
    client_relation_id = Column(Integer, ForeignKey("client_relations.id"))
    from_coach_id = Column(Integer, ForeignKey("users.id"))
    to_coach_id = Column(Integer, ForeignKey("users.id"))
    reason = Column(String, nullable=True)
    transferred_at = Column(DateTime, default=func.now())

    # 关系
    client_relation = relationship("ClientRelation", back_populates="transfer_history")
    from_coach = relationship("User", foreign_keys=[from_coach_id])
    to_coach = relationship("User", foreign_keys=[to_coach_id])
```

## 服务层实现

### 团队服务

团队服务（TeamService）提供团队的基本管理功能，包括创建、查询、更新和删除团队。

```python
# app/services/team_service.py
class TeamService:
    """团队服务，处理团队相关业务逻辑"""
    
    def __init__(self, db: Session):
        """初始化
        
        Args:
            db (Session): 数据库会话
        """
        self.db = db
    
    async def create_team(self, owner: User, team_data: TeamCreate) -> Team:
        """创建新团队"""
        # 实现创建团队逻辑
        
    async def get_team(self, team_id: int) -> Optional[Team]:
        """获取团队"""
        # 实现获取团队逻辑
        
    async def update_team(self, team_id: int, team_data: TeamUpdate, current_user: User) -> Team:
        """更新团队信息"""
        # 实现更新团队逻辑
        
    async def get_user_teams(self, user_id: int) -> List[Dict[str, Any]]:
        """获取用户的团队列表"""
        # 实现获取用户团队列表逻辑
        
    async def get_team_detail(self, team_id: int, user_id: int) -> Dict[str, Any]:
        """获取团队详情"""
        # 实现获取团队详情逻辑
        
    async def delete_team(self, team_id: int, current_user: User) -> bool:
        """删除团队"""
        # 实现删除团队逻辑
        
    async def _check_team_permission(self, team_id: int, user_id: int, required_roles: List[TeamRole]) -> bool:
        """检查用户在团队中的权限"""
        # 实现权限检查逻辑
        
    async def check_team_access(self, team_id: int, user_id: int) -> bool:
        """检查用户是否有权访问团队"""
        # 实现访问检查逻辑
        
    async def update_team_stats(self, team_id: int) -> TeamStats:
        """更新团队统计数据"""
        # 实现更新团队统计数据逻辑
```

### 团队成员服务

团队成员服务（TeamMemberService）提供团队成员管理功能，包括添加成员、更新角色、移除成员以及邀请管理。

```python
# app/services/team_member_service.py
class TeamMemberService:
    """团队成员服务，处理团队成员相关业务逻辑"""
    
    def __init__(self, db: Session):
        """初始化
        
        Args:
            db (Session): 数据库会话
        """
        self.db = db
        self.team_service = TeamService(db)
    
    async def add_team_member(self, team_id: int, membership_data: MembershipCreate, current_user: User) -> TeamMembership:
        """添加团队成员"""
        # 实现添加成员逻辑
        
    async def get_membership(self, team_id: int, user_id: int) -> Optional[TeamMembership]:
        """获取成员关系"""
        # 实现获取成员关系逻辑
        
    async def update_member_role(self, team_id: int, user_id: int, role_data: MembershipUpdate, current_user: User) -> TeamMembership:
        """更新成员角色"""
        # 实现更新角色逻辑
        
    async def remove_team_member(self, team_id: int, user_id: int, current_user: User) -> bool:
        """移除团队成员"""
        # 实现移除成员逻辑
        
    async def get_team_members(self, team_id: int, role: Optional[TeamRole] = None, current_user: User = None) -> List[Dict[str, Any]]:
        """获取团队成员列表"""
        # 实现获取成员列表逻辑
        
    async def create_team_invitation(self, team_id: int, invitation_data: InvitationCreate, current_user: User) -> TeamInvitation:
        """创建团队邀请"""
        # 实现创建邀请逻辑
        
    async def respond_to_invitation(self, invitation_id: int, accept: bool, current_user: User) -> Dict[str, Any]:
        """响应团队邀请"""
        # 实现响应邀请逻辑
        
    async def get_team_invitations(self, team_id: int, status: Optional[InvitationStatus] = None, current_user: User = None) -> List[Dict[str, Any]]:
        """获取团队邀请列表"""
        # 实现获取邀请列表逻辑
        
    async def get_user_invitations(self, user_id: int, status: Optional[InvitationStatus] = None) -> List[Dict[str, Any]]:
        """获取用户收到的邀请列表"""
        # 实现获取用户邀请列表逻辑
        
    async def cancel_invitation(self, invitation_id: int, current_user: User) -> bool:
        """取消邀请"""
        # 实现取消邀请逻辑
```

### 团队客户服务

团队客户服务（TeamClientService）提供客户管理功能，包括分配客户、转移客户、查询客户等。

```python
# app/services/team_client_service.py
class TeamClientService:
    """团队客户服务，处理团队客户相关业务逻辑"""
    
    def __init__(self, db: Session):
        """初始化
        
        Args:
            db (Session): 数据库会话
        """
        self.db = db
        self.team_service = TeamService(db)
    
    async def assign_client(self, team_id: int, client_data: ClientAssignment, current_user: User) -> ClientRelation:
        """分配客户"""
        # 实现分配客户逻辑
        
    async def get_client_relation(self, relation_id: int) -> Optional[ClientRelation]:
        """获取客户关系"""
        # 实现获取客户关系逻辑
        
    async def _get_client_relation_by_client(self, team_id: int, client_id: int) -> Optional[ClientRelation]:
        """根据客户ID获取客户关系"""
        # 实现根据客户ID获取客户关系逻辑
        
    async def transfer_client(self, relation_id: int, transfer_data: ClientTransfer, current_user: User) -> ClientRelation:
        """转移客户"""
        # 实现转移客户逻辑
        
    async def _transfer_client_with_history(self, client_relation: ClientRelation, new_coach_id: int, reason: Optional[str] = None) -> ClientRelation:
        """带历史记录的客户转移"""
        # 实现带历史记录的客户转移逻辑
        
    async def get_team_clients(self, team_id: int, status: Optional[ClientStatus] = None, coach_id: Optional[int] = None, current_user: User = None) -> List[Dict[str, Any]]:
        """获取团队客户列表"""
        # 实现获取团队客户列表逻辑
        
    async def get_client_detail(self, relation_id: int, current_user: User) -> Dict[str, Any]:
        """获取客户详情"""
        # 实现获取客户详情逻辑
        
    async def deactivate_client(self, relation_id: int, current_user: User) -> ClientRelation:
        """停用客户"""
        # 实现停用客户逻辑
        
    async def reactivate_client(self, relation_id: int, current_user: User) -> ClientRelation:
        """重新激活客户"""
        # 实现重新激活客户逻辑
```

### 团队训练服务

团队训练服务（TeamTrainingService）提供训练计划和课程管理功能，包括创建模板、分配计划、管理训练课程等。

```python
# app/services/team_training_service.py
class TeamTrainingService:
    """团队训练服务，处理团队训练相关业务逻辑"""
    
    def __init__(self, db: Session):
        """初始化
        
        Args:
            db (Session): 数据库会话
        """
        self.db = db
        self.team_service = TeamService(db)
        self.client_service = TeamClientService(db)
    
    async def create_training_template(self, team_id: int, template_data: TemplateCreate, current_user: User) -> TrainingPlanTemplate:
        """创建训练计划模板"""
        # 实现创建模板逻辑
        
    async def get_template(self, template_id: int) -> Optional[TrainingPlanTemplate]:
        """获取模板"""
        # 实现获取模板逻辑
        
    async def get_team_templates(self, team_id: int, current_user: User = None) -> List[Dict[str, Any]]:
        """获取团队模板列表"""
        # 实现获取团队模板列表逻辑
        
    async def assign_training_plan(self, client_relation_id: int, plan_data: ClientPlanCreate, current_user: User) -> ClientTrainingPlan:
        """为客户分配训练计划"""
        # 实现分配训练计划逻辑
        
    async def _validate_training_schedule(self, client_id: int, coach_id: int, plan_data: ClientPlanCreate) -> bool:
        """验证训练时间安排"""
        # 实现验证训练时间安排逻辑
        
    async def _check_coach_schedule_conflicts(self, coach_id: int, plan_data: ClientPlanCreate) -> List[Dict[str, Any]]:
        """检查教练时间冲突"""
        # 实现检查教练时间冲突逻辑
        
    async def _check_client_schedule_conflicts(self, client_id: int, plan_data: ClientPlanCreate) -> List[Dict[str, Any]]:
        """检查客户时间冲突"""
        # 实现检查客户时间冲突逻辑
        
    async def _calculate_next_session(self, start_date: datetime, scheduled_time: Dict[str, Any]) -> datetime:
        """计算下次训练时间"""
        # 实现计算下次训练时间逻辑
        
    async def _create_training_sessions(self, client_plan: ClientTrainingPlan, scheduled_time: Dict[str, Any]) -> List[TrainingSession]:
        """创建训练课程"""
        # 实现创建训练课程逻辑
        
    async def get_client_training_plans(self, client_relation_id: int, status: Optional[str] = None, current_user: User = None) -> List[Dict[str, Any]]:
        """获取客户训练计划列表"""
        # 实现获取客户训练计划列表逻辑
        
    async def get_training_plan(self, plan_id: int) -> Optional[ClientTrainingPlan]:
        """获取训练计划"""
        # 实现获取训练计划逻辑
        
    async def update_training_plan(self, plan_id: int, update_data: ClientPlanUpdate, current_user: User) -> ClientTrainingPlan:
        """更新训练计划"""
        # 实现更新训练计划逻辑
        
    async def get_training_sessions(self, plan_id: int, status: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取训练课程列表"""
        # 实现获取训练课程列表逻辑
        
    async def get_session(self, session_id: int) -> Optional[TrainingSession]:
        """获取训练课程"""
        # 实现获取训练课程逻辑
        
    async def start_session(self, session_id: int, current_user: User) -> TrainingSession:
        """开始训练课程"""
        # 实现开始训练课程逻辑
        
    async def complete_session(self, session_id: int, feedback_data: SessionFeedback, current_user: User) -> TrainingSession:
        """完成训练课程"""
        # 实现完成训练课程逻辑
        
    async def record_exercise_set(self, session_id: int, exercise_id: int, set_data: SetRecordCreate, current_user: User) -> SessionExerciseRecord:
        """记录训练组数据"""
        # 实现记录训练组数据逻辑
```

### 团队统计服务

团队统计服务（TeamStatsService）提供团队和客户统计数据的计算和查询功能。

```python
# app/services/team_stats_service.py
class TeamStatsService:
    """团队统计服务，处理团队统计相关业务逻辑"""
    
    def __init__(self, db: Session):
        """初始化
        
        Args:
            db (Session): 数据库会话
        """
        self.db = db
        self.team_service = TeamService(db)
    
    async def get_team_stats(self, team_id: int, current_user: User) -> Dict[str, Any]:
        """获取团队统计数据"""
        # 实现获取团队统计数据逻辑
        
    async def update_team_stats(self, team_id: int) -> TeamStats:
        """更新团队统计数据"""
        # 实现更新团队统计数据逻辑
        
    async def _calculate_monthly_stats(self, team_id: int) -> Dict[str, Any]:
        """计算月度统计数据"""
        # 实现计算月度统计数据逻辑
        
    async def _get_additional_stats(self, team_id: int) -> Dict[str, Any]:
        """获取额外的统计数据"""
        # 实现获取额外的统计数据逻辑
        
    async def get_client_stats(self, client_relation_id: int, current_user: User) -> Dict[str, Any]:
        """获取客户统计数据"""
        # 实现获取客户统计数据逻辑
        
    async def calculate_team_growth(self, team_id: int) -> Dict[str, Any]:
        """计算团队增长数据"""
        # 实现计算团队增长数据逻辑
```

## API接口实现

API接口层通过FastAPI路由器实现，主要包括团队管理、成员管理、客户管理、邀请管理、训练计划管理和统计接口等。

```python
# app/api/v1/endpoints/team.py
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from datetime import datetime
from app.api.deps import get_db, get_current_user
from app.models.user import User
from app.models.team import TeamRole, MembershipStatus, InvitationStatus, ClientStatus
from app.schemas.team import (
    TeamCreate, TeamUpdate, TeamResponse, TeamDetail, TeamListResponse,
    MembershipCreate, MembershipUpdate, MembershipResponse, TeamMemberResponse,
    ClientAssignment, ClientTransfer, ClientRelationResponse, ClientListResponse, ClientDetailResponse,
    InvitationCreate, InvitationResponse, InvitationListResponse,
    ClientPlanCreate, ClientPlanUpdate, ClientTrainingPlanResponse, SessionResponse, SetRecordCreate, SessionFeedback,
    TemplateCreate, TemplateResponse, TemplateListResponse
)
from app.services.team_service import TeamService, TeamServiceException, TeamNotFoundException, InsufficientPermissionException
from app.services.team_member_service import TeamMemberService, MembershipNotFoundException, InvitationNotFoundException
from app.services.team_client_service import TeamClientService, ClientRelationNotFoundException
from app.services.team_training_service import TeamTrainingService, TrainingPlanNotFoundException, SessionNotFoundException, TemplateNotFoundException, ScheduleConflictException
from app.services.team_stats_service import TeamStatsService

router = APIRouter()

# 团队管理接口
@router.post("/teams/", response_model=TeamResponse)
async def create_new_team(
    team_data: TeamCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建新团队"""
    team_service = TeamService(db)
    try:
        return await team_service.create_team(current_user, team_data)
    except TeamServiceException as e:
        raise HTTPException(status_code=400, detail=str(e))

# 其他接口实现...
```

## 数据流程

### 团队创建流程

1. 用户通过API发送创建团队请求
2. API接口调用TeamService的create_team方法
3. TeamService创建团队记录
4. TeamService创建所有者成员关系
5. TeamService初始化团队统计
6. 返回创建的团队信息

### 成员管理流程

1. 添加成员：
   - 检查权限
   - 创建成员关系
   - 更新团队统计

2. 更新角色：
   - 检查权限
   - 更新成员角色
   - 返回更新后的成员关系

3. 移除成员：
   - 检查权限
   - 删除成员关系
   - 更新团队统计

### 客户管理流程

1. 分配客户：
   - 检查权限
   - 创建客户关系
   - 更新团队统计

2. 转移客户：
   - 检查权限
   - 记录转移历史
   - 更新客户关系
   - 更新团队统计

### 训练计划管理流程

1. 创建模板：
   - 检查权限
   - 创建模板记录
   - 创建模板练习

2. 分配计划：
   - 检查权限
   - 验证训练时间安排
   - 创建训练计划
   - 创建训练课程

3. 训练课程管理：
   - 开始训练：更新课程状态，初始化训练记录
   - 记录训练：更新训练记录，计算完成率
   - 完成训练：更新课程状态，提交反馈，更新计划完成率

### 统计数据流程

1. 获取团队统计：
   - 检查访问权限
   - 获取或计算统计数据
   - 返回统计结果

2. 更新团队统计：
   - 计算成员数量
   - 计算客户数量
   - 计算活跃客户数量
   - 计算训练课程数量和完成率
   - 计算增长率
   - 更新月度统计

## 异常处理

系统实现了一系列自定义异常类，用于处理各种错误情况：

```python
class TeamServiceException(Exception):
    """团队服务异常基类"""
    pass

class TeamNotFoundException(TeamServiceException):
    """团队不存在异常"""
    pass

class InsufficientPermissionException(TeamServiceException):
    """权限不足异常"""
    pass

class MembershipNotFoundException(TeamServiceException):
    """成员关系不存在异常"""
    pass

class InvitationNotFoundException(TeamServiceException):
    """邀请不存在异常"""
    pass

class ClientRelationNotFoundException(TeamServiceException):
    """客户关系不存在异常"""
    pass

class TrainingPlanNotFoundException(TeamServiceException):
    """训练计划不存在异常"""
    pass

class SessionNotFoundException(TeamServiceException):
    """训练课程不存在异常"""
    pass

class TemplateNotFoundException(TeamServiceException):
    """模板不存在异常"""
    pass

class ScheduleConflictException(TeamServiceException):
    """时间冲突异常"""
    pass
```

API接口层捕获这些异常并转换为适当的HTTP错误响应：

```python
try:
    return await team_service.create_team(current_user, team_data)
except TeamNotFoundException:
    raise HTTPException(status_code=404, detail="Team not found")
except InsufficientPermissionException:
    raise HTTPException(status_code=403, detail="Not enough permissions")
except TeamServiceException as e:
    raise HTTPException(status_code=400, detail=str(e))
```

## 总结

团队功能实现了一个完整的团队管理系统，包括团队创建、成员管理、客户分配、训练计划管理和数据统计等核心功能。系统采用分层架构设计，通过服务层封装业务逻辑，通过API接口层提供外部访问接口。系统实现了完善的异常处理机制，确保了数据的一致性和安全性。

团队功能的实现为健身教练提供了一个强大的团队管理工具，可以有效地组织团队、管理客户、安排训练计划和跟踪训练进度，从而提高工作效率和服务质量。
