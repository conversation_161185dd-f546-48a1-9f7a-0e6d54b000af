# 团队功能接口文档

## 概述

团队功能模块提供了完整的团队管理系统，包括团队创建、成员管理、客户分配、训练计划管理和数据统计等功能。本文档详细介绍了各个接口的用途、参数和返回值。

## 功能架构

团队功能由以下几个主要服务组成：

1. **TeamService**: 核心团队管理功能
2. **TeamMemberService**: 团队成员和邀请管理
3. **TeamClientService**: 客户关系管理
4. **TeamTrainingService**: 训练计划和课程管理
5. **TeamStatsService**: 团队和客户统计数据

## API 接口列表

### 团队管理接口

#### 创建团队
```
POST /team/teams/
```

**请求参数**
- `name`: 团队名称（必填）
- `description`: 团队描述（可选）
- `settings`: 团队设置（可选，JSON格式）

**返回数据**
- `id`: 团队ID
- `name`: 团队名称
- `description`: 团队描述
- `owner_id`: 所有者ID
- `status`: 团队状态
- `created_at`: 创建时间
- `updated_at`: 更新时间

#### 获取团队详情
```
GET /team/teams/{team_id}
```

**路径参数**
- `team_id`: 团队ID

**返回数据**
- 团队基本信息
- 团队设置
- 团队统计数据

#### 更新团队信息
```
PUT /team/teams/{team_id}
```

**路径参数**
- `team_id`: 团队ID

**请求参数**
- `name`: 团队名称（可选）
- `description`: 团队描述（可选）
- `status`: 团队状态（可选）
- `settings`: 团队设置（可选，JSON格式）

**返回数据**
- 更新后的团队信息

#### 获取用户相关的团队列表
```
GET /team/teams/
```

**返回数据**
- 团队列表，包含团队基本信息、成员数量、客户数量和用户在团队中的角色

#### 删除团队
```
DELETE /team/teams/{team_id}
```

**路径参数**
- `team_id`: 团队ID

**说明**
- 此操作将团队标记为已删除状态，而不是真正删除

### 成员管理接口

#### 添加团队成员
```
POST /team/teams/{team_id}/members/
```

**路径参数**
- `team_id`: 团队ID

**请求参数**
- `user_id`: 用户ID（必填）
- `role`: 角色（必填，枚举值：OWNER=1, ADMIN=2, COACH=3, ASSISTANT=4）
- `status`: 状态（可选，默认为ACTIVE，枚举值：ACTIVE=1, INACTIVE=2, SUSPENDED=3）

**返回数据**
- 成员关系信息

#### 更新成员角色
```
PUT /team/teams/{team_id}/members/{user_id}
```

**路径参数**
- `team_id`: 团队ID
- `user_id`: 用户ID

**请求参数**
- `role`: 角色（可选，枚举值：OWNER=1, ADMIN=2, COACH=3, ASSISTANT=4）
- `status`: 状态（可选，枚举值：ACTIVE=1, INACTIVE=2, SUSPENDED=3）

**返回数据**
- 更新后的成员关系信息

#### 移除团队成员
```
DELETE /team/teams/{team_id}/members/{user_id}
```

**路径参数**
- `team_id`: 团队ID
- `user_id`: 用户ID

#### 获取团队成员列表
```
GET /team/teams/{team_id}/members
```

**路径参数**
- `team_id`: 团队ID

**查询参数**
- `role`: 角色过滤（可选）

**返回数据**
- 成员列表，包含用户信息和角色信息

### 客户管理接口

#### 分配客户
```
POST /team/teams/{team_id}/clients/
```

**路径参数**
- `team_id`: 团队ID

**请求参数**
- `client_id`: 客户ID（必填）
- `coach_id`: 教练ID（必填）
- `status`: 状态（可选，默认为ACTIVE，枚举值：ACTIVE=1, INACTIVE=2, TRANSFERRED=3）

**返回数据**
- 客户关系信息

#### 转移客户
```
POST /team/teams/{team_id}/clients/{client_relation_id}/transfer
```

**路径参数**
- `team_id`: 团队ID
- `client_relation_id`: 客户关系ID

**请求参数**
- `new_coach_id`: 新教练ID（必填）
- `reason`: 转移原因（可选）

**返回数据**
- 更新后的客户关系信息

#### 获取团队客户列表
```
GET /team/teams/{team_id}/clients
```

**路径参数**
- `team_id`: 团队ID

**查询参数**
- `status`: 状态过滤（可选）
- `coach_id`: 教练ID过滤（可选）

**返回数据**
- 客户列表，包含客户基本信息、教练信息和训练计划统计

#### 获取客户详情
```
GET /team/teams/{team_id}/clients/{client_relation_id}
```

**路径参数**
- `team_id`: 团队ID
- `client_relation_id`: 客户关系ID

**返回数据**
- 客户详细信息，包含客户基本信息、教练信息、统计数据和转移历史

#### 停用客户
```
PUT /team/teams/{team_id}/clients/{client_relation_id}/deactivate
```

**路径参数**
- `team_id`: 团队ID
- `client_relation_id`: 客户关系ID

**返回数据**
- 更新后的客户关系信息

#### 重新激活客户
```
PUT /team/teams/{team_id}/clients/{client_relation_id}/reactivate
```

**路径参数**
- `team_id`: 团队ID
- `client_relation_id`: 客户关系ID

**返回数据**
- 更新后的客户关系信息

### 邀请管理接口

#### 创建团队邀请
```
POST /team/teams/{team_id}/invitations/
```

**路径参数**
- `team_id`: 团队ID

**请求参数**
- `invitee_id`: 被邀请人ID（必填）
- `role`: 角色（必填，枚举值：OWNER=1, ADMIN=2, COACH=3, ASSISTANT=4）
- `expired_at`: 过期时间（可选，默认为7天后）

**返回数据**
- 邀请信息

#### 接受团队邀请
```
PUT /team/teams/invitations/{invitation_id}/accept
```

**路径参数**
- `invitation_id`: 邀请ID

**返回数据**
- 接受结果，包含团队ID和角色信息

#### 拒绝团队邀请
```
PUT /team/teams/invitations/{invitation_id}/reject
```

**路径参数**
- `invitation_id`: 邀请ID

**返回数据**
- 拒绝结果

#### 获取团队发出的邀请列表
```
GET /team/teams/{team_id}/invitations
```

**路径参数**
- `team_id`: 团队ID

**查询参数**
- `status`: 状态过滤（可选，枚举值：PENDING=1, ACCEPTED=2, REJECTED=3, EXPIRED=4）

**返回数据**
- 邀请列表

#### 获取用户收到的邀请列表
```
GET /team/user/invitations
```

**查询参数**
- `status`: 状态过滤（可选，枚举值：PENDING=1, ACCEPTED=2, REJECTED=3, EXPIRED=4）

**返回数据**
- 邀请列表

#### 取消邀请
```
DELETE /team/teams/invitations/{invitation_id}
```

**路径参数**
- `invitation_id`: 邀请ID

### 训练计划模板接口

#### 创建训练计划模板
```
POST /team/teams/{team_id}/templates/
```

**路径参数**
- `team_id`: 团队ID

**请求参数**
- `name`: 模板名称（必填）
- `description`: 模板描述（可选）
- `duration_weeks`: 持续周数（必填，1-52）
- `sessions_per_week`: 每周课程数（必填，1-7）
- `difficulty_level`: 难度级别（可选，1-5）
- `target_audience`: 目标受众（可选）
- `equipment_required`: 所需器材ID列表（可选）
- `is_public`: 是否公开（可选，默认为false）
- `exercises`: 训练动作列表（必填）

**返回数据**
- 模板信息

#### 获取团队训练计划模板列表
```
GET /team/teams/{team_id}/templates/
```

**路径参数**
- `team_id`: 团队ID

**返回数据**
- 模板列表

### 训练计划接口

#### 创建客户训练计划
```
POST /team/clients/{client_relation_id}/training-plans/
```

**路径参数**
- `client_relation_id`: 客户关系ID

**请求参数**
- `training_plan_id`: 训练计划ID（必填）
- `start_date`: 开始日期（必填）
- `end_date`: 结束日期（必填）
- `scheduled_time`: 时间安排（必填，JSON格式）
- `notes`: 备注（可选）

**返回数据**
- 训练计划信息

#### 获取客户训练计划列表
```
GET /team/clients/{client_relation_id}/training-plans/
```

**路径参数**
- `client_relation_id`: 客户关系ID

**查询参数**
- `status`: 状态过滤（可选）

**返回数据**
- 训练计划列表

#### 更新客户训练计划
```
PUT /team/clients/training-plans/{plan_id}
```

**路径参数**
- `plan_id`: 计划ID

**请求参数**
- `status`: 状态（可选）
- `start_date`: 开始日期（可选）
- `end_date`: 结束日期（可选）
- `scheduled_time`: 时间安排（可选，JSON格式）
- `notes`: 备注（可选）

**返回数据**
- 更新后的训练计划信息

### 训练课程接口

#### 获取训练课程列表
```
GET /team/training-plans/{plan_id}/sessions
```

**路径参数**
- `plan_id`: 计划ID

**查询参数**
- `status`: 状态过滤（可选）

**返回数据**
- 训练课程列表

#### 开始训练课程
```
POST /team/sessions/{session_id}/start
```

**路径参数**
- `session_id`: 课程ID

**返回数据**
- 更新后的训练课程信息

#### 记录训练组数据
```
POST /team/sessions/{session_id}/exercises/{exercise_id}/records
```

**路径参数**
- `session_id`: 课程ID
- `exercise_id`: 动作ID

**请求参数**
- `weight`: 重量（必填）
- `reps`: 重复次数（必填）
- `rpe`: 感知强度（可选，1-10）
- `notes`: 备注（可选）

**返回数据**
- 更新后的训练记录信息

#### 完成训练课程并提交反馈
```
PUT /team/sessions/{session_id}/complete
```

**路径参数**
- `session_id`: 课程ID

**请求参数**
- `feedback`: 反馈内容（可选）
- `mood_rating`: 心情评分（可选，1-5）
- `difficulty_rating`: 难度评分（可选，1-5）
- `notes`: 备注（可选）

**返回数据**
- 更新后的训练课程信息

### 统计接口

#### 获取团队统计数据
```
GET /team/teams/{team_id}/stats
```

**路径参数**
- `team_id`: 团队ID

**返回数据**
- 团队统计数据，包含成员数量、客户数量、活跃客户数量、训练课程数量、完成率等

#### 获取客户统计数据
```
GET /team/clients/{client_relation_id}/stats
```

**路径参数**
- `client_relation_id`: 客户关系ID

**返回数据**
- 客户统计数据，包含训练计划数量、已完成的训练计划数量、训练课程数量、完成率等

## 数据模型

### 团队相关枚举

```python
# 团队角色
class TeamRole(enum.IntEnum):
    OWNER = 1      # 所有者
    ADMIN = 2      # 管理员
    COACH = 3      # 教练
    ASSISTANT = 4  # 助理

# 成员状态
class MembershipStatus(enum.IntEnum):
    ACTIVE = 1     # 活跃
    INACTIVE = 2   # 非活跃
    SUSPENDED = 3  # 已暂停

# 团队状态
class TeamStatus(enum.IntEnum):
    ACTIVE = 1     # 活跃
    INACTIVE = 2   # 非活跃
    SUSPENDED = 3  # 已暂停

# 客户状态
class ClientStatus(enum.IntEnum):
    ACTIVE = 1     # 活跃
    INACTIVE = 2   # 非活跃
    TRANSFERRED = 3 # 已转移

# 邀请状态
class InvitationStatus(enum.IntEnum):
    PENDING = 1    # 待处理
    ACCEPTED = 2   # 已接受
    REJECTED = 3   # 已拒绝
    EXPIRED = 4    # 已过期
```

### 主要数据模型

1. **Team**: 团队基本信息
2. **TeamMembership**: 团队成员关系
3. **ClientRelation**: 客户关系
4. **ClientTransferHistory**: 客户转移历史
5. **TeamInvitation**: 团队邀请
6. **TeamStats**: 团队统计
7. **TrainingPlanTemplate**: 训练计划模板
8. **TemplateExercise**: 模板训练动作
9. **ClientTrainingPlan**: 客户训练计划
10. **TrainingSession**: 训练课程
11. **SessionExerciseRecord**: 训练动作记录

## 异常处理

系统实现了以下异常类型：

1. **TeamServiceException**: 团队服务基础异常
2. **TeamNotFoundException**: 团队不存在异常
3. **InsufficientPermissionException**: 权限不足异常
4. **MembershipNotFoundException**: 成员关系不存在异常
5. **InvitationNotFoundException**: 邀请不存在异常
6. **ClientRelationNotFoundException**: 客户关系不存在异常
7. **TrainingPlanNotFoundException**: 训练计划不存在异常
8. **SessionNotFoundException**: 训练课程不存在异常
9. **TemplateNotFoundException**: 模板不存在异常
10. **ScheduleConflictException**: 时间冲突异常

## 服务层实现

团队功能由以下几个服务组成：

1. **TeamService**: 提供团队创建、查询、更新、删除等基本功能
2. **TeamMemberService**: 提供成员添加、角色更新、移除以及邀请管理功能
3. **TeamClientService**: 提供客户分配、转移、查询等功能
4. **TeamTrainingService**: 提供训练计划模板创建、训练计划分配、训练课程管理等功能
5. **TeamStatsService**: 提供团队和客户统计数据计算和查询功能

这些服务共同构成了一个完整的团队管理系统，实现了团队组织、成员管理、客户分配、训练计划管理和数据统计等核心功能。
