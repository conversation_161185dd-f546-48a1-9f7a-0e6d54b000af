
I'll start by examining the existing team functionality to better understand the current implementation before updating the documentation.

Ran tool
Let me try again with a shorter timeout:

Ran tool
Let me try searching for team-related files to understand the implementation:

Ran tool
Let's try searching in the codebase for team-related models:

Search files...
Let me try a grep search to find team-related files:

Ran tool
Let me examine the team-related models to understand the implementation:

Read file: app/models/team/team.py

Read file: app/models/team/enums.py

Read file: app/models/team/membership.py

Read file: app/models/team/invitation.py
Now, let's check the existing API endpoints for team functionality:

Ran tool
Let's try searching for team API endpoints in the actual application:

Ran tool
Let's check for any routers in the API directory:

Ran tool
I found a team router at `app/api/v1/endpoints/team.py`. Let's examine this file:

Read file: app/api/v1/endpoints/team.py
Now, let's also check the team schemas:

Read file: app/schemas/team/team.py
Based on my analysis, I now have a good understanding of the implemented team functionality. I'll update the API documentation accordingly.

# 团队功能接口文档

## 功能流程图

### 团队管理流程
1. 用户创建团队 → 成为团队所有者
2. 团队所有者/管理员邀请成员 → 被邀请人接受/拒绝邀请
3. 团队所有者/管理员管理成员角色和权限
4. 团队所有者/管理员可删除团队或修改团队设置

### 会员管理流程
1. 团队所有者/管理员/教练分配会员 → 创建会员-教练关系
2. 教练可以查看和管理其负责的会员
3. 管理员可以在教练之间转移会员
4. 教练可以停用或重新激活会员

### 训练计划流程
1. 团队创建训练计划模板
2. 教练为会员分配训练计划
3. 会员执行训练计划课程
4. 会员/教练记录训练进度和反馈
5. 系统生成统计数据

## API 接口列表

### 团队管理接口

#### 创建团队
```
POST /team/teams/
```

**请求参数**
- `name`: 团队名称（必填，最大长度100）
- `description`: 团队描述（可选，最大长度500）
- `settings`: 团队设置（可选，JSON格式）

**返回数据**
```json
{
  "id": 1,
  "name": "团队名称",
  "description": "团队描述",
  "owner_id": 123,
  "status": 1,
  "created_at": "2023-05-20T12:00:00",
  "updated_at": "2023-05-20T12:00:00"
}
```

#### 获取团队详情
```
GET /team/teams/{team_id}
```

**路径参数**
- `team_id`: 团队ID

**返回数据**
```json
{
  "id": 1,
  "name": "团队名称",
  "description": "团队描述",
  "owner_id": 123,
  "status": 1,
  "created_at": "2023-05-20T12:00:00",
  "updated_at": "2023-05-20T12:00:00",
  "settings": {},
  "stats": {
    "member_count": 5,
    "client_count": 20,
    "active_client_count": 15,
    "completed_sessions": 150
  }
}
```

#### 更新团队信息
```
PUT /team/teams/{team_id}
```

**路径参数**
- `team_id`: 团队ID

**请求参数**
- `name`: 团队名称（可选，最大长度100）
- `description`: 团队描述（可选，最大长度500）
- `status`: 团队状态（可选，枚举值：ACTIVE=1, SUSPENDED=2, TERMINATED=3）
- `settings`: 团队设置（可选，JSON格式）

**返回数据**
与团队基本响应相同

#### 获取用户相关的团队列表
```
GET /team/teams/
```

**返回数据**
```json
[
  {
    "id": 1,
    "name": "团队名称",
    "description": "团队描述",
    "owner_id": 123,
    "status": 1,
    "created_at": "2023-05-20T12:00:00",
    "updated_at": "2023-05-20T12:00:00",
    "member_count": 5,
    "client_count": 20,
    "role": "OWNER"
  }
]
```

#### 删除团队
```
DELETE /team/teams/{team_id}
```

**路径参数**
- `team_id`: 团队ID

**说明**
- 此操作将团队标记为已删除状态，而不是真正删除

### 成员管理接口

#### 添加团队成员
```
POST /team/teams/{team_id}/members/
```

**路径参数**
- `team_id`: 团队ID

**请求参数**
- `user_id`: 用户ID（必填）
- `role`: 角色（必填，枚举值：OWNER=1, ADMIN=2, COACH=3, MEMBER=4）
- `status`: 状态（可选，默认为ACTIVE，枚举值：PENDING=1, ACTIVE=2, SUSPENDED=3, TERMINATED=4）

**返回数据**
成员关系信息

#### 更新成员角色
```
PUT /team/teams/{team_id}/members/{user_id}
```

**路径参数**
- `team_id`: 团队ID
- `user_id`: 用户ID

**请求参数**
- `role`: 角色（可选，枚举值：OWNER=1, ADMIN=2, COACH=3, MEMBER=4）
- `status`: 状态（可选，枚举值：PENDING=1, ACTIVE=2, SUSPENDED=3, TERMINATED=4）

**返回数据**
更新后的成员关系信息

#### 移除团队成员
```
DELETE /team/teams/{team_id}/members/{user_id}
```

**路径参数**
- `team_id`: 团队ID
- `user_id`: 用户ID

#### 获取团队成员列表
```
GET /team/teams/{team_id}/members
```

**路径参数**
- `team_id`: 团队ID

**查询参数**
- `role`: 角色过滤（可选）

**返回数据**
成员列表，包含用户信息和角色信息

### 会员管理接口

#### 分配会员
```
POST /team/teams/{team_id}/clients/
```

**路径参数**
- `team_id`: 团队ID

**请求参数**
- `client_id`: 会员ID（必填）
- `coach_id`: 教练ID（必填）
- `status`: 状态（可选，默认为ACTIVE，枚举值：PENDING=1, ACTIVE=2, PAUSED=3, TERMINATED=4）

**返回数据**
会员关系信息

#### 转移会员
```
POST /team/teams/{team_id}/clients/{client_relation_id}/transfer
```

**路径参数**
- `team_id`: 团队ID
- `client_relation_id`: 会员关系ID

**请求参数**
- `new_coach_id`: 新教练ID（必填）
- `reason`: 转移原因（可选）

**返回数据**
更新后的会员关系信息

#### 获取团队会员列表
```
GET /team/teams/{team_id}/clients
```

**路径参数**
- `team_id`: 团队ID

**查询参数**
- `status`: 状态过滤（可选）
- `coach_id`: 教练ID过滤（可选）

**返回数据**
会员列表，包含会员基本信息、教练信息和训练计划统计

#### 获取会员详情
```
GET /team/teams/{team_id}/clients/{client_relation_id}
```

**路径参数**
- `team_id`: 团队ID
- `client_relation_id`: 会员关系ID

**返回数据**
会员详细信息，包含会员基本信息、教练信息、统计数据和转移历史

#### 停用会员
```
PUT /team/teams/{team_id}/clients/{client_relation_id}/deactivate
```

**路径参数**
- `team_id`: 团队ID
- `client_relation_id`: 会员关系ID

**返回数据**
更新后的会员关系信息

#### 重新激活会员
```
PUT /team/teams/{team_id}/clients/{client_relation_id}/reactivate
```

**路径参数**
- `team_id`: 团队ID
- `client_relation_id`: 会员关系ID

**返回数据**
更新后的会员关系信息

### 邀请管理接口

#### 创建团队邀请
```
POST /team/teams/{team_id}/invitations/
```

**路径参数**
- `team_id`: 团队ID

**请求参数**
- `invitee_id`: 被邀请人ID（必填）
- `role`: 角色（必填，枚举值：OWNER=1, ADMIN=2, COACH=3, MEMBER=4）
- `expired_at`: 过期时间（可选，默认为7天后）

**返回数据**
邀请信息

#### 接受团队邀请
```
PUT /team/teams/invitations/{invitation_id}/accept
```

**路径参数**
- `invitation_id`: 邀请ID

**返回数据**
接受结果，包含团队ID和角色信息

#### 拒绝团队邀请
```
PUT /team/teams/invitations/{invitation_id}/reject
```

**路径参数**
- `invitation_id`: 邀请ID

**返回数据**
拒绝结果

#### 获取团队发出的邀请列表
```
GET /team/teams/{team_id}/invitations
```

**路径参数**
- `team_id`: 团队ID

**查询参数**
- `status`: 状态过滤（可选，枚举值：PENDING=1, ACCEPTED=2, REJECTED=3, EXPIRED=4）

**返回数据**
邀请列表

#### 获取用户收到的邀请列表
```
GET /team/user/invitations
```

**查询参数**
- `status`: 状态过滤（可选，枚举值：PENDING=1, ACCEPTED=2, REJECTED=3, EXPIRED=4）

**返回数据**
邀请列表

#### 取消邀请
```
DELETE /team/teams/invitations/{invitation_id}
```

**路径参数**
- `invitation_id`: 邀请ID

### 训练计划模板接口

#### 创建训练计划模板
```
POST /team/teams/{team_id}/templates/
```

**路径参数**
- `team_id`: 团队ID

**请求参数**
- `name`: 模板名称（必填）
- `description`: 模板描述（可选）
- `duration_weeks`: 持续周数（必填，1-52）
- `sessions_per_week`: 每周课程数（必填，1-7）
- `difficulty_level`: 难度级别（可选，1-5）
- `target_audience`: 目标受众（可选）
- `equipment_required`: 所需器材ID列表（可选）
- `is_public`: 是否公开（可选，默认为false）
- `exercises`: 训练动作列表（必填）

**返回数据**
模板信息

#### 获取团队训练计划模板列表
```
GET /team/teams/{team_id}/templates/
```

**路径参数**
- `team_id`: 团队ID

**返回数据**
模板列表

### 会员训练计划接口

#### 创建会员训练计划
```
POST /team/clients/{client_relation_id}/training-plans/
```

**路径参数**
- `client_relation_id`: 会员关系ID

**请求参数**
- `training_plan_id`: 训练计划ID（必填）
- `start_date`: 开始日期（必填）
- `end_date`: 结束日期（必填）
- `scheduled_time`: 时间安排（必填，JSON格式）
- `notes`: 备注（可选）

**返回数据**
训练计划信息

#### 获取会员训练计划列表
```
GET /team/clients/{client_relation_id}/training-plans/
```

**路径参数**
- `client_relation_id`: 会员关系ID

**查询参数**
- `status`: 状态过滤（可选）

**返回数据**
训练计划列表

#### 更新会员训练计划
```
PUT /team/clients/training-plans/{plan_id}
```

**路径参数**
- `plan_id`: 计划ID

**请求参数**
- `status`: 状态（可选）
- `start_date`: 开始日期（可选）
- `end_date`: 结束日期（可选）
- `scheduled_time`: 时间安排（可选，JSON格式）
- `notes`: 备注（可选）

**返回数据**
更新后的训练计划信息

### 训练课程接口

#### 获取训练课程列表
```
GET /team/training-plans/{plan_id}/sessions
```

**路径参数**
- `plan_id`: 计划ID

**查询参数**
- `status`: 状态过滤（可选）

**返回数据**
训练课程列表

#### 开始训练课程
```
POST /team/sessions/{session_id}/start
```

**路径参数**
- `session_id`: 课程ID

**返回数据**
更新后的训练课程信息

#### 记录训练组数据
```
POST /team/sessions/{session_id}/exercises/{exercise_id}/records
```

**路径参数**
- `session_id`: 课程ID
- `exercise_id`: 动作ID

**请求参数**
- `weight`: 重量（必填）
- `reps`: 重复次数（必填）
- `rpe`: 感知强度（可选，1-10）
- `notes`: 备注（可选）

**返回数据**
更新后的训练记录信息

#### 完成训练课程并提交反馈
```
PUT /team/sessions/{session_id}/complete
```

**路径参数**
- `session_id`: 课程ID

**请求参数**
- `feedback`: 反馈内容（可选）
- `mood_rating`: 心情评分（可选，1-5）
- `difficulty_rating`: 难度评分（可选，1-5）
- `notes`: 备注（可选）

**返回数据**
更新后的训练课程信息

### 统计接口

#### 获取团队统计数据
```
GET /team/teams/{team_id}/stats
```

**路径参数**
- `team_id`: 团队ID

**返回数据**
团队统计数据，包含成员数量、会员数量、活跃会员数量、训练课程数量、完成率等

#### 获取会员统计数据
```
GET /team/clients/{client_relation_id}/stats
```

**路径参数**
- `client_relation_id`: 会员关系ID

**返回数据**
会员统计数据，包含训练计划数量、已完成的训练计划数量、训练课程数量、完成率等

## 数据模型

### 团队相关枚举

```python
# 团队角色
class TeamRole(enum.IntEnum):
    OWNER = 1      # 拥有者
    ADMIN = 2      # 管理员
    COACH = 3      # 教练
    MEMBER = 4     # 普通成员

# 成员状态
class MembershipStatus(enum.IntEnum):
    PENDING = 1    # 待确认
    ACTIVE = 2     # 活跃
    SUSPENDED = 3  # 已暂停
    TERMINATED = 4 # 已终止

# 团队状态
class TeamStatus(enum.IntEnum):
    ACTIVE = 1     # 活跃
    SUSPENDED = 2  # 已暂停
    TERMINATED = 3 # 已终止

# 会员状态
class ClientStatus(enum.IntEnum):
    PENDING = 1    # 待确认
    ACTIVE = 2     # 活跃
    PAUSED = 3     # 已暂停
    TERMINATED = 4 # 已终止

# 邀请状态
class InvitationStatus(enum.IntEnum):
    PENDING = 1    # 待处理
    ACCEPTED = 2   # 已接受
    REJECTED = 3   # 已拒绝
    EXPIRED = 4    # 已过期
```

### 主要数据模型

1. **Team**: 团队基本信息
   - 包含团队ID、名称、描述、拥有者ID、状态、设置、创建时间、更新时间等

2. **TeamMembership**: 团队成员关系
   - 定义用户与团队的关系，包含团队ID、用户ID、角色、状态、权限等

3. **TeamInvitation**: 团队邀请
   - 记录邀请信息，包含团队ID、邀请人ID、被邀请人ID、角色、状态、过期时间等

4. **TeamStats**: 团队统计
   - 存储团队的统计数据，包括成员数量、会员数量、活跃会员数量等

5. **ClientRelation**: 会员关系
   - 定义会员与教练的关系，包含会员ID、教练ID、团队ID、状态等

6. **ClientTransferHistory**: 会员转移历史
   - 记录会员转移记录，包含会员关系ID、原教练ID、新教练ID、原因、时间等

7. **TrainingPlanTemplate**: 训练计划模板
   - 定义训练计划模板，包含名称、描述、周数、每周课程数、难度等信息

8. **TemplateExercise**: 模板训练动作
   - 定义模板中的训练动作，包含动作ID、组数、次数、重量等信息

9. **ClientTrainingPlan**: 会员训练计划
   - 为会员分配的训练计划，包含会员关系ID、模板ID、开始日期、结束日期等

10. **TrainingSession**: 训练课程
    - 定义训练课程，包含计划ID、日期、状态、反馈等信息

11. **SessionExerciseRecord**: 训练记录
    - 记录训练课程中的动作完成情况，包含课程ID、动作ID、重量、次数等

## 数据流程闭环

1. **团队创建与管理流程**
   - 用户创建团队 (TeamService.create_team)
   - 系统自动创建团队统计数据 (TeamStatsService.initialize_stats)
   - 用户管理团队信息和设置 (TeamService.update_team)
   - 团队所有者可删除团队 (TeamService.delete_team)

2. **成员管理流程**
   - 团队所有者/管理员创建邀请 (TeamMemberService.create_invitation)
   - 被邀请人接受/拒绝邀请 (TeamMemberService.accept_invitation/reject_invitation)
   - 成功加入后成为团队成员 (TeamMemberService.add_team_member)
   - 管理员可更新成员角色和状态 (TeamMemberService.update_member_role)
   - 管理员可移除成员 (TeamMemberService.remove_team_member)

3. **会员管理流程**
   - 教练分配会员 (TeamClientService.assign_client)
   - 管理员可在教练间转移会员 (TeamClientService.transfer_client)
   - 教练可管理会员状态 (TeamClientService.deactivate_client/reactivate_client)
   - 系统记录会员转移历史 (TeamClientService.record_transfer_history)

4. **训练计划管理流程**
   - 团队创建训练计划模板 (TeamTrainingService.create_template)
   - 教练为会员分配训练计划 (TeamTrainingService.create_client_plan)
   - 系统自动生成训练课程 (TeamTrainingService.generate_sessions)
   - 会员可查看分配的训练计划 (TeamTrainingService.get_client_plans)

5. **训练执行与记录流程**
   - 会员开始训练课程 (TeamTrainingService.start_session)
   - 会员记录训练组数据 (TeamTrainingService.record_exercise_set)
   - 会员完成训练并提交反馈 (TeamTrainingService.complete_session)
   - 系统更新训练统计数据 (TeamStatsService.update_training_stats)

6. **数据统计流程**
   - 系统实时更新团队统计数据 (TeamStatsService.calculate_team_stats)
   - 系统实时更新会员统计数据 (TeamStatsService.calculate_client_stats)
   - 管理员和教练可查看统计数据 (TeamStatsService.get_team_stats/get_client_stats)

这些流程构成了一个完整的闭环，从团队创建到成员管理，再到会员训练的执行与记录，最后通过统计数据分析训练效果，帮助管理员和教练更好地管理团队和指导会员。
