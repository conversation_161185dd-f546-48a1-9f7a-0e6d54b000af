# 团队训练管理系统设计文档

## 目录
1. [系统概述](#系统概述)
2. [数据模型](#数据模型)
3. [核心功能](#核心功能)
4. [API接口](#api接口)
5. [权限控制](#权限控制)
6. [数据统计](#数据统计)
7. [实现注意事项](#实现注意事项)
8. [技术架构](#技术架构)

## 系统概述

团队管理系统基于现有的用户体系(`app/models/user.py`)进行扩展，通过角色和权限的组合实现团队管理功能。系统支持教练团队的组织管理、会员分配、数据统计等功能。

### 主要特性
- 基于现有User模型的角色扩展
- 团队组织与成员管理
- 会员分配与转移追踪
- 多维度数据统计
- 标签化会员管理

## 数据模型

### 核心模型关系
```
User (app/models/user.py)
 ├── TeamMembership (团队成员关系)
 ├── TeamOwnership (团队所有权)
 ├── ClientRelation (会员关系)
 ├── TeamInvitation (团队邀请)
 ├── TeamStats (团队统计)
 └── ClientTrainingPlan (会员训练计划)
      ├── TrainingPlanTemplate (训练计划模板)
      ├── TrainingSession (训练记录)
      └── SessionExerciseRecord (训练动作记录)
```

### 详细模型定义

#### TeamMembership（团队成员关系）
```python
class TeamMembership(Base):
    __tablename__ = "team_memberships"

    id = Column(Integer, primary_key=True, index=True)
    team_id = Column(Integer, ForeignKey("teams.id"))
    user_id = Column(Integer, ForeignKey("users.id"))
    role = Column(Enum(TeamRole))  # 使用Enum类型
    status = Column(Enum(MembershipStatus))
    joined_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())

    # 关系
    team = relationship("Team", back_populates="memberships")
    user = relationship("User", back_populates="team_memberships")
    
    __table_args__ = (
        UniqueConstraint('team_id', 'user_id', name='uq_team_user'),
    )
```

#### Team（团队）
```python
class Team(Base):
    __tablename__ = "teams"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True)
    description = Column(String)
    owner_id = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())
    
    # 状态和配置
    status = Column(Enum(TeamStatus))
    settings = Column(JSON, default={})
    
    # 关系
    owner = relationship("User", back_populates="owned_teams")
    memberships = relationship("TeamMembership", back_populates="team")
    clients = relationship("ClientRelation", back_populates="team")
    invitations = relationship("TeamInvitation", back_populates="team")
    stats = relationship("TeamStats", back_populates="team", uselist=False)
```

#### ClientRelation（会员关系）
```python
class ClientRelation(Base):
    __tablename__ = "client_relations"

    id = Column(Integer, primary_key=True, index=True)
    team_id = Column(Integer, ForeignKey("teams.id"))
    client_id = Column(Integer, ForeignKey("users.id"))
    coach_id = Column(Integer, ForeignKey("users.id"))
    status = Column(Enum(ClientStatus))
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())

    # 关系
    team = relationship("Team", back_populates="clients")
    client = relationship("User", foreign_keys=[client_id])
    coach = relationship("User", foreign_keys=[coach_id])
    transfer_history = relationship("ClientTransferHistory")
```

#### TeamInvitation（团队邀请）
```python
class TeamInvitation(Base):
    __tablename__ = "team_invitations"

    id = Column(Integer, primary_key=True, index=True)
    team_id = Column(Integer, ForeignKey("teams.id"))
    inviter_id = Column(Integer, ForeignKey("users.id"))
    invitee_id = Column(Integer, ForeignKey("users.id"))
    role = Column(Enum(TeamRole))
    status = Column(Enum(InvitationStatus))
    created_at = Column(DateTime, default=func.now())
    expired_at = Column(DateTime)
    
    # 关系
    team = relationship("Team", back_populates="invitations")
    inviter = relationship("User", foreign_keys=[inviter_id])
    invitee = relationship("User", foreign_keys=[invitee_id])
```

#### ClientTrainingPlan（会员训练计划关系）
```python
class ClientTrainingPlan(Base):
    __tablename__ = "client_training_plans"

    id = Column(Integer, primary_key=True, index=True)
    client_relation_id = Column(Integer, ForeignKey("client_relations.id"))
    training_plan_id = Column(Integer, ForeignKey("training_plans.id"))
    coach_id = Column(Integer, ForeignKey("users.id"))
    status = Column(Enum('scheduled', 'in_progress', 'completed', 'cancelled'))
    start_date = Column(DateTime, nullable=False)
    end_date = Column(DateTime, nullable=False)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())
    
    # 预约相关
    scheduled_time = Column(JSON)  # 存储每周固定训练时间
    next_session = Column(DateTime)  # 下次训练时间
    
    # 进度追踪
    completion_rate = Column(Float, default=0)
    last_workout_date = Column(DateTime)
    
    # 备注
    notes = Column(Text)
    
    # 关系
    client_relation = relationship("ClientRelation", back_populates="training_plans")
    training_plan = relationship("TrainingPlan", back_populates="client_plans")
    coach = relationship("User", back_populates="coached_plans")
    
    __table_args__ = (
        UniqueConstraint(
            'client_relation_id', 
            'training_plan_id',
            'start_date',
            name='uq_client_plan_period'
        ),
    )
```

#### TrainingPlanTemplate（训练计划模板）
```python
class TrainingPlanTemplate(Base):
    __tablename__ = "training_plan_templates"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    description = Column(Text)
    creator_id = Column(Integer, ForeignKey("users.id"))
    team_id = Column(Integer, ForeignKey("teams.id"))
    duration_weeks = Column(Integer, default=4)
    sessions_per_week = Column(Integer, default=3)
    difficulty_level = Column(Integer)
    target_audience = Column(String)
    equipment_required = Column(ARRAY(Integer))
    is_public = Column(Boolean, default=False)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())
    
    # 关系
    exercises = relationship("TemplateExercise", back_populates="template")
    creator = relationship("User", back_populates="created_templates")
```

#### TrainingSession（训练记录）
```python
class TrainingSession(Base):
    __tablename__ = "training_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    client_plan_id = Column(Integer, ForeignKey("client_training_plans.id"))
    scheduled_start = Column(DateTime, nullable=False)
    actual_start = Column(DateTime)
    actual_end = Column(DateTime)
    status = Column(Enum('scheduled', 'in_progress', 'completed', 'cancelled'))
    completion_rate = Column(Float, default=0)
    feedback = Column(Text)
    mood_rating = Column(Integer)  # 1-5
    difficulty_rating = Column(Integer)  # 1-5
    notes = Column(Text)
    
    # 关系
    exercise_records = relationship("SessionExerciseRecord", back_populates="session")
    client_plan = relationship("ClientTrainingPlan", back_populates="sessions")
```

#### SessionExerciseRecord（训练动作记录）
```python
class SessionExerciseRecord(Base):
    __tablename__ = "session_exercise_records"
    
    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(Integer, ForeignKey("training_sessions.id"))
    exercise_id = Column(Integer, ForeignKey("exercises.id"))
    order = Column(Integer)
    sets_planned = Column(Integer)
    sets_completed = Column(Integer, default=0)
    
    # 详细记录（JSON格式存储每组详情）
    set_records = Column(JSON, default=list)
    # 示例: [
    #   {"set": 1, "weight": 50, "reps": 12, "completed": true},
    #   {"set": 2, "weight": 52.5, "reps": 10, "completed": true}
    # ]
    
    notes = Column(Text)
    status = Column(Enum('pending', 'in_progress', 'completed', 'skipped'))
```

### 枚举定义

```python
class TeamRole(enum.IntEnum):
    """团队角色"""
    OWNER = 1
    ADMIN = 2
    COACH = 3
    ASSISTANT = 4

class MembershipStatus(enum.IntEnum):
    """成员状态"""
    ACTIVE = 1
    INACTIVE = 2
    SUSPENDED = 3

class TeamStatus(enum.IntEnum):
    """团队状态"""
    ACTIVE = 1
    INACTIVE = 2
    SUSPENDED = 3

class ClientStatus(enum.IntEnum):
    """会员状态"""
    ACTIVE = 1
    INACTIVE = 2
    TRANSFERRED = 3
```

## 核心功能流程

### 1. 团队创建与初始化

```python
async def create_team(
    db: Session,
    owner: User,
    team_data: TeamCreate
) -> Team:
    """创建新团队"""
    async with db.begin():
        # 创建团队
        team = Team(
            name=team_data.name,
            description=team_data.description,
            owner_id=owner.id,
            status=TeamStatus.ACTIVE
        )
        db.add(team)
        await db.flush()
        
        # 创建所有者成员关系
        membership = TeamMembership(
            team_id=team.id,
            user_id=owner.id,
            role=TeamRole.OWNER,
            status=MembershipStatus.ACTIVE
        )
        db.add(membership)
        
        # 初始化团队统计
        stats = TeamStats(team_id=team.id)
        db.add(stats)
    
    return team
```

### 2. 会员分配流程

```python
async def assign_client(
    db: Session,
    team: Team,
    client: User,
    coach: User,
    current_user: User
) -> ClientRelation:
    """分配会员给教练"""
    # 权限检查
    if not await check_team_permission(team, current_user, [TeamRole.OWNER, TeamRole.ADMIN]):
        raise PermissionError("No permission to assign clients")
    
    async with db.begin():
        # 创建会员关系
        relation = ClientRelation(
            team_id=team.id,
            client_id=client.id,
            coach_id=coach.id,
            status=ClientStatus.ACTIVE
        )
        db.add(relation)
        
        # 更新统计
        await update_team_stats(db, team.id)
    
    return relation
```

### 会员训练计划管理

```python
async def assign_training_plan(
    db: Session,
    client_relation_id: int,
    plan_data: ClientPlanCreate,
    current_user: User
) -> ClientTrainingPlan:
    """为会员分配训练计划"""
    async with db.begin():
        # 验证客户关系
        client_relation = await validate_client_relation(
            db, 
            client_relation_id, 
            current_user.id
        )
        
        # 创建计划关系
        client_plan = ClientTrainingPlan(
            client_relation_id=client_relation_id,
            training_plan_id=plan_data.training_plan_id,
            coach_id=current_user.id,
            start_date=plan_data.start_date,
            end_date=plan_data.end_date,
            scheduled_time=plan_data.scheduled_time,
            notes=plan_data.notes
        )
        db.add(client_plan)
        
        # 计算并设置下次训练时间
        next_session = calculate_next_session(
            plan_data.start_date,
            plan_data.scheduled_time
        )
        client_plan.next_session = next_session
        
        # 更新统计
        await update_client_training_stats(db, client_relation_id)
        
    return client_plan
```

## API接口设计

### 团队管理接口

```python
@router.post("/teams/", response_model=TeamResponse)
async def create_team(
    team_data: TeamCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建新团队"""
    return await create_team(db, current_user, team_data)

@router.get("/teams/{team_id}", response_model=TeamDetail)
async def get_team(
    team_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取团队详情"""
    team = await get_team_by_id(db, team_id)
    if not await check_team_access(team, current_user):
        raise HTTPException(status_code=403, detail="No access to this team")
    return team

@router.put("/teams/{team_id}", response_model=TeamResponse)
async def update_team(
    team_id: int,
    team_data: TeamUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新团队信息"""
    # 实现更新逻辑

@router.get("/teams/", response_model=List[TeamListResponse])
async def list_teams(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户相关的团队列表"""
    return await get_user_teams(db, current_user.id)

@router.get("/teams/{team_id}", response_model=TeamDetailResponse)
async def get_team_detail(
    team_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取团队详细信息"""
    return await get_team_with_stats(db, team_id, current_user.id)

@router.get("/teams/{team_id}/members", response_model=List[TeamMemberResponse])
async def list_team_members(
    team_id: int,
    role: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取团队成员列表"""
    return await get_team_members(db, team_id, role)
```

### 成员管理接口

```python
@router.post("/teams/{team_id}/members/", response_model=MembershipResponse)
async def add_team_member(
    team_id: int,
    member_data: MembershipCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """添加团队成员"""
    # 实现添加成员逻辑

@router.put("/teams/{team_id}/members/{user_id}", response_model=MembershipResponse)
async def update_member_role(
    team_id: int,
    user_id: int,
    role_data: RoleUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新成员角色"""
    # 实现角色更新逻辑
```

### 会员管理接口

```python
@router.post("/teams/{team_id}/clients/", response_model=ClientRelationResponse)
async def assign_team_client(
    team_id: int,
    client_data: ClientAssignment,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """分配会员"""
    # 实现会员分配逻辑

@router.post("/teams/{team_id}/clients/{client_id}/transfer", response_model=TransferResponse)
async def transfer_client(
    team_id: int,
    client_id: int,
    transfer_data: ClientTransfer,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """转移会员"""
    # 实现会员转移逻辑

@router.get("/teams/{team_id}/clients", response_model=List[ClientListResponse])
async def list_team_clients(
    team_id: int,
    status: Optional[str] = None,
    coach_id: Optional[int] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取团队会员列表"""
    return await get_team_clients(db, team_id, status, coach_id)

@router.get("/clients/{client_id}", response_model=ClientDetailResponse)
async def get_client_detail(
    client_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取会员详细信息"""
    return await get_client_with_stats(db, client_id, current_user.id)
```

### 训练计划模板接口

```python
@router.post("/teams/{team_id}/templates/", response_model=TemplateResponse)
async def create_plan_template(
    team_id: int,
    template_data: TemplateCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建训练计划模板"""
    return await create_training_template(db, team_id, template_data, current_user)

@router.get("/teams/{team_id}/templates/", response_model=List[TemplateListResponse])
async def list_plan_templates(
    team_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取团队训练计划模板列表"""
    return await get_team_templates(db, team_id)
```

### 训练记录接口

```python
@router.post(
    "/clients/{client_id}/training-plans/",
    response_model=ClientTrainingPlanResponse
)
async def create_client_training_plan(
    client_id: int,
    plan_data: ClientPlanCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建会员训练计划"""
    return await assign_training_plan(db, client_id, plan_data, current_user)

@router.get(
    "/clients/{client_id}/training-plans/",
    response_model=List[ClientTrainingPlanResponse]
)
async def list_client_training_plans(
    client_id: int,
    status: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取会员训练计划列表"""
    return await get_client_training_plans(db, client_id, status)

@router.put(
    "/clients/training-plans/{plan_id}",
    response_model=ClientTrainingPlanResponse
)
async def update_client_training_plan(
    plan_id: int,
    update_data: ClientPlanUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新会员训练计划"""
    return await update_training_plan(db, plan_id, update_data, current_user)

@router.post("/sessions/{session_id}/start", response_model=SessionResponse)
async def start_training_session(
    session_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """开始训练"""
    return await start_session(db, session_id, current_user)

@router.post("/sessions/{session_id}/exercises/{exercise_id}/records")
async def record_exercise_set(
    session_id: int,
    exercise_id: int,
    set_data: SetRecordCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """记录训练组数据"""
    return await create_set_record(db, session_id, exercise_id, set_data)

@router.put("/sessions/{session_id}/complete", response_model=SessionResponse)
async def complete_training_session(
    session_id: int,
    feedback_data: SessionFeedback,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """完成训练并提交反馈"""
    return await complete_session(db, session_id, feedback_data, current_user)
```

## 数据统计实现

### 实时统计更新

```python
async def update_team_stats(db: Session, team_id: int):
    """更新团队统计数据"""
    stats = await db.query(TeamStats).filter(TeamStats.team_id == team_id).first()
    if not stats:
        stats = TeamStats(team_id=team_id)
        db.add(stats)
    
    # 更新各项统计
    stats.total_members = await count_team_members(db, team_id)
    stats.total_clients = await count_team_clients(db, team_id)
    stats.active_clients_30d = await count_active_clients(db, team_id, days=30)
    stats.updated_at = func.now()
```

### 定期统计任务

```python
@celery_app.task
async def calculate_team_analytics():
    """计算团队分析数据"""
    async with SessionLocal() as db:
        teams = await db.query(Team).filter(Team.status == TeamStatus.ACTIVE).all()
        for team in teams:
            await update_team_stats(db, team.id)
            await calculate_team_growth(db, team.id)
            await analyze_team_performance(db, team.id)
```

### 数据统计扩展

```python
async def update_client_training_stats(db: Session, client_relation_id: int):
    """更新会员训练统计"""
    stats = await db.query(ClientStats).filter(
        ClientStats.client_relation_id == client_relation_id
    ).first()
    
    if not stats:
        stats = ClientStats(client_relation_id=client_relation_id)
        db.add(stats)
    
    # 计算统计数据
    total_plans = await count_client_plans(db, client_relation_id)
    completed_plans = await count_client_completed_plans(db, client_relation_id)
    completion_rate = completed_plans / total_plans if total_plans > 0 else 0
    
    # 更新统计
    stats.total_training_plans = total_plans
    stats.completed_training_plans = completed_plans
    stats.plan_completion_rate = completion_rate
    stats.last_training_date = await get_last_training_date(db, client_relation_id)
    stats.updated_at = func.now()
```

## 权限控制实现

### 权限检查装饰器

```python
def require_team_permission(required_roles: List[TeamRole]):
    """团队权限检查装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            team_id = kwargs.get('team_id')
            current_user = kwargs.get('current_user')
            
            if not team_id or not current_user:
                raise HTTPException(status_code=400, detail="Missing required parameters")
            
            async with SessionLocal() as db:
                membership = await get_team_membership(db, team_id, current_user.id)
                if not membership or membership.role not in required_roles:
                    raise HTTPException(status_code=403, detail="Insufficient permissions")
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator
```

## 数据完整性保证

### 事务管理

```python
async def transfer_client_with_history(
    db: Session,
    client_relation: ClientRelation,
    new_coach_id: int,
    reason: str
) -> ClientRelation:
    """带历史记录的会员转移"""
    async with db.begin():
        # 记录转移历史
        history = ClientTransferHistory(
            client_relation_id=client_relation.id,
            from_coach_id=client_relation.coach_id,
            to_coach_id=new_coach_id,
            reason=reason,
            transferred_at=func.now()
        )
        db.add(history)
        
        # 更新关系
        client_relation.coach_id = new_coach_id
        client_relation.updated_at = func.now()
        
        # 更新统计
        await update_team_stats(db, client_relation.team_id)
    
    return client_relation
```

## 缓存策略

```python
async def get_team_stats_cached(
    redis: Redis,
    db: Session,
    team_id: int
) -> Dict:
    """获取缓存的团队统计数据"""
    cache_key = f"team_stats:{team_id}"
    
    # 尝试获取缓存
    cached = await redis.get(cache_key)
    if cached:
        return json.loads(cached)
    
    # 计算统计数据
    stats = await calculate_team_stats(db, team_id)
    
    # 设置缓存
    await redis.setex(
        cache_key,
        timedelta(minutes=15),
        json.dumps(stats)
    )
    
    return stats
```

## 异常处理

```python
class TeamServiceException(Exception):
    """团队服务异常基类"""
    pass

class TeamNotFoundException(TeamServiceException):
    """团队不存在异常"""
    pass

class InsufficientPermissionException(TeamServiceException):
    """权限不足异常"""
    pass

@router.exception_handler(TeamServiceException)
async def team_service_exception_handler(request: Request, exc: TeamServiceException):
    """团队服务异常处理器"""
    if isinstance(exc, TeamNotFoundException):
        return JSONResponse(
            status_code=404,
            content={"detail": str(exc)}
        )
    elif isinstance(exc, InsufficientPermissionException):
        return JSONResponse(
            status_code=403,
            content={"detail": str(exc)}
        )
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )
```

## 监控与日志

```python
async def log_team_activity(
    db: Session,
    team_id: int,
    user_id: int,
    action: str,
    details: Dict
):
    """记录团队活动日志"""
    log = TeamActivityLog(
        team_id=team_id,
        user_id=user_id,
        action=action,
        details=details,
        created_at=func.now()
    )
    db.add(log)
    await db.flush()
```

## 核心功能实现

### 训练计划分配与时间管理

```python
async def validate_training_schedule(
    db: Session,
    client_id: int,
    coach_id: int,
    schedule_data: ScheduleData
) -> bool:
    """验证训练时间安排"""
    # 检查教练时间冲突
    coach_conflicts = await check_coach_schedule_conflicts(
        db, coach_id, schedule_data
    )
    if coach_conflicts:
        raise ScheduleConflictException("教练时间冲突")
    
    # 检查会员时间冲突
    client_conflicts = await check_client_schedule_conflicts(
        db, client_id, schedule_data
    )
    if client_conflicts:
        raise ScheduleConflictException("会员时间冲突")
    
    return True

async def create_training_sessions(
    db: Session,
    client_plan: ClientTrainingPlan,
    schedule_data: ScheduleData
) -> List[TrainingSession]:
    """创建训练课程安排"""
    sessions = []
    current_date = client_plan.start_date
    
    while current_date <= client_plan.end_date:
        if should_schedule_session(current_date, schedule_data.weekly_schedule):
            session = TrainingSession(
                client_plan_id=client_plan.id,
                scheduled_start=combine_date_time(current_date, schedule_data.time),
                status='scheduled'
            )
            sessions.append(session)
        current_date += timedelta(days=1)
    
    db.add_all(sessions)
    return sessions
```

### 实时训练记录

```python
async def start_session(
    db: Session,
    session_id: int,
    current_user: User
) -> TrainingSession:
    """开始训练课程"""
    async with db.begin():
        session = await get_session(db, session_id)
        if not session:
            raise SessionNotFoundException()
            
        # 验证权限
        await validate_session_access(session, current_user)
        
        # 更新状态
        session.status = 'in_progress'
        session.actual_start = func.now()
        
        # 初始化训练记录
        exercise_records = []
        for exercise in session.planned_exercises:
            record = SessionExerciseRecord(
                session_id=session.id,
                exercise_id=exercise.id,
                sets_planned=exercise.sets,
                status='pending'
            )
            exercise_records.append(record)
        
        db.add_all(exercise_records)
    
    return session

async def update_exercise_record(
    db: Session,
    record_id: int,
    set_data: SetRecordCreate
) -> SessionExerciseRecord:
    """更新训练记录"""
    async with db.begin():
        record = await get_exercise_record(db, record_id)
        if not record:
            raise RecordNotFoundException()
        
        # 更新组记录
        set_records = record.set_records or []
        set_records.append({
            "set": len(set_records) + 1,
            "weight": set_data.weight,
            "reps": set_data.reps,
            "completed": True,
            "timestamp": datetime.now().isoformat()
        })
        
        record.set_records = set_records
        record.sets_completed = len(set_records)
        
        # 更新状态
        if record.sets_completed >= record.sets_planned:
            record.status = 'completed'
        
        # 更新会话完成率
        await update_session_completion_rate(db, record.session_id)
    
    return record
```

## 数据流闭环

1. 计划创建流程：
   - 选择/创建计划模板
   - 分配给会员
   - 设置训练时间
   - 生成训练课程安排

2. 训练执行流程：
   - 开始训练
   - 记录每个动作的完成情况
   - 提交训练反馈
   - 更新统计数据

3. 数据统计流程：
   - 实时更新完成率
   - 定期计算统计指标
   - 生成分析报告

## 实现注意事项

1. 时间冲突检查：
   - 教练和会员的时间安排冲突检查
   - 考虑时区问题
   - 支持重复性安排

2. 训练记录：
   - 支持实时记录和修改
   - 记录训练过程中的调整
   - 保存训练历史版本

3. 数据一致性：
   - 使用事务确保数据一致性
   - 定期数据校验
   - 异常情况处理

4. 性能优化：
   - 合理使用缓存
   - 分页加载
   - 延迟加载关联数据

## 后续优化建议

1. 实现团队层级结构，支持子团队管理
2. 添加团队资源配额管理
3. 实现更细粒度的权限控制
4. 优化统计性能，引入更多缓存策略
5. 添加团队活动日志分析功能
6. 实现团队数据导出功能
7. 添加团队运营分析报表
8. 优化会员转移流程，支持批量操作
9. 添加训练计划智能推荐
10. 实现训练计划数据可视化
11. 支持批量计划管理
12. 添加计划模板市场

## 数据模型关系

### 核心模型
```
TrainingPlan (app/models/training_plan.py)
 ├── Workout (训练日计划)
 │    └── WorkoutExercise (训练动作)
 │         └── SetRecord (组记录)
 ├── TrainingSession (训练记录)
 │    └── SessionExerciseRecord (动作记录)
 └── TrainingPlanTemplate (计划模板)
```

## 训练计划管理

### 1. 计划模板功能

```python
async def create_plan_template(
    db: Session,
    team_id: int,
    template_data: dict,
    creator_id: int
) -> TrainingPlan:
    """创建训练计划模板"""
    template = TrainingPlan(
        user_id=creator_id,
        team_id=team_id,
        is_template=True,
        plan_name=template_data["name"],
        description=template_data["description"],
        duration_weeks=template_data["duration_weeks"],
        difficulty_level=template_data["difficulty_level"],
        target_audience=template_data["target_audience"],
        equipment_required=template_data["equipment_required"]
    )
    
    db.add(template)
    await db.flush()
    
    # 创建训练日计划
    for workout_data in template_data["workouts"]:
        workout = await create_workout(db, template.id, workout_data)
        
        # 添加训练动作
        for exercise_data in workout_data["exercises"]:
            await create_workout_exercise(db, workout.id, exercise_data)
    
    return template
```

### 2. 训练计划分配

```python
async def assign_training_plan(
    db: Session,
    client_id: int,
    template_id: int,
    schedule_config: dict
) -> TrainingPlan:
    """基于模板分配训练计划"""
    # 获取模板
    template = await db.query(TrainingPlan).filter(
        TrainingPlan.id == template_id,
        TrainingPlan.is_template == True
    ).first()
    
    # 创建个人计划
    plan = TrainingPlan(
        user_id=client_id,
        template_id=template_id,
        plan_name=template.plan_name,
        description=template.description,
        duration_weeks=template.duration_weeks,
        schedule_config=schedule_config,
        is_template=False
    )
    
    db.add(plan)
    await db.flush()
    
    # 复制训练内容
    await copy_workouts_from_template(db, template.id, plan.id)
    
    # 生成训练课程
    await generate_training_sessions(db, plan.id, schedule_config)
    
    return plan
```

### 3. 训练动作管理

```python
async def create_workout_exercise(
    db: Session,
    workout_id: int,
    exercise_data: dict
) -> WorkoutExercise:
    """创建训练动作"""
    exercise = WorkoutExercise(
        workout_id=workout_id,
        exercise_id=exercise_data["exercise_id"],
        sets=exercise_data["sets"],
        reps=exercise_data["reps"],
        rest_seconds=exercise_data["rest_seconds"],
        order=exercise_data["order"],
        exercise_type=exercise_data["exercise_type"],
        weight=exercise_data["weight"],
        target_rpe=exercise_data.get("target_rpe"),
        tempo=exercise_data.get("tempo"),
        set_type=exercise_data.get("set_type", "normal"),
        progression_type=exercise_data.get("progression_type"),
        progression_config=exercise_data.get("progression_config")
    )
    
    db.add(exercise)
    return exercise
```

### 4. 训练记录追踪

```python
async def record_exercise_set(
    db: Session,
    session_id: int,
    exercise_id: int,
    set_data: dict
) -> SetRecord:
    """记录训练组数据"""
    record = SetRecord(
        session_id=session_id,
        exercise_id=exercise_id,
        set_number=set_data["set_number"],
        weight=set_data["weight"],
        reps=set_data["reps"],
        rpe=set_data.get("rpe"),
        notes=set_data.get("notes"),
        completed_at=func.now()
    )
    
    db.add(record)
    
    # 更新完成进度
    await update_exercise_completion(db, session_id, exercise_id)
    await update_session_completion(db, session_id)
    
    return record
```

## 实现细节

### 1. 时间管理

```python
def generate_training_schedule(plan: TrainingPlan, config: dict) -> List[DateTime]:
    """生成训练时间表"""
    schedule = []
    start_date = config["start_date"]
    weeks = plan.duration_weeks
    
    for week in range(weeks):
        for day in config["weekly_schedule"]:
            training_time = combine_date_time(
                start_date + timedelta(days=week * 7 + day["day_of_week"]),
                day["time"]
            )
            schedule.append(training_time)
    
    return schedule
```

### 2. 进度追踪

```python
async def update_training_progress(
    db: Session,
    plan_id: int
) -> Dict:
    """更新训练计划进度"""
    stats = {
        "total_sessions": 0,
        "completed_sessions": 0,
        "completion_rate": 0,
        "total_volume": 0,
        "average_intensity": 0
    }
    
    # 统计训练课程完成情况
    sessions = await db.query(TrainingSession).filter(
        TrainingSession.training_plan_id == plan_id
    ).all()
    
    stats["total_sessions"] = len(sessions)
    stats["completed_sessions"] = sum(1 for s in sessions if s.status == "completed")
    
    if stats["total_sessions"] > 0:
        stats["completion_rate"] = stats["completed_sessions"] / stats["total_sessions"]
    
    # 更新计划状态
    plan = await db.query(TrainingPlan).get(plan_id)
    plan.completion_rate = stats["completion_rate"]
    plan.updated_at = func.now()
    
    return stats
```

### 3. 数据验证

```python
def validate_exercise_data(exercise_data: dict) -> bool:
    """验证训练动作数据"""
    required_fields = ["exercise_id", "sets", "reps", "order"]
    
    # 检查必填字段
    for field in required_fields:
        if field not in exercise_data:
            raise ValueError(f"Missing required field: {field}")
    
    # 验证数值范围
    if exercise_data["sets"] < 1 or exercise_data["sets"] > 20:
        raise ValueError("Invalid sets number")
    
    if exercise_data.get("target_rpe"):
        if not (1 <= exercise_data["target_rpe"] <= 10):
            raise ValueError("Invalid RPE value")
    
    return True
```

## 注意事项

1. 训练计划模板：
   - 支持复制和修改现有模板
   - 验证模板完整性
   - 处理模板版本管理

2. 训练记录：
   - 支持实时修改和调整
   - 记录训练历史
   - 处理异常情况（如中断）

3. 数据一致性：
   - 使用事务确保关联数据一致
   - 定期验证数据完整性
   - 处理并发访问

4. 性能优化：
   - 缓存常用模板
   - 分页加载训练历史
   - 优化关联查询
