# 营养素目标值接口文档

## 接口概述

提供根据用户年龄、性别和特殊状态（如孕期/哺乳期）获取每日推荐营养素摄入量的接口。

## 基础信息

- **Base URL**: `/api/v1/nutrient`
- **需要认证**: 否

## 接口列表

### 1. 获取每日营养素推荐摄入量（POST）

#### 请求信息

- **路径**: `/daily-target`
- **方法**: POST
- **描述**: 根据用户年龄、性别和孕期阶段获取每日营养素推荐摄入量

#### 请求参数

**请求体**: JSON格式

| 参数名 | 类型 | 必填 | 描述 |
| ----- | ---- | ---- | ---- |
| age | number | 是 | 用户年龄(岁) |
| sex | string | 是 | 性别：只能是'male'或'female' |
| pregnancy_stage | string | 否 | 孕期阶段：可选'early'(早期)、'mid'(中期)、'late'(晚期)、'lactation'(哺乳期)或不提供 |
| dietary_names | array | 否 | 营养素字段名称列表，如'vitamin_a'、'phosphor'等，用于查询特定营养素的推荐值 |

#### 示例请求

```json
{
  "age": 30,
  "sex": "female",
  "pregnancy_stage": "mid",
  "dietary_names": ["vitamin_a", "phosphor"]
}
```

### 2. 获取每日营养素推荐摄入量（GET）

#### 请求信息

- **路径**: `/recommendation`
- **方法**: GET
- **描述**: 根据用户年龄、性别和孕期阶段获取每日营养素推荐摄入量(GET方法)

#### 请求参数

**查询参数**:

| 参数名 | 类型 | 必填 | 描述 |
| ----- | ---- | ---- | ---- |
| age | number | 是 | 用户年龄(岁) |
| sex | string | 是 | 性别：只能是'male'或'female' |
| pregnancy_stage | string | 否 | 孕期阶段：可选'early'(早期)、'mid'(中期)、'late'(晚期)、'lactation'(哺乳期)或不提供 |
| dietary_names | array | 否 | 营养素字段名称列表，如'vitamin_a'、'phosphor'等，用于查询特定营养素的推荐值，多个值以逗号分隔 |

#### 示例请求

```
GET /api/v1/nutrient/recommendation?age=30&sex=female&pregnancy_stage=mid&dietary_names=vitamin_a,phosphor
```

## 响应格式

两个接口返回相同的响应格式：

| 字段名 | 类型 | 描述 |
| ----- | ---- | ---- |
| macronutrients | object | 宏量营养素推荐摄入量 |
| vitamins | object | 维生素推荐摄入量 |
| minerals | object | 矿物质推荐摄入量 |
| water | object | 水分推荐摄入量 |
| other_dietary | array | 其他膳食成分推荐摄入量 |

### 宏量营养素格式 (macronutrients)

```json
{
  "protein": {
    "ear": 60,
    "rni": 65,
    "amdr": 0.20,
    "unit": "g"
  },
  "fat": {
    "amdr": 0.30,
    "unit": "%E"
  },
  "saturated_fat": {
    "amdr": 0.10,
    "unit": "%E"
  },
  "pufa": {
    "amdr": 0.09,
    "unit": "%E"
  },
  "n3fa": {
    "amdr": 0.02,
    "unit": "%E"
  },
  "la": {
    "ai": 0.04,
    "unit": "%E"
  },
  "ala": {
    "ai": 0.006,
    "unit": "%E"
  },
  "epa_dha": {
    "ai": 2.00,
    "unit": "g"
  },
  "carbohydrate": {
    "ear": 120,
    "amdr": 0.65,
    "unit": "g"
  },
  "fiber_dietary": {
    "ai": 30,
    "unit": "g"
  },
  "fructose": {
    "amdr": 0.10,
    "unit": "%E"
  }
}
```

### 维生素格式 (vitamins)

```json
{
  "vitamin_a": {
    "rni": 660,
    "unit": "μg"
  },
  "vitamin_d": {
    "rni": 10,
    "unit": "μg"
  },
  "vitamin_e": {
    "rni": 14,
    "unit": "mg"
  },
  "thiamine": {
    "rni": 1.4,
    "unit": "mg"
  },
  "lactoflavin": {
    "rni": 1.2,
    "unit": "mg"
  },
  "vitamin_b6": {
    "rni": 15,
    "unit": "mg"
  },
  "vitamin_b12": {
    "rni": 1.4,
    "unit": "μg"
  },
  "vitamin_c": {
    "rni": 400,
    "unit": "mg"
  },
  "niacin": {
    "rni": 2.4,
    "unit": "mg"
  },
  "folacin": {
    "rni": 5.0,
    "unit": "μg DFE"
  },
  "pantothenic": {
    "rni": 40,
    "unit": "mg"
  },
  "biotin": {
    "rni": 450,
    "unit": "μg"
  },
  "choline": {
    "rni": 100,
    "unit": "mg"
  }
}
```

### 矿物质格式 (minerals)

```json
{
  "calcium": {
    "rni": 800,
    "unit": "mg"
  },
  "phosphor": {
    "rni": 720,
    "unit": "mg"
  },
  "kalium": {
    "rni": 2000,
    "unit": "mg"
  },
  "natrium": {
    "rni": 1500,
    "unit": "mg"
  },
  "magnesium": {
    "rni": 330,
    "unit": "mg"
  },
  "chlorine": {
    "rni": 2300,
    "unit": "mg"
  },
  "iron": {
    "rni": 12.0,
    "unit": "mg"
  },
  "iodine": {
    "rni": 120,
    "unit": "μg"
  },
  "zinc": {
    "rni": 12.0,
    "unit": "mg"
  },
  "selenium": {
    "rni": 60,
    "unit": "μg"
  },
  "copper": {
    "rni": 0.8,
    "unit": "mg"
  },
  "fluorine": {
    "rni": 1.50,
    "unit": "mg"
  },
  "chromium": {
    "rni": 35.0,
    "unit": "μg"
  },
  "cobalt": {
    "rni": 25,
    "unit": "μg"
  },
  "manganese": {
    "rni": 4.5,
    "unit": "mg"
  }
}
```

### 水分格式 (water)

```json
{
  "drinking_ml": 1500,
  "total_water_ml": 2700,

}
```

### 其他膳食成分格式 (other_dietary)

当请求的dietary_names包含其他膳食成分的字段时，返回对应的名称与推荐值：

```json
[
  {
    "name_cn": "大豆异黄酮",
    "spl": 55,
    "spl_unit": "mg",
    "ul": 120,
    "ul_unit": "mg"
  },
  {
    "name_cn": "番茄红素",
    "spl": 15,
    "spl_unit": "mg",
    "ul": 70,
    "ul_unit": "mg"
  }
]
```

## 完整响应示例

```json
{
  "macronutrients": {
    "protein": {
      "ear": 50,
      "rni": 70,
      "amdr": 0.20,
      "unit": "g"
    },
    "fat": {
      "amdr": 0.30,
      "unit": "%E"
    },
    // 其他宏量营养素...
  },
  "vitamins": {
    "vitamin_a": {
      "rni": 660,
      "unit": "μg"
    },
    // 其他维生素...
  },
  "minerals": {
    "calcium": {
      "rni": 800,
      "unit": "mg"
    },
    "phosphor": {
      "rni": 720,
      "unit": "mg"
    },
    // 其他矿物质...
  },
  "water": {
    "drinking_ml": 1700,
    "total_water_ml": 3000
  },
  "other_dietary": [
    {
      "name_cn": "大豆异黄酮",
      "spl": 55,
      "spl_unit": "mg",
      "ul": 120,
      "ul_unit": "mg"
    },
    {
      "name_cn": "番茄红素",
      "spl": 15,
      "spl_unit": "mg",
      "ul": 70,
      "ul_unit": "mg"
    }
  ]
}
```

## 错误处理

### 错误响应格式

```json
{
  "detail": "错误信息"
}
```

### 常见错误码

| 状态码 | 描述 | 可能原因 |
| ----- | ---- | ------- |
| 400 | 请求参数错误 | 性别不是'male'或'female'；孕期阶段不在有效范围；男性指定了孕期阶段 |
| 500 | 服务器内部错误 | 数据库查询失败；计算错误等 |

### 示例错误

```json
{
  "detail": "性别必须为'male'或'female'"
}
```

## 参数说明

### 推荐摄入量类型简介

- **RNI**: 推荐营养素摄入量（Recommended Nutrient Intake）
- **EAR**: 平均需要量（Estimated Average Requirement）
- **AI**: 适宜摄入量（Adequate Intake）
- **AMDR**: 可接受宏量营养素分布范围（Acceptable Macronutrient Distribution Range）
- **SPL**: 推荐最大摄入量
- **UL**: 可耐受最高摄入量（Upper Level）

### 单位说明

- **%E**: 占总能量摄入百分比
- **g/d**: 克/天
- **mg**: 毫克
- **μg**: 微克
- **ml/d**: 毫升/天
- **μgRAE**: 视黄醇当量，微克
- **μg DFE**: 膳食叶酸当量，微克
- **mg α‑TE**: α-生育酚当量，毫克 