
# 饮食分析系统集成实现方案

基于前述的数据库集成方案设计，并结合当前项目结构，以下是详细的实现方案，包括数据库构建、接口设计和数据导入策略。

## 一、数据库构建方案

### 1. 数据库模型定义

在`app/models`目录下创建食品相关模型文件：

```python
# app/models/food.py
from sqlalchemy import Column, Integer, String, Boolean, Float, ForeignKey, DateTime, ARRAY, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base_class import Base


class Food(Base):
    """食品基本信息表"""
    __tablename__ = "foods"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(128), nullable=False, index=True)
    code = Column(String(64), unique=True, nullable=False, index=True)
    category = Column(String(64), index=True)
    food_type = Column(String(32), index=True)
    goods_id = Column(Integer, nullable=True)
    thumb_image_url = Column(String)
    large_image_url = Column(String)
    is_liquid = Column(Boolean, default=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    can_revise = Column(Boolean, default=False)

    # 关联关系
    nutritional_profile = relationship("NutritionalProfile", back_populates="food", uselist=False, cascade="all, delete-orphan")
    nutrients = relationship("FoodNutrientValue", back_populates="food", cascade="all, delete-orphan")
    units = relationship("FoodUnit", back_populates="food", cascade="all, delete-orphan")


class NutritionalProfile(Base):
    """食品营养概况表"""
    __tablename__ = "nutritional_profiles"

    id = Column(Integer, primary_key=True, index=True)
    food_id = Column(Integer, ForeignKey("foods.id", ondelete="CASCADE"), unique=True)
    health_light = Column(Integer, nullable=True)
    lights = Column(JSON, nullable=True)
    warnings = Column(JSON, nullable=True)
    warning_scenes = Column(JSON, nullable=True)
    calory = Column(Float, nullable=True)
    protein_fraction = Column(Float, nullable=True)
    fat_fraction = Column(Float, nullable=True)
    carb_fraction = Column(Float, nullable=True)
    food_rank = Column(Integer, nullable=True)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关联关系
    food = relationship("Food", back_populates="nutritional_profile")


class FoodNutrientValue(Base):
    """食品营养素明细表"""
    __tablename__ = "food_nutrient_values"

    id = Column(Integer, primary_key=True, index=True)
    food_id = Column(Integer, ForeignKey("foods.id", ondelete="CASCADE"), index=True)
    name_en = Column(String(64), nullable=False)
    name_cn = Column(String(64), nullable=False)
    value = Column(Float, nullable=True)
    unit = Column(String(16), nullable=True)
    unit_name = Column(String(16), nullable=True)
    precision = Column(Integer, nullable=True)
    nrv = Column(Float, nullable=True)
    category = Column(String(16), nullable=False, index=True)

    # 关联关系
    food = relationship("Food", back_populates="nutrients")

    __table_args__ = (
        # 复合唯一约束
        {'sqlite_autoincrement': True},  # 使用SQLite时适用
    )


class FoodUnit(Base):
    """食品计量单位表"""
    __tablename__ = "food_units"

    id = Column(Integer, primary_key=True, index=True)
    food_id = Column(Integer, ForeignKey("foods.id", ondelete="CASCADE"), index=True)
    unit_name = Column(String(32), nullable=False)
    weight = Column(Float, nullable=False)
    eat_weight = Column(Float, nullable=False)
    is_default = Column(Boolean, default=False)

    # 关联关系
    food = relationship("Food", back_populates="units")

    __table_args__ = (
        # 复合唯一约束
        {'sqlite_autoincrement': True},
    )
```

```python
# app/models/meal.py
from sqlalchemy import Column, Integer, String, Boolean, Float, ForeignKey, DateTime, Date, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from app.db.base_class import Base


class MealRecord(Base):
    """餐食记录表"""
    __tablename__ = "meal_records"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(String(100), ForeignKey("users.id"), index=True)
    date = Column(Date, nullable=False, index=True)
    meal_type = Column(String(20), nullable=False)
    image_url = Column(String, nullable=True)
    file_id = Column(String(100), nullable=True)
    thumb_data = Column(String, nullable=True)
    total_calory = Column(Float, default=0)
    total_protein = Column(Float, default=0)
    total_fat = Column(Float, default=0)
    total_carbohydrate = Column(Float, default=0)
    is_ai_recognized = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关联关系
    user = relationship("User", back_populates="meal_records")
    food_items = relationship("FoodItem", back_populates="meal_record", cascade="all, delete-orphan")
    health_recommendations = relationship("HealthRecommendation", back_populates="meal_record", cascade="all, delete-orphan")


class FoodItem(Base):
    """食物项表"""
    __tablename__ = "food_items"

    id = Column(Integer, primary_key=True, index=True)
    meal_record_id = Column(Integer, ForeignKey("meal_records.id", ondelete="CASCADE"), index=True)
    food_id = Column(Integer, ForeignKey("foods.id"), nullable=True)
    name = Column(String(100), nullable=False)
    quantity = Column(Float, default=1.0)
    unit_name = Column(String(20), default="份")
    weight = Column(Float, nullable=False)
    category = Column(String(50), nullable=True)
    cuisine_type = Column(String(50), nullable=True)
    cuisine_type_detail = Column(String(100), nullable=True)
    image_url = Column(String, nullable=True)
    
    # 营养相关字段
    health_light = Column(Integer, nullable=True)
    lights = Column(JSON, nullable=True)
    warnings = Column(JSON, nullable=True)
    warning_scenes = Column(JSON, nullable=True)
    calory = Column(Float, nullable=True)
    protein = Column(Float, nullable=True)
    fat = Column(Float, nullable=True)
    carbohydrate = Column(Float, nullable=True)
    protein_fraction = Column(Float, nullable=True)
    fat_fraction = Column(Float, nullable=True)
    carb_fraction = Column(Float, nullable=True)
    
    is_custom = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关联关系
    meal_record = relationship("MealRecord", back_populates="food_items")
    food = relationship("Food", backref="food_items")
    nutrient_intakes = relationship("FoodItemNutrientIntake", back_populates="food_item", cascade="all, delete-orphan")


class FoodItemNutrientIntake(Base):
    """食物营养素摄入详情表"""
    __tablename__ = "food_item_nutrient_intakes"

    id = Column(Integer, primary_key=True, index=True)
    food_item_id = Column(Integer, ForeignKey("food_items.id", ondelete="CASCADE"), index=True)
    name_en = Column(String(64), nullable=False)
    name_cn = Column(String(64), nullable=False)
    value = Column(Float, nullable=True)
    unit = Column(String(16), nullable=True)
    unit_name = Column(String(16), nullable=True)
    nrv_percentage = Column(Float, nullable=True)
    category = Column(String(16), nullable=False, index=True)

    # 关联关系
    food_item = relationship("FoodItem", back_populates="nutrient_intakes")


class HealthRecommendation(Base):
    """健康建议表"""
    __tablename__ = "health_recommendations"

    id = Column(Integer, primary_key=True, index=True)
    meal_record_id = Column(Integer, ForeignKey("meal_records.id", ondelete="CASCADE"), index=True)
    recommendation_text = Column(String, nullable=False)
    recommendation_type = Column(String(32), nullable=True)
    priority = Column(Integer, default=0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # 关联关系
    meal_record = relationship("MealRecord", back_populates="health_recommendations")
```

### 2. 数据库迁移文件

使用Alembic创建迁移脚本：

```bash
alembic revision --autogenerate -m "Add food and nutrition tables"
alembic upgrade head
```

### 3. 数据验证模式

在`app/schemas`目录下创建相应的Pydantic模型：

```python
# app/schemas/food.py
from datetime import datetime
from typing import List, Optional, Dict, Any, Union

from pydantic import BaseModel, Field


class FoodUnitBase(BaseModel):
    unit_name: str
    weight: float
    eat_weight: float
    is_default: bool = False


class FoodUnitCreate(FoodUnitBase):
    pass


class FoodUnit(FoodUnitBase):
    id: int
    food_id: int

    class Config:
        orm_mode = True


class FoodNutrientValueBase(BaseModel):
    name_en: str
    name_cn: str
    value: Optional[float] = None
    unit: Optional[str] = None
    unit_name: Optional[str] = None
    precision: Optional[int] = None
    nrv: Optional[float] = None
    category: str


class FoodNutrientValueCreate(FoodNutrientValueBase):
    pass


class FoodNutrientValue(FoodNutrientValueBase):
    id: int
    food_id: int

    class Config:
        orm_mode = True


class NutritionalProfileBase(BaseModel):
    health_light: Optional[int] = None
    lights: Optional[List[str]] = []
    warnings: Optional[List[str]] = []
    warning_scenes: Optional[List[str]] = []
    calory: Optional[float] = None
    protein_fraction: Optional[float] = None
    fat_fraction: Optional[float] = None
    carb_fraction: Optional[float] = None
    food_rank: Optional[int] = None


class NutritionalProfileCreate(NutritionalProfileBase):
    pass


class NutritionalProfile(NutritionalProfileBase):
    id: int
    food_id: int
    updated_at: datetime

    class Config:
        orm_mode = True


class FoodBase(BaseModel):
    name: str
    code: str
    category: Optional[str] = None
    food_type: Optional[str] = None
    goods_id: Optional[int] = None
    thumb_image_url: Optional[str] = None
    large_image_url: Optional[str] = None
    is_liquid: bool = False
    can_revise: bool = False


class FoodCreate(FoodBase):
    nutritional_profile: Optional[NutritionalProfileCreate] = None
    nutrients: Optional[List[FoodNutrientValueCreate]] = []
    units: Optional[List[FoodUnitCreate]] = []


class Food(FoodBase):
    id: int
    updated_at: datetime
    nutritional_profile: Optional[NutritionalProfile] = None
    nutrients: List[FoodNutrientValue] = []
    units: List[FoodUnit] = []

    class Config:
        orm_mode = True


class FoodSearch(BaseModel):
    id: int
    name: str
    code: str
    category: Optional[str] = None
    food_type: Optional[str] = None
    thumb_image_url: Optional[str] = None
    calory: Optional[float] = None
    
    class Config:
        orm_mode = True
```

```python
# app/schemas/meal.py
from datetime import datetime, date
from typing import List, Optional, Dict, Any, Union
from enum import Enum

from pydantic import BaseModel, Field


class MealType(str, Enum):
    BREAKFAST = "breakfast"
    LUNCH = "lunch"
    DINNER = "dinner"
    SNACK = "snack"


class FoodItemNutrientIntakeBase(BaseModel):
    name_en: str
    name_cn: str
    value: Optional[float] = None
    unit: Optional[str] = None
    unit_name: Optional[str] = None
    nrv_percentage: Optional[float] = None
    category: str


class FoodItemNutrientIntakeCreate(FoodItemNutrientIntakeBase):
    pass


class FoodItemNutrientIntake(FoodItemNutrientIntakeBase):
    id: int
    food_item_id: int

    class Config:
        orm_mode = True


class FoodItemBase(BaseModel):
    name: str
    quantity: float = 1.0
    unit_name: str = "份"
    weight: float
    category: Optional[str] = None
    cuisine_type: Optional[str] = None
    cuisine_type_detail: Optional[str] = None
    image_url: Optional[str] = None


class FoodItemCreate(FoodItemBase):
    food_id: Optional[int] = None
    health_light: Optional[int] = None
    lights: Optional[List[str]] = []
    warnings: Optional[List[str]] = []
    warning_scenes: Optional[List[str]] = []
    calory: Optional[float] = None
    protein: Optional[float] = None
    fat: Optional[float] = None
    carbohydrate: Optional[float] = None
    protein_fraction: Optional[float] = None
    fat_fraction: Optional[float] = None
    carb_fraction: Optional[float] = None
    is_custom: bool = False
    nutrient_intakes: Optional[List[FoodItemNutrientIntakeCreate]] = []


class FoodItem(FoodItemBase):
    id: int
    meal_record_id: int
    food_id: Optional[int] = None
    health_light: Optional[int] = None
    lights: List[str] = []
    warnings: List[str] = []
    warning_scenes: List[str] = []
    calory: Optional[float] = None
    protein: Optional[float] = None
    fat: Optional[float] = None
    carbohydrate: Optional[float] = None
    protein_fraction: Optional[float] = None
    fat_fraction: Optional[float] = None
    carb_fraction: Optional[float] = None
    is_custom: bool
    created_at: datetime
    updated_at: datetime
    nutrient_intakes: List[FoodItemNutrientIntake] = []

    class Config:
        orm_mode = True


class HealthRecommendationBase(BaseModel):
    recommendation_text: str
    recommendation_type: Optional[str] = None
    priority: int = 0


class HealthRecommendationCreate(HealthRecommendationBase):
    pass


class HealthRecommendation(HealthRecommendationBase):
    id: int
    meal_record_id: int
    created_at: datetime

    class Config:
        orm_mode = True


class MealRecordBase(BaseModel):
    date: date
    meal_type: MealType
    image_url: Optional[str] = None
    file_id: Optional[str] = None
    thumb_data: Optional[str] = None
    is_ai_recognized: bool = False


class MealRecordCreate(MealRecordBase):
    pass


class MealRecordUpdate(BaseModel):
    image_url: Optional[str] = None
    file_id: Optional[str] = None
    thumb_data: Optional[str] = None
    is_ai_recognized: Optional[bool] = None


class MealRecord(MealRecordBase):
    id: int
    user_id: str
    total_calory: float = 0
    total_protein: float = 0
    total_fat: float = 0
    total_carbohydrate: float = 0
    created_at: datetime
    updated_at: datetime
    food_items: List[FoodItem] = []
    health_recommendations: List[HealthRecommendation] = []

    class Config:
        orm_mode = True


class MealRecordSummary(BaseModel):
    id: int
    date: date
    meal_type: MealType
    image_url: Optional[str] = None
    total_calory: float
    total_protein: float
    total_fat: float
    total_carbohydrate: float

    class Config:
        orm_mode = True


class DailyNutritionSummary(BaseModel):
    date: date
    total_calory: float = 0
    total_protein: float = 0
    total_fat: float = 0
    total_carbohydrate: float = 0
    meals: List[MealRecordSummary] = []
```

## 二、接口设计方案

### 1. 基础食品库管理接口

```python
# app/api/endpoints/food.py
from typing import Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from app import crud, models, schemas
from app.api import deps

router = APIRouter()


@router.get("/", response_model=List[schemas.FoodSearch])
def read_foods(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    name: Optional[str] = None,
    category: Optional[str] = None,
    food_type: Optional[str] = None,
) -> Any:
    """
    获取食品列表，支持名称搜索。
    """
    return crud.food.get_multi(
        db, skip=skip, limit=limit, name=name, category=category, food_type=food_type
    )


@router.get("/categories", response_model=List[str])
def read_food_categories(
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    获取所有食品类别列表。
    """
    return crud.food.get_all_categories(db)


@router.get("/types", response_model=List[str])
def read_food_types(
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    获取所有食品类型列表。
    """
    return crud.food.get_all_types(db)


@router.get("/{food_id}", response_model=schemas.Food)
def read_food(
    *,
    db: Session = Depends(deps.get_db),
    food_id: int,
) -> Any:
    """
    通过ID获取食品详情。
    """
    food = crud.food.get(db=db, id=food_id)
    if not food:
        raise HTTPException(status_code=404, detail="食品不存在")
    return food


@router.get("/code/{code}", response_model=schemas.Food)
def read_food_by_code(
    *,
    db: Session = Depends(deps.get_db),
    code: str,
) -> Any:
    """
    通过编码获取食品详情。
    """
    food = crud.food.get_by_code(db=db, code=code)
    if not food:
        raise HTTPException(status_code=404, detail="食品不存在")
    return food


@router.post("/", response_model=schemas.Food)
def create_food(
    *,
    db: Session = Depends(deps.get_db),
    food_in: schemas.FoodCreate,
    # 以下依赖是管理员权限检查
    current_user: models.User = Depends(deps.get_current_active_superuser),
) -> Any:
    """
    创建新食品（仅管理员）。
    """
    # 检查食品编码是否已存在
    food = crud.food.get_by_code(db=db, code=food_in.code)
    if food:
        raise HTTPException(
            status_code=400,
            detail=f"编码为 {food_in.code} 的食品已存在",
        )
    return crud.food.create(db=db, obj_in=food_in)


@router.put("/{food_id}", response_model=schemas.Food)
def update_food(
    *,
    db: Session = Depends(deps.get_db),
    food_id: int,
    food_in: schemas.FoodCreate,
    current_user: models.User = Depends(deps.get_current_active_superuser),
) -> Any:
    """
    更新食品信息（仅管理员）。
    """
    food = crud.food.get(db=db, id=food_id)
    if not food:
        raise HTTPException(status_code=404, detail="食品不存在")
    
    # 检查是否可以修改
    if not food.can_revise:
        raise HTTPException(status_code=403, detail="该食品不允许修改")
        
    return crud.food.update(db=db, db_obj=food, obj_in=food_in)


@router.delete("/{food_id}", response_model=schemas.Food)
def delete_food(
    *,
    db: Session = Depends(deps.get_db),
    food_id: int,
    current_user: models.User = Depends(deps.get_current_active_superuser),
) -> Any:
    """
    删除食品（仅管理员）。
    """
    food = crud.food.get(db=db, id=food_id)
    if not food:
        raise HTTPException(status_code=404, detail="食品不存在")
    return crud.food.remove(db=db, id=food_id)
```

### 2. 用户餐食记录接口

```python
# app/api/endpoints/meals.py
from typing import Any, List, Optional
from datetime import date, timedelta

from fastapi import APIRouter, Depends, HTTPException, Query, UploadFile, File, Form
from sqlalchemy.orm import Session

from app import crud, models, schemas
from app.api import deps
from app.services import meal_service

router = APIRouter()


@router.get("/", response_model=List[schemas.MealRecordSummary])
def read_user_meals(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """
    获取当前用户的餐食记录列表。
    """
    # 默认为当天
    if not start_date:
        start_date = date.today()
    if not end_date:
        end_date = start_date
        
    return crud.meal.get_user_meals(
        db=db, 
        user_id=current_user.id, 
        start_date=start_date, 
        end_date=end_date,
        skip=skip,
        limit=limit
    )


@router.get("/daily", response_model=schemas.DailyNutritionSummary)
def read_daily_nutrition(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    query_date: date = Query(None)
) -> Any:
    """
    获取用户某一天的营养摄入汇总。
    """
    if not query_date:
        query_date = date.today()
        
    return meal_service.get_daily_nutrition(
        db=db,
        user_id=current_user.id,
        query_date=query_date
    )


@router.get("/{meal_id}", response_model=schemas.MealRecord)
def read_meal(
    *,
    db: Session = Depends(deps.get_db),
    meal_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    获取餐食详情。
    """
    meal = crud.meal.get(db=db, id=meal_id)
    if not meal:
        raise HTTPException(status_code=404, detail="餐食记录不存在")
    if meal.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限查看此餐食记录")
    return meal


@router.post("/", response_model=schemas.MealRecord)
def create_meal(
    *,
    db: Session = Depends(deps.get_db),
    meal_in: schemas.MealRecordCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    创建新的餐食记录。
    """
    return crud.meal.create_with_user_id(
        db=db, 
        obj_in=meal_in, 
        user_id=current_user.id
    )


@router.put("/{meal_id}", response_model=schemas.MealRecord)
def update_meal(
    *,
    db: Session = Depends(deps.get_db),
    meal_id: int,
    meal_in: schemas.MealRecordUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    更新餐食记录。
    """
    meal = crud.meal.get(db=db, id=meal_id)
    if not meal:
        raise HTTPException(status_code=404, detail="餐食记录不存在")
    if meal.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限更新此餐食记录")
    return crud.meal.update(db=db, db_obj=meal, obj_in=meal_in)


@router.delete("/{meal_id}", response_model=schemas.MealRecord)
def delete_meal(
    *,
    db: Session = Depends(deps.get_db),
    meal_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    删除餐食记录。
    """
    meal = crud.meal.get(db=db, id=meal_id)
    if not meal:
        raise HTTPException(status_code=404, detail="餐食记录不存在")
    if meal.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限删除此餐食记录")
    return crud.meal.remove(db=db, id=meal_id)


@router.post("/{meal_id}/food-items", response_model=schemas.FoodItem)
def add_food_item(
    *,
    db: Session = Depends(deps.get_db),
    meal_id: int,
    food_item_in: schemas.FoodItemCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    向餐食记录添加食物项。
    """
    meal = crud.meal.get(db=db, id=meal_id)
    if not meal:
        raise HTTPException(status_code=404, detail="餐食记录不存在")
    if meal.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限修改此餐食记录")
    
    # 创建食物项
    food_item = meal_service.add_food_item_to_meal(
        db=db,
        meal_id=meal_id,
        food_item_in=food_item_in
    )
    
    # 更新餐食营养总和
    meal_service.update_meal_nutrition_totals(db=db, meal_id=meal_id)
    
    return food_item


@router.delete("/food-items/{food_item_id}", response_model=schemas.FoodItem)
def remove_food_item(
    *,
    db: Session = Depends(deps.get_db),
    food_item_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any:
    """
    从餐食记录移除食物项。
    """
    food_item = crud.food_item.get(db=db, id=food_item_id)
    if not food_item:
        raise HTTPException(status_code=404, detail="食物项不存在")
    
    meal = crud.meal.get(db=db, id=food_item.meal_record_id)
    if meal.user_id != current_user.id:
        raise HTTPException(status_code=403, detail="没有权限修改此餐食记录")
    
    food_item = crud.food_item.remove(db=db, id=food_item_id)
    
    # 更新餐食营养总和
    meal_service.update_meal_nutrition_totals(db=db, meal_id=food_item.meal_record_id)
    
    return food_item
```

### 3. 食物识别与分析接口

```python
# app/api/endpoints/food_recognition.py
from typing import Any, List
from datetime import date

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from sqlalchemy.orm import Session

from app import crud, models, schemas
from app.api import deps
from app.services import food_recognition_service

router = APIRouter()


@router.post("/analyze", response_model=schemas.FoodRecognitionResponse)
async def analyze_food_image(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    image: UploadFile = File(...),
    meal_type: schemas.MealType,
    meal_date: date = None,
) -> Any:
    """
    分析食物图片，识别食物项。
    """
    if not meal_date:
        meal_date = date.today()
    
    try:
        # 读取图片内容
        contents = await image.read()
        
        # 调用食物识别服务
        recognition_result = await food_recognition_service.analyze_image(
            db=db,
            user_id=current_user.id,
            image_data=contents,
            meal_type=meal_type,
            meal_date=meal_date
        )
        
        return recognition_result
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"食物识别失败: {str(e)}"
        )


@router.post("/{recognition_id}/confirm", response_model=schemas.MealRecord)
async def confirm_recognition(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    recognition_id: int,
    confirmation: schemas.FoodRecognitionConfirmation,
) -> Any:
    """
    确认或修正食物识别结果。
    """
    try:
        # 获取临时识别记录
        recognition = crud.food_recognition.get(db=db, id=recognition_id)
        if not recognition:
            raise HTTPException(status_code=404, detail="识别记录不存在")
        
        if recognition.user_id != current_user.id:
            raise HTTPException(status_code=403, detail="没有权限确认此识别结果")
        
        # 确认识别结果
        meal_record = await food_recognition_service.confirm_recognition(
            db=db,
            recognition_id=recognition_id,
            confirmation=confirmation
        )
        
        return meal_record
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"确认识别结果失败: {str(e)}"
        )
```

## 三、数据导入实现方案

### 1. 创建数据导入服务

```python
# app/services/food_import_service.py
import json
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional

from sqlalchemy.orm import Session

from app import crud, models, schemas
from app.core.config import settings

logger = logging.getLogger(__name__)


class FoodImportService:
    """食品数据导入服务"""
    
    @staticmethod
    def import_from_json(db: Session, file_path: str) -> Dict[str, Any]:
        """从JSON文件导入食品数据"""
        try:
            # 读取JSON文件
            file_path = Path(file_path)
            if not file_path.exists():
                return {"success": False, "message": f"文件不存在: {file_path}"}
            
            with open(file_path, "r", encoding="utf-8") as f:
                data = json.load(f)
            
            # 解析食品基本信息
            food_in = FoodImportService._parse_food_data(data)
            
            # 检查食品是否已存在
            existing_food = crud.food.get_by_code(db, code=food_in.code)
            if existing_food:
                logger.info(f"食品已存在: {food_in.code}, 进行更新操作")
                food = crud.food.update(db, db_obj=existing_food, obj_in=food_in)
            else:
                logger.info(f"创建新食品: {food_in.code}")
                food = crud.food.create(db, obj_in=food_in)
            
            return {
                "success": True, 
                "food_id": food.id, 
                "message": f"成功导入食品: {food.name}"
            }
            
        except Exception as e:
            logger.error(f"导入食品数据失败: {str(e)}")
            return {"success": False, "message": f"导入失败: {str(e)}"}
    
    @staticmethod
    def import_batch_from_directory(db: Session, dir_path: str) -> Dict[str, Any]:
        """从目录批量导入食品数据"""
        dir_path = Path(dir_path)
        if not dir_path.exists() or not dir_path.is_dir():
            return {"success": False, "message": f"目录不存在: {dir_path}"}
        
        results = {
            "total": 0,
            "success": 0,
            "failed": 0,
            "failures": []
        }
        
        # 遍历目录中的所有JSON文件
        for json_file in dir_path.glob("*.json"):
            try:
                result = FoodImportService.import_from_json(db, str(json_file))
                results["total"] += 1
                
                if result["success"]:
                    results["success"] += 1
                else:
                    results["failed"] += 1
                    results["failures"].append({
                        "file": json_file.name,
                        "message": result["message"]
                    })

            except Exception as e:
                logger.error(f"导入文件 {json_file.name} 失败: {str(e)}")
                results["failed"] += 1
                results["failures"].append({
                    "file": json_file.name,
                    "message": str(e)
                })
        
        return {
            "success": True,
            "message": f"总计处理: {results['total']}，成功: {results['success']}，失败: {results['failed']}",
            "results": results
        }
    
    @staticmethod
    def _parse_food_data(data: Dict[str, Any]) -> schemas.FoodCreate:
        """解析JSON数据为FoodCreate对象"""
        # 提取基本食品信息
        food_data = data.get("food", {})
        food_base = {
            "name": food_data.get("name", ""),
            "code": food_data.get("code", ""),
            "category": food_data.get("category"),
            "food_type": food_data.get("food_type"),
            "goods_id": food_data.get("goods_id"),
            "thumb_image_url": food_data.get("thumb_image_url"),
            "large_image_url": food_data.get("large_image_url"),
            "is_liquid": food_data.get("is_liquid", False),
            "can_revise": food_data.get("can_revise", False)
        }
        
        # 提取营养概况
        nutrition_profile = data.get("nutritional_profile", {})
        red_green_section = nutrition_profile.get("red_green_section", {})
        nutrients_section = nutrition_profile.get("nutrients_section", {})
        
        profile_data = {
            "health_light": red_green_section.get("health_light"),
            "lights": red_green_section.get("lights", []),
            "warnings": red_green_section.get("warnings", []),
            "warning_scenes": red_green_section.get("warning_scenes", []),
            "calory": nutrients_section.get("calory"),
            "food_rank": nutrition_profile.get("other_section", {}).get("food_rank")
        }
        
        # 提取能量分配
        energy_fractions = nutrients_section.get("energy_fractions", {})
        if energy_fractions:
            profile_data.update({
                "protein_fraction": energy_fractions.get("protein"),
                "fat_fraction": energy_fractions.get("fat"),
                "carb_fraction": energy_fractions.get("carbohydrate")
            })
        
        # 提取计量单位
        units_data = []
        for unit in nutrients_section.get("units", []):
            units_data.append({
                "unit_name": unit.get("unit_name", "克"),
                "weight": float(unit.get("weight", 100)),
                "eat_weight": float(unit.get("eat_weight", 100)),
                "is_default": unit.get("id", 0) == 0  # 假设id=0的为默认单位
            })
        
        # 提取详细营养素数据
        nutrients_data = []
        main_values = nutrients_section.get("values", {}).get("main", [])
        vitamin_values = nutrients_section.get("values", {}).get("vitamin", [])
        minerals_values = nutrients_section.get("values", {}).get("minerals", [])
        other_values = nutrients_section.get("values", {}).get("other", [])
        
        # 处理主要营养素
        for nutrient in main_values:
            nutrients_data.append({
                "name_en": nutrient.get("name_en", ""),
                "name_cn": nutrient.get("name", ""),
                "value": nutrient.get("value"),
                "unit": nutrient.get("unit", ""),
                "unit_name": nutrient.get("unit_name", ""),
                "precision": nutrient.get("precision"),
                "nrv": nutrient.get("nrv"),
                "category": "main"
            })
        
        # 处理维生素
        for nutrient in vitamin_values:
            nutrients_data.append({
                "name_en": nutrient.get("name_en", ""),
                "name_cn": nutrient.get("name", ""),
                "value": nutrient.get("value"),
                "unit": nutrient.get("unit", ""),
                "unit_name": nutrient.get("unit_name", ""),
                "precision": nutrient.get("precision"),
                "nrv": nutrient.get("nrv"),
                "category": "vitamin"
            })
        
        # 处理矿物质
        for nutrient in minerals_values:
            nutrients_data.append({
                "name_en": nutrient.get("name_en", ""),
                "name_cn": nutrient.get("name", ""),
                "value": nutrient.get("value"),
                "unit": nutrient.get("unit", ""),
                "unit_name": nutrient.get("unit_name", ""),
                "precision": nutrient.get("precision"),
                "nrv": nutrient.get("nrv"),
                "category": "minerals"
            })
        
        # 处理其他营养素
        for nutrient in other_values:
            nutrients_data.append({
                "name_en": nutrient.get("name_en", ""),
                "name_cn": nutrient.get("name", ""),
                "value": nutrient.get("value"),
                "unit": nutrient.get("unit", ""),
                "unit_name": nutrient.get("unit_name", ""),
                "precision": nutrient.get("precision"),
                "nrv": nutrient.get("nrv"),
                "category": "other"
            })
        
        # 组装最终的FoodCreate对象
        return schemas.FoodCreate(
            **food_base,
            nutritional_profile=schemas.NutritionalProfileCreate(**profile_data),
            nutrients=[schemas.FoodNutrientValueCreate(**n) for n in nutrients_data if n.get("name_en")],
            units=[schemas.FoodUnitCreate(**u) for u in units_data]
        )
```

### 2. 食物项添加逻辑服务

```python
# app/services/meal_service.py
from typing import Dict, Any, List, Optional
from datetime import date

from sqlalchemy.orm import Session

from app import crud, models, schemas
from app.core.config import settings


class MealService:
    """餐食相关业务逻辑服务"""
    
    @staticmethod
    def add_food_item_to_meal(
        db: Session, 
        meal_id: int, 
        food_item_in: schemas.FoodItemCreate
    ) -> models.FoodItem:
        """添加食物项到餐食记录，同时处理营养素计算"""
        
        # 创建基础食物项
        db_food_item = models.FoodItem(
            meal_record_id=meal_id,
            name=food_item_in.name,
            quantity=food_item_in.quantity,
            unit_name=food_item_in.unit_name,
            weight=food_item_in.weight,
            category=food_item_in.category,
            cuisine_type=food_item_in.cuisine_type,
            cuisine_type_detail=food_item_in.cuisine_type_detail,
            image_url=food_item_in.image_url,
            is_custom=food_item_in.is_custom
        )
        
        # 如果有关联到食品库
        if food_item_in.food_id:
            # 获取食品库中的食物数据
            food = crud.food.get(db, id=food_item_in.food_id)
            if food:
                db_food_item.food_id = food.id
                
                # 获取食物的营养概况
                if food.nutritional_profile:
                    profile = food.nutritional_profile
                    
                    # 复制健康信号灯数据
                    db_food_item.health_light = profile.health_light
                    db_food_item.lights = profile.lights
                    db_food_item.warnings = profile.warnings
                    db_food_item.warning_scenes = profile.warning_scenes
                    
                    # 能量分配比例
                    db_food_item.protein_fraction = profile.protein_fraction
                    db_food_item.fat_fraction = profile.fat_fraction
                    db_food_item.carb_fraction = profile.carb_fraction
                    
                    # 获取指定单位的重量转换
                    unit = None
                    for u in food.units:
                        if u.unit_name == food_item_in.unit_name:
                            unit = u
                            break
                    
                    # 计算营养素摄入量
                    base_weight = 100  # 默认基准重量(100克)
                    if unit:
                        base_weight = unit.weight
                    
                    # 计算转换因子
                    conversion_factor = food_item_in.weight / base_weight * food_item_in.quantity
                    
                    # 计算卡路里和宏量营养素
                    if profile.calory is not None:
                        db_food_item.calory = profile.calory * conversion_factor
                    
                    # 计算蛋白质、脂肪、碳水的克数
                    # 假设calory有值，否则无法计算
                    if profile.calory is not None:
                        if profile.protein_fraction is not None:
                            # 蛋白质1克 = 4千卡
                            db_food_item.protein = (profile.calory * profile.protein_fraction / 100 / 4) * conversion_factor
                        
                        if profile.fat_fraction is not None:
                            # 脂肪1克 = 9千卡
                            db_food_item.fat = (profile.calory * profile.fat_fraction / 100 / 9) * conversion_factor
                        
                        if profile.carb_fraction is not None:
                            # 碳水1克 = 4千卡
                            db_food_item.carbohydrate = (profile.calory * profile.carb_fraction / 100 / 4) * conversion_factor
                    
                    # 处理详细营养素摄入
                    for nutrient in food.nutrients:
                        if nutrient.value is not None:
                            # 创建营养素摄入记录
                            nutrient_intake = models.FoodItemNutrientIntake(
                                name_en=nutrient.name_en,
                                name_cn=nutrient.name_cn,
                                value=nutrient.value * conversion_factor,
                                unit=nutrient.unit,
                                unit_name=nutrient.unit_name,
                                nrv_percentage=nutrient.nrv * conversion_factor if nutrient.nrv else None,
                                category=nutrient.category
                            )
                            db_food_item.nutrient_intakes.append(nutrient_intake)
        else:
            # 自定义食物，直接使用用户提供的营养数据
            db_food_item.health_light = food_item_in.health_light
            db_food_item.lights = food_item_in.lights
            db_food_item.warnings = food_item_in.warnings
            db_food_item.warning_scenes = food_item_in.warning_scenes
            db_food_item.calory = food_item_in.calory
            db_food_item.protein = food_item_in.protein
            db_food_item.fat = food_item_in.fat
            db_food_item.carbohydrate = food_item_in.carbohydrate
            db_food_item.protein_fraction = food_item_in.protein_fraction
            db_food_item.fat_fraction = food_item_in.fat_fraction
            db_food_item.carb_fraction = food_item_in.carb_fraction
            
            # 处理自定义营养素数据
            if food_item_in.nutrient_intakes:
                for intake_in in food_item_in.nutrient_intakes:
                    nutrient_intake = models.FoodItemNutrientIntake(**intake_in.dict())
                    db_food_item.nutrient_intakes.append(nutrient_intake)
        
        # 保存食物项
        db.add(db_food_item)
        db.commit()
        db.refresh(db_food_item)
        
        return db_food_item
    
    @staticmethod
    def update_meal_nutrition_totals(db: Session, meal_id: int) -> models.MealRecord:
        """更新餐食记录的总营养值"""
        meal = crud.meal.get(db=db, id=meal_id)
        if not meal:
            return None
        
        # 重新计算总营养值
        total_calory = 0
        total_protein = 0
        total_fat = 0
        total_carbohydrate = 0
        
        for food_item in meal.food_items:
            if food_item.calory:
                total_calory += food_item.calory
            if food_item.protein:
                total_protein += food_item.protein
            if food_item.fat:
                total_fat += food_item.fat
            if food_item.carbohydrate:
                total_carbohydrate += food_item.carbohydrate
        
        # 更新餐食记录
        meal.total_calory = total_calory
        meal.total_protein = total_protein
        meal.total_fat = total_fat
        meal.total_carbohydrate = total_carbohydrate
        
        db.add(meal)
        db.commit()
        db.refresh(meal)
        
        return meal
    
    @staticmethod
    def get_daily_nutrition(db: Session, user_id: str, query_date: date) -> Dict[str, Any]:
        """获取用户某天的营养摄入汇总"""
        # 获取该日期的所有餐食记录
        meals = crud.meal.get_user_meals_by_date(
            db=db, 
            user_id=user_id, 
            date=query_date
        )
        
        # 计算总营养值
        total_calory = 0
        total_protein = 0
        total_fat = 0
        total_carbohydrate = 0
        
        meal_summaries = []
        
        for meal in meals:
            # 将每个餐食添加到列表
            meal_summary = schemas.MealRecordSummary(
                id=meal.id,
                date=meal.date,
                meal_type=meal.meal_type,
                image_url=meal.image_url,
                total_calory=meal.total_calory,
                total_protein=meal.total_protein,
                total_fat=meal.total_fat,
                total_carbohydrate=meal.total_carbohydrate
            )
            meal_summaries.append(meal_summary)
            
            # 累加总营养值
            total_calory += meal.total_calory
            total_protein += meal.total_protein
            total_fat += meal.total_fat
            total_carbohydrate += meal.total_carbohydrate
        
        # 构建日营养摄入汇总
        return schemas.DailyNutritionSummary(
            date=query_date,
            total_calory=total_calory,
            total_protein=total_protein,
            total_fat=total_fat,
            total_carbohydrate=total_carbohydrate,
            meals=meal_summaries
        )
```

### 3. 食物识别服务框架

```python
# app/services/food_recognition_service.py
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import date

from sqlalchemy.orm import Session

from app import crud, models, schemas

logger = logging.getLogger(__name__)


class FoodRecognitionService:
    """食物识别服务"""
    
    @staticmethod
    async def analyze_image(
        db: Session,
        user_id: str,
        image_data: bytes,
        meal_type: schemas.MealType,
        meal_date: date
    ) -> Dict[str, Any]:
        """分析食物图片并识别其中的食物项"""
        try:
            # 上传图片到存储服务
            from app.services.storage import StorageService
            file_path = f"food_recognition/{user_id}/{meal_date.isoformat()}/{meal_type.value}.jpg"
            upload_result = await StorageService.upload_image(image_data, file_path)
            
            # 构建临时识别记录
            recognition = models.FoodRecognition(
                user_id=user_id,
                meal_date=meal_date,
                meal_type=meal_type.value,
                image_url=upload_result.get("url"),
                status="processing"
            )
            db.add(recognition)
            db.commit()
            db.refresh(recognition)
            
            # TODO: 调用实际的食物识别AI服务
            # 此处为示例，实际项目中需替换为真实的AI服务调用
            recognized_items = await FoodRecognitionService._mock_ai_recognition(db, image_data)
            
            # 保存识别结果
            recognition.status = "completed"
            recognition.recognition_result = {
                "food_items": [item.dict() for item in recognized_items]
            }
            db.add(recognition)
            db.commit()
            
            # 生成健康建议
            health_recommendation = "这是一顿营养均衡的餐食，蛋白质含量适中。" # 示例，实际应基于识别结果生成
            
            # 构建响应
            return {
                "success": True,
                "recognition_id": recognition.id,
                "food_items": recognized_items,
                "health_recommendation": health_recommendation,
                "image_url": upload_result.get("url")
            }
            
        except Exception as e:
            logger.error(f"食物识别失败: {str(e)}")
            raise e
    
    @staticmethod
    async def confirm_recognition(
        db: Session,
        recognition_id: int,
        confirmation: schemas.FoodRecognitionConfirmation
    ) -> models.MealRecord:
        """确认或修正识别结果，并创建正式的餐食记录"""
        try:
            # 获取识别记录
            recognition = crud.food_recognition.get(db, id=recognition_id)
            if not recognition:
                raise ValueError("识别记录不存在")
            
            # 创建餐食记录
            meal_record = models.MealRecord(
                user_id=recognition.user_id,
                date=recognition.meal_date,
                meal_type=recognition.meal_type,
                image_url=recognition.image_url,
                is_ai_recognized=True
            )
            db.add(meal_record)
            db.commit()
            db.refresh(meal_record)
            
            # 添加食物项
            from app.services.meal_service import MealService
            for food_item_data in confirmation.food_items:
                # 查找匹配的食品库条目
                food = None
                if food_item_data.food_id:
                    food = crud.food.get(db, id=food_item_data.food_id)
                else:
                    # 尝试通过名称匹配
                    foods = crud.food.get_by_name(db, name=food_item_data.name, limit=1)
                    if foods:
                        food = foods[0]
                        food_item_data.food_id = food.id
                
                # 添加食物项
                MealService.add_food_item_to_meal(
                    db=db,
                    meal_id=meal_record.id,
                    food_item_in=food_item_data
                )
            
            # 更新餐食营养总值
            MealService.update_meal_nutrition_totals(db=db, meal_id=meal_record.id)
            
            # 添加健康建议
            if confirmation.health_recommendation:
                health_rec = models.HealthRecommendation(
                    meal_record_id=meal_record.id,
                    recommendation_text=confirmation.health_recommendation
                )
                db.add(health_rec)
                db.commit()
            
            # 更新识别记录状态
            recognition.status = "confirmed"
            recognition.meal_record_id = meal_record.id
            db.add(recognition)
            db.commit()
            
            # 返回创建的餐食记录
            return crud.meal.get(db, id=meal_record.id)
            
        except Exception as e:
            logger.error(f"确认识别结果失败: {str(e)}")
            raise e
    
    @staticmethod
    async def _mock_ai_recognition(db: Session, image_data: bytes) -> List[schemas.FoodItemCreate]:
        """模拟AI食物识别结果（示例）"""
        # 在实际项目中，应替换为真实AI服务调用
        # 此处只是示例返回一些基本食物
        
        # 随机选择几个食品库中的食物
        foods = crud.food.get_multi(db, limit=3)
        
        result = []
        if foods:
            for food in foods:
                if food.nutritional_profile:
                    profile = food.nutritional_profile
                    
                    # 找到默认单位
                    default_unit = next((u for u in food.units if u.is_default), None)
                    unit_name = "份" if not default_unit else default_unit.unit_name
                    weight = 100 if not default_unit else default_unit.weight
                    
                    # 创建食物项
                    food_item = schemas.FoodItemCreate(
                        food_id=food.id,
                        name=food.name,
                        quantity=1.0,
                        unit_name=unit_name,
                        weight=weight,
                        category=food.category,
                        health_light=profile.health_light,
                        lights=profile.lights,
                        warnings=profile.warnings,
                        warning_scenes=profile.warning_scenes,
                        calory=profile.calory,
                        protein_fraction=profile.protein_fraction,
                        fat_fraction=profile.fat_fraction,
                        carb_fraction=profile.carb_fraction
                    )
                    result.append(food_item)
        
        # 如果没有找到食品库条目，返回一个默认食物
        if not result:
            result.append(schemas.FoodItemCreate(
                name="未识别食物",
                quantity=1.0,
                unit_name="份",
                weight=100,
                calory=200,
                protein=5,
                fat=10,
                carbohydrate=20,
                is_custom=True
            ))
        
        return result
```

### 4. 命令行工具实现

```python
# app/cli.py
import typer
import json
from pathlib import Path
from datetime import datetime
import logging

from app.db.session import SessionLocal
from app.services.food_import_service import FoodImportService

app = typer.Typer()
logger = logging.getLogger(__name__)

@app.command()
def import_food(file_path: str):
    """从JSON文件导入单个食品数据"""
    db = SessionLocal()
    try:
        result = FoodImportService.import_from_json(db, file_path)
        if result["success"]:
            typer.echo(f"成功导入食品: {result['message']}")
        else:
            typer.echo(f"导入失败: {result['message']}", err=True)
    finally:
        db.close()

@app.command()
def import_batch(directory: str):
    """批量导入目录中的所有JSON食品数据"""
    db = SessionLocal()
    try:
        result = FoodImportService.import_batch_from_directory(db, directory)
        if result["success"]:
            typer.echo(f"批量导入结果: {result['message']}")
            
            # 输出失败记录
            if result["results"]["failed"] > 0:
                typer.echo("\n失败记录:")
                for failure in result["results"]["failures"]:
                    typer.echo(f"  - {failure['file']}: {failure['message']}")
        else:
            typer.echo(f"批量导入失败: {result['message']}", err=True)
    finally:
        db.close()

@app.command()
def export_food(food_id: int, output_dir: str = "./exports"):
    """导出单个食品数据到JSON文件"""
    db = SessionLocal()
    try:
        from app import crud, schemas
        
        # 获取食品数据
        food = crud.food.get(db, id=food_id)
        if not food:
            typer.echo(f"食品不存在: ID={food_id}", err=True)
            return
        
        # 转换为Pydantic模型
        food_data = schemas.Food.from_orm(food)
        
        # 确保输出目录存在
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 创建文件名
        file_name = f"{food.code}_{datetime.now().strftime('%Y%m%d')}.json"
        file_path = output_path / file_name
        
        # 写入JSON文件
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(food_data.dict(), f, ensure_ascii=False, indent=2)
        
        typer.echo(f"成功导出食品数据到: {file_path}")
    finally:
        db.close()

if __name__ == "__main__":
    app()
```

## 四、整合实现方案

### 1. 路由配置与API注册

```python
# app/api/v1/api.py - 添加食品和餐食相关路由
from fastapi import APIRouter

from app.api.endpoints import (
    login, users, food, meals, food_recognition
)

api_router = APIRouter()
api_router.include_router(login.router, tags=["登录"])
api_router.include_router(users.router, prefix="/users", tags=["用户"])
api_router.include_router(food.router, prefix="/foods", tags=["食品库"])
api_router.include_router(meals.router, prefix="/meals", tags=["餐食记录"])
api_router.include_router(food_recognition.router, prefix="/food-recognition", tags=["食物识别"])
```

### 2. CRUD实现

```python
# app/crud/food.py
from typing import List, Dict, Any, Optional, Union

from sqlalchemy.orm import Session
from sqlalchemy import or_

from app.crud.base import CRUDBase
from app.models.food import Food, NutritionalProfile, FoodNutrientValue, FoodUnit
from app.schemas.food import FoodCreate, FoodUpdate


class CRUDFood(CRUDBase[Food, FoodCreate, FoodUpdate]):
    def get_by_code(self, db: Session, *, code: str) -> Optional[Food]:
        return db.query(Food).filter(Food.code == code).first()
    
    def get_by_name(self, db: Session, *, name: str, limit: int = 10) -> List[Food]:
        return (
            db.query(Food)
            .filter(Food.name.like(f"%{name}%"))
            .limit(limit)
            .all()
        )
    
    def get_multi(
        self, db: Session, *, skip: int = 0, limit: int = 100, 
        name: Optional[str] = None, category: Optional[str] = None,
        food_type: Optional[str] = None
    ) -> List[Food]:
        query = db.query(Food)
        
        # 应用过滤条件
        if name:
            query = query.filter(Food.name.like(f"%{name}%"))
        if category:
            query = query.filter(Food.category == category)
        if food_type:
            query = query.filter(Food.food_type == food_type)
        
        return query.offset(skip).limit(limit).all()
    
    def get_all_categories(self, db: Session) -> List[str]:
        return [
            cat for cat, in db.query(Food.category)
            .filter(Food.category.isnot(None))
            .distinct()
            .order_by(Food.category)
            .all()
        ]
    
    def get_all_types(self, db: Session) -> List[str]:
        return [
            type_ for type_, in db.query(Food.food_type)
            .filter(Food.food_type.isnot(None))
            .distinct()
            .order_by(Food.food_type)
            .all()
        ]
    
    def create(self, db: Session, *, obj_in: FoodCreate) -> Food:
        # 创建食品基本信息
        db_obj = Food(
            name=obj_in.name,
            code=obj_in.code,
            category=obj_in.category,
            food_type=obj_in.food_type,
            goods_id=obj_in.goods_id,
            thumb_image_url=obj_in.thumb_image_url,
            large_image_url=obj_in.large_image_url,
            is_liquid=obj_in.is_liquid,
            can_revise=obj_in.can_revise
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        
        # 创建营养概况
        if obj_in.nutritional_profile:
            profile = NutritionalProfile(
                food_id=db_obj.id,
                health_light=obj_in.nutritional_profile.health_light,
                lights=obj_in.nutritional_profile.lights,
                warnings=obj_in.nutritional_profile.warnings,
                warning_scenes=obj_in.nutritional_profile.warning_scenes,
                calory=obj_in.nutritional_profile.calory,
                protein_fraction=obj_in.nutritional_profile.protein_fraction,
                fat_fraction=obj_in.nutritional_profile.fat_fraction,
                carb_fraction=obj_in.nutritional_profile.carb_fraction,
                food_rank=obj_in.nutritional_profile.food_rank
            )
            db.add(profile)
        
        # 创建营养素明细
        if obj_in.nutrients:
            for nutrient in obj_in.nutrients:
                db_nutrient = FoodNutrientValue(
                    food_id=db_obj.id,
                    name_en=nutrient.name_en,
                    name_cn=nutrient.name_cn,
                    value=nutrient.value,
                    unit=nutrient.unit,
                    unit_name=nutrient.unit_name,
                    precision=nutrient.precision,
                    nrv=nutrient.nrv,
                    category=nutrient.category
                )
                db.add(db_nutrient)
        
        # 创建计量单位
        if obj_in.units:
            for unit in obj_in.units:
                db_unit = FoodUnit(
                    food_id=db_obj.id,
                    unit_name=unit.unit_name,
                    weight=unit.weight,
                    eat_weight=unit.eat_weight,
                    is_default=unit.is_default
                )
                db.add(db_unit)
        
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def update(
        self, db: Session, *, db_obj: Food, obj_in: Union[FoodUpdate, Dict[str, Any]]
    ) -> Food:
        # 更新基本信息
        db_obj = super().update(db, db_obj=db_obj, obj_in=obj_in)
        
        # 更新营养概况
        if isinstance(obj_in, dict):
            profile_data = obj_in.get("nutritional_profile")
        else:
            profile_data = obj_in.nutritional_profile
        
        if profile_data:
            # 检查是否存在营养概况
            profile = db.query(NutritionalProfile).filter(
                NutritionalProfile.food_id == db_obj.id
            ).first()
            
            if profile:
                # 更新现有记录
                for key, value in profile_data.dict().items():
                    setattr(profile, key, value)
            else:
                # 创建新记录
                profile = NutritionalProfile(food_id=db_obj.id, **profile_data.dict())
                db.add(profile)
        
        # 更新营养素明细 (先删除旧的，再添加新的)
        if isinstance(obj_in, dict):
            nutrients_data = obj_in.get("nutrients", [])
        else:
            nutrients_data = obj_in.nutrients if obj_in.nutrients else []
        
        if nutrients_data:
            # 删除旧记录
            db.query(FoodNutrientValue).filter(
                FoodNutrientValue.food_id == db_obj.id
            ).delete()
            
            # 添加新记录
            for nutrient in nutrients_data:
                nutrient_dict = nutrient.dict() if hasattr(nutrient, "dict") else nutrient
                db_nutrient = FoodNutrientValue(
                    food_id=db_obj.id,
                    **nutrient_dict
                )
                db.add(db_nutrient)
        
        # 更新计量单位 (先删除旧的，再添加新的)
        if isinstance(obj_in, dict):
            units_data = obj_in.get("units", [])
        else:
            units_data = obj_in.units if obj_in.units else []
        
        if units_data:
            # 删除旧记录
            db.query(FoodUnit).filter(
                FoodUnit.food_id == db_obj.id
            ).delete()
            
            # 添加新记录
            for unit in units_data:
                unit_dict = unit.dict() if hasattr(unit, "dict") else unit
                db_unit = FoodUnit(
                    food_id=db_obj.id,
                    **unit_dict
                )
                db.add(db_unit)
        
        db.commit()
        db.refresh(db_obj)
        return db_obj


food = CRUDFood(Food)
```

```python
# app/crud/meal.py
from typing import List, Dict, Any, Optional, Union
from datetime import date, timedelta

from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.meal import MealRecord, FoodItem, FoodItemNutrientIntake, HealthRecommendation
from app.schemas.meal import MealRecordCreate, MealRecordUpdate


class CRUDMeal(CRUDBase[MealRecord, MealRecordCreate, MealRecordUpdate]):
    def create_with_user_id(
        self, db: Session, *, obj_in: MealRecordCreate, user_id: str
    ) -> MealRecord:
        obj_in_data = obj_in.dict()
        db_obj = MealRecord(**obj_in_data, user_id=user_id)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def get_user_meals(
        self, db: Session, *, user_id: str, 
        start_date: date, end_date: date = None,
        skip: int = 0, limit: int = 100
    ) -> List[MealRecord]:
        """获取用户在指定日期范围内的餐食记录"""
        # 如果end_date未指定，默认为start_date
        if not end_date:
            end_date = start_date
            
        return (
            db.query(MealRecord)
            .filter(
                MealRecord.user_id == user_id,
                MealRecord.date >= start_date,
                MealRecord.date <= end_date
            )
            .order_by(MealRecord.date.desc(), MealRecord.meal_type)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_user_meals_by_date(
        self, db: Session, *, user_id: str, date: date
    ) -> List[MealRecord]:
        """获取用户某一天的所有餐食记录"""
        return (
            db.query(MealRecord)
            .filter(
                MealRecord.user_id == user_id,
                MealRecord.date == date
            )
            .order_by(MealRecord.meal_type)
            .all()
        )
    
    def get_recent_meals(
        self, db: Session, *, user_id: str, days: int = 7
    ) -> List[MealRecord]:
        """获取用户最近几天的餐食记录"""
        start_date = date.today() - timedelta(days=days)
        return self.get_user_meals(
            db=db, 
            user_id=user_id, 
            start_date=start_date, 
            end_date=date.today()
        )


meal = CRUDMeal(MealRecord)


class CRUDFoodItem(CRUDBase[FoodItem, Dict[str, Any], Dict[str, Any]]):
    def get_by_meal(
        self, db: Session, *, meal_record_id: int
    ) -> List[FoodItem]:
        """获取餐食记录中的所有食物项"""
        return (
            db.query(FoodItem)
            .filter(FoodItem.meal_record_id == meal_record_id)
            .all()
        )
    
    def create_with_meal(
        self, db: Session, *, obj_in: Dict[str, Any], meal_record_id: int
    ) -> FoodItem:
        """创建餐食记录关联的食物项"""
        obj_in_data = dict(obj_in)
        obj_in_data["meal_record_id"] = meal_record_id
        db_obj = FoodItem(**obj_in_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj


food_item = CRUDFoodItem(FoodItem)


class CRUDFoodItemNutrientIntake(CRUDBase[FoodItemNutrientIntake, Dict[str, Any], Dict[str, Any]]):
    def get_by_food_item(
        self, db: Session, *, food_item_id: int
    ) -> List[FoodItemNutrientIntake]:
        """获取食物项关联的所有营养素摄入记录"""
        return (
            db.query(FoodItemNutrientIntake)
            .filter(FoodItemNutrientIntake.food_item_id == food_item_id)
            .all()
        )
    
    def create_with_food_item(
        self, db: Session, *, obj_in: Dict[str, Any], food_item_id: int
    ) -> FoodItemNutrientIntake:
        """创建食物项关联的营养素摄入记录"""
        obj_in_data = dict(obj_in)
        obj_in_data["food_item_id"] = food_item_id
        db_obj = FoodItemNutrientIntake(**obj_in_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj


food_item_nutrient_intake = CRUDFoodItemNutrientIntake(FoodItemNutrientIntake)


class CRUDHealthRecommendation(CRUDBase[HealthRecommendation, Dict[str, Any], Dict[str, Any]]):
    def get_by_meal_record(
        self, db: Session, *, meal_record_id: int
    ) -> List[HealthRecommendation]:
        """获取餐食记录关联的所有健康建议"""
        return (
            db.query(HealthRecommendation)
            .filter(HealthRecommendation.meal_record_id == meal_record_id)
            .order_by(HealthRecommendation.priority.desc())
            .all()
        )
    
    def create_with_meal_record(
        self, db: Session, *, obj_in: Dict[str, Any], meal_record_id: int
    ) -> HealthRecommendation:
        """创建餐食记录关联的健康建议"""
        obj_in_data = dict(obj_in)
        obj_in_data["meal_record_id"] = meal_record_id
        db_obj = HealthRecommendation(**obj_in_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj


health_recommendation = CRUDHealthRecommendation(HealthRecommendation)
```

### 3. 数据库迁移实现

```python
# alembic/versions/xxxx_add_food_and_nutrition_tables.py
"""add food and nutrition tables

Revision ID: xxxx
Revises: xxxx
Create Date: 2023-xx-xx xx:xx:xx.xxxx

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'xxxx'
down_revision = 'xxxx'
branch_labels = None
depends_on = None


def upgrade():
    # 创建食品基础表
    op.create_table(
        'foods',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=128), nullable=False),
        sa.Column('code', sa.String(length=64), nullable=False),
        sa.Column('category', sa.String(length=64), nullable=True),
        sa.Column('food_type', sa.String(length=32), nullable=True),
        sa.Column('goods_id', sa.Integer(), nullable=True),
        sa.Column('thumb_image_url', sa.String(), nullable=True),
        sa.Column('large_image_url', sa.String(), nullable=True),
        sa.Column('is_liquid', sa.Boolean(), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('can_revise', sa.Boolean(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_foods_category'), 'foods', ['category'], unique=False)
    op.create_index(op.f('ix_foods_code'), 'foods', ['code'], unique=True)
    op.create_index(op.f('ix_foods_food_type'), 'foods', ['food_type'], unique=False)
    op.create_index(op.f('ix_foods_id'), 'foods', ['id'], unique=False)
    op.create_index(op.f('ix_foods_name'), 'foods', ['name'], unique=False)
    
    # 创建营养概况表
    op.create_table(
        'nutritional_profiles',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('food_id', sa.Integer(), nullable=True),
        sa.Column('health_light', sa.Integer(), nullable=True),
        sa.Column('lights', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('warnings', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('warning_scenes', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('calory', sa.Float(), nullable=True),
        sa.Column('protein_fraction', sa.Float(), nullable=True),
        sa.Column('fat_fraction', sa.Float(), nullable=True),
        sa.Column('carb_fraction', sa.Float(), nullable=True),
        sa.Column('food_rank', sa.Integer(), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['food_id'], ['foods.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('food_id')
    )
    op.create_index(op.f('ix_nutritional_profiles_id'), 'nutritional_profiles', ['id'], unique=False)
    
    # 创建食品营养素值表
    op.create_table(
        'food_nutrient_values',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('food_id', sa.Integer(), nullable=True),
        sa.Column('name_en', sa.String(length=64), nullable=False),
        sa.Column('name_cn', sa.String(length=64), nullable=False),
        sa.Column('value', sa.Float(), nullable=True),
        sa.Column('unit', sa.String(length=16), nullable=True),
        sa.Column('unit_name', sa.String(length=16), nullable=True),
        sa.Column('precision', sa.Integer(), nullable=True),
        sa.Column('nrv', sa.Float(), nullable=True),
        sa.Column('category', sa.String(length=16), nullable=False),
        sa.ForeignKeyConstraint(['food_id'], ['foods.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_food_nutrient_values_category'), 'food_nutrient_values', ['category'], unique=False)
    op.create_index(op.f('ix_food_nutrient_values_food_id'), 'food_nutrient_values', ['food_id'], unique=False)
    op.create_index(op.f('ix_food_nutrient_values_id'), 'food_nutrient_values', ['id'], unique=False)
    
    # 创建食品单位表
    op.create_table(
        'food_units',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('food_id', sa.Integer(), nullable=True),
        sa.Column('unit_name', sa.String(length=32), nullable=False),
        sa.Column('weight', sa.Float(), nullable=False),
        sa.Column('eat_weight', sa.Float(), nullable=False),
        sa.Column('is_default', sa.Boolean(), nullable=True),
        sa.ForeignKeyConstraint(['food_id'], ['foods.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_food_units_food_id'), 'food_units', ['food_id'], unique=False)
    op.create_index(op.f('ix_food_units_id'), 'food_units', ['id'], unique=False)
    
    # 创建餐食记录表
    op.create_table(
        'meal_records',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.String(length=100), nullable=True),
        sa.Column('date', sa.Date(), nullable=False),
        sa.Column('meal_type', sa.String(length=20), nullable=False),
        sa.Column('image_url', sa.String(), nullable=True),
        sa.Column('file_id', sa.String(length=100), nullable=True),
        sa.Column('thumb_data', sa.String(), nullable=True),
        sa.Column('total_calory', sa.Float(), nullable=True),
        sa.Column('total_protein', sa.Float(), nullable=True),
        sa.Column('total_fat', sa.Float(), nullable=True),
        sa.Column('total_carbohydrate', sa.Float(), nullable=True),
        sa.Column('is_ai_recognized', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_meal_records_date'), 'meal_records', ['date'], unique=False)
    op.create_index(op.f('ix_meal_records_id'), 'meal_records', ['id'], unique=False)
    op.create_index(op.f('ix_meal_records_user_id'), 'meal_records', ['user_id'], unique=False)
    
    # 创建食物项表
    op.create_table(
        'food_items',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('meal_record_id', sa.Integer(), nullable=True),
        sa.Column('food_id', sa.Integer(), nullable=True),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('quantity', sa.Float(), nullable=True),
        sa.Column('unit_name', sa.String(length=20), nullable=True),
        sa.Column('weight', sa.Float(), nullable=False),
        sa.Column('category', sa.String(length=50), nullable=True),
        sa.Column('cuisine_type', sa.String(length=50), nullable=True),
        sa.Column('cuisine_type_detail', sa.String(length=100), nullable=True),
        sa.Column('image_url', sa.String(), nullable=True),
        sa.Column('health_light', sa.Integer(), nullable=True),
        sa.Column('lights', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('warnings', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('warning_scenes', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('calory', sa.Float(), nullable=True),
        sa.Column('protein', sa.Float(), nullable=True),
        sa.Column('fat', sa.Float(), nullable=True),
        sa.Column('carbohydrate', sa.Float(), nullable=True),
        sa.Column('protein_fraction', sa.Float(), nullable=True),
        sa.Column('fat_fraction', sa.Float(), nullable=True),
        sa.Column('carb_fraction', sa.Float(), nullable=True),
        sa.Column('is_custom', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['food_id'], ['foods.id'], ),
        sa.ForeignKeyConstraint(['meal_record_id'], ['meal_records.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_food_items_id'), 'food_items', ['id'], unique=False)
    op.create_index(op.f('ix_food_items_meal_record_id'), 'food_items', ['meal_record_id'], unique=False)
    
    # 创建健康建议表
    op.create_table(
        'health_recommendations',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('meal_record_id', sa.Integer(), nullable=True),
        sa.Column('recommendation_text', sa.String(), nullable=False),
        sa.Column('recommendation_type', sa.String(length=32), nullable=True),
        sa.Column('priority', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['meal_record_id'], ['meal_records.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_health_recommendations_id'), 'health_recommendations', ['id'], unique=False)
    op.create_index(op.f('ix_health_recommendations_meal_record_id'), 'health_recommendations', ['meal_record_id'], unique=False)
    
    # 创建食物项营养素摄入表
    op.create_table(
        'food_item_nutrient_intakes',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('food_item_id', sa.Integer(), nullable=True),
        sa.Column('name_en', sa.String(length=64), nullable=False),
        sa.Column('name_cn', sa.String(length=64), nullable=False),
        sa.Column('value', sa.Float(), nullable=True),
        sa.Column('unit', sa.String(length=16), nullable=True),
        sa.Column('unit_name', sa.String(length=16), nullable=True),
        sa.Column('nrv_percentage', sa.Float(), nullable=True),
        sa.Column('category', sa.String(length=16), nullable=False),
        sa.ForeignKeyConstraint(['food_item_id'], ['food_items.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_food_item_nutrient_intakes_category'), 'food_item_nutrient_intakes', ['category'], unique=False)
    op.create_index(op.f('ix_food_item_nutrient_intakes_food_item_id'), 'food_item_nutrient_intakes', ['food_item_id'], unique=False)
    op.create_index(op.f('ix_food_item_nutrient_intakes_id'), 'food_item_nutrient_intakes', ['id'], unique=False)


def downgrade():
    # 按照创建的相反顺序删除表
    op.drop_table('food_item_nutrient_intakes')
    op.drop_table('health_recommendations')
    op.drop_table('food_items')
    op.drop_table('meal_records')
    op.drop_table('food_units')
    op.drop_table('food_nutrient_values')
    op.drop_table('nutritional_profiles')
    op.drop_table('foods')
```

### 4. 添加管理后台界面

在项目的 `app/api/admin` 目录下，创建食品管理相关的管理页面：

```python
# app/api/admin/routes/food.py
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Request, Form, UploadFile, File
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from sqlalchemy.orm import Session

from app import crud, models, schemas
from app.api import deps
from app.services.food_import_service import FoodImportService

router = APIRouter()
templates = Jinja2Templates(directory="app/templates")


@router.get("/", response_class=HTMLResponse)
async def food_list(
    request: Request,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_admin),
    name: Optional[str] = None,
    category: Optional[str] = None,
    page: int = 1,
    per_page: int = 20,
):
    """食品库列表页"""
    # 分页查询
    skip = (page - 1) * per_page
    
    # 获取食品总数
    total = crud.food.count(db, name=name, category=category)
    
    # 获取食品列表
    foods = crud.food.get_multi(
        db, 
        skip=skip, 
        limit=per_page,
        name=name,
        category=category
    )
    
    # 获取所有分类
    categories = crud.food.get_all_categories(db)
    
    # 计算总页数
    total_pages = (total + per_page - 1) // per_page
    
    return templates.TemplateResponse(
        "admin/food/list.html",
        {
            "request": request,
            "foods": foods,
            "categories": categories,
            "current_page": page,
            "total_pages": total_pages,
            "total": total,
            "name": name,
            "category": category,
            "current_user": current_user,
        },
    )


@router.get("/detail/{food_id}", response_class=HTMLResponse)
async def food_detail(
    request: Request,
    food_id: int,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_admin),
):
    """食品详情页"""
    food = crud.food.get(db, id=food_id)
    if not food:
        raise HTTPException(status_code=404, detail="食品不存在")
    
    return templates.TemplateResponse(
        "admin/food/detail.html",
        {
            "request": request,
            "food": food,
            "current_user": current_user,
        },
    )


@router.get("/create", response_class=HTMLResponse)
async def food_create_form(
    request: Request,
    current_user: models.User = Depends(deps.get_current_active_admin),
    db: Session = Depends(deps.get_db),
):
    """创建食品表单页"""
    # 获取所有分类
    categories = crud.food.get_all_categories(db)
    food_types = crud.food.get_all_types(db)
    
    return templates.TemplateResponse(
        "admin/food/create.html",
        {
            "request": request,
            "categories": categories,
            "food_types": food_types,
            "current_user": current_user,
        },
    )


@router.post("/create")
async def food_create(
    request: Request,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_admin),
    name: str = Form(...),
    code: str = Form(...),
    category: Optional[str] = Form(None),
    food_type: Optional[str] = Form(None),
    calory: Optional[float] = Form(None),
    protein_fraction: Optional[float] = Form(None),
    fat_fraction: Optional[float] = Form(None),
    carb_fraction: Optional[float] = Form(None),
    health_light: Optional[int] = Form(None),
    is_liquid: bool = Form(False),
    can_revise: bool = Form(True),
    thumb_image: Optional[UploadFile] = File(None),
    large_image: Optional[UploadFile] = File(None),
):
    """创建食品处理"""
    # 检查编码是否已存在
    food = crud.food.get_by_code(db, code=code)
    if food:
        raise HTTPException(status_code=400, detail=f"编码为 {code} 的食品已存在")
    
    # 处理图片上传
    from app.services.storage import StorageService
    thumb_image_url = None
    large_image_url = None
    
    if thumb_image:
        thumb_content = await thumb_image.read()
        thumb_upload = await StorageService.upload_image(
            thumb_content, 
            f"food/{code}/thumb.jpg"
        )
        thumb_image_url = thumb_upload.get("url")
    
    if large_image:
        large_content = await large_image.read()
        large_upload = await StorageService.upload_image(
            large_content, 
            f"food/{code}/large.jpg"
        )
        large_image_url = large_upload.get("url")
    
    # 创建营养概况
    nutritional_profile = schemas.NutritionalProfileCreate(
        health_light=health_light,
        calory=calory,
        protein_fraction=protein_fraction,
        fat_fraction=fat_fraction,
        carb_fraction=carb_fraction,
    )
    
    # 创建食品
    food_in = schemas.FoodCreate(
        name=name,
        code=code,
        category=category,
        food_type=food_type,
        thumb_image_url=thumb_image_url,
        large_image_url=large_image_url,
        is_liquid=is_liquid,
        can_revise=can_revise,
        nutritional_profile=nutritional_profile
    )
    
    food = crud.food.create(db, obj_in=food_in)
    
    return {"success": True, "food_id": food.id}


@router.get("/import", response_class=HTMLResponse)
async def food_import_form(
    request: Request,
    current_user: models.User = Depends(deps.get_current_active_admin),
):
    """导入食品表单页"""
    return templates.TemplateResponse(
        "admin/food/import.html",
        {
            "request": request,
            "current_user": current_user,
        },
    )


@router.post("/import")
async def food_import(
    request: Request,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_admin),
    file: UploadFile = File(...),
):
    """导入食品处理"""
    # 读取上传的JSON文件
    content = await file.read()
    
    # 创建临时文件
    import tempfile
    import os
    temp_dir = tempfile.gettempdir()
    temp_file_path = os.path.join(temp_dir, file.filename)
    
    with open(temp_file_path, "wb") as f:
        f.write(content)
    
    # 导入食品数据
    result = FoodImportService.import_from_json(db, temp_file_path)
    
    # 删除临时文件
    os.remove(temp_file_path)
    
    return result
```

## 五、数据导入实战案例

以下是使用命令行工具导入食品数据的实际操作示例：

### 1. 基于 JSON 文件导入单个食品

```bash
# 导入单个食品数据
python -m app.cli import-food app/data/aduxian.json

# 输出
成功导入食品: 腌笃鲜(不含汤)
```

### 2. 批量导入目录中的全部食品

```bash
# 批量导入目录中的所有食品数据
python -m app.cli import-batch app/data/foods/

# 输出
批量导入结果: 总计处理: 120，成功: 118，失败: 2

失败记录:
  - invalid_format.json: JSON格式不正确
  - missing_code.json: 缺少必要的食品编码
```

### 3. 环境变量配置示例

```env
# .env 文件
# 数据库连接
DATABASE_URL=postgresql://user:password@localhost:5432/sciencefit

# 图片存储配置
STORAGE_TYPE=oss  # 可选: local, oss, cos
OSS_ACCESS_KEY=your_access_key
OSS_SECRET_KEY=your_secret_key
OSS_BUCKET=your_bucket
OSS_ENDPOINT=oss-cn-shanghai.aliyuncs.com
OSS_DOMAIN=https://your-bucket.oss-cn-shanghai.aliyuncs.com

# API配置
API_V1_STR=/api/v1
SECRET_KEY=your_secret_key_here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60
```

## 六、SQL查询示例

以下是针对设计的数据库模型的一些常用SQL查询示例:

### 1. 获取热量最高的10种食品

```sql
SELECT f.name, f.code, f.category, np.calory 
FROM foods f
JOIN nutritional_profiles np ON f.id = np.food_id
WHERE np.calory IS NOT NULL
ORDER BY np.calory DESC
LIMIT 10;
```

### 2. 查询用户一周的总卡路里摄入量

```sql
SELECT 
    mr.date,
    SUM(mr.total_calory) as daily_calory
FROM 
    meal_records mr
WHERE 
    mr.user_id = '用户ID' 
    AND mr.date BETWEEN CURRENT_DATE - INTERVAL '7 days' AND CURRENT_DATE
GROUP BY 
    mr.date
ORDER BY 
    mr.date;
```

### 3. 查询某食品的所有营养素含量

```sql
SELECT 
    fnv.name_cn as nutrient_name,
    fnv.value,
    fnv.unit_name,
    fnv.category
FROM 
    food_nutrient_values fnv
WHERE 
    fnv.food_id = (SELECT id FROM foods WHERE code = '食品编码')
ORDER BY 
    fnv.category, fnv.name_cn;
```

### 4. 统计用户常吃的食物类型

```sql
SELECT 
    f.category,
    COUNT(*) as count
FROM 
    food_items fi
JOIN 
    meal_records mr ON fi.meal_record_id = mr.id
JOIN 
    foods f ON fi.food_id = f.id
WHERE 
    mr.user_id = '用户ID'
    AND mr.date > CURRENT_DATE - INTERVAL '30 days'
GROUP BY 
    f.category
ORDER BY 
    count DESC;
```

### 5. 使用PostgreSQL特有的JSON查询

```sql
-- 提取含有特定健康警告的食品
SELECT 
    f.name, 
    f.code
FROM 
    foods f
JOIN 
    nutritional_profiles np ON f.id = np.food_id
WHERE 
    np.warnings @> '["高脂肪"]'::jsonb;

-- 按能量组成比例查询食品
SELECT 
    f.name, 
    np.protein_fraction, 
    np.fat_fraction, 
    np.carb_fraction
FROM 
    foods f
JOIN 
    nutritional_profiles np ON f.id = np.food_id
WHERE 
    np.protein_fraction > 30
    AND np.fat_fraction < 30
ORDER BY 
    np.protein_fraction DESC;
```

## 七、总结

本方案实现了一个完整的食品数据库和饮食分析系统，主要特点如下：

1. **数据模型优化**：设计了高效、扩展性强的数据库结构，兼顾标准化和查询性能。

2. **API功能完善**：提供了包括食品库管理、餐食记录、食物识别在内的全方位API。

3. **数据导入灵活**：支持单个和批量导入JSON格式的食品数据，确保系统数据丰富性。

4. **管理后台集成**：提供管理界面，方便管理员维护食品库数据。

5. **PostgreSQL特性应用**：充分利用PostgreSQL的JSONB类型，提高了数据灵活性。

6. **业务逻辑分离**：将核心业务逻辑封装在服务层，保证代码可维护性和可测试性。

7. **可扩展性考虑**：设计时预留了扩展空间，可以轻松添加食物识别、个性化推荐等高级功能。

该实现方案无缝集成到现有的项目结构中，可以作为智能健身教练小程序后端服务的重要组成部分，为用户提供全面的饮食记录和分析功能。
