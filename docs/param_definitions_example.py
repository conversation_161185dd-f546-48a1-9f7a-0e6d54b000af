#!/usr/bin/env python3
"""
参数定义中心使用示例

本示例展示如何使用统一的参数定义中心进行参数提取、验证和处理
"""

from app.core.param_definitions import (
    # 常量
    BODY_PART_CATEGORIES, MUSCLE_CATEGORIES, EQUIPMENT_CATEGORIES,
    BODY_PART_SYNONYMS, TRAINING_SCENARIOS, TRAINING_GOALS,
    
    # 辅助函数
    normalize_input, get_required_params_for_intent, get_param_collection_order,
    get_param_question, get_param_confirmation, format_param_value
)

def demo_parameter_extraction():
    """演示参数提取功能"""
    print("=== 参数提取演示 ===")
    
    # 1. 获取指定意图所需的参数
    intent = "recommend_exercise"
    required_params = get_required_params_for_intent(intent)
    print(f"意图 '{intent}' 所需参数: {required_params}")
    
    # 2. 获取参数收集顺序
    collection_order = get_param_collection_order(intent)
    print(f"参数收集顺序: {collection_order}")
    
    # 3. 演示参数问题生成
    for param in required_params:
        question = get_param_question(param)
        print(f"参数 '{param}' 问题: {question}")
    
    # 4. 演示参数确认生成
    sample_params = {
        "body_part": "胸部",
        "scenario": "home",
        "training_goal": "增肌"
    }
    
    for param, value in sample_params.items():
        confirmation = get_param_confirmation(param, value)
        print(f"参数 '{param}' 确认: {confirmation}")

def demo_parameter_validation():
    """演示参数验证和格式化功能"""
    print("\n=== 参数验证和格式化演示 ===")
    
    # 标准化输入
    text_samples = ["  胸部  ", "BACK", " Home Training "]
    for text in text_samples:
        normalized = normalize_input(text)
        print(f"原始: '{text}' -> 标准化: '{normalized}'")
    
    # 参数值格式化
    param_values = {
        "scenario": "home",
        "plan_type": "daily",
        "difficulty": 3,
        "training_goal": "增肌"
    }
    
    for param, value in param_values.items():
        formatted = format_param_value(param, value)
        print(f"参数 '{param}' 值 '{value}' 格式化: '{formatted}'")

def demo_parameter_matching():
    """演示参数匹配功能"""
    print("\n=== 参数匹配演示 ===")
    
    # 身体部位同义词查找
    body_parts = ["chest", "背部", "肩膀", "二头"]
    for part in body_parts:
        print(f"身体部位 '{part}' 的同义词:")
        for category, synonyms in BODY_PART_SYNONYMS.items():
            if part.lower() in map(str.lower, [category] + synonyms):
                print(f"  匹配到 '{category}': {synonyms}")
    
    # 训练目标匹配
    goals = ["增加肌肉", "减肥", "增强力量"]
    for goal in goals:
        print(f"训练目标 '{goal}' 匹配:")
        for category, keywords in TRAINING_GOALS.items():
            for keyword in keywords:
                if keyword.lower() in goal.lower():
                    print(f"  匹配到 '{category}'")
                    break

def main():
    """主函数"""
    print("参数定义中心示例程序\n")
    
    # 运行演示函数
    demo_parameter_extraction()
    demo_parameter_validation()
    demo_parameter_matching()
    
    print("\n演示完成")

if __name__ == "__main__":
    main() 