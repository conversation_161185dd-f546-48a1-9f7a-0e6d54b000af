# 数据库管理文档

## 数据库概述

系统使用PostgreSQL作为主要数据库，用于存储用户信息、设置数据、分享记录等核心数据。同时，使用Redis进行缓存和会话管理。

## 数据库配置

### PostgreSQL配置

项目默认使用PostgreSQL数据库。数据库连接配置如下：

1. **开发环境**：
   ```
   数据库主机：127.0.0.1 或 localhost
   端口：5432（本地安装）或 5433（Docker映射端口）
   用户名：postgres
   密码：postgres
   数据库名：fitness_db
   ```

2. **生产环境**：
   配置在 `.env` 文件中：
   ```
   DATABASE_URL=postgresql://fitnessuser:password@localhost:5432/fitness_db
   ```

3. **Alembic 迁移**：
   配置在 `alembic.ini` 文件中：
   ```
   sqlalchemy.url = postgresql://postgres:postgres@127.0.0.1:5433/fitness_db
   ```

### Redis配置

Redis用于缓存和会话管理：

```
REDIS_URL=redis://localhost:6379/0
```

## 数据库迁移

项目使用Alembic进行数据库版本控制和迁移管理。

### 创建迁移

当数据模型发生变化时，创建新的迁移文件：

```bash
# 自动生成迁移文件
alembic revision --autogenerate -m "描述变更内容"
```

### 应用迁移

将迁移应用到数据库：

```bash
# 升级到最新版本
alembic upgrade head

# 升级到特定版本
alembic upgrade <revision_id>

# 回滚到上一个版本
alembic downgrade -1
```

### 查看迁移历史

```bash
# 查看当前版本
alembic current

# 查看迁移历史
alembic history
```

## 模拟数据库

为简化开发和测试，系统提供了模拟数据库功能。在开发环境中，系统使用内存中的模拟数据，无需连接真实数据库。

### 模拟数据库主要特性

- 支持常见的SQLAlchemy查询操作
- 提供预设的测试数据（用户、设置、分享数据等）
- 自动处理错误和异常

### 启用/禁用模拟数据库

```python
# app/db/session.py
USE_MOCK_SESSION = True  # 设置为 False 使用真实数据库
```

## 数据库管理工具

项目提供两种数据库管理方案：

1. **Docker集成的pgAdmin** - 适用于团队协作和服务器部署环境
2. **本地安装的DBeaver** - 适用于个人开发环境

### pgAdmin (Docker集成)

项目已在Docker配置中集成pgAdmin，便于团队成员通过Web界面管理数据库。

#### 访问pgAdmin

1. 确保已启动Docker服务：
   ```bash
   docker-compose up -d
   ```

2. 在浏览器中访问：
   ```
   http://localhost:5050
   ```

3. 使用默认凭据登录：
   - 邮箱：<EMAIL>
   - 密码：sciencefit2025

#### 首次设置

1. 登录后，右键点击左侧导航栏中的"Servers"，选择"Register" > "Server"
2. 在"General"选项卡中，为服务器取一个名称（如"Fitness DB"）
3. 切换到"Connection"选项卡，填写以下信息：
   ```
   Host name/address: db  # 使用Docker网络中的服务名
   Port: 5432
   Maintenance database: postgres
   Username: postgres  # 与docker-compose.yml中设置一致
   Password: postgres  # 与docker-compose.yml中设置一致
   ```
4. 点击"Save"保存配置

### 本地DBeaver

DBeaver是一个跨平台的数据库工具，支持多种数据库管理。

#### 安装DBeaver

1. 从[官方网站](https://dbeaver.io/download/)下载并安装DBeaver
2. 启动DBeaver应用程序

#### 连接数据库

1. 点击"新建连接"按钮
2. 选择"PostgreSQL"
3. 填写连接信息：
   ```
   Server Host: localhost
   Port: 5433  # Docker映射端口或5432（本地安装）
   Database: fitness_db
   Username: postgres
   Password: postgres
   ```
4. 点击"测试连接"确认连接成功
5. 点击"完成"保存连接

## SSH隧道连接

为了安全地访问生产环境或远程服务器上的数据库，使用SSH隧道是一个良好实践。

### pgAdmin中设置SSH隧道

1. 在pgAdmin中添加新服务器时，切换到**SSH隧道**选项卡
2. 勾选**使用SSH隧道**
3. 填写SSH连接信息：
   ```
   隧道主机：服务器的IP地址或域名
   隧道端口：22（默认SSH端口）
   用户名：SSH用户名
   认证方式：
     - 密码：输入SSH用户密码
     或
     - 身份文件：选择您的SSH私钥文件
   ```

### 命令行设置SSH隧道

通过命令行手动创建SSH隧道：

```bash
ssh -L 本地端口:数据库主机:数据库端口 用户名@服务器地址
```

例如，将远程服务器上的PostgreSQL端口转发到本地5433端口：

```bash
ssh -L 5433:localhost:5432 <EMAIL>
```

## 数据备份与恢复

### 创建备份

```bash
# 备份整个数据库
pg_dump -U postgres -d fitness_db > backup_full_$(date +%Y%m%d).sql

# 仅备份架构（不含数据）
pg_dump -U postgres -d fitness_db --schema-only > backup_schema_$(date +%Y%m%d).sql

# 仅备份数据（不含架构）
pg_dump -U postgres -d fitness_db --data-only > backup_data_$(date +%Y%m%d).sql

# 备份特定表
pg_dump -U postgres -d fitness_db -t users -t user_settings > backup_users_$(date +%Y%m%d).sql
```

### 自动备份

设置定时任务进行自动备份：

```bash
# 编辑crontab
crontab -e

# 添加每日凌晨2点备份任务
0 2 * * * pg_dump -U postgres -d fitness_db > /backup/fitness_db_$(date +\%Y\%m\%d).sql
```

### 恢复备份

```bash
# 恢复完整备份
psql -U postgres -d fitness_db < backup_file.sql

# 恢复到新数据库
createdb -U postgres fitness_db_new
psql -U postgres -d fitness_db_new < backup_file.sql
```

## 常见数据库操作

### 用户表操作

```sql
-- 查看所有用户
SELECT * FROM users;

-- 查找特定用户
SELECT * FROM users WHERE nickname LIKE '%关键词%';

-- 更新用户状态
UPDATE users SET is_active = FALSE WHERE id = 123;

-- 删除用户（慎用）
DELETE FROM users WHERE id = 123;
```

### 统计查询

```sql
-- 用户注册统计（按日）
SELECT DATE(created_at) as registration_date, COUNT(*) as new_users
FROM users
GROUP BY DATE(created_at)
ORDER BY registration_date DESC;

-- 资料完成率
SELECT 
    COUNT(*) as total_users,
    SUM(CASE WHEN completed = TRUE THEN 1 ELSE 0 END) as completed_profiles,
    ROUND(SUM(CASE WHEN completed = TRUE THEN 1 ELSE 0 END)::numeric / COUNT(*) * 100, 2) as completion_rate
FROM users;

-- 分享统计（按类型）
SELECT 
    type, 
    COUNT(*) as share_count
FROM share_tracks
GROUP BY type
ORDER BY share_count DESC;
```

## 索引优化

关键表上已创建以下索引以提高查询性能：

```sql
-- 用户表索引
CREATE INDEX idx_users_openid ON users(openid);
CREATE INDEX idx_users_created_at ON users(created_at);

-- 分享表索引
CREATE INDEX idx_share_tracks_user_id ON share_tracks(user_id);
CREATE INDEX idx_share_tracks_created_at ON share_tracks(created_at);
```

## 性能优化建议

1. **定期执行VACUUM**：清理无用数据，避免数据库膨胀
   ```sql
   VACUUM ANALYZE;
   ```

2. **监控慢查询**：分析并优化性能问题
   ```sql
   -- 启用查询日志
   ALTER SYSTEM SET log_min_duration_statement = 200;  -- 记录执行时间超过200ms的查询
   ```

3. **使用适当的连接池**：配置SQLAlchemy连接池
   ```python
   # 在app/db/session.py配置
   engine = create_engine(
       settings.DATABASE_URL,
       pool_size=10,
       max_overflow=20,
       pool_timeout=30
   )
   ``` 