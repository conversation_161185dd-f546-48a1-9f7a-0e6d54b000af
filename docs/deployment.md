# 部署文档

## 环境要求

部署智能健身教练后端服务需要以下环境：

- Python 3.11+
- Docker & Docker Compose
- PostgreSQL 15
- Redis 7
- Nginx 1.18+
- 可选：supervisord（生产环境运行管理）

## 本地开发环境部署

### 1. 克隆项目

```bash
git clone <repository-url>
cd backend
```

### 2. 创建Python虚拟环境

```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
.\venv\Scripts\activate  # Windows
```

### 3. 安装依赖

```bash
pip install -r requirements.txt
```

### 4. 配置环境变量

```bash
cp .env.example .env
# 编辑.env文件，设置必要的环境变量
```

关键的环境变量包括：

```
# 应用设置
APP_NAME=ScienceFit
DEBUG=True
SECRET_KEY=your_secret_key_here
API_PREFIX=/api/v1

# 数据库设置
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/fitness_db

# Redis设置
REDIS_URL=redis://localhost:6379/0

# 微信设置
WECHAT_APP_ID=your_wechat_appid
WECHAT_APP_SECRET=your_wechat_secret

# JWT设置
ACCESS_TOKEN_EXPIRE_DAYS=30
```

### 5. 启动开发服务器

```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

## Docker部署

### 1. 配置Docker环境

确保已安装Docker和Docker Compose，然后编辑`docker-compose.yml`文件中的相关配置。

### 2. 启动Docker服务

```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f
```

### 3. 检查服务状态

```bash
docker-compose ps
```

## 生产环境部署

### 1. 服务器环境准备

更新系统并安装必要的包：

```bash
sudo apt update
sudo apt upgrade -y
sudo apt install -y python3-pip python3-venv nginx postgresql redis-server supervisor
```

### 2. 创建数据库

```bash
sudo -u postgres psql

# 在PostgreSQL命令行中执行
CREATE DATABASE fitness_db;
CREATE USER fitnessuser WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE fitness_db TO fitnessuser;
```

### 3. 部署应用代码

```bash
# 创建应用目录
sudo mkdir -p /opt/sciencefit
sudo chown $USER:$USER /opt/sciencefit

# 克隆代码
git clone <repository-url> /opt/sciencefit
cd /opt/sciencefit

# 设置环境
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# 配置环境变量
cp .env.example .env
# 编辑.env文件
```

### 4. 数据库迁移

```bash
alembic upgrade head
```

### 5. 配置Supervisor

创建supervisor配置文件：

```bash
sudo nano /etc/supervisor/conf.d/sciencefit.conf
```

配置内容：

```ini
[program:sciencefit]
command=/opt/sciencefit/venv/bin/uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
directory=/opt/sciencefit
user=www-data
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
stderr_logfile=/var/log/sciencefit/err.log
stdout_logfile=/var/log/sciencefit/out.log
environment=
    DATABASE_URL="postgresql://fitnessuser:your_password@localhost:5432/fitness_db",
    SECRET_KEY="your_production_secret_key",
    DEBUG="False"
```

创建日志目录：

```bash
sudo mkdir -p /var/log/sciencefit
sudo chown www-data:www-data /var/log/sciencefit
```

重新加载supervisor:

```bash
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl status sciencefit
```

### 6. 配置Nginx

创建Nginx配置文件：

```bash
sudo nano /etc/nginx/sites-available/sciencefit
```

配置内容：

```nginx
server {
    listen 80;
    server_name your_domain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态文件缓存
    location /static/ {
        alias /opt/sciencefit/static/;
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
    }

    # 媒体文件处理
    location /data/ {
        alias /data/;
        expires 7d;
        add_header Cache-Control "public, max-age=604800";
    }
}
```

启用配置并重启Nginx：

```bash
sudo ln -s /etc/nginx/sites-available/sciencefit /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### 7. 创建静态文件和媒体文件目录

```bash
# 创建用户文件存储目录
sudo mkdir -p /data/users
sudo chown -R www-data:www-data /data
sudo chmod -R 755 /data
```

### 8. 配置HTTPS (可选但推荐)

使用Certbot安装SSL证书：

```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your_domain.com
```

### 9. 数据库更新/迁移

部署新功能时，需要更新数据库结构：

```bash
# 登录到PostgreSQL
sudo -u postgres psql -d fitness_db

# 执行SQL命令添加列
ALTER TABLE share_tracks ADD COLUMN IF NOT EXISTS qrcode_url VARCHAR;

# 退出PostgreSQL
\q
```

或使用Alembic自动迁移：

```bash
# 创建迁移
alembic revision --autogenerate -m "add qrcode_url column"

# 应用迁移
alembic upgrade head
```

## Docker生产环境部署

### 1. 准备生产环境Docker配置

创建生产环境docker-compose文件：

```bash
cp docker-compose.yml docker-compose.prod.yml
```

编辑`docker-compose.prod.yml`，调整为生产环境配置。

### 2. 配置环境变量

```bash
cp .env.example .env.prod
# 编辑.env.prod文件，设置生产环境变量
```

### 3. 启动Docker服务

```bash
docker-compose -f docker-compose.prod.yml up -d
```

### 4. 配置反向代理

配置Nginx反向代理到Docker服务：

```nginx
server {
    listen 80;
    server_name your_domain.com;
    
    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 维护与操作

### 重启服务

#### 使用Supervisor:

```bash
sudo supervisorctl restart sciencefit
```

#### 使用Docker:

```bash
docker-compose restart
```

### 查看日志

#### Supervisor日志:

```bash
sudo tail -f /var/log/sciencefit/out.log
sudo tail -f /var/log/sciencefit/err.log
```

#### Docker日志:

```bash
docker-compose logs -f
```

### 数据库备份

```bash
# 创建备份
pg_dump -U postgres -d fitness_db > backup_$(date +%Y%m%d).sql

# 还原备份
psql -U postgres -d fitness_db < backup_file.sql
```

### 更新应用

```bash
# 拉取最新代码
git pull

# 安装新依赖
pip install -r requirements.txt

# 应用数据库迁移
alembic upgrade head

# 重启服务
sudo supervisorctl restart sciencefit
```

## 故障排除

### 应用无法启动

检查日志文件：
```bash
sudo tail -f /var/log/sciencefit/err.log
```

常见问题：
- 数据库连接失败：检查DATABASE_URL配置和PostgreSQL服务状态
- 权限问题：检查文件和目录权限
- 依赖问题：确保所有依赖都已正确安装

### 数据库连接问题

检查PostgreSQL服务状态：
```bash
sudo systemctl status postgresql
```

检查数据库用户和权限：
```bash
sudo -u postgres psql -c "\du"
```

### Nginx配置问题

测试Nginx配置：
```bash
sudo nginx -t
```

查看Nginx错误日志：
```bash
sudo tail -f /var/log/nginx/error.log
```

## 安全建议

1. 使用强密码和密钥
2. 定期更新系统和依赖
3. 启用HTTPS
4. 限制数据库和Redis访问
5. 使用防火墙限制端口访问
6. 定期备份数据
7. 监控服务器资源使用情况 