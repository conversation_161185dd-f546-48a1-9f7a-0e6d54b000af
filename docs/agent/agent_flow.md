# 智能健身AI助手消息处理流程

## 整体流程概述

智能健身AI助手处理用户消息的流程包括以下主要步骤：

1. **消息接收与预处理**：接收用户消息，获取会话上下文，处理中断和恢复机制
2. **会话状态检查**：检查是否处于特定流程中（如信息收集、训练参数收集等）
3. **意图识别与上下文传递**：识别用户意图，传递上下文增强精准度
4. **参数提取与完整性检查**：提取意图相关参数，验证完整性
5. **用户信息检查**：检查用户信息是否完整，必要时收集用户信息
6. **意图执行**：根据识别的意图执行相应的处理逻辑
7. **响应生成**：生成个性化的回复，并使用智能代理应用增强智能性
8. **会话状态更新**：更新会话状态、元数据和上下文

系统采用模块化设计，核心服务入口为`ConversationService`类（位于`app/services/conversation/orchestrator.py`），该类协调多个专门的管理器组件，包括：

- **IntentRecognizer**：负责识别用户意图并提取相关参数，支持上下文感知的意图识别
- **IntentHandler**：处理不同类型意图的执行逻辑，根据意图类型分发到具体处理函数
- **UserProfileManager**：负责用户信息的收集、验证和管理，替代原ActiveQueryManager
- **TrainingParamManager**：负责训练参数的提取、收集和管理，处理训练相关参数
- **ParameterExtractor**：专注于从用户消息中提取训练相关参数，支持LLM提取和关键词匹配
- **ConversationStateManager**：管理会话状态，实现状态模式设计模式，根据元数据确定当前会话状态
- **CharacterManager**：管理AI回复的性格和风格，支持不同的回复风格
- **TrainingPlanManager**：管理训练计划的生成和格式化，调用TrainingPlanService
- **InterruptionHandler**：处理对话中断和恢复机制，提升长时间对话体验
- **PendingRequestManager**：管理待处理请求的保存和恢复

## 详细流程说明

### 1. 消息接收与预处理

当用户发送消息时，`ConversationService.process_message_stream`方法首先进行以下处理：

- 获取或创建会话ID和用户信息：
  - 使用传入的user_id或初始化时设置的user_id
  - 记录会话ID（session_id）和消息内容
  - 获取或创建conversation对象，用于存储会话状态

- 检查会话的连续性，处理可能的中断：
  - 获取上一条AI消息及其时间戳
  - 计算与上条消息的时间差
  - 判断是否处于特定流程（如信息收集、参数收集）
  - 如果时间差过大且处于流程中，进行中断处理：
    - 调用`_check_message_relevance`检查新消息与当前流程的相关性
    - 如果不相关，询问用户是否继续之前流程
    - 调用`_analyze_continuation_response`分析用户选择
    - 根据用户选择继续原流程或切换到新问题

- 获取会话历史并进行处理：
  - 调用`crud.crud_message.get_conversation_history_raw`获取原始历史消息
  - 过滤重复内容
  - 限制历史消息数量（使用HISTORY_MESSAGE_LIMIT常量）
  - 简化消息内容减少字符量使用

- 提取上下文信息：
  - 从会话元数据（meta_info）中获取关键信息
  - 检查是否存在活跃训练计划（related_plan_id）
  - 检查是否处于特定会话流程（active_flow）
  - 检查是否处于参数收集状态（collecting_training_params）
  - 检查是否处于用户信息收集状态（waiting_for_info）
  - 记录这些信息到response_meta_info供后续步骤使用

### 2. 会话状态检查

系统通过`ConversationStateManager`检查当前会话是否处于特定的状态或流程中，并根据状态分发到不同的处理逻辑。`ConversationStateManager`实现了状态模式设计模式，根据元数据确定当前会话状态：

```python
def get_state(self, meta_info: Dict[str, Any]) -> ConversationState:
    # 优先处理中断确认状态
    if meta_info.get("confirming_continuation"):
        return self.states["interruption_confirmation"]

    # 其次处理用户信息收集状态
    if meta_info.get("waiting_for_info"):
        return self.states["user_profile_collection"]

    # 再次处理训练参数收集状态
    if meta_info.get("collecting_training_params"):
        return self.states["training_param_collection"]

    # 默认为正常对话状态
    return self.states["normal"]
```

系统支持以下状态类型：

- **中断确认状态** (`InterruptionConfirmationState`)：如果`confirming_continuation`标记为真：
  - 处理用户对中断询问的响应
  - 分析用户是否选择继续之前的流程或处理新问题
  - 根据用户选择恢复原始状态或清除状态标志

- **用户信息收集状态** (`UserProfileCollectionState`)：如果`waiting_for_info`标记非空：
  - 调用`_handle_user_profile_collection`方法处理用户输入的个人信息
  - 使用`UserProfileManager.validate_and_parse`验证用户输入
  - 调用`crud.crud_user.update`更新用户数据库记录
  - 如有更多缺失字段，继续收集；否则完成流程并处理原始请求：
    - 如果有待处理请求（`pending_request`），恢复并处理该请求
    - 否则生成确认消息并结束流程

- **训练参数收集状态** (`TrainingParamCollectionState`)：如果`collecting_training_params`标记为真：
  - 调用`_handle_training_param_collection`方法处理用户输入的训练参数
  - 从`response_meta_info["asking_param"]`获取当前询问的参数名
  - 使用`ParameterExtractor`和`TrainingParamManager`解析用户输入并更新训练参数
  - 调用`TrainingParamManager.check_required_training_params`检查参数完整性
  - 如果参数完整：
    - 设置`collecting_training_params=False`
    - 执行训练计划生成（调用`_handle_intent_execution`）
  - 否则：
    - 更新`asking_param`为下一个缺失参数
    - 生成询问下一个参数的提示消息
    - 继续参数收集流程

- **正常对话状态** (`NormalConversationState`)：如果不在特殊状态中：
  - 进行意图识别（通过`IntentRecognizer`）
  - 提取训练参数（对训练相关意图）
  - 检查用户信息完整性（对特定意图）
  - 执行意图处理逻辑（通过`IntentHandler`）

### 3. 意图识别与上下文传递

系统使用`IntentRecognizer`类识别用户意图，流程如下：

1. 检查是否有快速意图（通过API参数`quick_intent`指定），有则直接使用：
   - 快速意图允许前端或其他服务直接指定意图，跳过识别步骤
   - 保存在`self.quick_intent`中供后续使用

2. 否则，构建意图上下文（context字典）：
   - 从`response_meta_info`中提取关键信息
   - 如果存在关联计划ID（`related_plan_id`），添加到上下文
   - 如果存在活跃流程（`active_flow`），添加到上下文
   - 如果存在训练参数（`training_params`），添加到上下文

3. 调用`IntentRecognizer.recognize_intent`方法（传入消息和上下文）：
   - 首先尝试使用专用意图识别模型（`intent-recognition-app`）
   - 如果识别失败，使用基于大语言模型的通用方法（`LLM_INTENT_RECOGNITION_MODEL`）
   - 对于上下文感知的识别，大语言模型会收到扩展的提示，包含上下文信息
   - 返回`IntentData`对象，包含意图名称、置信度和提取的参数

4. 对识别结果进行后处理：
   - 记录意图名称（`intent_data.intent`）和置信度（`intent_data.confidence`）
   - 将意图信息更新到`response_meta_info`中
   - 记录日志，便于调试和分析

针对特定意图和特定条件，系统支持以下强化处理：

- 对于"X怎么练"格式的查询，`IntentRecognizer`会优先识别为`recommend_exercise`意图
- 对于继续讨论已有训练计划的消息，如果上下文中有`related_plan_id`，会优先识别为`discuss_training_plan`意图
- 通过上下文传递，提升相关流程的意图识别准确性，例如：
  - 在训练计划讨论流程中，更容易识别出与训练相关的问题
  - 在用户信息收集流程中，更准确地识别用户提供的个人信息

### 4. 参数提取与完整性检查

对于训练相关意图（如`daily_workout_plan`、`recommend_exercise`等），系统使用`ParameterExtractor`和`TrainingParamManager`提取和管理参数：

1. 调用`TrainingParamManager.extract_training_parameters`方法提取参数：
   - 首先尝试使用`ParameterExtractor._extract_parameters_with_llm`通过LLM提取参数
   - 如果LLM提取失败，回退到`ParameterExtractor._extract_parameters_with_keywords`使用关键词匹配
   - 提取的参数包括：
     - 身体部位（`body_part`）：如胸部、背部、腿部等
     - 训练场景（`scenario`）：健身房（gym）或居家（home）
     - 计划类型（`plan_type`）：单日（daily）或周期（weekly）
     - 肌肉群（`muscle`）：如二头肌、三角肌等
     - 器材（`equipment`）：如哑铃、杠铃等
     - 训练目标（`training_goal`）：如增肌、减脂等
     - 难度级别（`difficulty`）：初级、中级、高级等

2. 整合提取的参数与现有元数据：
   - 将提取的参数与`response_meta_info["training_params"]`合并
   - 处理各种数据格式（列表、字符串等）
   - 使用`TrainingParamManager`进行数据清洗和标准化
   - 更新`response_meta_info["training_params"]`

3. 检查参数完整性：
   - 调用`TrainingParamManager.check_required_training_params`检查必要参数是否完整
   - 根据意图类型确定必要参数（如训练计划需要body_part、scenario、plan_type）
   - 如果有缺失参数，进入参数收集模式：
     - 设置`response_meta_info["collecting_training_params"] = True`
     - 设置`response_meta_info["asking_param"]`为第一个缺失参数
     - 调用`TrainingParamManager.get_training_prompt_message`生成询问提示
     - 流式返回询问消息
     - 更新会话元数据并退出当前处理流程，等待用户响应

4. 参数验证与规范化：
   - 对提取的参数进行验证，确保符合预期格式和范围
   - 将非标准表达映射到标准参数值（如"在家"→"home"）
   - 处理参数冲突和优先级（如用户明确指定的参数优先于提取的参数）

### 5. 用户信息检查

对于需要用户信息的意图（如训练计划、营养建议等），系统使用`UserProfileManager`检查和收集用户信息：

1. 调用`UserProfileManager.get_missing_fields`方法检查必要的用户信息：
   - 根据意图类型确定所需的用户信息字段（如性别、年龄、身高、体重、健身目标等）
   - 从数据库获取当前用户信息（`crud.crud_user.get`）
   - 检查哪些必要字段缺失或需要更新

2. 如果有缺失字段，进入引导式信息收集模式：
   - 保存当前请求到`response_meta_info["pending_request"]`，包含原始消息和意图数据
   - 设置`response_meta_info["session_state"] = "guided"`标识引导式收集
   - 设置`response_meta_info["waiting_for_info"]`，包含第一个需要收集的字段信息：
     ```python
     {
         "field": "first_missing_field",  # 如"height"、"weight"、"gender"等
         "display_name": "显示名称",      # 如"身高"、"体重"、"性别"等
         "is_retry": False                # 是否是重试收集（验证失败后）
     }
     ```
   - 调用`UserProfileManager.generate_query_message`生成自然、友好的询问消息
   - 流式返回询问消息
   - 更新会话元数据并退出当前处理流程，等待用户响应

3. 用户信息验证与处理：
   - 当用户响应信息收集询问时，使用`UserProfileManager.validate_and_parse`验证输入
   - 对于枚举类型字段（如性别、健身目标），将文本映射到对应的数值代码
   - 对于数值类型字段（如身高、体重），进行范围和单位验证
   - 如果验证失败，重新询问并提供更明确的指导
   - 验证成功后更新用户数据库记录

4. 自动计算衍生信息：
   - 当更新身高或体重时，自动计算BMI并更新到用户记录
   - 根据用户信息推断默认训练参数（如经验水平影响训练强度）

### 6. 意图执行流程

当用户信息和训练参数都完整后，系统调用`_handle_intent_execution`方法，该方法委托给`IntentHandler`类处理不同类型的意图。`IntentHandler`根据意图类型分发到具体的处理函数：

```python
async def handle_intent(
    self,
    intent_data: IntentData,
    meta_info: Dict[str, Any],
    user_data: Dict[str, Any],
    history: List[Dict[str, Any]]
) -> AsyncGenerator[Dict[str, Any], None]:
    # 根据意图类型分发
    if intent_data.intent in ["search_exercise", "recommend_exercise"]:
        async for response in self.handle_exercise_intent(intent_data, user_data, history, meta_info):
            yield response
    elif intent_data.intent in ["daily_workout_plan", "weekly_workout_plan"]:
        async for response in self.handle_training_plan_intent(intent_data, user_data, history, meta_info):
            yield response
    elif intent_data.intent in ["nutrition_advice", "diet_suggestion", "macro_calculation"]:
        async for response in self.handle_fitness_advice_intent(intent_data, user_data, history, meta_info):
            yield response
    elif intent_data.intent == "discuss_training_plan":
        async for response in self._handle_discuss_training_plan_intent(intent_data, user_data, history, meta_info):
            yield response
    else:  # 处理 "general_chat" 和未识别的意图
        async for response in self.handle_general_chat(intent_data, user_data, history, meta_info):
            yield response
```

#### 6.1 健身动作推荐/查询（`handle_exercise_intent`）

处理`search_exercise`、`recommend_exercise`等意图：

1. 获取目标身体部位（从`intent_data.parameters`或`response_meta_info["identified_body_part"]`）
2. 获取训练场景（`scenario`，如"gym"或"home"）
3. 获取用户个人资料，调用`_get_user_profile`和`_get_recommended_difficulty`确定推荐难度范围
4. 调用`get_candidate_exercises`从数据库查询符合条件的候选训练动作
5. 使用`personalized_filtering`和`_rule_based_filtering`进行个性化二次筛选：
   - 根据用户经验水平过滤难度不适合的动作
   - 根据训练目标调整推荐的组数和次数
   - 考虑用户的健康状况和限制
6. 如果数据库中没有合适动作，使用LLM生成建议
7. 格式化结果并流式返回训练动作列表及详细信息

#### 6.2 训练计划制定（`handle_training_plan_intent`）

处理`daily_workout_plan`、`weekly_workout_plan`等意图：

1. 从`intent_data.parameters`和`response_meta_info["training_params"]`获取训练参数：
   - 计划类型（`plan_type`）：单日（daily）或周期（weekly）
   - 身体部位（`body_part`）和训练场景（`scenario`）
   - 训练目标（`training_goal`）和难度（`difficulty`）
2. 根据用户活动水平和经验确定训练频率和强度
3. 根据计划类型调用不同的提示模板：
   - 单日计划：`TrainingPlanManager.get_daily_workout_prompt`
   - 周期计划：`TrainingPlanManager.get_weekly_plan_prompt`
4. 调用`TrainingPlanService.generate_training_plan`生成训练计划：
   - 使用`LLM_EXERCISE_GENERATION_MODEL`生成结构化计划
   - 验证计划结构符合预期格式
   - 保存计划到数据库并获取计划ID
5. 更新元数据：
   - 设置`response_meta_info["related_plan_id"] = plan_id`
   - 设置`response_meta_info["active_flow"] = "training_plan_discussion"`
6. 调用`TrainingPlanManager.format_training_plan_response`格式化计划
7. 流式返回计划概览和结构化数据

#### 6.3 健身/营养咨询（`handle_fitness_advice_intent`）

处理`nutrition_advice`、`diet_suggestion`、`macro_calculation`等意图：

1. 调用`_get_user_profile_text`获取格式化的用户资料文本
2. 构建上下文提示，包含用户资料和问题
3. 过滤历史消息，保留相关的健身咨询历史
4. 尝试使用专门的健身建议模型：
   - 首先尝试`agent-app`模型
   - 如果失败，回退到`fitness_advice`模型
5. 使用`LLMProxyService.stream_chat`流式返回个性化建议

#### 6.4 讨论训练计划（`_handle_discuss_training_plan_intent`）

处理`discuss_training_plan`意图：

1. 从`response_meta_info["related_plan_id"]`获取关联计划ID
2. 使用`crud.crud_training_plan.get_with_workouts`获取完整计划详情
3. 调用`_format_training_plan_summary`格式化计划摘要
4. 构建提示，包含用户资料、计划摘要和用户问题
5. 使用`LLMProxyService.stream_chat`生成回复
6. 根据回复内容管理活跃流程状态：
   - 如果回复表明讨论结束，清除`active_flow`
   - 否则保持`active_flow = "training_plan_discussion"`继续讨论

#### 6.5 一般聊天（`handle_general_chat`）

处理`general_chat`和未识别的意图：

1. 获取用户个人资料和对话历史
2. 构建系统提示和上下文
3. 根据用户偏好选择回复风格（通过`CharacterManager`）
4. 使用`LLMProxyService.stream_chat`调用通用对话模型（`conversation_model`）
5. 流式返回回复，支持分段和格式化

所有意图处理函数都通过异步生成器（`AsyncGenerator`）流式返回响应，支持两种格式：
- 文本块：直接返回字符串，如`yield "这是回复的一部分"`
- 结构化数据：返回字典，如`yield {"type": "token", "content": "文本片段"}`

### 7. 响应生成与会话更新

所有意图处理函数都通过异步生成器（`AsyncGenerator`）流式返回响应，并在`_finalize_conversation`方法中完成会话更新：

1. 生成流式响应：
   - 使用`yield`关键字即时返回内容块，支持两种格式：
     - 文本块：直接返回字符串，如`yield "这是回复的一部分"`
     - 结构化数据：返回字典，如`yield {"type": "token", "content": "文本片段"}`
   - 支持元数据更新：`yield {"meta_info_update": {"key": "value"}}`
   - 支持特殊消息类型：`yield {"type": "thinking", "content": "思考过程"}`

2. 元数据更新：
   - 在流式响应过程中，可以通过特殊格式更新元数据
   - 关键元数据字段包括：
     - `intent`：当前识别的意图
     - `collecting_training_params`：是否处于参数收集状态
     - `waiting_for_info`：是否处于信息收集状态
     - `training_params`：收集的训练参数
     - `related_plan_id`：关联的训练计划ID
     - `active_flow`：当前活跃的对话流程

3. 保存AI回复：
   - 调用`_save_ai_response`方法将完整回复保存到数据库
   - 创建新的`Message`记录，包含：
     - `conversation_id`：会话ID
     - `role`："assistant"
     - `content`：完整的AI回复文本
     - `created_at`：当前时间戳
     - `meta_info`：包含意图、置信度等元数据

4. 更新会话状态：
   - 调用`crud.crud_conversation.update`更新会话元数据
   - 提交所有数据库更改（`self.db.commit()`）
   - 记录会话处理完成的日志

## 上下文和连续性机制

系统通过`ConversationStateManager`和元数据管理实现了多种机制来维护对话的上下文和连续性：

### 会话上下文维护

1. **训练计划上下文**：
   - 使用`response_meta_info["related_plan_id"]`标识当前讨论的训练计划
   - 使用`response_meta_info["active_flow"] = "training_plan_discussion"`标识正在讨论训练计划
   - 这些上下文会传递给`IntentRecognizer`，提高后续消息的意图识别准确性
   - 在`_handle_discuss_training_plan_intent`中自动加载相关计划详情

2. **参数收集状态**：
   - 使用`response_meta_info["collecting_training_params"] = true`标识参数收集模式
   - 使用`response_meta_info["asking_param"] = "body_part"`跟踪当前询问的参数
   - 使用`response_meta_info["training_params"]`存储已收集的参数
   - `TrainingParamManager`负责管理整个参数收集流程

3. **用户信息收集状态**：
   - 使用`response_meta_info["waiting_for_info"] = {"field": "height", ...}`标识信息收集模式
   - 使用`response_meta_info["session_state"] = "guided"`标识引导式收集
   - 使用`response_meta_info["pending_request"]`保存原始请求，等待信息收集完成后恢复
   - `UserProfileManager`负责管理整个信息收集流程

4. **状态转换管理**：
   - `ConversationStateManager`根据元数据确定当前会话状态
   - 支持三种主要状态：`NormalConversationState`、`UserProfileCollectionState`和`TrainingParamCollectionState`
   - 每种状态有自己的处理逻辑，实现了状态模式设计模式

### 中断处理机制

1. **中断检测**：
   - 在`process_message_stream`中计算与上一条消息的时间间隔
   - 检查`response_meta_info`中的状态标志（如`waiting_for_info`、`collecting_training_params`）
   - 如果时间间隔超过阈值（如60秒）且处于特定流程中，触发中断处理
   - 调用`_check_message_relevance`判断新消息与当前流程的相关性

2. **中断响应**：
   - 如果新消息与当前流程不相关，生成询问消息："您想继续之前的对话还是回答新问题？"
   - 设置`response_meta_info["confirming_continuation"] = true`
   - 保存当前状态和新消息到`response_meta_info["pending_new_message"]`
   - 流式返回询问消息并等待用户响应

3. **流程恢复**：
   - 当用户响应中断询问时，调用`_analyze_continuation_response`分析用户选择
   - 如果用户选择继续之前流程：
     - 清除`confirming_continuation`标志
     - 恢复原始状态（保持`waiting_for_info`或`collecting_training_params`）
     - 重新发送之前的询问消息
   - 如果用户选择处理新问题：
     - 清除所有特殊状态标志
     - 处理`pending_new_message`中保存的新消息
     - 进入正常的意图识别和处理流程

4. **自动恢复**：
   - 如果用户在中断后发送的新消息与当前流程高度相关（如回答正在询问的问题）
   - 系统会自动继续当前流程，无需显式询问用户

## 优化方案与未来增强

基于当前实现，以下是提升系统效率和自动化程度的优化方案：

### 1. 意图识别增强

- **扩展意图类型**：增加更细分的健身相关意图，如`exercise_form_check`、`progress_tracking`等
- **一次多意图识别**：识别用户消息中可能包含的多个意图并按优先级处理
- **上下文加权识别**：根据对话历史和用户偏好调整不同意图的权重
- **模糊匹配支持**：处理拼写变化、同义词和方言表达
- **意图转换预测**：预测用户可能从一个意图转向另一个意图的概率

### 2. 智能参数提取

- **增强LLM参数提取**：优化`ParameterExtractor._extract_parameters_with_llm`的提示模板
- **隐式参数提取**：从对话上下文中提取未明确表达的参数
- **默认参数智能推断**：基于用户历史记录和偏好设置默认参数
- **参数冲突解决**：当新旧参数冲突时实现智能处理机制
- **参数验证增强**：提供更自然的参数验证失败反馈

### 3. 对话流程优化

- **状态管理重构**：进一步完善`ConversationStateManager`，支持更复杂的状态转换
- **批量参数收集**：一次性询问多个相关参数，减少交互次数
- **渐进式信息收集**：根据重要性分阶段收集用户信息，避免过长的初始信息收集
- **自动流程跨越**：允许在单次对话中完成参数收集、计划生成和讨论的完整流程
- **中断处理增强**：更智能地判断中断后的恢复策略

### 4. 性能优化

- **选择性历史加载**：只加载与当前意图相关的历史消息
- **流式处理增强**：更早地开始生成和返回响应
- **批量数据库操作**：减少数据库交互次数
- **LLM调用优化**：合并多个LLM调用，减少API请求次数
- **Agent静态实例管理**：优化`_initialize_agent`中的静态实例管理，避免重复初始化

### 5. 用户体验提升

- **角色定制增强**：扩展`CharacterManager`功能，支持更多角色类型和个性化设置
- **响应长度控制**：根据场景智能调整回复长度
- **积极主动式交互**：在用户没有明确指示时主动推荐合理的后续行动
- **多模态支持**：集成图像识别功能，支持用户上传健身相关图片进行分析
- **训练计划可视化**：提供训练计划的可视化表示，增强用户理解

### 6. 知识库集成

- **实现RAG知识库**：集成检索增强生成（RAG）功能，提供更准确的健身知识
- **动态知识更新**：支持健身知识库的定期更新和扩展
- **个性化知识推荐**：根据用户兴趣和目标推荐相关健身知识
- **引用来源追踪**：为提供的健身建议添加可靠来源引用

## 流程图与决策树

### 主要流程图

```
用户消息 → 预处理 → 中断检测 → 会话状态检查 → 意图识别 → 参数提取 → 用户信息检查 → 意图执行 → 响应生成 → 状态更新
             ↑                      ↓                               ↓                      ↓
             └─── 流程恢复 ←── 中断处理             信息收集流程 ←─┘              IntentHandler分发
                                    ↓                               ↓                      ↓
                               参数收集 ───────────→ 完成收集 ───→ 返回主流程         生成/返回响应
```

### 状态管理决策树

```
ConversationStateManager.get_state(meta_info)
├── meta_info["waiting_for_info"] 非空? → UserProfileCollectionState
│   ├── _handle_user_profile_collection
│   ├── UserProfileManager.validate_and_parse
│   ├── 更新用户信息
│   └── 检查是否有更多字段需要收集
├── meta_info["collecting_training_params"] 为真? → TrainingParamCollectionState
│   ├── _handle_training_param_collection
│   ├── ParameterExtractor.extract_training_parameters
│   ├── 更新训练参数
│   └── 检查参数完整性
└── 默认 → NormalConversationState
    ├── 意图识别
    ├── 参数提取
    ├── 用户信息检查
    └── 意图执行
```

### 意图处理决策树

```
IntentHandler.handle_intent(intent_data)
├── intent in ["search_exercise", "recommend_exercise"] → handle_exercise_intent
│   ├── 获取身体部位和训练场景
│   ├── 调用get_candidate_exercises查询数据库
│   ├── 使用personalized_filtering进行个性化筛选
│   └── 返回训练动作列表
├── intent in ["daily_workout_plan", "weekly_workout_plan"] → handle_training_plan_intent
│   ├── 获取训练参数（body_part, scenario, plan_type等）
│   ├── 根据计划类型选择提示模板
│   ├── 调用TrainingPlanService.generate_training_plan
│   ├── 设置related_plan_id和active_flow
│   └── 返回格式化的训练计划
├── intent in ["nutrition_advice", "diet_suggestion", "macro_calculation"] → handle_fitness_advice_intent
│   ├── 获取用户资料
│   ├── 构建提示上下文
│   ├── 尝试使用agent-app模型
│   └── 如果失败，回退到fitness_advice模型
├── intent == "discuss_training_plan" → _handle_discuss_training_plan_intent
│   ├── 获取关联计划ID
│   ├── 加载训练计划详情
│   ├── 格式化计划摘要
│   ├── 调用LLM生成回复
│   └── 管理活跃流程状态
└── 其他 → handle_general_chat
    ├── 获取用户资料和对话历史
    ├── 构建系统提示
    ├── 使用conversation_model生成回复
    └── 流式返回回复
```

### 中断处理流程

```
process_message_stream
├── 检测中断条件
│   ├── 计算时间间隔 > 阈值
│   ├── 检查是否处于特殊状态
│   └── 调用_check_message_relevance判断相关性
├── 中断处理
│   ├── 设置confirming_continuation = true
│   ├── 保存pending_new_message
│   ├── 询问用户是否继续
│   └── 等待用户响应
└── 中断恢复
    ├── 调用_analyze_continuation_response分析用户选择
    ├── 如果继续：恢复原始状态，重新发送询问
    └── 如果处理新问题：清除状态，处理新消息
```

## 自动化与智能化

针对不同场景，系统实现了以下自动化和智能化机制：

1. **训练计划上下文感知**：
   - 当用户询问"第一个动作是什么"等问题时，系统通过`IntentRecognizer`自动识别为`discuss_training_plan`意图
   - 通过`response_meta_info["related_plan_id"]`自动关联到之前生成的训练计划
   - 无需用户明确指出是在讨论哪个计划，系统能够维持上下文连续性

2. **参数智能提取与补全**：
   - 使用`ParameterExtractor._extract_parameters_with_llm`从自然语言中提取结构化参数
   - 通过`TrainingParamManager`自动补全缺失参数，如从用户历史中获取默认训练场景偏好
   - 根据用户健身目标（如增肌、减脂）自动调整训练计划参数（如组数、次数、休息时间）
   - 根据用户经验水平自动调整训练难度和复杂度

3. **状态管理与对话连贯性**：
   - 通过`ConversationStateManager`实现状态模式，自动管理不同对话状态间的转换
   - 使用`_check_message_relevance`和`_analyze_continuation_response`智能处理对话中断
   - 通过`response_meta_info`持久化会话状态，确保会话连贯性
   - 在参数收集、用户信息收集和正常对话状态间平滑过渡

4. **个性化响应生成**：
   - 通过`CharacterManager`根据用户偏好调整回复风格（如专业型、鼓励型）
   - 使用`_get_user_profile_text`将用户信息融入提示，生成个性化回复
   - 在健身建议中考虑用户的健康状况、限制和目标
   - 为训练计划生成个性化的说明和建议

5. **智能错误处理与恢复**：
   - 当LLM调用失败时，实现模型回退机制（如从`agent-app`回退到`fitness_advice`）
   - 在参数验证失败时提供清晰的错误提示和纠正建议
   - 使用`try-except`块捕获各种异常，确保对话流程不会中断
   - 记录详细日志，便于调试和分析

6. **模块化设计与扩展性**：
   - 通过专门的管理器类（如`UserProfileManager`、`TrainingParamManager`）实现关注点分离
   - 使用`IntentHandler`实现意图处理的分发机制，便于添加新的意图处理逻辑
   - 通过`ToolRegistrar`注册和管理Agent工具，支持动态扩展系统能力
   - 使用异步编程模式（`async/await`）提高系统响应性
