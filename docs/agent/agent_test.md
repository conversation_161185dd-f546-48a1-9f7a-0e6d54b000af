# AI 智能健身助手：功能与模型集成测试计划

## 1. 引言

本文档为 AI 智能健身助手提供了一套全面的测试计划，旨在确保其核心功能流程的正确性、稳定性以及与大语言模型（LLM）集成的有效性。测试将分两个主要阶段进行：

1.  **功能流程测试 (基于模拟 LLM 响应)**：此阶段专注于验证对话逻辑、状态管理、信息收集、参数提取和意图路由等核心流程，通过模拟 LLM 的输出来隔离外部依赖，确保内部逻辑的健壮性。
2.  **真实模型调用测试 (集成测试)**：在功能流程得到充分验证的基础上，集成真实的 LLM 调用，测试系统在实际环境下的表现，包括意图识别准确率、响应质量、Agent 工具使用等。

本文档的测试设计基于对 `app/services/conversation/orchestrator.py` 核心服务及其依赖组件的分析，并参考了 `docs/agent/agent.md`、`docs/agent/AI stage2.md` 和 `docs/agent/agent_flow.md`。

本测试计划最后更新于2023年12月，反映了当前系统的实现状态。

## 2. 测试环境与准备

-   **数据库**: 准备一个独立的测试数据库，包含必要的测试用户数据、运动数据等。使用`alembic`迁移脚本创建测试数据库架构。
-   **模拟 LLM 服务/模块**: 开发`MockLLMProxyService`类，用于模拟LLM响应。该模块应能根据输入的prompt类型或特定标记，返回预设的、符合预期的响应数据（包括文本、意图、参数、结构化数据等）。
-   **模拟管理器组件**: 为关键管理器组件（如`IntentRecognizer`、`UserProfileManager`、`TrainingParamManager`等）创建模拟版本，以便隔离测试特定组件。
-   **API 测试工具**: 使用Postman、Insomnia或自定义Python脚本（使用`httpx`或`requests`）进行API测试。
-   **WebSocket 测试工具**: 使用Postman（支持WebSocket）或专门的WebSocket客户端工具测试流式响应。
-   **测试配置文件**: 创建专门的测试配置文件（`.env.test`），配置测试环境变量，包括测试数据库连接、模拟LLM服务等。
-   **日志配置**: 确保应用日志级别设置为DEBUG，以便追踪详细的执行流程。配置日志输出到专门的测试日志文件。
-   **测试数据生成器**: 开发测试数据生成器，用于创建各种测试场景所需的用户数据、训练计划数据、运动数据等。

## 3. 阶段一：功能流程测试 (基于模拟 LLM 响应)

此阶段的核心目标是验证 `ConversationService` 内部的逻辑流程，而不依赖真实 LLM 的不确定性。

### 3.1. 测试范围

-   **会话创建与管理**：测试会话初始化、元数据管理和会话状态持久化
-   **用户消息处理入口**：测试`process_message_stream`方法的核心流程
-   **状态管理**：测试`ConversationStateManager`的状态转换逻辑
-   **中断处理与恢复机制**：测试中断检测、相关性判断和流程恢复
-   **用户信息收集流程**：测试`_handle_user_profile_collection`和`UserProfileManager`的交互
-   **训练参数收集流程**：测试`_handle_training_param_collection`和`TrainingParamManager`的交互
-   **意图识别与路由**：测试`IntentRecognizer`的模拟和意图路由逻辑
-   **参数提取**：测试`ParameterExtractor`的参数提取和验证逻辑
-   **用户信息完整性检查**：测试用户信息验证和缺失字段收集流程
-   **意图执行**：测试`IntentHandler`的意图分发和处理逻辑
-   **训练计划生成**：测试`TrainingPlanManager`和`TrainingPlanService`的交互
-   **Agent工具调用**：测试Agent的初始化和工具调用逻辑
-   **响应生成**：测试不同类型响应（文本、结构化数据）的生成
-   **状态更新**：测试元数据更新和持久化
-   **流式响应**：测试异步生成器和流式响应的正确性

### 3.2. 模拟 LLM 响应设计

为了有效测试系统逻辑而不依赖真实LLM的不确定性，需要开发`MockLLMProxyService`类，针对不同的LLM调用点设计模拟响应：

-   **意图识别 (`IntentRecognizer`)**:
    *   创建`MockIntentRecognizer`类，根据输入消息和上下文返回预设的`IntentData`对象
    *   例：输入"我想练胸肌" -> 模拟返回`IntentData(intent="recommend_exercise", confidence=0.95, parameters={"body_part": ["胸部"]})`
    *   支持上下文感知的模拟，如当上下文包含`related_plan_id`时，优先识别为`discuss_training_plan`意图

-   **参数提取 (`ParameterExtractor`, `TrainingParamManager`)**:
    *   创建`MockParameterExtractor`类，根据输入消息和当前上下文返回预设的参数字典
    *   支持两种提取模式的模拟：LLM提取和关键词匹配
    *   例：在收集训练场景时，输入"在家里" -> 模拟返回`{"scenario": "home"}`
    *   支持模拟参数提取失败的情况，返回空字典或部分参数

-   **信息校验/解析 (`UserProfileManager`)**:
    *   创建`MockUserProfileManager`类，根据输入和当前字段返回`(is_valid, parsed_value)`
    *   例：在收集身高时，输入"175cm" -> 模拟返回`(True, 175)`
    *   支持模拟验证失败的情况，返回`(False, None)`和适当的错误消息

-   **询问消息生成**:
    *   为`UserProfileManager.generate_query_message`和`TrainingParamManager.get_training_prompt_message`创建模拟方法
    *   返回预设的询问文本，如"请告诉我您的身高是？"或"您想在哪里训练？(居家/健身房)"

-   **中断处理相关判断**:
    *   为`_check_message_relevance`创建模拟方法，根据输入消息和当前流程返回`True`或`False`
    *   为`_analyze_continuation_response`创建模拟方法，根据用户回复判断是继续原流程还是处理新问题

-   **Agent执行 (`AgentExecutor`)**:
    *   创建`MockAgentExecutor`类，模拟Agent的工具调用和响应生成
    *   支持模拟SQL工具调用，返回预设的查询结果
    *   支持模拟最终答案生成，返回格式化的回复

-   **意图处理 (`IntentHandler`)**:
    *   创建`MockIntentHandler`类，根据意图类型返回预设的处理结果
    *   支持模拟不同类型意图的处理逻辑，如训练计划生成、动作推荐、健身建议等

-   **训练计划生成 (`TrainingPlanService`)**:
    *   创建`MockTrainingPlanService`类，返回预设的结构化训练计划数据
    *   支持不同类型的计划模板（单日、周期）和不同的训练参数组合

### 3.3. 测试用例设计

#### 3.3.1. 会话与消息基础测试

| 用例ID   | 描述                                   | 前置条件                      | 操作步骤                                     | 预期结果 (模拟LLM)                                                                                                | 状态元数据检查 (`response_meta_info`) | WebSocket 流检查                     |
| :------- | :------------------------------------- | :---------------------------- | :------------------------------------------- | :---------------------------------------------------------------------------------------------------------------- | :--------------------------------- | :----------------------------------- |
| TC_FUNC_001 | 新用户首次消息 - 创建会话并返回通用回复 | 无                            | POST `/message` (无 `session_id`) "你好"       | 模拟 `IntentRecognizer` 返回 `general_chat`，模拟LLM返回 "你好，有什么可以帮您？"                                       | `intent="general_chat"`          | 接收到完整回复                       |
| TC_FUNC_002 | 已有会话 - 继续对话                   | TC_FUNC_001 完成，获得 `session_id` | POST `/message` (带 `session_id`) "今天天气不错" | 模拟 `IntentRecognizer` 返回 `general_chat`，模拟LLM返回 "是的，天气很好。"                                       | `intent="general_chat"`          | 接收到完整回复                       |
| TC_FUNC_003 | 会话状态管理 - 验证状态转换            | 用户信息完整                   | 1. POST "我想练胸肌" <br> 2. POST "在健身房" | 1. 模拟`IntentRecognizer`返回`recommend_exercise`，模拟`ParameterExtractor`提取`body_part="胸部"`但缺少`scenario` <br> 2. 模拟参数收集完成，返回推荐动作 | 1. `collecting_training_params=true`, `asking_param="scenario"` <br> 2. `collecting_training_params=false`, `intent="recommend_exercise"` | 接收到参数询问和最终推荐 |
| TC_FUNC_004 | 角色风格测试 - 验证不同角色风格         | 用户信息完整                   | 1. 设置`character_type="motivational"` <br> 2. POST "我今天不想训练" | 模拟`CharacterManager`返回鼓励风格的回复："坚持就是胜利！即使只做一点点也比不做好，我相信你可以的！" | `character_type="motivational"` | 接收到鼓励风格的回复 |
| TC_FUNC_005 | 会话历史加载 - 验证历史消息限制         | 已有10条以上历史消息           | POST "继续我们的对话" | 模拟`LLMProxyService`接收到的历史消息数量不超过`HISTORY_MESSAGE_LIMIT`常量值 | `intent="general_chat"` | 接收到考虑了有限历史的回复 |

#### 3.3.2. 用户信息收集流程 (`UserProfileManager`)

| 用例ID   | 描述                                       | 前置条件                                  | 操作步骤 (按顺序)                                                                 | 预期结果 (模拟LLM)                                                                                                                               | 状态元数据检查                                                                                               | WebSocket 流检查                     |
| :------- | :----------------------------------------- | :---------------------------------------- | :-------------------------------------------------------------------------------- | :----------------------------------------------------------------------------------------------------------------------------------------------- | :--------------------------------------------------------------------------------------------------------- | :----------------------------------- |
| TC_FUNC_010 | 触发信息收集 (如请求训练计划) - 逐步提供有效信息 | 用户数据不完整                             | 1. POST "我要训练计划" (模拟 `IntentRecognizer` 返回 `daily_workout_plan`) <br> 2. POST (回复询问) "175cm" <br> 3. POST (回复询问) "70kg" <br> 4. POST (回复询问) "男" <br> 5. POST (回复询问) "30岁" <br> 6. POST (回复询问) "增肌" | 1. AI: "为了给您制定合适的训练计划，我需要了解您的一些基本信息。请问您的身高是？" <br> 2. AI: "好的，您的体重是？" <br> 3. AI: "您的性别是？" <br> 4. AI: "您的年龄是？" <br> 5. AI: "您的健身目标是？(增肌/减脂/塑形/增强体能)" <br> 6. AI: "感谢您提供的信息，现在我可以为您生成训练计划了..." | `waiting_for_info` 字段按预期变化 (`field`, `display_name`, `is_retry`)，`pending_request` 包含原始请求，`session_state="guided"` 后转为 `"normal"` | 逐步接收到询问和确认消息             |
| TC_FUNC_011 | 信息收集 - 提供无效信息后重试并成功          | TC_FUNC_010 开始，AI询问身高             | 1. POST (回复身高) "不知道" <br> 2. POST (回复重试询问) "180厘米"                     | 1. AI: "抱歉，我无法识别您的输入。请以数字形式告诉我您的身高（厘米）？" <br> 2. AI: "好的，您的体重是？"                                                                              | `waiting_for_info.is_retry=true` 然后重置为`false`，`waiting_for_info.field`更新为下一字段                                                                  | 接收到重试询问和后续询问             |
| TC_FUNC_012 | 信息收集 - 多次无效信息后跳过该字段        | TC_FUNC_010 开始，AI询问身高             | 1. POST (回复身高) "不告诉你" <br> 2. POST (回复重试询问) "还是不告诉你" <br> 3. POST (回复第二次重试询问) "保密"                | 1. AI: "抱歉，我无法识别您的输入。请以数字形式告诉我您的身高（厘米）？" <br> 2. AI: "我仍然无法理解您的输入。请再试一次，或者输入'跳过'来跳过这个问题。" <br> 3. AI: "由于无法获取您的身高信息，我将暂时跳过这个问题。您的体重是？"                     | `waiting_for_info.is_retry`从`false`变为`true`再变为`true`，然后`waiting_for_info.field`更新为下一字段，`skipped_fields`包含"height"        | 接收到重试询问、第二次重试询问和跳过提示             |
| TC_FUNC_013 | 信息收集完成 - 恢复待处理请求              | TC_FUNC_010 中用户信息已完整，存在 `pending_request` | (信息收集最后一步完成)                                                              | AI: "感谢您提供的信息，现在我可以为您生成训练计划了..." (然后执行原`pending_request`的意图，生成训练计划)                                                                      | `waiting_for_info`被清除，`pending_request`被处理并清除，`session_state`从"guided"变为"normal"                                                                                 | 接收到确认消息和训练计划       |
| TC_FUNC_014 | 用户信息更新 - 已有用户更新单个字段        | 用户数据已存在但需要更新                  | 1. POST "我现在体重75公斤了" <br> 2. POST (回复确认) "是的"                          | 1. AI: "您是想更新您的体重信息为75公斤吗？" <br> 2. AI: "好的，我已经更新了您的体重信息。有什么可以帮您的吗？"                                                                      | 不触发完整的`waiting_for_info`流程，但会更新用户数据库记录                                                                                 | 接收到确认询问和更新确认       |

#### 3.3.3. 训练参数收集流程 (`TrainingParamManager`)

| 用例ID   | 描述                                       | 前置条件                                     | 操作步骤 (按顺序)                                                                   | 预期结果 (模拟LLM)                                                                                                                                 | 状态元数据检查                                                                                                     | WebSocket 流检查                     |
| :------- | :----------------------------------------- | :------------------------------------------- | :---------------------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------- | :--------------------------------------------------------------------------------------------------------------- | :----------------------------------- |
| TC_FUNC_020 | 触发参数收集 (请求特定部位动作) - 逐步提供有效参数 | 用户信息完整                                  | 1. POST "推荐一些练胸的动作" (模拟 `IntentRecognizer` 返回 `recommend_exercise`) <br> 2. POST (回复询问) "健身房" <br> 3. POST (回复询问) "单日计划" | 1. AI: "好的，您想在哪里训练？(居家/健身房)" (模拟 `TrainingParamManager` 判断缺少 `scenario`) <br> 2. AI: "您想要单日计划还是周期计划？" <br> 3. AI: "好的，这是为您推荐的健身房胸部单日训练动作..." | `collecting_training_params=true`, `asking_param` 按预期变化 (`scenario`, `plan_type`), `training_params` 被逐步填充, 最后 `collecting_training_params=false` | 逐步接收到询问和最终推荐             |
| TC_FUNC_021 | 参数收集中 - 用户一次性提供了部分或全部参数     | 用户信息完整                                  | POST "我想在健身房练腿，给我个今天的安排" (模拟 `IntentRecognizer` 返回 `daily_workout_plan`，模拟 `ParameterExtractor` 提取到 `body_part="腿部"`, `scenario="gym"`, `plan_type="daily"`) | AI: "好的，这是为您推荐的健身房腿部单日训练计划..." (模拟`TrainingPlanManager`生成训练计划)                                                                                                | `training_params` 直接被填充 (`body_part`, `scenario`, `plan_type`), `collecting_training_params` 不触发                                             | 直接接收到训练计划                   |
| TC_FUNC_022 | 参数收集 - 无法识别身体部位                | 用户信息完整                                  | 1. POST "我想练个那个...就是那个..." (模拟 `IntentRecognizer` 返回 `recommend_exercise`) <br> 2. POST (回复询问) "嗯...手臂吧" | 1. AI: "抱歉，我无法确定您想训练的身体部位。请选择以下部位之一：胸部、背部、腿部、手臂、肩部、核心..." <br> 2. AI: "好的，您想在哪里训练手臂？(居家/健身房)"                                                                      | 1. `collecting_training_params=true`, `asking_param="body_part"` <br> 2. `training_params.body_part="手臂"`, `asking_param="scenario"`                                     | 接收到部位选择提示和后续参数询问     |
| TC_FUNC_023 | 参数收集 - LLM参数提取失败后使用关键词匹配  | 用户信息完整                                  | POST "我想在家里练背部" (模拟 `ParameterExtractor._extract_parameters_with_llm` 失败，但 `_extract_parameters_with_keywords` 成功) | AI: "好的，您想要单日计划还是周期计划？" (模拟 `TrainingParamManager` 已提取 `body_part="背部"` 和 `scenario="home"` 但缺少 `plan_type`)                                                                      | `training_params.body_part="背部"`, `training_params.scenario="home"`, `asking_param="plan_type"`                                     | 接收到计划类型询问     |
| TC_FUNC_024 | 参数收集 - 用户提供多个参数的复杂回复      | 用户信息完整                                  | 1. POST "推荐一些动作" (模拟 `IntentRecognizer` 返回 `recommend_exercise`) <br> 2. POST (回复询问) "我想在健身房练胸和背，主要是增肌" | 1. AI: "您想训练哪个部位？" <br> 2. AI: "好的，您想要单日计划还是周期计划？" (模拟 `ParameterExtractor` 从复杂回复中提取多个参数: `body_part=["胸部", "背部"]`, `scenario="gym"`, `training_goal="增肌"`)                                                                      | `training_params` 包含多个提取的参数, `asking_param="plan_type"`                                     | 接收到计划类型询问     |

#### 3.3.4. 中断处理与恢复流程

| 用例ID   | 描述                                            | 前置条件                                                              | 操作步骤 (按顺序)                                                                                                | 预期结果 (模拟LLM)                                                                                                                                                           | 状态元数据检查                                                                                                      | WebSocket 流检查                           |
| :------- | :---------------------------------------------- | :-------------------------------------------------------------------- | :------------------------------------------------------------------------------------------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------------------------------------- | :--------------------------------------- |
| TC_FUNC_030 | 信息收集中断 (超时) - 新消息不相关 - 用户选择继续原流程 | AI 已询问身高，超过60秒未回复                                          | 1. POST "今天天气怎么样？" (模拟 `_check_message_relevance` 返回 `False`) <br> 2. POST "继续问吧" (模拟 `_analyze_continuation_response` 返回 `True`) | 1. AI: "您好，我们之前正在收集您的个人健身信息。请问您是想继续这个流程，还是希望我先回答您刚才的问题？" <br> 2. AI: "好的，我们继续之前的流程。请告诉我您的身高是？"                                                 | `confirming_continuation=true` 然后 `false`, `pending_new_message` 被设置然后清除, `waiting_for_info` 恢复 | 接收到确认继续的询问，然后是原流程的询问 |
| TC_FUNC_031 | 信息收集中断 (超时) - 新消息不相关 - 用户选择处理新问题 | AI 已询问身高，超过60秒未回复                                          | 1. POST "今天天气怎么样？" (模拟 `_check_message_relevance` 返回 `False`) <br> 2. POST "回答新问题" (模拟 `_analyze_continuation_response` 返回 `False`) | 1. AI: "您好，我们之前正在收集您的个人健身信息。请问您是想继续这个流程，还是希望我先回答您刚才的问题？" <br> 2. AI: "好的，我将回答您的新问题。今天天气确实不错！" (模拟 `IntentRecognizer` 返回 `general_chat`)            | `confirming_continuation` 清除, `waiting_for_info` 等原流程状态清除, `intent="general_chat"`                          | 接收到确认继续的询问，然后是新问题的回复   |
| TC_FUNC_032 | 信息收集中断 (超时) - 新消息相关                 | AI 已询问身高，超过60秒未回复                                          | POST "我身高1米8" (模拟 `_check_message_relevance` 返回 `True`)                                                              | AI: "好的，您的体重是？" (流程正常继续)                                                                                                                                                 | `waiting_for_info` 等状态从上一轮 AI 消息的元数据中恢复并正常流转                                                | 接收到原流程的下一个询问                 |
| TC_FUNC_033 | 参数收集中断 (超时) - 新消息不相关 - 用户选择继续原流程 | AI 已询问训练场景，超过60秒未回复                                          | 1. POST "你能告诉我一些健身知识吗？" (模拟 `_check_message_relevance` 返回 `False`) <br> 2. POST "继续之前的问题" (模拟 `_analyze_continuation_response` 返回 `True`) | 1. AI: "您好，我们之前正在确定您的训练参数。请问您是想继续这个流程，还是希望我先回答您刚才的问题？" <br> 2. AI: "好的，我们继续之前的流程。您想在哪里训练？(居家/健身房)"                                                 | `confirming_continuation=true` 然后 `false`, `collecting_training_params=true` 恢复, `asking_param="scenario"` | 接收到确认继续的询问，然后是原流程的询问 |
| TC_FUNC_034 | 训练计划讨论中断 (超时) - 新消息相关              | AI 已生成训练计划，`active_flow="training_plan_discussion"`，超过60秒未回复 | POST "这个计划的第一天是练什么的？" (模拟 `_check_message_relevance` 返回 `True`)                                                              | AI: "第一天是胸部和肱三头肌训练，包括..." (流程正常继续，模拟 `IntentRecognizer` 返回 `discuss_training_plan`)                                                                                                                                                 | `active_flow="training_plan_discussion"` 保持不变, `intent="discuss_training_plan"`                                                | 接收到计划详情的回复                 |

#### 3.3.5. 意图处理与 Agent 调用 (模拟)

| 用例ID   | 描述                                     | 前置条件                                             | 操作步骤                                      | 预期结果 (模拟LLM)                                                                                                                                | 状态元数据检查                                                                     | WebSocket 流检查                     |
| :------- | :--------------------------------------- | :--------------------------------------------------- | :-------------------------------------------- | :------------------------------------------------------------------------------------------------------------------------------------------------ | :------------------------------------------------------------------------------- | :----------------------------------- |
| TC_FUNC_040 | 健身建议意图 (无需工具)                    | 用户信息完整                                         | POST "我应该怎么开始健身？" (模拟 `IntentRecognizer` 返回 `fitness_qa`) | AI: "初学者可以从每周3次全身抗阻训练开始，配合有氧运动..." (模拟 `IntentHandler.handle_fitness_advice_intent` 调用LLM生成的回复)                                            | `intent="fitness_qa"`                                                    | 接收到完整建议                       |
| TC_FUNC_041 | 营养建议意图 (无需工具)                    | 用户信息完整                                         | POST "增肌期间应该怎么吃？" (模拟 `IntentRecognizer` 返回 `nutrition_advice`) | AI: "增肌期间应保持适度的热量盈余，增加蛋白质摄入..." (模拟 `IntentHandler.handle_fitness_advice_intent` 调用LLM生成的回复)                                            | `intent="nutrition_advice"`                                                    | 接收到完整建议                       |
| TC_FUNC_042 | 训练动作查询意图 (需要 Agent SQLTool)    | 用户信息完整, 数据库中有 "深蹲" 动作                 | POST "深蹲怎么做？" (模拟 `IntentRecognizer` 返回 `search_exercise`, 模拟 Agent 调用 `SQLToolService` 查询数据库并返回动作描述) | AI: "深蹲是一种复合训练动作，主要锻炼大腿和臀部肌肉。执行时注意..." (模拟 `IntentHandler.handle_exercise_intent` 调用Agent整合工具结果后的回复)                                                                 | `intent="search_exercise"`, `identified_exercise="深蹲"`                               | 接收到动作描述                       |
| TC_FUNC_043 | 训练动作推荐意图 (需要 Agent SQLTool)    | 用户信息完整                                         | POST "推荐一些练胸的动作" (模拟 `IntentRecognizer` 返回 `recommend_exercise`, 训练参数已完整) | AI: "以下是几个有效的胸部训练动作：1. 卧推：..." (模拟 `IntentHandler.handle_exercise_intent` 调用Agent查询并筛选动作后的回复)                                                                 | `intent="recommend_exercise"`, `training_params.body_part="胸部"`                               | 接收到动作列表                       |
| TC_FUNC_044 | 生成单日训练计划意图 (调用 `TrainingPlanService` 模拟) | 用户及训练参数完整 (`body_part="胸部"`, `scenario="gym"`, `plan_type="daily"`) | POST "给我来个今天的胸部训练" (模拟 `IntentRecognizer` 返回 `daily_workout_plan`)  | 模拟 `IntentHandler.handle_training_plan_intent` 调用 `TrainingPlanService` 后，返回结构化的单日训练计划 JSON 和一段引导文本。例如：AI: "好的，这是您的单日胸部训练计划：..." + JSON | `intent="daily_workout_plan"`, `related_plan_id` 被设置为新计划ID, `active_flow="training_plan_discussion"` | 接收到引导文本和计划JSON数据         |
| TC_FUNC_045 | 生成周期训练计划意图 (调用 `TrainingPlanService` 模拟) | 用户及训练参数完整 (`body_part="全身"`, `scenario="gym"`, `plan_type="weekly"`) | POST "给我制定一个一周的训练计划" (模拟 `IntentRecognizer` 返回 `weekly_workout_plan`)  | 模拟 `IntentHandler.handle_training_plan_intent` 调用 `TrainingPlanService` 后，返回结构化的周期训练计划 JSON 和一段引导文本。例如：AI: "好的，这是您的一周训练计划：..." + JSON | `intent="weekly_workout_plan"`, `related_plan_id` 被设置为新计划ID, `active_flow="training_plan_discussion"` | 接收到引导文本和计划JSON数据         |
| TC_FUNC_046 | 讨论训练计划意图 (已有计划) | 用户信息完整，已有训练计划，`related_plan_id` 非空 | POST "这个计划的第一天是练什么的？" (模拟 `IntentRecognizer` 返回 `discuss_training_plan`)  | 模拟 `IntentHandler._handle_discuss_training_plan_intent` 加载计划详情并回复："第一天是胸部和肱三头肌训练，包括..." | `intent="discuss_training_plan"`, `active_flow="training_plan_discussion"` | 接收到计划详情的回复         |
| TC_FUNC_047 | 一般聊天意图 (无需工具) | 用户信息完整 | POST "你好，今天过得怎么样？" (模拟 `IntentRecognizer` 返回 `general_chat`)  | 模拟 `IntentHandler.handle_general_chat` 调用LLM生成回复："我是AI助手，没有感受，但我很乐意帮助您！您今天有什么健身相关的问题吗？" | `intent="general_chat"` | 接收到聊天回复         |

### 3.4. 执行与结果记录

-   对每个测试用例，详细记录：
    -   **测试环境**：测试数据库状态、模拟组件配置、测试时间等
    -   **输入数据**：用户消息内容、初始元数据、会话状态等
    -   **模拟配置**：
        -   `MockIntentRecognizer`的预期返回值
        -   `MockParameterExtractor`的预期参数提取结果
        -   `MockUserProfileManager`的预期验证结果
        -   其他模拟组件的配置
    -   **实际输出**：
        -   通过WebSocket或API接收到的所有文本片段
        -   元数据更新序列（每次更新的内容和时间）
        -   最终的会话元数据（`conversation.meta_info`存储在数据库中的值）
    -   **结果分析**：
        -   与预期结果的差异
        -   状态转换是否符合预期
        -   响应内容是否符合预期
        -   响应时间是否在可接受范围内
    -   **问题记录**：
        -   发现的缺陷/问题
        -   可能的原因分析
        -   建议的修复方案
    -   **测试覆盖率**：
        -   代码覆盖率报告（如果可用）
        -   未覆盖的代码路径分析

-   使用标准化的测试报告模板，包含以下部分：
    -   测试用例ID和描述
    -   测试日期和执行人
    -   测试环境配置
    -   测试步骤详细记录
    -   测试结果（通过/失败）
    -   详细的输出日志
    -   问题追踪链接（如果有）

-   对于失败的测试用例，创建详细的问题报告，包含：
    -   问题描述
    -   复现步骤
    -   预期行为vs实际行为
    -   相关日志和截图
    -   可能的根本原因分析

## 4. 阶段二：真实模型调用测试 (集成测试)

此阶段在功能流程基本稳定的前提下进行，将模拟LLM替换为真实的LLM服务调用，验证系统在实际环境中的表现。

### 4.1. 测试范围

-   **意图识别与参数提取**：
    -   `IntentRecognizer`使用真实`intent-recognition-app`模型的意图识别准确率
    -   `ParameterExtractor`使用真实LLM进行参数提取的能力
    -   上下文感知的意图识别效果

-   **用户信息处理**：
    -   `UserProfileManager`使用真实LLM进行信息解析和验证的效果
    -   自然语言输入的解析准确性（如"一米七五"→175cm）
    -   多种表达方式的处理能力

-   **中断处理判断**：
    -   `_check_message_relevance`使用真实LLM判断消息相关性的准确性
    -   `_analyze_continuation_response`使用真实LLM分析用户选择的准确性
    -   复杂或模糊回复的处理能力

-   **Agent工具调用**：
    -   LangChain Agent (`AgentExecutor`)使用真实LLM的工具调用能力
    -   SQL查询工具的使用准确性
    -   工具调用结果的整合质量

-   **训练计划生成**：
    -   `TrainingPlanService`使用真实`LLM_EXERCISE_GENERATION_MODEL`生成计划的质量
    -   结构化输出的准确性和一致性
    -   计划内容的专业性和合理性

-   **对话质量**：
    -   整体对话的流畅性、自然性和相关性
    -   连续多轮对话的上下文维护能力
    -   复杂问题的理解和回答质量

-   **个性化与风格**：
    -   `CharacterManager`配置下的回复风格一致性
    -   不同角色类型（如专业型、鼓励型）的表现差异
    -   用户偏好的适应能力

### 4.2. 测试策略

-   **基于场景的端到端测试**：设计覆盖主要用户场景的完整对话脚本。
    -   **场景1: 新用户入门**
        -   新用户首次咨询
        -   完成用户信息收集
        -   获得初学者健身建议
        -   生成适合初学者的训练计划

    -   **场景2: 特定训练需求**
        -   老用户请求特定部位的训练动作
        -   根据场景（居家/健身房）推荐不同动作
        -   询问动作细节和替代方案
        -   讨论训练技巧和注意事项

    -   **场景3: 训练计划生成与讨论**
        -   用户请求生成一周的健身房增肌训练计划
        -   讨论计划细节和调整建议
        -   询问特定训练日的内容
        -   请求修改计划中的某些部分

    -   **场景4: 训练执行与反馈**
        -   用户在训练计划执行过程中提问
        -   报告训练中遇到的困难
        -   请求调整训练强度或动作
        -   讨论训练后的恢复策略

    -   **场景5: 中断处理与恢复**
        -   对话被意外打断（用户长时间未回复后继续）
        -   用户切换话题后再回到原话题
        -   在信息收集过程中中断并恢复
        -   在参数收集过程中中断并恢复

    -   **场景6: 复杂输入处理**
        -   用户提供模糊不清或有歧义的输入
        -   用户一次性提供多个信息或参数
        -   用户使用非标准表达（方言、简写、错别字）
        -   用户提出复杂的多部分问题

-   **探索性测试**：
    -   测试人员扮演不同类型的用户（初学者、专业健身爱好者、特殊需求用户等）
    -   自由与AI对话，不遵循预设脚本
    -   记录非预期行为、体验问题和用户困惑点
    -   尝试边界情况和极端输入

-   **特定功能点测试**：
    -   **意图识别鲁棒性测试**：
        -   测试多种不同表述方式对意图识别的影响
        -   测试意图之间的边界情况（如健身建议vs营养建议）
        -   测试上下文对意图识别的影响

    -   **参数提取准确性测试**：
        -   测试不同身体部位、场景、计划类型组合的提取
        -   测试复杂表达中的参数提取（如"我想在家里练胸和背，主要是增肌"）
        -   测试非标准表达的参数提取（如"我想练马甲线"→"腹部"）

    -   **Agent工具调用测试**：
        -   测试SQL查询工具的调用准确性
        -   测试复杂查询条件的构建能力
        -   测试查询结果的处理和整合能力

    -   **训练计划生成质量测试**：
        -   测试不同参数组合下的计划生成质量
        -   测试计划结构的一致性和合理性
        -   测试特殊需求（如伤病恢复、特定目标）的适应能力

### 4.3. 测试用例设计 (示例)

| 用例ID   | 描述                                     | 场景/功能点                     | 操作步骤 (对话脚本片段)                                                                                                | 预期LLM行为/系统响应 (关键点)                                                                                                                                                                                                                              | 评估指标                                                                                             |
| :------- | :--------------------------------------- | :------------------------------ | :------------------------------------------------------------------------------------------------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :--------------------------------------------------------------------------------------------------- |
| TC_LLM_001 | 意图识别 - 多种表述方式测试              | 意图识别鲁棒性测试                        | 1. User: "我想练腹肌" <br> 2. User: "有什么练腹部的动作" <br> 3. User: "马甲线怎么练" <br> 4. User: "腹肌训练推荐"                                                                                                     | `IntentRecognizer`应在所有表述方式下准确识别为`recommend_exercise`，并提取参数`body_part="腹部"`或`muscle="腹肌"`。                                                                                                                            | 意图识别准确率≥90%，参数提取准确率≥85%                                                                               |
| TC_LLM_002 | 信息收集 - 多种自然语言表达测试          | 用户信息收集                    | 1. AI: "请问您的身高是？" <br> User: "我大概一米七五吧" <br> 2. AI: "好的，那体重呢？" <br> User: "体重是150斤左右" <br> 3. AI: "您的性别是？" <br> User: "男生" <br> 4. AI: "您的年龄是？" <br> User: "30出头"                | `UserProfileManager`应能正确解析各种表达：<br> - "一米七五"→175cm <br> - "150斤"→75kg <br> - "男生"→"male" <br> - "30出头"→约31-33岁                                                                                                                                                                       | 信息解析准确率≥90%，单位转换正确性≥95%                                                                             |
| TC_LLM_003 | 训练计划生成 - 专业性与个性化测试        | 训练计划生成质量测试                    | 1. (已完成信息收集：30岁男性，175cm，75kg，中级健身水平，增肌目标) <br> 2. User: "给我制定一个健身房的胸背训练计划" <br> 3. (收到计划后) User: "我肩膀有点旧伤，能调整一下吗？"                                          | 1. `TrainingPlanService`应生成结构合理的训练计划，包含：<br> - 适合中级水平的胸背训练动作 <br> - 合理的组数、次数和重量建议 <br> - 科学的训练顺序和休息时间 <br> 2. 系统应能根据肩伤信息调整计划，避免加重肩部负担的动作                                                                                                      | 计划专业性评分≥4/5，个性化程度≥4/5，结构准确性100%                                                                           |
| TC_LLM_004 | 复杂参数提取 - 一次性提供多个参数测试    | 参数提取准确性测试               | User: "我想在家里做一个针对手臂和肩膀的训练，主要是增肌，我是初学者"                                                                          | `ParameterExtractor`应能从单条消息中提取多个参数：<br> - `scenario="home"` <br> - `body_part=["手臂", "肩膀"]` <br> - `training_goal="增肌"` <br> - `difficulty="beginner"`                                                                                                                                                 | 参数提取完整性≥85%，准确率≥90%                                                                               |
| TC_LLM_005 | 中断恢复 - 复杂场景测试   | 中断处理与恢复                        | 1. (用户在参数收集过程中，被询问训练场景时，超过60秒无响应) <br> 2. User: "对了，我想问一下蛋白质应该什么时候补充？" <br> 3. (AI询问是否继续原流程) <br> 4. User: "先回答蛋白质问题，然后我们继续" <br> 5. (AI回答蛋白质问题后) User: "好的，继续之前的问题，我想在健身房训练" | 1. `_check_message_relevance`应判断新消息与原流程不相关 <br> 2. `_analyze_continuation_response`应理解用户想先处理新问题再继续原流程 <br> 3. 系统应先回答营养问题，然后恢复参数收集流程 <br> 4. 参数收集应从中断点（训练场景）继续                                                                             | 相关性判断准确率≥85%，流程恢复准确率≥90%，用户体验评分≥4/5                                                                           |
| TC_LLM_006 | 角色风格 - 不同角色类型对比测试 | 个性化与风格测试 | 1. (配置`CharacterManager`为"专业型") <br> User: "我今天不想练了" <br> 2. (配置`CharacterManager`为"鼓励型") <br> User: "我今天不想练了" <br> 3. (配置`CharacterManager`为"友好型") <br> User: "我今天不想练了"                                                | 系统应根据不同角色配置生成风格明显不同的回复：<br> 1. 专业型："规律训练对达成目标至关重要。即使是轻度活动也比完全休息更有益。" <br> 2. 鼓励型："我理解有时候会感到疲惫！但记住，每一次坚持都是向目标迈进的一步。也许今天可以做些轻松的恢复训练？" <br> 3. 友好型："没关系，休息也是训练的一部分！明天再继续吧，有什么我能帮你的吗？"                                                                                                                                             | 风格差异明显度≥4/5，风格与配置一致性≥90%，回复质量评分≥4/5                                                                                       |
| TC_LLM_007 | Agent工具调用 - 复杂查询测试 | Agent SQL工具使用测试 | User: "帮我找一下适合我的胸肌训练动作，我是初学者，在健身房训练" | 1. Agent应能构建合适的SQL查询，包含多个条件：<br> - 针对胸部的训练动作 <br> - 适合初学者难度 <br> - 健身房场景可执行 <br> 2. 应能处理查询结果，按照合理性排序并提供详细说明 | 工具调用成功率≥95%，查询条件构建准确率≥90%，结果处理质量评分≥4/5 |
| TC_LLM_008 | 多轮对话 - 上下文维护测试 | 对话质量测试 | 1. User: "我想增肌" <br> 2. AI: (提供增肌建议) <br> 3. User: "需要怎么吃？" <br> 4. AI: (提供增肌饮食建议) <br> 5. User: "那训练呢？" <br> 6. AI: (提供增肌训练建议) <br> 7. User: "有什么适合我的动作？" | 系统应在整个对话过程中维持"增肌"这一核心上下文，每次回复都应与增肌目标相关，且能理解省略的主题 | 上下文维护准确率≥90%，回复相关性≥95%，对话连贯性评分≥4/5 |

### 4.4. 执行与结果评估

-   **人工评估**:
    -   **专家评估**：由健身领域专家评估回复内容的专业性和准确性
    -   **用户体验评估**：由测试人员从用户角度评估对话流畅性和自然度
    -   **评分标准**：使用1-5分制评估不同维度（专业性、自然度、相关性、个性化程度等）
    -   **对比评估**：与基准系统（如没有特殊组件的基础版本）进行对比测试

-   **自动化评估**:
    -   **结构验证**：对训练计划JSON等结构化输出进行自动验证
        -   使用JSON Schema验证格式正确性
        -   检查必要字段（如动作名称、组数、次数）的存在性
        -   验证数值范围的合理性（如重量、次数不能为负）
    -   **意图识别准确率**：自动计算测试集上的意图识别准确率
    -   **参数提取准确率**：自动计算参数提取的准确率和完整性
    -   **状态转换正确性**：验证状态转换是否按预期进行

-   **日志与分析**:
    -   **LLM调用分析**：
        -   记录并分析所有LLM调用的输入（prompts）和输出（responses）
        -   识别导致错误或非预期行为的模式
        -   评估提示模板的有效性
    -   **会话流分析**：
        -   分析完整会话流程，识别卡点和中断
        -   评估状态转换的流畅性
        -   识别用户困惑或不满的模式

-   **性能与资源监控**:
    -   **响应时间**：
        -   记录端到端的响应时间
        -   分析不同组件的耗时分布
        -   识别性能瓶颈
    -   **资源消耗**：
        -   监控LLM API的调用次数和token消耗
        -   计算每次会话的平均成本
        -   分析不同意图类型的资源消耗差异
    -   **并发性能**：
        -   测试在多用户并发场景下的系统表现
        -   评估资源扩展需求

-   **持续改进机制**:
    -   建立测试结果反馈循环
    -   根据测试结果优化提示模板
    -   改进参数提取和意图识别算法
    -   优化状态管理逻辑

## 5. 测试总结与报告

在每个测试阶段完成后，应编写详细的测试总结报告，内容包括：

### 5.1. 测试执行概述

-   **测试范围**：详细说明测试覆盖的功能模块和场景
-   **测试环境**：描述测试环境配置，包括软硬件环境、数据库状态、LLM模型版本等
-   **测试周期**：测试开始和结束时间，测试总时长
-   **测试团队**：参与测试的人员及其角色
-   **测试数据**：使用的测试数据集和测试用例数量

### 5.2. 测试结果统计

-   **测试用例执行情况**：
    -   总测试用例数量
    -   通过的测试用例数量和比例
    -   失败的测试用例数量和比例
    -   阻塞的测试用例数量和比例
-   **缺陷统计**：
    -   按严重程度分类的缺陷数量（致命、严重、一般、轻微）
    -   按模块分类的缺陷分布
    -   缺陷密度（每千行代码的缺陷数）
-   **性能指标**：
    -   平均响应时间
    -   95%响应时间
    -   资源消耗统计（内存、CPU、API调用次数、token消耗）

### 5.3. 质量评估

-   **功能完整性**：各功能模块的实现完整度评估
-   **准确性评估**：
    -   意图识别准确率
    -   参数提取准确率
    -   训练计划生成质量评分
    -   回复内容专业性评分
-   **稳定性评估**：
    -   系统崩溃率
    -   异常处理有效性
    -   边界情况处理能力
-   **用户体验评估**：
    -   对话流畅性评分
    -   回复自然度评分
    -   个性化程度评分
    -   整体满意度评分

### 5.4. 缺陷分析

-   **关键缺陷详情**：
    -   缺陷ID和描述
    -   严重程度和优先级
    -   复现步骤
    -   相关日志和截图
    -   根本原因分析
    -   修复建议
-   **缺陷模式分析**：
    -   常见缺陷类型
    -   高发缺陷区域
    -   缺陷根因分类

### 5.5. 改进建议

-   **短期改进项**：
    -   高优先级缺陷修复计划
    -   关键性能瓶颈优化建议
    -   提示模板优化建议
-   **中长期改进项**：
    -   架构优化建议
    -   新功能或增强建议
    -   测试自动化建议
-   **风险评估**：
    -   已知风险及其缓解措施
    -   潜在风险及监控建议

### 5.6. 测试结论

-   **总体质量评估**：系统整体质量状态评估
-   **发布建议**：是否建议发布，以及发布条件
-   **后续测试计划**：下一阶段测试重点和计划

通过以上两个阶段的全面测试和详细报告，可以确保AI智能健身助手的质量，为用户提供可靠、专业和个性化的服务。测试不仅验证系统的功能正确性，还评估了用户体验和专业性，为持续改进提供了数据支持和方向指导。