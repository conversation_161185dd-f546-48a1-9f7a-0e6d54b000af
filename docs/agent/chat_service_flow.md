# AI聊天服务流程图文档

## 1. 消息处理主流程

```mermaid
sequenceDiagram
    participant User
    participant ConversationService
    participant StateManager
    participant IntentHandler
    participant Database

    User->>ConversationService: 发送消息
    ConversationService->>Database: 保存用户消息
    ConversationService->>StateManager: 获取当前状态
    StateManager-->>ConversationService: 返回状态
    ConversationService->>IntentHandler: 处理消息
    IntentHandler-->>ConversationService: 返回响应
    ConversationService->>Database: 保存AI响应
    ConversationService-->>User: 返回响应
```

## 2. 意图识别和处理流程

```mermaid
flowchart TD
    A[用户输入] --> B[意图识别器]
    B --> C{意图类型}
    C -->|训练计划| D[训练计划处理]
    C -->|健身建议| E[建议处理]
    C -->|营养咨询| F[营养处理]
    C -->|一般聊天| G[聊天处理]
    
    D --> H[参数收集]
    H --> I[计划生成]
    I --> J[返回响应]
    
    E --> K[分析用户需求]
    K --> L[生成建议]
    L --> J
    
    F --> M[分析饮食需求]
    M --> N[生成建议]
    N --> J
    
    G --> O[生成回复]
    O --> J
```

## 3. 用户信息收集流程

```mermaid
stateDiagram-v2
    [*] --> 检查信息
    检查信息 --> 需要收集: 发现缺失
    检查信息 --> 完成: 信息完整
    
    需要收集 --> 询问信息
    询问信息 --> 验证输入
    验证输入 --> 更新信息: 有效
    验证输入 --> 重试: 无效
    重试 --> 询问信息: 次数<3
    重试 --> 跳过: 次数>=3
    
    更新信息 --> 检查信息
    跳过 --> 检查信息
    
    完成 --> [*]
```

## 4. 训练参数收集流程

```mermaid
flowchart TD
    A[开始收集] --> B{检查必要参数}
    B -->|缺少身体部位| C[询问身体部位]
    B -->|缺少场景| D[询问训练场景]
    B -->|缺少计划类型| E[询问计划类型]
    
    C --> F{验证输入}
    D --> F
    E --> F
    
    F -->|有效| G[更新参数]
    F -->|无效| H[提供重试]
    
    H -->|重试次数<3| I[重新询问]
    H -->|重试次数>=3| J[使用默认值]
    
    I --> F
    J --> G
    
    G --> K{参数完整?}
    K -->|是| L[开始生成计划]
    K -->|否| B
```

## 5. 状态管理流程

```mermaid
stateDiagram-v2
    [*] --> 正常对话
    正常对话 --> 收集用户信息: 需要用户信息
    正常对话 --> 收集训练参数: 需要训练参数
    正常对话 --> 生成计划: 参数完整
    
    收集用户信息 --> 正常对话: 收集完成
    收集用户信息 --> 收集用户信息: 继续收集
    
    收集训练参数 --> 正常对话: 收集完成
    收集训练参数 --> 收集训练参数: 继续收集
    
    生成计划 --> 正常对话: 计划生成完成
```

## 6. 错误处理流程

```mermaid
flowchart TD
    A[错误发生] --> B{错误类型}
    B -->|输入验证错误| C[提供重试选项]
    B -->|系统错误| D[记录日志]
    B -->|网络错误| E[重试请求]
    
    C --> F[更新状态]
    D --> G[返回友好提示]
    E --> H{重试成功?}
    
    H -->|是| I[继续处理]
    H -->|否| J[降级处理]
    
    F --> K[继续对话]
    G --> K
    I --> K
    J --> K
``` 