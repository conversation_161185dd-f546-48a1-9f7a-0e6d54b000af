# 智能健身教练AI助手系统实现细节

本文档详细说明智能健身教练AI助手系统各核心模块的实现细节，为开发者提供实现指南。

## 1. ConversationService 实现

### 1.1 核心方法

`process_message_stream` 方法是对话服务的核心，以异步生成器形式实现流式响应：

```python
async def process_message_stream(
    self, 
    message: str, 
    session_id: str, 
    user_id: int, 
    quick_intent: Optional[str] = None
) -> AsyncGenerator[Union[str, Dict[str, Any]], None]:
    """处理用户消息并生成流式响应"""
    # 初始化会话和状态
    # 处理中断与恢复
    # 根据状态分发处理
    # 意图识别和执行
    # 生成响应
```

### 1.2 状态驱动处理

基于状态模式实现对话状态管理：

```python
# ConversationStateManager 负责状态管理
class ConversationStateManager:
    def get_state(self, meta_info: Dict[str, Any]) -> ConversationState:
        """根据元信息获取当前状态"""
        if meta_info.get("waiting_for_info"):
            return UserProfileCollectionState(self.service)
        elif meta_info.get("collecting_training_params"):
            return TrainingParamCollectionState(self.service)
        elif meta_info.get("confirming_continuation"):
            return InterruptionConfirmationState(self.service)
        else:
            return NormalConversationState(self.service)

# 状态基类和具体状态类
class ConversationState(ABC):
    @abstractmethod
    async def process_message(self, message: str, response_meta_info: Dict[str, Any]) -> AsyncGenerator[Union[str, Dict[str, Any]], None]:
        """处理当前状态下的消息"""
        pass
```

### 1.3 中断与恢复处理

通过 `InterruptionHandler` 处理对话中断：

```python
class InterruptionHandler:
    async def check_interruption(self, message: str, meta_info: Dict[str, Any]) -> bool:
        """检查是否存在对话中断"""
        # 1. 检查时间差
        # 2. 检查当前状态是否为特殊流程
        # 3. 检查消息相关性
        
    async def analyze_continuation_response(self, message: str) -> bool:
        """分析用户是否确认继续之前的流程"""
        # 分析用户回答中的意向
        # 返回是否继续原流程
```

### 1.4 用户信息收集

`UserProfileManager` 实现用户信息收集与验证：

```python
class UserProfileManager:
    async def get_missing_fields(self, user_id: int, intent: str) -> List[Dict[str, Any]]:
        """获取缺失的用户信息字段"""
        # 根据意图类型确定必要字段
        # 查询当前用户资料
        # 返回缺失字段列表
    
    async def handle_user_profile_collection(self, message: str, waiting_info: Dict[str, Any], user_id: int) -> Tuple[bool, str, Dict[str, Any]]:
        """处理用户对信息收集的回答"""
        # 解析用户回答
        # 验证输入有效性
        # 更新用户资料
        # 返回处理结果和下一步提示
```

### 1.5 训练参数收集

`TrainingParamManager` 实现训练参数收集与验证：

```python
class TrainingParamManager:
    async def extract_training_parameters(self, message: str, intent: str) -> Dict[str, Any]:
        """从用户消息中提取训练参数"""
        # 使用参数提取器提取参数
        # 验证参数有效性
        # 返回提取的参数字典
    
    async def handle_training_param_collection(self, message: str, asking_param: str, current_params: Dict[str, Any]) -> Tuple[bool, Dict[str, Any], str, Optional[str]]:
        """处理用户对参数收集的回答"""
        # 解析用户回答
        # 更新参数字典
        # 检查参数完整性
        # 返回处理结果和下一步提示
```

## 2. 意图识别与处理

### 2.1 意图识别

`IntentRecognizer` 实现用户意图识别：

```python
class IntentRecognizer:
    async def recognize_intent(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """识别用户意图"""
        # 构建提示模板
        # 添加上下文信息
        # 调用LLM识别意图
        # 解析LLM响应
        # 返回意图和置信度
```

### 2.2 意图处理

`IntentHandler` 实现不同意图的处理逻辑：

```python
class IntentHandler:
    async def handle_intent(self, intent: str, message: str, user_id: int, session_id: str, response_meta_info: Dict[str, Any]) -> AsyncGenerator[Union[str, Dict[str, Any]], None]:
        """根据意图类型分发处理"""
        if intent == "training_plan":
            yield from self.handle_training_plan_intent(message, user_id, session_id, response_meta_info)
        elif intent == "exercise":
            yield from self.handle_exercise_intent(message, user_id, session_id, response_meta_info)
        elif intent == "fitness_advice":
            yield from self.handle_fitness_advice_intent(message, user_id, session_id, response_meta_info)
        else:
            yield from self.handle_general_chat(message, user_id, session_id, response_meta_info)
```

## 3. LLM代理服务

### 3.1 模型调用

`LLMProxyService` 实现不同模型的统一调用接口：

```python
class LLMProxyService:
    async def acompletion_with_retry(self, model: str, prompt: str, **kwargs) -> str:
        """异步调用LLM并支持重试"""
        # 选择合适的LLM客户端
        # 构建请求参数
        # 发送请求并处理响应
        # 支持错误重试
        
    async def acompletion_stream(self, model: str, prompt: str, **kwargs) -> AsyncGenerator[str, None]:
        """流式调用LLM"""
        # 选择合适的LLM客户端
        # 构建流式请求
        # 逐块返回响应
```

### 3.2 提示模板管理

使用模板函数生成不同场景的提示：

```python
def get_intent_recognition_prompt(message: str, history: List[Dict[str, str]], user_profile: Optional[Dict[str, Any]] = None) -> str:
    """生成意图识别的提示模板"""
    # 构建系统提示
    # 添加用户信息
    # 添加历史对话
    # 添加当前消息
    # 添加输出格式说明
    
def get_training_plan_prompt(user_profile: Dict[str, Any], parameters: Dict[str, Any]) -> str:
    """生成训练计划的提示模板"""
    # 构建系统提示
    # 添加用户资料
    # 添加训练参数
    # 添加输出格式要求
```

## 4. 参数提取系统

### 4.1 参数提取器

`ParameterExtractor` 实现参数提取功能：

```python
class ParameterExtractor:
    async def extract_parameters(self, message: str, parameter_type: str) -> Dict[str, Any]:
        """从消息中提取指定类型的参数"""
        # 构建参数提取提示
        # 调用LLM提取参数
        # 解析LLM响应
        # 验证提取的参数
        # 返回参数字典
```

### 4.2 关键词匹配

部分参数使用关键词匹配方式提取：

```python
def extract_body_parts(message: str) -> List[str]:
    """提取消息中提到的身体部位"""
    body_parts = {
        "胸部": ["胸", "胸肌", "胸大肌", "上胸", "中胸", "下胸"],
        "背部": ["背", "背阔肌", "背肌", "上背", "下背", "背部"],
        # 更多身体部位...
    }
    
    # 对消息进行分词
    # 匹配关键词
    # 返回匹配到的身体部位列表
```

## 5. 训练计划生成

### 5.1 训练计划管理器

`TrainingPlanManager` 协调训练计划生成流程：

```python
class TrainingPlanManager:
    async def generate_training_plan(self, user_id: int, parameters: Dict[str, Any]) -> AsyncGenerator[Union[str, Dict[str, Any]], None]:
        """生成训练计划"""
        # 获取用户资料
        # 构建训练计划请求
        # 调用训练计划服务
        # 格式化计划结果
        # 保存计划到数据库
        # 流式返回计划内容
```

### 5.2 训练计划服务

`TrainingPlanService` 实现训练计划生成逻辑：

```python
class TrainingPlanService:
    async def generate_plan(self, user_profile: Dict[str, Any], parameters: Dict[str, Any]) -> Dict[str, Any]:
        """生成训练计划"""
        # 构建提示模板
        # 调用LLM生成计划
        # 解析结构化输出
        # 验证计划有效性
        # 返回计划数据
        
    async def generate_daily_workout(self, user_id: int, training_plan_id: Optional[int] = None) -> Dict[str, Any]:
        """生成单日训练计划"""
        # 获取用户训练历史
        # 获取用户偏好
        # 构建单日训练提示
        # 调用LLM生成单日计划
        # 返回单日计划数据
```

## 6. 数据库交互

### 6.1 SQL工具服务

`SQLToolService` 提供安全的数据库交互功能：

```python
class SQLToolService:
    def get_user_info(self, user_id: int) -> Dict[str, Any]:
        """获取用户信息"""
        # 构建SQL查询
        # 执行查询
        # 格式化结果
        
    def get_exercise_by_body_part(self, body_part: str, equipment: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取指定身体部位的训练动作"""
        # 构建SQL查询
        # 添加设备过滤
        # 执行查询
        # 格式化结果
```

### 6.2 CRUD操作

各实体的CRUD操作封装：

```python
# 会话CRUD
class CRUDConversation:
    def create(self, db: Session, user_id: int, session_id: str) -> Conversation:
        """创建新会话"""
        
    def get_by_session_id(self, db: Session, session_id: str) -> Optional[Conversation]:
        """通过会话ID获取会话"""
        
    def update_metadata(self, db: Session, session_id: str, metadata: Dict[str, Any]) -> Conversation:
        """更新会话元数据"""

# 消息CRUD
class CRUDMessage:
    def create(self, db: Session, conversation_id: int, content: str, role: str) -> Message:
        """创建新消息"""
        
    def get_conversation_messages(self, db: Session, conversation_id: int, limit: int = 50) -> List[Message]:
        """获取会话的消息历史"""
```

## 7. 状态维护

### 7.1 会话状态

会话状态通过会话的元数据字段维护：

```python
# 会话元数据示例
{
    "conversation_state": "normal",  # 当前状态：normal, guided
    "waiting_for_info": {            # 等待用户输入的信息
        "field": "height",
        "attempt": 0
    },
    "skipped_fields": ["weight"],    # 用户跳过的字段
    "collecting_training_params": True,  # 是否正在收集训练参数
    "asking_param": "body_part",     # 当前询问的参数
    "training_params": {             # 已收集的训练参数
        "body_part": ["胸部"],
        "scenario": "gym"
    },
    "pending_request": {             # 暂停的原始请求
        "message": "我想要一个训练计划",
        "intent": "training_plan"
    },
    "confirming_continuation": False,  # 是否正在询问继续
    "original_interrupted_state": {}   # 中断前的原始状态
}
```

### 7.2 持久化

将状态持久化到数据库，确保会话恢复：

```python
async def _save_conversation_state(self, db: Session, conversation: Conversation, response_meta_info: Dict[str, Any]) -> None:
    """保存会话状态到数据库"""
    # 更新会话元数据
    # 提交数据库变更
```

## 8. 工具注册与使用

### 8.1 工具注册

`ToolRegistrar` 实现工具注册与获取：

```python
class ToolRegistrar:
    def register_tools(self) -> List[Tool]:
        """注册所有可用工具"""
        tools = [
            Tool(
                name="get_user_info",
                func=self.sql_tool_service.get_user_info,
                description="获取用户信息",
                args_schema=GetUserInfoArgsSchema
            ),
            Tool(
                name="get_exercise_by_body_part",
                func=self.sql_tool_service.get_exercise_by_body_part,
                description="获取指定身体部位的训练动作",
                args_schema=GetExerciseArgsSchema
            ),
            # 更多工具...
        ]
        return tools
        
    def get_tools(self, tool_names: Optional[List[str]] = None) -> List[Tool]:
        """获取指定名称的工具"""
        # 如果未指定，返回所有工具
        # 否则返回指定名称的工具
```

### 8.2 Agent配置

使用LangChain的Agent框架配置Agent：

```python
def create_agent(self, tools: List[Tool], system_message: str) -> AgentExecutor:
    """创建Agent执行器"""
    # 配置LLM
    # 配置工具
    # 设置系统消息
    # 创建Agent
    # 返回AgentExecutor
```

## 9. 测试实现

### 9.1 单元测试

针对关键组件的单元测试实现：

```python
# 意图识别测试
async def test_intent_recognition():
    # 准备测试数据
    # 创建模拟IntentRecognizer
    # 调用recognize_intent方法
    # 验证返回的意图是否符合预期
    
# 参数提取测试
async def test_parameter_extraction():
    # 准备测试数据
    # 创建模拟ParameterExtractor
    # 调用extract_parameters方法
    # 验证提取的参数是否正确
```

### 9.2 集成测试

测试组件间的交互：

```python
# 对话状态测试
async def test_conversation_state_transition():
    # 准备测试数据
    # 创建模拟会话和状态
    # 模拟用户输入
    # 验证状态转换是否符合预期
    
# 端到端对话流程测试
async def test_end_to_end_flow():
    # 准备测试环境和数据
    # 模拟完整对话流程
    # 验证系统响应和状态变化
    # 验证数据库记录
```

## 10. 部署配置

### 10.1 环境变量

配置必要的环境变量：

```
# .env 文件示例
DATABASE_URL=postgresql://user:password@localhost/dbname
REDIS_URL=redis://localhost:6379/0
BAILIAN_API_KEY=your_api_key
QWEN_API_KEY=your_api_key
ZHIPU_API_KEY=your_api_key
DEFAULT_MODEL=bailian
```

### 10.2 服务启动

FastAPI应用启动配置：

```python
# main.py
from fastapi import FastAPI
import uvicorn
from app.api.v1.api import api_router
from app.core.config import settings

app = FastAPI(title=settings.PROJECT_NAME, version=settings.VERSION)
app.include_router(api_router, prefix=settings.API_V1_STR)

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
``` 