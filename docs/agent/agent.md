# 智能健身AI助手：架构与实现文档

本文档详细说明了智能健身教练后端的对话式AI助手架构和实现。系统基于FastAPI和LangChain框架构建，提供个性化健身指导、训练计划生成和健康建议。

## 1. 系统概述

智能健身AI助手是一个对话式系统，能够：
1.  利用知识库（RAG）回答用户关于饮食和运动的问题。
2.  访问并利用PostgreSQL数据库中的用户信息以提供个性化响应。
3.  主动询问个性化所需的缺失用户信息。
4.  根据用户目标和现有运动数据生成个性化的训练计划。
5.  维护对话上下文并提供类似真人的互动体验。
6.  支持高并发并提供实时或近乎实时的响应。

## 2. 对话服务架构

### 核心组件

`ConversationService` (位于 `app/services/conversation/orchestrator.py`) 是系统的核心编排者，负责处理用户消息、理解意图、协调各个子服务，并生成响应。其架构如下：

```
                                  ┌─────────────────┐
                                  │  LLMProxyService │
                                  └─────────────────┘
                                          ▲
                                          │
┌─────────────┐    ┌─────────────────┐    │    ┌─────────────────────┐
│   客户端    │───▶│      API层      │────┼───▶│ ConversationService  │
└─────────────┘    └─────────────────┘    │    └─────────────────────┘
                                          │       │ ▲ ▲ ▲ ▲
                                          │       │ │ │ │ │
                                  ┌─────────────────┐ │ │ │ │ ┌──────────────────┐
                                  │  SQLToolService │◀┘ │ │ │ │ │ CharacterManager │
                                  └─────────────────┘   │ │ │ │ └──────────────────┘
                                          │             │ │ │ │
                                          │   ┌────────────────────┐ │ │ ┌─────────────────────┐
                                          │   │ UserProfileManager │◀┘ │ │ │ TrainingParamManager│◀┘
                                          │   └────────────────────┘   │ │ └─────────────────────┘
                                          │              │             │ │
                                          │      ┌───────────────────┐ │ │ ┌─────────────────────┐
                                          │      │ParameterExtractor │ │ │ │ConversationStateManager│
                                          │      └───────────────────┘ │ │ └─────────────────────┘
                                          │                            │ │
                                          │      ┌───────────────────┐ │ │ ┌─────────────────────┐
                                          │      │TrainingPlanManager│◀┘ │ │    IntentHandler    │◀┘
                                          │      └───────────────────┘   │ └─────────────────────┘
                                          │                              │
                                          │      ┌───────────────────┐   │ ┌─────────────────────┐
                                          │      │InterruptionHandler│◀──┘ │PendingRequestManager│
                                          │      └───────────────────┘     └─────────────────────┘
                                          ▼              ▼                          ▼
                                  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
                                  │     数据库      │  │   知识库        │  │   训练计划服务  │
                                  └─────────────────┘  └─────────────────┘  └─────────────────┘
```

### 模块化设计

ConversationService 采用模块化设计，将不同功能拆分为专门的管理器和处理器：

1. **核心服务**
   - `LLMProxyService`: 封装LLM调用，支持流式输出和多种模型（如agent-app、fitness_advice等）
   - `SQLToolService`: 提供数据库访问工具，允许AI查询和操作数据
   - `MemoryService`: 管理对话历史，基于LangChain的ConversationBufferMemory
   - `LLMLogService`: 记录LLM调用日志，用于监控和分析
   - `KnowledgeBaseService` (规划中): 提供RAG知识库检索能力，尚未实现

2. **管理器与处理器 (位于 `app/services/conversation/`)**
   - `IntentRecognizer`: 识别用户意图，支持上下文感知的意图识别
   - `IntentHandler`: 处理不同类型意图的执行逻辑，减轻ConversationService的负担
   - `UserProfileManager`: 管理用户信息收集、验证和更新，替代原ActiveQueryManager
   - `TrainingParamManager`: 管理训练参数收集、提取和验证，处理训练相关参数
   - `ParameterExtractor`: 辅助从消息中提取特定参数，支持LLM提取和关键词匹配
   - `TrainingPlanManager`: 协调训练计划生成的对话流程，调用TrainingPlanService
   - `CharacterManager`: 管理AI角色的性格和回复风格，支持不同的回复风格
   - `ConversationStateManager`: 管理会话状态，实现状态模式设计模式
   - `InterruptionHandler`: 处理对话中断和恢复机制，提升长时间对话体验
   - `PendingRequestManager`: 管理待处理请求的保存和恢复
   - `ToolRegistrar`: 注册和管理Agent工具，提供工具访问接口

3. **业务服务 (位于 `app/services/`)**
   - `TrainingPlanService`: 负责训练计划生成的具体业务逻辑
   - `ExerciseService`: 管理训练动作的查询和推荐
   - `ModelService`: 根据任务类型选择合适的模型


### 对话流程

1. **消息接收与预处理 (`process_message_stream`)**
   - 用户通过API发送消息，系统创建/获取会话和消息记录
   - `ConversationService` 开始处理，加载历史和会话状态
   - 保存用户消息到数据库，创建会话对象（如果不存在）
   - 初始化 `response_meta_info` 字典，用于跟踪会话状态

2. **中断与恢复处理 (通过 `InterruptionHandler`)**
   - 检测对话是否超时中断（计算与上一条消息的时间差）
   - 若中断且当前处于特定流程（如信息收集、参数收集），调用 `_check_message_relevance` 判断新消息与当前流程的相关性
   - 若不相关，设置 `confirming_continuation = true`，询问用户是否继续之前流程
   - 调用 `_analyze_continuation_response` 分析用户选择，根据选择恢复原流程或处理新问题

3. **状态驱动处理 (通过 `ConversationStateManager`)**
   - 调用 `state_manager.get_state(response_meta_info)` 确定当前会话状态
   - 根据状态分发到不同的处理逻辑：
     - **用户信息收集状态** (`UserProfileCollectionState`): 如果 `waiting_for_info` 非空，调用 `_handle_user_profile_collection` 处理用户信息收集
     - **训练参数收集状态** (`TrainingParamCollectionState`): 如果 `collecting_training_params` 为真，调用 `_handle_training_param_collection` 处理参数收集
     - **中断确认状态** (`InterruptionConfirmationState`): 如果 `confirming_continuation` 为真，处理用户对中断的响应
     - **正常对话状态** (`NormalConversationState`): 如果不在特殊状态中，进入正常的意图识别和处理流程

4. **意图识别与参数提取**
   - 检查是否有快速意图（通过API参数 `quick_intent` 指定），有则直接使用
   - 否则，构建意图上下文，调用 `IntentRecognizer.recognize_intent` 识别用户意图
   - 对训练相关意图，调用 `TrainingParamManager.extract_training_parameters` 提取参数
   - 检查参数完整性，若不足则进入参数收集模式

5. **用户信息完整性检查**
   - 对特定意图，调用 `UserProfileManager.get_missing_fields` 检查用户信息完整性
   - 若有缺失字段，保存当前请求到 `pending_request`，进入用户信息收集状态
   - 设置 `waiting_for_info` 和 `session_state="guided"`，生成询问消息

6. **意图执行 (通过 `IntentHandler`)**
   - 调用 `IntentHandler.handle_intent` 方法，根据意图类型分发到具体处理函数：
     - `handle_exercise_intent`: 处理健身动作推荐/查询
     - `handle_training_plan_intent`: 处理训练计划生成
     - `handle_fitness_advice_intent`: 处理健身/营养咨询
     - `_handle_discuss_training_plan_intent`: 处理训练计划讨论
     - `handle_general_chat`: 处理一般聊天
   - 处理函数可能使用Agent (通过 `agent_executor`)、直接调用LLM、调用 `TrainingPlanService` 或其他服务

7. **响应生成与会话更新**
   - 使用 `yield` 关键字流式返回内容块（文本或结构化数据）
   - 调用 `_finalize_conversation` 完成会话更新
   - 调用 `_save_ai_response` 将完整回复保存到数据库
   - 更新会话元数据并提交数据库更改

## 3. LangChain集成理由

使用LangChain具有以下几个优势：
-   **模块化:** 简化连接LLM、记忆、提示和工具的过程。
-   **上下文管理:** 内置的记忆模块有效处理对话历史。
-   **RAG（检索增强生成）:** 简化将LLM响应与我们特定的健身知识库（动作库、营养信息、最佳实践）相结合的过程。
-   **工具使用:** 使LLM能够通过SQL或专用函数与现有数据库（用户、运动动作）交互。
-   **结构化输出:** 便于生成一致的JSON输出，例如训练计划。
-   **Agent框架:** 允许为不同任务（通用问答、计划生成）创建专门的Agent。

## 4. 架构更新

核心架构仍基于FastAPI，新增一个LangChain编排层，在API端点与LLM/数据库/缓存之间进行交互。

```
用户界面 <---> FastAPI API层 (`app/api/`) <---> LangChain编排层 (`app/services/`) <---> LLM API (通义千问AI)
                                                            |
                                                            v
                                             PostgreSQL (`app/db/`, `app/models/`)
                                             Redis (`app/core/config.py` 设置)
                                             知识库 (向量存储 @ `/data/vectorstore`)
```

## 5. 技术栈补充

-   **库:** `langchain`, `langchain-community`, `langchain-core`, `faiss-cpu` (或 `faiss-gpu`), `tiktoken`, LLM SDKs (`tongyi-qwen`, `zhipuai`)。需更新 `requirements.txt`。 (注意: tongyi-qwen 和 zhipuai SDKs 如果项目中实际使用，应确保已添加)
-   **向量存储:** 初期使用FAISS，本地存储于 `/data/vectorstore`。后续可考虑托管向量数据库。
-   **LLM提供商:** 主要使用通义千问或智谱AI，通过 `.env` 和 `app/core/config.py` 配置。
-   **Embedding模型:** 相应的Embedding模型，通过 `.env` 配置。

## 6. 数据模型更新 (`app/models/`)

**(需要在 `app/models/` 中新增的模型)**

1.  **`Conversation` (`conversation.py` - 新文件):**
    ```python
    # app/models/conversation.py
    from sqlalchemy import Column, Integer, String, DateTime, Boolean, ForeignKey, JSON
    from sqlalchemy.orm import relationship
    from sqlalchemy.sql import func
    from app.db.base_class import Base # 使用现有的基类

    class Conversation(Base):
        __tablename__ = "conversations"
        id = Column(Integer, primary_key=True, index=True)
        user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
        session_id = Column(String, unique=True, index=True, nullable=False) # Langchain 会话 ID
        start_time = Column(DateTime(timezone=True), server_default=func.now())
        last_active = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
        is_active = Column(Boolean, default=True)
        metadata = Column(JSON, nullable=True) # 存储意图、主题等上下文信息

        # 关系
        messages = relationship("Message", back_populates="conversation", cascade="all, delete-orphan")
        user = relationship("User", back_populates="conversations") # 反向关联到 User 模型
    ```

2.  **`Message` (`message.py` - 新文件):**
    ```python
    # app/models/message.py
    from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, Enum, JSON
    from sqlalchemy.orm import relationship
    from sqlalchemy.sql import func
    from app.db.base_class import Base
    import enum

    class MessageRole(str, enum.Enum):
        USER = "user"
        ASSISTANT = "assistant"
        SYSTEM = "system"
        TOOL = "tool" # 用于 Langchain 工具调用/结果

    class Message(Base):
        __tablename__ = "messages"
        id = Column(Integer, primary_key=True, index=True)
        conversation_id = Column(Integer, ForeignKey("conversations.id"), nullable=False, index=True)
        # 如果总能从 conversation 推断，可以考虑移除 user_id
        # user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
        content = Column(Text, nullable=False)
        role = Column(Enum(MessageRole), nullable=False)
        created_at = Column(DateTime(timezone=True), server_default=func.now())
        metadata = Column(JSON, nullable=True) # 存储工具调用信息等

        # 关系
        conversation = relationship("Conversation", back_populates="messages")
        # user = relationship("User", back_populates="messages") # 反向关联到 User 模型
    ```

3.  **`QAPair` (`qa_pair.py` - 新文件):** (用于日志记录/微调，可能替代 `llm_logs` 的部分功能)
    ```python
    # app/models/qa_pair.py
    from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, JSON
    from sqlalchemy.orm import relationship
    from sqlalchemy.sql import func
    from app.db.base_class import Base

    class QAPair(Base):
        __tablename__ = "qa_pairs"
        id = Column(Integer, primary_key=True, index=True)
        user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
        session_id = Column(String, index=True, nullable=True) # 关联到会话 session_id
        question = Column(Text, nullable=False)
        answer = Column(Text, nullable=False)
        intent = Column(String(50), nullable=True, index=True) # 意图类别
        retrieved_docs = Column(JSON, nullable=True) # 存储检索到的文档ID/片段
        tool_used = Column(String(50), nullable=True) # 使用的工具名称
        feedback_score = Column(Integer, nullable=True) # 可选：1-5星评分
        created_at = Column(DateTime(timezone=True), server_default=func.now())
        metadata = Column(JSON, nullable=True) # 其他元数据

        # 关系
        user = relationship("User", back_populates="qa_pairs")
    ```

4.  **训练计划模型 (`training_plan.py`, `workout.py`, `workout_exercise.py` - 新文件):**
    ```python
    # app/models/training_plan.py
    from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, SmallInteger, Boolean, JSON
    from sqlalchemy.orm import relationship
    from sqlalchemy.sql import func
    from app.db.base_class import Base

    class TrainingPlan(Base):
        __tablename__ = "training_plans"
        id = Column(Integer, primary_key=True, index=True)
        user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
        plan_name = Column(String(100), nullable=False, default="个性化训练计划")
        description = Column(Text, nullable=True)
        fitness_goal = Column(SmallInteger, nullable=True) # 参照 User 模型的枚举
        experience_level = Column(SmallInteger, nullable=True) # 参照 User 模型的枚举
        duration_weeks = Column(SmallInteger, nullable=True, default=4)
        created_at = Column(DateTime(timezone=True), server_default=func.now())
        is_active = Column(Boolean, default=True)
        is_template = Column(Boolean, default=False, nullable=False)
        privacy_setting = Column(SmallInteger, default=1, nullable=False) # 0: Public, 1: Private (or use Enum)

        # 关系
        user = relationship("User", back_populates="training_plans")
        workouts = relationship("Workout", back_populates="training_plan", cascade="all, delete-orphan")

    # app/models/workout.py
    class Workout(Base):
        __tablename__ = "workouts"
        id = Column(Integer, primary_key=True, index=True)
        training_plan_id = Column(Integer, ForeignKey("training_plans.id"), nullable=False, index=True)
        name = Column(String(100), nullable=False) # 例如 "第一天：上肢训练"
        day_of_week = Column(SmallInteger, nullable=True) # 1-7 或 null 表示灵活安排
        description = Column(Text, nullable=True)

        # 关系
        training_plan = relationship("TrainingPlan", back_populates="workouts")
        workout_exercises = relationship("WorkoutExercise", back_populates="workout", cascade="all, delete-orphan", order_by="WorkoutExercise.order")

    # app/models/workout_exercise.py
    class WorkoutExercise(Base):
        __tablename__ = "workout_exercises"
        id = Column(Integer, primary_key=True, index=True)
        workout_id = Column(Integer, ForeignKey("workouts.id"), nullable=False, index=True)
        exercise_id = Column(Integer, ForeignKey("exercises.id"), nullable=False, index=True)
        sets = Column(SmallInteger, nullable=False) # 组数 (Number of sets)
        reps = Column(String(50), nullable=False) # 次数范围或描述 (Rep range or description, e.g., "10-12", "AMRAP", "30s")
        rest_seconds = Column(SmallInteger, nullable=True) # 此动作后的休息秒数 (Rest time in seconds after this exercise)
        order = Column(SmallInteger, nullable=False, default=0) # 在训练日中的顺序 (Order within workout)
        notes = Column(Text, nullable=True) # 此训练中该动作的具体指导 (Specific instructions for this exercise in this workout)
        exercise_type = Column(String(50), default='weight_reps', nullable=False) # 类型，如 'weight_reps', 'timed', 'reps_only', 'duration'
        superset_group = Column(Integer, nullable=True, index=True) # 同一workout中相同组号的动作组成超级组 (Exercises in same workout with same group number form a superset)

        # 关系
        workout = relationship("Workout", back_populates="workout_exercises")
        exercise = relationship("Exercise") # 关联到现有的 Exercise 模型

        # --- 重要提示 ---
        # 与跟踪已执行组相关的数据（例如：实际重量、实际次数、完成状态）
        # 属于单独的 'Log' 表（例如 SetLog），不属于此计划定义模型的一部分。
        # --- END NOTE ---
    ```

**(现有模型更新)**

5.  **`User` (`app/models/user.py` - 更新):** 添加到新模型的关系。
    ```python
    # ... 现有的 User 模型定义 ...
        # 添加新关系
        conversations = relationship("Conversation", back_populates="user")
        # messages = relationship("Message", back_populates="user") # 如果通过 Conversation 访问，可能不需要
        qa_pairs = relationship("QAPair", back_populates="user")
        training_plans = relationship("TrainingPlan", back_populates="user")
    ```

6.  **`Exercise` (`app/models/exercise.py` - 更新):** 无需直接更改，但确保字段（如 `level`, `body_part_id`, `equipment_id`）足够全面以支持计划生成。

**(数据库迁移)**
- 使用 Alembic 为这些新表和关系生成迁移脚本。

## 7. 模块化实施阶段与任务分解

### 阶段一：核心框架与LLM集成 (基础，预计2周)

**已完成：**

1. **环境配置**
   - ✅ 已添加LangChain相关依赖到`requirements.txt`。
   - ✅ 已更新`app/core/config.py`添加LLM API配置。

2. **数据模型**
   - ✅ 已实现核心SQLAlchemy模型：`Conversation`, `Message`, `QAPair` (用于日志), `TrainingPlan` (简化版或完整版待确认)。
   - ✅ 已更新`User`模型添加所需关系。
   - ✅ 已创建并应用数据库迁移。

3. **服务层**
   - ✅ 已实现`LLMProxyService` (`app/services/llm_proxy_service.py`)：封装LLM调用，支持同步/异步和流式输出。
   - ✅ 已实现`ChatLogService` (`app/services/chat_log_service.py`) (或其功能已整合入`LLMLogService`或`crud_qa_pair`)。

4. **API层**
   - ✅ 已实现基础Chat API端点(`app/api/endpoints/chat.py`)，包括消息发送、历史获取、会话管理和WebSocket流式响应。
   - ✅ 已在`app/api/v1/api.py`中集成chat_router。

5. **CRUD层**
   - ✅ 已实现新模型的CRUD操作 (`crud_conversation.py`, `crud_message.py`, `crud_qa_pair.py`)。

**未完成或待优化：** (根据`orchestrator.py`分析，部分内容可能已调整或完成)

1. **服务层**
   - ⚠️ LLM提供商封装 (如通义千问、智谱AI) 已在 `LLMProxyService` 中。
   - ❌ 基于向量存储的知识库服务 (`KnowledgeBaseService`) 待实现 (阶段四)。

2. **训练计划模型**
   - ⚠️ `TrainingPlan` 模型的完整性（包括 `workout.py`, `workout_exercise.py` 中定义的结构）需结合 `TrainingPlanService` 和 `TrainingPlanManager` 的实现来评估。

3. **流式响应**
   - ✅ WebSocket端点已实现并集成到 `ConversationService`。

### 阶段二：对话服务架构与实现 (核心功能)

#### 已完成：

1. **ConversationService 核心架构**
   - ✅ 已实现 `ConversationService` (`app/services/conversation/orchestrator.py`)：作为对话功能的核心编排者。
   - ✅ 模块化设计：将不同功能拆分为多个专门的管理器和处理器 (如 `UserProfileManager`, `TrainingParamManager`, `ParameterExtractor`, `TrainingPlanManager`, `CharacterManager`)。
   - ✅ 流式响应：实现 `process_message_stream` 异步生成器方法，支持实时流式返回。
   - ✅ 复杂状态管理：包括中断处理、信息收集状态、参数收集状态的维持与恢复。

2. **意图识别与处理**
   - ✅ 已实现 `IntentRecognizer` (`app/services/intent_recognizer.py`)：识别用户消息的意图。
   - 已实现意图处理模块 (`app/services/conversation/intent_handler.py`)：处理各类意图，并能调用相应服务或Agent。

3. **用户信息管理**
   - ✅ 已实现 `UserProfileManager` (`app/services/conversation/user_profile_manager.py`)：
     - 检查用户信息完整性。
     - 验证和解析用户输入。
     - 生成询问消息。
     - 处理用户信息更新。
   - ✅ 已实现用户信息收集流程：检测缺失信息并主动询问，验证用户输入并更新数据库，支持跳过，完成后可恢复原始请求。

4. **训练参数管理**
   - ✅ 已实现 `TrainingParamManager` (`app/services/conversation/training_param_manager.py`) 和 `ParameterExtractor` (`app/services/conversation/parameter_extractor.py`)：
     - 提取训练相关参数（身体部位、训练场景、计划类型等）。
     - 检查必要参数是否完整。
     - 生成参数询问消息。
   - ✅ 已实现训练参数收集流程：检测缺失参数并主动询问，解析用户回答并更新参数，参数完整后执行训练相关意图。

5. **LangChain Agent集成**
   - ✅ 已实现 `ToolRegistrar` (`app/services/tool_registrar.py`)：注册和管理可供Agent使用的工具。
   - ✅ 已配置 LangChain Agent (`AgentExecutor`)：使用 `create_openai_functions_agent` 创建基于函数调用的Agent。
   - ✅ 已实现 `SQLToolService` (`app/services/sql_tool_service.py`)：提供数据库查询工具。

#### ConversationService 详细流程 (基于 `orchestrator.py` 更新)

1.  **初始化阶段**: 创建服务实例, 初始化管理器, 注册工具并初始化Agent, 配置系统消息。
2.  **消息处理核心流程 (`process_message_stream`)**:
    *   **准备与中断处理**: 获取用户/会话数据, 处理潜在的对话中断 (超时、用户切换话题)。
    *   **状态检查与处理**:
        *   若处于用户信息收集状态 (`waiting_for_info`), 调用 `_handle_user_profile_collection`。
        *   若处于训练参数收集状态 (`collecting_training_params`), 调用 `_handle_training_param_collection`。
    *   **正常流程 (非收集状态)**:
        *   进行意图识别 (`IntentRecognizer`)。
        *   对训练相关意图, 提取训练参数 (`TrainingParamManager`, `ParameterExtractor`)；若参数不足，进入收集状态。
        *   对特定意图, 检查用户信息完整性 (`UserProfileManager`)；若信息不足，进入收集状态并暂存当前请求。
        *   执行意图处理逻辑 (调用 `_handle_intent_execution`, 进而分发到 `intent_handler.py` 中的具体函数)。
    *   **响应与保存**: 流式返回响应, 更新会话元数据, 保存AI消息。

#### 会话状态管理 (基于 `orchestrator.py` 更新)

`ConversationService` 通过会话对象的 `meta_info` 字段 (数据库中 `Conversation.metadata`) 和 `process_message_stream` 方法内部的 `response_meta_info` 字典维护会话状态，包括：

- `conversation_state`: (e.g., "normal", "guided")
- `waiting_for_info`: 等待用户输入的信息字段和尝试次数 (e.g., `{"field": "height", "attempt": 0}`)
- `skipped_fields`: 用户跳过的信息字段列表
- `collecting_training_params`: 是否正在收集训练参数 (boolean)
- `asking_param`: 当前正在询问的训练参数名 (e.g., "body_part", "scenario")
- `training_params`: 已收集的训练参数字典 (e.g., `{"body_part": ["胸部"], "scenario": "gym"}`)
- `pending_request`: 因信息不完整而暂停的原始请求详情 (包括原消息、原意图)
- `confirming_continuation`: 是否正在询问用户是否继续被中断的流程 (boolean)
- `original_interrupted_state`: 当流程被中断时，保存的原始状态，用于恢复。

这种状态管理机制使得对话能够跨多个交互保持连贯性，处理中断，并在收集必要信息后恢复原始请求处理。

### 阶段三：训练计划生成与流式交互 (核心功能，预计1-2周)

*   **目标:** 实现核心的训练计划生成功能，并提供流畅的流式交互体验。
*   **任务:**
    *   **Schema层:**
        *   ✅ 定义训练计划相关的Pydantic输出模型 (`app/schemas/training_plan.py`, `app/schemas/daily_workout.py`) 应该已部分或全部完成，以支持结构化输出。
    *   **服务层:**
        *   ⚠️ 实现 `TrainingPlanService` (`app/services/training_plan_service.py`)：这是核心。应包含 `TrainingPlanGenerator` 和 `DailyWorkoutGenerator` (或类似逻辑)。使用 `PydanticOutputParser`。创建详细的Prompt模板。
        *   ⚠️ `TrainingPlanManager` (`app/services/conversation/training_plan_manager.py`)：新引入的组件，负责协调 `ConversationService` 与 `TrainingPlanService` 之间的交互，管理与计划生成相关的对话状态。
        *   ✅ 更新 `ConversationService`: 已能根据意图调用训练计划相关逻辑 (通过 `_handle_training_plan_intent`)。
        *   ✅ `LLMProxyService` 和 `ConversationService` 已支持流式输出。
    *   **API层:**
        *   ⚠️ 实现训练计划相关API端点 (`app/api/endpoints/training_plan.py`)：如 `POST /generate`, `POST /daily` 等。这些端点应调用 `TrainingPlanService`。
        *   ✅ WebSocket端点 `WS /api/v1/chat/stream/{session_id}` (`app/api/endpoints/chat.py`) 已实现。
        *   ⚠️ `training_plan_router` 集成到 `app/api/v1/api.py` 状态需确认。
    *   **CRUD层:**
        *   ⚠️ 完善 `crud_training_plan.py`，实现 `crud_workout.py` 和 `crud_workout_exercise.py` (若模型已完全定义)。
    *   **测试:**
        *   ⚠️ 训练计划生成功能、基于历史的推荐、WebSocket流式响应、主动询问与计划生成的集成，都需要在 `TrainingPlanService` 和相关管理器完善后进行测试。
*   **产出:** (部分已通过 `orchestrator.py` 的参数收集和意图分发逻辑打下基础)
    *   用户可以通过对话请求并接收结构化的完整训练计划。
    *   用户可以根据过去训练记录获取智能生成的单日训练计划。
    *   用户可以通过WebSocket获得实时的流式回复。
    *   系统能够在缺失必要信息时主动询问再生成计划。

### 阶段四：RAG知识库集成 (增强，预计2周)

*   **目标:** 引入外部健身知识库，使AI助手的回答更专业、更准确。
*   **任务:**
    *   **知识准备:** 整理健身知识文档或从数据库提取信息。
    *   **服务层:**
        *   ❌ 实现 `KnowledgeBaseService` (`app/services/knowledge_service.py`)：加载文档、分块、生成嵌入、管理FAISS索引、提供 retriever。 (此为主要待办事项)
        *   ❌ **(重点)** 重构 `ConversationService`: 将Agent/Chain替换或增强为支持RAG的链/Agent。集成 `KnowledgeBaseService.get_retriever()`。
    *   **LangChain:**
        *   优化提示，指示优先使用检索知识。
    *   **测试:** 针对健身知识的问答，验证RAG效果。
*   **产出:** AI助手能够基于提供的健身知识库回答问题。 (待实现)

### 阶段五：记忆、测试、优化与监控 (完善，预计1周)

*   **目标:** 实现长期对话记忆，完成端到端测试，进行性能优化，并部署监控系统。
*   **任务:**
    *   **记忆:**
        *   选择并实现合适的LangChain Memory类（如 `ConversationTokenBufferMemory`）。
        *   将其后端存储配置为Redis (`RedisChatMessageHistory`) 或数据库（需要自定义存储逻辑或使用现有集成），更新 `ConversationService` 以使用此Memory实例加载和保存历史。
    *   **测试:**
        *   进行全面的端到端测试，覆盖所有功能模块（问答、个性化、工具使用、计划生成、RAG、流式、记忆）。
        *   进行压力测试，评估高并发下的性能表现。
    *   **优化:**
        *   根据测试结果进行性能优化：数据库查询优化、Redis缓存应用（缓存用户数据、常用知识片段）、异步任务处理 (`BackgroundTasks`)。
        *   LLM调用优化：考虑对简单任务使用更快的模型，或增加缓存。
    *   **监控:**
        *   集成Prometheus和Grafana（使用 `starlette-exporter`）：添加指标跟踪API请求、响应时间、LLM调用、工具使用、意图分布、错误率等。
        *   设置日志聚合和告警（如使用ELK栈或云服务商日志服务）。
    *   **管理后台:** 更新 `app/api/admin/`，添加查看新数据模型（会话、消息、计划、QA对）和知识库管理的功能。
    *   **文档:** 最终完善所有技术文档和API文档。
*   **产出:** 具备连贯对话能力的、经过测试和优化的、带有监控的AI助手系统。

## 8. 风险与缓解措施

-   **LLM性能/成本:** 监控使用情况，实施缓存，探索用于简单意图的更小/更便宜的模型。准备回退逻辑。
-   **输出质量/幻觉:** 严重依赖RAG，使用强提示，实施输出验证/解析，对敏感建议考虑人工介入。
-   **数据隐私:** 确保传递给LLM的用户数据最小化并在可能的情况下匿名化。在隐私政策中明确说明数据使用。遵守法规。
-   **可伸缩性:** 设计支持水平扩展（FastAPI工作进程，可能需要Redis集群）。对繁重任务使用异步处理和消息队列。
-   **提示工程复杂性:** 分配时间进行迭代式提示优化。对提示进行版本控制。

此调整后的计划旨在更快地交付核心用户价值（个性化交互和训练计划生成），同时保持了模块化的开发流程。

## 9. 当前实现状态

### ConversationService 核心功能

目前 `ConversationService` 已实现以下核心功能：

1. **模块化架构**
   - 将不同功能拆分为专门的管理器和处理器（如 `UserProfileManager`, `TrainingParamManager`, `IntentHandler` 等）
   - 支持流式响应的异步生成器模式（`AsyncGenerator`）
   - LangChain Agent 集成（通过 `create_openai_functions_agent` 创建）
   - 状态模式实现（通过 `ConversationStateManager` 和状态类）

2. **状态管理**
   - 会话状态跟踪（通过 `ConversationStateManager`）
   - 用户信息收集状态（`UserProfileCollectionState`）
   - 训练参数收集状态（`TrainingParamCollectionState`）
   - 中断确认状态（`InterruptionConfirmationState`）
   - 请求暂存与恢复（通过 `PendingRequestManager`）

3. **意图处理**
   - 意图识别（通过 `IntentRecognizer`，支持上下文感知）
   - 意图执行（通过 `IntentHandler`，根据意图类型分发）
   - 训练动作查询与推荐（`handle_exercise_intent`）
   - 训练计划生成（单日/周期）（`handle_training_plan_intent`）
   - 健身/营养建议（`handle_fitness_advice_intent`）
   - 训练计划讨论（`_handle_discuss_training_plan_intent`）
   - 一般聊天（`handle_general_chat`）

4. **信息收集与参数提取**
   - 用户信息主动询问（通过 `UserProfileManager`）
   - 训练参数主动询问（通过 `TrainingParamManager`）
   - 参数提取（通过 `ParameterExtractor`，支持LLM提取和关键词匹配）
   - 输入验证与重试机制
   - 信息收集完成后恢复原始请求

5. **中断处理与恢复**
   - 对话中断检测（计算时间差）
   - 相关性判断（`_check_message_relevance`）
   - 用户选择分析（`_analyze_continuation_response`）
   - 流程恢复机制

6. **个性化响应**
   - 角色管理（通过 `CharacterManager`）
   - 用户资料整合（`_get_user_profile_text`）
   - 训练难度自适应（`_get_recommended_difficulty`）

### 待优化项

1. **知识库集成**
   - 实现 `KnowledgeBaseService`
   - 添加基于向量存储的RAG功能
   - 集成健身知识文档
   - 实现知识来源引用机制

2. **记忆管理**
   - 优化长期对话记忆机制
   - 实现基于Redis的记忆存储（`RedisChatMessageHistory`）
   - 实现对话摘要（`ConversationSummaryBufferMemory`）
   - 添加选择性记忆机制

3. **性能优化**
   - 缓存机制增强（意图识别、参数提取、数据库查询）
   - LLM调用优化（模型选择、批量参数提取）
   - 异步处理增强（减少阻塞操作）
   - 提高高并发下的稳定性

4. **测试与监控**
   - 实现单元测试和集成测试
   - 添加详细的性能指标
   - 实现结构化日志记录
   - 建立监控仪表板

5. **多模态支持**
   - 集成图像识别功能
   - 实现训练计划可视化
   - 支持语音输入和输出
