# AI健身助手第二阶段开发分析与前端接入文档

## 一、第二阶段开发完成情况分析

根据`docs/agent/agent.md`文档，第二阶段"数据库工具与个性化"的开发目标是使AI助手能够访问数据库信息、理解用户意图并主动询问缺失信息。结合 `app/services/conversation/orchestrator.py` 的最新实现，第二阶段及部分第三阶段功能已取得显著进展。

### 已完成部分 (基于 `orchestrator.py`)

1.  **数据模型扩展**: (假设 `UserTrainingRecord` 等已按 `agent.md` 实现)
    *   ✅ `User` 模型及相关关系已定义。
2.  **核心服务层 (`app/services/conversation/` 及 `app/services/`)**:
    *   ✅ `LLMProxyService`: 封装LLM调用。
    *   ✅ `SQLToolService`: 数据库交互工具服务。
    *   ✅ `IntentRecognizer`: 意图识别，并能接收上下文信息。
    *   ✅ `UserProfileManager`: 强大的用户信息收集、验证、更新和流程管理，替代并增强了原 `ActiveQueryManager`。
    *   ✅ `TrainingParamManager` 与 `ParameterExtractor`: 实现了训练相关参数的提取、收集、验证和流程管理。
    *   ✅ `ConversationService` (`orchestrator.py`): 作为核心编排器，实现了复杂的对话逻辑，包括：
        *   高级状态管理 (信息收集、参数收集、中断恢复等)。
        *   中断处理与用户选择引导 (`_check_message_relevance`, `_analyze_continuation_response`)。
        *   Agent (`AgentExecutor`) 集成与工具调用。
        *   流式响应处理。
    *   ✅ `ToolRegistrar`: 动态注册Agent工具。
    *   ✅ `MemoryService`: 基础对话记忆。
    *   ✅ `CharacterManager`: 初步引入，用于管理AI角色性格。
    *   ✅ `TrainingPlanManager`: 初步引入，用于协调训练计划生成的对话流程。

### 需进一步明确和完善的部分 (可能涉及第三阶段内容)

1.  **`TrainingPlanService`**: 其具体实现和与 `TrainingPlanManager` 的协同，以及结构化训练计划的Pydantic模型输出细节。
2.  **API层增强**: `POST /api/v1/chat/message` 已通过 `orchestrator.py` 支持复杂的会话状态管理。训练计划专属API端点 (`app/api/endpoints/training_plan.py`) 的具体实现状态。
3.  **CRUD层**: 针对完整训练计划模型 (`TrainingPlan`, `Workout`, `WorkoutExercise`) 的CRUD操作完善程度。

## 二、AI助手对话流程梳理 (基于 `orchestrator.py`)

### 对话基本流程

```
用户 -> 发送消息 -> 后端API (`/chat/message` 或 `/chat/stream`) -> ConversationService (`process_message_stream`)
                                                                      |
                                        +-----------------------------+
                                        | 1. 初始化与数据准备 (用户, 会话) |
                                        +-----------------------------+
                                                      |
                                        +-----------------------------+
                                        | 2. 中断处理与恢复机制         |
                                        |    (超时? 相关? 继续/新问题?) |
                                        +-----------------------------+
                                                      |
                                        +-----------------------------+
                                        | 3. 状态驱动处理             |
                                        |  - 用户信息收集?           |
                                        |  - 训练参数收集?           |
                                        +-----------------------------+
                                                      | (若非收集状态)
                                        +-----------------------------+
                                        | 4. 意图识别 (`IntentRecognizer`)|
                                        |  + 参数提取 (`TrainingParamManager`)| 
                                        +-----------------------------+
                                                      |
                                        +-----------------------------+
                                        | 5. 信息/参数完整性检查      |
                                        |  (不足则进入相应收集状态)   |
                                        +-----------------------------+
                                                      | (若完整)
                                        +-----------------------------+
                                        | 6. 意图执行 (`_handle_intent_execution`)| 
                                        |  (Agent, LLM, Services)     |
                                        +-----------------------------+
                                                      |
                                        +-----------------------------+
                                        | 7. 响应生成 (流式) 与保存   |
                                        +-----------------------------+
                                                      |
                                                      v
                                                    用户
```

### 详细流程说明

1.  **消息接收与处理 (`process_message_stream`)**: 用户通过API发送消息。系统获取或创建会话 (`Conversation`) 和消息 (`Message`) 记录。`ConversationService` 加载会话状态和历史。
2.  **中断处理**: 系统检查距离上一条AI消息的时间。若特定流程（如信息收集）中发生超时，通过LLM判断新消息与之前流程的相关性。若不相关，会询问用户是否继续原流程或处理新问题。根据用户回复，恢复原流程状态或处理新请求。
3.  **状态驱动处理**:
    *   **用户信息收集**: 若会话元数据中 `waiting_for_info` 为真，调用 `UserProfileManager` 处理用户对信息询问的回复。这包括验证输入、更新用户数据、管理重试/跳过逻辑。信息收集完成后，若有待处理请求，则恢复该请求。
    *   **训练参数收集**: 若会话元数据中 `collecting_training_params` 为真，调用 `TrainingParamManager` 和 `ParameterExtractor` 处理。这包括提取参数、更新已收集参数、询问缺失参数。参数完整后，转换为相应的训练相关意图并执行。
4.  **意图识别**: 若不处于特殊信息收集状态，则调用 `IntentRecognizer` 识别用户意图。对于训练相关意图，会调用 `TrainingParamManager` 提取参数；若参数不足，则会话进入训练参数收集状态，并向用户询问缺失参数。
5.  **用户信息完整性检查**: 对于特定意图（如生成训练计划、营养建议），会调用 `UserProfileManager` 检查当前用户的必要信息是否完整。若不完整，则保存当前用户请求（意图和消息），会话进入用户信息收集状态，并向用户询问第一个缺失的信息字段。
6.  **意图执行**: 当所有前置条件（如信息完整、参数完整）满足后，调用内部的 `_handle_intent_execution` 方法（通常此方法会委托给 `intent_handler.py` 中的具体处理函数）。这些函数会根据意图类型：
    *   直接调用 LLM 生成回复 (如一般聊天、简单健身建议)。
    *   调用 LangChain Agent (`AgentExecutor`) 执行需要工具（如 `SQLToolService`）的复杂查询或任务。
    *   调用专门的服务，如 `TrainingPlanService` 来生成结构化的训练计划。
7.  **响应生成与保存**: 以异步生成器 (`AsyncGenerator`) 的方式流式返回文本片段或结构化数据给客户端。最终的AI回复消息会通过 `_save_ai_response` 方法保存到数据库，同时更新会话的元数据。

## 三、前端接入文档

### 1. API概览

| 端点                                       | 方法      | 描述                                     |
| :----------------------------------------- | :-------- | :--------------------------------------- |
| `/api/v1/chat/message`                     | POST      | 发送消息并获取一次性回复 (主要用于非流式场景或简单测试) |
| `/api/v1/chat/conversations`               | GET       | 获取用户会话列表                         |
| `/api/v1/chat/conversations/{session_id}/messages` | GET       | 获取指定会话的消息历史                   |
| `/api/v1/chat/conversations/{session_id}`  | DELETE    | 删除指定会话                             |
| `/api/v1/chat/stream/{session_id}`         | WebSocket | **推荐使用**: 获取实时、流式对话响应         |

### 2. 发送消息 (一次性POST请求 - `/api/v1/chat/message`)

主要用于测试或不需要流式响应的场景。推荐使用WebSocket接口进行主应用交互。

#### 请求格式

```typescript
// POST /api/v1/chat/message
interface ChatRequest {
  message: string;           // 用户消息内容
  session_id?: string;       // 可选，会话ID。如不提供则创建新会话
  meta_info?: {              // 可选，客户端可传递的初始元数据，会被服务端 `process_message_stream` 中的 `meta_info` 参数接收并与服务端状态合并。
    clientMessageId?: string; // 客户端生成的消息ID，用于追踪
    [key: string]: any;
  };
  quick_intent?: string;     // 可选，用于快速指定意图，绕过LLM意图识别，如 "start_user_profile_collection"
}
```

#### 响应格式

```typescript
interface ChatResponse {
  response: string;          // AI回复的完整文本内容 (非流式)
  session_id: string;        // 会话ID
  meta_info: {               // AI返回的元数据，反映了处理后的会话状态
    intent?: string;         // 识别到的意图
    confidence?: number;     // 意图置信度
    parameters?: object;     // 意图相关参数
    conversation_state?: "normal" | "guided"; // 对话引导状态
    waiting_for_info?: { field: string; attempt: number } | null; // 是否等待用户输入特定信息
    skipped_fields?: string[]; // 用户跳过的信息字段
    collecting_training_params?: boolean; // 是否正在收集训练参数
    asking_param?: string | null; // 当前正在询问的训练参数
    training_params?: object | null; // 已收集的训练参数
    pending_request?: object | null; // 因信息不完整而暂停的原始请求详情
    confirming_continuation?: boolean; // 是否在确认是否继续被中断的流程
    clientMessageId?: string; // 服务端返回的对应客户端消息ID (可能是数据库中的 message_id)
    db_message_id?: number;  // 用户消息在数据库中的ID
    ai_message_id?: number; // AI消息在数据库中的ID
    [key: string]: any;
  };
}
```

### 3. 获取会话列表 (`/api/v1/chat/conversations`)

响应格式中的 `metadata` 可能包含该会话最后的状态信息。

```typescript
interface ConversationListResponse {
  success: boolean;
  data: Array<{
    id: number;             // 会话的数据库ID
    user_id: number;
    session_id: string;     // Langchain会话ID (通常是UUID)
    start_time: string;     // ISO 8601 格式
    last_active: string;    // ISO 8601 格式
    is_active: boolean;
    title: string | null;
    meta_info?: {           // 会话最后一次交互的元数据快照
      intent?: string;
      conversation_state?: string;
      // ... 其他在 ChatResponse.meta_info 中可能出现的字段
      [key: string]: any;
    }
  }>;
  total: number;
}
```

### 4. 获取会话消息历史 (`/api/v1/chat/conversations/{session_id}/messages`)

```typescript
interface MessageListResponse {
  success: boolean;
  data: Array<{
    id: number;            // 消息ID
    conversation_id: number;
    content: string;       // 消息内容
    role: "user" | "assistant" | "system" | "tool"; // 消息角色
    created_at: string;    // 创建时间
    meta_info?: any;       // 消息相关的元数据 (例如工具调用信息)
  }>;
  session_info?: {         // 当前会话的最新信息 (可选)
    id: number;
    session_id: string;
    is_active: boolean;
    meta_info?: any;       // 当前会话的最新元数据
  }
  total: number;
}
```

### 5. 删除会话 (`/api/v1/chat/conversations/{session_id}`)

   保持不变。

### 6. WebSocket流式响应 (推荐 - `/api/v1/chat/stream/{session_id}`)

#### 连接URL

```
ws://your-api-domain/api/v1/chat/stream/{session_id}
// 或 wss:// (如果使用HTTPS)
// {session_id} 可以是已存在的，也可以是客户端新生成的UUID，服务端会复用或创建新会话。
```

#### 发送消息格式（客户端到服务器 - JSON字符串）

```typescript
interface WSChatRequest {
  message: string;         // 用户消息
  meta_info?: {            // 可选，客户端可传递的初始元数据
    clientMessageId?: string; // 客户端生成的消息ID，用于追踪
    [key: string]: any;
  };
  quick_intent?: string;   // 可选，用于快速指定意图
}
```

#### 接收消息格式（服务器到客户端 - JSON对象）

服务器会发送多种类型的消息对象，前端需要根据 `event` 字段来区分处理：

```typescript
// 文本片段 (AI回复的组成部分)
interface WSContentChunk {
  event: "content_chunk";
  data: string; // AI回复的文本片段
}

// 元数据更新 (会话状态变化)
interface WSMetaInfoUpdate {
  event: "meta_info_update";
  data: { // 包含一个或多个需要更新的元数据字段
    intent?: string;
    confidence?: number;
    parameters?: object;
    conversation_state?: "normal" | "guided";
    waiting_for_info?: { field: string; attempt: number } | null;
    skipped_fields?: string[];
    collecting_training_params?: boolean;
    asking_param?: string | null;
    training_params?: object | null;
    pending_request?: boolean; // 简化为布尔值，表示是否存在待处理请求
    confirming_continuation?: boolean;
    clientMessageId?: string; // 服务端确认/映射的客户端消息ID (可能是数据库ID)
    [key: string]: any;
  };
}

// 结构化数据 (例如训练计划)
interface WSStructuredData {
  event: "structured_data"; // 或更具体的如 "training_plan", "exercise_list"
  type: string; // 具体数据类型，如 "daily_workout", "weekly_plan"
  data: any;    // 实际的结构化数据对象 (Pydantic模型序列化后的JSON)
}

// AI消息保存确认
interface WSAIMessageSaved {
  event: "ai_message_saved";
  data: { ai_message_id: number; };
}

// 错误消息
interface WSErrorMessage {
  event: "error";
  message: string; // 错误描述
}

// 流结束的信号 (可选，根据实际需要)
// interface WSStreamEnd {
//   event: "stream_end";
// }

// 前端收到的可能是以上类型的联合	ype ServerWebSocketMessage = WSContentChunk | WSMetaInfoUpdate | WSStructuredData | WSAIMessageSaved | WSErrorMessage; // | WSStreamEnd;
```

### 7. 使用示例 (WebSocket)

```typescript
// 示例: WebSocket流式响应
function पानीconnectWebSocket(sessionId: string, initialMessage: string) {
  const ws = new WebSocket(`ws://your-api-domain/api/v1/chat/stream/${sessionId}`);
  let currentFullMessage = '';
  const clientMessageId = `client-${Date.now()}`;

  ws.onopen = () => {
    console.log('WebSocket已连接');
    // 发送初始消息
    ws.send(JSON.stringify({
      message: initialMessage,
      meta_info: { clientMessageId }
    }));
  };

  ws.onmessage = (event) => {
    const serverMessage: ServerWebSocketMessage = JSON.parse(event.data as string);
    console.log("Received from server:", serverMessage);

    switch(serverMessage.event) {
      case 'content_chunk':
        currentFullMessage += serverMessage.data;
        // 更新UI，实时显示AI回复
        // displayAIMessageChunk(serverMessage.data);
        break;
      case 'meta_info_update':
        // 更新本地的会话元数据状态
        // updateLocalMetaInfo(serverMessage.data);
        console.log("MetaInfo Updated:", serverMessage.data);
        // 根据元数据状态，可能需要更新UI，例如显示正在等待用户输入特定信息
        if (serverMessage.data.waiting_for_info) {
          // promptUserForInfo(serverMessage.data.waiting_for_info.field);
        }
        if (serverMessage.data.clientMessageId && serverMessage.data.clientMessageId === clientMessageId) {
            console.log(`Client message ${clientMessageId} acknowledged by server, mapped to ${serverMessage.data.clientMessageId}`);
        }
        break;
      case 'structured_data':
        // 处理并展示结构化数据，如训练计划
        // displayStructuredData(serverMessage.type, serverMessage.data);
        console.log("Structured Data Received:", serverMessage.type, serverMessage.data);
        currentFullMessage = ''; // 通常结构化数据后会有新的引导语，或作为一段独立内容
        break;
      case 'ai_message_saved':
        console.log("AI message saved to DB with ID:", serverMessage.data.ai_message_id);
        // 可以在这里认为一轮AI回复结束
        currentFullMessage = ''; // 重置累积消息
        break;
      case 'error':
        console.error('WebSocket Error Message:', serverMessage.message);
        // displayErrorToUser(serverMessage.message);
        break;
      // case 'stream_end':
      //   console.log('AI回复流结束');
      //   // 可以在这里处理完整的消息 currentFullMessage
      //   // saveFullAIMessage(currentFullMessage);
      //   currentFullMessage = ''; // 重置累积消息
      //   break;
      default:
        console.warn("Unhandled WebSocket message event:", serverMessage);
    }
  };

  ws.onclose = (event) => {
    console.log('WebSocket已断开:', event.code, event.reason);
  };

  ws.onerror = (error) => {
    console.error('WebSocket发生错误:', error);
  };

  return ws; // 返回WebSocket实例，方便外部控制（如发送后续消息）
}

// 后续发送消息
// if (ws && ws.readyState === WebSocket.OPEN) {
//   const nextClientMessageId = `client-${Date.now()}`;
//   ws.send(JSON.stringify({
//     message: "我的下一个问题是...",
//     meta_info: { clientMessageId: nextClientMessageId }
//   }));
// }
```

### 8. 状态处理指南 (前端)

前端应维护一个本地的会话状态对象，该对象根据从 WebSocket 收到的 `meta_info_update` 事件进行更新。关键状态字段包括：

-   `waiting_for_info`: 如果非空，表示AI正在等待用户提供特定字段的信息。UI应提示用户输入该字段 (`waiting_for_info.field`)。
-   `collecting_training_params` 和 `asking_param`: 如果 `collecting_training_params` 为 `true`，UI应提示用户AI正在收集训练计划的参数，当前询问的是 `asking_param`。
-   `confirming_continuation`: 如果为 `true`，表示AI因为检测到对话中断，正在询问用户是否继续之前的流程。UI应相应地展示此询问，并允许用户回答"是"或"否"（或类似意图的自然语言）。
-   `pending_request`: 如果为 `true`，表示有一个因信息不完整而被暂停的原始用户请求。当所需信息收集完毕后，AI会自动处理此请求。

前端应根据这些状态调整UI，例如：

-   当 `waiting_for_info` 或 `asking_param` 有值时，可以禁用通用的消息输入框，或引导用户回答当前问题。
-   当 `confirming_continuation` 为 `true` 时，可以提供快捷按钮让用户选择是否继续。

### 9. 错误处理

-   通过 WebSocket `onerror` 事件捕获连接级别错误。
-   通过接收 `event: "error"` 消息捕获应用级别错误，并向用户展示友好的错误信息。
