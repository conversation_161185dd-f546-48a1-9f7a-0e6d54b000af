# 智能健身AI助手系统 - 全面测试方案

## 概述

本文档描述了针对智能健身AI助手系统新版架构（`app/services/ai_assistant/`）的全面测试方案，包括核心功能测试、API端点测试、多轮对话测试和实时测试系统。

## 测试架构设计

### 1. 测试层次结构

```
测试金字塔
    ┌─────────────────────────────────┐
    │        E2E测试 (10%)            │  ← 实时测试界面、完整用户场景
    │     端到端集成测试              │
    └─────────────────────────────────┘
    ┌─────────────────────────────────┐
    │      集成测试 (20%)             │  ← API端点、组件集成、数据库
    │   API + 组件集成测试            │
    └─────────────────────────────────┘
    ┌─────────────────────────────────┐
    │      单元测试 (70%)             │  ← 核心逻辑、状态管理、意图处理
    │   核心功能单元测试              │
    └─────────────────────────────────┘
```

### 2. 测试范围矩阵

| 测试类型 | 核心功能 | API端点 | 数据持久化 | 错误处理 | 性能 |
|---------|---------|---------|-----------|---------|------|
| **单元测试** | ✅ 意图识别<br>✅ 状态管理<br>✅ LLM集成 | ❌ | ❌ | ✅ 异常处理<br>✅ 降级机制 | ❌ |
| **集成测试** | ✅ 流式响应<br>✅ 组件协作 | ✅ HTTP端点<br>✅ WebSocket | ✅ 消息存储<br>✅ 会话管理 | ✅ 超时处理<br>✅ 连接断开 | ✅ 响应时间 |
| **E2E测试** | ✅ 多轮对话<br>✅ 上下文保持 | ✅ 完整流程 | ✅ 数据一致性 | ✅ 用户体验 | ✅ 并发测试 |

## 测试组件架构

### 1. 核心测试模块

```
tests/
├── unit/                           # 单元测试
│   ├── ai_assistant/
│   │   ├── test_intent_recognition.py      # 意图识别测试
│   │   ├── test_state_management.py        # 状态管理测试
│   │   ├── test_llm_integration.py         # LLM集成测试
│   │   ├── test_conversation_orchestrator.py # 协调器测试
│   │   └── test_knowledge_retrieval.py     # 知识检索测试
│   └── utils/
│       ├── test_helpers.py                 # 测试辅助函数
│       └── mock_factories.py               # Mock对象工厂
├── integration/                    # 集成测试
│   ├── api/
│   │   ├── test_chat_endpoints.py          # 聊天API测试
│   │   ├── test_websocket_endpoints.py     # WebSocket测试
│   │   └── test_error_handling.py          # 错误处理测试
│   ├── database/
│   │   ├── test_message_persistence.py     # 消息持久化测试
│   │   └── test_conversation_management.py # 会话管理测试
│   └── performance/
│       ├── test_response_time.py           # 响应时间测试
│       └── test_concurrent_users.py        # 并发用户测试
├── e2e/                           # 端到端测试
│   ├── scenarios/
│   │   ├── test_fitness_consultation.py    # 健身咨询场景
│   │   ├── test_training_plan_creation.py  # 训练计划制定
│   │   └── test_multi_turn_conversation.py # 多轮对话测试
│   └── user_journeys/
│       ├── test_new_user_onboarding.py     # 新用户引导
│       └── test_returning_user_flow.py     # 老用户流程
├── interactive/                   # 交互式测试
│   ├── gradio_test_app.py                  # Gradio测试界面
│   ├── streamlit_dashboard.py              # Streamlit仪表板
│   └── websocket_test_client.py            # WebSocket测试客户端
└── fixtures/                      # 测试数据和配置
    ├── test_data/
    │   ├── user_profiles.json              # 测试用户档案
    │   ├── conversation_scenarios.json     # 对话场景
    │   └── expected_responses.json         # 预期响应
    └── config/
        ├── test_config.py                  # 测试配置
        └── mock_config.py                  # Mock配置
```

### 2. 测试数据管理

```python
# 测试用户档案分类
USER_PROFILES = {
    "beginner_male": {
        "age": 25, "gender": 1, "height": 175, "weight": 70,
        "fitness_goal": 3, "experience_level": 1, "activity_level": 2
    },
    "intermediate_female": {
        "age": 30, "gender": 2, "height": 165, "weight": 60,
        "fitness_goal": 1, "experience_level": 2, "activity_level": 3
    },
    "advanced_athlete": {
        "age": 28, "gender": 1, "height": 180, "weight": 85,
        "fitness_goal": 3, "experience_level": 3, "activity_level": 5
    }
}

# 对话场景模板
CONVERSATION_SCENARIOS = {
    "fitness_consultation": [
        {"user": "我想开始健身，但不知道从哪里开始", "expected_intent": "fitness_advice"},
        {"user": "我的目标是减肥10公斤", "expected_intent": "fitness_advice"},
        {"user": "请帮我制定一个训练计划", "expected_intent": "training_plan"}
    ],
    "training_guidance": [
        {"user": "深蹲的正确姿势是什么？", "expected_intent": "exercise_action"},
        {"user": "我应该做几组深蹲？", "expected_intent": "training_plan"},
        {"user": "深蹲时膝盖疼怎么办？", "expected_intent": "fitness_advice"}
    ]
}
```

## 测试策略

### 1. 单元测试策略

**目标**: 验证各个组件的独立功能
**覆盖率要求**: > 90%
**执行频率**: 每次代码提交

**重点测试内容**:
- 意图识别准确性（各种输入场景）
- 状态转换逻辑（所有可能的状态路径）
- LLM提供商集成（模拟响应测试）
- 错误处理和边界条件

### 2. 集成测试策略

**目标**: 验证组件间协作和API功能
**覆盖率要求**: > 80%
**执行频率**: 每日构建

**重点测试内容**:
- API端点完整性（请求/响应格式）
- WebSocket连接稳定性（断线重连）
- 数据库事务一致性（并发操作）
- 缓存机制有效性（命中率测试）

### 3. 端到端测试策略

**目标**: 验证完整用户场景和业务流程
**覆盖率要求**: 核心场景100%
**执行频率**: 发布前

**重点测试内容**:
- 多轮对话连贯性（上下文保持）
- 用户体验流畅性（响应时间）
- 业务逻辑正确性（健身建议质量）
- 系统稳定性（长时间运行）

## 测试环境配置

### 1. 测试环境隔离

```yaml
# docker-compose.test.yml
version: '3.8'
services:
  test-db:
    image: postgres:13
    environment:
      POSTGRES_DB: fitness_test_db
      POSTGRES_USER: test_user
      POSTGRES_PASSWORD: test_password
    ports:
      - "5433:5432"
  
  test-redis:
    image: redis:6-alpine
    ports:
      - "6380:6379"
  
  test-app:
    build: .
    environment:
      - DATABASE_URL=*************************************************/fitness_test_db
      - REDIS_URL=redis://test-redis:6379/0
      - TESTING=true
    depends_on:
      - test-db
      - test-redis
```

### 2. Mock服务配置

```python
# tests/fixtures/config/mock_config.py
class MockLLMConfig:
    """Mock LLM配置"""
    MOCK_RESPONSES = {
        "fitness_advice": "这是模拟的健身建议响应...",
        "training_plan": "这是模拟的训练计划响应...",
        "general_chat": "这是模拟的一般对话响应..."
    }
    
    RESPONSE_DELAY = 0.1  # 模拟网络延迟
    ERROR_RATE = 0.05     # 5%的错误率用于测试错误处理

class MockDatabaseConfig:
    """Mock数据库配置"""
    USE_IN_MEMORY_DB = True
    AUTO_ROLLBACK = True  # 测试后自动回滚
```

## 性能测试指标

### 1. 响应时间要求

| 操作类型 | 目标响应时间 | 最大可接受时间 |
|---------|-------------|---------------|
| 意图识别 | < 100ms | < 200ms |
| 简单对话 | < 500ms | < 1s |
| 复杂查询 | < 2s | < 5s |
| 流式首token | < 1s | < 2s |
| WebSocket连接 | < 100ms | < 300ms |

### 2. 并发性能要求

| 指标 | 目标值 | 测试方法 |
|-----|-------|---------|
| 并发用户数 | 100+ | 逐步增加负载 |
| 吞吐量 | 1000 req/min | 持续负载测试 |
| 错误率 | < 1% | 压力测试 |
| 内存使用 | < 2GB | 长时间运行测试 |
| CPU使用率 | < 80% | 峰值负载测试 |

## 测试自动化

### 1. CI/CD集成

```yaml
# .github/workflows/test.yml
name: 测试流水线
on: [push, pull_request]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: 运行单元测试
        run: |
          python -m pytest tests/unit/ -v --cov=app --cov-report=xml
      
  integration-tests:
    runs-on: ubuntu-latest
    needs: unit-tests
    steps:
      - uses: actions/checkout@v2
      - name: 启动测试环境
        run: docker-compose -f docker-compose.test.yml up -d
      - name: 运行集成测试
        run: python -m pytest tests/integration/ -v
      
  e2e-tests:
    runs-on: ubuntu-latest
    needs: integration-tests
    if: github.ref == 'refs/heads/main'
    steps:
      - name: 运行端到端测试
        run: python -m pytest tests/e2e/ -v --maxfail=1
```

### 2. 测试报告生成

```python
# tests/utils/report_generator.py
class TestReportGenerator:
    """测试报告生成器"""
    
    def generate_coverage_report(self):
        """生成覆盖率报告"""
        pass
    
    def generate_performance_report(self):
        """生成性能测试报告"""
        pass
    
    def generate_quality_metrics(self):
        """生成代码质量指标"""
        pass
```

## 下一步实施计划

1. **第一阶段** (Week 1-2): 核心单元测试实现
2. **第二阶段** (Week 3-4): API集成测试开发
3. **第三阶段** (Week 5-6): 交互式测试界面构建
4. **第四阶段** (Week 7-8): 端到端测试场景实现
5. **第五阶段** (Week 9-10): 性能测试和优化

---

*文档版本: v1.0*
*创建时间: 2025-01-27*
*负责人: AI Assistant*
