# 意图处理详解

## 概述

意图处理系统是AI助手的核心组件之一，负责理解用户输入的真实意图，并将其分发给相应的处理器。系统支持多种健身相关意图，采用模块化设计，易于扩展新的意图类型。

## 意图识别架构

### 1. 意图识别器基类

**位置**: `app/services/ai_assistant/intent/recognition/recognizer.py`

```python
class IntentResult:
    """意图识别结果"""
    
    def __init__(
        self, 
        intent: str, 
        confidence: float, 
        entities: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ):
        self.intent = intent
        self.confidence = confidence
        self.entities = entities or {}
        self.metadata = metadata or {}

class BaseIntentRecognizer(ABC):
    """意图识别器基类"""
    
    @abstractmethod
    async def arecognize(self, message: str) -> IntentResult:
        """异步意图识别"""
        pass
    
    @abstractmethod
    def recognize(self, message: str) -> IntentResult:
        """同步意图识别"""
        pass
    
    def preprocess_message(self, message: str) -> str:
        """消息预处理"""
        # 去除多余空格、标点符号标准化等
        return message.strip().lower()
```

### 2. LLM意图识别器

```python
class LLMIntentRecognizer(BaseIntentRecognizer):
    """基于LLM的意图识别器"""
    
    def __init__(self, llm_proxy: LLMProxy):
        self.llm_proxy = llm_proxy
        self.intent_definitions = self._load_intent_definitions()
    
    async def arecognize(self, message: str) -> IntentResult:
        """异步意图识别"""
        
        processed_message = self.preprocess_message(message)
        prompt = self._build_recognition_prompt(processed_message)
        
        try:
            response = await self.llm_proxy.generate_text(prompt)
            return self._parse_recognition_response(response)
        except Exception as e:
            logger.error(f"Intent recognition failed: {e}")
            return IntentResult("general_chat", 0.5)
    
    def _build_recognition_prompt(self, message: str) -> str:
        """构建意图识别提示词"""
        
        intent_descriptions = "\n".join([
            f"- {intent}: {desc}" 
            for intent, desc in self.intent_definitions.items()
        ])
        
        return f"""你是一个专业的意图识别系统，专门识别健身相关的用户意图。

支持的意图类型：
{intent_descriptions}

用户输入："{message}"

请分析用户的真实意图，并按以下JSON格式返回：
{{
    "intent": "意图名称",
    "confidence": 0.95,
    "entities": {{
        "body_part": "胸部",
        "exercise_type": "力量训练"
    }},
    "reasoning": "识别理由"
}}

要求：
1. confidence范围0.0-1.0
2. 如果不确定，选择general_chat
3. entities提取关键信息
4. reasoning简要说明识别理由

请返回JSON："""
    
    def _parse_recognition_response(self, response: str) -> IntentResult:
        """解析识别响应"""
        
        try:
            # 提取JSON部分
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if not json_match:
                raise ValueError("No JSON found in response")
            
            result_data = json.loads(json_match.group())
            
            return IntentResult(
                intent=result_data.get("intent", "general_chat"),
                confidence=float(result_data.get("confidence", 0.5)),
                entities=result_data.get("entities", {}),
                metadata={
                    "reasoning": result_data.get("reasoning", ""),
                    "raw_response": response
                }
            )
            
        except (json.JSONDecodeError, ValueError) as e:
            logger.error(f"Failed to parse intent recognition response: {e}")
            return IntentResult("general_chat", 0.5)
    
    def _load_intent_definitions(self) -> Dict[str, str]:
        """加载意图定义"""
        return {
            "fitness_advice": "用户寻求健身建议、训练指导或健康咨询",
            "training_plan": "用户想要制定训练计划或询问训练安排",
            "exercise_action": "用户询问具体运动动作、技巧或执行方法",
            "diet_advice": "用户寻求饮食建议、营养指导或膳食规划",
            "progress_tracking": "用户想要记录或查看训练进度、体重变化等",
            "equipment_advice": "用户询问健身器材选择、使用方法或购买建议",
            "injury_prevention": "用户关心运动伤害预防或康复相关问题",
            "general_chat": "一般性对话、问候或与健身无直接关系的内容"
        }
```

## 意图处理器系统

### 1. 处理器基类

**位置**: `app/services/ai_assistant/intent/handlers/base.py`

```python
class BaseIntentHandler(ABC):
    """意图处理器基类"""
    
    def __init__(
        self,
        llm_proxy: Optional[LLMProxy] = None,
        knowledge_retriever: Optional[KnowledgeRetriever] = None
    ):
        self.llm_proxy = llm_proxy
        self.knowledge_retriever = knowledge_retriever
        self.logger = logging.getLogger(f"handler.{self.__class__.__name__}")
    
    @abstractmethod
    async def handle(
        self,
        intent_result: IntentResult,
        message: str,
        user_context: Dict[str, Any],
        conversation_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """处理意图"""
        pass
    
    @abstractmethod
    async def handle_stream(
        self,
        intent_result: IntentResult,
        message: str,
        user_context: Dict[str, Any],
        conversation_context: Dict[str, Any]
    ) -> AsyncGenerator[Union[str, Dict[str, Any]], None]:
        """流式处理意图"""
        pass
    
    def extract_entities(self, message: str) -> Dict[str, Any]:
        """提取实体信息"""
        # 基础实体提取逻辑
        entities = {}
        
        # 身体部位识别
        body_parts = ["胸部", "背部", "腿部", "肩部", "手臂", "腹部", "臀部"]
        for part in body_parts:
            if part in message:
                entities["body_part"] = part
                break
        
        # 运动类型识别
        exercise_types = ["力量训练", "有氧运动", "拉伸", "瑜伽", "跑步", "游泳"]
        for ex_type in exercise_types:
            if ex_type in message:
                entities["exercise_type"] = ex_type
                break
        
        return entities
    
    def validate_user_context(self, user_context: Dict[str, Any]) -> bool:
        """验证用户上下文"""
        required_fields = ["user_id"]
        return all(field in user_context for field in required_fields)
```

### 2. 健身建议处理器

**位置**: `app/services/ai_assistant/intent/handlers/fitness_advice.py`

```python
class FitnessAdviceHandler(BaseIntentHandler):
    """健身建议处理器"""
    
    async def handle_stream(
        self,
        intent_result: IntentResult,
        message: str,
        user_context: Dict[str, Any],
        conversation_context: Dict[str, Any]
    ) -> AsyncGenerator[Union[str, Dict[str, Any]], None]:
        """流式处理健身建议"""
        
        # 发送处理开始信号
        yield {
            "type": "meta_info_update",
            "meta_info_update": {
                "intent": "fitness_advice",
                "confidence": intent_result.confidence,
                "processing": True
            }
        }
        
        # 构建专业提示词
        prompt = await self._build_advice_prompt(
            message, 
            user_context, 
            intent_result.entities
        )
        
        # 流式生成建议
        full_response = ""
        async for chunk in self.llm_proxy.astream(prompt):
            full_response += chunk
            yield {
                "type": "token",
                "content": chunk,
                "role": "assistant"
            }
        
        # 发送完整响应
        yield {
            "type": "message",
            "content": full_response,
            "role": "assistant",
            "intent": "fitness_advice",
            "entities": intent_result.entities
        }
        
        # 发送处理完成信号
        yield {
            "type": "meta_info_update",
            "meta_info_update": {
                "processing": False,
                "completed": True,
                "response_length": len(full_response)
            }
        }
    
    async def _build_advice_prompt(
        self, 
        message: str, 
        user_context: Dict[str, Any],
        entities: Dict[str, Any]
    ) -> str:
        """构建健身建议提示词"""
        
        # 获取用户信息
        user_profile = self._format_user_profile(user_context)
        
        # 获取相关知识
        knowledge_context = ""
        if self.knowledge_retriever:
            knowledge_results = await self.knowledge_retriever.retrieve(
                message, 
                limit=3
            )
            knowledge_context = self._format_knowledge_context(knowledge_results)
        
        # 分析实体信息
        entity_context = self._format_entity_context(entities)
        
        prompt = f"""你是一位资深的健身教练和运动科学专家，拥有多年的专业指导经验。

用户档案：
{user_profile}

用户问题："{message}"

识别的关键信息：
{entity_context}

相关专业知识：
{knowledge_context}

请根据用户的具体情况和问题，提供专业、个性化的健身建议。

回复要求：
1. 针对用户的具体情况（年龄、性别、健身目标等）给出个性化建议
2. 提供科学、安全的健身指导，避免可能导致受伤的建议
3. 语言专业但通俗易懂，避免过于技术性的术语
4. 如果涉及健康问题或特殊情况，建议咨询专业医生
5. 可以包含具体的动作推荐、训练频率、注意事项等
6. 回复结构清晰，使用适当的分段和要点

请开始你的专业建议："""
        
        return prompt
    
    def _format_user_profile(self, user_context: Dict[str, Any]) -> str:
        """格式化用户档案"""
        
        profile_parts = []
        
        # 基础信息
        if user_context.get('age'):
            profile_parts.append(f"年龄：{user_context['age']}岁")
        
        if user_context.get('gender'):
            gender_map = {1: '男性', 2: '女性', 0: '未知'}
            profile_parts.append(f"性别：{gender_map.get(user_context['gender'], '未知')}")
        
        # 身体指标
        if user_context.get('height') and user_context.get('weight'):
            height = user_context['height']
            weight = user_context['weight']
            bmi = weight / ((height / 100) ** 2)
            profile_parts.append(f"身高：{height}cm，体重：{weight}kg，BMI：{bmi:.1f}")
        
        # 健身目标
        if user_context.get('fitness_goal'):
            goal_map = {1: '减肥', 2: '保持体型', 3: '增肌', 4: '提高体能'}
            profile_parts.append(f"健身目标：{goal_map.get(user_context['fitness_goal'], '未设定')}")
        
        # 健身经验
        if user_context.get('experience_level'):
            level_map = {1: '初学者', 2: '中级', 3: '高级'}
            profile_parts.append(f"健身经验：{level_map.get(user_context['experience_level'], '未知')}")
        
        # 活动水平
        if user_context.get('activity_level'):
            activity_map = {
                1: '久坐不动', 2: '轻度活动', 3: '中度活动', 
                4: '高度活动', 5: '极高活动'
            }
            profile_parts.append(f"活动水平：{activity_map.get(user_context['activity_level'], '未知')}")
        
        return '\n'.join(profile_parts) if profile_parts else "用户信息不完整，请提供更多个人信息以获得更准确的建议"
    
    def _format_entity_context(self, entities: Dict[str, Any]) -> str:
        """格式化实体上下文"""
        
        if not entities:
            return "未识别到特定的健身相关信息"
        
        entity_parts = []
        
        if entities.get('body_part'):
            entity_parts.append(f"目标部位：{entities['body_part']}")
        
        if entities.get('exercise_type'):
            entity_parts.append(f"运动类型：{entities['exercise_type']}")
        
        if entities.get('equipment'):
            entity_parts.append(f"可用器材：{entities['equipment']}")
        
        if entities.get('time_constraint'):
            entity_parts.append(f"时间限制：{entities['time_constraint']}")
        
        return '\n'.join(entity_parts)
```

### 3. 训练计划处理器

```python
class TrainingPlanHandler(BaseIntentHandler):
    """训练计划处理器"""
    
    async def handle_stream(
        self,
        intent_result: IntentResult,
        message: str,
        user_context: Dict[str, Any],
        conversation_context: Dict[str, Any]
    ) -> AsyncGenerator[Union[str, Dict[str, Any]], None]:
        """流式处理训练计划"""
        
        # 发送处理开始信号
        yield {
            "type": "meta_info_update",
            "meta_info_update": {
                "intent": "training_plan",
                "confidence": intent_result.confidence,
                "processing": True
            }
        }
        
        # 检查是否需要收集更多信息
        missing_info = self._check_required_info(user_context, intent_result.entities)
        
        if missing_info:
            # 需要收集更多信息
            yield {
                "type": "parameter_collection",
                "missing_parameters": missing_info,
                "message": f"为了制定更适合您的训练计划，我需要了解一些信息：{', '.join(missing_info)}"
            }
            return
        
        # 生成训练计划
        prompt = await self._build_training_plan_prompt(
            message, 
            user_context, 
            intent_result.entities
        )
        
        # 流式生成计划
        full_response = ""
        async for chunk in self.llm_proxy.astream(prompt):
            full_response += chunk
            yield {
                "type": "token",
                "content": chunk,
                "role": "assistant"
            }
        
        # 发送完整计划
        yield {
            "type": "message",
            "content": full_response,
            "role": "assistant",
            "intent": "training_plan",
            "entities": intent_result.entities
        }
        
        # 发送处理完成信号
        yield {
            "type": "meta_info_update",
            "meta_info_update": {
                "processing": False,
                "completed": True,
                "plan_generated": True
            }
        }
    
    def _check_required_info(
        self, 
        user_context: Dict[str, Any], 
        entities: Dict[str, Any]
    ) -> List[str]:
        """检查制定训练计划所需的信息"""
        
        missing_info = []
        
        # 检查基础信息
        if not user_context.get('fitness_goal'):
            missing_info.append("健身目标")
        
        if not user_context.get('experience_level'):
            missing_info.append("健身经验水平")
        
        if not entities.get('training_frequency'):
            missing_info.append("每周训练频率")
        
        if not entities.get('available_time'):
            missing_info.append("每次训练可用时间")
        
        if not entities.get('training_location'):
            missing_info.append("训练地点（家庭/健身房）")
        
        return missing_info
```

## 意图处理器工厂

### 1. 工厂实现

**位置**: `app/services/ai_assistant/intent/handlers/factory.py`

```python
class IntentHandlerFactory:
    """意图处理器工厂"""
    
    _handlers: Dict[str, Type[BaseIntentHandler]] = {}
    _instances: Dict[str, BaseIntentHandler] = {}
    
    @classmethod
    def register_handler(cls, intent: str, handler_class: Type[BaseIntentHandler]):
        """注册意图处理器"""
        cls._handlers[intent] = handler_class
    
    @classmethod
    def create_handler(
        cls, 
        intent: str,
        llm_proxy: Optional[LLMProxy] = None,
        knowledge_retriever: Optional[KnowledgeRetriever] = None
    ) -> BaseIntentHandler:
        """创建意图处理器"""
        
        if intent not in cls._handlers:
            # 返回默认处理器
            intent = "general_chat"
        
        # 使用单例模式
        if intent not in cls._instances:
            handler_class = cls._handlers[intent]
            cls._instances[intent] = handler_class(
                llm_proxy=llm_proxy,
                knowledge_retriever=knowledge_retriever
            )
        
        return cls._instances[intent]
    
    @classmethod
    def get_supported_intents(cls) -> List[str]:
        """获取支持的意图列表"""
        return list(cls._handlers.keys())

# 注册默认处理器
IntentHandlerFactory.register_handler("fitness_advice", FitnessAdviceHandler)
IntentHandlerFactory.register_handler("training_plan", TrainingPlanHandler)
IntentHandlerFactory.register_handler("exercise_action", ExerciseActionHandler)
IntentHandlerFactory.register_handler("diet_advice", DietAdviceHandler)
IntentHandlerFactory.register_handler("general_chat", GeneralChatHandler)
```

## 意图处理流程

### 1. 完整处理流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Orch as 协调器
    participant Recog as 意图识别器
    participant Factory as 处理器工厂
    participant Handler as 意图处理器
    participant LLM as LLM服务

    User->>Orch: 发送消息
    Orch->>Recog: 识别意图
    Recog->>LLM: 调用LLM识别
    LLM-->>Recog: 返回意图结果
    Recog-->>Orch: IntentResult
    Orch->>Factory: 创建处理器
    Factory-->>Orch: 处理器实例
    Orch->>Handler: 处理意图
    Handler->>LLM: 生成响应
    LLM-->>Handler: 流式响应
    Handler-->>Orch: 流式结果
    Orch-->>User: 返回响应
```

### 2. 错误处理机制

```python
class RobustIntentProcessor:
    """健壮的意图处理器"""
    
    async def process_intent(
        self,
        message: str,
        user_context: Dict[str, Any],
        conversation_context: Dict[str, Any]
    ) -> AsyncGenerator[Union[str, Dict[str, Any]], None]:
        """处理意图（带错误处理）"""
        
        try:
            # 意图识别
            intent_result = await self.intent_recognizer.arecognize(message)
            
            # 置信度检查
            if intent_result.confidence < 0.3:
                intent_result.intent = "general_chat"
                intent_result.confidence = 0.5
            
            # 创建处理器
            handler = self.handler_factory.create_handler(
                intent_result.intent,
                llm_proxy=self.llm_proxy,
                knowledge_retriever=self.knowledge_retriever
            )
            
            # 流式处理
            async for chunk in handler.handle_stream(
                intent_result, message, user_context, conversation_context
            ):
                yield chunk
                
        except Exception as e:
            self.logger.error(f"Intent processing failed: {e}", exc_info=True)
            
            # 降级处理
            yield {
                "type": "error",
                "message": "抱歉，我遇到了一些问题。让我们重新开始对话吧。",
                "error_type": "processing_error"
            }
```

## 性能优化

### 1. 意图缓存

```python
class CachedIntentRecognizer(BaseIntentRecognizer):
    """带缓存的意图识别器"""
    
    def __init__(self, base_recognizer: BaseIntentRecognizer, cache_service: CacheService):
        self.base_recognizer = base_recognizer
        self.cache_service = cache_service
    
    async def arecognize(self, message: str) -> IntentResult:
        """带缓存的意图识别"""
        
        # 生成缓存键
        cache_key = f"intent:{hashlib.md5(message.encode()).hexdigest()}"
        
        # 检查缓存
        cached_result = await self.cache_service.get(cache_key)
        if cached_result:
            return IntentResult(**cached_result)
        
        # 执行识别
        result = await self.base_recognizer.arecognize(message)
        
        # 缓存结果
        await self.cache_service.set(
            cache_key,
            {
                "intent": result.intent,
                "confidence": result.confidence,
                "entities": result.entities,
                "metadata": result.metadata
            },
            ttl=600  # 10分钟缓存
        )
        
        return result
```

### 2. 批量处理

```python
class BatchIntentProcessor:
    """批量意图处理器"""
    
    async def process_batch(
        self, 
        messages: List[str]
    ) -> List[IntentResult]:
        """批量处理意图识别"""
        
        # 并发处理
        tasks = [
            self.intent_recognizer.arecognize(message) 
            for message in messages
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.logger.error(f"Batch processing failed for message {i}: {result}")
                processed_results.append(IntentResult("general_chat", 0.5))
            else:
                processed_results.append(result)
        
        return processed_results
```

---

*文档版本: v1.0*
*更新时间: 2025-01-27*
