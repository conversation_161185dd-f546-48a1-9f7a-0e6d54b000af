# 部署指南

## 概述

本文档提供智能健身AI助手系统的完整部署指南，包括开发环境、测试环境和生产环境的配置。

## 系统要求

### 1. 硬件要求

**最低配置**:
- CPU: 2核心
- 内存: 4GB RAM
- 存储: 20GB SSD
- 网络: 100Mbps

**推荐配置**:
- CPU: 4核心以上
- 内存: 8GB RAM以上
- 存储: 50GB SSD以上
- 网络: 1Gbps

**生产环境**:
- CPU: 8核心以上
- 内存: 16GB RAM以上
- 存储: 100GB SSD以上
- 网络: 1Gbps以上

### 2. 软件要求

- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / macOS 10.15+
- **Python**: 3.11+
- **PostgreSQL**: 13+
- **Redis**: 6.0+
- **Nginx**: 1.18+ (生产环境)

## 环境配置

### 1. Python环境

```bash
# 创建虚拟环境
python3.11 -m venv venv
source venv/bin/activate

# 升级pip
pip install --upgrade pip

# 安装依赖
pip install -r requirements.txt
```

### 2. 数据库配置

#### PostgreSQL安装和配置

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# CentOS/RHEL
sudo dnf install postgresql postgresql-server postgresql-contrib

# 初始化数据库
sudo postgresql-setup --initdb

# 启动服务
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

#### 数据库创建

```sql
-- 创建数据库用户
CREATE USER fitness_user WITH PASSWORD 'your_secure_password';

-- 创建数据库
CREATE DATABASE fitness_db OWNER fitness_user;

-- 授权
GRANT ALL PRIVILEGES ON DATABASE fitness_db TO fitness_user;
```

#### Redis安装和配置

```bash
# Ubuntu/Debian
sudo apt install redis-server

# CentOS/RHEL
sudo dnf install redis

# 启动服务
sudo systemctl start redis
sudo systemctl enable redis

# 测试连接
redis-cli ping
```

### 3. 环境变量配置

创建 `.env` 文件：

```bash
# 数据库配置
DATABASE_URL=postgresql://fitness_user:your_secure_password@localhost:5432/fitness_db

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# JWT配置
SECRET_KEY=your_super_secret_jwt_key_here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=43200

# 微信小程序配置
WECHAT_MINI_APPID=your_wechat_appid
WECHAT_MINI_SECRET=your_wechat_secret

# AI模型配置
QWEN_API_KEY=your_qwen_api_key
QWEN_API_BASE=https://dashscope.aliyuncs.com/compatible-mode/v1
LLM_MODEL=qwen-max
LLM_PROVIDER=qwen

# 应用配置
API_URL=http://localhost:8000
IS_DEV=false
LOG_LEVEL=INFO

# 静态文件目录
STATIC_DIR=/path/to/static/files
VECTOR_STORE_PATH=/path/to/vector/store
```

## 数据库迁移

### 1. 初始化Alembic

```bash
# 如果是首次部署
alembic init alembic

# 配置alembic.ini
# sqlalchemy.url = postgresql://fitness_user:password@localhost:5432/fitness_db
```

### 2. 执行迁移

```bash
# 生成迁移文件
alembic revision --autogenerate -m "Initial migration"

# 执行迁移
alembic upgrade head

# 验证迁移
alembic current
alembic history
```

### 3. 初始数据

```bash
# 创建初始数据
python scripts/init_data.py

# 导入运动数据
python scripts/import_exercises.py

# 创建管理员用户
python scripts/create_admin.py
```

## 应用部署

### 1. 开发环境

```bash
# 激活虚拟环境
source venv/bin/activate

# 启动开发服务器
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# 或使用脚本
python scripts/start_dev.py
```

### 2. 测试环境

```bash
# 使用Gunicorn启动
gunicorn app.main:app \
    --workers 2 \
    --worker-class uvicorn.workers.UvicornWorker \
    --bind 0.0.0.0:8000 \
    --timeout 120 \
    --keep-alive 2
```

### 3. 生产环境

#### 使用Systemd服务

创建服务文件 `/etc/systemd/system/fitness-api.service`：

```ini
[Unit]
Description=Fitness AI Assistant API
After=network.target postgresql.service redis.service

[Service]
Type=exec
User=fitness
Group=fitness
WorkingDirectory=/home/<USER>/app
Environment=PATH=/home/<USER>/app/venv/bin
EnvironmentFile=/home/<USER>/app/.env
ExecStart=/home/<USER>/app/venv/bin/gunicorn app.main:app \
    --workers 4 \
    --worker-class uvicorn.workers.UvicornWorker \
    --bind 127.0.0.1:8000 \
    --timeout 120 \
    --keep-alive 2 \
    --max-requests 1000 \
    --max-requests-jitter 100
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

启动服务：

```bash
# 重载systemd配置
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start fitness-api
sudo systemctl enable fitness-api

# 检查状态
sudo systemctl status fitness-api

# 查看日志
sudo journalctl -u fitness-api -f
```

## Nginx配置

### 1. 安装Nginx

```bash
# Ubuntu/Debian
sudo apt install nginx

# CentOS/RHEL
sudo dnf install nginx

# 启动服务
sudo systemctl start nginx
sudo systemctl enable nginx
```

### 2. 配置文件

创建 `/etc/nginx/sites-available/fitness-api`：

```nginx
upstream fitness_api {
    server 127.0.0.1:8000;
}

server {
    listen 80;
    server_name your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL配置
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
    
    # 客户端最大请求大小
    client_max_body_size 10M;
    
    # 静态文件
    location /static/ {
        alias /home/<USER>/app/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # API代理
    location / {
        proxy_pass http://fitness_api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 健康检查
    location /health {
        access_log off;
        proxy_pass http://fitness_api;
    }
}
```

启用配置：

```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/fitness-api /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重载配置
sudo systemctl reload nginx
```

## SSL证书配置

### 1. 使用Let's Encrypt

```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 2. 手动证书

```bash
# 生成私钥
openssl genrsa -out private.key 2048

# 生成证书签名请求
openssl req -new -key private.key -out cert.csr

# 安装证书文件到指定位置
sudo cp cert.pem /etc/ssl/certs/
sudo cp private.key /etc/ssl/private/
sudo chmod 600 /etc/ssl/private/private.key
```

## 监控和日志

### 1. 应用日志

```python
# app/core/logging.py
import logging
from logging.handlers import RotatingFileHandler

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        RotatingFileHandler('logs/app.log', maxBytes=10485760, backupCount=5),
        logging.StreamHandler()
    ]
)
```

### 2. 系统监控

```bash
# 安装监控工具
sudo apt install htop iotop nethogs

# 监控系统资源
htop
iotop
nethogs

# 监控服务状态
sudo systemctl status fitness-api
sudo systemctl status postgresql
sudo systemctl status redis
sudo systemctl status nginx
```

### 3. 日志管理

```bash
# 查看应用日志
tail -f logs/app.log

# 查看系统日志
sudo journalctl -u fitness-api -f
sudo journalctl -u nginx -f

# 日志轮转配置
sudo vim /etc/logrotate.d/fitness-api
```

## 备份策略

### 1. 数据库备份

```bash
#!/bin/bash
# backup_db.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/database"
DB_NAME="fitness_db"
DB_USER="fitness_user"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
pg_dump -U $DB_USER -h localhost $DB_NAME | gzip > $BACKUP_DIR/fitness_db_$DATE.sql.gz

# 删除7天前的备份
find $BACKUP_DIR -name "fitness_db_*.sql.gz" -mtime +7 -delete

echo "Database backup completed: fitness_db_$DATE.sql.gz"
```

### 2. 应用备份

```bash
#!/bin/bash
# backup_app.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/application"
APP_DIR="/home/<USER>/app"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份应用文件
tar -czf $BACKUP_DIR/app_$DATE.tar.gz -C $APP_DIR \
    --exclude='venv' \
    --exclude='__pycache__' \
    --exclude='*.pyc' \
    --exclude='logs' \
    .

echo "Application backup completed: app_$DATE.tar.gz"
```

### 3. 自动备份

```bash
# 添加到crontab
crontab -e

# 每天凌晨2点备份数据库
0 2 * * * /home/<USER>/scripts/backup_db.sh

# 每周日凌晨3点备份应用
0 3 * * 0 /home/<USER>/scripts/backup_app.sh
```

## 性能优化

### 1. 数据库优化

```sql
-- PostgreSQL配置优化
-- /etc/postgresql/13/main/postgresql.conf

shared_buffers = 256MB
effective_cache_size = 1GB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200
```

### 2. Redis优化

```bash
# /etc/redis/redis.conf
maxmemory 512mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### 3. 应用优化

```python
# Gunicorn配置
workers = multiprocessing.cpu_count() * 2 + 1
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 120
keepalive = 2
```

## 故障排除

### 1. 常见问题

**数据库连接失败**:
```bash
# 检查PostgreSQL状态
sudo systemctl status postgresql

# 检查连接
psql -U fitness_user -d fitness_db -h localhost

# 查看日志
sudo tail -f /var/log/postgresql/postgresql-13-main.log
```

**Redis连接失败**:
```bash
# 检查Redis状态
sudo systemctl status redis

# 测试连接
redis-cli ping

# 查看日志
sudo tail -f /var/log/redis/redis-server.log
```

**应用启动失败**:
```bash
# 检查服务状态
sudo systemctl status fitness-api

# 查看详细日志
sudo journalctl -u fitness-api -n 50

# 手动启动测试
cd /home/<USER>/app
source venv/bin/activate
python -m app.main
```

### 2. 性能问题

**高CPU使用率**:
- 检查数据库查询性能
- 优化AI模型调用频率
- 增加缓存使用

**高内存使用率**:
- 检查内存泄漏
- 优化数据库连接池
- 调整worker数量

**响应时间慢**:
- 检查数据库索引
- 优化API查询
- 增加缓存层

---

*文档版本: v1.0*
*更新时间: 2025-01-27*
