# 状态管理详解

## 概述

新版AI助手系统采用状态机架构进行对话状态管理，提供清晰的状态定义、转换逻辑和上下文管理。状态机设计使得系统更易于理解、测试和扩展。

## 状态机架构

### 1. 状态基类

**位置**: `app/services/ai_assistant/conversation/states/base.py`

```python
class ConversationState(ABC):
    """对话状态抽象基类"""
    
    def __init__(self, context: Dict[str, Any]):
        self.context = context
        self.name = self.__class__.__name__.lower().replace('state', '')
        self.logger = logging.getLogger(f"state.{self.name}")
    
    @abstractmethod
    async def handle_message(
        self, 
        message: str, 
        intent: str, 
        user_info: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """处理消息的抽象方法"""
        pass
        
    @abstractmethod
    async def handle_message_stream(
        self, 
        message: str, 
        intent: str, 
        user_info: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[Union[str, Dict[str, Any]], None]:
        """流式处理消息的抽象方法"""
        pass
    
    def can_transition_to(self, target_state: str) -> bool:
        """检查是否可以转换到目标状态"""
        return target_state in self.get_allowed_transitions()
    
    @abstractmethod
    def get_allowed_transitions(self) -> List[str]:
        """获取允许的状态转换"""
        pass
```

### 2. 状态工厂

```python
class ConversationStateFactory:
    """对话状态工厂"""
    
    _state_classes: Dict[str, Type[ConversationState]] = {}
    
    @classmethod
    def register_state(cls, name: str, state_class: Type[ConversationState]):
        """注册状态类"""
        cls._state_classes[name] = state_class
    
    @classmethod
    def create_state(cls, state_name: str, context: Dict[str, Any]) -> ConversationState:
        """创建状态实例"""
        if state_name not in cls._state_classes:
            raise ValueError(f"Unknown state: {state_name}")
        return cls._state_classes[state_name](context)
    
    @classmethod
    def get_available_states(cls) -> List[str]:
        """获取可用状态列表"""
        return list(cls._state_classes.keys())
```

## 具体状态实现

### 1. 空闲状态 (IdleState)

**位置**: `app/services/ai_assistant/conversation/states/idle.py`

```python
class IdleState(ConversationState):
    """空闲状态 - 处理一般性对话和意图分发"""
    
    def __init__(self, context: Dict[str, Any]):
        super().__init__(context)
        self.llm_proxy = context.get('llm_proxy')
        self.intent_recognizer = context.get('intent_recognizer')
    
    async def handle_message(
        self, 
        message: str, 
        intent: str, 
        user_info: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """处理消息"""
        
        # 根据意图决定处理方式
        if intent == "fitness_advice":
            return await self._handle_fitness_advice(message, user_info)
        elif intent == "general_chat":
            return await self._handle_general_chat(message, user_info)
        else:
            return await self._handle_unknown_intent(message, intent, user_info)
    
    async def handle_message_stream(
        self, 
        message: str, 
        intent: str, 
        user_info: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[Union[str, Dict[str, Any]], None]:
        """流式处理消息"""
        
        # 发送意图识别结果
        yield {
            "type": "meta_info_update",
            "meta_info_update": {
                "intent": intent,
                "state": self.name,
                "processing": True
            }
        }
        
        # 根据意图进行流式处理
        if intent == "fitness_advice":
            async for chunk in self._handle_fitness_advice_stream(message, user_info):
                yield chunk
        else:
            async for chunk in self._handle_general_chat_stream(message, user_info):
                yield chunk
        
        # 发送处理完成信号
        yield {
            "type": "meta_info_update",
            "meta_info_update": {
                "processing": False,
                "completed": True
            }
        }
    
    def get_allowed_transitions(self) -> List[str]:
        """获取允许的状态转换"""
        return ["fitness_advice", "training_plan", "diet_advice"]
    
    async def _handle_general_chat(self, message: str, user_info: Dict[str, Any]) -> Dict[str, Any]:
        """处理一般对话"""
        
        prompt = f"""你是一个友好的健身AI助手。用户说："{message}"
        
请给出自然、友好的回复。如果用户询问健身相关问题，可以提供基础建议。
        
回复要求：
- 语气友好自然
- 回复简洁明了
- 如果涉及专业健身问题，建议用户详细咨询
        """
        
        response = await self.llm_proxy.generate_text(prompt)
        
        return {
            "response": response,
            "intent": "general_chat",
            "state": self.name,
            "next_state": None
        }
```

### 2. 健身建议状态 (FitnessAdviceState)

**位置**: `app/services/ai_assistant/conversation/states/fitness_advice.py`

```python
class FitnessAdviceState(ConversationState):
    """健身建议状态 - 提供专业健身指导"""
    
    def __init__(self, context: Dict[str, Any]):
        super().__init__(context)
        self.llm_proxy = context.get('llm_proxy')
        self.knowledge_retriever = context.get('knowledge_retriever')
        self.use_bailian = context.get('use_bailian', True)
    
    async def handle_message_stream(
        self, 
        message: str, 
        intent: str, 
        user_info: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[Union[str, Dict[str, Any]], None]:
        """流式处理健身建议"""
        
        # 构建专业提示词
        prompt = await self._build_prompt(message, user_info)
        
        # 发送开始处理信号
        yield {
            "type": "meta_info_update",
            "meta_info_update": {
                "intent": "fitness_advice",
                "state": self.name,
                "processing": True
            }
        }
        
        # 流式生成响应
        full_response = ""
        async for chunk in self.llm_proxy.astream(prompt):
            full_response += chunk
            yield {
                "type": "token",
                "content": chunk,
                "role": "assistant"
            }
        
        # 发送完整消息
        yield {
            "type": "message",
            "content": full_response,
            "role": "assistant",
            "intent": "fitness_advice"
        }
        
        # 发送处理完成信号
        yield {
            "type": "meta_info_update",
            "meta_info_update": {
                "processing": False,
                "completed": True,
                "response_length": len(full_response)
            }
        }
    
    async def _build_prompt(self, message: str, user_info: Dict[str, Any]) -> str:
        """构建健身建议提示词"""
        
        # 获取用户基础信息
        user_profile = self._format_user_profile(user_info)
        
        # 检索相关知识
        knowledge_context = ""
        if self.knowledge_retriever:
            knowledge_results = await self.knowledge_retriever.retrieve(message)
            knowledge_context = self._format_knowledge_context(knowledge_results)
        
        # 构建提示词
        prompt = f"""你是一位专业的健身教练和营养师，具有丰富的健身指导经验。

用户信息：
{user_profile}

用户问题："{message}"

{knowledge_context}

请根据用户的具体情况，提供专业、个性化的健身建议。

回复要求：
1. 针对用户的具体情况给出个性化建议
2. 提供科学、安全的健身指导
3. 语言专业但易懂，避免过于技术性的术语
4. 如果涉及健康问题，建议咨询专业医生
5. 回复结构清晰，可以包含具体的动作推荐、训练计划等

请开始你的专业建议："""
        
        return prompt
    
    def _format_user_profile(self, user_info: Dict[str, Any]) -> str:
        """格式化用户档案"""
        
        profile_parts = []
        
        if user_info.get('age'):
            profile_parts.append(f"年龄：{user_info['age']}岁")
        
        if user_info.get('gender'):
            gender_map = {1: '男性', 2: '女性'}
            profile_parts.append(f"性别：{gender_map.get(user_info['gender'], '未知')}")
        
        if user_info.get('height') and user_info.get('weight'):
            height = user_info['height']
            weight = user_info['weight']
            bmi = weight / ((height / 100) ** 2)
            profile_parts.append(f"身高：{height}cm，体重：{weight}kg，BMI：{bmi:.1f}")
        
        if user_info.get('fitness_goal'):
            goal_map = {1: '减肥', 2: '保持体型', 3: '增肌'}
            profile_parts.append(f"健身目标：{goal_map.get(user_info['fitness_goal'], '未设定')}")
        
        if user_info.get('experience_level'):
            level_map = {1: '初学者', 2: '中级', 3: '高级'}
            profile_parts.append(f"健身经验：{level_map.get(user_info['experience_level'], '未知')}")
        
        return '\n'.join(profile_parts) if profile_parts else "用户信息不完整"
    
    def get_allowed_transitions(self) -> List[str]:
        """获取允许的状态转换"""
        return ["idle", "training_plan", "diet_advice"]
```

## 状态管理器

### 1. 状态管理器实现

**位置**: `app/services/ai_assistant/conversation/states/manager.py`

```python
class ConversationStateManager:
    """对话状态管理器"""
    
    def __init__(self):
        self.conversations: Dict[str, Dict[str, Any]] = {}
        self.state_factory = ConversationStateFactory()
        self.logger = logging.getLogger("state_manager")
    
    async def get_current_state(self, conversation_id: str) -> ConversationState:
        """获取当前状态"""
        
        conversation = self.get_or_create_conversation(conversation_id)
        current_state_name = conversation.get('current_state', 'idle')
        
        # 创建状态实例
        context = self._build_state_context(conversation)
        return self.state_factory.create_state(current_state_name, context)
    
    async def transition_state(
        self, 
        conversation_id: str, 
        intent: str, 
        message: str, 
        response: str
    ) -> ConversationState:
        """状态转换"""
        
        conversation = self.get_or_create_conversation(conversation_id)
        current_state_name = conversation.get('current_state', 'idle')
        
        # 确定目标状态
        target_state = self._determine_target_state(intent, current_state_name)
        
        # 检查转换是否允许
        current_state = await self.get_current_state(conversation_id)
        if not current_state.can_transition_to(target_state):
            self.logger.warning(
                f"Invalid state transition: {current_state_name} -> {target_state}"
            )
            target_state = current_state_name
        
        # 执行状态转换
        if target_state != current_state_name:
            self.logger.info(
                f"State transition: {current_state_name} -> {target_state} "
                f"for conversation {conversation_id}"
            )
            
            conversation['current_state'] = target_state
            conversation['state_history'].append({
                'from_state': current_state_name,
                'to_state': target_state,
                'intent': intent,
                'timestamp': time.time(),
                'message': message[:100]  # 截断消息用于日志
            })
        
        # 更新上下文
        self._update_conversation_context(conversation_id, intent, message, response)
        
        # 返回新状态
        context = self._build_state_context(conversation)
        return self.state_factory.create_state(target_state, context)
    
    def get_or_create_conversation(self, conversation_id: str) -> Dict[str, Any]:
        """获取或创建对话上下文"""
        
        if conversation_id not in self.conversations:
            self.conversations[conversation_id] = {
                'id': conversation_id,
                'current_state': 'idle',
                'created_at': time.time(),
                'last_active': time.time(),
                'message_count': 0,
                'state_history': [],
                'context': {},
                'user_info': {},
                'meta_info': {}
            }
            
            self.logger.info(f"Created new conversation: {conversation_id}")
        
        # 更新最后活跃时间
        self.conversations[conversation_id]['last_active'] = time.time()
        
        return self.conversations[conversation_id]
    
    def _determine_target_state(self, intent: str, current_state: str) -> str:
        """确定目标状态"""
        
        # 意图到状态的映射
        intent_state_map = {
            'fitness_advice': 'fitness_advice',
            'training_plan': 'training_plan',
            'diet_advice': 'diet_advice',
            'exercise_action': 'exercise_action',
            'general_chat': 'idle'
        }
        
        target_state = intent_state_map.get(intent, current_state)
        
        # 特殊转换逻辑
        if current_state == 'fitness_advice' and intent == 'general_chat':
            # 从健身建议状态回到空闲状态
            target_state = 'idle'
        
        return target_state
    
    def _build_state_context(self, conversation: Dict[str, Any]) -> Dict[str, Any]:
        """构建状态上下文"""
        
        from app.services.ai_assistant.llm.factory import LLMProxyFactory
        from app.services.ai_assistant.knowledge.retriever import KnowledgeRetriever
        
        return {
            'conversation_id': conversation['id'],
            'conversation': conversation,
            'llm_proxy': LLMProxyFactory.get_provider('qwen'),
            'knowledge_retriever': KnowledgeRetriever(),
            'use_bailian': True
        }
    
    def _update_conversation_context(
        self, 
        conversation_id: str, 
        intent: str, 
        message: str, 
        response: str
    ):
        """更新对话上下文"""
        
        conversation = self.conversations[conversation_id]
        conversation['message_count'] += 1
        
        # 更新上下文信息
        conversation['context']['last_intent'] = intent
        conversation['context']['last_message'] = message
        conversation['context']['last_response'] = response
        
        # 保持最近的对话历史（最多10条）
        if 'recent_messages' not in conversation['context']:
            conversation['context']['recent_messages'] = []
        
        conversation['context']['recent_messages'].append({
            'message': message,
            'response': response,
            'intent': intent,
            'timestamp': time.time()
        })
        
        # 保持最近10条消息
        if len(conversation['context']['recent_messages']) > 10:
            conversation['context']['recent_messages'] = \
                conversation['context']['recent_messages'][-10:]
```

## 状态转换图

```mermaid
stateDiagram-v2
    [*] --> Idle
    
    Idle --> FitnessAdvice : fitness_advice
    Idle --> TrainingPlan : training_plan
    Idle --> DietAdvice : diet_advice
    Idle --> ExerciseAction : exercise_action
    
    FitnessAdvice --> Idle : general_chat
    FitnessAdvice --> TrainingPlan : training_plan
    FitnessAdvice --> DietAdvice : diet_advice
    
    TrainingPlan --> Idle : general_chat
    TrainingPlan --> FitnessAdvice : fitness_advice
    TrainingPlan --> ExerciseAction : exercise_action
    
    DietAdvice --> Idle : general_chat
    DietAdvice --> FitnessAdvice : fitness_advice
    DietAdvice --> TrainingPlan : training_plan
    
    ExerciseAction --> Idle : general_chat
    ExerciseAction --> FitnessAdvice : fitness_advice
    ExerciseAction --> TrainingPlan : training_plan
    
    FitnessAdvice --> [*] : session_end
    TrainingPlan --> [*] : session_end
    DietAdvice --> [*] : session_end
    ExerciseAction --> [*] : session_end
    Idle --> [*] : session_end
```

## 上下文管理

### 1. 对话上下文结构

```python
conversation_context = {
    'id': 'conversation_uuid',
    'current_state': 'fitness_advice',
    'created_at': 1640995200.0,
    'last_active': 1640995800.0,
    'message_count': 5,
    'state_history': [
        {
            'from_state': 'idle',
            'to_state': 'fitness_advice',
            'intent': 'fitness_advice',
            'timestamp': 1640995500.0,
            'message': '我想减肥，应该怎么做？'
        }
    ],
    'context': {
        'last_intent': 'fitness_advice',
        'last_message': '我想减肥，应该怎么做？',
        'last_response': '减肥需要结合饮食控制和运动...',
        'recent_messages': [...]
    },
    'user_info': {
        'age': 25,
        'gender': 1,
        'height': 170,
        'weight': 70,
        'fitness_goal': 1
    },
    'meta_info': {
        'session_type': 'fitness_consultation',
        'preferred_language': 'zh-CN'
    }
}
```

### 2. 上下文持久化

```python
class PersistentStateManager(ConversationStateManager):
    """持久化状态管理器"""
    
    def __init__(self, cache_service: CacheService):
        super().__init__()
        self.cache_service = cache_service
    
    async def get_or_create_conversation(self, conversation_id: str) -> Dict[str, Any]:
        """从缓存获取或创建对话"""
        
        # 尝试从缓存获取
        cached_conversation = await self.cache_service.get(
            f"conversation:{conversation_id}"
        )
        
        if cached_conversation:
            self.conversations[conversation_id] = cached_conversation
            return cached_conversation
        
        # 创建新对话
        conversation = super().get_or_create_conversation(conversation_id)
        
        # 保存到缓存
        await self.cache_service.set(
            f"conversation:{conversation_id}",
            conversation,
            ttl=3600  # 1小时过期
        )
        
        return conversation
    
    async def save_conversation(self, conversation_id: str):
        """保存对话到缓存"""
        
        if conversation_id in self.conversations:
            await self.cache_service.set(
                f"conversation:{conversation_id}",
                self.conversations[conversation_id],
                ttl=3600
            )
```

## 状态扩展

### 1. 添加新状态

```python
class CustomState(ConversationState):
    """自定义状态示例"""
    
    def __init__(self, context: Dict[str, Any]):
        super().__init__(context)
        # 自定义初始化
    
    async def handle_message(self, message: str, intent: str, user_info: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """处理消息"""
        # 实现自定义逻辑
        return {
            "response": "自定义响应",
            "intent": intent,
            "state": self.name
        }
    
    async def handle_message_stream(self, message: str, intent: str, user_info: Optional[Dict[str, Any]] = None):
        """流式处理"""
        yield "自定义流式响应"
    
    def get_allowed_transitions(self) -> List[str]:
        """允许的状态转换"""
        return ["idle", "fitness_advice"]

# 注册新状态
ConversationStateFactory.register_state("custom", CustomState)
```

### 2. 状态中间件

```python
class StateMiddleware:
    """状态中间件基类"""
    
    async def before_handle(self, state: ConversationState, message: str, intent: str):
        """处理前钩子"""
        pass
    
    async def after_handle(self, state: ConversationState, response: Dict[str, Any]):
        """处理后钩子"""
        pass

class LoggingStateMiddleware(StateMiddleware):
    """日志中间件"""
    
    async def before_handle(self, state: ConversationState, message: str, intent: str):
        logger.info(f"State {state.name} handling message: {message[:50]}...")
    
    async def after_handle(self, state: ConversationState, response: Dict[str, Any]):
        logger.info(f"State {state.name} response: {response.get('response', '')[:50]}...")
```

---

*文档版本: v1.0*
*更新时间: 2025-01-27*
