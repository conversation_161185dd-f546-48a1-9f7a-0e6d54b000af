# 智能健身AI助手 - 架构总览

## 概述

智能健身AI助手系统采用分层架构设计，当前处于架构过渡期，同时支持两套AI助手实现：

- **新版AI助手系统** (`app/services/ai_assistant/`) - 基于状态机的现代化实现 ✅ **推荐使用**
- **旧版对话系统** (`app/services/conversation/`) - 基于意图处理器的传统实现 ⚠️ **逐步废弃**

## 整体架构图

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                              客户端层                                        │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │  微信小程序  │  │   Web应用   │  │  移动应用   │  │   管理后台   │        │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                              API网关层                                       │
│  ┌─────────────────────────────────────────────────────────────────────────┐ │
│  │                    FastAPI + Uvicorn                                   │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  │ │
│  │  │  认证中间件  │  │  CORS中间件 │  │  限流中间件  │  │  日志中间件  │  │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘  │ │
│  └─────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                              API路由层                                       │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │  用户管理   │  │  聊天对话   │  │  训练计划   │  │  健康数据   │        │
│  │   /user     │  │   /chat     │  │  /training  │  │   /health   │        │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                            AI助手系统层                                      │
│  ┌─────────────────────────────────┐  ┌─────────────────────────────────┐  │
│  │         新版AI助手系统           │  │         旧版对话系统            │  │
│  │    (ai_assistant) ✅推荐        │  │    (conversation) ⚠️废弃        │  │
│  │  ┌─────────────────────────────┐ │  │  ┌─────────────────────────────┐ │  │
│  │  │     ConversationOrchestrator │ │  │  │     ConversationOrchestrator │ │  │
│  │  │         (状态机架构)         │ │  │  │        (意图处理架构)        │ │  │
│  │  └─────────────────────────────┘ │  │  └─────────────────────────────┘ │  │
│  │  ┌─────────────────────────────┐ │  │  ┌─────────────────────────────┐ │  │
│  │  │       状态管理器            │ │  │  │       意图处理器            │ │  │
│  │  │   • IdleState              │ │  │  │   • ExerciseHandler         │ │  │
│  │  │   • FitnessAdviceState     │ │  │  │   • TrainingPlanHandler     │ │  │
│  │  └─────────────────────────────┘ │  │  └─────────────────────────────┘ │  │
│  └─────────────────────────────────┘  └─────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                            共享服务层                                        │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │  LLM提供商  │  │  知识检索   │  │  缓存服务   │  │  参数处理   │        │
│  │   服务      │  │   服务      │  │             │  │   服务      │        │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                            数据存储层                                        │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ PostgreSQL  │  │    Redis    │  │    FAISS    │  │  静态文件   │        │
│  │  主数据库   │  │    缓存     │  │  向量数据库  │  │   存储      │        │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────────────────┐
│                            外部服务层                                        │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │  通义千问   │  │   OpenAI    │  │ DashScope   │  │   微信API   │        │
│  │    API      │  │    API      │  │    API      │  │             │        │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────────────────────┘
```

## 核心设计原则

### 1. 分层解耦
- **API层**: 处理HTTP请求和响应格式化
- **业务层**: AI助手逻辑和状态管理
- **服务层**: 可复用的业务服务
- **数据层**: 数据持久化和缓存

### 2. 模块化设计
- 每个模块职责单一，边界清晰
- 通过依赖注入实现松耦合
- 支持模块独立测试和部署

### 3. 可扩展性
- 工厂模式支持新LLM提供商
- 状态机模式支持新对话状态
- 插件化架构支持功能扩展

### 4. 高可用性
- 异步处理提高并发能力
- 多级缓存减少延迟
- 错误处理和降级机制

## 技术选型

### 后端框架
- **FastAPI**: 现代Python Web框架，支持异步和自动API文档
- **SQLAlchemy**: ORM框架，支持多种数据库
- **Pydantic**: 数据验证和序列化

### AI框架
- **LangChain**: AI应用开发框架
- **自研状态机**: 轻量级对话状态管理

### 数据存储
- **PostgreSQL**: 主数据库，存储用户和业务数据
- **Redis**: 缓存和会话存储
- **FAISS**: 向量数据库，支持语义搜索

### 外部服务
- **通义千问**: 主要LLM提供商
- **OpenAI**: 备用LLM提供商
- **微信API**: 用户认证和小程序集成

## 数据流向

### 1. 用户消息处理流程
```
用户输入 → API层 → 消息持久化 → AI助手系统 → LLM调用 → 响应生成 → 响应持久化 → 返回用户
```

### 2. 流式响应流程
```
WebSocket连接 → 消息接收 → 状态机处理 → LLM流式调用 → 实时推送 → 完整响应保存
```

### 3. 缓存策略
```
请求 → 缓存检查 → 缓存命中(返回) / 缓存未命中(处理) → 结果缓存 → 返回响应
```

## 性能特性

### 1. 响应时间
- **API响应**: < 100ms (缓存命中)
- **LLM调用**: 1-3秒 (首token)
- **流式响应**: 实时推送

### 2. 并发能力
- **异步处理**: 支持高并发请求
- **连接池**: 数据库和Redis连接复用
- **负载均衡**: 支持水平扩展

### 3. 缓存效率
- **多级缓存**: 内存 + Redis + 数据库
- **智能失效**: 基于TTL和业务逻辑
- **预热机制**: 常用数据预加载

## 安全机制

### 1. 认证授权
- **JWT Token**: 无状态认证
- **微信OAuth**: 第三方登录
- **权限控制**: 基于角色的访问控制

### 2. 数据安全
- **数据加密**: 敏感数据加密存储
- **SQL注入防护**: ORM参数化查询
- **XSS防护**: 输入输出过滤

### 3. API安全
- **CORS配置**: 跨域请求控制
- **限流机制**: 防止API滥用
- **日志审计**: 完整的操作日志

## 监控和运维

### 1. 日志系统
- **结构化日志**: JSON格式便于分析
- **分级日志**: DEBUG/INFO/WARN/ERROR
- **链路追踪**: 请求ID跟踪

### 2. 监控指标
- **系统指标**: CPU、内存、磁盘
- **业务指标**: 响应时间、成功率
- **AI指标**: LLM调用次数、成本

### 3. 告警机制
- **阈值告警**: 基于指标阈值
- **异常告警**: 错误率异常
- **业务告警**: 关键业务流程异常

---

*文档版本: v1.0*
*更新时间: 2025-01-27*
