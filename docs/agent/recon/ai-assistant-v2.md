# 新版AI助手系统 (推荐)

## 概述

新版AI助手系统位于 `app/services/ai_assistant/`，采用现代化的状态机架构，提供更清晰的对话流程管理和更好的可扩展性。

**推荐理由**:
- ✅ 清晰的状态机架构
- ✅ 优秀的流式处理支持
- ✅ 模块化设计，易于扩展
- ✅ 更好的性能和缓存机制
- ✅ 简洁的代码结构

## 核心组件

### 1. 对话协调器 (ConversationOrchestrator)

**位置**: `app/services/ai_assistant/conversation/orchestrator.py`

#### 1.1 主要功能

```python
class ConversationOrchestrator:
    """新版对话系统的主要入口，基于状态机架构"""
    
    async def process_message(
        self, 
        message: str, 
        conversation_id: str,
        user_info: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """标准消息处理流程"""
        
    async def process_message_stream(
        self, 
        user_input: str, 
        conversation_id: str,
        user_id: Optional[str] = None,
        meta_info: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[Union[str, Dict[str, Any]], None]:
        """流式消息处理流程"""
```

#### 1.2 处理流程

1. **缓存检查**: 首先检查是否有缓存的响应
2. **状态获取**: 获取当前对话状态
3. **意图识别**: 识别用户意图和置信度
4. **消息处理**: 委托给当前状态处理
5. **状态转换**: 根据需要转换到新状态
6. **结果缓存**: 缓存处理结果

#### 1.3 依赖组件

```python
def __init__(
    self,
    intent_recognizer: Optional[BaseIntentRecognizer] = None,
    handler_factory: Optional[IntentHandlerFactory] = None,
    llm_proxy: Optional[LLMProxy] = None,
    knowledge_retriever: Optional[KnowledgeRetriever] = None,
    cache_service: Optional[CacheService] = None,
    use_bailian: bool = True
):
```

### 2. 状态管理系统

**位置**: `app/services/ai_assistant/conversation/states/`

#### 2.1 状态基类

```python
class ConversationState(ABC):
    """对话状态抽象基类"""
    
    def __init__(self, context: Dict[str, Any]):
        self.context = context
        self.name = self.__class__.__name__.lower().replace('state', '')
    
    @abstractmethod
    async def handle_message(
        self, 
        message: str, 
        intent: str, 
        user_info: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """处理消息的抽象方法"""
        
    @abstractmethod
    async def handle_message_stream(
        self, 
        message: str, 
        intent: str, 
        user_info: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[Union[str, Dict[str, Any]], None]:
        """流式处理消息的抽象方法"""
```

#### 2.2 具体状态实现

**空闲状态 (IdleState)**:
- 处理一般性对话
- 意图识别和分发
- 状态转换决策

**健身建议状态 (FitnessAdviceState)**:
- 专业健身指导
- 个性化建议生成
- 知识库检索集成

#### 2.3 状态管理器

```python
class ConversationStateManager:
    """对话状态管理器"""
    
    async def get_current_state(self, conversation_id: str) -> ConversationState:
        """获取当前状态"""
        
    async def transition_state(
        self, 
        conversation_id: str, 
        intent: str, 
        message: str, 
        response: str
    ) -> ConversationState:
        """状态转换"""
        
    def get_or_create_conversation(self, conversation_id: str) -> Dict[str, Any]:
        """获取或创建对话上下文"""
```

### 3. 意图处理系统

**位置**: `app/services/ai_assistant/intent/`

#### 3.1 意图识别器

```python
class BaseIntentRecognizer(ABC):
    """意图识别器基类"""
    
    @abstractmethod
    async def arecognize(self, message: str) -> IntentResult:
        """异步意图识别"""
```

#### 3.2 意图处理器

**处理器工厂**:
```python
class IntentHandlerFactory:
    """意图处理器工厂"""
    
    @classmethod
    def create_handler(cls, intent_type: str) -> BaseIntentHandler:
        """创建意图处理器"""
```

**具体处理器**:
- `FitnessAdviceHandler`: 健身建议处理
- `TrainingPlanHandler`: 训练计划处理
- `ExerciseActionHandler`: 运动动作处理
- `GeneralChatHandler`: 一般对话处理

### 4. LLM集成系统

**位置**: `app/services/ai_assistant/llm/`

#### 4.1 LLM代理基类

```python
class LLMProxy(ABC):
    """LLM代理抽象基类"""
    
    @abstractmethod
    async def astream(
        self, 
        prompt: str, 
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """流式生成接口"""
        
    @abstractmethod
    async def generate_text(
        self, 
        prompt: str, 
        **kwargs
    ) -> str:
        """文本生成接口"""
```

#### 4.2 提供商实现

**通义千问代理 (QwenLLMProxy)**:
```python
class QwenLLMProxy(LLMProxy):
    """通义千问LLM代理实现"""
    
    async def astream(self, prompt: str, **kwargs):
        """流式生成实现"""
        
    async def chat(self, messages: List[Dict], **kwargs):
        """对话接口"""
        
    async def get_embeddings(self, texts: List[str]):
        """嵌入向量生成"""
```

#### 4.3 工厂模式

```python
class LLMProxyFactory:
    """LLM提供商工厂"""
    
    @classmethod
    def get_provider(cls, provider_name: str) -> LLMProxy:
        """获取提供商实例"""
        
    @classmethod
    def load_providers(cls):
        """加载所有提供商"""
```

### 5. 知识检索系统

**位置**: `app/services/ai_assistant/knowledge/`

#### 5.1 知识检索器

```python
class KnowledgeRetriever:
    """知识检索器"""
    
    async def retrieve(
        self, 
        query: str, 
        limit: int = 5,
        use_cache: bool = True
    ) -> List[Dict[str, Any]]:
        """检索相关知识"""
```

#### 5.2 向量存储

```python
class VectorStore:
    """向量存储管理"""
    
    def add_documents(self, documents: List[str]):
        """添加文档"""
        
    def similarity_search(self, query: str, k: int = 5):
        """相似性搜索"""
```

## 流式处理机制

### 1. 流式响应类型

```python
# 文本片段
yield "这是一个文本片段"

# 结构化数据
yield {
    "type": "token",
    "content": "文本内容",
    "meta_info": {}
}

# 元数据更新
yield {
    "type": "meta_info_update",
    "meta_info_update": {
        "intent": "fitness_advice",
        "confidence": 0.9
    }
}

# 完整消息
yield {
    "type": "message",
    "content": "完整的响应内容",
    "role": "assistant"
}
```

### 2. 流式处理流程

```python
async def handle_message_stream(self, message, intent, user_info):
    """流式处理示例"""
    
    # 1. 构建提示词
    prompt = self._build_prompt(message, user_info)
    
    # 2. 流式调用LLM
    async for chunk in self.llm_proxy.astream(prompt):
        yield {
            "type": "token",
            "content": chunk,
            "role": "assistant"
        }
    
    # 3. 发送完成信号
    yield {
        "type": "meta_info_update",
        "meta_info_update": {
            "processing_complete": True
        }
    }
```

## 缓存机制

### 1. 多级缓存

```python
# 1. 响应缓存 (15分钟)
cache_key = f"conversation:process:{conversation_id}:{message}"
cached_response = await self.cache_service.get(cache_key)

# 2. 意图缓存 (10分钟)
intent_cache_key = f"intent:recognize:{message_hash}"
cached_intent = await self.cache_service.get(intent_cache_key)

# 3. LLM缓存 (1小时)
llm_cache_key = f"llm:response:{prompt_hash}"
cached_llm_response = await self.cache_service.get(llm_cache_key)
```

### 2. 缓存策略

- **响应缓存**: 完整的对话响应，避免重复处理
- **意图缓存**: 意图识别结果，加速意图识别
- **LLM缓存**: LLM调用结果，减少API成本
- **知识缓存**: 知识检索结果，提高检索效率

## 配置和初始化

### 1. 默认实例

```python
# app/services/ai_assistant/conversation/__init__.py
from .orchestrator import ConversationOrchestrator

# 创建默认实例
conversation_orchestrator = ConversationOrchestrator()
```

### 2. 自定义配置

```python
# 自定义LLM提供商
llm_proxy = LLMProxyFactory.get_provider("qwen")

# 自定义知识检索器
knowledge_retriever = KnowledgeRetriever(vector_store_path="custom_path")

# 创建自定义协调器
custom_orchestrator = ConversationOrchestrator(
    llm_proxy=llm_proxy,
    knowledge_retriever=knowledge_retriever,
    use_bailian=False
)
```

## 扩展开发

### 1. 添加新状态

```python
class CustomState(ConversationState):
    """自定义状态"""
    
    async def handle_message(self, message, intent, user_info):
        # 实现消息处理逻辑
        pass
        
    async def handle_message_stream(self, message, intent, user_info):
        # 实现流式处理逻辑
        async for chunk in self._process_stream(message):
            yield chunk
```

### 2. 添加新意图处理器

```python
class CustomIntentHandler(BaseIntentHandler):
    """自定义意图处理器"""
    
    async def handle(self, intent, message, user_context):
        # 实现意图处理逻辑
        return {
            "response_type": "custom",
            "content": "自定义响应"
        }
```

### 3. 添加新LLM提供商

```python
class CustomLLMProxy(LLMProxy):
    """自定义LLM代理"""
    
    async def astream(self, prompt, **kwargs):
        # 实现流式生成
        for chunk in self._generate_stream(prompt):
            yield chunk
```

## 性能优化

### 1. 异步处理
- 所有I/O操作使用async/await
- 并发处理多个请求
- 非阻塞的流式响应

### 2. 内存管理
- 轻量级状态对象
- 及时清理过期上下文
- 合理的缓存大小限制

### 3. 错误处理
- 完善的异常捕获
- 优雅的降级机制
- 详细的错误日志

---

*文档版本: v1.0*
*更新时间: 2025-01-27*
