# LLM集成详解

## 概述

智能健身AI助手系统支持多种大语言模型提供商，通过统一的LLM代理接口实现模型的无缝切换和管理。系统采用工厂模式设计，支持流式响应和缓存优化。

## LLM架构设计

### 1. 统一代理接口

**位置**: `app/services/ai_assistant/llm/proxy.py`

```python
class LLMProxy(ABC):
    """LLM代理抽象基类"""
    
    @abstractmethod
    async def astream(
        self, 
        prompt: str, 
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """流式生成接口"""
        pass
        
    @abstractmethod
    async def generate_text(
        self, 
        prompt: str, 
        **kwargs
    ) -> str:
        """文本生成接口"""
        pass
        
    @abstractmethod
    async def chat(
        self, 
        messages: List[Dict[str, str]], 
        **kwargs
    ) -> str:
        """对话接口"""
        pass
```

### 2. 工厂模式管理

**位置**: `app/services/ai_assistant/llm/factory.py`

```python
class LLMProxyFactory:
    """LLM提供商工厂"""
    
    _providers: Dict[str, Type[LLMProxy]] = {}
    _instances: Dict[str, LLMProxy] = {}
    
    @classmethod
    def register_provider(cls, name: str, provider_class: Type[LLMProxy]):
        """注册LLM提供商"""
        cls._providers[name] = provider_class
        
    @classmethod
    def get_provider(cls, provider_name: str) -> LLMProxy:
        """获取提供商实例"""
        if provider_name not in cls._instances:
            if provider_name not in cls._providers:
                raise ValueError(f"Unknown provider: {provider_name}")
            cls._instances[provider_name] = cls._providers[provider_name]()
        return cls._instances[provider_name]
```

## 支持的LLM提供商

### 1. 通义千问 (Qwen) 🔥 推荐

**位置**: `app/services/ai_assistant/llm/providers/qwen_proxy.py`

#### 1.1 配置

```python
# .env配置
QWEN_API_KEY=your_qwen_api_key
QWEN_API_BASE=https://dashscope.aliyuncs.com/compatible-mode/v1
LLM_MODEL=qwen-max
LLM_PROVIDER=qwen
```

#### 1.2 实现特点

```python
class QwenLLMProxy(LLMProxy):
    """通义千问LLM代理实现"""
    
    def __init__(self):
        self.api_key = settings.QWEN_API_KEY
        self.base_url = settings.QWEN_API_BASE
        self.model = settings.LLM_MODEL
        
    async def astream(self, prompt: str, **kwargs):
        """流式生成实现"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.model,
            "messages": [{"role": "user", "content": prompt}],
            "stream": True,
            "temperature": kwargs.get("temperature", 0.7),
            "max_tokens": kwargs.get("max_tokens", 1000)
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=data
            ) as response:
                async for line in response.content:
                    if line.startswith(b"data: "):
                        chunk_data = line[6:].decode().strip()
                        if chunk_data and chunk_data != "[DONE]":
                            try:
                                chunk = json.loads(chunk_data)
                                content = chunk["choices"][0]["delta"].get("content", "")
                                if content:
                                    yield content
                            except json.JSONDecodeError:
                                continue
```

#### 1.3 优势特点

- **性能优秀**: 响应速度快，质量高
- **中文优化**: 对中文理解和生成能力强
- **成本效益**: 相比国外模型成本更低
- **稳定性好**: 服务稳定，可用性高

### 2. OpenAI

**位置**: `app/services/ai_assistant/llm/providers/openai.py`

#### 2.1 配置

```python
# .env配置
OPENAI_API_KEY=your_openai_api_key
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4
```

#### 2.2 实现

```python
class OpenAILLMProxy(LLMProxy):
    """OpenAI LLM代理实现"""
    
    def __init__(self):
        self.client = AsyncOpenAI(
            api_key=settings.OPENAI_API_KEY,
            base_url=settings.OPENAI_BASE_URL
        )
        
    async def astream(self, prompt: str, **kwargs):
        """流式生成实现"""
        stream = await self.client.chat.completions.create(
            model=kwargs.get("model", "gpt-4"),
            messages=[{"role": "user", "content": prompt}],
            stream=True,
            temperature=kwargs.get("temperature", 0.7),
            max_tokens=kwargs.get("max_tokens", 1000)
        )
        
        async for chunk in stream:
            if chunk.choices[0].delta.content:
                yield chunk.choices[0].delta.content
```

### 3. DashScope

**位置**: `app/services/ai_assistant/llm/providers/dashscope.py`

#### 3.1 配置

```python
# .env配置
DASHSCOPE_API_KEY=your_dashscope_api_key
DASHSCOPE_MODEL=qwen-turbo
```

#### 3.2 实现

```python
class DashScopeLLMProxy(LLMProxy):
    """DashScope LLM代理实现"""
    
    def __init__(self):
        import dashscope
        dashscope.api_key = settings.DASHSCOPE_API_KEY
        
    async def astream(self, prompt: str, **kwargs):
        """流式生成实现"""
        from dashscope import Generation
        
        responses = Generation.call(
            model=kwargs.get("model", "qwen-turbo"),
            prompt=prompt,
            stream=True,
            temperature=kwargs.get("temperature", 0.7)
        )
        
        for response in responses:
            if response.status_code == 200:
                yield response.output.text
```

### 4. 百炼 (Bailian)

**位置**: `app/services/ai_assistant/llm/providers/bailian_proxy.py`

#### 4.1 配置

```python
# .env配置
BAILIAN_API_KEY=your_bailian_api_key
BAILIAN_APP_ID=your_app_id
```

#### 4.2 实现

```python
class BailianLLMProxy(LLMProxy):
    """百炼LLM代理实现"""
    
    def __init__(self):
        self.api_key = settings.BAILIAN_API_KEY
        self.app_id = settings.BAILIAN_APP_ID
        
    async def generate_bailian_response(
        self, 
        message: str, 
        user_info: Dict[str, Any], 
        intent_type: str
    ) -> str:
        """调用百炼应用"""
        # 百炼应用调用逻辑
        pass
```

## 流式处理机制

### 1. 流式响应格式

```python
# 标准流式响应
async def astream(self, prompt: str, **kwargs):
    """流式生成示例"""
    
    # 连接LLM API
    async with self._create_stream_connection(prompt, **kwargs) as stream:
        async for chunk in stream:
            # 解析响应块
            content = self._parse_chunk(chunk)
            if content:
                yield content
```

### 2. 错误处理

```python
async def astream_with_retry(self, prompt: str, **kwargs):
    """带重试的流式生成"""
    
    max_retries = kwargs.get("max_retries", 3)
    retry_delay = kwargs.get("retry_delay", 1)
    
    for attempt in range(max_retries):
        try:
            async for chunk in self.astream(prompt, **kwargs):
                yield chunk
            return
        except Exception as e:
            if attempt == max_retries - 1:
                raise e
            await asyncio.sleep(retry_delay * (2 ** attempt))
```

### 3. 流式数据格式

```python
# 文本片段
yield "这是一个文本片段"

# 结构化数据
yield {
    "type": "token",
    "content": "文本内容",
    "timestamp": time.time()
}

# 元数据
yield {
    "type": "metadata",
    "model": "qwen-max",
    "tokens_used": 150
}
```

## 缓存机制

### 1. 多级缓存

```python
class CachedLLMProxy(LLMProxy):
    """带缓存的LLM代理"""
    
    def __init__(self, base_proxy: LLMProxy, cache_service: CacheService):
        self.base_proxy = base_proxy
        self.cache_service = cache_service
        
    async def generate_text(self, prompt: str, **kwargs) -> str:
        """带缓存的文本生成"""
        
        # 生成缓存键
        cache_key = self._generate_cache_key(prompt, **kwargs)
        
        # 检查缓存
        cached_result = await self.cache_service.get(cache_key)
        if cached_result:
            return cached_result
            
        # 调用基础代理
        result = await self.base_proxy.generate_text(prompt, **kwargs)
        
        # 缓存结果
        await self.cache_service.set(
            cache_key, 
            result, 
            ttl=kwargs.get("cache_ttl", 3600)
        )
        
        return result
```

### 2. 缓存策略

- **提示词缓存**: 相同提示词的响应缓存1小时
- **用户缓存**: 用户特定的响应缓存30分钟
- **通用缓存**: 通用知识类响应缓存24小时
- **流式缓存**: 流式响应的完整结果缓存

### 3. 缓存键生成

```python
def _generate_cache_key(self, prompt: str, **kwargs) -> str:
    """生成缓存键"""
    
    # 提取关键参数
    key_params = {
        "model": kwargs.get("model", self.model),
        "temperature": kwargs.get("temperature", 0.7),
        "max_tokens": kwargs.get("max_tokens", 1000)
    }
    
    # 生成哈希
    prompt_hash = hashlib.md5(prompt.encode()).hexdigest()
    params_hash = hashlib.md5(
        json.dumps(key_params, sort_keys=True).encode()
    ).hexdigest()
    
    return f"llm:{self.__class__.__name__}:{prompt_hash}:{params_hash}"
```

## 性能优化

### 1. 连接池管理

```python
class PooledLLMProxy(LLMProxy):
    """使用连接池的LLM代理"""
    
    def __init__(self):
        self.session = aiohttp.ClientSession(
            connector=aiohttp.TCPConnector(
                limit=100,  # 总连接数限制
                limit_per_host=20,  # 每个主机连接数限制
                ttl_dns_cache=300,  # DNS缓存时间
                use_dns_cache=True
            ),
            timeout=aiohttp.ClientTimeout(total=60)
        )
```

### 2. 请求优化

```python
async def optimized_request(self, prompt: str, **kwargs):
    """优化的请求处理"""
    
    # 请求压缩
    headers = {
        "Accept-Encoding": "gzip, deflate",
        "Content-Encoding": "gzip"
    }
    
    # 数据压缩
    data = json.dumps(self._build_request_data(prompt, **kwargs))
    compressed_data = gzip.compress(data.encode())
    
    # 发送请求
    async with self.session.post(
        self.api_url,
        headers=headers,
        data=compressed_data
    ) as response:
        return await response.json()
```

### 3. 并发控制

```python
class RateLimitedLLMProxy(LLMProxy):
    """带限流的LLM代理"""
    
    def __init__(self, base_proxy: LLMProxy, rate_limit: int = 10):
        self.base_proxy = base_proxy
        self.semaphore = asyncio.Semaphore(rate_limit)
        
    async def generate_text(self, prompt: str, **kwargs) -> str:
        """限流的文本生成"""
        async with self.semaphore:
            return await self.base_proxy.generate_text(prompt, **kwargs)
```

## 监控和日志

### 1. 请求监控

```python
class MonitoredLLMProxy(LLMProxy):
    """带监控的LLM代理"""
    
    async def generate_text(self, prompt: str, **kwargs) -> str:
        """带监控的文本生成"""
        
        start_time = time.time()
        
        try:
            result = await self.base_proxy.generate_text(prompt, **kwargs)
            
            # 记录成功指标
            self._record_success_metrics(
                duration=time.time() - start_time,
                prompt_length=len(prompt),
                response_length=len(result)
            )
            
            return result
            
        except Exception as e:
            # 记录失败指标
            self._record_error_metrics(
                error_type=type(e).__name__,
                duration=time.time() - start_time
            )
            raise
```

### 2. 成本跟踪

```python
class CostTrackingLLMProxy(LLMProxy):
    """成本跟踪LLM代理"""
    
    def __init__(self, base_proxy: LLMProxy):
        self.base_proxy = base_proxy
        self.cost_tracker = CostTracker()
        
    async def generate_text(self, prompt: str, **kwargs) -> str:
        """带成本跟踪的文本生成"""
        
        result = await self.base_proxy.generate_text(prompt, **kwargs)
        
        # 计算成本
        cost = self._calculate_cost(
            model=kwargs.get("model", self.model),
            input_tokens=self._count_tokens(prompt),
            output_tokens=self._count_tokens(result)
        )
        
        # 记录成本
        await self.cost_tracker.record_cost(
            provider=self.__class__.__name__,
            model=kwargs.get("model", self.model),
            cost=cost,
            timestamp=time.time()
        )
        
        return result
```

## 配置管理

### 1. 模型配置

```python
# app/core/config.py
AI_API_CONFIGS: Dict[str, Dict[str, str]] = {
    "qwen": {
        "api_key": os.environ.get("QWEN_API_KEY"),
        "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
        "model": "qwen-max",
        "description": "阿里云通义千问"
    },
    "openai": {
        "api_key": os.environ.get("OPENAI_API_KEY"),
        "base_url": "https://api.openai.com/v1",
        "model": "gpt-4",
        "description": "OpenAI GPT-4"
    }
}
```

### 2. 动态配置

```python
class ConfigurableLLMProxy(LLMProxy):
    """可配置的LLM代理"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.api_key = config["api_key"]
        self.base_url = config["base_url"]
        self.model = config["model"]
        
    def update_config(self, new_config: Dict[str, Any]):
        """更新配置"""
        self.config.update(new_config)
        # 重新初始化连接等
```

## 扩展开发

### 1. 添加新提供商

```python
class CustomLLMProxy(LLMProxy):
    """自定义LLM代理"""
    
    def __init__(self):
        # 初始化自定义配置
        pass
        
    async def astream(self, prompt: str, **kwargs):
        """实现流式生成"""
        # 自定义实现
        pass
        
    async def generate_text(self, prompt: str, **kwargs) -> str:
        """实现文本生成"""
        # 自定义实现
        pass

# 注册新提供商
LLMProxyFactory.register_provider("custom", CustomLLMProxy)
```

### 2. 中间件支持

```python
class LLMMiddleware:
    """LLM中间件基类"""
    
    async def before_request(self, prompt: str, **kwargs):
        """请求前处理"""
        pass
        
    async def after_response(self, response: str, **kwargs):
        """响应后处理"""
        pass

class LoggingMiddleware(LLMMiddleware):
    """日志中间件"""
    
    async def before_request(self, prompt: str, **kwargs):
        logger.info(f"LLM请求: {prompt[:100]}...")
        
    async def after_response(self, response: str, **kwargs):
        logger.info(f"LLM响应: {response[:100]}...")
```

---

*文档版本: v1.0*
*更新时间: 2025-01-27*
