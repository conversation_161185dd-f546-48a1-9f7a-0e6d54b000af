# API端点详解

## 概述

智能健身AI助手系统的API层位于 `app/api/v2/endpoints/chat.py`，提供了完整的对话管理和消息处理功能。

## 核心端点

### 1. 消息发送 - POST /message

**功能**: 向AI助手发送消息并获取回复

**请求模型**:
```python
class ChatRequest(BaseModel):
    message: str = Field(..., description="用户消息内容")
    session_id: Optional[str] = Field(None, description="会话ID，如果为空则创建新会话")
    meta_info: Optional[Dict[str, Any]] = Field(None, description="消息元数据")
    quick_intent: Optional[str] = Field(None, description="快速意图参数")
    system_prompt: Optional[str] = Field(None, description="系统提示词")
```

**响应模型**:
```python
class ChatResponse(BaseModel):
    response: str = Field(..., description="AI助手响应内容")
    conversation_id: str = Field(..., description="会话ID")
    session_id: str = Field(..., description="会话ID（兼容性）")
    intent_type: Optional[str] = Field(None, description="识别的意图类型")
    confidence: Optional[float] = Field(None, description="意图识别置信度")
    success: bool = Field(True, description="处理是否成功")
    error: Optional[str] = Field(None, description="错误信息")
    meta_info: Optional[Dict[str, Any]] = Field(None, description="响应元数据")
```

**处理流程**:
1. 验证用户身份
2. 获取或创建会话
3. 保存用户消息到数据库
4. 调用新版AI助手系统处理
5. 保存AI回复到数据库
6. 返回格式化响应

### 2. 流式对话 - WebSocket /stream/{session_id}

**功能**: 提供实时流式对话体验

**连接流程**:
1. 建立WebSocket连接
2. 验证用户身份
3. 获取或创建会话
4. 发送连接成功事件

**消息格式**:
```json
{
    "message": "用户消息内容",
    "meta_info": {
        "additional_context": "额外上下文"
    }
}
```

**响应事件类型**:
- `connected`: 连接成功
- `message_saved`: 用户消息已保存
- `chunk`: 流式文本片段
- `message`: 完整消息
- `meta_info_update`: 元数据更新
- `done`: 处理完成
- `error`: 错误信息
- `heartbeat`: 心跳检测

### 3. 会话管理

#### 3.1 获取会话列表 - GET /conversations

**功能**: 获取当前用户的所有活跃会话

**参数**:
- `skip`: 跳过数量 (默认: 0)
- `limit`: 限制数量 (默认: 100)

**响应**:
```python
class ConversationList(BaseModel):
    conversations: List[Conversation]
    total: int
```

#### 3.2 获取消息历史 - GET /sessions/{session_id}/messages

**功能**: 获取指定会话的消息历史

**参数**:
- `session_id`: 会话ID
- `skip`: 跳过数量 (默认: 0)
- `limit`: 限制数量 (默认: 20)

**特性**:
- 自动创建不存在的会话
- 倒序返回消息（最新在前）
- 消息格式适配

#### 3.3 获取新消息 - GET /conversations/{session_id}/messages/since/{message_id}

**功能**: 获取指定消息ID之后的新消息

**用途**: 支持增量消息获取

### 4. 轮询接口

#### 4.1 轮询新消息 - GET /poll/{session_id}

**功能**: 轮询获取会话中的新消息

**参数**:
- `session_id`: 会话ID
- `last_message_id`: 上次接收到的最后一条消息的ID

**用途**: 为不支持WebSocket的环境提供实时消息获取

### 5. 用户信息更新

#### 5.1 更新用户信息 - POST /update_user_info

**功能**: 更新用户信息并继续对话

**请求模型**:
```python
class UserInfoUpdateRequest(BaseModel):
    session_id: str = Field(..., description="会话ID")
    field: str = Field(..., description="要更新的字段名")
    value: Any = Field(..., description="字段值")
    value_text: str = Field(..., description="用户输入的原始文本")
```

**支持字段**:
- `height`: 身高
- `weight`: 体重
- `age`: 年龄
- `gender`: 性别
- `fitness_goal`: 健身目标
- `experience_level`: 健身经验
- `activity_level`: 活动水平

### 6. 训练计划生成

#### 6.1 生成训练计划 - POST /generate_training_plan

**功能**: 生成个性化训练计划

**请求模型**:
```python
class TrainingPlanRequest(BaseModel):
    session_id: str = Field(..., description="会话ID")
    plan_type: str = Field("single_day", description="计划类型")
    body_part: Optional[str] = Field(None, description="目标训练部位")
    training_scene: Optional[str] = Field("gym", description="训练场景")
    duration_weeks: Optional[int] = Field(4, description="计划持续周数")
    days_per_week: Optional[int] = Field(3, description="每周训练天数")
    available_time: Optional[int] = Field(60, description="可用时间（分钟）")
    additional_notes: Optional[str] = Field(None, description="额外备注")
```

### 7. 兼容性接口

#### 7.1 流式消息发送 - POST /stream/{session_id}/message

**功能**: 发送流式消息（兼容性接口）

**特点**:
- 后台异步处理AI回复
- 客户端需要通过轮询获取响应
- 支持复杂的元数据处理

#### 7.2 WebSocket连接信息 - GET /stream/{session_id}/connect

**功能**: 获取WebSocket连接信息

**用途**: 微信小程序等环境的WebSocket连接准备

## 错误处理

### 1. 标准错误响应

```python
{
    "success": false,
    "error": "错误描述",
    "conversation_id": "会话ID",
    "session_id": "会话ID"
}
```

### 2. 常见错误类型

- **401 Unauthorized**: 用户未认证
- **403 Forbidden**: 无权访问会话
- **404 Not Found**: 会话或消息不存在
- **400 Bad Request**: 请求参数错误
- **500 Internal Server Error**: 服务器内部错误

## 认证机制

### 1. JWT Token认证

所有API端点都需要有效的JWT Token：

```python
current_user: models.User = Depends(deps.get_current_active_user)
```

### 2. WebSocket认证

WebSocket连接使用专门的认证依赖：

```python
current_user: models.User = Depends(deps.get_current_user_websocket)
```

## 数据持久化

### 1. 消息存储

所有用户消息和AI回复都会持久化到数据库：

```python
# 用户消息
user_message = await create_user_message(
    db, conversation.id, current_user.id, content, meta_info
)

# AI回复
assistant_message = await create_assistant_message(
    db, conversation.id, current_user.id, response_content, response_meta_info
)
```

### 2. 会话管理

会话信息包括：
- 会话ID (session_id)
- 用户ID (user_id)
- 创建时间 (start_time)
- 最后活跃时间 (last_active)
- 元数据 (meta_info)

## 性能优化

### 1. 异步处理

所有API端点都使用异步处理：

```python
async def send_message(...)
async def websocket_endpoint(...)
```

### 2. 后台任务

复杂的AI处理使用后台任务：

```python
background_tasks.add_task(process_ai_response)
```

### 3. 连接管理

WebSocket连接包含：
- 心跳检测机制
- 连接状态跟踪
- 资源清理

## 监控和日志

### 1. 请求日志

每个API调用都有详细的日志记录：

```python
logger.info(f"处理消息: {message[:50]}...")
logger.error(f"处理消息时出错: {str(e)}", exc_info=True)
```

### 2. 性能监控

关键指标包括：
- API响应时间
- WebSocket连接数
- 消息处理成功率
- AI调用延迟

---

*文档版本: v1.0*
*更新时间: 2025-01-27*
