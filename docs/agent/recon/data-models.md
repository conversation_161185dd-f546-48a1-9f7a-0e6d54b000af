# 数据模型详解

## 概述

智能健身AI助手系统使用PostgreSQL作为主数据库，通过SQLAlchemy ORM进行数据访问。数据模型设计支持用户管理、对话系统、健身数据和社区功能。

## 核心数据模型

### 1. 用户相关模型

#### 1.1 用户模型 (User)

**位置**: `app/models/user.py`

```python
class User(Base):
    __tablename__ = "users"
    
    # 基础信息
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=True)
    phone = Column(String, unique=True, index=True, nullable=True)
    
    # 微信相关字段
    openid = Column(String, unique=True, index=True, nullable=False)
    unionid = Column(String, unique=True, index=True, nullable=True)
    nickname = Column(String)
    avatar_url = Column(String)
    
    # 个人信息
    gender = Column(Integer)  # 0: 未知, 1: 男性, 2: 女性
    age = Column(Integer, nullable=True)
    height = Column(Float, nullable=True)  # cm
    weight = Column(Float, nullable=True)  # kg
    activity_level = Column(Integer, default=3)
    experience_level = Column(Integer, nullable=True)  # 1-初学者, 2-中级, 3-高级
    fitness_goal = Column(Integer, nullable=True)  # 1-减肥, 2-保持, 3-增肌
    
    # 健康信息
    health_conditions = Column(ARRAY(String), nullable=True)
    allergies = Column(ARRAY(String), nullable=True)
    
    # 计算的健康指标
    bmi = Column(Float, nullable=True)
    tedd = Column(Integer, nullable=True)  # 每日总能量消耗(kcal)
```

**关键特性**:
- 支持微信登录集成
- 完整的健身档案信息
- 健康指标自动计算
- 数组字段存储复杂数据

#### 1.2 用户设置 (UserSetting)

```python
class UserSetting(Base):
    __tablename__ = "user_settings"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # 通知设置
    enable_notifications = Column(Boolean, default=True)
    notification_time = Column(Time, nullable=True)
    
    # 隐私设置
    profile_visibility = Column(String, default="public")
    data_sharing = Column(Boolean, default=False)
    
    # 应用设置
    language = Column(String, default="zh-CN")
    theme = Column(String, default="light")
```

### 2. 对话系统模型

#### 2.1 对话模型 (Conversation)

**位置**: `app/models/conversation.py`

```python
class Conversation(Base):
    __tablename__ = "conversations"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    session_id = Column(String, unique=True, index=True, nullable=False)
    start_time = Column(DateTime(timezone=True), server_default=func.now())
    last_active = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    is_active = Column(Boolean, default=True)
    meta_info = Column(JSON, nullable=True)  # 存储意图、主题等上下文信息
    
    # 关系
    messages = relationship("Message", back_populates="conversation", cascade="all, delete-orphan")
    user = relationship("User", back_populates="conversations")
```

**关键特性**:
- 唯一的session_id标识
- 自动更新最后活跃时间
- JSON字段存储元数据
- 级联删除消息

#### 2.2 消息模型 (Message)

**位置**: `app/models/message.py`

```python
class MessageRole(str, enum.Enum):
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    TOOL = "tool"  # 用于LangChain工具调用/结果

class Message(Base):
    __tablename__ = "messages"
    
    id = Column(Integer, primary_key=True, index=True)
    conversation_id = Column(Integer, ForeignKey("conversations.id"), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    content = Column(Text, nullable=False)
    role = Column(Enum(MessageRole), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    meta_info = Column(JSON, nullable=True)  # 存储工具调用信息等
    
    # 关系
    conversation = relationship("Conversation", back_populates="messages")
    user = relationship("User", back_populates="messages")
```

**关键特性**:
- 支持多种消息角色
- 自动时间戳管理
- 元数据存储支持
- 双向关系定义

### 3. 健身相关模型

#### 3.1 运动模型 (Exercise)

```python
class Exercise(Base):
    __tablename__ = "exercises"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False, index=True)
    name_en = Column(String, nullable=True)
    category = Column(String, nullable=False, index=True)
    target_muscles = Column(ARRAY(String), nullable=True)
    equipment = Column(ARRAY(String), nullable=True)
    difficulty_level = Column(Integer, default=1)  # 1-5
    instructions = Column(Text, nullable=True)
    tips = Column(Text, nullable=True)
    image_url = Column(String, nullable=True)
    video_url = Column(String, nullable=True)
    
    # 元数据
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
```

#### 3.2 训练计划模型 (TrainingPlan)

```python
class TrainingPlan(Base):
    __tablename__ = "training_plans"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    plan_type = Column(String, nullable=False)  # daily, weekly, monthly
    target_goal = Column(String, nullable=True)
    difficulty_level = Column(Integer, default=1)
    duration_weeks = Column(Integer, default=4)
    days_per_week = Column(Integer, default=3)
    
    # 计划内容 (JSON格式)
    plan_data = Column(JSON, nullable=False)
    
    # 状态
    is_active = Column(Boolean, default=True)
    start_date = Column(Date, nullable=True)
    end_date = Column(Date, nullable=True)
    
    # 关系
    user = relationship("User", back_populates="training_plans")
    workouts = relationship("Workout", back_populates="training_plan")
```

#### 3.3 训练记录模型 (Workout)

```python
class Workout(Base):
    __tablename__ = "workouts"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    training_plan_id = Column(Integer, ForeignKey("training_plans.id"), nullable=True)
    name = Column(String, nullable=False)
    workout_date = Column(Date, nullable=False)
    start_time = Column(DateTime(timezone=True), nullable=True)
    end_time = Column(DateTime(timezone=True), nullable=True)
    
    # 训练统计
    total_duration = Column(Integer, nullable=True)  # 分钟
    total_calories = Column(Integer, nullable=True)
    total_volume = Column(Float, nullable=True)  # 总重量
    
    # 状态
    status = Column(String, default="planned")  # planned, in_progress, completed, skipped
    notes = Column(Text, nullable=True)
    
    # 关系
    user = relationship("User", back_populates="workouts")
    training_plan = relationship("TrainingPlan", back_populates="workouts")
    exercises = relationship("WorkoutExercise", back_populates="workout")
```

#### 3.4 训练动作记录 (WorkoutExercise)

```python
class WorkoutExercise(Base):
    __tablename__ = "workout_exercises"
    
    id = Column(Integer, primary_key=True, index=True)
    workout_id = Column(Integer, ForeignKey("workouts.id"), nullable=False)
    exercise_id = Column(Integer, ForeignKey("exercises.id"), nullable=False)
    order_index = Column(Integer, default=0)
    
    # 计划参数
    planned_sets = Column(Integer, nullable=True)
    planned_reps = Column(Integer, nullable=True)
    planned_weight = Column(Float, nullable=True)
    planned_duration = Column(Integer, nullable=True)  # 秒
    
    # 实际执行
    actual_sets = Column(Integer, nullable=True)
    notes = Column(Text, nullable=True)
    
    # 关系
    workout = relationship("Workout", back_populates="exercises")
    exercise = relationship("Exercise")
    set_records = relationship("SetRecord", back_populates="workout_exercise")
```

#### 3.5 组数记录 (SetRecord)

```python
class SetRecord(Base):
    __tablename__ = "set_records"
    
    id = Column(Integer, primary_key=True, index=True)
    workout_exercise_id = Column(Integer, ForeignKey("workout_exercises.id"), nullable=False)
    set_number = Column(Integer, nullable=False)
    
    # 执行数据
    reps = Column(Integer, nullable=True)
    weight = Column(Float, nullable=True)
    duration = Column(Integer, nullable=True)  # 秒
    distance = Column(Float, nullable=True)  # 米
    rest_time = Column(Integer, nullable=True)  # 秒
    
    # 状态
    completed = Column(Boolean, default=False)
    notes = Column(Text, nullable=True)
    
    # 关系
    workout_exercise = relationship("WorkoutExercise", back_populates="set_records")
```

### 4. 营养相关模型

#### 4.1 食物模型 (Food)

```python
class Food(Base):
    __tablename__ = "foods"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False, index=True)
    name_en = Column(String, nullable=True)
    category = Column(String, nullable=False, index=True)
    brand = Column(String, nullable=True)
    
    # 营养信息 (每100g)
    calories = Column(Float, nullable=True)
    protein = Column(Float, nullable=True)
    carbs = Column(Float, nullable=True)
    fat = Column(Float, nullable=True)
    fiber = Column(Float, nullable=True)
    sugar = Column(Float, nullable=True)
    sodium = Column(Float, nullable=True)
    
    # 其他信息
    image_url = Column(String, nullable=True)
    barcode = Column(String, nullable=True, index=True)
    
    # 关系
    nutritional_profile = relationship("NutritionalProfile", back_populates="food", uselist=False)
```

#### 4.2 餐食记录 (MealRecord)

```python
class MealRecord(Base):
    __tablename__ = "meal_records"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    meal_date = Column(Date, nullable=False)
    meal_type = Column(String, nullable=False)  # breakfast, lunch, dinner, snack
    
    # 总营养统计
    total_calories = Column(Float, nullable=True)
    total_protein = Column(Float, nullable=True)
    total_carbs = Column(Float, nullable=True)
    total_fat = Column(Float, nullable=True)
    
    # 其他信息
    notes = Column(Text, nullable=True)
    image_url = Column(String, nullable=True)
    
    # 关系
    user = relationship("User", back_populates="meal_records")
    food_items = relationship("FoodItem", back_populates="meal_record")
```

### 5. 社区相关模型

#### 5.1 帖子模型 (Post)

```python
class PostStatus(str, enum.Enum):
    DRAFT = "draft"
    PUBLISHED = "published"
    HIDDEN = "hidden"
    DELETED = "deleted"

class Post(Base):
    __tablename__ = "posts"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    title = Column(String, nullable=False)
    content = Column(Text, nullable=False)
    status = Column(Enum(PostStatus), default=PostStatus.DRAFT)
    
    # 关联数据
    related_workout_id = Column(Integer, ForeignKey("workouts.id"), nullable=True)
    tags = Column(ARRAY(String), nullable=True)
    
    # 统计数据
    like_count = Column(Integer, default=0)
    comment_count = Column(Integer, default=0)
    view_count = Column(Integer, default=0)
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    published_at = Column(DateTime(timezone=True), nullable=True)
    
    # 关系
    user = relationship("User", back_populates="posts")
    related_workout = relationship("Workout")
    comments = relationship("Comment", back_populates="post")
    images = relationship("Image", back_populates="post")
```

## 数据关系图

```mermaid
erDiagram
    User ||--o{ Conversation : has
    User ||--o{ Message : sends
    User ||--o{ TrainingPlan : creates
    User ||--o{ Workout : performs
    User ||--o{ MealRecord : logs
    User ||--o{ Post : publishes
    
    Conversation ||--o{ Message : contains
    
    TrainingPlan ||--o{ Workout : includes
    Workout ||--o{ WorkoutExercise : contains
    WorkoutExercise ||--o{ SetRecord : tracks
    WorkoutExercise }o--|| Exercise : references
    
    MealRecord ||--o{ FoodItem : contains
    FoodItem }o--|| Food : references
    
    Post ||--o{ Comment : receives
    Post ||--o{ Image : includes
    Post }o--o| Workout : relates_to
```

## 数据库配置

### 1. 连接配置

```python
# app/core/config.py
DATABASE_URL: str = os.environ.get(
    "DATABASE_URL",
    "postgresql://postgres:!scienceFit0219@127.0.0.1:5432/fitness_db"
)
```

### 2. SQLAlchemy配置

```python
# app/db/session.py
engine = create_engine(
    settings.DATABASE_URL,
    pool_pre_ping=True,
    pool_size=10,
    max_overflow=20
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
```

### 3. 基类定义

```python
# app/db/base_class.py
class Base:
    id: Any
    __name__: str
    
    # 自动生成表名
    @declared_attr
    def __tablename__(cls) -> str:
        return cls.__name__.lower()
```

## CRUD操作

### 1. 基础CRUD

```python
# app/crud/base.py
class CRUDBase(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    def __init__(self, model: Type[ModelType]):
        self.model = model
    
    def get(self, db: Session, id: Any) -> Optional[ModelType]:
        return db.query(self.model).filter(self.model.id == id).first()
    
    def get_multi(self, db: Session, *, skip: int = 0, limit: int = 100) -> List[ModelType]:
        return db.query(self.model).offset(skip).limit(limit).all()
    
    def create(self, db: Session, *, obj_in: CreateSchemaType) -> ModelType:
        obj_in_data = jsonable_encoder(obj_in)
        db_obj = self.model(**obj_in_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
```

### 2. 专用CRUD

```python
# app/crud/crud_conversation.py
class CRUDConversation(CRUDBase[Conversation, ConversationCreate, ConversationUpdate]):
    def get_by_session_id(self, db: Session, *, session_id: str) -> Optional[Conversation]:
        return db.query(Conversation).filter(Conversation.session_id == session_id).first()
    
    def get_active_by_user(self, db: Session, *, user_id: int, skip: int = 0, limit: int = 100) -> List[Conversation]:
        return db.query(Conversation).filter(
            Conversation.user_id == user_id,
            Conversation.is_active == True
        ).offset(skip).limit(limit).all()
```

## 数据迁移

### 1. Alembic配置

```python
# alembic/env.py
from app.db.base import Base
from app.models import *  # 导入所有模型

target_metadata = Base.metadata
```

### 2. 迁移脚本

```bash
# 创建迁移
alembic revision --autogenerate -m "Add new table"

# 执行迁移
alembic upgrade head

# 回滚迁移
alembic downgrade -1
```

## 性能优化

### 1. 索引策略

- 主键自动索引
- 外键字段索引
- 查询频繁字段索引
- 复合索引优化

### 2. 查询优化

- 使用eager loading减少N+1查询
- 合理使用join和subquery
- 分页查询避免大结果集
- 使用数据库函数减少应用层计算

### 3. 连接池配置

- 合理设置pool_size
- 配置max_overflow
- 启用pool_pre_ping
- 监控连接使用情况

---

*文档版本: v1.0*
*更新时间: 2025-01-27*
