# 用户模块文档

## 用户数据模型

### 用户(User)模型

| 字段 | 类型 | 说明 |
|------|------|------|
| id | Integer | 用户ID |
| openid | String | 微信openid |
| unionid | String | 微信unionid |
| nickname | String | 用户昵称 |
| avatar_url | String | 头像URL |
| phone | String | 手机号 |
| gender | Enum | 性别(MALE,FEMALE,UNKNOWN) |
| country | String | 国家 |
| province | String | 省份 |
| city | String | 城市 |
| age | Integer | 年龄 |
| weight | Float | 体重(kg) |
| height | Float | 身高(cm) |
| activity_level | Integer | 活动水平(1-5) |
| body_type | String | 体型 |
| experience_level | Enum | 健身经验(BEGINNER,INTERMEDIATE,ADVANCED) |
| fitness_goal | Enum | 健身目标(WEIGHT_LOSS,MUSCLE_GAIN,MAINTENANCE) |
| bmi | Float | 体质指数 |
| tedd | Integer | 每日总能量消耗 |
| completed | Boolean | 是否完成资料 |
| created_at | DateTime | 创建时间 |
| is_active | Boolean | 是否活跃 |

### 用户设置(UserSettings)模型

| 字段 | 类型 | 说明 |
|------|------|------|
| id | Integer | 设置ID |
| user_id | Integer | 用户ID |
| notification_enabled | Boolean | 是否开启通知 |
| theme | String | 主题设置 |
| language | String | 语言设置 |

## 用户相关接口

### 获取用户个人资料

```
GET /api/v1/user/profile
```

**请求头**
- Authorization: Bearer {token}

**响应内容**

```json
{
  "id": 1,
  "nickname": "用户昵称",
  "avatar_url": "/api/v1/user/avatar/image/abc123def456/avatar_20240406.jpg",
  "phone": "13800138000",
  "gender": "MALE",
  "gender_int": 1,
  "country": "中国",
  "province": "广东",
  "city": "深圳",
  "age": 30,
  "weight": 70.5,
  "height": 175.0,
  "activity_level": 3,
  "body_type": "中等体型",
  "experience_level": "BEGINNER",
  "fitness_goal": "WEIGHT_LOSS",
  "bmi": 23.0,
  "tedd": 2500,
  "completed": true,
  "created_at": "2024-04-05T12:00:00",
  "notification_enabled": true
}
```

### 更新用户个人资料

```
POST /api/v1/user/profile
```

**请求头**
- Authorization: Bearer {token}

**请求参数**

```json
{
  "nickname": "新昵称",
  "avatar_url": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEA...",  // 支持base64格式
  "gender": "MALE",
  "age": 30,
  "weight": 70.5,
  "height": 175.0,
  "activity_level": 3,
  "body_type": "中等体型",
  "experience_level": "BEGINNER",
  "fitness_goal": "WEIGHT_LOSS"
}
```

**响应内容**

```json
{
  "id": 1,
  "nickname": "新昵称",
  "avatar_url": "/api/v1/user/avatar/image/abc123def456/avatar_20240406.jpg",
  "gender": "MALE",
  "gender_int": 1,
  "age": 30,
  "weight": 70.5,
  "height": 175.0,
  "activity_level": 3,
  "body_type": "中等体型",
  "experience_level": "BEGINNER",
  "fitness_goal": "WEIGHT_LOSS",
  "bmi": 23.0,
  "tedd": 2500,
  "completed": true,
  "created_at": "2024-04-05T12:00:00"
}
```

### 上传用户头像

```
POST /api/v1/user/avatar
```

**请求头**
- Authorization: Bearer {token}
- Content-Type: multipart/form-data

**请求参数**
- file: 图片文件（支持JPG, PNG, GIF）
- url: 图片URL地址（可替代file）
- avatar_data: Base64编码的图像数据（可替代file）

**响应内容**

```json
{
  "success": true,
  "avatar_url": "/api/v1/user/avatar/image/abc123def456/avatar_20240406.jpg"
}
```

### 访问用户头像

```
GET /api/v1/user/avatar/image/{secure_path}/{filename}
```

获取用户头像图片。

### 检查用户是否存在

```
GET /api/v1/user/exists?openid={openid}
GET /api/v1/user/check?openid={openid}
```

**请求参数**
- openid: 微信用户openid（查询参数）

**响应内容**

```json
{
  "exists": true,
  "user_id": 1
}
```

### 获取用户设置

```
GET /api/v1/user/settings
```

**请求头**
- Authorization: Bearer {token}

**响应内容**

```json
{
  "notification_enabled": true,
  "theme": "light",
  "language": "zh_CN"
}
```

### 更新用户设置

```
POST /api/v1/user/settings
```

**请求头**
- Authorization: Bearer {token}

**请求参数**

```json
{
  "notification_enabled": false,
  "theme": "dark",
  "language": "en_US"
}
```

**响应内容**

```json
{
  "success": true,
  "message": "设置更新成功",
  "settings": {
    "notification_enabled": false,
    "theme": "dark",
    "language": "en_US"
  }
}
```

## 用户头像处理

用户头像URL格式为：
```
/api/v1/user/avatar/image/{secure_path}/{filename}
```

系统支持三种方式更新头像：
1. 上传图片文件
2. 提供base64编码的图像
3. 提供远程图片URL

处理流程：
1. 接收头像数据
2. 基于用户ID生成安全路径
3. 保存到文件系统
4. 更新数据库中的avatar_url字段

存储路径：
```
/data/users/{secure_path}/avatar/{filename}
```

## 用户数据计算

系统会基于用户提供的信息自动计算以下数据：

### BMI (体质指数)
基于身高和体重计算：
```
BMI = 体重(kg) / (身高(m) * 身高(m))
```

### TEDD (每日总能量消耗)
基于基础代谢率(BMR)和活动水平计算：
```
BMR = 10 * 体重(kg) + 6.25 * 身高(cm) - 5 * 年龄 + 性别系数
TEDD = BMR * 活动系数
```
其中：
- 性别系数：男性 = 5，女性 = -161
- 活动系数：根据activity_level确定，范围1.2-1.9 