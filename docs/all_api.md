# API文档

## 概述

所有API接口的基础路径为`/api/v1`，整体采用RESTful风格设计，支持JSON格式的数据交换。

## 基础信息

- 认证方式：Bearer Token（JWT）
- 请求头格式：`Authorization: Bearer {token}`
- 响应格式：JSON

## 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 资源创建成功 |
| 400 | 请求参数错误 |
| 401 | 未认证或认证失败 |
| 403 | 无权限访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## API结构

系统API采用模块化设构，主要分为以下模块：

### 目录结构
```
app/api/
├── admin/           # 管理后台相关
├── deps.py          # 依赖项注入
├── endpoints/       # API端点实现
│   ├── auth.py      # 认证相关接口
│   ├── chat.py      # 聊天相关接口
│   ├── community.py # 社区相关接口
│   ├── exercise.py  # 运动相关接口
│   ├── food.py      # 食物相关接口
│   ├── food_item.py # 食物项相关接口
│   ├── food_recognition.py # 食物识别相关接口
│   ├── health.py    # 健康检查接口
│   ├── health_recommendation.py # 健康建议相关接口
│   ├── meal.py      # 餐食相关接口
│   ├── nutrient.py  # 营养相关接口
│   ├── qrcode.py    # 二维码生成接口
│   ├── share.py     # 分享相关接口
│   ├── training_plan.py # 训练计划相关接口
│   └── user.py      # 用户相关接口
├── openapi_docs.py  # API文档配置
└── v1/              # V1版本API
```

## 认证接口

### 微信小程序登录

```
POST /auth/login/wechat
```

**请求参数**

```json
{
  "code": "string",                     // 必填，微信临时登录凭证
  "userInfo": {                         // 可选，用户信息对象
    "nickname": "微信用户",
    "avatar_url": "https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132"
  },
  "encryptedData": "string",            // 可选，微信加密数据
  "iv": "string",                       // 可选，加密算法的初始向量
  "openid": "string",                   // 可选，用户openid
  "unionid": "string"                   // 可选，用户unionid
}
```

**响应内容**

```json
{
  "success": true,
  "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "expires_at": "2024-05-06T12:00:00",
  "user": {
    "id": 1,
    "nickname": "微信用户",
    "avatarUrl": "https://example.com/avatar.jpg",
    "completed": false,
    "created_at": "2024-04-05T12:00:00"
  },
  "is_new_user": false
}
```

### 刷新访问令牌

```
POST /auth/refresh-token
```

**请求头**
- Authorization: Bearer {token}

**响应内容**

```json
{
  "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "expires_at": "2024-05-06T12:00:00"
}
```

### 用户登出

```
POST /auth/logout
```

**请求头**
- Authorization: Bearer {token}

**响应内容**

```json
{
  "success": true,
  "message": "登出成功"
}
```

### 绑定微信手机号

```
POST /auth/wechat/phone
```

**请求参数**

```json
{
  "code": "string",                    // 必填，微信临时登录凭证
  "encrypted_data": "string",          // 必填，微信加密数据
  "iv": "string"                       // 必填，加密算法的初始向量
}
```

**响应内容**

```json
{
  "success": true,
  "message": "手机号绑定成功",
  "phone": "13800138000"
}
```

## 食物相关接口

### 获取食物列表

```
GET /food/
```

**请求头**
- Authorization: Bearer {token}

**查询参数**
- skip: 跳过的记录数
- limit: 返回的记录数
- name: 食物名称（可选）
- category: 食物类别（可选）
- food_type: 食物类型（可选）

**响应内容**

```json
{
  "items": [
    {
      "id": 1,
      "name": "食物名称",
      "code": "食物编码",
      "category": "食物类别",
      "food_type": "食物类型",
      "thumb_image_url": "缩略图URL",
      "calory": 100.0,
      "protein_fraction": 0.3,
      "fat_fraction": 0.2,
      "carb_fraction": 0.5,
      "hot": 100
    }
  ],
  "total": 10
}
```

### 搜索食物

```
GET /food/search
```

**请求头**
- Authorization: Bearer {token}

**查询参数**
- name: 食物名称搜索关键词
- limit: 返回结果数量上限

**响应内容**

```json
{
  "items": [
    {
      "id": 1,
      "name": "食物名称",
      "code": "食物编码",
      "category": "食物类别",
      "food_type": "食物类型",
      "thumb_image_url": "缩略图URL",
      "calory": 100.0,
      "protein_fraction": 0.3,
      "fat_fraction": 0.2,
      "carb_fraction": 0.5,
      "hot": 100
    }
  ],
  "total": 10
}
```

### 获取食物详情

```
GET /food/{food_id}
```

**请求头**
- Authorization: Bearer {token}

**响应内容**

```json
{
  "id": 1,
  "name": "食物名称",
  "code": "食物编码",
  "category": "食物类别",
  "food_type": "食物类型",
  "description": "食物描述",
  "nutritional_profile": {
    "calory": 100.0,
    "protein": 10.0,
    "fat": 5.0,
    "carb": 15.0,
    "fiber": 2.0,
    "sodium": 100.0
  },
  "images": ["image_url1", "image_url2"],
  "hot": 100,
  "created_at": "2024-04-05T12:00:00"
}
```

## 食物识别接口

### 分析食物图片

```
POST /food-recognition/analyze
```

**请求头**
- Authorization: Bearer {token}
- Content-Type: multipart/form-data

**请求参数**
- image: 食物图片文件

**响应内容**

```json
{
  "id": "recognition_id",
  "foods": [
    {
      "name": "食物名称",
      "confidence": 0.95,
      "bounding_box": [x1, y1, x2, y2]
    }
  ],
  "status": "pending"
}
```

### 确认食物识别结果

```
POST /food-recognition/{id}/confirm
```

**请求头**
- Authorization: Bearer {token}

**请求参数**

```json
{
  "food_id": "确认的食物ID",
  "quantity": 100,
  "unit": "g"
}
```

**响应内容**

```json
{
  "success": true,
  "message": "确认成功"
}
```

### 获取待处理的食物识别列表

```
GET /food-recognition/pending
```

**请求头**
- Authorization: Bearer {token}

**响应内容**

```json
{
  "items": [
    {
      "id": "recognition_id",
      "image_url": "图片URL",
      "created_at": "2024-04-05T12:00:00"
    }
  ]
}
```

## 餐食记录接口

### 获取用户餐食记录

```
GET /meal/
```

**请求头**
- Authorization: Bearer {token}

**查询参数**
- start_date: 开始日期
- end_date: 结束日期
- skip: 跳过的记录数
- limit: 返回的记录数

**响应内容**

```json
{
  "items": [
    {
      "id": 1,
      "meal_type": "breakfast",
      "meal_date": "2024-04-05",
      "food_items": [
        {
          "id": 1,
          "name": "食物名称",
          "quantity": 100,
          "unit": "g",
          "calory": 100.0
        }
      ],
      "total_calory": 500.0,
      "created_at": "2024-04-05T12:00:00"
    }
  ],
  "total": 10
}
```

### 创建餐食记录

```
POST /meal/
```

**请求头**
- Authorization: Bearer {token}

**请求参数**

```json
{
  "meal_type": "breakfast",
  "meal_date": "2024-04-05",
  "food_items": [
    {
      "name": "食物名称",
      "quantity": 100,
      "unit": "g"
    }
  ],
  "notes": "备注信息"
}
```

**响应内容**

```json
{
  "id": 1,
  "meal_type": "breakfast",
  "meal_date": "2024-04-05",
  "food_items": [
    {
      "id": 1,
      "name": "食物名称",
      "quantity": 100,
      "unit": "g",
      "calory": 100.0
    }
  ],
  "total_calory": 500.0,
  "created_at": "2024-04-05T12:00:00"
}
```

## 营养相关接口

### 获取每日营养素目标

```
POST /nutrient/daily-target
```

**请求参数**

```json
{
  "age": 30,
  "sex": "male",
  "pregnancy_stage": "early",  // 可选，仅适用于女性
  "dietary_names": ["vitamin_a", "phosphor"]  // 可选，需要查询的营养素列表
}
```

**响应内容**

```json
{
  "nutrients": {
    "vitamin_a": {
      "value": 900,
      "unit": "μg",
      "description": "维生素A每日推荐摄入量"
    },
    "phosphor": {
      "value": 700,
      "unit": "mg",
      "description": "磷每日推荐摄入量"
    }
  }
}
```

### 获取营养素推荐

```
GET /nutrient/recommendation
```

**查询参数**
- age: 用户年龄(岁)
- sex: 性别(male/female)
- pregnancy_stage: 孕期阶段(early/mid/late/lactation)（可选）
- dietary_names: 营养素字段名称列表（可选）

**响应内容**

```json
{
  "nutrients": {
    "vitamin_a": {
      "value": 900,
      "unit": "μg",
      "description": "维生素A每日推荐摄入量"
    },
    "phosphor": {
      "value": 700,
      "unit": "mg",
      "description": "磷每日推荐摄入量"
    }
  }
}
```

## 健康建议接口

### 获取餐食健康建议

```
GET /health-recommendation/{meal_id}/recommendations
```

**请求头**
- Authorization: Bearer {token}

**响应内容**

```json
{
  "items": [
    {
      "id": 1,
      "meal_id": 1,
      "type": "nutrition",
      "content": "建议减少脂肪摄入",
      "severity": "warning",
      "created_at": "2024-04-05T12:00:00"
    }
  ],
  "total": 1
}
```

### 创建健康建议

```
POST /health-recommendation/{meal_id}/recommendations
```

**请求头**
- Authorization: Bearer {token}

**请求参数**

```json
{
  "type": "nutrition",
  "content": "建议减少脂肪摄入",
  "severity": "warning"
}
```

**响应内容**

```json
{
  "id": 1,
  "meal_id": 1,
  "type": "nutrition",
  "content": "建议减少脂肪摄入",
  "severity": "warning",
  "created_at": "2024-04-05T12:00:00"
}
```

## 收藏接口

### 收藏/取消收藏运动

```
POST /favorites/exercises/{exercise_id}/favorite
```

**请求头**
- Authorization: Bearer {token}

**请求参数**
- notes: 备注信息（可选）

**响应内容**

```json
{
  "success": true,
  "is_favorite": true,
  "message": "收藏成功"
}
```

### 收藏/取消收藏食物

```
POST /favorites/foods/{food_id}/favorite
```

**请求头**
- Authorization: Bearer {token}

**请求参数**
- notes: 备注信息（可选）

**响应内容**

```json
{
  "success": true,
  "is_favorite": true,
  "message": "收藏成功"
}
```

### 获取收藏的运动列表

```
GET /favorites/exercises/favorites
```

**请求头**
- Authorization: Bearer {token}

**查询参数**
- skip: 跳过的记录数
- limit: 返回的记录数

**响应内容**

```json
{
  "items": [
    {
      "id": 1,
      "name": "运动名称",
      "description": "运动描述",
      "image_url": "图片URL",
      "notes": "备注信息",
      "created_at": "2024-04-05T12:00:00"
    }
  ],
  "total": 10
}
```

## 健康检查接口

### 服务状态检查

```
GET /ping
```

**响应内容**

```json
{
  "status": "success",
  "message": "pong"
}
```

## 错误响应格式

当API调用出错时，会返回如下格式的错误信息：

```json
{
  "detail": "错误详细信息"
}
```

或者更详细的错误信息：

```json
{
  "detail": {
    "detail": "用户认证失败",
    "code": "token_expired"
  }
}
```

## 接口文档访问

- Swagger UI: http://localhost:8000/api/v1/docs
- OpenAPI JSON: http://localhost:8000/api/v1/openapi.json

## 社区接口

### 创建单日训练

```
POST /community/daily-workouts/
```

**请求头**
- Authorization: Bearer {token}

**请求参数**

```json
{
  "title": "训练标题",
  "description": "训练描述",
  "exercises": [
    {
      "name": "运动名称",
      "sets": 3,
      "reps": 12,
      "weight": 20.0
    }
  ]
}
```

**响应内容**

```json
{
  "id": 1,
  "title": "训练标题",
  "description": "训练描述",
  "exercises": [
    {
      "id": 1,
      "name": "运动名称",
      "sets": 3,
      "reps": 12,
      "weight": 20.0
    }
  ],
  "created_at": "2024-04-05T12:00:00"
}
```

### 获取单日训练列表

```
GET /community/daily-workouts/
```

**请求头**
- Authorization: Bearer {token}（可选）

**查询参数**
- skip: 跳过的记录数
- limit: 返回的记录数

**响应内容**

```json
{
  "items": [
    {
      "id": 1,
      "title": "训练标题",
      "description": "训练描述",
      "exercises": [
        {
          "id": 1,
          "name": "运动名称",
          "sets": 3,
          "reps": 12,
          "weight": 20.0
        }
      ],
      "created_at": "2024-04-05T12:00:00"
    }
  ],
  "total": 10
}
```

### 获取单日训练详情

```
GET /community/daily-workouts/{id}
```

**请求头**
- Authorization: Bearer {token}（可选）

**响应内容**

```json
{
  "id": 1,
  "title": "训练标题",
  "description": "训练描述",
  "exercises": [
    {
      "id": 1,
      "name": "运动名称",
      "sets": 3,
      "reps": 12,
      "weight": 20.0
    }
  ],
  "created_at": "2024-04-05T12:00:00"
}
```

### 创建帖子

```
POST /community/posts/
```

**请求头**
- Authorization: Bearer {token}
- Content-Type: multipart/form-data

**请求参数**
- title: 帖子标题
- content: 帖子内容
- related_workout_id: 关联的单日训练ID（可选）
- images: 上传的图片列表（可选）

**响应内容**

```json
{
  "id": 1,
  "title": "帖子标题",
  "content": "帖子内容",
  "images": ["image_url1", "image_url2"],
  "created_at": "2024-04-05T12:00:00",
  "author": {
    "id": 1,
    "nickname": "用户昵称",
    "avatar_url": "avatar_url"
  }
}
```

### 获取帖子列表

```
GET /community/posts/
```

**请求头**
- Authorization: Bearer {token}（可选）

**查询参数**
- skip: 跳过的记录数
- limit: 返回的记录数

**响应内容**

```json
{
  "items": [
    {
      "id": 1,
      "title": "帖子标题",
      "content": "帖子内容",
      "images": ["image_url1", "image_url2"],
      "created_at": "2024-04-05T12:00:00",
      "author": {
        "id": 1,
        "nickname": "用户昵称",
        "avatar_url": "avatar_url"
      }
    }
  ],
  "total": 10
}
```

### 获取帖子详情

```
GET /community/posts/{id}
```

**请求头**
- Authorization: Bearer {token}（可选）

**响应内容**

```json
{
  "id": 1,
  "title": "帖子标题",
  "content": "帖子内容",
  "images": ["image_url1", "image_url2"],
  "created_at": "2024-04-05T12:00:00",
  "author": {
    "id": 1,
    "nickname": "用户昵称",
    "avatar_url": "avatar_url"
  }
}
```

### 获取用户帖子列表

```
GET /community/users/{user_id}/posts/
```

**请求头**
- Authorization: Bearer {token}（可选）

**查询参数**
- skip: 跳过的记录数
- limit: 返回的记录数

**响应内容**

```json
{
  "items": [
    {
      "id": 1,
      "title": "帖子标题",
      "content": "帖子内容",
      "images": ["image_url1", "image_url2"],
      "created_at": "2024-04-05T12:00:00",
      "author": {
        "id": 1,
        "nickname": "用户昵称",
        "avatar_url": "avatar_url"
      }
    }
  ],
  "total": 10
}
```

### 创建评论

```
POST /community/posts/{id}/comments/
```

**请求头**
- Authorization: Bearer {token}

**请求参数**

```json
{
  "content": "评论内容",
  "parent_id": 0  // 可选，回复的评论ID
}
```

**响应内容**

```json
{
  "id": 1,
  "content": "评论内容",
  "user_id": 1,
  "post_id": 1,
  "parent_id": null,
  "status": "active",
  "reported_count": 0,
  "created_at": "2024-04-05T12:00:00",
  "updated_at": "2024-04-05T12:00:00",
  "like_count": 0,
  "is_liked_by_current_user": false,
  "user": {
    "id": 1,
    "nickname": "用户昵称",
    "avatar_url": "avatar_url"
  },
  "replies": []
}
```

### 获取评论列表

```
GET /community/posts/{id}/comments/
```

**请求头**
- Authorization: Bearer {token}（可选）

**查询参数**
- skip: 跳过的记录数
- limit: 返回的记录数

**响应内容**

```json
{
  "items": [
    {
      "id": 1,
      "content": "评论内容",
      "user_id": 1,
      "post_id": 1,
      "parent_id": null,
      "status": "active",
      "reported_count": 0,
      "created_at": "2024-04-05T12:00:00",
      "updated_at": "2024-04-05T12:00:00",
      "like_count": 0,
      "is_liked_by_current_user": false,
      "user": {
        "id": 1,
        "nickname": "用户昵称",
        "avatar_url": "avatar_url"
      },
      "replies": []
    }
  ],
  "total": 10
}
```

### 点赞帖子

```
POST /community/posts/{id}/like/
```

**请求头**
- Authorization: Bearer {token}

**响应内容**

```json
{
  "success": true,
  "message": "点赞成功",
  "like_count": 10
}
```

### 点赞评论

```
POST /community/comments/{id}/like/
```

**请求头**
- Authorization: Bearer {token}

**响应内容**

```json
{
  "success": true,
  "message": "点赞成功",
  "like_count": 5
}
```

### 举报帖子

```
POST /community/posts/{id}/report/
```

**请求头**
- Authorization: Bearer {token}

**请求参数**
- reason: 举报原因

**响应内容**

```json
{
  "success": true,
  "message": "举报成功"
}
```

### 举报评论

```
POST /community/comments/{id}/report/
```

**请求头**
- Authorization: Bearer {token}

**请求参数**
- reason: 举报原因

**响应内容**

```json
{
  "success": true,
  "message": "举报成功"
}
```

### 管理帖子

```
PUT /community/posts/{id}/moderate/
```

**请求头**
- Authorization: Bearer {token}（需要管理员权限）

**请求参数**

```json
{
  "action": "delete"  // 可选值：delete, hide, restore
}
```

**响应内容**

```json
{
  "success": true,
  "message": "操作成功"
}
```

### 管理评论

```
PUT /community/comments/{id}/moderate/
```

**请求头**
- Authorization: Bearer {token}（需要管理员权限）

**请求参数**

```json
{
  "action": "delete"  // 可选值：delete, hide, restore
}
```

**响应内容**

```json
{
  "success": true,
  "message": "操作成功"
}
```

### 获取通知列表

```
GET /community/notifications/
```

**请求头**
- Authorization: Bearer {token}

**查询参数**
- skip: 跳过的记录数
- limit: 返回的记录数

**响应内容**

```json
{
  "items": [
    {
      "id": 1,
      "type": "like",
      "content": "用户A点赞了你的帖子",
      "is_read": false,
      "created_at": "2024-04-05T12:00:00"
    }
  ],
  "total": 10
}
```

### 标记通知为已读

```
PATCH /community/notifications/{id}/read/
```

**请求头**
- Authorization: Bearer {token}

**响应内容**

```json
{
  "success": true,
  "message": "标记成功"
}
```

### 标记所有通知为已读

```
PATCH /community/notifications/read-all/
```

**请求头**
- Authorization: Bearer {token}

**响应内容**

```json
{
  "success": true,
  "message": "标记成功"
}
```

### 获取图片

```
GET /community/images/{filename}
```

**响应内容**
- 图片文件 

## 训练计划接口

### 生成训练计划

```
POST /training-plans/generate
```

**请求头**
- Authorization: Bearer {token}

**请求参数**

```json
{
  "goal": "训练目标",
  "level": "训练水平",
  "duration": 12,
  "frequency": 3,
  "preferences": {
    "equipment": ["哑铃", "杠铃"],
    "body_parts": ["胸部", "背部"]
  }
}
```

**响应内容**

```json
{
  "id": "plan_id",
  "name": "训练计划名称",
  "description": "计划描述",
  "weeks": [
    {
      "week_number": 1,
      "sessions": [
        {
          "day": "周一",
          "exercises": [
            {
              "name": "运动名称",
              "sets": 3,
              "reps": 12,
              "rest": 60
            }
          ]
        }
      ]
    }
  ]
}
```

### 获取用户训练计划列表

```
GET /training-plans/user
```

**请求头**
- Authorization: Bearer {token}

**响应内容**

```json
{
  "items": [
    {
      "id": "plan_id",
      "name": "训练计划名称",
      "created_at": "2024-04-05T12:00:00",
      "status": "active"
    }
  ]
}
```

## 聊天接口

### 发送消息

```
POST /chat/messages
```

**请求头**
- Authorization: Bearer {token}

**请求参数**

```json
{
  "content": "消息内容",
  "conversation_id": "会话ID"  // 可选，不提供则创建新会话
}
```

**响应内容**

```json
{
  "message_id": "消息ID",
  "content": "AI回复内容",
  "conversation_id": "会话ID",
  "created_at": "2024-04-05T12:00:00"
}
```

### 获取会话列表

```
GET /chat/conversations
```

**请求头**
- Authorization: Bearer {token}

**响应内容**

```json
{
  "items": [
    {
      "id": "conversation_id",
      "title": "会话标题",
      "last_message": "最后一条消息",
      "updated_at": "2024-04-05T12:00:00"
    }
  ]
}
```

### 删除会话

```
DELETE /chat/conversations/{id}
```

**请求头**
- Authorization: Bearer {token}

**响应内容**

```json
{
  "success": true,
  "message": "删除成功"
}
```

## 分享和二维码接口

### 生成分享码

```
POST /share/generate
```

**请求头**
- Authorization: Bearer {token}

**请求参数**

```json
{
  "type": "workout",  // 可选值：workout, meal, plan
  "content_id": "内容ID"
}
```

**响应内容**

```json
{
  "share_code": "分享码",
  "expires_at": "2024-04-06T12:00:00"
}
```

### 扫描分享码

```
POST /share/scan
```

**请求头**
- Authorization: Bearer {token}

**请求参数**

```json
{
  "share_code": "分享码"
}
```

**响应内容**

```json
{
  "type": "workout",
  "content": {
    "id": "内容ID",
    "title": "内容标题"
  }
}
```

### 生成二维码

```
POST /qrcode/generate
```

**请求头**
- Authorization: Bearer {token}

**请求参数**

```json
{
  "data": "二维码数据",
  "size": 200  // 可选，默认200
}
```

**响应内容**

```json
{
  "image_url": "二维码图片URL",
  "expires_at": "2024-04-06T12:00:00"
}
```

## LLM日志接口

### 获取日期日志

```
GET /llm-logs/date/{date}
```

**请求头**
- Authorization: Bearer {token}（需要管理员权限）

**响应内容**

```json
{
  "items": [
    {
      "id": "log_id",
      "user_id": "用户ID",
      "prompt": "用户输入",
      "response": "AI回复",
      "created_at": "2024-04-05T12:00:00"
    }
  ]
}
```

### 获取用户日志

```
GET /llm-logs/user/{user_id}
```

**请求头**
- Authorization: Bearer {token}（需要管理员权限）

**查询参数**
- start_date: 开始日期
- end_date: 结束日期

**响应内容**

```json
{
  "items": [
    {
      "id": "log_id",
      "prompt": "用户输入",
      "response": "AI回复",
      "created_at": "2024-04-05T12:00:00"
    }
  ]
}
```

## 运动相关接口

### 获取运动列表

```
GET /exercise/
```

**请求头**
- Authorization: Bearer {token}

**查询参数**
- skip: 跳过的记录数
- limit: 返回的记录数
- body_part: 身体部位（可选）
- equipment: 器械类型（可选）

**响应内容**

```json
{
  "items": [
    {
      "id": 1,
      "name": "运动名称",
      "description": "运动描述",
      "body_part": "胸部",
      "equipment": "哑铃",
      "image_url": "图片URL",
      "video_url": "视频URL",
      "difficulty": "beginner",
      "muscles": ["胸大肌", "三角肌"],
      "instructions": ["步骤1", "步骤2"],
      "tips": ["提示1", "提示2"],
      "created_at": "2024-04-05T12:00:00"
    }
  ],
  "total": 10
}
```

### 搜索运动

```
GET /exercise/search
```

**请求头**
- Authorization: Bearer {token}

**查询参数**
- keyword: 搜索关键词
- body_part: 身体部位（可选）
- equipment: 器械类型（可选）
- difficulty: 难度级别（可选）
- limit: 返回结果数量上限

**响应内容**

```json
{
  "items": [
    {
      "id": 1,
      "name": "运动名称",
      "description": "运动描述",
      "body_part": "胸部",
      "equipment": "哑铃",
      "image_url": "图片URL",
      "difficulty": "beginner"
    }
  ],
  "total": 10
}
```

### 创建运动

```
POST /exercise/
```

**请求头**
- Authorization: Bearer {token}

**请求参数**

```json
{
  "name": "运动名称",
  "description": "运动描述",
  "body_part": "胸部",
  "equipment": "哑铃",
  "image_url": "图片URL",
  "video_url": "视频URL",
  "difficulty": "beginner",
  "muscles": ["胸大肌", "三角肌"],
  "instructions": ["步骤1", "步骤2"],
  "tips": ["提示1", "提示2"]
}
```

**响应内容**

```json
{
  "id": 1,
  "name": "运动名称",
  "description": "运动描述",
  "body_part": "胸部",
  "equipment": "哑铃",
  "image_url": "图片URL",
  "video_url": "视频URL",
  "difficulty": "beginner",
  "muscles": ["胸大肌", "三角肌"],
  "instructions": ["步骤1", "步骤2"],
  "tips": ["提示1", "提示2"],
  "created_at": "2024-04-05T12:00:00"
}
```

### 获取运动详情

```
GET /exercise/{exercise_id}
```

**请求头**
- Authorization: Bearer {token}

**响应内容**

```json
{
  "id": 1,
  "name": "运动名称",
  "description": "运动描述",
  "body_part": "胸部",
  "equipment": "哑铃",
  "image_url": "图片URL",
  "video_url": "视频URL",
  "difficulty": "beginner",
  "muscles": ["胸大肌", "三角肌"],
  "instructions": ["步骤1", "步骤2"],
  "tips": ["提示1", "提示2"],
  "created_at": "2024-04-05T12:00:00"
}
```

### 记录运动点击

```
POST /exercise/{exercise_id}/hit
```

**请求头**
- Authorization: Bearer {token}

**响应内容**

```json
{
  "success": true,
  "message": "记录成功"
}
```

### 更新运动

```
PUT /exercise/{exercise_id}
```

**请求头**
- Authorization: Bearer {token}

**请求参数**

```json
{
  "name": "运动名称",
  "description": "运动描述",
  "body_part": "胸部",
  "equipment": "哑铃",
  "image_url": "图片URL",
  "video_url": "视频URL",
  "difficulty": "beginner",
  "muscles": ["胸大肌", "三角肌"],
  "instructions": ["步骤1", "步骤2"],
  "tips": ["提示1", "提示2"]
}
```

**响应内容**

```json
{
  "id": 1,
  "name": "运动名称",
  "description": "运动描述",
  "body_part": "胸部",
  "equipment": "哑铃",
  "image_url": "图片URL",
  "video_url": "视频URL",
  "difficulty": "beginner",
  "muscles": ["胸大肌", "三角肌"],
  "instructions": ["步骤1", "步骤2"],
  "tips": ["提示1", "提示2"],
  "updated_at": "2024-04-05T12:00:00"
}
```

### 删除运动

```
DELETE /exercise/{exercise_id}
```

**请求头**
- Authorization: Bearer {token}

**响应内容**

```json
{
  "success": true,
  "message": "删除成功"
}
```

### 获取运动详细说明

```
GET /exercise/{exercise_id}/detail
```

**请求头**
- Authorization: Bearer {token}

**响应内容**

```json
{
  "id": 1,
  "exercise_id": 1,
  "muscles": [
    {
      "name": "胸大肌",
      "role": "主要发力肌群"
    }
  ],
  "instructions": [
    {
      "step": 1,
      "description": "步骤描述",
      "image_url": "图片URL"
    }
  ],
  "tips": [
    {
      "content": "提示内容",
      "type": "safety"
    }
  ],
  "variations": [
    {
      "name": "变式名称",
      "description": "变式描述",
      "difficulty": "intermediate"
    }
  ]
}
```

### 创建运动详细说明

```
POST /exercise/{exercise_id}/detail
```

**请求头**
- Authorization: Bearer {token}

**请求参数**

```json
{
  "muscles": [
    {
      "name": "胸大肌",
      "role": "主要发力肌群"
    }
  ],
  "instructions": [
    {
      "step": 1,
      "description": "步骤描述",
      "image_url": "图片URL"
    }
  ],
  "tips": [
    {
      "content": "提示内容",
      "type": "safety"
    }
  ],
  "variations": [
    {
      "name": "变式名称",
      "description": "变式描述",
      "difficulty": "intermediate"
    }
  ]
}
```

**响应内容**

```json
{
  "id": 1,
  "exercise_id": 1,
  "muscles": [
    {
      "name": "胸大肌",
      "role": "主要发力肌群"
    }
  ],
  "instructions": [
    {
      "step": 1,
      "description": "步骤描述",
      "image_url": "图片URL"
    }
  ],
  "tips": [
    {
      "content": "提示内容",
      "type": "safety"
    }
  ],
  "variations": [
    {
      "name": "变式名称",
      "description": "变式描述",
      "difficulty": "intermediate"
    }
  ],
  "created_at": "2024-04-05T12:00:00"
}
```

### 获取肌肉列表

```
GET /exercise/muscles
```

**请求头**
- Authorization: Bearer {token}

**响应内容**

```json
{
  "items": [
    {
      "id": 1,
      "name": "肌肉名称",
      "description": "肌肉描述",
      "image_url": "图片URL"
    }
  ],
  "total": 10
}
```

### 获取身体部位列表

```
GET /exercise/body-parts
```

**请求头**
- Authorization: Bearer {token}

**响应内容**

```json
{
  "items": [
    {
      "id": 1,
      "name": "身体部位名称",
      "description": "部位描述",
      "image_url": "图片URL"
    }
  ],
  "total": 10
}
```

### 获取器械列表

```
GET /exercise/equipment
```

**请求头**
- Authorization: Bearer {token}

**响应内容**

```json
{
  "items": [
    {
      "id": 1,
      "name": "器械名称",
      "description": "器械描述",
      "image_url": "图片URL"
    }
  ],
  "total": 10
}
``` 