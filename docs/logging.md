# 日志管理文档

本文档详细说明了ScienceFit后端系统的日志配置、管理和最佳实践。正确的日志配置对于系统监控、调试和性能优化至关重要。

## 目录

1. [日志系统概述](#日志系统概述)
2. [日志配置](#日志配置)
3. [日志级别](#日志级别)
4. [日志文件位置](#日志文件位置)
5. [SQLAlchemy日志配置](#sqlalchemy日志配置)
6. [Uvicorn日志配置](#uvicorn日志配置)
7. [自定义日志记录器](#自定义日志记录器)
8. [日志最佳实践](#日志最佳实践)
9. [常见问题排查](#常见问题排查)

## 日志系统概述

ScienceFit后端使用Python标准库的`logging`模块进行日志管理，并针对不同组件（如SQLAlchemy、Uvicorn等）进行了特定配置。系统日志分为以下几个主要部分：

- **应用日志**：记录应用程序的运行状态、请求处理和业务逻辑
- **数据库日志**：记录数据库操作和SQL查询（可配置详细程度）
- **服务器日志**：记录Uvicorn服务器的运行状态
- **LLM调用日志**：记录大模型API调用的请求和响应

## 日志配置

### 全局日志配置

全局日志配置在`app/main.py`中设置：

```python
# 配置日志
log_file = "logs.txt"
# 配置日志，同时输出到文件和控制台
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()  # 添加控制台输出
    ]
)
```

### 环境变量配置

日志级别可以通过环境变量或配置文件设置，在`app/core/config.py`中定义：

```python
# 日志配置
LOG_LEVEL: str = "DEBUG"  # 设置为DEBUG级别以输出所有日志
CONSOLE_LOG: bool = True  # 启用控制台日志输出
```

## 日志级别

系统使用标准的Python日志级别，从低到高依次为：

- **DEBUG**：详细的调试信息，通常仅在开发环境使用
- **INFO**：常规信息，记录应用正常运行的状态
- **WARNING**：警告信息，表示可能的问题但不影响系统运行
- **ERROR**：错误信息，表示发生了错误但应用仍在运行
- **CRITICAL**：严重错误，可能导致应用崩溃或无法运行

在生产环境中，建议将日志级别设置为`INFO`或`WARNING`，以减少日志量并提高性能。

## 日志文件位置

系统日志文件存储在以下位置：

- **主应用日志**：`logs.txt`（项目根目录）
- **应用详细日志**：`logs/app.log`（由`app/logger/logger.py`创建）
- **Uvicorn服务器日志**：`uvicorn.log`（使用`uvicorn app.main:app > uvicorn.log 2>&1`启动时）
- **LLM调用日志**：`/data/llm_logs/{日期}/user_{用户ID}/{时间戳}`

## SQLAlchemy日志配置

SQLAlchemy日志配置在`app/main.py`中设置，用于控制数据库操作的日志输出：

```python
# 设置 SQLAlchemy Engine 的日志级别
sql_engine_logger = logging.getLogger("sqlalchemy.engine")
sql_engine_logger.setLevel(logging.ERROR)  # 只显示ERROR级别的日志，完全禁止INFO和WARNING
sql_engine_logger.propagate = False  # 阻止消息向上传播

# 同时设置sqlalchemy.pool的日志级别
sql_pool_logger = logging.getLogger("sqlalchemy.pool")
sql_pool_logger.setLevel(logging.ERROR)
sql_pool_logger.propagate = False

# 设置sqlalchemy.orm的日志级别
sql_orm_logger = logging.getLogger("sqlalchemy.orm")
sql_orm_logger.setLevel(logging.ERROR)
sql_orm_logger.propagate = False
```

在数据库引擎创建时，也可以通过`echo`参数控制SQL语句的日志输出：

```python
# 在app/db/session.py中
engine = create_engine(
    db_url,
    pool_size=50,
    max_overflow=50,
    pool_timeout=20,
    pool_recycle=1200,
    pool_pre_ping=True,
    echo=False  # 禁用SQL语句日志记录，无论是否为开发环境
)
```

### 控制SQL语句日志

如果需要在开发环境中查看SQL语句，可以修改以下配置：

1. 在`app/main.py`中将SQLAlchemy日志级别设置为`INFO`：
   ```python
   sql_engine_logger.setLevel(logging.INFO)
   ```

2. 在`app/db/session.py`中启用SQL语句日志：
   ```python
   echo=True  # 或 echo=settings.IS_DEV
   ```

3. 在`alembic.ini`中修改SQLAlchemy日志级别：
   ```ini
   [logger_sqlalchemy]
   level = INFO
   handlers =
   qualname = sqlalchemy.engine
   ```

## Uvicorn日志配置

Uvicorn服务器的日志配置在启动命令中设置：

### 开发环境（scripts/start.sh）

```bash
# 设置日志级别为warning，禁止显示SQL语句
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --log-level warning
```

### 生产环境（Dockerfile）

```dockerfile
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--log-level", "warning"]
```

### 可用的日志级别

Uvicorn支持以下日志级别：
- `critical`
- `error`
- `warning`
- `info`
- `debug`
- `trace`

## 自定义日志记录器

系统提供了自定义日志记录器的工具，在`app/logger/logger.py`中定义：

```python
def setup_logger(name: str, level: str = "INFO", log_to_file: bool = True):
    """设置并返回一个命名的日志记录器"""
    # 实现细节...

# 默认应用日志记录器
app_logger = setup_logger("fitness-coach-api")

# 获取指定名称的日志记录器
def get_logger(name: str, level: str = "INFO"):
    """获取指定名称的日志记录器"""
    return setup_logger(name, level)
```

### 使用示例

```python
from app.logger import get_logger

# 创建模块专用的日志记录器
logger = get_logger("my-module")

def some_function():
    logger.info("函数开始执行")
    try:
        # 业务逻辑
        logger.debug("详细调试信息")
    except Exception as e:
        logger.error(f"发生错误: {str(e)}")
    logger.info("函数执行完成")
```

## 日志最佳实践

### 1. 选择合适的日志级别

- **DEBUG**：用于详细的调试信息，如变量值、函数调用等
- **INFO**：用于记录正常的业务流程，如请求处理、任务完成等
- **WARNING**：用于记录可能的问题，如性能下降、非关键错误等
- **ERROR**：用于记录错误情况，如API调用失败、数据库错误等
- **CRITICAL**：用于记录严重错误，如系统崩溃、无法恢复的错误等

### 2. 结构化日志信息

使用一致的格式记录日志，包含必要的上下文信息：

```python
logger.info(f"处理请求: 用户ID={user_id}, 操作={action}, 参数={params}")
```

### 3. 敏感信息处理

确保不在日志中记录敏感信息，如密码、令牌等：

```python
# 使用SafeLogging工具类处理敏感信息
from app.utils.safe_logging import SafeLogging

safe_data = SafeLogging.mask_sensitive_data(request_data)
logger.info(f"请求数据: {safe_data}")
```

### 4. 异常日志记录

记录异常时，包含完整的堆栈信息：

```python
try:
    # 业务逻辑
except Exception as e:
    logger.error(f"操作失败: {str(e)}", exc_info=True)
```

### 5. 性能考虑

- 在生产环境中使用较高的日志级别（INFO或WARNING）
- 避免在循环中过度记录日志
- 使用条件判断减少不必要的日志记录：
  ```python
  if logger.isEnabledFor(logging.DEBUG):
      logger.debug(f"复杂计算结果: {expensive_calculation()}")
  ```

## 常见问题排查

### 1. 日志文件过大

- 配置日志轮转，限制单个日志文件大小
- 提高日志级别，减少记录的信息量
- 定期清理旧的日志文件

### 2. SQL语句过多

如果日志中出现大量SQL语句，可以：

- 在`app/db/session.py`中设置`echo=False`
- 将SQLAlchemy日志级别设置为ERROR：
  ```python
  logging.getLogger("sqlalchemy.engine").setLevel(logging.ERROR)
  ```

### 3. 日志不显示

- 检查日志级别设置是否正确
- 确认日志处理器配置正确
- 验证日志文件路径是否存在且可写

### 4. 日志格式问题

如果日志格式不一致或缺少信息，检查：

- 日志格式字符串配置
- 是否使用了多个不同配置的日志记录器
- 是否正确传递了上下文信息
