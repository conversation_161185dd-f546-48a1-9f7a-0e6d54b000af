# Science Fit 饮食记录系统 - FastAPI 开发文档

## 1. 系统概述

本系统用于记录和管理用户的饮食数据，包括食物识别、营养成分分析、饮食记录管理等功能。系统将从微信小程序云函数架构迁移到基于 FastAPI 的后端服务架构。

## 重要提示：API路径规范

**请注意**: 所有食物识别相关API均使用连字符(-)形式的路径，而非下划线(_)。

正确的路径格式:
```
/api/v1/food-recognition/analyze
/api/v1/food-recognition/{recognition_id}/confirm
/api/v1/food-recognition/pending
```

错误的路径格式:
```
/api/v1/food_recognition/analyze  (使用了下划线)
```

虽然系统配置了从下划线版本到连字符版本的自动重定向，但为了确保API调用的稳定性，请始终使用连字符版本的路径。

## 2. 数据模型设计

### 2.1 用户模型 (User)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | Integer | 用户ID |
| openid | String | 微信openid |
| unionid | String | 微信unionid |
| nickname | String | 用户昵称 |
| avatar_url | String | 头像URL |
| phone | String | 手机号 |
| gender | Enum | 性别(MALE,FEMALE,UNKNOWN) |
| country | String | 国家 |
| province | String | 省份 |
| city | String | 城市 |
| age | Integer | 年龄 |
| weight | Float | 体重(kg) |
| height | Float | 身高(cm) |
| activity_level | Integer | 活动水平(1-5) |
| body_type | String | 体型 |
| experience_level | Enum | 健身经验(BEGINNER,INTERMEDIATE,ADVANCED) |
| fitness_goal | Enum | 健身目标(WEIGHT_LOSS,MUSCLE_GAIN,MAINTENANCE) |
| bmi | Float | 体质指数 |
| tedd | Integer | 每日总能量消耗 |
| completed | Boolean | 是否完成资料 |
| created_at | DateTime | 创建时间 |
| is_active | Boolean | 是否活跃 |

### 2.2 食品库主表 (Food)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | Integer | 主键ID |
| name | VARCHAR(128) | 食物名称 |
| code | VARCHAR(64) | 唯一编码 |
| category | VARCHAR(64) | 食物类别 |
| food_type | VARCHAR(32) | 食物类型 |
| goods_id | Integer | 商品ID |
| thumb_image_url | Text | 缩略图URL |
| large_image_url | Text | 大图URL |
| is_liquid | Boolean | 是否为液体 |
| health_light | SmallInt | 健康信号灯 |
| lights | JSONB | 信号灯详情JSON |
| warnings | JSONB | 警告信息JSON |
| warning_scenes | JSONB | 警告场景JSON |
| calory | Numeric(7,2) | 热量 |
| energy_fractions | JSONB | 能量分配JSON |
| units | JSONB | 单位JSON |
| blood_sugar | JSONB | 血糖指数JSON |
| food_rank | SmallInt | 食物排名 |
| updated_at | Timestamp | 更新时间 |
| can_revise | Boolean | 是否可修改 |

### 2.3 食物营养素明细表 (FoodNutrientValue)

| 字段 | 类型 | 说明 |
|------|------|------|
| id | Serial | 主键ID |
| food_id | Integer | 关联的食物ID |
| name_en | VARCHAR(64) | 营养素英文名 |
| name_cn | VARCHAR(64) | 营养素中文名 |
| value | Numeric(9,4) | 数值 |
| unit | VARCHAR(16) | 单位 |
| unit_name | VARCHAR(16) | 单位名称 |
| precision | SmallInt | 精度 |
| nrv | Numeric(7,2) | 营养参考值 |
| category | VARCHAR(16) | 营养素类别 |

### 2.4 营养目标模型 (NutritionGoal)

```python
class NutritionGoal(BaseModel):
    id: int
    user_id: str  # 关联到用户
    calories_goal: float = 1800  # 默认目标
    carbs_goal: float = 50
    protein_goal: float = 100
    fat_goal: float = 50
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    
    class Config:
        orm_mode = True
```

### 2.5 餐食记录模型 (MealRecord)

```python
class MealType(str, Enum):
    BREAKFAST = "breakfast"
    LUNCH = "lunch"
    DINNER = "dinner"
    SNACK = "snack"

class MealRecord(BaseModel):
    id: int
    user_id: str  # 关联到用户
    date: date
    meal_type: MealType
    image_url: Optional[str] = None
    file_id: Optional[str] = None  # 存储云端文件ID
    thumb_data: Optional[str] = None  # 可选缩略图数据
    total_calory: float = 0  # 餐食总卡路里
    total_protein: float = 0  # 餐食总蛋白质
    total_fat: float = 0  # 餐食总脂肪
    total_carbohydrate: float = 0  # 餐食总碳水化合物
    is_ai_recognized: bool = False  # 是否由AI识别
    json_data: Optional[Dict[str, Any]] = None  # 保存原始JSON数据
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    
    class Config:
        orm_mode = True
```

### 2.6 食物项模型 (FoodItem)

```python
class FoodItem(BaseModel):
    id: int
    meal_record_id: int  # 关联到餐食记录
    food_id: Optional[int] = None  # 关联到食品库
    name: str
    quantity: float = 1.0
    unit_name: str = "份"
    weight: float  # 重量，单位为克
    category: Optional[str] = None
    cuisine_type: Optional[str] = None
    cuisine_type_detail: Optional[str] = None
    image_url: Optional[str] = None
    health_light: Optional[int] = None  # 健康信号灯(1-3)
    lights: List[str] = []  # 营养特点列表["高蛋白", "低脂肪"等]
    warnings: List[str] = []  # 健康警告列表["脂肪偏高"等]
    warning_scenes: List[str] = []  # 警告适用场景
    is_custom: bool = False  # 是否为自定义食物
    calory: float = 0  # 卡路里
    protein: float = 0  # 蛋白质(克)
    fat: float = 0  # 脂肪(克)
    carbohydrate: float = 0  # 碳水化合物(克)
    protein_fraction: Optional[float] = None  # 蛋白质能量占比
    fat_fraction: Optional[float] = None  # 脂肪能量占比
    carb_fraction: Optional[float] = None  # 碳水能量占比
    json_data: Optional[Dict[str, Any]] = None  # 保存原始JSON数据
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    
    class Config:
        orm_mode = True
```
 
### 2.7 食物识别模型 (FoodRecognition)

```python
class FoodRecognition(BaseModel):
    id: int
    user_id: str  # 关联到用户
    meal_date: date  # 餐食日期
    meal_type: str  # 餐食类型
    image_url: str  # 图片URL
    thumb_image_url: Optional[str] = None  # 缩略图URL
    secure_path: str  # 安全路径
    status: str  # 处理状态(processing, completed, error, rejected)
    recognition_result: Optional[Dict[str, Any]] = None  # 识别结果JSON
    nutrition_totals: Optional[Dict[str, Any]] = None  # 总营养值
    matched_foods: Optional[List[Dict[str, Any]]] = None  # 匹配的食物列表
    error_message: Optional[str] = None  # 错误信息
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    
    class Config:
        orm_mode = True
```

### 2.8 营养素摄入模型 (FoodItemNutrientIntake)

```python
class FoodItemNutrientIntake(BaseModel):
    id: int
    food_item_id: int  # 关联到食物项
    name_en: str  # 营养素英文名
    name_cn: str  # 营养素中文名
    value: float  # 摄入值
    unit: str  # 单位
    unit_name: str  # 单位名称
    nrv_percentage: Optional[float] = None  # NRV百分比
    category: str  # 营养素类别
    created_at: datetime = Field(default_factory=datetime.now)
    
    class Config:
        orm_mode = True
```

## 3. 食物识别与记录功能实现

### 3.1 食物识别服务

项目中实现了完整的食物识别服务，主要功能包括：

1. **图像处理与存储**：
   - 支持上传二进制图像和Base64编码图像
   - 图像优化处理（缩放、压缩）
   - 自动生成并存储缩略图
   - 安全存储原始图像

2. **AI食物识别**：
   - 基于OpenAI模型的食物识别
   - 支持自定义识别服务
   - 识别结果包含食物项列表、营养成分和健康建议

3. **识别确认机制**：
   - 临时存储识别结果
   - 支持用户确认或修正识别结果
   - 可上传新图像替换原图
   - 提供Base64图像确认方式

4. **状态追踪**：
   - 识别处理状态跟踪（处理中、已完成、错误、已拒绝）
   - 错误处理和异常记录

### 3.2 餐食服务

餐食服务实现了以下核心功能：

1. **餐食记录管理**：
   - 创建、查询、更新和删除餐食记录
   - 按日期、日期范围查询餐食
   - 获取每日营养汇总

2. **食物项管理**：
   - 添加食物项到餐食记录
   - 关联食品库数据或自定义食物
   - 移除食物项

3. **营养计算**：
   - 根据食物重量和单位自动计算营养摄入
   - 计算餐食总营养值
   - 支持详细营养素摄入记录

4. **用户权限控制**：
   - 确保用户只能访问和修改自己的餐食记录

### 3.3 API端点实现

#### 3.3.1 食物识别API

```python
# 分析食物图片
@router.post("/analyze", response_model=schemas.FoodRecognitionResponse)
async def analyze_food_image(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    image: UploadFile = File(...),
    meal_type: schemas.MealType,
    meal_date: date = None,
    optimize: bool = True
) -> Any

# 分析Base64编码的食物图片
@router.post("/analyze-base64", response_model=schemas.FoodRecognitionResponse)
async def analyze_base64_food_image(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    image_data: schemas.Base64ImageRequest,
    meal_type: schemas.MealType,
    meal_date: date = None,
    optimize: bool = True
) -> Any

# 确认或修正识别结果
@router.post("/{recognition_id}/confirm", response_model=schemas.FoodRecognitionConfirmResponse)
async def confirm_recognition(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    recognition_id: int,
    confirmation: schemas.FoodRecognitionConfirmation,
    image: Optional[UploadFile] = File(None),
    base64_image: Optional[str] = None,
    optimize: bool = True
) -> Any

# 获取待确认的识别记录
@router.get("/pending", response_model=List[schemas.FoodRecognitionResponse])
def get_pending_recognitions(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any

# 获取食物图像
@router.get("/image/{secure_path}/{date}/{filename}", response_class=FileResponse)
async def get_food_image(
    secure_path: str,
    date: str,
    filename: str
)

# 拒绝识别结果
@router.post("/{recognition_id}/reject")
async def reject_recognition(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    recognition_id: int,
    rejection: schemas.FoodRecognitionRejection,
) -> Any
```

#### 3.3.2 餐食记录API

```python
# 获取用户餐食记录列表
@router.get("/", response_model=List[schemas.MealRecordSummary])
def read_user_meals(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    skip: int = 0,
    limit: int = 100,
) -> Any

# 获取用户每日营养汇总
@router.get("/daily", response_model=schemas.DailyNutritionSummary)
def read_daily_nutrition(
    *,
    db: Session = Depends(deps.get_db),
    current_user: models.User = Depends(deps.get_current_active_user),
    query_date: date = Query(None)
) -> Any

# 获取餐食详情
@router.get("/{meal_id}", response_model=schemas.MealRecord)
def read_meal(
    *,
    db: Session = Depends(deps.get_db),
    meal_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any

# 创建餐食记录
@router.post("/", response_model=schemas.MealRecord)
def create_meal(
    *,
    db: Session = Depends(deps.get_db),
    meal_in: schemas.MealRecordCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any

# 更新餐食记录
@router.put("/{meal_id}", response_model=schemas.MealRecord)
def update_meal(
    *,
    db: Session = Depends(deps.get_db),
    meal_id: int,
    meal_in: schemas.MealRecordUpdate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any

# 删除餐食记录
@router.delete("/{meal_id}", response_model=schemas.MealRecord)
def delete_meal(
    *,
    db: Session = Depends(deps.get_db),
    meal_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any

# 添加食物项到餐食
@router.post("/{meal_id}/food-items", response_model=schemas.FoodItem)
def add_food_item(
    *,
    db: Session = Depends(deps.get_db),
    meal_id: int,
    food_item_in: schemas.FoodItemCreate,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any

# 从餐食移除食物项
@router.delete("/food-items/{food_item_id}", response_model=schemas.FoodItem)
def remove_food_item(
    *,
    db: Session = Depends(deps.get_db),
    food_item_id: int,
    current_user: models.User = Depends(deps.get_current_active_user),
) -> Any
```

### 3.4 关键业务逻辑

#### 3.4.1 食物识别流程

1. **图像上传与处理**：
   - 接收用户上传的食物图像
   - 优化图像尺寸和质量
   - 生成缩略图
   - 存储图像到安全位置

2. **AI分析处理**：
   - 调用OpenAI模型分析图像
   - 识别图像中的食物
   - 匹配食物库中的食物数据
   - 计算食物营养成分

3. **结果存储和返回**：
   - 将识别结果存储为临时记录
   - 返回识别的食物项列表
   - 提供健康饮食建议

#### 3.4.2 营养计算逻辑

1. **基于食品库计算**：
   - 关联食品库数据进行精确计算
   - 根据重量和单位自动转换
   - 计算热量和宏量营养素
   - 计算详细营养素摄入

2. **自定义食物计算**：
   - 支持用户提供自定义营养数据
   - 非标准食物的营养计算

3. **餐食营养统计**：
   - 自动累加食物项营养数据
   - 更新餐食总营养值
   - 生成每日营养摄入报告

### 3.4 营养数据查询功能

提供了以下API端点来查询食品营养数据：

```python
# 获取食品列表，支持名称搜索
@router.get("/", response_model=List[schemas.FoodSearch])

# 基于名称搜索食品，使用高级相似度匹配算法
@router.get("/search", response_model=List[schemas.FoodSearch])

# 获取所有食品类别列表
@router.get("/categories", response_model=List[str])

# 获取所有食品类型列表
@router.get("/types", response_model=List[str])

# 通过ID获取食品详情
@router.get("/{food_id}", response_model=schemas.Food)

# 通过编码获取食品详情
@router.get("/code/{code}", response_model=schemas.Food)

# 获取特定食品的指定营养素数据
@router.post("/nutrients", response_model=schemas.NutrientsResponse)
def get_nutrients(
    *,
    db: Session = Depends(deps.get_db),
    nutrient_request: schemas.NutrientRequest,
) -> Any:
    """
    获取特定食品的指定营养素数据。
    
    接收食品ID和营养素名称列表，返回对应的营养素详细数据。
    每个营养素数据包含：value（数值）、unit（单位）、unit_name（单位名称）、
    precision（精度）、nrv（营养参考值）和category（分类）。
    
    示例请求：
    {
        "food_id": 10,
        "nutrient_names": ["膳食纤维", "脂肪酸", "叶酸"]
    }
    
    示例响应：
    {
        "food_id": 10,
        "nutrients": [
            {
                "name_cn": "膳食纤维",
                "value": 2.5,
                "unit": "g",
                "unit_name": "克",
                "precision": 1,
                "nrv": 25.0,
                "category": "碳水化合物"
            },
            ...
        ]
    }
    """
```

## 4. 存储服务

系统实现了专门的存储服务，用于处理图像文件的存储和优化：

1. **图像上传功能**：
   - 支持二进制和Base64图像上传
   - 安全路径生成和访问控制
   - 图像归档和管理

2. **图像优化处理**：
   - 自动缩放过大的图像
   - 压缩图像减少存储空间
   - 生成缩略图便于快速加载

3. **访问控制**：
   - 安全路径设计防止未授权访问
   - 按用户ID和日期分类存储
   - 提供安全的图像访问API

## 5. 数据库设计

### 5.1 数据表结构

```sql
-- 用户表
CREATE TABLE users (
    id VARCHAR(100) PRIMARY KEY,  -- 微信 openId
    name VARCHAR(100),
    avatar_url TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 营养目标表
CREATE TABLE nutrition_goals (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(100) REFERENCES users(id),
    calories_goal FLOAT DEFAULT 1800,
    carbs_goal FLOAT DEFAULT 50,
    protein_goal FLOAT DEFAULT 100,
    fat_goal FLOAT DEFAULT 50,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (user_id)
);

-- 餐食记录表
CREATE TABLE meal_records (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(100) REFERENCES users(id),
    date DATE NOT NULL,
    meal_type VARCHAR(20) NOT NULL,  -- breakfast, lunch, dinner, snack
    image_url TEXT,
    thumb_image_url TEXT,
    file_id VARCHAR(100),
    total_calory FLOAT DEFAULT 0,  -- 餐食总卡路里
    total_protein FLOAT DEFAULT 0,  -- 餐食总蛋白质
    total_fat FLOAT DEFAULT 0,     -- 餐食总脂肪
    total_carbohydrate FLOAT DEFAULT 0,  -- 餐食总碳水化合物
    is_ai_recognized BOOLEAN DEFAULT FALSE,  -- 是否由AI识别
    meal_name VARCHAR(100),  -- 餐食名称
    json_data JSONB,  -- 保存原始JSON数据
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 食物项表
CREATE TABLE food_items (
    id SERIAL PRIMARY KEY,
    meal_record_id INTEGER REFERENCES meal_records(id) ON DELETE CASCADE,
    food_id INTEGER REFERENCES food(id),  -- 关联到食品库
    name VARCHAR(100) NOT NULL,
    quantity FLOAT DEFAULT 1.0,
    unit_name VARCHAR(20) DEFAULT '份',
    weight FLOAT NOT NULL,  -- 以克为单位
    category VARCHAR(50),
    cuisine_type VARCHAR(50),
    cuisine_type_detail VARCHAR(100),
    image_url TEXT,
    health_light SMALLINT,  -- 健康信号灯(1-3)
    lights JSONB,  -- 营养特点列表["高蛋白", "低脂肪"等]
    warnings JSONB,  -- 健康警告列表["脂肪偏高"等]
    warning_scenes JSONB,  -- 警告适用场景
    is_custom BOOLEAN DEFAULT FALSE,  -- 是否为自定义食物
    calory FLOAT DEFAULT 0,  -- 卡路里
    protein FLOAT DEFAULT 0,  -- 蛋白质(克)
    fat FLOAT DEFAULT 0,  -- 脂肪(克)
    carbohydrate FLOAT DEFAULT 0,  -- 碳水化合物(克)
    protein_fraction FLOAT,  -- 蛋白质能量占比
    fat_fraction FLOAT,  -- 脂肪能量占比
    carb_fraction FLOAT,  -- 碳水能量占比
    json_data JSONB,  -- 保存原始JSON数据
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 食物项营养素摄入表
CREATE TABLE food_item_nutrient_intakes (
    id SERIAL PRIMARY KEY,
    food_item_id INTEGER REFERENCES food_items(id) ON DELETE CASCADE,
    name_en VARCHAR(64) NOT NULL,
    name_cn VARCHAR(64) NOT NULL,
    value FLOAT NOT NULL,
    unit VARCHAR(16) NOT NULL,
    unit_name VARCHAR(16) NOT NULL,
    nrv_percentage FLOAT,
    category VARCHAR(32) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 食物识别表
CREATE TABLE food_recognitions (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(100) REFERENCES users(id),
    meal_date DATE NOT NULL,
    meal_type VARCHAR(20) NOT NULL,
    image_url TEXT NOT NULL,
    thumb_image_url TEXT,
    secure_path VARCHAR(255) NOT NULL,
    status VARCHAR(20) NOT NULL,  -- processing, completed, error, rejected
    recognition_result JSONB,
    nutrition_totals JSONB,
    matched_foods JSONB,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 5.2 索引设计

```sql
-- 用户营养数据查询优化
CREATE INDEX idx_meal_records_user_date ON meal_records(user_id, date);

-- 餐食食物项查询优化
CREATE INDEX idx_food_items_meal_record ON food_items(meal_record_id);

-- 食物项营养素摄入查询优化
CREATE INDEX idx_nutrient_intakes_food_item ON food_item_nutrient_intakes(food_item_id);

-- 食物识别查询优化
CREATE INDEX idx_food_recognitions_user_date ON food_recognitions(user_id, meal_date);
CREATE INDEX idx_food_recognitions_status ON food_recognitions(status);
```

## 6. 项目结构

```
app/
├── __init__.py
├── main.py
├── api/
│   ├── __init__.py
│   ├── deps.py
│   ├── endpoints/
│   │   ├── __init__.py
│   │   ├── food.py  
│   │   ├── food_recognition.py  # 食物识别API
│   │   ├── meal.py  # 餐食记录API
├── core/
│   ├── __init__.py
│   ├── config.py  # 配置文件
├── crud/
│   ├── __init__.py
│   ├── food.py
│   ├── food_item.py
│   ├── food_recognition.py
│   ├── meal.py
├── db/
│   ├── __init__.py
│   ├── base.py
│   ├── session.py
├── models/
│   ├── __init__.py
│   ├── food.py
│   ├── meal.py
│   ├── food_recognition.py
│   ├── user.py
├── schemas/
│   ├── __init__.py
│   ├── food.py
│   ├── food_recognition.py
│   ├── meal.py
│   ├── user.py
├── services/
│   ├── __init__.py
│   ├── food_recognition_service.py  # 食物识别服务
│   ├── meal_service.py  # 餐食服务
│   ├── storage.py  # 存储服务
```

## 7. 食物识别技术实现

### 7.1 图像识别方法

系统基于OpenAI的模型实现图像识别，主要步骤包括：

1. **图像预处理**：调整尺寸、格式转换，优化上传
2. **AI模型调用**：向AI服务发送图像数据
3. **结果解析与处理**：解析AI返回结果，提取食物信息
4. **食物库匹配**：将识别结果与数据库中的食品数据匹配
5. **营养成分计算**：根据匹配结果计算营养成分

### 7.2 存储服务优化

1. **图像优化技术**：
   - 使用Pillow库进行图像处理
   - 自动缩放大图像（保持纵横比）
   - JPEG压缩优化，减少存储空间
   - 缩略图生成，提高加载速度

2. **存储路径设计**：
   - 按用户ID分级存储
   - 按日期和餐食类型归档
   - 使用安全路径防止未授权访问

### 7.3 识别结果处理

1. **识别数据存储**：临时存储识别结果，等待用户确认
2. **结果修正机制**：用户可以修正识别错误的食物
3. **智能匹配**：将识别到的食物与食品库进行匹配
4. **营养计算**：基于匹配结果自动计算营养成分

## 8. 部署要求

### 8.1 系统依赖

- Python 3.8+
- PostgreSQL 12+
- OpenAI API (或兼容API)
- Pillow 图像处理库
- FastAPI 0.68.0+
- SQLAlchemy 1.4+
- Alembic 迁移工具

### 8.2 环境变量配置

主要配置项包括：

```
DATABASE_URL=postgresql://user:password@localhost/db_name
STATIC_FILES_DIR=/path/to/storage
AI_API_KEY=your_api_key
AI_BASE_URL=https://api.openai.com/v1
```

### 8.3 存储要求

- 图像存储空间：根据用户量和使用频率，建议最少10GB
- 数据库存储：PostgreSQL，建议最少5GB
- 备份策略：定期备份数据库和图像文件

## 9. 扩展性考虑

### 9.1 多服务架构

系统设计为模块化架构，各组件可独立扩展：

1. **食物识别服务**：可替换为其他AI服务或本地模型
2. **存储服务**：可迁移到云存储（如AWS S3、阿里云OSS）
3. **数据库服务**：支持主从复制和读写分离

### 9.2 性能优化

1. **图像处理优化**：多线程处理、异步任务队列
2. **识别服务负载均衡**：多实例部署、请求限流
3. **数据库优化**：适当索引、查询优化、数据分区

### 9.3 API版本管理

系统支持API版本管理，便于平滑升级和向后兼容：

1. **版本路由**：通过URL路径(/api/v1/, /api/v2/)区分版本
2. **兼容层**：新版本兼容旧客户端调用
3. **文档更新**：API变更记录和迁移指南

## 10. 迁移规划

从微信小程序云函数架构迁移到FastAPI后端的步骤：

1. **数据迁移**：导出云函数数据，导入新数据库
2. **功能测试**：确保所有API功能正常
3. **性能测试**：验证系统在高负载下的表现
4. **灰度发布**：部分用户先使用新系统
5. **全量上线**：所有用户切换到新系统

@router.delete("/{food_id}", response_model=schemas.Food)
def delete_food(
    *,
    db: Session = Depends(deps.get_db),
    food_id: int,
    current_user: models.User = Depends(deps.get_current_active_superuser),
) -> Any:
    """
    删除食品（仅管理员）。
    """
    food = crud.food.get(db=db, id=food_id)
    if not food:
        raise HTTPException(status_code=404, detail="食品不存在")
    return crud.food.remove(db=db, id=food_id) 

@router.post("/nutrients", response_model=schemas.NutrientsResponse)
def get_nutrients(
    *,
    db: Session = Depends(deps.get_db),
    nutrient_request: schemas.NutrientRequest,
) -> Any:
    """
    获取特定食品的指定营养素数据。
    
    接收食品ID和营养素名称列表，返回对应的营养素详细数据。
    每个营养素数据包含：value（数值）、unit（单位）、unit_name（单位名称）、
    precision（精度）、nrv（营养参考值）和category（分类）。
    """
