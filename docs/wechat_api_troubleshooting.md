# 微信小程序 Chat API 接口文档

## 基础信息
* **基础路径**: `/api/v1/chat`
* **认证方式**: <PERSON><PERSON> (JWT)
* **内容类型**: application/json

## 核心数据模型 (Schemas)

以下是API请求和响应中常用的数据结构：

1.  **`Message` (消息对象)**
    ```json
    {
      "id": 123,                   // 消息的唯一ID (int)
      "content": "你好！",         // 消息内容 (string)
      "role": "user",              // 消息角色 ("user", "assistant", "system") (string)
      "conversation_id": 45,       // 所属会话ID (int)
      "user_id": 67,               // 所属用户ID (int)
      "created_at": "2023-10-27T10:00:00Z", // 创建时间 (string, ISO 8601)
      "updated_at": "2023-10-27T10:00:01Z", // 更新时间 (string, ISO 8601) 
      "meta_info": {               // 元数据 (object, optional)
        "key": "value"
      }
    }
    ```

2.  **`ConversationBrief` (会话简要信息)**
    ```json
    {
      "id": 45,                   // 会话的唯一ID (int)
      "title": "健身计划讨论",     // 会话标题 (string, optional)
      "created_at": "2023-10-27T09:58:00Z", // 创建时间 (string, ISO 8601)
      "updated_at": "2023-10-27T10:05:00Z"  // 最后更新时间 (string, ISO 8601)
    }
    ```

3.  **`ChatRequest` (发送消息请求体)**
    ```json
    {
      "message": "今天练什么？",      // 用户消息内容 (string, required)
      "session_id": "uuid-1234-abcd", // 会话ID (string, optional). 如果不提供或提供新ID，将创建新会话
      "system_prompt": "你是一个专业的健身教练。" // 系统提示 (string, optional). 仅在创建新会话时有效
    }
    ```

4.  **`ChatResponse` (发送消息响应体)**
    ```json
    {
      "response": "今天建议练胸和三头。", // AI回复内容 (string)
      "session_id": "uuid-1234-abcd"     // 当前会话的ID (string)
    }
    ```

5.  **`MessageList` (消息列表响应体)**
    ```json
    {
      "messages": [ Message, Message, ... ], // 消息对象列表 (array)
      "total": 15,                          // 会话中的总消息数 (int)
      "from_cache": false                   // 是否来自缓存 (boolean, optional)
    }
    ```

6.  **`RecentMessageList` (最近消息列表响应体)**
    ```json
    {
      "messages": [ Message, Message, ... ], // 最近消息列表 (array)
      "conversations": {                    // 相关会话信息字典 (object)
        "45": ConversationBrief,
        "48": ConversationBrief,
        ...
      },
      "total": 50                           // 用户总消息数 (int)
    }
    ```

7.  **`BasicResponse` (通用响应)**
    ```json
    {
        "status": "success",
        "message": "操作成功",
        "data": { ... } // 可选的附加数据
    }
    ```

## API 端点详解

### 1. 发送消息 (非流式)

*   **Endpoint**: `POST /message`
*   **认证**: 需要
*   **描述**: 向指定会话（或新会话）发送消息，并获取AI的完整回复。
*   **请求体**: `ChatRequest`
*   **成功响应 (200 OK)**: `ChatResponse`
*   **错误响应**:
    *   `400 Bad Request`: 请求体格式错误。
    *   `404 Not Found`: 如果提供了`session_id`但会话不存在且不属于当前用户。
    *   `429 Too Many Requests`: 请求频率过高。

### 2. 获取用户会话列表

*   **Endpoint**: `GET /conversations`
*   **认证**: 需要
*   **描述**: 获取当前用户的所有活跃会话列表（简要信息）。
*   **查询参数**:
    *   `skip` (int, optional, default=0): 跳过的会话数量（用于分页）。
    *   `limit` (int, optional, default=100): 返回的会话数量上限（用于分页）。
*   **成功响应 (200 OK)**: `ConversationList` (包含 `conversations` 列表和 `total` 总数)
*   **错误响应**: 无特定错误，认证失败除外。

### 3. 获取会话消息

*   **Endpoint**: `GET /conversations/{session_id}/messages`
*   **认证**: 需要
*   **描述**: 获取指定会话的消息列表。默认返回最新的5条。如果会话不存在，会自动为当前用户创建。支持分页加载历史消息。
*   **路径参数**:
    *   `session_id` (string, required): 会话的唯一标识符。
*   **查询参数**:
    *   `skip` (int, optional, default=0): 跳过的消息数量（用于分页加载历史）。
    *   `limit` (int, optional, default=5): 返回的消息数量上限。
*   **成功响应 (200 OK)**: `MessageList`。注意：返回的消息是按时间 **倒序** 排列的（最新的在前面）。`total` 字段表示该会话的总消息数。
*   **错误响应**:
    *   `404 Not Found`: 会话存在但不属于当前用户。

### 4. 获取会话中某条消息之后的新消息

*   **Endpoint**: `GET /conversations/{session_id}/messages/since/{message_id}`
*   **认证**: 需要
*   **描述**: 获取指定会话中，ID大于 `message_id` 的所有新消息。用于轮询或同步。
*   **路径参数**:
    *   `session_id` (string, required): 会话的唯一标识符。
    *   `message_id` (int, required): 作为起点的消息ID。
*   **查询参数**:
    *   `limit` (int, optional, default=20): 返回的消息数量上限。
*   **成功响应 (200 OK)**: `MessageList`。`total` 字段表示本次获取到的新消息数量。
*   **错误响应**:
    *   `404 Not Found`: 会话不存在或不属于当前用户。

### 5. 删除会话

*   **Endpoint**: `DELETE /conversations/{session_id}`
*   **认证**: 需要
*   **描述**: 逻辑删除一个会话（将其标记为非活跃）。如果会话不存在，也返回成功。
*   **路径参数**:
    *   `session_id` (string, required): 要删除的会话ID。
*   **成功响应 (200 OK)**: `{"status": "success"}`
*   **错误响应**:
    *   `404 Not Found`: 会话存在但不属于当前用户。

### 6. 获取用户最近消息（跨会话）

*   **Endpoint**: `GET /recent-messages`
*   **认证**: 需要
*   **描述**: 获取用户所有会话中时间上最近的消息，以及这些消息所属的会话简要信息。用于在应用启动时快速展示最近的对话概览。
*   **查询参数**:
    *   `skip` (int, optional, default=0): 跳过的消息数量。
    *   `limit` (int, optional, default=5): 返回的消息数量上限。
*   **成功响应 (200 OK)**: `RecentMessageList`。消息按时间 **倒序** 排列。`total` 是用户所有消息的总数。
*   **错误响应**: 无特定错误，认证失败除外。

### 7. 获取用户最近会话及消息

*   **Endpoint**: `GET /recent-conversations`
*   **认证**: 需要
*   **描述**: 获取用户所有会话中最近的消息，不区分会话ID，按时间倒序。用于前端启动预加载和下拉刷新。
*   **查询参数**:
    *   `skip` (int, optional, default=0): 跳过记录数。
    *   `limit` (int, optional, default=5): 返回记录数。
*   **成功响应 (200 OK)**: `RecentMessageList`。消息按时间 **倒序**。 `total` 是本次返回的消息数量。
*   **错误响应**: 无特定错误，认证失败除外。

### 8. 获取会话状态

*   **Endpoint**: `GET /stream/{session_id}/status`
*   **认证**: 需要
*   **描述**: 检查指定 `session_id` 的会话是否存在且属于当前用户。
*   **路径参数**:
    *   `session_id` (string, required): 会话ID。
*   **成功响应 (200 OK)**: `BasicResponse` (包含 `session_id` 和 `is_active` 状态)
*   **错误响应**:
    *   `404 Not Found`: 会话不存在或不属于当前用户。

## WebSocket 实时聊天

*   **Endpoint**: `WS /stream/{session_id}`
*   **认证**: 需要通过连接时传递认证信息（Token）。
*   **描述**: 用于实时双向通信。
*   **路径参数**:
    *   `session_id` (string, required): 会话ID。如果不存在，服务器会自动创建。
*   **消息格式**: JSON

*   **客户端 -> 服务器**:
    ```json
    {
      "content": "用户的消息内容"
    }
    ```

*   **服务器 -> 客户端**:
    *   **状态消息**:
        ```json
        {
          "type": "status",
          "message": "会话已创建", // 或其他状态信息
          "session_id": "uuid-1234-abcd"
        }
        ```
    *   **流式响应片段**:
        ```json
        {
          "type": "delta",
          "content": "AI回复的片段", // AI回复的一部分文本
          "is_final": false          // 是否是最后一个片段
        }
        ```
    *   **流式响应结束**:
        ```json
        {
          "type": "delta",
          "content": "",
          "is_final": true
        }
        ```
    *   **错误消息**:
        ```json
        {
          "type": "error",
          "message": "处理消息时发生错误: ..."
        }
        ```

*   **行为**:
    1.  客户端连接WebSocket。
    2.  如果 `session_id` 不存在，服务器创建会话并可能发送 `status` 消息。
    3.  客户端发送包含 `content` 的JSON消息。
    4.  服务器接收消息，保存，调用LLM。
    5.  服务器通过多个 `delta` 类型的消息将AI回复流式传输给客户端。
    6.  最后一个 `delta` 消息的 `is_final` 为 `true`。
    7.  服务器在流式传输结束后，将完整的AI回复保存到数据库。

## WebSocket 兼容接口 (用于不支持WS的环境)

这些接口模拟WebSocket交互，需要前端轮询。

### 1. 模拟连接 (可选)

*   **Endpoint**: `GET /stream/{session_id}/connect`
*   **认证**: 需要
*   **描述**: 客户端可以先调用此接口确保会话存在（不存在则创建）。
*   **路径参数**: `session_id` (string, required)
*   **查询参数**: `token` (string, optional): 如果认证需要通过Query参数传递。
*   **成功响应 (200 OK)**: `BasicResponse`
*   **错误响应**: `403 Forbidden` (无权访问), `500 Internal Server Error`

### 2. 发送消息 (轮询模式)

*   **Endpoint**: `POST /stream/{session_id}/message`
*   **认证**: 需要
*   **描述**: 客户端发送用户消息。服务器接收后会 **异步** 处理AI回复并保存。
*   **路径参数**: `session_id` (string, required)
*   **请求体**: 可以是 `{"message": "内容"}` 或 `{"content": "内容"}` 或直接是消息字符串。
*   **成功响应 (200 OK)**: `Message` (用户发送并已保存的消息对象)
*   **错误响应**: `400 Bad Request` (消息内容无效), `404 Not Found` (会话不存在或无权访问)
*   **注意**: 此接口 **不** 返回AI的回复。客户端需要调用轮询接口获取。

### 3. 轮询新消息

*   **Endpoint**: `GET /stream/{session_id}/poll`
*   **认证**: 需要
*   **描述**: 客户端定期调用此接口，获取指定会话中比 `last_message_id` 更新的消息（包括AI的回复）。
*   **路径参数**: `session_id` (string, required)
*   **查询参数**: `last_message_id` (string/int, optional): 客户端收到的最后一条消息的ID。如果为空或无效，则视为0，获取所有消息。
*   **成功响应 (200 OK)**: `MessageList` (包含新消息列表和本次获取的数量`total`)
*   **错误响应**: `404 Not Found` (会话不存在或无权访问)

## 参数命名一致性说明

为确保API接口的一致性和可预测性，所有获取新消息的接口都使用相同的参数命名约定：

- 当需要指定一个消息ID作为起点，获取该ID之后的新消息时，统一使用 `last_message_id` 参数
- 在WebSocket轮询接口 `/stream/{session_id}/poll` 中，查询参数使用 `last_message_id`
- 在消息同步接口 `/conversations/{session_id}/messages/since/{message_id}` 中，路径参数 `message_id` 的语义等同于 `last_message_id`

这种一致性有助于前端开发者理解API的行为和参数的含义，减少集成过程中的错误。
