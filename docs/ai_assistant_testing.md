# 健身AI助手系统测试方案

## 一、测试策略概述

健身AI助手系统测试采用多层次测试策略，包括单元测试、集成测试和端到端测试，确保系统各组件功能正常，整体流程顺畅，并能提供高质量的健身指导服务。

## 二、测试环境

- **开发环境**：本地开发机器，用于单元测试和基础集成测试
- **测试环境**：专用测试服务器，模拟生产环境配置
- **预生产环境**：与生产环境配置一致，用于最终验证

## 三、测试用户画像

| 用户类型 | 描述 | 健身水平 | 测试重点 |
|---------|------|----------|----------|
| 初学者（王小明） | 25岁男性，无健身经验，目标减脂 | 初级 | 易懂的指导、基础动作、安全建议 |
| 进阶爱好者（李晓华） | 30岁女性，2年健身经验，目标增肌 | 中级 | 专业训练计划、营养建议、进阶技巧 |
| 专业健身人士（张教练） | 35岁男性，5年以上经验，备战比赛 | 高级 | 专业术语、详细计划、深度知识 |
| 特殊需求人群（刘阿姨） | 45岁女性，腰椎问题，目标康复 | 初级/特殊 | 安全性、适应性、康复指导 |

## 四、测试范围

### 1. 核心组件测试

- **LLM代理服务**：测试百炼模型接口、响应解析、错误处理
- **意图识别系统**：测试意图分类准确性、参数提取
- **对话状态管理**：测试状态转换、上下文维护、会话恢复
- **缓存机制**：测试缓存命中率、过期策略、性能提升
- **参数提取系统**：测试训练参数提取准确性、用户信息提取准确性

### 2. 业务流程测试

- **用户引导流程**：测试新用户信息收集、兴趣引导
- **健身咨询流程**：测试专业问题应答、响应质量
- **训练计划生成**：测试个性化计划生成、适应性调整
- **多轮对话连贯性**：测试上下文保持、回顾引用、主题切换
- **中断与恢复机制**：测试对话中断检测、相关性判断、流程恢复

## 五、测试文件结构

```
tests/
├── integration/
│   └── ai_assistant/
│       ├── test_conversation_flow.py    # 对话流程测试
│       ├── test_intent_handling.py      # 意图处理测试
│       ├── test_conversation_states.py  # 对话状态测试
│       ├── test_bailian_integration.py  # 百炼模型集成测试
│       ├── test_caching.py              # 缓存机制测试
│       ├── test_parameter_extraction.py # 参数提取测试
│       ├── test_user_scenarios.py       # 用户场景测试
│       └── test_end_to_end_flow.py      # 端到端对话流程测试
├── unit/
│   └── ai_assistant/
│       ├── test_llm_proxy.py            # LLM代理单元测试
│       ├── test_state_manager.py        # 状态管理器单元测试
│       ├── test_cache_service.py        # 缓存服务单元测试
│       ├── test_intent_recognizer.py    # 意图识别器单元测试
│       ├── test_parameter_extractor.py  # 参数提取器单元测试
│       └── test_training_param_manager.py # 训练参数管理器单元测试
└── conftest.py                          # 测试fixtures
```

## 六、测试用例设计

### 测试集1：对话状态管理测试

#### 测试文件：`tests/integration/ai_assistant/test_conversation_states.py`

```python
# 简略用例
async def test_state_transition_from_idle_to_fitness():
    # 测试从空闲状态转换到健身建议状态
    
async def test_context_preservation_between_states():
    # 测试状态转换过程中上下文保存和恢复
    
async def test_long_term_user_profile_memory():
    # 测试用户信息长期记忆功能
```

### 测试集2：意图处理测试

#### 测试文件：`tests/integration/ai_assistant/test_intent_handling.py`

```python
# 简略用例
async def test_general_chat_intent_handling():
    # 测试一般聊天意图处理
    
async def test_fitness_advice_intent_handling():
    # 测试健身建议意图处理
    
async def test_workout_plan_generation():
    # 测试训练计划生成
```

### 测试集3：参数提取测试

#### 测试文件：`tests/integration/ai_assistant/test_parameter_extraction.py`

```python
# 简略用例
async def test_extract_training_parameters():
    # 测试从用户消息中提取训练参数
    
async def test_extract_user_information():
    # 测试从用户消息中提取用户信息
    
async def test_parameter_validation():
    # 测试参数验证和纠正机制
```

### 测试集4：百炼模型集成测试

#### 测试文件：`tests/integration/ai_assistant/test_bailian_integration.py`

```python
# 简略用例
async def test_bailian_prompt_template():
    # 测试百炼提示模板效果
    
async def test_professionalism_adjustment_by_level():
    # 测试根据用户水平调整专业度
    
async def test_query_type_specific_response():
    # 测试根据查询类型的专业回答
```

### 测试集5：缓存机制测试

#### 测试文件：`tests/integration/ai_assistant/test_caching.py`

```python
# 简略用例
async def test_llm_response_caching():
    # 测试LLM响应缓存
    
async def test_intent_recognition_caching():
    # 测试意图识别结果缓存
    
async def test_cache_expiration_policy():
    # 测试缓存过期策略
```

### 测试集6：用户场景测试

#### 测试文件：`tests/integration/ai_assistant/test_user_scenarios.py`

```python
# 简略用例
async def test_beginner_user_guidance():
    # 测试初学者用户引导场景
    
async def test_advanced_user_professional_advice():
    # 测试进阶用户专业建议场景
    
async def test_special_needs_safe_recommendations():
    # 测试特殊需求用户安全建议
```

### 测试集7：端到端对话流程测试

#### 测试文件：`tests/integration/ai_assistant/test_end_to_end_flow.py`

```python
# 简略用例
async def test_complete_user_profile_collection_flow():
    # 测试完整的用户信息收集流程
    # 模拟用户：空资料 -> 系统询问基本信息 -> 用户回答 -> 系统验证与保存 -> 继续原始意图处理
    
async def test_training_parameter_collection_flow():
    # 测试完整的训练参数收集流程
    # 模拟用户：请求训练计划但缺少参数 -> 系统询问参数 -> 用户回答 -> 系统生成计划
    
async def test_interruption_and_continuation_flow():
    # 测试中断与恢复流程
    # 模拟用户：正在收集信息过程中 -> 用户发送无关消息 -> 系统询问是否继续 -> 用户确认继续 -> 恢复原流程
    
async def test_multi_turn_conversation_coherence():
    # 测试多轮对话连贯性
    # 模拟一段10轮以上的对话，检查上下文保持和引用能力
    
async def test_topic_switching_and_context_preservation():
    # 测试话题切换时的上下文保存与恢复
    # 模拟用户：健身话题 -> 饮食话题 -> 返回健身话题，检查是否记住前面的内容
```

## 七、测试数据

测试数据包括：

1. **模拟用户消息**：预设的用户问题和对话流
2. **模拟用户配置文件**：包含不同用户特征的配置
3. **LLM响应模拟**：模拟百炼模型的响应
4. **对话场景数据集**：预设的完整对话场景，包含多种用户类型和意图

## 八、测试执行流程

1. **准备阶段**
   - 设置测试环境和依赖
   - 准备测试数据和fixtures
   - 初始化模拟服务

2. **执行阶段**
   - 运行单元测试验证组件功能
   - 运行集成测试验证组件交互
   - 运行用户场景测试验证端到端流程

3. **验证阶段**
   - 收集测试结果和指标
   - 分析失败测试和性能瓶颈
   - 生成测试报告

## 九、质量指标

| 指标类型 | 指标名称 | 目标值 | 测量方法 |
|---------|---------|-------|---------|
| 功能性 | 意图识别准确率 | ≥95% | 正确识别数/总测试数 |
| 功能性 | 专业回答准确率 | ≥90% | 专家评估 |
| 功能性 | 参数提取准确率 | ≥85% | 正确提取数/总测试数 |
| 性能 | 平均响应时间 | ≤2秒 | 自动化测试计时 |
| 性能 | 缓存命中率 | ≥70% | 缓存监控 |
| 可靠性 | 会话完成率 | ≥95% | 成功会话/总会话 |
| 可靠性 | 流程中断恢复率 | ≥90% | 成功恢复数/总中断数 |
| 用户体验 | 满意度评分 | ≥4.5/5 | 用户反馈 |
| 用户体验 | 对话连贯性评分 | ≥4.0/5 | 专家评估 |

## 十、持续集成与部署

测试方案将整合到CI/CD流程中：

1. 每次代码提交触发单元测试
2. 每日构建触发集成测试
3. 版本发布前进行完整的测试套件执行
4. 测试报告自动生成并通知团队

## 十一、缺陷跟踪与修复流程

1. 发现缺陷后在项目管理工具中创建缺陷报告
2. 分配开发人员修复并设置优先级
3. 修复后执行回归测试
4. 验证缺陷解决并关闭报告

## 十二、下一步测试计划

1. 扩展测试覆盖率到所有边缘情况
2. 实现自动化测试脚本完善
3. 添加性能基准测试
4. 建立用户体验测试框架
5. 开发对话流程可视化工具，帮助分析测试结果