

Ran tool

Read file: /home/<USER>/backend/app/schemas/meal.py
# 餐食管理接口详细文档

## 一、餐食记录接口

### 1. 获取用户餐食记录列表
- **接口路径**：`/`
- **请求方法**：GET
- **查询参数**：
  - `start_date`：开始日期 (可选，格式：YYYY-MM-DD，默认为当天)
  - `end_date`：结束日期 (可选，格式：YYYY-MM-DD，默认为start_date)
  - `skip`：分页跳过条数 (可选，默认0)
  - `limit`：分页大小 (可选，默认100)
- **返回数据**：餐食记录摘要列表 (`List[MealRecordSummary]`)
  ```json
  [
    {
      "id": 1,
      "date": "2023-05-01",
      "meal_type": "breakfast",
      "image_url": "http://example.com/image.jpg",
      "total_calory": 500.0,
      "total_protein": 20.0,
      "total_fat": 15.0,
      "total_carbohydrate": 65.0
    }
  ]
  ```

### 2. 获取每日营养摄入汇总
- **接口路径**：`/daily`
- **请求方法**：GET
- **查询参数**：
  - `query_date`：查询日期 (可选，格式：YYYY-MM-DD，默认为当天)
- **返回数据**：每日营养摄入汇总数据 (`DailyNutritionSummary`)
  ```json
  {
    "date": "2023-05-01",
    "total_calory": 1800.0,
    "total_protein": 75.0,
    "total_fat": 50.0,
    "total_carbohydrate": 225.0,
    "meals": [
      {
        "id": 1,
        "date": "2023-05-01",
        "meal_type": "breakfast",
        "image_url": "http://example.com/image.jpg",
        "total_calory": 500.0,
        "total_protein": 20.0,
        "total_fat": 15.0,
        "total_carbohydrate": 65.0
      }
    ]
  }
  ```

### 3. 获取餐食详情
- **接口路径**：`/{meal_id}`
- **请求方法**：GET
- **路径参数**：
  - `meal_id`：餐食记录ID
- **返回数据**：餐食详细信息 (`MealRecord`)
  ```json
  {
    "id": 1,
    "user_id": "user123",
    "date": "2023-05-01",
    "meal_type": "breakfast",
    "image_url": "http://example.com/image.jpg",
    "file_id": "file123",
    "thumb_image_url": "http://example.com/thumb.jpg",
    "is_ai_recognized": true,
    "total_calory": 500.0,
    "total_protein": 20.0,
    "total_fat": 15.0,
    "total_carbohydrate": 65.0,
    "created_at": "2023-05-01T08:00:00",
    "updated_at": "2023-05-01T08:30:00",
    "food_items": [
      {
        "id": 1,
        "meal_record_id": 1,
        "name": "鸡蛋",
        "quantity": 2.0,
        "unit_name": "个",
        "weight": 100.0,
        "category": "蛋类",
        "cuisine_type": "家常菜",
        "cuisine_type_detail": null,
        "image_url": "http://example.com/egg.jpg",
        "food_id": 101,
        "health_light": 1,
        "lights": ["绿灯"],
        "warnings": [],
        "warning_scenes": [],
        "calory": 150.0,
        "protein": 13.0,
        "fat": 10.0,
        "carbohydrate": 1.0,
        "protein_fraction": 0.35,
        "fat_fraction": 0.6,
        "carb_fraction": 0.05,
        "is_custom": false,
        "created_at": "2023-05-01T08:00:00",
        "updated_at": "2023-05-01T08:00:00",
        "nutrient_intakes": [
          {
            "id": 1,
            "food_item_id": 1,
            "name_en": "vitamin_a",
            "name_cn": "维生素A",
            "value": 200.0,
            "unit": "μg",
            "unit_name": "微克",
            "nrv_percentage": 25.0,
            "category": "vitamins"
          }
        ]
      }
    ],
    "health_recommendations": [
      {
        "id": 1,
        "meal_record_id": 1,
        "recommendation_text": "增加蔬菜摄入量",
        "recommendation_type": "nutritional",
        "priority": 1,
        "created_at": "2023-05-01T08:30:00"
      }
    ]
  }
  ```

### 4. 创建餐食记录
- **接口路径**：`/`
- **请求方法**：POST
- **请求体**：餐食创建数据 (`MealRecordCreate`)
  ```json
  {
    "date": "2023-05-01",
    "meal_type": "breakfast",
    "image_url": "http://example.com/image.jpg",
    "file_id": "file123",
    "thumb_image_url": "http://example.com/thumb.jpg",
    "is_ai_recognized": false
  }
  ```
- **返回数据**：创建的餐食记录 (`MealRecord`) - 同上述餐食详情返回格式

### 5. 更新餐食记录
- **接口路径**：`/{meal_id}`
- **请求方法**：PUT
- **路径参数**：
  - `meal_id`：餐食记录ID
- **请求体**：餐食更新数据 (`MealRecordUpdate`)
  ```json
  {
    "image_url": "http://example.com/new_image.jpg",
    "file_id": "newfile123",
    "thumb_image_url": "http://example.com/new_thumb.jpg",
    "is_ai_recognized": true
  }
  ```
- **返回数据**：更新后的餐食记录 (`MealRecord`) - 同上述餐食详情返回格式

### 6. 删除餐食记录
- **接口路径**：`/{meal_id}`
- **请求方法**：DELETE
- **路径参数**：
  - `meal_id`：餐食记录ID
- **返回数据**：被删除的餐食记录 (`MealRecord`) - 同上述餐食详情返回格式

## 二、食物项接口

### 1. 添加食物项
- **接口路径**：`/{meal_id}/food-items`
- **请求方法**：POST
- **路径参数**：
  - `meal_id`：餐食记录ID
- **请求体**：食物项创建数据 (`FoodItemCreate`)
  ```json
  {
    "name": "鸡蛋",
    "quantity": 2.0,
    "unit_name": "个",
    "weight": 100.0,
    "category": "蛋类",
    "cuisine_type": "家常菜",
    "cuisine_type_detail": null,
    "image_url": "http://example.com/egg.jpg",
    "food_id": 101,
    "health_light": 1,
    "lights": ["绿灯"],
    "warnings": [],
    "warning_scenes": [],
    "calory": 150.0,
    "protein": 13.0,
    "fat": 10.0,
    "carbohydrate": 1.0,
    "protein_fraction": 0.35,
    "fat_fraction": 0.6,
    "carb_fraction": 0.05,
    "is_custom": false,
    "nutrient_intakes": [
      {
        "name_en": "vitamin_a",
        "name_cn": "维生素A",
        "value": 200.0,
        "unit": "μg",
        "unit_name": "微克",
        "nrv_percentage": 25.0,
        "category": "vitamins"
      }
    ]
  }
  ```
- **返回数据**：创建的食物项 (`FoodItem`)
  ```json
  {
    "id": 1,
    "meal_record_id": 1,
    "name": "鸡蛋",
    "quantity": 2.0,
    "unit_name": "个",
    "weight": 100.0,
    "category": "蛋类",
    "cuisine_type": "家常菜",
    "cuisine_type_detail": null,
    "image_url": "http://example.com/egg.jpg",
    "food_id": 101,
    "health_light": 1,
    "lights": ["绿灯"],
    "warnings": [],
    "warning_scenes": [],
    "calory": 150.0,
    "protein": 13.0,
    "fat": 10.0,
    "carbohydrate": 1.0,
    "protein_fraction": 0.35,
    "fat_fraction": 0.6,
    "carb_fraction": 0.05,
    "is_custom": false,
    "created_at": "2023-05-01T08:00:00",
    "updated_at": "2023-05-01T08:00:00",
    "nutrient_intakes": [
      {
        "id": 1,
        "food_item_id": 1,
        "name_en": "vitamin_a",
        "name_cn": "维生素A",
        "value": 200.0,
        "unit": "μg",
        "unit_name": "微克",
        "nrv_percentage": 25.0,
        "category": "vitamins"
      }
    ]
  }
  ```

### 2. 获取食物项详情
- **接口路径**：`/food-items/{food_item_id}`
- **请求方法**：GET
- **路径参数**：
  - `food_item_id`：食物项ID
- **返回数据**：食物项详细信息 (`FoodItem`) - 同上述食物项返回格式

### 3. 更新食物项
- **接口路径**：`/food-items/{food_item_id}`
- **请求方法**：PUT
- **路径参数**：
  - `food_item_id`：食物项ID
- **请求体**：食物项更新数据 (`FoodItemUpdate`)
  ```json
  {
    "name": "鸡蛋",
    "quantity": 3.0,
    "unit_name": "个",
    "weight": 150.0,
    "category": "蛋类",
    "cuisine_type": "家常菜",
    "cuisine_type_detail": null,
    "image_url": "http://example.com/egg_updated.jpg",
    "food_id": 101,
    "calory": 225.0,
    "protein": 19.5,
    "fat": 15.0,
    "carbohydrate": 1.5,
    "is_custom": false
  }
  ```
- **返回数据**：更新后的食物项 (`FoodItem`) - 同上述食物项返回格式

### 4. 删除食物项
- **接口路径**：`/food-items/{food_item_id}`
- **请求方法**：DELETE
- **路径参数**：
  - `food_item_id`：食物项ID
- **返回数据**：被删除的食物项 (`FoodItem`) - 同上述食物项返回格式

## 三、数据模型枚举说明

### MealType（餐食类型）
```json
{
  "BREAKFAST": "breakfast",
  "LUNCH": "lunch",
  "DINNER": "dinner",
  "SNACK": "snack"
}
```

## 四、接口调用注意事项

1. 所有接口需要用户身份验证，请在请求头中包含有效的认证信息
2. 用户只能操作自己的数据
3. 对食物项的操作会自动更新关联餐食记录的营养总值
4. 日期格式统一为ISO格式：`YYYY-MM-DD`
5. 时间戳格式统一为ISO格式：`YYYY-MM-DDThh:mm:ss`
