#!/bin/bash

# 检查ffmpeg是否安装
if ! command -v ffmpeg &> /dev/null; then
    echo "错误: 需要安装ffmpeg"
    exit 1
fi

# 设置目标目录
VIDEO_DIR="/data/exercises/videos/"

# 检查目录是否存在
if [ ! -d "$VIDEO_DIR" ]; then
    echo "错误: 目录 $VIDEO_DIR 不存在"
    exit 1
fi

# 计数器
total_files=0
converted_files=0

# 查找所有MP4文件并转换
echo "开始转换视频到H264编码..."
for video in "$VIDEO_DIR"/*.mp4; do
    # 检查文件是否存在（避免通配符不匹配的情况）
    if [ ! -f "$video" ]; then
        echo "未找到MP4文件"
        exit 0
    fi
    
    total_files=$((total_files+1))
    
    # 创建临时文件
    temp_file="${video}.temp.mp4"
    
    echo "正在转换: $video"
    
    # 使用ffmpeg转换视频编码为H264，将输出写入临时文件
    if ffmpeg -i "$video" -c:v libx264 -crf 23 -preset medium -c:a copy "$temp_file" -y; then
        # 转换成功，用临时文件替换原文件
        mv "$temp_file" "$video"
        converted_files=$((converted_files+1))
        echo "✓ 成功转换: $video"
    else
        # 转换失败，删除临时文件
        rm -f "$temp_file"
        echo "✗ 转换失败: $video"
    fi
done

echo "转换完成: 共处理 $total_files 个文件，成功转换 $converted_files 个文件" 