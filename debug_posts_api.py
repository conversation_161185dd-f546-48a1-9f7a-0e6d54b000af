#!/usr/bin/env python3
import asyncio
import requests
import json
import sys
from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.services.community_service import CommunityService
from app.models.community import Post, PostStatus

async def debug_get_posts_api():
    """调试 GET /api/v1/community/posts/ 接口"""
    print("\n===== 开始调试社区帖子API =====")
    
    # 1. 直接检查数据库中的帖子
    db = SessionLocal()
    try:
        print("\n-- 数据库中的帖子 --")
        posts = db.query(Post).filter(Post.status == PostStatus.ACTIVE).all()
        print(f"数据库中有 {len(posts)} 个活动帖子")
        
        if posts:
            for i, post in enumerate(posts[:3]):  # 只打印前3个
                print(f"帖子 {i+1}: ID={post.id}, 标题={post.title}, 状态={post.status}")
        
        # 2. 通过服务直接获取帖子
        print("\n-- 通过CommunityService获取帖子 --")
        service = CommunityService(db)
        result = await service.get_posts(skip=0, limit=10, current_user_id=None)
        
        print(f"服务返回: 总数={result['total']}, 项目数={len(result['items'])}")
        if result['items']:
            print(f"第一个帖子: ID={result['items'][0]['id']}, 标题={result['items'][0]['title']}")
            print(f"状态类型: {type(result['items'][0]['status'])}")
            print(f"帖子字段: {list(result['items'][0].keys())}")
        
        # 3. 通过HTTP API获取帖子
        print("\n-- 通过HTTP API获取帖子 --")
        api_url = "http://localhost:8000/api/v1/community/posts/"
        response = requests.get(api_url)
        
        print(f"API响应状态码: {response.status_code}")
        if response.status_code == 200:
            api_result = response.json()
            print(f"API返回: 总数={api_result.get('total', 'N/A')}, 项目数={len(api_result.get('items', []))}")
            
            if api_result.get('items'):
                print(f"第一个帖子: ID={api_result['items'][0].get('id')}, 标题={api_result['items'][0].get('title')}")
                print(f"响应数据类型: {type(api_result)}")
                print(f"响应内容: {json.dumps(api_result, indent=2)[:500]}...")  # 只打印前500个字符
            else:
                print("API返回的items列表为空或不存在")
        else:
            print(f"API响应内容: {response.text}")
            
        # 检查路由顺序问题
        print("\n-- 检查路由冲突 --")
        print("检查app/api/endpoints/community.py中的路由顺序...")
        print("GET /posts/{post_id}路由是否在GET /posts/路由之前定义? (可能导致路由冲突)")
        
        # 检查过滤条件问题
        print("\n-- 检查服务中的过滤条件 --")
        service_code = "查看app/services/community_service.py和app/crud/crud_community.py"
        print(f"建议检查: {service_code}")
        print("确保使用了正确的枚举值: PostStatus.ACTIVE而不是字符串'approved'或其他值")
        
        # 检查响应模型问题
        print("\n-- 检查响应模型 --")
        print("确保app/schemas/community.py中的PostListResponse模型与实际返回的数据结构匹配")
            
    except Exception as e:
        print(f"调试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()
        print("\n===== 调试结束 =====")

if __name__ == "__main__":
    asyncio.run(debug_get_posts_api()) 