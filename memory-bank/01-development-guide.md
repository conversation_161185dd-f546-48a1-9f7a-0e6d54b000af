
## 快速开始

### 环境要求
- Python 3.11+
- Docker & Docker Compose
- PostgreSQL 15
- Redis 7

### Tips
接下来会介绍两种开发，本地开发和docker部署，其中目前我已经完成了本地开发，开启了后端fastapi服务。到那时还没有使用docker部署，我也不知道docker部署是否是必须的。

### 本地开发

1. 克隆项目
```bash
git clone <repository-url>
cd backend
```

2. 创建虚拟环境并安装依赖
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
.\venv\Scripts\activate  # Windows
pip install -r requirements.txt
```

3. 配置环境变量
```bash
cp .env.example .env
# 编辑.env文件，设置必要的环境变量
```

4. 启动服务
```bash
source venv/bin/activate  
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload > uvicorn.log 2>&1
```

### Docker部署

```bash
./docker-compose-v2 up -d
docker-compose up -d
docker-compose stop backend
docker build
```

更多详细的部署和开发说明，请参考 [部署文档](docs/deployment.md)。

## 核心数据模型 (SQLAlchemy Models)

- **用户 (User):** 核心用户信息（微信openid, 昵称, 头像, 基本身体数据等）。
- **用户设置 (UserSetting):** 用户特定配置，如通知开关。
- **健身动作 (Exercise):** 动作基本信息（名称, 部位, 器材, 图片/GIF链接, 热度等）。
- **动作详情 (ExerciseDetail):** 动作的详细指导（目标肌肉, 教学步骤, 视频等）。
- **肌肉 (Muscle):** 肌肉信息。
- **身体部位 (BodyPart):** 身体部位信息。
- **器材 (Equipment):** 健身器材信息。
- **用户收藏 (UserFavoriteExercise, UserFavoriteFood):** 用户收藏的动作和食物。
- **食物 (Food):** 食物基础信息（名称, 分类, 图片等）。
- **营养概况 (NutritionalProfile):** 食物的主要营养成分概览（热量, 三大营养素比例等）。
- **食物营养素 (FoodNutrientValue):** 食物详细营养素含量。
- **食物单位 (FoodUnit):** 食物的计量单位（如克, 份）。
- **餐食记录 (MealRecord):** 用户记录的餐食（日期, 类型, 图片, 总营养等）。
- **食物项 (FoodItem):** 餐食记录中的具体食物项（名称, 重量, 营养详情）。
- **食物项营养摄入 (FoodItemNutrientIntake):** 食物项的详细营养素摄入记录。
- **健康建议 (HealthRecommendation):** 基于餐食记录生成的健康建议。
- **食物识别 (FoodRecognition):** AI食物识别任务记录（图片, 状态, 识别结果）。
- **分享追踪 (ShareTrack):** 小程序码分享和扫码追踪记录。

注意：更多详细的属性和关系请直接查阅 `app/models/` 目录下的代码。

## 主要API接口 (app/api/endpoints)

接口基于FastAPI Router组织，主要模块如下：

### 认证 (auth.py)
- `POST /auth/login/wechat`: 微信登录/注册
- `POST /auth/refresh-token`: 刷新JWT令牌
- `POST /auth/logout`: 用户登出
- `POST /auth/wechat/phone`: 绑定微信手机号
- `POST /auth/wechat/code2session`: 获取微信session

### 用户 (user.py)
- `GET /user/profile`: 获取当前用户资料
- `POST /user/profile`: 更新用户资料
- `POST /user/avatar`: 上传/更新用户头像 (支持文件/URL/Base64)
- `GET /user/settings`: 获取用户设置
- `POST /user/settings`: 更新用户设置
- `GET /user/exists`: 检查用户是否存在 (根据openid)

### 健身动作 (exercise.py)
- `GET /exercises/`: 获取动作列表 (可按部位/器材过滤)
- `GET /exercises/search`: 搜索动作 (关键词, 部位, 器材, 肌肉, 难度)
- `GET /exercises/{id}`: 获取动作详情
- `POST /exercises/{id}/hit`: 增加动作浏览次数
- `GET /exercises/{id}/detail`: 获取动作详细教学信息
- `GET /body-parts/`: 获取身体部位列表
- `GET /muscles/`: 获取肌肉列表
- `GET /equipment/`: 获取器材列表
- `GET /image/{filename}`: 获取动作图片 (已弃用, 使用下方路径)
- `GET /gif/{filename}`: 获取动作GIF (已弃用, 使用下方路径)
- `GET /video/{filename}`: 获取动作视频 (支持范围请求)
- `GET /image/exercises/{filename}`: 获取动作图片 (推荐)
- `GET /video_wx/{filename}`: 获取微信兼容的视频流

### 收藏 (favorites.py)
- `POST /exercises/{exercise_id}/favorite`: 添加/取消收藏动作
- `POST /foods/{food_id}/favorite`: 添加/取消收藏食物
- `GET /exercises/favorites`: 获取收藏的动作列表
- `GET /foods/favorites`: 获取收藏的食物列表
- `GET /exercises/{exercise_id}/is-favorite`: 检查动作是否已收藏
- `GET /foods/{food_id}/is-favorite`: 检查食物是否已收藏

### 食物库 (food.py)
- `GET /foods/`: 获取食物列表 (支持名称/分类/类型过滤, 按热度排序)
- `GET /foods/search`: 按名称搜索食物 (相似度匹配, 按热度排序)
- `GET /foods/categories`: 获取所有食物分类
- `GET /foods/types`: 获取所有食物类型
- `GET /foods/{id}`: 获取食物详情 (按ID)
- `GET /foods/code/{code}`: 获取食物详情 (按编码)

### 食物识别 (food_recognition.py)
- `POST /food-recognitions/analyze`: 通过上传图片分析食物
- `POST /food-recognitions/analyze-base64`: 通过Base64图片分析食物
- `POST /food-recognitions/{id}/confirm`: 用户确认/修改识别结果，生成餐食记录
- `GET /food-recognitions/pending`: 获取当前用户待处理的识别任务
- `GET /food-recognitions/image/{secure_path}/{date}/{filename}`: 获取识别任务关联的图片
- `GET /food-recognitions/`: 获取识别记录列表 (支持日期/状态过滤)
- `GET /food-recognitions/{id}`: 获取单个识别记录详情
- `POST /food-recognitions/{id}/reject`: 用户拒绝识别结果
- `GET /food-recognitions/stats`: 获取识别任务统计

### 餐食记录 (meal.py & food_item.py)
- `GET /meals/`: 获取用户餐食记录列表 (支持日期过滤)
- `GET /meals/daily`: 获取用户某日营养汇总
- `GET /meals/{id}`: 获取单条餐食记录详情
- `POST /meals/`: 创建餐食记录
- `PUT /meals/{id}`: 更新餐食记录
- `DELETE /meals/{id}`: 删除餐食记录
- `POST /meals/{id}/food-items`: 向餐食中添加食物项
- `DELETE /meals/food-items/{food_item_id}`: 从餐食中移除食物项
- `GET /meals/food-items/{food_item_id}`: 获取食物项详情
- `PUT /meals/food-items/{food_item_id}`: 更新食物项
- `GET /meals/food-items/{id}/nutrient-intakes`: 获取食物项的详细营养素列表

### 健康建议 (health_recommendation.py)
- `GET /meals/{meal_id}/recommendations`: 获取餐食的健康建议列表
- `GET /meals/recommendations/{id}`: 获取单条健康建议详情

### 小程序码 (qrcode.py)
- `POST /qrcode/generate`: 生成小程序码 (返回URL和本地路径)
- `GET /qrcode/image/{secure_path}/{filename}`: 获取二维码图片文件
- `DELETE /qrcode/cleanup`: 清理过期的二维码文件和记录 (管理员)

### 分享追踪 (share.py)
- `POST /share/track`: 记录分享事件 (如按钮分享)
- `GET /share/scan/{share_code}`: 记录扫码事件 (暂未启用)

### 健康检查 (health.py)
- `GET /health/ping`: API健康检查

### LLM日志 (llm_logs.py, 仅管理员)
- `/llm-logs/...`: 用于查看、管理与LLM交互相关的日志。

完整API文档请访问部署后的 `/api/v1/docs` 或 `/api/v1/redoc`。