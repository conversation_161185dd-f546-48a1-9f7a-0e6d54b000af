# API文档

## 概述

所有API接口的基础路径为`/api/v1`，整体采用RESTful风格设计，支持JSON格式的数据交换。

## 基础信息

- 认证方式：Bearer Token（JWT）
- 请求头格式：`Authorization: Bearer {token}`
- 响应格式：JSON

## 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 资源创建成功 |
| 400 | 请求参数错误 |
| 401 | 未认证或认证失败 |
| 403 | 无权限访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## API结构

系统API采用模块化设构，主要分为以下模块：

### 目录结构
```
app/api/
├── admin/           # 管理后台相关
├── deps.py          # 依赖项注入
├── endpoints/       # API端点实现
│   ├── auth.py      # 认证相关接口
│   ├── health.py    # 健康检查接口
│   ├── qrcode.py    # 二维码生成接口
│   ├── share.py     # 分享相关接口
│   └── user.py      # 用户相关接口
├── openapi_docs.py  # API文档配置
└── v1/              # V1版本API
```

## 认证接口

### 微信小程序登录

```
POST /auth/login/wechat
```

**请求参数**

```json
{
  "code": "string",                     // 必填，微信临时登录凭证
  "userInfo": {                         // 可选，用户信息对象
    "nickname": "微信用户",
    "avatar_url": "https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132"
  },
  "encryptedData": "string",            // 可选，微信加密数据
  "iv": "string",                       // 可选，加密算法的初始向量
  "openid": "string",                   // 可选，用户openid
  "unionid": "string"                   // 可选，用户unionid
}
```

**响应内容**

```json
{
  "success": true,
  "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "expires_at": "2024-05-06T12:00:00",
  "user": {
    "id": 1,
    "nickname": "微信用户",
    "avatarUrl": "https://example.com/avatar.jpg",
    "completed": false,
    "created_at": "2024-04-05T12:00:00"
  },
  "is_new_user": false
}
```

### 刷新访问令牌

```
POST /auth/refresh-token
```

**请求头**
- Authorization: Bearer {token}

**响应内容**

```json
{
  "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "expires_at": "2024-05-06T12:00:00"
}
```

### 用户登出

```
POST /auth/logout
```

**请求头**
- Authorization: Bearer {token}

**响应内容**

```json
{
  "success": true,
  "message": "登出成功"
}
```

### 绑定微信手机号

```
POST /auth/wechat/phone
```

**请求参数**

```json
{
  "code": "string",                    // 必填，微信临时登录凭证
  "encrypted_data": "string",          // 必填，微信加密数据
  "iv": "string"                       // 必填，加密算法的初始向量
}
```

**响应内容**

```json
{
  "success": true,
  "message": "手机号绑定成功",
  "phone": "13800138000"
}
```

## 用户接口

### 获取用户个人资料

```
GET /user/profile
```

**请求头**
- Authorization: Bearer {token}

**响应内容**

```json
{
  "id": 1,
  "nickname": "用户昵称",
  "avatar_url": "/api/v1/user/avatar/image/abc123def456/avatar_20240406.jpg",
  "phone": "13800138000",
  "gender": "MALE",
  "gender_int": 1,
  "country": "中国",
  "province": "广东",
  "city": "深圳",
  "age": 30,
  "weight": 70.5,
  "height": 175.0,
  "activity_level": 3,
  "body_type": "中等体型",
  "experience_level": "BEGINNER",
  "fitness_goal": "WEIGHT_LOSS",
  "bmi": 23.0,
  "tedd": 2500,
  "completed": true,
  "created_at": "2024-04-05T12:00:00",
  "notification_enabled": true
}
```

### 更新用户个人资料

```
POST /user/profile
```

**请求头**
- Authorization: Bearer {token}

**请求参数**

```json
{
  "nickname": "新昵称",
  "avatar_url": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEA...",  // 支持base64格式
  "gender": "MALE",
  "age": 30,
  "weight": 70.5,
  "height": 175.0,
  "activity_level": 3,
  "body_type": "中等体型",
  "experience_level": "BEGINNER",
  "fitness_goal": "WEIGHT_LOSS"
}
```

**响应内容**

```json
{
  "id": 1,
  "nickname": "新昵称",
  "avatar_url": "/api/v1/user/avatar/image/abc123def456/avatar_20240406.jpg",
  "gender": "MALE",
  "gender_int": 1,
  "age": 30,
  "weight": 70.5,
  "height": 175.0,
  "activity_level": 3,
  "body_type": "中等体型",
  "experience_level": "BEGINNER",
  "fitness_goal": "WEIGHT_LOSS",
  "bmi": 23.0,
  "tedd": 2500,
  "completed": true,
  "created_at": "2024-04-05T12:00:00"
}
```

### 上传用户头像

```
POST /user/avatar
```

**请求头**
- Authorization: Bearer {token}
- Content-Type: multipart/form-data

**请求参数**
- file: 图片文件（支持JPG, PNG, GIF）
- url: 图片URL地址（可替代file）
- avatar_data: Base64编码的图像数据（可替代file）

**响应内容**

```json
{
  "success": true,
  "avatar_url": "/api/v1/user/avatar/image/abc123def456/avatar_20240406.jpg"
}
```

### 访问用户头像

```
GET /user/avatar/image/{secure_path}/{filename}
```

获取用户头像图片。

### 检查用户是否存在

```
GET /user/exists?openid={openid}
GET /user/check?openid={openid}
```

**请求参数**
- openid: 微信用户openid（查询参数）

**响应内容**

```json
{
  "exists": true,
  "user_id": 1
}
```

### 获取用户设置

```
GET /user/settings
```

**请求头**
- Authorization: Bearer {token}

**响应内容**

```json
{
  "notification_enabled": true,
  "theme": "light",
  "language": "zh_CN"
}
```

### 更新用户设置

```
POST /user/settings
```

**请求头**
- Authorization: Bearer {token}

**请求参数**

```json
{
  "notification_enabled": false,
  "theme": "dark",
  "language": "en_US"
}
```

**响应内容**

```json
{
  "success": true,
  "message": "设置更新成功",
  "settings": {
    "notification_enabled": false,
    "theme": "dark",
    "language": "en_US"
  }
}
```

## 二维码相关接口

### 生成小程序码

```
POST /qrcode/generate
```

**请求头**
- Authorization: Bearer {token}

**请求参数**

```json
{
  "page": "pages/index/index",
  "scene": "id=1",
  "width": 280,
  "auto_color": true,
  "line_color": {"r":0,"g":0,"b":0},
  "is_hyaline": false
}
```

**响应内容**

```json
{
  "success": true,
  "qrcode_url": "/api/v1/qrcode/image/abc123def456/qrcode_20240406.jpg"
}
```

### 访问小程序码图片

```
GET /qrcode/image/{secure_path}/{filename}
```

获取生成的小程序码图片。

## 分享追踪接口

### 记录分享事件

```
POST /share/event
```

**请求头**
- Authorization: Bearer {token}

**请求参数**

```json
{
  "type": "program",
  "content_id": 1,
  "channel": "wechat_moments"
}
```

**响应内容**

```json
{
  "success": true,
  "message": "分享事件记录成功",
  "share_id": "1234567890",
  "qrcode_url": "/api/v1/qrcode/image/abc123def456/qrcode_20240406.jpg"
}
```

### 获取分享统计

```
GET /share/stats
```

**请求头**
- Authorization: Bearer {token}

**响应内容**

```json
{
  "total_shares": 10,
  "program_shares": 5,
  "article_shares": 3,
  "other_shares": 2,
  "channels": {
    "wechat_moments": 6,
    "wechat_friends": 4
  }
}
```

## 健康检查接口

### 服务状态检查

```
GET /ping
```

**响应内容**

```json
{
  "status": "success",
  "message": "pong"
}
```

## 错误响应格式

当API调用出错时，会返回如下格式的错误信息：

```json
{
  "detail": "错误详细信息"
}
```

或者更详细的错误信息：

```json
{
  "detail": {
    "detail": "用户认证失败",
    "code": "token_expired"
  }
}
```

## 接口文档访问

- Swagger UI: http://localhost:8000/api/v1/docs
- OpenAPI JSON: http://localhost:8000/api/v1/openapi.json 