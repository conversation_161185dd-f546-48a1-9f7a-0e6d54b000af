# Project Overview

## 项目名
智能健身教练后端服务

## 项目概述
这是智能健身教练小程序的后端服务，基于FastAPI框架开发，提供RESTful API接口。系统包含用户管理、健身数据跟踪和管理后台等功能，已实现用户登录验证、个人资料导入和修改等核心功能。

## 技术栈
- Python 3.11
- FastAPI
- PostgreSQL
- Redis
- SQLAlchemy
- Alembic
- Jinja2 (管理后台页面渲染)
- Docker

## 项目结构
```
backend/
├── app/
│   ├── api/
│   │   ├── admin/         # 管理后台API
│   │   ├── endpoints/     # 主要API端点
│   │   └── v1/            # v1版本API路由
│   ├── core/              # 核心配置 (settings, security)
│   ├── crud/              # CRUD数据库操作
│   ├── data/              # 初始/示例数据
│   ├── db/                # 数据库会话和基类
│   ├── models/            # SQLAlchemy数据模型
│   ├── schemas/           # Pydantic数据验证模式
│   ├── scripts/           # 数据库脚本等
│   ├── services/          # 业务逻辑服务
│   ├── static/            # 应用内静态文件 (例如二维码html)
│   ├── templates/         # Jinja2模板 (主要是管理后台)
│   ├── utils/             # 工具函数
│   ├── avatar_converter.py # 头像转换工具
│   ├── main.py            # FastAPI应用入口
│   └── __init__.py
├── static/                # 全局静态资源文件 (由Nginx服务)
├── tests/                 # 测试代码 (待添加)
├── alembic/               # 数据库迁移
├── docs/                  # 详细文档
│   ├── user.md            # 用户相关功能文档
│   ├── auth.md            # 认证相关功能文档
│   ├── admin.md           # 管理后台文档
│   ├── api.md             # 详细API文档
│   ├── database.md        # 数据库管理文档
│   ├── deployment.md      # 详细部署指南
│   └── exercise.md        # 健身动作模块文档
├── .env                   # 环境变量
├── docker-compose.yml     # Docker配置
├── Dockerfile             # Docker构建文件
└── requirements.txt       # 依赖包
```

## 已实现功能

### 用户管理
- 用户注册与登录（微信小程序授权）
- 用户资料管理（基本信息、身体数据）
- 用户头像上传与处理
- 用户设置管理

### 认证系统
- JWT令牌认证
- 令牌刷新机制
- 微信登录集成
- 用户会话管理

### 健身动作库
- 健身动作基础信息管理
- 动作详细信息与教学指导
- 根据身体部位、器材和肌肉分类查询
- 动作相关图片、GIF和视频资源
- 动作热度统计与排序
- 动作搜索功能

### 小程序码生成
- 动态生成分享小程序码
- 分享追踪统计

### 管理后台
- 用户数据概览
- 用户管理功能
- 分享数据统计

## 待实现功能
- 健身计划管理
- 健身数据统计与分析
- 用户行为数据分析
- 推送通知系统
- 社区互动功能