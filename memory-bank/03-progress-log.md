## Process-log
### #1
我目前拿到了这个项目，安装要求配置完了FastAPI，并执行了uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload 完成了后端配置
另外我的数据库Database有一个在生产环境线上的，我在开发的时候不希望docker启动一个本地数据库然后做alembic的数据库迁移，而是准备直接调用线上数据库的端口，映射到本地，从而完成数据库配置。
远程服务器信息如下：
服务器: **************
用户名: dingyan
密码: sciencefit123
因此我执行了 ssh -L 5433:127.0.0.1:5432 -o ServerAliveInterval=60 -o ServerAliveCountMax=120 dingyan@**************
并且计划在本地通过psql -h 127.0.0.1 -p 5433 -U postgres -d fitness_db 来访问到数据库